namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///废弃物处置详细表
    ///</summary>
    [SugarTable("dc_WasteDisposalDetail", "废弃物处置详细表")]
    public class DcWasteDisposalDetail : BaseEntity
    {

        public DcWasteDisposalDetail()
        {

        }

        /// <summary>
        ///废弃物处置主表Id
        /// </summary>
        public long WasteDisposalId { get; set; }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///基础分类Id
        /// </summary>
        public long BaseWasteId { get; set; }

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Num { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        ///操作人
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///更新时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal WasteRecordNum { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string WasteRecordRemark { get; set; }
    }


}

