﻿using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.IServices;
using NetTaste;
using Hyun.Core.Model.Models.Uniform;
using Hyun.Core.IServices.Uniform;
using Google.Protobuf.WellKnownTypes;
using Dm;
using Hyun.Core.Model.Models;
using NPOI.Util;
using StackExchange.Redis;
using SqlSugar;
using Hyun.Old.Util;
namespace Hyun.Core.Api
{
    /// <summary>
    /// 校服选购
    /// </summary>
    [Route("api/hyun/xuniformscheme")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformSchemeController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformSchemeServices ixuniformschemeservices;
        private readonly IXUniformSchemeOpinionServices ixuniformSchemeOpinionManager;
        private readonly IPUnitServices ipunitManager;
        private readonly IPSchoolExtensionServices ipschoolExtensionServices;
        private readonly IBAreaServices ibareaManager;
        private readonly IBDictionaryServices ibdictionaryManager;
        private readonly IBAttachmentServices ibattachmentManager;
        private readonly IXUniformConfigServices ixuniformConfigManager;
        private readonly IPStudentServices ipstudentManager;
        private readonly IPParentStudentServices ipparentStudentManager;
        public XUniformSchemeController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IXUniformSchemeServices _ixuniformschemeservicesManager, IXUniformSchemeOpinionServices _ixuniformSchemeOpinionManager, IPUnitServices _ipunitService, IPSchoolExtensionServices _pSchoolExtensionServices, IBAreaServices _ibareaManager, IBAttachmentServices _ibattachmentManager, IXUniformConfigServices _ixuniformConfigManager, IBDictionaryServices _ibdictionaryManager, IPStudentServices _ipstudentManager, IPParentStudentServices _ipparentStudentManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ixuniformschemeservices = _ixuniformschemeservicesManager;
            ixuniformSchemeOpinionManager = _ixuniformSchemeOpinionManager;
            ipunitManager = _ipunitService;
            ipschoolExtensionServices = _pSchoolExtensionServices;
            ibareaManager = _ibareaManager;
            ibattachmentManager = _ibattachmentManager;
            ixuniformConfigManager = _ixuniformConfigManager;
            ibdictionaryManager = _ibdictionaryManager;
            ipstudentManager = _ipstudentManager;
            ipparentStudentManager = _ipparentStudentManager;
        }

        #region 查询

        /// <summary>
        /// 方案选用-查询
        /// </summary>
        /// <param name="param">XUniformSchemeParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<XUniformSchemeDto>>> GetPaged([FromBody] XUniformSchemeParam param)
        {
            Result<List<XUniformSchemeDto>> r = new Result<List<XUniformSchemeDto>>();
            param.UnitType = user.UnitTypeId;
            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }

            if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
            {
                param.SchoolId = user.UnitId;
                if (param.SourceType != 1)
                {
                    param.SolicitedStatuz = UniformSolicitedEnum.Publish.ObjToInt();
                    if (param.StatuzFiling == UniformFilingEnum.SubmitNone.ObjToInt())
                    {
                        param.SeekStatuz = 2;
                    }
                }
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
            {
                param.SolicitedStatuz = UniformSolicitedEnum.Publish.ObjToInt();
                param.CountyId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
            {
                param.SolicitedStatuz = UniformSolicitedEnum.Publish.ObjToInt();
                param.CityId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.Parent.ObjToInt().ToString()))
            {
                //家长，根据学生查单位。
                param.SolicitedStatuz = UniformSolicitedEnum.Publish.ObjToInt();
                param.userId = user.UserId;
            }
            var pg = await ixuniformschemeservices.GetPaged(param);
            List<BArea> listArea = null;
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
                {
                    if (param.SourceType == 1)
                    {
                        r.data.rows = (from item in pg.data
                                       select new XUniformSchemeDto()
                                       {
                                           Id = item.Id,
                                           SchoolId = item.SchoolId,
                                           SchemeYear = item.SchemeYear,
                                           SchemeNo = item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt() ? item.SchemeNo : "--",
                                           SolicitedNum = item.SolicitedNum,
                                           SolicitedDeadlineStr = item.SolicitedDeadline == null ? "--" : ((DateTime)item.SolicitedDeadline).ToString("yyyy-MM-dd"),
                                           SolicitedStatuz = item.SolicitedStatuz,
                                           SolicitedStatuzName = item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt() ? UniformSolicitedEnum.Publish.GetDescription() : UniformSolicitedEnum.Cretae.GetDescription(),
                                           IsSolicitedEnd = item.SolicitedDeadline == null ? 2 : (item.SolicitedDeadline < DateTime.Now.Date ? 1 : 2),
                                           ReleaseTimeStr = item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt() ? ((DateTime)item.ReleaseTime).ToString("yyyy-MM-dd") : "--",
                                           IsCountyManager = 1
                                       }).ToList();
                    }
                    else
                    {
                        r.data.rows = (from item in pg.data
                                       select new XUniformSchemeDto
                                       {
                                           Id = item.Id,
                                           SchoolId = item.SchoolId,
                                           SchemeYear = item.SchemeYear,
                                           SchemeNo = item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt() ? item.SchemeNo : "--",
                                           SolicitedNum = item.SolicitedNum,
                                           AgreeNum = item.AgreeNum,
                                           AgreeRate = item.AgreeRate,
                                           PurchaseMethodName = item.PurchaseMethodName,
                                           SolicitedStatuz = item.SolicitedStatuz,
                                           SolicitedStatuzName = GetSolicitedStatuzName(item),
                                           FilingStatuz = item.FilingStatuz,
                                           IsFiling = item.IsFiling,
                                           IsCountyManager = 1,
                                           FilingStatuzName = GetFilingStatuzName(item)
                                       }).ToList();
                    }

                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    r.data.rows = (from item in pg.data
                                   select new XUniformSchemeDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SchemeYear = item.SchemeYear,
                                       SchoolName = item.SchoolName,
                                       SchemeNo = item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt() ? item.SchemeNo : "--",
                                       SolicitedNum = item.SolicitedNum,
                                       AgreeNum = item.AgreeNum,
                                       AgreeRate = item.AgreeRate,
                                       PurchaseMethodName = item.PurchaseMethodName,
                                       SolicitedStatuz = item.SolicitedStatuz,
                                       SolicitedStatuzName = GetSolicitedStatuzName(item),
                                       FilingStatuz = item.FilingStatuz,
                                       IsFiling = item.IsFiling,
                                       IsCountyManager = 1,
                                       FilingStatuzName = GetFilingStatuzName(item)
                                   }).ToList();
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                    r.data.rows = (from item in pg.data
                                   select new XUniformSchemeDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SchoolName = item.SchoolName,
                                       CountyName = GetAreaName(item.CountyAreaId, item.CountyName, listArea),
                                       SchemeYear = item.SchemeYear,
                                       SchemeNo = item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt() ? item.SchemeNo : "--",
                                       SolicitedNum = item.SolicitedNum,
                                       AgreeNum = item.AgreeNum,
                                       AgreeRate = item.AgreeRate,
                                       PurchaseMethodName = item.PurchaseMethodName,
                                       SolicitedStatuz = item.SolicitedStatuz,
                                       SolicitedStatuzName = GetSolicitedStatuzName(item),
                                       FilingStatuz = item.FilingStatuz,
                                       IsFiling = item.IsFiling,
                                       IsCountyManager = 1,
                                       FilingStatuzName = GetFilingStatuzName(item)
                                   }).ToList();
                }
            }
            if (param.isFirst)
            {  //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                List<dropdownModel> dropStatuzSolicited = new List<dropdownModel>();
                var listSolicitedStatuz = EnumExtensions.EnumToList<UniformSolicitedEnum>();
                if (listSolicitedStatuz != null)
                {
                    foreach (var item in listSolicitedStatuz)
                    {
                        dropStatuzSolicited.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                List<dropdownModel> dropStatuzSeek = new List<dropdownModel>();
                var listSeekStatuz = EnumExtensions.EnumToList<UniformSeekStatuzEnum>();
                if (listSeekStatuz != null)
                {
                    foreach (var item in listSeekStatuz)
                    {
                        dropStatuzSeek.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }
                List<dropdownModel> dropStatuzFiling = new List<dropdownModel>();
                var listFilingStatuz = EnumExtensions.EnumToList<UniformFilingEnum>();
                if (listFilingStatuz != null)
                {
                    foreach (var item in listFilingStatuz)
                    {
                        dropStatuzFiling.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }
                List<dropdownModel> dropStatuzPurchaseMethod = new List<dropdownModel>();

                if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
                {
                    if (param.SourceType == 1)
                    {
                        r.data.other = new { StatuzSolicited = dropStatuzSolicited };
                    }
                    else
                    {
                        var listConfig = await ixuniformConfigManager.Find(m => m.IsDeleted == false && m.Code == ConfigEnum.Procure.ObjToInt().ToString() && (m.UnitId == user.UnitPId || m.UnitId == 0));
                        if (listConfig != null && listConfig.Count > 0)
                        {
                            if (listConfig.Where(m => m.UnitId == user.UnitPId).Count() > 0)
                            {
                                listConfig = listConfig.Where(m => m.UnitId == user.UnitPId).ToList();
                            }
                            foreach (var item in listConfig)
                            {
                                dropStatuzPurchaseMethod.Add(new dropdownModel() { value = item.DicValue, label = item.Title });
                            }
                        }
                        r.data.other = new { StatuzSeek = dropStatuzSeek, StatuzFiling = dropStatuzFiling, PurchaseMethod = dropStatuzPurchaseMethod };
                    }
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listSchool = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSchool != null)
                    {
                        foreach (var item in listSchool)
                        {
                            dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                    var listConfig = await ixuniformConfigManager.Find(m => m.IsDeleted == false && m.Code == ConfigEnum.Procure.ObjToInt().ToString() && (m.UnitId == user.UnitId || m.UnitId == 0));
                    if (listConfig != null && listConfig.Count > 0)
                    {
                        if (listConfig.Where(m => m.UnitId == user.UnitId).Count() > 0)
                        {
                            listConfig = listConfig.Where(m => m.UnitId == user.UnitId).ToList();
                        }
                        foreach (var item in listConfig)
                        {
                            dropStatuzPurchaseMethod.Add(new dropdownModel() { value = item.DicValue, label = item.Title });
                        }
                    }
                    r.data.other = new { SchoolList = dropdownSchool, StatuzSeek = dropStatuzSeek, StatuzFiling = dropStatuzFiling, PurchaseMethod = dropStatuzPurchaseMethod };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listCounty = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        if (listArea == null)
                        {
                            listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                        }
                        //获取所有区域Id
                        foreach (var item in listCounty)
                        {
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = GetAreaName(item.AreaId, item.Name, listArea) });
                        }
                    }
                    var listConfig = await ixuniformConfigManager.Find(m => m.IsDeleted == false && m.Code == ConfigEnum.Procure.ObjToInt().ToString() && m.UnitId == 0);
                    if (listConfig != null && listConfig.Count > 0)
                    {
                        foreach (var item in listConfig)
                        {
                            dropStatuzPurchaseMethod.Add(new dropdownModel() { value = item.DicValue, label = item.Title });
                        }
                    }
                    r.data.other = new { SchoolList = dropdownSchool, CountyList = dropdownCounty, StatuzSeek = dropStatuzSeek, StatuzFiling = dropStatuzFiling, PurchaseMethod = dropStatuzPurchaseMethod };
                }
            }
            r.flag = 1;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 获取区域名称
        /// </summary>
        /// <param name="countyAreaId">区县区域Id</param>
        /// <param name="name">区县单位名称，默认值</param>
        /// <param name="listArea">区域集合</param>
        /// <returns></returns>
        private string GetAreaName(long countyAreaId, string name, List<BArea> listArea)
        {
            if (listArea != null && listArea.Where(m => m.Id == countyAreaId).Count() > 0)
            {
                name = listArea.Where(m => m.Id == countyAreaId).FirstOrDefault().Name;
            }
            return name;
        }

        /// <summary>
        /// 方案选用-查询(家长展示公示结果)
        /// </summary>
        /// <param name="param">XUniformSchemeParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getparentpaged")]
        [AllowAnonymous]
        public async Task<Result<List<XUniformSchemeDto>>> GetParentPaged([FromBody] XUniformSchemeParam param)
        {
            Result<List<XUniformSchemeDto>> r = new Result<List<XUniformSchemeDto>>();
            param.UnitType = user.UnitTypeId;
            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }
            if (param.UserRoleIdList.Contains(RoleTypes.Parent.ObjToInt().ToString()))
            {
                //家长，根据学生查单位。
                param.SolicitedStatuz = UniformSolicitedEnum.Publish.ObjToInt();
                param.userId = user.UserId;
            }
            else
            {
                return baseFailed<List<XUniformSchemeDto>>("非法操作，你无权访问！");
            }
            var pg = await ixuniformschemeservices.GetParentPaged(param);
            return baseSucc(pg.data, pg.dataCount, "查询成功", pg.Other);
        }

        /// <summary>
        /// 方案选用-获取当前校服选用公示状态名称
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private string GetFilingStatuzPublicityName(XUniformSchemeDto item)
        {
            string name = "--";
            item.IsPublicity = 0;
            item.IsCountyManager = 1;
            if (item.IsCountyManager == 0)
            {
                name = "等待公示";
                if (item.SolicitedDeadline < DateTime.Now.Date)
                {
                    name = "公示结果";
                    item.IsPublicity = 1;
                }
            }
            else
            {
                name = "等待公示";
                if (item.FilingStatuz > UniformFilingEnum.SubmitNone.ObjToInt())
                {
                    name = "公示结果";
                    item.IsPublicity = 1;
                }
            }
            return name;
        }


        /// <summary>
        /// 方案选用-获取当前校服选用备案状态名称
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private string GetFilingStatuzName(XUniformSchemeDto item)
        {
            string name = "--";
            if (item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt())
            {
                //判断截止时间，是否结束。
                if (item.SolicitedDeadline < DateTime.Now.Date)
                {
                    if (item.FilingStatuz == UniformFilingEnum.SubmitNone.ObjToInt())
                    {
                        name = UniformFilingEnum.SubmitNone.GetDescription();
                    }
                    else if (item.FilingStatuz == UniformFilingEnum.Wait.ObjToInt())
                    {
                        name = UniformFilingEnum.Wait.GetDescription();
                    }
                    else if (item.FilingStatuz == UniformFilingEnum.Filinged.ObjToInt())
                    {
                        name = UniformFilingEnum.Filinged.GetDescription();
                    }
                }
            }
            return name;
        }

        /// <summary>
        /// 方案选用-获取当前校服选用状态名称
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private string GetSolicitedStatuzName(XUniformSchemeDto item)
        {
            string name = "--";
            if (item.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt())
            {
                name = "正在征求";
                //判断截止时间，是否结束。
                if (item.SolicitedDeadline < DateTime.Now.Date)
                {
                    name = "征求结束";
                }
            }
            return name;
        }

        /// <summary>
        /// 方案选用-根据Id获取对象
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbyid")]
        public async Task<Result<XUniformSchemeDto>> GetById(long id)
        {
            Result<XUniformSchemeDto> r = new Result<XUniformSchemeDto>();
            XUniformScheme m = await ixuniformschemeservices.GetById(id);
            if (m != null)
            {
                r.data.rows = mapper.Map<XUniformSchemeDto>(m);

                var list = await ibattachmentManager.Find(f => f.ObjectId == m.Id && f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.SchemeCreate.ObjToInt());
                if (list != null && list.Count > 0)
                {
                    r.data.footer = (from item in list
                                     select new
                                     {
                                         Id = item.Id,
                                         Title = item.Title,
                                         Path = item.Path,
                                         Width = item.Width,
                                         Height = item.Height,
                                         DocType = item.DocType,
                                         IsDefault = item.IsDefault,
                                         Remark = item.Remark,
                                         FileCategory = item.FileCategory,
                                         Ext = item.Ext,
                                     });
                }

            }
            r.data.other = new { ModelPath = "/Download/关于征订校服的征求家长意见书.docx" };
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }

        /// <summary>
        /// 方案选用-家长扫码填报获取详情
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsolicitedbyid")]
        [AllowAnonymous]
        public async Task<Result<XUniformSchemeOpinionDto>> GetSolicitedById(long id)
        {
            Result<XUniformSchemeOpinionDto> r = new Result<XUniformSchemeOpinionDto>();
            XUniformScheme m = await ixuniformschemeservices.GetById(id);
            if (m != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                 
                string schoolName = "";
                var entityUnit = await ipunitManager.QueryById(m.SchoolId);
                if (entityUnit != null)
                {
                    schoolName = entityUnit.Name;
                }
             
                List<dropdownModel> dropdownGrade = new List<dropdownModel>();
                List<dropdownModel> dropdownClass = new List<dropdownModel>();
                var listUnitExt = await ipschoolExtensionServices.Find(n => n.UnitId == m.SchoolId);
                if (listUnitExt != null && listUnitExt.Count > 0)
                {
                    var entityUnitExt = listUnitExt.FirstOrDefault();
                    //获取当前学校年级集合，根据学校学段获取。
                    var listDic = await ibdictionaryManager.Find(m => m.Statuz == StatuzEnum.Enable.ObjToInt());
                    if (listDic != null && listDic.Count > 0)
                    {
                        dropdownGrade = listDic.Where(m => entityUnitExt.Period.Contains(m.TypeCode)).Select(item => new dropdownModel() { value = item.DicValue, label = item.DicName }).ToList();
                        dropdownClass = listDic.Where(m => m.TypeCode == DictionaryTypeCodeEnum.Class.ObjToInt().ToString()).Select(item => new dropdownModel() { value = item.DicValue, label = item.DicName }).ToList();
                    }
                }

                List<dropdownModel> dropdownOpinion = new List<dropdownModel>();
                var listOpinionStatuz = EnumExtensions.EnumToList<StatuzOpinionEnum>();
                if (listOpinionStatuz != null)
                {
                    foreach (var item in listOpinionStatuz)
                    {
                        dropdownOpinion.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }
                List<dropdownModel> dropdownMethod = new List<dropdownModel>();
                if (m.PurchaseMethodNames != null && m.PurchaseMethodNames.Length > 0 && m.PurchaseMethodIds != null && m.PurchaseMethodIds.Length > 0)
                {
                    var names = m.PurchaseMethodNames.Split(',');
                    var ids = m.PurchaseMethodIds.Split(',');

                    for (int i = 0; i < names.Length; i++)
                    {
                        if (ids.Length >= i && ids[i] != null)
                        {
                            dropdownMethod.Add(new dropdownModel() { value = ids[i], label = names[i] });
                        }
                    }
                }
                r.data.other = new
                {
                    OpinionStatuzList = dropdownOpinion,
                    PurchaseMethodList = dropdownMethod,
                    SchoolName = schoolName,
                    // UserName = user.UserName,
                    ParentOpinion = m.ParentOpinion,
                    //  StudentList = dropdownStudent,
                    GradeList = dropdownGrade,
                    ClassList = dropdownClass,
                    ModelPath = "/Download/关于征订校服的征求家长意见书.docx",
                    Id = m.Id
                };
            }
            return r;
        }

        /// <summary>
        /// 方案选用-扫码填报获取详情预览
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getpreview")]
        [AllowAnonymous]
        public async Task<Result<XUniformSchemeOpinionDto>> GetPreview(long id)
        {
            Result<XUniformSchemeOpinionDto> r = new Result<XUniformSchemeOpinionDto>();
            XUniformScheme m = await ixuniformschemeservices.GetById(id);
            if (m != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                string schoolName = "";
                var entityUnit = await ipunitManager.QueryById(m.SchoolId);
                if (entityUnit != null)
                {
                    schoolName = entityUnit.Name;
                }

                List<dropdownModel> dropdownMethod = new List<dropdownModel>();
                if (m.PurchaseMethodNames != null && m.PurchaseMethodNames.Length > 0 && m.PurchaseMethodIds != null && m.PurchaseMethodIds.Length > 0)
                {
                    var names = m.PurchaseMethodNames.Split(',');
                    var ids = m.PurchaseMethodIds.Split(',');

                    for (int i = 0; i < names.Length; i++)
                    {
                        if (ids.Length >= i && ids[i] != null)
                        {
                            dropdownMethod.Add(new dropdownModel() { value = ids[i], label = names[i] });
                        }
                    }
                }
                r.data.other = new
                {
                    PurchaseMethodList = dropdownMethod,
                    SchoolName = schoolName,
                    ParentOpinion = m.ParentOpinion,
                    ModelPath = "/Download/关于征订校服的征求家长意见书.docx",
                    Id = m.Id
                };
            }
            return r;
        }


        /// <summary>
        /// 方案选用-家长查看公示结果
        /// </summary>
        /// <param name="id">选用方案Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getparentinfo")]
        public async Task<Result> GetParentInfo(long id)
        {
            Result r = new Result();
            XUniformScheme m = await ixuniformschemeservices.GetById(id);
            if (m != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<XUniformSchemeDto>(m);
                //获取单位名称
                string schoolname = "";
                var entityUnit = await ipunitManager.QueryById(m.SchoolId);
                if (entityUnit != null)
                {
                    schoolname = entityUnit.Name;
                }
                entityUnit.IsCountyManager = 1;
                if ((entityUnit.IsCountyManager == 0 && m.SolicitedDeadline >= DateTime.Now.Date) || (entityUnit.IsCountyManager == 1 && m.FilingStatuz == UniformFilingEnum.SubmitNone.ObjToInt()))
                {
                    r.data.rows = new
                    {
                        SchoolName = schoolname,
                        SchoolId = m.SchoolId,
                        SchemeNo = m.SchemeNo,
                        SolicitedNum = m.SolicitedNum,
                        ReleaseTime = ((DateTime)m.ReleaseTime).ToString("yyyy-MM-dd"),
                        AgreeNum = "--",
                        AgreeRate = "--",
                        PurchaseMethodName = "--",
                    };
                }
                else
                {
                    r.data.rows = new
                    {
                        SchoolName = schoolname,
                        SchoolId = m.SchoolId,
                        SchemeNo = m.SchemeNo,
                        SolicitedNum = m.SolicitedNum,
                        ReleaseTime = ((DateTime)m.ReleaseTime).ToString("yyyy-MM-dd"),
                        AgreeNum = m.AgreeNum,
                        AgreeRate = m.AgreeRate,
                        PurchaseMethodName = m.PurchaseMethodName,
                    };
                    //获取当前家长提交意见。
                    //var list = await ixuniformschemeservices.GetOpinion(id, user.UserId);
                    //if (list != null && list.Count > 0)
                    //{
                    //    r.data.footer = (from item in list
                    //                     select new
                    //                     {
                    //                         Statuz = item.Statuz,
                    //                         StatuzName = ((StatuzOpinionEnum)item.Statuz).GetDescription(),
                    //                         Opinion = item.Opinion,
                    //                         PurchaseMethodName = item.PurchaseMethodName,
                    //                         OpinionTime = ((DateTime)item.ModifyTime).ToString("yyyy-MM-dd")

                    //                     });
                    //}
                    //else
                    //{
                    //    r.data.footer = new { };
                    //}
                }
            }
            return r;
        }

        /// <summary>
        /// 方案选用-获取采购方式。
        /// </summary>
        /// <param name="id">单位Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getpurchasemethod")]
        public async Task<Result<List<dropdownModel>>> GetPurchaseMethod(long id)
        {
            Result<List<dropdownModel>> r = new Result<List<dropdownModel>>();
            List<XUniformConfig> listConfig = new List<XUniformConfig>();
            var userRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                userRoleIdList.AddRange(roleIds);
            }
            long countyId = 0;
            var entitySchool = await ipunitManager.QueryById(id);
            if (entitySchool != null)
            {
                countyId = entitySchool.PId;
            }

            if (userRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
            {
                listConfig = await ixuniformConfigManager.Find(m => m.IsDeleted == false && m.ValueNum == 1 && m.Code == ConfigEnum.Procure.ObjToInt().ToString() && (m.UnitId == 0 || m.UnitId == user.UnitPId));
                if (listConfig != null && listConfig.Where(m => m.UnitId == user.UnitPId).Count() > 0)
                {
                    listConfig = listConfig.Where(m => m.UnitId == user.UnitPId).ToList();
                }
            }
            else if (userRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
            {
                listConfig = await ixuniformConfigManager.Find(m => m.IsDeleted == false && m.ValueNum == 1 && m.Code == ConfigEnum.Procure.ObjToInt().ToString() && (m.UnitId == 0 || m.UnitId == user.UnitId));
                if (listConfig != null && listConfig.Where(m => m.UnitId == user.UnitId).Count() > 0)
                {
                    listConfig = listConfig.Where(m => m.UnitId == user.UnitId).ToList();
                }
            }
            else
            {
                listConfig = await ixuniformConfigManager.Find(m => m.IsDeleted == false && m.ValueNum == 1 && m.Code == ConfigEnum.Procure.ObjToInt().ToString() && (m.UnitId == 0 || m.UnitId == countyId));
                if (countyId > 0 && listConfig != null && listConfig.Where(m => m.UnitId == countyId).Count() > 0)
                {
                    listConfig = listConfig.Where(m => m.UnitId == countyId).ToList();
                }

            }
            if (listConfig != null && listConfig.Count > 0)
            {
                var dropdown = new List<dropdownModel>();
                foreach (var item in listConfig)
                {
                    dropdown.Add(new dropdownModel() { value = item.DicValue, label = item.Title });
                }
                return baseSucc(dropdown, listConfig.Count, "查询成功");
            }
            return baseFailed<List<dropdownModel>>("未查询到数据");
        }

        #endregion

        #region 查询意见

        /// <summary>
        /// 方案选用-查询意见详情列表
        /// </summary>
        /// <param name="param">XUniformSchemeParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getopinionpaged")]
        public async Task<Result<List<XUniformSchemeOpinionDto>>> GetOpinionPaged([FromBody] XUniformSchemeOpinionParam param)
        {
            Result<List<XUniformSchemeOpinionDto>> r = new Result<List<XUniformSchemeOpinionDto>>();
            if (param == null || param.UniformSchemeId <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作，请从页面点击操作。";
                return r;
            }
            var pg = await ixuniformschemeservices.GetOpinionPaged(param);
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                r.data.rows = (from item in pg.data
                               select new XUniformSchemeOpinionDto()
                               {
                                   ParentName = item.ParentName,
                                   Statuz = item.Statuz,
                                   PurchaseMethodName = item.PurchaseMethodName,
                                   StudentName = item.StudentName,
                                   GradeName = item.GradeName,
                                   ClassName = item.ClassName,
                                   Mobile = item.Mobile,
                                   Opinion = item.Opinion
                               }).ToList();
            }
            r.flag = 1;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                List<dropdownModel> dropClass = new List<dropdownModel>();
                var listClass = await ibdictionaryManager.Find(m => m.TypeCode == "300000");
                if (listClass != null)
                {
                    foreach (var item in listClass)
                    {
                        dropClass.Add(new dropdownModel() { value = item.DicValue, label = item.DicName });
                    }
                }
                List<dropdownModel> dropGrade = new List<dropdownModel>();
                var listGrade = await ibdictionaryManager.GetGradeInfo();
                if (listGrade != null)
                {
                    foreach (var item in listGrade)
                    {
                        dropGrade.Add(new dropdownModel() { value = item.DicValue, label = item.DicName });
                    }
                }
                List<dropdownModel> dropStatuz = new List<dropdownModel>();
                var listOpinionStatuz = EnumExtensions.EnumToList<StatuzOpinionEnum>();
                if (listOpinionStatuz != null)
                {
                    foreach (var item in listOpinionStatuz)
                    {
                        dropStatuz.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }

                List<dropdownModel> dropMethod = new List<dropdownModel>();
                if (param.PurchaseMethodList != null)
                {
                    dropMethod = param.PurchaseMethodList;
                }
                //添加班级。班级由于选用方案没有选择方案，班级可能一个，可能多个，下拉只能去当前单位所有班级，所以建议用模糊查询。（这里暂时不加）
                r.data.other = new { OpinionStatuzList = dropStatuz, PurchaseMethod = dropMethod, GradeList = dropGrade, ClassList = dropClass, MethodRate = param.PurchaseMethodRate, SchemeNo = param.SchemeNo };
            }
            else
            {
                r.data.other = new { MethodRate = param.PurchaseMethodRate, SchemeNo = param.SchemeNo };
            }
            return r;
        }

        /// <summary>
        /// 方案选用-导出意见详情列表
        /// </summary>
        /// <param name="param">XUniformSchemeParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportopinion")]
        public async Task<Result<string>> ExportOpinion([FromBody] XUniformSchemeOpinionParam param)
        {
            Result<string> r = new Result<string>();
            if (param == null || param.UniformSchemeId <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作，请从页面点击操作。";
                return r;
            }
            var entity = await ixuniformschemeservices.GetById(param.UniformSchemeId);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "当前校服选用方案已不存在，请刷新重新操作。";
                return r;
            }
            var entityUnit = await ipunitManager.QueryById(entity.SchoolId);
            if (entityUnit == null)
            {
                r.flag = 0;
                r.msg = "当前校服选用方案已不存在，请刷新重新操作。";
                return r;
            }
            string exl_title = string.Format("{0}_{1}_{2}", entityUnit.Name, entity.SchemeNo, "校服选用征求意见详情");
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            var pg = await ixuniformschemeservices.GetOpinionPaged(param);
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                pg.data.ForEach(m =>
                {
                    m.StatuzName = (m.Statuz == 1 ? "同意" : "不同意");
                });
                List<string> columns = new List<string>() { "GradeName", "ClassName", "StudentName", "Mobile", "StatuzName", "PurchaseMethodName", "Opinion" };
                string file = new ExcelHelper<XUniformSchemeOpinionDto>().ExportToExcel(env.WebRootPath, "校服选用征求意见详情.xls", exl_title, pg.data.ToList(), columns.ToArray());
                //r.data.rows = Path.Combine(ApplicationConfig.CurrentServerUrl, file);
                r.flag = 1;
                r.msg = "导出成功";
                r.data.headers = "校服选用征求意见详情";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }

        #endregion

        #region 提交

        /// <summary>
        /// 方案选用-保存（添加）OptStatuz = 0：添加
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveadd")]
        public async Task<Result<XUniformSchemeDto>> Add([FromBody] XUniformSchemeDto request)
        {
            request.CreateId = user.UserId;
            request.CreateBy = user.UserName;
            request.SchoolId = user.UnitId;
            request.CountyId = user.UnitPId;
            request.OptStatuz = 0;
            request.Id = 0;
            var restData = await ixuniformschemeservices.SavePublish(request);
            if (restData.flag == 1)
            {
                return baseSucc(mapper.Map<XUniformSchemeDto>(restData.data.rows), 0, restData.msg);
            }
            else
            {
                return baseFailed<XUniformSchemeDto>(restData.msg);
            }
        }

        /// <summary>
        /// 方案选用-保存（修改）OptStatuz = 0：修改 保存
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveedit")]
        public async Task<Result<XUniformSchemeDto>> Edit([FromBody] XUniformSchemeDto request)
        {
            request.CreateId = user.UserId;
            request.CreateBy = user.UserName;
            request.SchoolId = user.UnitId;
            request.CountyId = user.UnitPId;
            request.OptStatuz = 0;
            if (request.Id <= 0)
            {
                return Result<XUniformSchemeDto>.Fail("非法操作，请从页面点击操作。");
            }
            var restData = await ixuniformschemeservices.SavePublish(request);
            if (restData.flag == 1)
            {
                return baseSucc(mapper.Map<XUniformSchemeDto>(restData.data.rows), 0, restData.msg);
            }
            else
            {
                return baseFailed<XUniformSchemeDto>(restData.msg);
            }
        }

        /// <summary>
        /// 方案选用-保存（发布）OptStatuz = 1：发布
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("savepublish")]
        public async Task<Result<XUniformSchemeDto>> Post([FromBody] XUniformSchemeDto request)
        {
            Result r = new Result();
            request.CreateId = user.UserId;
            request.CreateBy = user.UserName;
            request.SchoolId = user.UnitId;
            request.CountyId = user.UnitPId;
            request.OptStatuz = 1;
            var restData = await ixuniformschemeservices.SavePublish(request);
            if (restData.flag == 1)
            {
                return baseSucc(mapper.Map<XUniformSchemeDto>(restData.data.rows), 0, restData.msg);
            }
            else
            {
                return baseFailed<XUniformSchemeDto>(restData.msg);
            }
        }

        /// <summary>
        /// 方案选用-根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delbyid")]
        public async Task<Result> DeleteById(long id)
        {
            Result r = new Result();
            var model = new XUniformSchemeDto();
            model.Id = id;
            model.SchoolId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformschemeservices.UpdateDelete(model);
            return r;
        }

        /// <summary>
        /// 方案选用-根据Id删除附件数据
        /// </summary>
        /// <param name="id">校服选用表Id</param>
        /// <param name="attid">附件Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("delattachmentbyid")]
        public async Task<Result> DeleteAttachmentById(long id, long attid)
        {
            Result r = new Result();
            var model = new XUniformSchemeDto();
            model.Id = id;
            model.AttachmentId = attid;
            model.SchoolId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformschemeservices.UpdateAttachmentDelete(model);
            return r;
        }

        /// <summary>
        /// 方案选用-发布撤销
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpPut]
        [Route("publishcancel")]
        public async Task<Result> PublishCancel(long id)
        {
            Result r = new Result();
            var model = new XUniformSchemeDto();
            model.Id = id;
            model.SchoolId = user.UnitId;
            r = await ixuniformschemeservices.PublishCancel(model);
            return r;
        }
        /// <summary>
        /// 方案选用-复制
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpPut]
        [Route("savecopy")]
        public async Task<Result<long>> Copy(long id)
        {
            return await ixuniformschemeservices.SaveCopy(id, user.UserId);
        }

        #endregion

        #region 提交意见    

        /// <summary>
        /// 方案选用-保存家长意见（添加、修改）
        /// </summary>
        /// <param name="request"></param>
        /// <remarks>
        /// 2025-09-04 by  lss 不登录，
        /// 1：直接存在班级学生姓名、手机号，不去找学生匹配
        /// 2：是否还需要修改，什么情况下修改
        /// </remarks>
        /// <returns></returns>
        [HttpPost]
        [Route("saveopinion")]
        [AllowAnonymous]
        public async Task<Result> PostOpinion([FromBody] XUniformSchemeOpinionDto request)
        {
            //var ddd = Hyun.Core.Common.Helper.Base64.DecodeBase64("MzIxMDg0MTk4MjEyMDIyMzQ2");
            return await ixuniformschemeservices.SaveOpinion(request);
        }

        #endregion

        #region 提交备案


        /// <summary>
        /// 方案选用-提交备案
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpPut]
        [Route("savefiling")]
        public async Task<Result> FilingSubmit(long id)
        {
            Result r = new Result();
            var model = new XUniformSchemeDto();
            model.Id = id;
            model.SchoolId = user.UnitId;
            r = await ixuniformschemeservices.FilingSubmit(model);
            return r;
        }

        /// <summary>
        /// 方案选用-区县审核
        /// </summary>
        /// <param name="id">方案标识</param>
        /// <param name="statuz">状态 1：通过  2：退回</param>
        /// <param name="explanation">说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("confirmfiling")]
        public async Task<Result> FilingConfirm(long id, int statuz, string explanation = "")
        {
            Result r = new Result();
            var model = new XUniformSchemeDto();
            model.Id = id;
            if (statuz == 1)
            {
                model.FilingStatuz = UniformFilingEnum.Filinged.ObjToInt();
            }
            else
            {
                model.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
            }
            model.FilingExplanation = explanation;
            model.CountyId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformschemeservices.FilingConfirm(model);
            return r;
        }

        /// <summary>
        /// 方案选用-区县退回
        /// </summary>
        /// <param name="id">方案标识</param>
        /// <param name="explanation">说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("filingbackout")]
        public async Task<Result> FilingBackout(long id, string explanation = "")
        {
            Result r = new Result();
            var model = new XUniformSchemeDto();
            model.Id = id;
            model.FilingExplanation = explanation;
            model.SchoolId = user.UnitId;
            model.CountyId = user.UnitPId;
            r = await ixuniformschemeservices.FilingBackout(model);
            return r;
        }
        #endregion
    }
}
