﻿///using Hyun.Core.Model.Models.Uniform;
namespace Hyun.Core.IServices.Uniform
{

    ///<summary>
    ///XUniformShow接口方法
    ///</summary>
    public interface IXUniformShelfServices : IBaseServices<XUniformShelf>
    {
        /// <summary>
        /// 校服待审核列表-校服审核
        /// </summary>
        /// <param name="model">XUniformShow对象</param>
        /// <returns></returns>
        Task<Result> Audit(XUniformShelfDto model);

        /// <summary>
        /// 已提交校服列表-根据Id删除数据【假删除】
        /// </summary>
        /// <param name="model">实体信息</param>
        /// <returns></returns>
        Task<Result> FakeDeleteById(XUniformShelfDto model);
        /// <summary>
        /// 已审核校服列表-审核撤销
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> Revoked(XUniformShelfDto model);
        /// <summary>
        /// 校服管理-已提交审核列表-设置状态启用禁用
        /// </summary>
        /// <param name="model">XUniformShow对象</param>
        /// <returns></returns>
        Task<Result> SetStatuz(XUniformShelfDto model);

        Task<Result> SetShowStatuz(XUniformShelfDto model);
        /// <summary>
        /// 已提交校服列表-设置排序值
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> SetSortById(XUniformShelfDto model);
        /// <summary>
        /// 已上架校服-根据Id获取详情
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<XUniformShelf> GetById(long id);

        /// <summary>
        /// 已上架校服-根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<XUniformShelf>> Find(Expression<Func<XUniformShelf, bool>> expression);

        /// <summary>
        /// 已上架校服-获取校已提交校服列表
        /// </summary>
        /// <param name="param">XUniformShowParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<XUniformShelfDto>> GetPaged(XUniformShelfParam param);
        /// <summary>
        /// 校服管理-校服审核待审核、已审核列表（单位）
        /// </summary>
        /// <param name="param">XUniformShowParam实体参数</param>
        /// <returns></returns>
        Task<PageModel<XUniformShelfDto>> GetAuditPaged(XUniformShelfParam param);

        /// <summary>
        /// 根据单位数量和每个单位需要获取的数量获取校服列表
        /// </summary>
        /// <param name="schoolnum">单位数量</param>
        /// <param name="num">每个单位需要查询的数量</param>
        /// <returns></returns>
        Task<PageModel<XUniformShelfDto>> GetSchoolNumListNum(int schoolnum, int num);
        /// <summary>
        /// 校服展示列表-根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        Task<PageModel<XUniformShelfDto>> GetPageList(XUniformShelfParam param);
    }
}

