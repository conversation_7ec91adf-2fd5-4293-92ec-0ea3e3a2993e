﻿using Hyun.Core.Common.DB;
using Hyun.Core.IServices.Uniform;

namespace Hyun.Core.Services.Uniform
{

    ///<summary>
    ///XUniformScheme方法
    ///</summary>
    public class XUniformSchemeServices : BaseServices<XUniformScheme>, IXUniformSchemeServices
    {
        private readonly IXUniformPurchaseServices uniformPurchaseManager;
        public XUniformSchemeServices(IXUniformPurchaseServices _uniformPurchaseManager)
        {
            uniformPurchaseManager = _uniformPurchaseManager;
        }


        #region 查询数据
        /// <summary>
        /// 方案选用-根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        public async Task<XUniformScheme> GetById(long id)
        {
            return await base.QueryById(id);
        }


        /// <summary>
        /// 方案选用-根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        public async Task<List<XUniformScheme>> Find(Expression<Func<XUniformScheme, bool>> expression)
        {
            return await base.Query(expression);
        }

        /// <summary>
        /// 方案选用-分页选用列表查询方法
        /// </summary>
        /// <param name="param">XUniformSchemeParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <remarks>
        /// </remarks>
        /// <returns></returns>
        public async Task<PageModel<XUniformSchemeDto>> GetPaged(XUniformSchemeParam param)
        {
            PageModel<XUniformSchemeDto> pg = new PageModel<XUniformSchemeDto>();
            var expression = ListFilter(param);
            string orderByFields = " CreateTime DESC ";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }

            RefAsync<int> total = 0;
            var listExpression = this.Db.Queryable<XUniformScheme>()
                  .InnerJoin<PUnit>((scheme, school) => scheme.SchoolId == school.Id)
                  .InnerJoinIF<PUnit>(param.UnitType == UnitTypes.City.ObjToInt(), (scheme, school, county) => school.PId == county.Id)
                  .WhereIF(param.SchoolId > 0, (scheme, school, county) => scheme.SchoolId == param.SchoolId)
                  .WhereIF(param.CountyId > 0 && param.UnitType != UnitTypes.School.ObjToInt(), (scheme, school, county) => school.PId == param.CountyId)
                  .WhereIF(param.CityId > 0 && param.UnitType == UnitTypes.City.ObjToInt(), (scheme, school, county) => county.PId == param.CityId)
                  .WhereIF(param.SchemeYear != -10000, (scheme, school, county) => scheme.SchemeYear == param.SchemeYear)
                  .WhereIF(param.SolicitedStatuz != -10000, (scheme, school, county) => scheme.SolicitedStatuz == param.SolicitedStatuz)
                  .WhereIF(param.SeekStatuz == 1, (scheme, school, county) => scheme.SolicitedDeadline >= DateTime.Now.Date)
                  .WhereIF(param.SeekStatuz == 2, (scheme, school, county) => scheme.SolicitedDeadline < DateTime.Now.Date)
                  .WhereIF(param.PurchaseMethod != -10000, (scheme, school, county) => scheme.PurchaseMethod == param.PurchaseMethod)
                  .WhereIF(param.StatuzFiling != -10000, (scheme, school, county) => scheme.FilingStatuz == param.StatuzFiling)
                  .WhereIF(param.Name != null && param.Name.Length > 0, (scheme, school, county) => scheme.SchemeNo.Contains(param.Name) || scheme.PurchaseMethodName.Contains(param.Name));
           var list=new List<XUniformSchemeDto>();
            if (param.UnitType == UnitTypes.School.ObjToInt())
            {
                list = await listExpression.Select((scheme) => new XUniformSchemeDto()
                {
                    Id = scheme.Id,
                    IsDeleted = scheme.IsDeleted,
                    CreateTime = scheme.CreateTime,
                    ModifyTime = scheme.ModifyTime,
                    CreateBy = scheme.CreateBy,
                    ModifyBy = scheme.ModifyBy,
                    SchemeYear = scheme.SchemeYear,
                    SchemeNo = scheme.SchemeNo,
                    SolicitedNum = scheme.SolicitedNum,
                    SolicitedDeadline = scheme.SolicitedDeadline,
                    ReleaseTime = scheme.ReleaseTime,
                    SolicitedStatuz = scheme.SolicitedStatuz,
                    AgreeNum = scheme.AgreeNum,
                    AgreeRate = scheme.AgreeRate,
                    PurchaseMethod = scheme.PurchaseMethod,
                    PurchaseMethodName = scheme.PurchaseMethodName,
                    FilingStatuz = scheme.FilingStatuz,
                    FilingExplanation = scheme.FilingExplanation,
                    ParentOpinion = scheme.ParentOpinion,
                    Memo = scheme.Memo
                }).MergeTable()
                  .OrderBy(orderByFields)
                  .ToPageListAsync(param.pageIndex, param.pageSize, total);
            }
            else if (param.UnitType == UnitTypes.Couty.ObjToInt() || param.UnitType == UnitTypes.Parent.ObjToInt())
            {
                list = await listExpression.Select((scheme, school, county) => new XUniformSchemeDto()
                {
                    Id = scheme.Id,
                    IsDeleted = scheme.IsDeleted,
                    CreateTime = scheme.CreateTime,
                    ModifyTime = scheme.ModifyTime,
                    CreateBy = scheme.CreateBy,
                    ModifyBy = scheme.ModifyBy,
                    SchemeYear = scheme.SchemeYear,
                    SchemeNo = scheme.SchemeNo,
                    SolicitedNum = scheme.SolicitedNum,
                    SolicitedDeadline = scheme.SolicitedDeadline,
                    ReleaseTime = scheme.ReleaseTime,
                    SolicitedStatuz = scheme.SolicitedStatuz,
                    AgreeNum = scheme.AgreeNum,
                    AgreeRate = scheme.AgreeRate,
                    PurchaseMethod = scheme.PurchaseMethod,
                    PurchaseMethodName = scheme.PurchaseMethodName,
                    FilingStatuz = scheme.FilingStatuz,
                    FilingExplanation = scheme.FilingExplanation,
                    ParentOpinion = scheme.ParentOpinion,
                    Memo = scheme.Memo,
                    PurchaseMethodIds = scheme.PurchaseMethodIds,
                    PurchaseMethodNames = scheme.PurchaseMethodNames,
                    SchoolName = school.Name,
                    IsCountyManager = 1
                }).MergeTable()
                  .OrderBy(orderByFields)
                  .ToPageListAsync(param.pageIndex, param.pageSize, total);
            }
            else if (param.UnitType == UnitTypes.City.ObjToInt())
            {
                list = await listExpression.Select((scheme, school, county) => new XUniformSchemeDto()
                {
                    Id = scheme.Id,
                    IsDeleted = scheme.IsDeleted,
                    CreateTime = scheme.CreateTime,
                    ModifyTime = scheme.ModifyTime,
                    CreateBy = scheme.CreateBy,
                    ModifyBy = scheme.ModifyBy,
                    SchemeYear = scheme.SchemeYear,
                    SchemeNo = scheme.SchemeNo,
                    SolicitedNum = scheme.SolicitedNum,
                    SolicitedDeadline = scheme.SolicitedDeadline,
                    ReleaseTime = scheme.ReleaseTime,
                    SolicitedStatuz = scheme.SolicitedStatuz,
                    AgreeNum = scheme.AgreeNum,
                    AgreeRate = scheme.AgreeRate,
                    PurchaseMethod = scheme.PurchaseMethod,
                    PurchaseMethodName = scheme.PurchaseMethodName,
                    FilingStatuz = scheme.FilingStatuz,
                    FilingExplanation = scheme.FilingExplanation,
                    ParentOpinion = scheme.ParentOpinion,
                    Memo = scheme.Memo,
                    PurchaseMethodIds = scheme.PurchaseMethodIds,
                    PurchaseMethodNames = scheme.PurchaseMethodNames,
                    SchoolName = school.Name,
                    CountyName = county.Name,
                    CountyAreaId=county.AreaId
                }).MergeTable()
                  .OrderBy(orderByFields)
                  .ToPageListAsync(param.pageIndex, param.pageSize, total);
            }
            param.totalCount = total;
            pg.data = list;
            pg.dataCount = total;
            return pg;
        }

        /// <summary>
        /// 方案选用-分页选用列表查询方法(家长)
        /// </summary>
        /// <param name="param">XUniformSchemeParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <remarks>
        ///  注：
        ///  1. 如果是家长，则查询公示的，过了截止日期的。
        /// </remarks>
        /// <returns></returns>
        public async Task<PageModel<XUniformSchemeDto>> GetParentPaged(XUniformSchemeParam param)
        {
            PageModel<XUniformSchemeDto> pg = new PageModel<XUniformSchemeDto>();
            var expression = ListFilter(param);
            string orderByFields = " CreateTime DESC ";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            //先根据家长Id获取学生填报列表。
            //var listOpinion = await this.Db.Queryable<XUniformSchemeOpinion>()
            //.Where((opinion) => opinion.CreateId == param.userId && opinion.IsDeleted == false).ToListAsync();

            //根据家长表获取学生,班级，并且未毕业。并返回学校列表。
            var listSchool = await this.Db.Queryable<PParentStudent>()
                .InnerJoin<PStudent>((p,s)=>p.StudentId == s.Id)
                .InnerJoin<PClassInfo>((p,s,c)=>s.UniformClassId==c.Id)
                .InnerJoin<PUnit>((p, s, c,u) => s.SchoolId == u.Id)
                .Where((p, s, c, u) =>c.IsGraduate == 0 && p.CreateId == param.userId)
                .GroupBy((p, s, c, u) => new { u.Id, u.Name })
                .Select((p, s, c, u) => new PUnitDto() { Id = u.Id, Name = u.Name })
                .ToListAsync();

            pg.Other = listSchool;

            if (param.isFirst && param.SchoolId <= 0 && listSchool != null && listSchool.Count > 1)
            {
                //需求：如果存在多个学校，学校就会有下拉，根据选择展示。
                //未选择时给一个默认值。
                param.SchoolId = listSchool.FirstOrDefault().Id;
            }

            if (listSchool != null && listSchool.Count > 0)
            {
                var schoolIdList = listSchool.Select(m => m.Id);
                RefAsync<int> total = 0;
                var list = await this.Db.Queryable<XUniformScheme>()
                    .InnerJoin<PUnit>((scheme, school) => scheme.SchoolId == school.Id)
                    .Where((scheme, school) => schoolIdList.Contains(scheme.SchoolId))
                    .WhereIF(param.SchoolId > 0, (scheme, school) => scheme.SchoolId == param.SchoolId)
                    .WhereIF(param.SchemeYear != -10000, (scheme, school) => scheme.SchemeYear == param.SchemeYear)
                    .WhereIF(param.SolicitedStatuz != -10000, (scheme, school) => scheme.SolicitedStatuz == param.SolicitedStatuz)
                    .WhereIF(param.SeekStatuz == 1, (scheme, school) => scheme.SolicitedDeadline >= DateTime.Now.Date)
                    .WhereIF(param.SeekStatuz == 2, (scheme, school) => scheme.SolicitedDeadline < DateTime.Now.Date)
                    .WhereIF(param.PurchaseMethod != -10000, (scheme, school) => scheme.PurchaseMethod == param.PurchaseMethod)
                    .WhereIF(param.StatuzFiling != -10000, (scheme, school) => scheme.FilingStatuz == param.StatuzFiling)
                    .WhereIF(param.Name != null && param.Name.Length > 0, (scheme, school) => scheme.SchemeNo.Contains(param.Name) || scheme.PurchaseMethodName.Contains(param.Name))
                    .Select((scheme, school) => new XUniformSchemeDto()
                    {
                        Id = scheme.Id,
                        IsDeleted = scheme.IsDeleted,
                        CreateTime = scheme.CreateTime,
                        ModifyTime = scheme.ModifyTime,
                        CreateBy = scheme.CreateBy,
                        ModifyBy = scheme.ModifyBy,
                        SchemeYear = scheme.SchemeYear,
                        SchemeNo = scheme.SchemeNo,
                        SolicitedNum = scheme.SolicitedNum,
                        SolicitedDeadline = scheme.SolicitedDeadline,
                        ReleaseTime = scheme.ReleaseTime,
                        SolicitedStatuz = scheme.SolicitedStatuz,
                        AgreeNum = scheme.AgreeNum,
                        AgreeRate = scheme.AgreeRate,
                        PurchaseMethod = scheme.PurchaseMethod,
                        PurchaseMethodName = scheme.PurchaseMethodName,
                        FilingStatuz = scheme.FilingStatuz,
                        FilingExplanation = scheme.FilingExplanation,
                        ParentOpinion = scheme.ParentOpinion,
                        Memo = scheme.Memo,
                        PurchaseMethodIds = scheme.PurchaseMethodIds,
                        PurchaseMethodNames = scheme.PurchaseMethodNames,
                        SchoolName = school.Name,
                        IsCountyManager=1
                    }).MergeTable()
                    .OrderBy(orderByFields)
                    .ToPageListAsync(param.pageIndex, param.pageSize, total);

                param.totalCount = total;
                pg.data = list;
                pg.dataCount = total;
            }
            return pg;
        }

        #endregion

        #region 查询意见详情


        /// <summary>
        /// 方案选用-分页方案意见详情查询列表
        /// </summary>
        /// <param name="param">XUniformSchemeOpinionParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <remarks>
        ///  注：
        ///  1. 如果是家长，则查询公示的，过了截止日期的。
        /// </remarks>
        /// <returns></returns>
        public async Task<PageModel<XUniformSchemeOpinionDto>> GetOpinionPaged(XUniformSchemeOpinionParam param)
        {
            PageModel<XUniformSchemeOpinionDto> pg = new PageModel<XUniformSchemeOpinionDto>();

            string orderByFields = " Id asc ";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }

            var list = this.Db.Queryable<XUniformSchemeOpinion>()
                  .Where((opinion) => opinion.UniformSchemeId == param.UniformSchemeId)
                  .WhereIF(param.Statuz != -10000, (opinion) => opinion.Statuz == param.Statuz)
                  .WhereIF(param.PurchaseMethod != -10000, (opinion) => opinion.PurchaseMethod == param.PurchaseMethod)
                  .WhereIF(param.GradeId != -10000, (opinion) => opinion.GradeId == param.GradeId)
                  .WhereIF(param.UniformClassId != -10000, (opinion) => opinion.UniformClassId == param.UniformClassId)
                  .WhereIF(param.UniformClassId != -10000, (opinion) => opinion.UniformClassId == param.UniformClassId)
                  .WhereIF(param.PurchaseMethod != -10000, (opinion) => opinion.PurchaseMethod == param.PurchaseMethod)
                  .WhereIF(param.ClassName!=null && param.ClassName.Trim().Length>0, (opinion) => opinion.ClassName == param.ClassName)
                  .WhereIF(param.Name != null && param.Name.Length > 0, (opinion) => opinion.ClassName.Contains(param.Name) || opinion.StudentName.Contains(param.Name))
                  .Select((opinion) => new XUniformSchemeOpinionDto()
                  {
                      Id = opinion.Id,
                      IsDeleted = opinion.IsDeleted,
                      CreateTime = opinion.CreateTime,
                      ModifyTime = opinion.ModifyTime,
                      CreateBy = opinion.CreateBy,
                      ModifyBy = opinion.ModifyBy,
                      ParentName = opinion.ParentName,
                      Statuz = opinion.Statuz,
                      PurchaseMethodName = opinion.PurchaseMethodName,
                      StudentName = opinion.StudentName,
                      GradeName = opinion.GradeName,
                      ClassName = opinion.ClassName,
                      Mobile = opinion.Mobile,
                      Opinion = opinion.Opinion

                  })
                  .OrderBy(orderByFields)
                  .ToList();
            var entity = await base.QueryById(param.UniformSchemeId);
            if (entity != null && list != null)
            {
                param.totalCount = list.Count;
                if (param.pageIndex <= 0)
                {
                    param.pageIndex = 1;
                }
                pg.data = list.Skip((param.pageIndex - 1) * param.pageSize).Take(param.pageSize).ToList();
                pg.dataCount = param.totalCount;
                //三种采购方式。
                var listTemp = list.Where(m => m.Statuz == 1).GroupBy(m => m.PurchaseMethodName);
                if (!string.IsNullOrEmpty(entity.PurchaseMethodNames))
                {
                    var methodArr = entity.PurchaseMethodNames.Split(",");
                    var finalResultCorrected = methodArr.GroupJoin(list, method => method, item => item.PurchaseMethodName,
                   (method, items) => new
                   {
                       Method = method,
                       Count = items.Where(n => n.Statuz == 1).Any() ? items.Where(n => n.Statuz == 1).Count() : 0,// 如果items不为空（即存在匹配项），则计数；否则为0  
                       Rate = GetMethodRate(items.Where(n => n.Statuz == 1).Any() ? items.Where(n => n.Statuz == 1).Count() : 0, (int)entity.AgreeNum)
                   });
                    param.PurchaseMethodRate = finalResultCorrected;
                }
            

            }
            if (entity!=null)
            {
                param.SchemeNo = entity.SchemeNo;
                if (!string.IsNullOrEmpty(entity.PurchaseMethodNames))
                {
                    var methodArr = entity.PurchaseMethodNames.Split(",");
                    //输出采购方式
                    param.PurchaseMethodList = new List<dropdownModel>();
                    var purchasemethodidlist = entity.PurchaseMethodIds.Split(",");
                    for (int i = 0; i < methodArr.Length; i++)
                    {
                        if (purchasemethodidlist[i] != null)
                        {
                            param.PurchaseMethodList.Add(new dropdownModel() { value = purchasemethodidlist[i], label = methodArr[i] });
                        }
                    }
                }
            }
            
            return pg;
        }
        /// <summary>
        /// 计算采购方式占比
        /// </summary>
        /// <param name="successfulCount"></param>
        /// <param name="totalCount"></param>
        /// <returns></returns>
        private double GetMethodRate(int successfulCount, int totalCount)
        {
            // 如果总数为0，则直接返回0.00%，避免除以0的错误  
            if (totalCount == 0)
            {
                return 0.00;
            }

            // 计算百分比并保留两位小数  
            return Math.Round((successfulCount / (totalCount * 1.00)) * 100, 2);
        }
        /// <summary>
        /// 方案选用-获取本次方案家长填写意见列表
        /// </summary>
        /// <param name="schemeid"></param>
        /// <param name="parentid"></param>
        /// <returns></returns>
        public async Task<List<XUniformSchemeOpinion>> GetOpinion(long schemeid, long parentid)
        {
            return await this.Db.Queryable<XUniformSchemeOpinion>()
            .Where((opinion) => opinion.UniformSchemeId == schemeid && opinion.CreateId == parentid && opinion.IsDeleted == false)
            .ToListAsync();
        }

        #endregion
         
        #region 提交保存
        /// <summary>
        /// 方案选用-删除附件
        /// </summary>
        /// <param name="model">撤销信息</param>
        /// <returns></returns>
        public async Task<Result> UpdateAttachmentDelete(XUniformSchemeDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前选用方案已不存在，请刷新重新操作。";
                return r;
            }
            var listAddAttachment = new List<BAttachment>();
            var entityAttachment = await this.Db.Queryable<BAttachment>()
            .Where((att) => att.Id == model.AttachmentId && att.ObjectId == entity.Id && att.ModuleType == ModuleTypeEnum.SchemeCreate.ObjToInt() && att.IsDeleted == false).FirstAsync();

            if (entityAttachment == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前附件已不存在，请刷新重新操作。";
                return r;
            }
            var resultNo = await this.Db.Updateable<BAttachment>().SetColumns((att) => new BAttachment()
            {
                IsDeleted = true,
                ModifyTime = DateTime.Now,
                ModifyBy = model.CreateBy,
                ModifyId = model.CreateId
            })
            .Where((att) => att.Id == entityAttachment.Id).ExecuteCommandAsync();
            if (resultNo >0)
            {
                r.flag = 1;
                r.msg = "删除成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "删除失败,请刷新重试，如无法解决请联系客服协助处理！";
            }    
            return r;
        }

        /// <summary>
        /// 方案选用-删除选用方案（单位）
        /// </summary>
        /// <param name="model">撤销信息</param>
        /// <returns></returns>
        public async Task<Result> UpdateDelete(XUniformSchemeDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            if (entity.CreateId != model.CreateId)
            {
                r.flag = 0;
                r.msg = "执行失败，非本人添加方案禁止删除。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.SubmitNone.ObjToInt().ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前状态不禁止删除 。";
                return r;
            }
            //发布后，禁止删除
            if (entity.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，已发布禁止删除。";
                return r;
            }
            //entity.IsDeleted = true;
            await this.Db.Updateable<XUniformScheme>().SetColumns((scheme) => new XUniformScheme()
            {
                IsDeleted = true,
                ModifyTime = DateTime.Now,
                ModifyBy = model.CreateBy,
                ModifyId = model.CreateId
            })
            .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();
            r.flag = 1;
            r.msg = "删除成功！";
            //if (await base.Update(entity))
            //{
            //    r.flag = 1;
            //    r.msg = "删除成功！";
            //}
            //else
            //{
            //    r.flag = 0;
            //    r.msg = "删除失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            //}
            return r;
        }

        /// <summary>
        /// 方案选用-已发布撤销
        /// </summary>
        /// <param name="model">撤销信息</param>
        /// <returns></returns>
        public async Task<Result> PublishCancel(XUniformSchemeDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.SubmitNone.ObjToInt().ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前状态不禁止撤销 。";
                return r;
            }
            //发布后，禁止修改年度
            if (entity.SolicitedStatuz != UniformSolicitedEnum.Publish.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据未发布。";
                return r;
            }
            //如果存在填写了的征用人，则禁止撤销发布。
            if (entity.ResponseNum > 0)
            {
                r.flag = 0;
                r.msg = "执行失败，当家长已填报意见后，禁止撤销。";
                return r;
            }
            entity.SolicitedStatuz = UniformSolicitedEnum.Cretae.ObjToInt();
            entity.SchemeNo = "";//清空批次
            var resultNo = await this.Db.Updateable<XUniformScheme>().SetColumns((scheme) => new XUniformScheme()
            {
                SolicitedStatuz = UniformSolicitedEnum.Cretae.ObjToInt(),
                SchemeNo = "",
                ModifyTime = DateTime.Now,
                ModifyBy = model.CreateBy,
                ModifyId = model.CreateId
            })
           .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();
            r.flag = 1;
            r.msg = "撤销成功！";
            if (resultNo > 0)
            {
                r.flag = 1;
                r.msg = "撤销成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "撤销失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            }
            return r;
        }

        /// <summary>
        /// 方案选用-校服选用方案（保存、发布）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<Result<XUniformScheme>> SavePublish(XUniformSchemeDto model)
        {
            Result<XUniformScheme> r = new Result<XUniformScheme>();
            XUniformScheme entity = null;
            if (model == null)
            {
                r.flag = 0;
                r.msg = "执行失败，请填写数据后再提交。";
                return r;
            }
            //if (model.SchemeYear != DateTime.Now.Year && model.SchemeYear != (DateTime.Now.Year - 1))
            //{
            //    r.flag = 0;
            //    r.msg = "执行失败，采购年度只能是当前年或者上一年。";
            //    return r;
            //}
            if (model.OptStatuz == 1)
            {
                if (model.SchemeYear <= 0)
                {
                    r.flag = 0;
                    r.msg = "发布失败，请填写年度。";
                    return r;
                }
                if (model.SolicitedNum <= 0)
                {
                    r.flag = 0;
                    r.msg = "发布失败，请填写应征求人数。";
                    return r;
                }
                if (model.SolicitedDeadline == null)
                {
                    r.flag = 0;
                    r.msg = "发布失败，请填写征求意见截止时间。";
                    return r;
                }
                //if (model.SolicitedDeadline.Value.Date < DateTime.Now.Date)
                //{
                //    r.flag = 0;
                //    r.msg = "执行失败，征求意见截止时间不能小于当前时间。";
                //    return r;
                //}

                if (string.IsNullOrEmpty(model.ParentOpinion))
                {
                    r.flag = 0;
                    r.msg = "发布失败，请填写关于征订校服的征求家长意见书。";
                    return r;
                }
            }
            if (model.Id == 0)
            {
                entity = new XUniformScheme();
                entity.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();//待提交
                entity.ResponseNum = 0;
                entity.AgreeNum = 0;
                entity.AgreeRate = 0;
                entity.SolicitedStatuz = UniformSolicitedEnum.Cretae.ObjToInt();
                entity.SchoolId = model.SchoolId;
                if (model.SolicitedDeadline < DateTime.Now.Date)
                {
                    r.flag = 0;
                    r.msg = "执行失败，征求意见截止时间必须不小于当前时间。";
                    return r;
                }
            }
            else
            {
                entity = await base.QueryById(model.Id);
                if (entity == null || entity.SchoolId != model.SchoolId)
                {
                    r.flag = 0;
                    r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                    return r;
                }
                if (entity.FilingStatuz != UniformFilingEnum.SubmitNone.ObjToInt())
                {
                    r.flag = 0;
                    r.msg = "执行失败，当前状态禁止修改。";
                    return r;
                }
            }
            //验证附件
            var listAddAttachment = new List<BAttachment>();
            var listAttachmentConfig = await this.Db.Queryable<BAttachmentConfig>()
            .Where((config) => config.Statuz == StatuzEnum.Enable.ObjToInt() && config.ModuleType == ModuleTypeEnum.SchemeCreate.ObjToInt() && (config.UnitId == model.CountyId || config.UnitId == 0)
            && config.IsDeleted == false).ToListAsync();
            if (listAttachmentConfig != null && listAttachmentConfig.Count > 0)
            {
                if (listAttachmentConfig.Where(x => x.UnitId == model.CountyId).Count() > 0)
                {
                    listAttachmentConfig = listAttachmentConfig.Where(x => x.UnitId == model.CountyId).ToList();
                }
                if (model.AttachmentIdList != null && model.AttachmentIdList.Count > 0)
                {
                    var listAttData = await this.Db.Queryable<BAttachmentData>()
                    .Where((att) => att.CreateId == model.CreateId && att.UnitId == model.SchoolId
                    && model.AttachmentIdList.Contains(att.Id)
                    && att.ModuleType == ModuleTypeEnum.SchemeCreate.ObjToInt() && att.IsDeleted == false).ToListAsync();
                    if (listAttData != null && listAttData.Count > 0)
                    {
                        foreach (var item in listAttData)
                        {
                            var itemConfig = listAttachmentConfig.Where(m => m.ModuleType == item.ModuleType && m.FileCategory == item.FileCategory).FirstOrDefault();
                            if (itemConfig != null)
                            {
                                var entityAtt = SetAttachment.SetModel(item);
                                if (entityAtt != null)
                                {
                                    listAddAttachment.Add(entityAtt);
                                }
                            }
                        }
                    }
                }
            }
            entity.SolicitedNum = model.SolicitedNum;
            entity.SolicitedDeadline = model.SolicitedDeadline;
            entity.ParentOpinion = model.ParentOpinion;

            if (entity.Id == 0)
            {
                entity.SchemeYear = model.SchemeYear;
                await base.Add(entity);
            }
            else
            {
                //发布后，禁止修改年度
                if (entity.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt())
                {
                    if (entity.SchemeYear != model.SchemeYear)
                    {
                        r.flag = 0;
                        r.msg = "执行失败，已发布禁止修改年度。";
                        return r;
                    }
                    //已发布修改时间必须不小于之前的发布时间
                    if (model.SolicitedDeadline < DateTime.Now.Date)
                    {
                        if (entity.SolicitedDeadline.Value > model.SolicitedDeadline.Value)
                        {
                            r.flag = 0;
                            r.msg = "执行失败，已截止的方案，征求意见截止时间必须不小于上次发布的时间。";
                            return r;
                        }
                    }
                }
                entity.SchemeYear = model.SchemeYear;
                // await base.Update(entity);
                await this.Db.Updateable<XUniformScheme>()
                .SetColumns((scheme) => new XUniformScheme()
                {
                    SchemeYear = entity.SchemeYear,
                    SolicitedDeadline = entity.SolicitedDeadline,
                    SolicitedNum = entity.SolicitedNum,
                    ParentOpinion = entity.ParentOpinion,
                    ModifyTime = DateTime.Now,
                    ModifyBy = model.CreateBy,
                    ModifyId = model.CreateId
                })
                .Where((scheme) => scheme.Id == entity.Id)
                .ExecuteCommandAsync();
            }
            //处理附件
            if (listAddAttachment.Count > 0)
            {
                foreach (var item in listAddAttachment)
                {
                    item.ObjectId = entity.Id;
                    item.Id = BaseDBConfig.GetYitterId();
                }
                await this.Db.Insertable<BAttachment>(listAddAttachment).ExecuteCommandAsync();
            }

            r.flag = 1;
            r.msg = "保存成功！"; 
            r.data.rows = entity; 
            if (model.OptStatuz == UniformSolicitedEnum.Publish.ObjToInt() && entity.SolicitedStatuz == UniformSolicitedEnum.Cretae.ObjToInt())
            {
                entity.SolicitedStatuz = UniformSolicitedEnum.Publish.ObjToInt();
                //添加采购方式。
                var purchaseMethodList = await this.Db.Queryable<XUniformConfig>().Where(m => m.IsDeleted == false && m.ValueNum == 1 && m.Code == ConfigEnum.Procure.ObjToInt().ToString() && (m.UnitId == 0 || m.UnitId == model.CountyId)).ToListAsync();
                if (purchaseMethodList != null && purchaseMethodList.Count > 0)
                {
                    var listTemp = purchaseMethodList.Where(m => m.UnitId == model.CountyId);
                    if (listTemp != null && listTemp.Count() > 0)
                    {
                        entity.PurchaseMethodIds = string.Join(",", listTemp.Select(m => m.DicValue));
                        entity.PurchaseMethodNames = string.Join(",", listTemp.Select(m => m.Title));

                    }
                    else
                    {
                        entity.PurchaseMethodIds = string.Join(",", purchaseMethodList.Select(m => m.DicValue));
                        entity.PurchaseMethodNames = string.Join(",", purchaseMethodList.Select(m => m.Title));
                    }
                }

                var listAllAttachment = new List<BAttachment>();
                //是否是修改，上次上传的附件 
                var listAttachment = await this.Db.Queryable<BAttachment>()
                .Where(attachment => attachment.ModuleType == ModuleTypeEnum.SchemeCreate.ObjToInt() && attachment.IsDeleted == false && attachment.ObjectId == entity.Id).ToListAsync();
                if (listAttachment != null && listAttachment.Count > 0)
                {
                    listAllAttachment.AddRange(listAttachment);
                }
                if (listAddAttachment.Count > 0)
                {
                    listAllAttachment.AddRange(listAddAttachment);
                }
                //发布前验证附件是否必填。
                if (listAttachmentConfig != null && listAttachmentConfig.Count > 0)
                {
                    string errorMsg = "";
                    foreach (var item in listAttachmentConfig)
                    {
                        if (item.IsFilled == 1)
                        {
                            var listTemp = listAllAttachment.Where(m => m.FileCategory == item.FileCategory && m.ModuleType == item.ModuleType);
                            if (!(listTemp != null && listTemp.Count() > 0))
                            {
                                errorMsg += $"附件{item.Name}必须上传。<br/>";
                            }
                        }
                    }
                    if (errorMsg != "")
                    {
                        r.flag = 0;
                        r.msg = errorMsg;
                        return r;
                    }
                }
                //生成选用批次
                //var schemeNum = 0;
                //var schemeNo = await this.Db.Queryable<XUniformScheme>().Where(f => f.SchemeYear == DateTime.Now.Year && f.SolicitedStatuz == UniformSolicitedEnum.Publish.ObjToInt()).MaxAsync(m => m.SchemeNo);
                //if (schemeNo != null && schemeNo.IndexOf($"XY{DateTime.Now.Year}") > -1)
                //{
                //    schemeNo = schemeNo.Replace($"XY{DateTime.Now.Year}", "").TrimStart('0');
                //    int.TryParse(schemeNo, out schemeNum);
                //}
                //entity.SchemeNo = "XY" + DateTime.Now.Year.ObjToInt() + (schemeNum + 1).ToString().PadLeft(4, '0');
   
                entity.SchemeNo = await uniformPurchaseManager.GenerateCode(1, entity.SchemeYear); ;

                entity.ReleaseTime = DateTime.Now;

                await this.Db.Updateable<XUniformScheme>()
                  .SetColumns((scheme) => new XUniformScheme()
                  {
                      SchemeNo = entity.SchemeNo,
                      SolicitedStatuz = entity.SolicitedStatuz,
                      PurchaseMethodIds = entity.PurchaseMethodIds,
                      PurchaseMethodNames = entity.PurchaseMethodNames,
                      ReleaseTime = entity.ReleaseTime,
                      ModifyTime = DateTime.Now,
                      ModifyBy = model.CreateBy,
                      ModifyId = model.CreateId
                  })
                  .Where((scheme) => scheme.Id == entity.Id)
                  .ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "发布成功！";
                r.data.rows = entity;
            }
            return r;
        }

        /// <summary>
        /// 方案选用-校服选用方案（复制）
        /// </summary>
        /// <param name="id">拷贝id</param>
        /// <returns></returns>
        public async Task<Result<long>> SaveCopy(long id,long userid)
        {
            Result<long> r = new Result<long>();
            var model = await base.QueryById(id);
            if (model == null)
            {
                r.flag = 0;
                r.msg = "执行失败，你选择的数据不存在，请刷新重新操作。";
                return r;
            }
            XUniformScheme entity = new XUniformScheme();
            entity.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();//待提交
            entity.ResponseNum = 0;
            entity.AgreeNum = 0;
            entity.AgreeRate = 0;
            entity.SolicitedStatuz = UniformSolicitedEnum.Cretae.ObjToInt();
            entity.SchoolId = model.SchoolId;
            entity.SolicitedNum = model.SolicitedNum;
            entity.SolicitedDeadline = model.SolicitedDeadline;
            entity.ParentOpinion = model.ParentOpinion;
            entity.SchemeYear = model.SchemeYear;


            //附件
            var listAttachment = await this.Db.Queryable<BAttachment>()
            .Where((att) => att.ModuleType == ModuleTypeEnum.SchemeCreate.ObjToInt() && att.ObjectId== model.Id && att.IsDeleted == false).ToListAsync();
           
            await base.Add(entity);

            //处理附件
            if (listAttachment.Count > 0)
            {
                foreach (var item in listAttachment)
                {
                    item.ObjectId = entity.Id;
                    item.Id = BaseDBConfig.GetYitterId();
                    item.UserId = userid;
                }
                await this.Db.Insertable<BAttachment>(listAttachment).ExecuteCommandAsync();
            }

            r.flag = 1;
            r.msg = "复制成功！";
            r.data.rows = entity.Id;
            return r;
        }

        /// <summary>
        /// 方案选用-校服选用方案（保存、发布）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<Result> SaveOpinion(XUniformSchemeOpinionDto model)
        {
            Result r = new Result();
            //model.ParentStudentId = model.StudentId;//如果传递的参数改了，这里就不用赋值了。
            XUniformScheme entityScheme = await base.QueryById(model.UniformSchemeId);
            if (entityScheme == null || entityScheme.SolicitedStatuz != UniformSolicitedEnum.Publish.ObjToInt())
            {
                r.flag = 0;
                r.msg = "保存失败，当前校服选用方案已不存在。";
                return r;
            }
            if (entityScheme.SolicitedStatuz != UniformSolicitedEnum.Publish.ObjToInt() || entityScheme.SolicitedDeadline < DateTime.Now.Date)
            {
                r.flag = 0;
                r.msg = "保存失败，当前校服选用方案已过征求意见截止时间。";
                return r;
            }
            var listVersify = await this.Db.Queryable<XUniformSchemeOpinion>().Where((opinion) => opinion.UniformSchemeId == model.UniformSchemeId && opinion.StudentName == model.StudentName && opinion.Mobile == model.Mobile ).ToListAsync();
            if (listVersify != null && listVersify.Count > 0)
            {
                r.flag = 0;
                r.msg = "执行失败，校服选用方你已提交征求意见，禁止重复提交。";
                return r;
            }

            if (model.Statuz == StatuzOpinionEnum.Ok.ObjToInt())
            {
                //判断采购方式。
                if (model.PurchaseMethod <= 0)
                {
                    //自购
                    r.flag = 0;
                    r.msg = "保存失败，请选择校服采购方式。";
                    return r;
                }
            }
            else if (model.Statuz == StatuzOpinionEnum.Not.ObjToInt())
            {
                //验证意见
                if (string.IsNullOrEmpty(model.Opinion))
                {
                    r.flag = 0;
                    r.msg = "保存失败，请填写意见。";
                    return r;
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "执行失败，请选择同意或不同意校服选用。";
                return r;
            }
            if (model.StudentName == null || model.StudentName.Length < 2 || model.StudentName.Length > 20)
            {
                r.flag = 0;
                r.msg = "执行失败，请正确填写学生姓名。";
                return r;
            }
            if (model.Mobile == null || model.Mobile.Length < 7 || model.Mobile.Length > 20)
            {
                r.flag = 0;
                r.msg = "执行失败，请正确填写手机号码。";
                return r;
            }
            int gradeId = 0;
            string gradeName = "";
            string className = "";
            //int classid = 0;
            var listUnitExt = await Db.Queryable<PSchoolExtension>().Where(n => n.UnitId == entityScheme.SchoolId).ToListAsync();
            if (listUnitExt != null && listUnitExt.Count > 0)
            {
                var entityUnitExt = listUnitExt.FirstOrDefault();
                //获取当前学校年级集合，根据学校学段获取。
                var listDic = await Db.Queryable<BDictionary>().Where(m => m.Statuz == StatuzEnum.Enable.ObjToInt() ).ToListAsync();
                if (listDic != null && listDic.Count > 0)
                {
                  var   gradeTemp = listDic.Where(m => entityUnitExt.Period.Contains(m.TypeCode) && m.DicValue == model.GradeId.ToString()).FirstOrDefault();
                    if (gradeTemp!=null)
                    {
                        gradeName = gradeTemp.DicName;
                        gradeId = model.GradeId;
                    }
                    var classtemp = listDic.Where(m => m.TypeCode == DictionaryTypeCodeEnum.Class.ObjToInt().ToString() && m.DicName == model.ClassName).FirstOrDefault();
                    if (classtemp!=null)
                    {
                        className = classtemp.DicName;
                        //classid = model.ClassId;
                    }
                }
            }
            if (gradeId <= 0)
            {
                r.flag = 0;
                r.msg = "执行失败，请选择年级。";
                return r;
            }
            //采购方式验证。 
            string opinionPurchaseMethodName = "";
            if (entityScheme.PurchaseMethodIds != null && entityScheme.PurchaseMethodIds.Split(',').Contains(model.PurchaseMethod.ToString()))
            {
                var idArr = entityScheme.PurchaseMethodIds.Split(',');
                for (int i = 0; i < idArr.Length; i++)
                {
                    if (idArr[i] == model.PurchaseMethod.ToString())
                    {
                        var nameArr = entityScheme.PurchaseMethodNames.Split(',');
                        if (nameArr.Length >= i && nameArr[i] != null)
                        {
                            opinionPurchaseMethodName = nameArr[i];
                        }
                    }
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "保存失败，当前校服采购方式不存在，请刷新重新操作。";
                return r;
            }

            XUniformSchemeOpinion entity = new XUniformSchemeOpinion();
            entity.UniformSchemeId = model.UniformSchemeId;
            entity.Statuz = model.Statuz;
            // entity.StudentId = entityStudent.StudentId;

       
            entity.ParentName = "";//当前登录用户名称

            entity.GradeId = gradeId;
            entity.GradeName = gradeName;
            entity.ClassName = className;
           // entity.ParentStudentId= model.ParentStudentId;
            entity.Opinion = model.Opinion;
            entity.StudentName = model.StudentName;
            entity.Mobile = model.Mobile;
            entity.PurchaseMethod = model.PurchaseMethod;
            entity.PurchaseMethodName = opinionPurchaseMethodName;//这个要去数据库中查询

            await this.Db.Insertable<XUniformSchemeOpinion>(entity).ExecuteCommandAsync();
           
            //更新数量
            long purchaseMethod = 0;
            string purchaseMethodName = "";
            var list = await this.Db.Queryable<XUniformSchemeOpinion>().Where((opinion) => opinion.UniformSchemeId == model.UniformSchemeId && opinion.IsDeleted == false).ToListAsync();
            if (list != null && list.Count > 0)
            {
                entityScheme.ResponseNum = list.Count;
                entityScheme.AgreeNum = list.Where(m => m.Statuz == StatuzOpinionEnum.Ok.ObjToInt()).Count();
                if (entityScheme.ResponseNum > entityScheme.SolicitedNum)
                {
                    entityScheme.AgreeRate = Math.Round((entityScheme.AgreeNum ?? 0) / (decimal)entityScheme.ResponseNum.Value, 2);
                }
                else
                {
                    entityScheme.AgreeRate = Math.Round((entityScheme.AgreeNum ?? 0) / (decimal)entityScheme.SolicitedNum.Value, 2);
                }
                var purchasemethodGroup = list.GroupBy(m => m.PurchaseMethodName).Select(m => new XUniformScheme() { PurchaseMethodName = m.FirstOrDefault().PurchaseMethodName, PurchaseMethod = m.FirstOrDefault().PurchaseMethod, ResponseNum = m.Count(), }).ToList();
                if (purchasemethodGroup != null && purchasemethodGroup.Count > 0)
                {
                    var entityTemp = purchasemethodGroup.OrderByDescending(m => m.ResponseNum).FirstOrDefault();
                    purchaseMethod = entityTemp.PurchaseMethod ?? 0;
                    purchaseMethodName = entityTemp.PurchaseMethodName;
                }
            }
            //await base.Update(entityScheme);
            await this.Db.Updateable<XUniformScheme>()
                .SetColumns((scheme) => new XUniformScheme()
                {
                    ResponseNum = entityScheme.ResponseNum,
                    AgreeNum = entityScheme.AgreeNum,
                    AgreeRate = entityScheme.AgreeRate,
                    PurchaseMethod = purchaseMethod,
                    PurchaseMethodName = purchaseMethodName
                })
                .Where((scheme) => scheme.Id == entityScheme.Id)
                .ExecuteCommandAsync();

            r.flag = 1;
            r.msg = "保存成功！";
            return r;
        }
        #endregion

        #region 提交备案


        /// <summary>
        /// 方案选用-单位提交
        /// </summary>
        /// <param name="model">提交</param>
        /// <returns></returns>
        public async Task<Result> FilingSubmit(XUniformSchemeDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //发布后，禁止修改年度
            if (entity.SolicitedStatuz != UniformSolicitedEnum.Publish.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据未发布。";
                return r;
            }
            //发布后，禁止修改年度
            if (entity.SolicitedDeadline >= DateTime.Now.Date)
            {
                r.flag = 0;
                r.msg = "执行失败，征求意见截止时间还未结束。";
                return r;
            }
            if (!(entity.FilingStatuz == UniformFilingEnum.SubmitNone.ObjToInt()))
            {
                r.flag = 0;
                r.msg = "执行失败，当前状态已变更，禁止提交，请刷新重新操作 。";
                return r;
            }
            entity.FilingStatuz = UniformFilingEnum.Filinged.ObjToInt();
            entity.IsFiling = 0;
            //如果不需要审核，直接结束
            var entityConfigList = await this.Db.Queryable<XUniformConfig>().Where((config) => (config.UnitId == model.CountyId || config.UnitId == 0) && config.Code == "2000" && config.DicValue == "201").ToListAsync();
            if (entityConfigList != null && entityConfigList.Count() > 0)
            {
                var entityTempconfig = entityConfigList.Where(m => m.UnitId == model.CountyId).FirstOrDefault();
                if (entityTempconfig != null)
                {
                    if (entityTempconfig.ValueNum == 1)
                    {
                        entity.FilingStatuz = UniformFilingEnum.Wait.ObjToInt();
                    }
                }
                else if (entityConfigList.FirstOrDefault().ValueNum == 1)
                {
                    entity.FilingStatuz = UniformFilingEnum.Wait.ObjToInt();
                }
            }
            var resultNo = await this.Db.Updateable<XUniformScheme>().SetColumns((scheme) => new XUniformScheme() { FilingStatuz = entity.FilingStatuz, IsFiling = entity.IsFiling })
                .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();

            if (resultNo > 0)
            {
                //插入记录表 UniformAuditLog
                var entityLog = new XUniformAuditLog();
                entityLog.UniformId = entity.Id;
                entityLog.AuditTime = DateTime.Now;
                entityLog.AduitStatuz = entity.FilingStatuz;
                entityLog.DataSourceCode = "XY" + UniformAuditLogCodeEnum.Scheme.ObjToInt().ToString();
                entityLog.AuditExplain = "提交备案";
                entityLog.IsCurrent = 1;
                await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "提交成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "提交失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            }
            return r;
        }


        /// <summary>
        /// 方案选用-区县审核
        /// </summary>
        /// <param name="model">提交</param>
        /// <returns></returns>
        public async Task<Result> FilingConfirm(XUniformSchemeDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //获取父级Id验证
            var entityUnit = await this.Db.Queryable<PUnit>().Where((unit) => unit.Id == entity.SchoolId).FirstAsync();
            if (entityUnit == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据单位信息不存在。";
                return r;
            }
            if (entityUnit.PId != model.CountyId)
            {
                r.flag = 0;
                r.msg = "执行失败，非本单位的下属单位禁止操作。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.Wait.ObjToInt().ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据状态已变更，禁止操作 。";
                return r;
            }
            entity.FilingStatuz = model.FilingStatuz;
            entity.FilingTime = DateTime.Now;
            entity.FilingExplanation = model.FilingExplanation;
            if (model.FilingStatuz == UniformFilingEnum.Filinged.ObjToInt())
            {
                //审核通过。  
                entity.IsFiling = 1;
            }
            else
            {
                //验证审核退回，请填写退回原因。
                if (string.IsNullOrEmpty(model.FilingExplanation))
                {
                    r.flag = 0;
                    r.msg = "执行失败，退回请填写退回说明 。";
                    return r;
                }
            }
            var resultNo = await this.Db.Updateable<XUniformScheme>().SetColumns((scheme) => new XUniformScheme()
            {
                FilingStatuz = entity.FilingStatuz,
                FilingTime = entity.FilingTime,
                FilingExplanation = entity.FilingExplanation,
                IsFiling = entity.IsFiling
            })
             .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                //插入记录表 UniformAuditLog
                var entityLog = new XUniformAuditLog();
                entityLog.UniformId = entity.Id;
                entityLog.AuditTime = DateTime.Now;
                entityLog.AduitStatuz = entity.FilingStatuz;
                entityLog.DataSourceCode = "XY" + UniformAuditLogCodeEnum.Scheme.ObjToInt().ToString();
                entityLog.AuditExplain = "审核";
                entityLog.IsCurrent = 1;
                await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "审核成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "审核失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            }
            return r;
        }

        /// <summary>
        /// 方案选用-区县退回
        /// </summary>
        /// <param name="model">Id</param>
        /// <returns></returns>
        public async Task<Result> FilingBackout(XUniformSchemeDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //获取父级Id验证
            var entityUnit = await this.Db.Queryable<PUnit>().Where((unit) => unit.Id == entity.SchoolId).FirstAsync();
            if (entityUnit == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据单位信息不存在。";
                return r;
            }
            if (entityUnit.PId != model.CountyId)
            {
                r.flag = 0;
                r.msg = "执行失败，非本单位的下属单位禁止操作。";
                return r;
            }
            if (entity.IsFiling == 1)
            {
                r.flag = 0;
                r.msg = "执行失败，当前选用方案已审核通过，禁止退回 。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.Filinged.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前选用方案状态已变更，请刷新重新操作。";
                return r;
            }
            //验证审核退回，请填写退回原因。
            if (string.IsNullOrEmpty(model.FilingExplanation))
            {
                r.flag = 0;
                r.msg = "执行失败，退回请填写退回说明 。";
                return r;
            }
            entity.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
            entity.FilingTime = DateTime.Now;
            entity.FilingExplanation = model.FilingExplanation;
            var resultNo = await this.Db.Updateable<XUniformScheme>().SetColumns((scheme) => new XUniformScheme()
            {
                FilingStatuz = entity.FilingStatuz,
                FilingTime = entity.FilingTime,
                FilingExplanation = entity.FilingExplanation
            })
            .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                //插入记录表 UniformAuditLog
                var entityLog = new XUniformAuditLog();
                entityLog.UniformId = entity.Id;
                entityLog.AuditTime = DateTime.Now;
                entityLog.AduitStatuz = entity.FilingStatuz;
                entityLog.DataSourceCode = "XY" + UniformAuditLogCodeEnum.Scheme.ObjToInt().ToString();
                entityLog.AuditExplain = "提交退回";
                entityLog.IsCurrent = 1;
                await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "退回成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "退回失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            }
            return r;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 方案选用-组装查询条件
        /// </summary>
        /// <param name="param">XUniformSchemeParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<XUniformScheme, bool>> ListFilter(XUniformSchemeParam param)
        {
            var expression = LinqExtensions.True<XUniformScheme>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
            }
            return expression;
        }
        #endregion

    }
}

