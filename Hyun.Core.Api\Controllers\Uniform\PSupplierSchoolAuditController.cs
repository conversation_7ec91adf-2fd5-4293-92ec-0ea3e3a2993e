﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models;
using Hyun.Core.IServices;
using SkyWalking.NetworkProtocol.V3;
namespace Hyun.Core.Api
{
    /// <summary>
    /// 单位和企业认证审核
    /// </summary>
    [Route("api/hyun/psupplierschoolaudit")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PSupplierSchoolAuditController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IPSupplierSchoolAuditServices ipsupplierschoolauditservicesManager;
        private readonly IPUnitServices unitManager;

        public PSupplierSchoolAuditController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IPSupplierSchoolAuditServices _ipsupplierschoolauditservicesManager, IPUnitServices _unitManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ipsupplierschoolauditservicesManager = _ipsupplierschoolauditservicesManager;
            unitManager = _unitManager;
        }


        /// <summary>
        /// 超管查询待审核认证信息列表
        /// </summary>
        /// <param name="param">PSupplierSchoolAuditParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<PUnitView>>> PSupplierSchoolAuditGetPaged([FromBody] PSupplierSchoolAuditParam param)
        {
            var msgdata = new Result<List<PUnitView>>();
            if (user.IsSystemUser)
            {
                PageModel<PUnitView> pg = await ipsupplierschoolauditservicesManager.GetPaged(param);
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;

            ////单位名称
            //var pgUnit = await unitManager.Find(f => f.Statuz == 1);
            //var listUnit = pgUnit.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name });

            ////单位类型
            //var pgUnitType = EnumExtensions.EnumToList<UnitTypeEnum>();
            //List<dropdownModel> listUnitType = pgUnitType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

            ////审核类型
            //var pgAuditType = EnumExtensions.EnumToList<AuditTypeEnum>();
            //List<dropdownModel> listAuditType = pgAuditType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

        }

        /// <summary>
        /// 单位、企业申请认证
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("apply")]
        public async Task<Result> PSupplierSchoolApplyCertification([FromBody] PSupplierSchoolAuditDto obj)
        {
            Result r = new Result();
            PSupplierSchoolAudit o = mapper.Map<PSupplierSchoolAudit>(obj);
            o.UnitType = user.UnitTypeId;
            o.UnitId = user.UnitId;
            r = await ipsupplierschoolauditservicesManager.ApplyCertification(o);
            return r;
        }

        /// <summary>
        /// 获取当前单位信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getmyunit")]
        public async Task<Result> PSupplierSchoolMyUnit()
        {
            Result r = new Result();
            r = await ipsupplierschoolauditservicesManager.PSupplierSchoolMyUnit(user.UnitId);
            return r;
        }


        /// <summary>
        /// 根据认证表Id获取待审核信息
        /// </summary>
        /// <param name="id">认证表Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbyid")]
        public async Task<Result> PSupplierSchoolAuditById(long id)
        {
            Result r = new Result();
            r = await ipsupplierschoolauditservicesManager.GetStayAuditData(id);
            return r;
        }

        /// <summary>
        /// 超管审核不修改内容
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("auditunit")]
        public async Task<Result> UnitAudit([FromBody] SuperAuditDto o)
        {
            Result r = new Result();
            r = await ipsupplierschoolauditservicesManager.UnitAudit(o);
            return r;
        }


        /// <summary>
        /// 删除单位企业申请认证附件
        /// </summary>
        /// <param name="id">附件Id</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delfilebyid")]
        public async Task<Result<string>> DeleteFileById(long id)
        {
            var r = await ipsupplierschoolauditservicesManager.SupplierFileDelete(id);
            return r;
        }
    }
}
