﻿using Org.BouncyCastle.Bcpg.OpenPgp;

namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///查询统计页面表
    ///</summary>
    [SugarTable("wf_PageDefinition", "查询统计页面表")]
    public class WfPageDefinition : BaseEntity
    {

        public WfPageDefinition()
        {

        }

        /// <summary>
        ///对应模块表xa_Module中Id
        /// </summary>
        public long ModuleId { get; set; } = 0;

        /// <summary>
        ///名称
        /// </summary>
        [SugarColumn(Length = 127)]
        public string Name { get; set; }

        /// <summary>
        ///显示名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string ShowName { get; set; }

        /// <summary>
        ///审批流程配置表Id集合
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = true)]
        public string ProcessIds { get; set; }

        /// <summary>
        ///单位类别（单位3，区县2， 市级1，为了方便运营管理，运营商超管看所有）
        /// </summary>
        public int Unittype { get; set; }

        /// <summary>
        ///数据源（1：项目列表；2：待审批列表；3：已审批列表 4:项目清单列表）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int SourceData { get; set; }

        /// <summary>
        ///适用范围（1：电脑； 2：小程序；3：全部）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int UseType { get; set; }

        /// <summary>
        ///页面类型（1：查询统计；2：待审批；2：已审批）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int PageType { get; set; }

        /// <summary>
        ///默认每页显示数量
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int PageSize { get; set; }

        /// <summary>
        ///总计显示名称，如不需要，为空
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string TotalName { get; set; }

        /// <summary>
        ///总计显示位置（字段列）
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string TotalCounmn { get; set; }

        /// <summary>
        ///使用单位Id
        /// </summary>
        public long UseUnitId { get; set; } = 0;

        /// <summary>
        ///Logo
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Logo { get; set; }

        /// <summary>
        ///状态(3：设置中 1：启用 2：禁用)
        /// </summary>
        public int Statuz { get; set; } = 3;

        /// <summary>
        ///默认排序字段
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string DefaultSort { get; set; }

        /// <summary>
        ///默认查询条件
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string DefaultWhere { get; set; }

        /// <summary>
        ///单一主键或唯一值键（下拉框可选可填）
        /// </summary>
        [SugarColumn(Length = 63)]
        public string PkId { get; set; } = "";

        /// <summary>
        ///排序规则 1:正序asc 2:倒序desc 3:多列排序方法 ，配合"默认排序字段"字段使用，当选择3的时候默认排序为手填
        /// </summary>
        public int SortType { get; set; } = 1;

        /// <summary>
        ///使用单位字段名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string UseUnitField { get; set; } = "";

        /// <summary>
        ///使用用户字段名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string UseUserField { get; set; } = "";

        /// <summary>
        /// 是否为子页面（1：是，0：否）
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int IsSubpage { get; set; } = 0;

        /// <summary>
        ///查询类型（1：默认，2：自定义）默认1
        /// </summary>
        public int SearchType { get; set; } = 1;

        /// <summary>
        ///跳转地址
        /// </summary>
        [SugarColumn(Length = 1023)]
        public string Url { get; set; } = "Process3_0/ProjectList.aspx";

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 适用单位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrUnittype { get; set; }

        /// <summary>
        /// 适用范围
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrUseType { get; set; }

        /// <summary>
        /// 页面类型
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrPageType { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrStatuz { get; set; }

        /// <summary>
        /// 字段Code编码（只有当数据源选择“项目清单列表”时使用）
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string FieldCode { get; set; }

    }


    /// <summary>
    /// 查询列表
    /// </summary>
    public class WfPageListView
    {
        /// <summary>
        /// 查询统计页面Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 显示名称
        /// </summary>
        public string ShowName { get; set; }

        /// <summary>
        /// 显示图标
        /// </summary>
        public string Logo { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Url { get; set; }

       
    }
}

