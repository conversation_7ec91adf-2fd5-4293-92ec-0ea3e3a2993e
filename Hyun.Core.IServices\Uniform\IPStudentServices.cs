﻿using Hyun.Core.Model.Models;
namespace Hyun.Core.IServices
{

    ///<summary>
    ///PStudent接口方法
    ///</summary>
    public interface IPStudentServices : IBaseServices<PStudent>
    {

        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="o">PStudent对象</param>
        /// <returns></returns>
        Task<Result> InsertUpdate(PStudent o);

        /// <summary>
        /// 根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        Task<Result> FakeDeleteById(long id);

        /// <summary>
        /// 根据Id集合批量删除数据【假删除】
        /// </summary>
        ///  <param name="ids">id集合逗号分隔</param>
        /// <returns></returns>
        Task<Result> FakeDeleteByIds(string ids);

        /// <summary>
        /// 根据Id删除数据【真删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        Task<Result> DeleteById(long id);

        /// <summary>
        /// 根据Id集合批量删除数据【真删除】
        /// </summary>
        /// <param name="ids">id集合逗号分隔</param>
        /// <returns></returns>
        Task<Result> DeleteByIds(string ids);

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<PStudent> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<PStudent>> Find(Expression<Func<PStudent, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">PStudentParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<PStudentDto>> GetPaged(PStudentParam param);


        /// <summary>
        /// 单位管理员导入班级学生信息
        /// </summary>
        /// <param name="list"></param>
        /// <param name="uniformClassId"></param>
        /// <returns></returns>
        Task<Result> ImportStudentInfo(List<PStudentDto> list,long uniformClassId);

        /// <summary>
        /// 班主任学生管理列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<PStudentDto>> GetStudentPagedByTeacher(PStudentParam param);

        /// <summary>
        /// 班主任添加修改学生信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> TeacherStudentInsertUpdate(PStudentModel o);

        /// <summary>
        /// 班主任删除学生信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result<string>> TeacherDelStudent(long id);

        /// <summary>
        /// 班主任导入学生信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<Result> TeacherImportStudentInfo(List<PStudentModel> list);
    }
}

