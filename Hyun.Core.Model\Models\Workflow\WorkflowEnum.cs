﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hyun.Core.Model.Models.Workflow
{

    /// <summary>
    /// 使用方式
    /// </summary>
    public enum WfUsageEnum
    {
        /// <summary>
        /// 本单位
        /// </summary>
        [Description("本单位")]
        MyUnit = 1,

        /// <summary>
        /// 本单位及所有下属单位
        /// </summary>
        [Description("本单位及所有下属单位")]
        OtherUnit = 2
    }

    /// <summary>
    /// 写入第三方库类型
    /// </summary>
    public enum WfInputDataType
    {
        /// <summary>
        /// 主数据
        /// </summary>
        [Description("主数据")]
        MainTable = 1,

        /// <summary>
        /// 项目清单数据
        /// </summary>
        [Description("项目清单数据")]
        DetailTable = 2,

        /// <summary>
        /// 资金分配指定字段
        /// </summary>
        [Description("资金分配指定字段")]
        FundTable = 3,
    }

    /// <summary>
    /// 节点类型
    /// </summary>
    public enum WfNodeTypeType
    {
        /// <summary>
        /// 填报
        /// </summary>
        [Description("填报")]
        Filling = 1,

        /// <summary>
        /// 审批
        /// </summary>
        [Description("审批")]
        Approval = 2,

        /// <summary>
        /// 项目库
        /// </summary>
        [Description("项目库")]
        FundPool = 3,

        /// <summary>
        /// 完善模式
        /// </summary>
        [Description("完善模式")]
        Improve = 4,
    }

    /// <summary>
    /// 流程级别
    /// </summary>
    public enum WfProcessLevel
    {
        /// <summary>
        /// 市级
        /// </summary>
        [Description("市级")]
        City = 1,

        /// <summary>
        /// 区级
        /// </summary>
        [Description("区级")]
        County = 2,

        /// <summary>
        /// 校级
        /// </summary>
        [Description("校级")]
        School = 3,
    }

    /// <summary>
    /// 审批方式
    /// </summary>
    public enum WfAduitType
    {
        /// <summary>
        /// 单用户审批
        /// </summary>
        [Description("单用户审批")]
        Single = 1,

        /// <summary>
        /// 多用户审批
        /// </summary>
        [Description("多用户审批")]
        Multiple = 2,
    }

    /// <summary>
    /// 审核人员类型
    /// </summary>
    public enum WfAduitUserType
    {
        /// <summary>
        /// 该流程所有人
        /// </summary>
        [Description("该流程所有人")]
        All = 1,

        /// <summary>
        /// 该流程指定用户
        /// </summary>
        [Description("该流程指定用户")]
        Specify = 2,
    }

    /// <summary>
    /// 指定审批人对象类型
    /// </summary>
    public enum WfAuditObjectType
    {
        /// <summary>
        /// 指定的审批人
        /// </summary>
        [Description("指定的审批人")]
        Appoint = 1,

        /// <summary>
        /// 专家列表 
        /// </summary>
        [Description("专家列表 ")]
        Expert = 2,
    }

    /// <summary>
    /// 指定人员数量
    /// </summary>
    public enum WfDesigneeNum
    {
        /// <summary>
        /// 单人
        /// </summary>
        [Description("单人")]
        SinglePerson = 1,

        /// <summary>
        /// 多人 
        /// </summary>
        [Description("多人")]
        MultiplePeople = 2,
    }

    /// <summary>
    /// 字典数据类型
    /// </summary>
    public enum WfDicType
    {
        /// <summary>
        /// 普通下拉框
        /// </summary>
        [Description("普通下拉框")]
        NormalDropdown = 1,

        /// <summary>
        /// 单选
        /// </summary>
        [Description("单选")]
        RadioSingle = 11,

        /// <summary>
        /// 多选
        /// </summary>
        [Description("多选")]
        CheckBoxMultiple = 12,

        /// <summary>
        /// 下拉框多选
        /// </summary>
        [Description("下拉框多选")]
        ComboxMultiple = 13,
    }

    /// <summary>
    /// 条件符号
    /// </summary>
    public enum WfConditionSymbol
    {
        /// <summary>
        /// 等于
        /// </summary>
        [Description("等于(==)")]
        EqualTo = 1,

        /// <summary>
        /// 不等于
        /// </summary>
        [Description("不等于(!=)")]
        NoEqualTo = 2,

        /// <summary>
        /// 大于
        /// </summary>
        [Description("大于(>)")]
        GreaterThan = 3,

        /// <summary>
        /// 大于等于
        /// </summary>
        [Description("大于等于(>=)")]
        GreaterThanEqual = 4,

        /// <summary>
        /// 小于
        /// </summary>
        [Description("小于(<)")]
        LessThan = 5,

        /// <summary>
        /// 小于等于
        /// </summary>
        [Description("小于等于(<=)")]
        LessThanEqual = 6,

        ///// <summary>
        ///// 包括
        ///// </summary>
        //[Description("包括(in)")]
        //Include = 7,

        ///// <summary>
        ///// 模糊匹配
        ///// </summary>
        //[Description("模糊匹配(like)")]
        //Vague = 8,

        /// <summary>
        /// 占位
        /// </summary>
        [Description("占位(#)")]
        Seat = 10,
    }

    /// <summary>
    /// 数据源
    /// </summary>
    public enum WfSourceData
    {
        /// <summary>
        /// 项目列表
        /// </summary>
        [Description("项目列表")]
        ProjectList = 1,

        /// <summary>
        /// 待审批
        /// </summary>
        [Description("待审批列表")]
        Approval = 2,

        /// <summary>
        /// 已审批
        /// </summary>
        [Description("已审批列表")]
        Approvaled = 3,

        /// <summary>
        /// 项目清单
        /// </summary>
        [Description("项目清单列表")]
        ListItem = 4,
    }

    /// <summary>
    /// 查询类型
    /// </summary>
    public enum WfSearchType
    {
        /// <summary>
        /// 默认
        /// </summary>
        [Description("默认")]
        Default = 1,

        /// <summary>
        /// 自定义
        /// </summary>
        [Description("自定义")]
        Custome = 2,

    }

    /// <summary>
    /// 页面类型
    /// </summary>
    public enum WfPageType
    {
        /// <summary>
        /// 查询统计
        /// </summary>
        [Description("查询统计")]
        Search = 1,

        /// <summary>
        /// 待审批
        /// </summary>
        [Description("待审批")]
        Approval = 2,

        /// <summary>
        /// 已审批
        /// </summary>
        [Description("已审批")]
        Approvaled = 3,

    }

    /// <summary>
    /// 使用单位类型
    /// </summary>
    public enum WfUnittype
    {
        /// <summary>
        /// 市级
        /// </summary>
        [Description("市级")]
        City = 1,

        /// <summary>
        /// 区县
        /// </summary>
        [Description("区县")]
        County = 2,

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        School = 3,
    }


    /// <summary>
    /// 适用范围
    /// </summary>
    public enum WfUseType
    {
        /// <summary>
        /// 电脑端
        /// </summary>
        [Description("电脑端")]
        Computer = 1,

        /// <summary>
        /// 小程序
        /// </summary>
        [Description("小程序")]
        Applet = 2,

        /// <summary>
        /// 全部
        /// </summary>
        [Description("全部")]
        All = 3,
    }

    /// <summary>
    /// 排序规则
    /// </summary>
    public enum WfSortType
    {
        /// <summary>
        /// 升序
        /// </summary>
        [Description("升序(ASC)")]
        ASC = 1,

        /// <summary>
        /// 降序
        /// </summary>
        [Description("降序(DESC)")]
        DESC = 2,

        /// <summary>
        /// 多列排序
        /// </summary>
        [Description("多列排序")]
        MulColumn = 3,
    }

    /// <summary>
    /// 数据库类型
    /// </summary>
    public enum WfDataType
    {
        /// <summary>
        /// 数字
        /// </summary>
        [Description("数字")]
        Number = 1,

        /// <summary>
        /// 金额
        /// </summary>
        [Description("金额")]
        Money = 2,

        /// <summary>
        /// 文本
        /// </summary>
        [Description("文本")]
        Text = 3,

        /// <summary>
        /// 日期
        /// </summary>
        [Description("日期")]
        DateTime = 4,

        /// <summary>
        /// 按钮
        /// </summary>
        [Description("按钮")]
        Button = 5,

    }

    /// <summary>
    /// 所属菜单
    /// </summary>
    public enum WfMenuType
    {
        /// <summary>
        /// 待处理
        /// </summary>
        [Description("待处理")]
        Number = 1,

        /// <summary>
        /// 已处理
        /// </summary>
        [Description("已处理")]
        Money = 2,

    }

    /// <summary>
    /// 绑定的功能
    /// </summary>
    public enum WfBindFuc
    {
        /// <summary>
        /// 定义DataGrid列
        /// </summary>
        [Description("定义DataGrid列")]
        DgGridCol = 1,

        /// <summary>
        /// 详情、打印
        /// </summary>
        [Description("详情、打印")]
        DetailPrint = 2,
    }

    /// <summary>
    /// 输入框类型
    /// </summary>
    public enum WfTypeBox
    {
        /// <summary>
        /// 文本输入框
        /// </summary>
        [Description("文本输入框")]
        Text = 1,

        /// <summary>
        /// 字典表(B_Dictionary)下拉框组件
        /// </summary>
        [Description("字典表(B_Dictionary)下拉框组件")]
        DicBox = 2,

        /// <summary>
        /// 审批字典表(Wf_Dictionary)下拉框组件
        /// </summary>
        [Description("审批字典表(Wf_Dictionary)下拉框组件")]
        ProcessBox = 3,

        /// <summary>
        /// 联系人组件
        /// </summary>
        [Description("联系人组件")]
        LinkPersonBox = 4,

        /// <summary>
        /// 年度近三年
        /// </summary>
        [Description("年度近三年")]
        ThreeYearControl = 5,

        /// <summary>
        /// 年度当年
        /// </summary>
        [Description("年度当年")]
        CurrentYearControl = 7,

        /// <summary>
        /// 下一年
        /// </summary>
        [Description("下一年")]
        NextYearControl = 8,

        /// <summary>
        /// 资金来源组件
        /// </summary>
        [Description("资金来源组件")]
        SourceFundControl = 6,

        /// <summary>
        /// 本单位名称
        /// </summary>
        [Description("本单位名称")]
        UnitNameControl = 9,

        /// <summary>
        /// Div分割线
        /// </summary>
        [Description("Div分割线")]
        DivControl = 11,

        /// <summary>
        /// 多行文本框
        /// </summary>
        [Description("多行文本框")]
        MulText = 12,

        /// <summary>
        /// 帮助文字
        /// </summary>
        [Description("帮助文字")]
        HelpControl = 13,

        /// <summary>
        /// 编辑器
        /// </summary>
        [Description("编辑器")]
        EditControl = 14,

        /// <summary>
        /// 表格控件
        /// </summary>
        [Description("表格控件")]
        TableControl = 15,

        /// <summary>
        /// 开关
        /// </summary>
        [Description("开关")]
        SwitchControl = 16,

        /// <summary>
        /// 文件上传
        /// </summary>
        [Description("文件上传")]
        FileControl = 17,

        /// <summary>
        /// 日期控件
        /// </summary>
        [Description("日期控件")]
        DateControl = 18,

        /// <summary>
        /// 按钮
        /// </summary>
        [Description("按钮")]
        ButtonControl = 20,

        /// <summary>
        /// 状态下拉框组件
        /// </summary>
        [Description("状态下拉框组件")]
        StatuzControl = 21,

        /// <summary>
        /// 单位名称下拉框组件
        /// </summary>
        [Description("单位名称下拉框组件")]
        SchoolNameControl = 22,

        /// <summary>
        /// 项目清单组件
        /// </summary>
        [Description("项目清单组件")]
        ProjectListControl = 25,

        /// <summary>
        /// 项目清单审核组件
        /// </summary>
        [Description("项目清单审核组件")]
        ProjectListAuditControl =26,
    }

    /// <summary>
    /// 退回方式
    /// </summary>
    public enum WfBackWay
    {
        /// <summary>
        /// 退到源头
        /// </summary>
        [Description("退到源头")]
        ToSource = 1,

        /// <summary>
        /// 退到上一级 
        /// </summary>
        [Description("退到上一级 ")]
        PreNode = 2,

        ///// <summary>
        ///// 无
        ///// </summary>
        //[Description("无")]
        //NotHave = 3,

        ///// <summary>
        ///// 终止
        ///// </summary>
        //[Description("终止")]
        //End = 4,

    }


    /// <summary>
    /// 是否
    /// </summary>
    public enum WfYesNo
    {
        /// <summary>
        /// 是
        /// </summary>
        [Description("是")]
        Yes =1,

        /// <summary>
        /// 否 
        /// </summary>
        [Description("否")]
        No = 2,
    }

    /// <summary>
    /// 审批人员方式
    /// </summary>
    public enum WfApprovalMethod
    {
        /// <summary>
        /// 个人                  
        /// </summary>
        [Description("个人")]
        Person = 1,

        /// <summary>
        /// 部门
        /// </summary>
        [Description("部门")]
        Department = 2,

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        Unit = 3,
    }

    /// <summary>
    /// 列表操作按钮
    /// </summary>
    //public enum WfListButton
    //{

    //}
}
