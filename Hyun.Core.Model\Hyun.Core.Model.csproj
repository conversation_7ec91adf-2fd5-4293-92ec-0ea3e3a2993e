﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\build\common.targets" />

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>..\Hyun.Core.Api\Hyun.Core.Model.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>..\Hyun.Core\Hyun.Core.Model.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="ViewModels\View\**" />
	  <EmbeddedResource Remove="ViewModels\View\**" />
	  <None Remove="ViewModels\View\**" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
		<PackageReference Include="NPOI" Version="2.7.1" />		
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Models\RootTkey\Interface\" />
	  <Folder Include="Models\LowValue\" />
	  <Folder Include="Models\DangerChemicals\" />
	  <Folder Include="Models\Project\" />
	  <Folder Include="SearchModels\LowValue\" />
	  <Folder Include="SearchModels\DangerChemicals\" />
	  <Folder Include="SearchModels\Project\" />
	  <Folder Include="ViewModels\Project\" />
	  <Folder Include="ViewModels\LowValue\" />
	  <Folder Include="ViewModels\DangerChemicals\" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\OpenIddict.SqlSugar.Models\OpenIddict.SqlSugar.Models.csproj" />
	</ItemGroup>

</Project>
