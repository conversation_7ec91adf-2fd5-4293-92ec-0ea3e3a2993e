﻿using Hyun.Core.Model.Helper;
using Org.BouncyCastle.Asn1.Mozilla;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using NPOI.SS.UserModel;
using System.Web;

namespace Hyun.Core.Model
{

    ///<summary>
    ///校服采购
    ///</summary>
    public class XUniformPurchaseDto : BaseEntity
    {

        public XUniformPurchaseDto()
        {

        }
        /// <summary>
        ///  3：提交
        /// </summary>
        public int OptType { get; set; } = 1;
        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }
        /// <summary>
        /// 区县Id
        /// </summary>
        public long CountyId { get; set; }
        /// <summary>
        ///年度
        /// </summary>
        public int PurchaseYear { get; set; }

        /// <summary>
        ///合同批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        ///采购编码
        /// </summary>
        public string PurchaseCode { get; set; }

        /// <summary>
        ///是否续签合同
        /// </summary>
        public int IsContractRenewal { get; set; }

        /// <summary>
        ///合同开始时间
        /// </summary>
        public DateTime? ContractStartDate { get; set; }
        /// <summary>
        ///合同开始时间字符形式
        /// </summary>
        public string ContractStartDateStr { get; set; }
        /// <summary>
        ///合同终止时间
        /// </summary>
        public DateTime? ContractEndDate { get; set; }

        /// <summary>
        ///供应商
        /// </summary>
        public long? SupplierId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }

        /// <summary>
        ///采购组织形式名称
        /// </summary>
        public string OrganizationFormName { get; set; }

        /// <summary>
        ///采购组织形式
        /// </summary>
        public int? OrganizationForm { get; set; }

        /// <summary>
        ///备案状态(0:待备案 10:待审核 100:已备案)
        /// </summary>
        public int? FilingStatuz { get; set; }
        /// <summary>
        ///备案状态(0:待备案 10:待审核 100:已备案)
        /// </summary>
        public string FilingStatuzName { get; set; }
        /// <summary>
        ///审核时间
        /// </summary>
        public DateTime? FilingTime { get; set; }

        /// <summary>
        /// 是否审核 0：未审核 1：审核（经过区县审核的数据） 2：过期（超过一年）
        /// </summary>
        public int? IsFiling { get; set; }
        /// <summary>
        ///备案说明
        /// </summary>
        public string FilingExplanation { get; set; }
        /// <summary>
        /// 是否关联校服（0：未关联  1：关联（即审核校服通过，选择了该合同批次））
        /// </summary>
        public int IsRelatedUniform { get; set; }
        /// <summary>
        ///征订截止时间
        /// </summary>
        public DateTime? SubscriptionDeadline { get; set; }

        /// <summary>
        ///需订购人数
        /// </summary>
        public int OrderNum { get; set; }

        /// <summary>
        ///已订购人数
        /// </summary>
        public int OrderedNum { get; set; }

        /// <summary>
        ///征订状态（0：填报中  10：正在征订  100：征订结束）
        /// </summary>
        public int SubscriptionStatuz { get; set; }
        /// <summary>
        /// 征订状态名称
        /// </summary>
        public string SubscriptionStatuzName { get; set; }
        /// <summary>
        ///调换截止时间
        /// </summary>
        public DateTime? SwapDeadline { get; set; }

        /// <summary>
        ///总调换人数
        /// </summary>
        public int? SwapStudentNum { get; set; }

        /// <summary>
        ///资助人数
        /// </summary>
        public int? SponsorUserNum { get; set; }

        /// <summary>
        ///资助总金额
        /// </summary>
        public decimal? SponsorAmount { get; set; }

        /// <summary>
        ///资助来源名称
        /// </summary>
        public string SponsorSourceName { get; set; }

        /// <summary>
        ///资助时间
        /// </summary>
        public DateTime? SponsorTime { get; set; }

        /// <summary>
        ///评价截止日期
        /// </summary>
        public DateTime? EvaluateDeadline { get; set; }

        /// <summary>
        ///评价参与人数
        /// </summary>
        public int? EvaluateNum { get; set; }

        /// <summary>
        ///评价综合得分
        /// </summary>
        public decimal? EvaluateScore { get; set; }

        /// <summary>
        ///评价创建时间
        /// </summary>
        public DateTime? EvaluateTime { get; set; }


        /// <summary>
        ///合同主体Id
        /// </summary>
        public int? ContractMainBodyId { get; set; }

        /// <summary>
        ///合同主体
        /// </summary>
        public string ContractMainBody { get; set; }

        /// <summary>
        ///签约日期
        /// </summary>
        public DateTime? ContractSignDate { get; set; }

        /// <summary>
        ///支付方式Id
        /// </summary>
        public int? PayMethodId { get; set; }

        /// <summary>
        ///支付方式
        /// </summary>
        public string PayMethod { get; set; }

        /// <summary>
        ///供应商属地Id
        /// </summary>
        public int? SupplierLocationId { get; set; }

        /// <summary>
        ///供应商属地
        /// </summary>
        public string SupplierLocation { get; set; }
        /// <summary>
        ///供货期
        /// </summary>
        public int? GoodsDeadline { get; set; }

        /// <summary>
        ///质保月
        /// </summary>
        public int? WarrantyMonth { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        public string Memo { get; set; }

        /// <summary>
        ///订购人数
        /// </summary>
        public int? ContractPersonNum { get; set; }

        /// <summary>
        ///合同金额
        /// </summary>
        public decimal? ContractAmount { get; set; }

        /// <summary>
        ///校服采购表Id（x_UniformBuy）
        /// </summary>
        public long? UniformBuyId { get; set; }
        /// <summary>
        ///校服采购批次
        /// </summary>
        public string UniformBuyNo { get; set; }

        /// <summary>
        ///送货日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        ///验收日期
        /// </summary>
        public DateTime? AcceptanceDate { get; set; }

        /// <summary>
        ///供应商送检日期
        /// </summary>
        public DateTime? SupplierSendTestDate { get; set; }

        /// <summary>
        ///学校送检日期
        /// </summary>
        public DateTime? SchoolSendTestDate { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }
        /// <summary>
        /// 区县名称
        /// </summary>
        public string CountyName { get; set; }

        /// <summary>
        /// 附件集合
        /// </summary>
        public List<long> AttachmentIdList { get; set; }
        /// <summary>
        /// 是否区县认证0:未认证 1:认证 
        /// </summary>
        public int IsCountyManager { get; set; }

        /// <summary>
        /// 附件表id（b_Attachment）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long AttachmentId { get; set; }
        /// <summary>
        /// 获取选中的班级集合
        /// </summary>
        public List<int> GradeList { get; set; }
        /// <summary>
        /// 区县区域Id
        /// </summary>
        public long CountyAreaId { get; set; }
        /// <summary>
        /// 是否公示 0:未公示 1:公示
        /// </summary>
        public int IsPublicity { get; set; }
        /// <summary>
        /// 年级名称
        /// </summary>
        public string GradeName { get; set; }
        /// <summary>
        /// 班级名称
        /// </summary>
        public string ClassName { get; set; }
        /// <summary>
        /// 班级表Id
        /// </summary>
        public long ClassInfoId { get; set; }
    }

    /// <summary>
    /// 校服采购调换
    /// </summary>
    public class XUniformPurchaseSwapDto : BaseEntityDto
    {

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///年度
        /// </summary>
        public int PurchaseYear { get; set; }

        /// <summary>
        ///合同批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 调换开始时间
        /// </summary>
        public DateTime? SwapBegin { get; set; }

        /// <summary>
        /// 调换截止时间
        /// </summary>
        public DateTime? SwapDeadline { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public long? SupplierId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }

        /// <summary>
        /// 状态（1：待发起，2：已发起）
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        /// 订购总人数
        /// </summary>
        public int OrderedNum { get; set; }

        /// <summary>
        /// 总调换人数
        /// </summary>
        public int SwapStudentNum { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 调换状态（1：正在填报，2：调换结束）
        /// </summary>
        public int SwapStatuz { get; set; }


        /// <summary>
        /// 根据当前时间与调换结束时间比较，小于为正在填报，大于为调换结束
        /// </summary>
        public string StrSwapStatuz { get; set; }
    }

    /// <summary>
    /// 校服采购评价
    /// </summary>
    public class XUniformPurchaseEvaluateDto
    {

        /// <summary>
        /// 校服采购Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        ///年度
        /// </summary>
        public int PurchaseYear { get; set; }

        /// <summary>
        ///合同批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 合同终止时间
        /// </summary>
        public DateTime? ContractEndDate { get; set; }

        /// <summary>
        /// 征订截止时间
        /// </summary>
        public DateTime? SubscriptionDeadline { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }

        /// <summary>
        /// 评价截止日期
        /// </summary>
        public DateTime? EvaluateDeadline { get; set; }

        /// <summary>
        /// 评价参与人数
        /// </summary>
        public int EvaluateNum { get; set; }

        /// <summary>
        /// 评价综合得分
        /// </summary>
        public decimal EvaluateScore { get; set; }

        /// <summary>
        /// 评价状态（1：正在评价，2：评价结束）
        /// </summary>
        public int EvaluateStatuz { get; set; }


        /// <summary>
        /// 根据当前时间与评价结束时间比较，小于为正在评价，大于为评价结束
        /// </summary>
        public string StrEvaluateStatuz { get; set; }

        /// <summary>
        /// 是否续签合同
        /// </summary>
        public string ContractRenewal { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }


        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string AreaName { get; set; }
    }


    /// <summary>
    /// 发起修改实体
    /// </summary>
    public class XUniformPurchaseSwapModel : BaseEntityDto
    {

        /// <summary>
        /// 单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        /// 调换开始时间
        /// </summary>
        public DateTime SwapBegin { get; set; }

        /// <summary>
        /// 调换截止时间
        /// </summary>
        public DateTime SwapDeadline { get; set; }
    }

    /// <summary>
    /// 校服资助列表及详情
    /// </summary>
    public class XuniformSponsorDto : BaseEntityDto
    {
        /// <summary>
        /// 年度
        /// </summary>
        public int PurchaseYear { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        public string AreaName { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 合同批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 订购总人数
        /// </summary>
        public int OrderNum { get; set; }

        /// <summary>
        /// 资助人数
        /// </summary>
        public int SponsorUserNum { get; set; }

        /// <summary>
        /// 资助人数占比
        /// </summary>
        public string SponsorRatio { get; set; }

        /// <summary>
        /// 资助总额
        /// </summary>
        public string SponsorAmount { get; set; }

        /// <summary>
        /// 每人资助金额
        /// </summary>
        public string PersonSupport { get; set; }

        /// <summary>
        /// 资助来源
        /// </summary>
        public string SponsorSourceName { get; set; }


        /// <summary>
        /// 附件集合（详情使用）
        /// </summary>
        public List<BAttachment> ListAttachment { get; set; }
    }

    /// <summary>
    /// 校服资助创建修改实体
    /// </summary>
    public class XuniformSponsorModel : BaseEntityDto
    {
        /// <summary>
        /// 校服采购表Id
        /// </summary>
        public long UniformPurchaseId { get; set; }

        /// <summary>
        /// 每人资助金额(必传)
        /// </summary>
        public decimal PersonSupport { get; set; }

        /// <summary>
        /// 资助人数(必传)
        /// </summary>
        public int SponsorUserNum { get; set; }

        /// <summary>
        /// 资助来源(必传)
        /// </summary>
        public string SponsorSourceName { get; set; }

        /// <summary>
        /// 附件ID集合(必传)
        /// </summary>
        public List<long> ListAttachmentId { get; set; }
    }

    /// <summary>
    /// 采购单位详情
    /// </summary>
    public class XuniformPurchaseStudentModel : BaseEntity
    {
        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }
        /// <summary>
        /// 区县Id
        /// </summary>
        public long CountyId { get; set; }
        /// <summary>
        ///年度
        /// </summary>
        [Description("年度")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public int PurchaseYear { get; set; }

        /// <summary>
        ///合同批次
        /// </summary>
        [Description("合同批次")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        [Description("单位名称")]
        [ExportExcel(HorizontalAlignment.Center, 20)]
        public string SchoolName { get; set; }
        /// <summary>
        /// 区县名称
        /// </summary>
        [Description("区县名称")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string CountyName { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }
        /// <summary>
        /// 年级Id
        /// </summary>
        public int? GradeId { get; set; }
        /// <summary>
        /// 年级名称
        /// </summary>
        [Description("年级")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string GradeName { get; set; }
        /// <summary>
        /// 班级名称
        /// </summary>
        [Description("班级")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string ClassName { get; set; }
        /// <summary>
        /// 单位学号
        /// </summary>
        [Description("学号")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string StudentNo { get; set; }
        /// <summary>
        /// 学生姓名
        /// </summary>
        [Description("学生姓名")]
        [ExportExcel(HorizontalAlignment.Center, 20)]
        public string StudentName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        [Description("性别")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string Sex { get; set; }

        /// <summary>
        ///种类
        /// </summary>
        [Description("种类")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Uniformtype { get; set; }

        /// <summary>
        ///品名
        /// </summary>
        [Description("品名")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Name { get; set; }
        /// <summary>
        /// 尺码
        /// </summary>
        [Description("尺码")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string SizeDes { get; set; }
        /// <summary>
        /// 征订数量
        /// </summary>
        [Description("征订数量")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public int Num { get; set; }
        /// <summary>
        /// 征订数量
        /// </summary>
        [Description("征订数量")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string NumStr { get; set; }
        /// <summary>
        /// 征订价格
        /// </summary>
        [Description("单价")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public decimal Price { get; set; }

        [Description("单价")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string PriceStr { get; set; }

        private decimal _Amount { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        [Description("金额")]
        [ExportExcel(HorizontalAlignment.Right, 15)]
        public decimal Amount { get { return Math.Round(_Amount, 2); } set { _Amount = value; } }
        /// <summary>
        /// 金额
        /// </summary>
        [Description("金额")]
        [ExportExcel(HorizontalAlignment.Right, 15)]
        public string AmountStr { get; set; }
        /// <summary>
        /// 征订状态 1：已征订 2：未征订
        /// </summary>
        public int Statuz { get; set; }
        /// <summary>
        /// 征订状态 1：已征订 2：未征订
        /// </summary>
        [Description("状态")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string StatuzName { get; set; }

        /// <summary>
        /// 家长联系电话
        /// </summary>
        [Description("联系电话")]
        [ExportExcel(HorizontalAlignment.Left, 20)]
        public string ParentMobile { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string UnitName { get; set; }
    }
}

