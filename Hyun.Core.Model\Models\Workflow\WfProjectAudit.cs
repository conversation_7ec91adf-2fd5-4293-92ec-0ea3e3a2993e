﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///采购立项审批表
    ///</summary>
    [SugarTable("wf_ProjectAudit", "采购立项审批表")]
    public class WfProjectAudit : BaseEntity
    {

        public WfProjectAudit()
        {

        }

        /// <summary>
        ///流程配置表Id
        /// </summary>
        public long ProcessId { get; set; }

        /// <summary>
        ///立项申报Id
        /// </summary>
        public long ProjectDeclarationId { get; set; } = 0;

        /// <summary>
        ///单位Id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        /// 部门Id
        /// </summary>
        public long DepartmentId { get; set; }

        /// <summary>
        ///流程节点关系表Id
        /// </summary>
        public long ProcessNodeLinkId { get; set; } = 0;

        /// <summary>
        ///流程节点表Id
        /// </summary>
        public long ProcessNodeId { get; set; } = 0;

        /// <summary>
        ///审批状态结果（0：申报提交；1：通过；2：不通过；3：暂存）
        /// </summary>
        public int ApprovalStatuz { get; set; } = 0;

        /// <summary>
        ///是否显示(2:否   1：是)原则上都是需要显示的
        /// </summary>
        public int IsShow { get; set; } = 2;

        /// <summary>
        ///是否有效(2：无效   1：有效)
        ///因为一个节点的已审批对一个项目只能有一条记录，为了避免退回再次提交，
        ///多次审批造成的多条记录，所以此时将该字段设置为无效，
        ///已审批列表加载数据必须IsUsable = 1
        ///退回申报，则在转交下一步，将所有节点置为IsUsable=2
        ///退回上一步，直接将上一步对应的节点IsUsable=2
        /// </summary>
        public int IsUsable { get; set; } = 2;

        /// <summary>
        ///是否可撤回（2：否；1：是）
        /// </summary>
        public int IsWithdraw { get; set; } = 2;

        /// <summary>
        ///FromId：主表FromId的镜像，非主表更新后的值
        /// </summary>
        public long FromId { get; set; } = 0;

        /// <summary>
        ///To：主表ToId的镜像，非主表更新后的值
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long ToId { get; set; } = 0;

        /// <summary>
        ///流程审批上一流程在本表中id
        /// </summary>
        public long PreProjectAuditId { get; set; } = 0;

        /// <summary>
        ///审批人员类型（1：所有人审批，  2指定单人审批    3：指定多人都审批）
        /// </summary>
        public int ApprovalType { get; set; } = 1;

        /// <summary>
        ///审批序号
        /// </summary>
        public int ApprovalNo { get; set; } = 0;

        /// <summary>
        ///项目审批过程状态（表xa_ProcessCurrenStatuz 中Statuz的值)
        /// </summary>
        public int ProcessStatuz { get; set; } = 0;

        /// <summary>
        ///FormJson
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        public string FormContent { get; set; }

        /// <summary>
        ///数据Json
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        public string DataContent { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
    }


}

