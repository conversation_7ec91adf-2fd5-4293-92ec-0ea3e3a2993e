using System.ComponentModel;

namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///校服班级表
    ///</summary>
    [SugarTable("p_ClassInfo", "校服班级表")]
    public class PClassInfo : BaseEntity
    {

        public PClassInfo()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        /// 单位属性
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int SchoolStage { get; set; }

        /// <summary>
        ///入学年份
        /// </summary>
        public int StartYear { get; set; }

        /// <summary>
        ///年级名称
        /// </summary>
        [SugarColumn(Length = 31)]
        public string GradeName { get; set; }

        /// <summary>
        ///年级
        /// </summary>
        public int GradeId { get; set; }

        /// <summary>
        ///班级
        /// </summary>
        [SugarColumn(Length = 31)]
        public string ClassName { get; set; }

        /// <summary>
        ///学生数
        /// </summary>
        public int StudentNum { get; set; }

        /// <summary>
        /// 是否毕业（0：否，1：是）默认否
        /// </summary>

        public int IsGraduate { get; set; } = 0;

        /// <summary>
        ///班主任
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string TeacherName { get; set; }

        /// <summary>
        ///手机
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string TeacherMobile { get; set; }


        ///// <summary>
        ///// 学段
        ///// </summary>
        //[SugarColumn(Length = 255, IsNullable = true)]
        //public string Period { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 原年级Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int OldGradeId { get; set; }

        /// <summary>
        /// 年级名称
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string OldGradeName { get; set; }

        /// <summary>
        ///班级
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string OldClassName { get; set; }

    }


}

