using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using SqlSugar;
using Yitter.IdGenerator;
using Hyun.Core.Common;

namespace Hyun.Core.Common.DB
{
    public class BaseDBConfig
    {
        private static readonly object _idLock = new object();
        private static long _fallbackCounter = 0;

        /// <summary>
        /// 所有库配置
        /// </summary>
        public static readonly List<ConnectionConfig> AllConfigs = new();

        /// <summary>
        /// 主库的备用连接配置
        /// </summary>
        public static readonly List<ConnectionConfig> ReuseConfigs = new();

        /// <summary>
        /// 有效的库连接(除去Log库)
        /// </summary>
        public static List<ConnectionConfig> ValidConfig = new();

        public static ConnectionConfig MainConfig;
        public static ConnectionConfig LogConfig; //日志库

        public static bool IsMulti => ValidConfig.Count > 1;

        /// <summary>
        /// 获取雪花ID，根据配置选择生成策略
        /// </summary>
        /// <returns></returns>
        public static long GetYitterId()
        {
            // 检查是否启用高性能模式
            var useHighPerformanceMode = GetConfigBool("SnowIdOptions", "UseHighPerformanceMode", false);
            if (useHighPerformanceMode)
            {
                return GetFastId();
            }

            try
            {
                // 直接调用，不使用锁，提高性能
                return YitIdHelper.NextId();
            }
            catch (Exception ex)
            {
                // 检查是否启用日志记录
                var enableLogging = GetConfigBool("SnowIdOptions", "EnableFallbackLogging", true);
                if (enableLogging)
                {
                    Console.WriteLine($"YitIdHelper.NextId() 异常: {ex.Message}");
                }

                // 使用备用ID生成策略，这里需要锁保护
                lock (_idLock)
                {
                    var fallbackId = GenerateFallbackId();
                    if (enableLogging)
                    {
                        Console.WriteLine($"使用备用ID生成策略，生成ID: {fallbackId}");
                    }
                    return fallbackId;
                }
            }
        }

        /// <summary>
        /// 快速生成ID，用于高频场景，直接使用备用算法
        /// </summary>
        /// <returns></returns>
        public static long GetFastId()
        {
            lock (_idLock)
            {
                return GenerateFallbackId();
            }
        }

        /// <summary>
        /// 生成备用ID，确保唯一性
        /// 使用高性能的ID生成算法
        /// </summary>
        /// <returns></returns>
        private static long GenerateFallbackId()
        {
            // 使用时间戳 + WorkerId + 计数器确保唯一性和高性能
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var workerId = GetWorkerId(); // 获取当前WorkerId
            var counter = Interlocked.Increment(ref _fallbackCounter) % 4096; // 0-4095的计数器，12位

            // 雪花算法格式：时间戳(41位) + WorkerId(10位) + 序列号(12位)
            // 为了简化，我们使用：时间戳(42位) + WorkerId(10位) + 计数器(12位)
            return ((timestamp - 1288834974657L) << 22) | (workerId << 12) | counter;
        }

        /// <summary>
        /// 获取WorkerId，如果配置不存在则使用默认值
        /// </summary>
        /// <returns></returns>
        private static long GetWorkerId()
        {
            try
            {
                var workIdStr = AppSettings.app(new string[] { "SnowIdOptions", "WorkerId" });
                if (!string.IsNullOrEmpty(workIdStr) && long.TryParse(workIdStr, out long workId))
                {
                    return workId & 0x3FF; // 确保在10位范围内 (0-1023)
                }
            }
            catch
            {
                // 忽略配置读取异常
            }
            return 2; // 默认WorkerId
        }

        /// <summary>
        /// 获取布尔配置值
        /// </summary>
        /// <param name="section">配置节</param>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns></returns>
        private static bool GetConfigBool(string section, string key, bool defaultValue)
        {
            try
            {
                var value = AppSettings.app(new string[] { section, key });
                if (!string.IsNullOrEmpty(value) && bool.TryParse(value, out bool result))
                {
                    return result;
                }
            }
            catch
            {
                // 忽略配置读取异常
            }
            return defaultValue;
        }

        /// <summary>
        /// 测试ID生成性能和唯一性（仅用于调试）
        /// </summary>
        /// <param name="count">生成数量</param>
        /// <returns>性能统计信息</returns>
        public static string TestIdGeneration(int count = 10000)
        {
            var sw = System.Diagnostics.Stopwatch.StartNew();
            var ids = new HashSet<long>();
            var duplicates = 0;

            for (int i = 0; i < count; i++)
            {
                var id = GetFastId();
                if (!ids.Add(id))
                {
                    duplicates++;
                }
            }

            sw.Stop();
            return $"生成{count}个ID，耗时{sw.ElapsedMilliseconds}ms，重复{duplicates}个，平均{(double)sw.ElapsedMilliseconds / count:F4}ms/个";
        }

        /* 之前的单库操作已经删除，如果想要之前的代码，可以查看我的GitHub的历史记录
         * 目前是多库操作，默认加载的是appsettings.json设置为true的第一个db连接。
         *
         * 优化配置连接
         * 老的配置方式,再多库和从库中有些冲突
         * 直接在单个配置中可以配置从库
         *
         * 新增故障转移方案
         * 增加主库备用连接,配置方式为ConfigId为主库的ConfigId+随便数字 只要不重复就好
         *
         * 主库在无法连接后会自动切换到备用链接
         */
        public static (List<MutiDBOperate> allDbs, List<MutiDBOperate> slaveDbs) MutiConnectionString => MutiInitConn();

        private static string DifDBConnOfSecurity(params string[] conn)
        {
            foreach (var item in conn)
            {
                try
                {
                    if (File.Exists(item))
                    {
                        return File.ReadAllText(item).Trim();
                    }
                }
                catch (System.Exception)
                {
                }
            }

            return conn[conn.Length - 1];
        }


        public static (List<MutiDBOperate>, List<MutiDBOperate>) MutiInitConn()
        {
            List<MutiDBOperate> listdatabase = AppSettings.app<MutiDBOperate>("DBS")
                .Where(i => i.Enabled).ToList();
            var mainDbId = AppSettings.app(new string[] {"MainDB"}).ObjToString();
            var mainDbModel = listdatabase.Single(d => d.ConnId == mainDbId);
            listdatabase.Remove(mainDbModel);
            listdatabase.Insert(0, mainDbModel);

            foreach (var i in listdatabase) SpecialDbString(i);
            return (listdatabase, mainDbModel.Slaves);
        }

        /// <summary>
        /// 定制Db字符串
        /// 目的是保证安全：优先从本地txt文件获取，若没有文件则从appsettings.json中获取
        /// </summary>
        /// <param name="mutiDBOperate"></param>
        /// <returns></returns>
        private static MutiDBOperate SpecialDbString(MutiDBOperate mutiDBOperate)
        {
            if (mutiDBOperate.DbType == DataBaseType.Sqlite)
            {
                mutiDBOperate.Connection =
                    $"DataSource=" + Path.Combine(Environment.CurrentDirectory, mutiDBOperate.Connection);
            }
            else if (mutiDBOperate.DbType == DataBaseType.SqlServer)
            {
                mutiDBOperate.Connection = DifDBConnOfSecurity(@"D:\my-file\dbCountPsw1_SqlserverConn.txt",
                    mutiDBOperate.Connection);
            }
            else if (mutiDBOperate.DbType == DataBaseType.MySql)
            {
                mutiDBOperate.Connection =
                    DifDBConnOfSecurity(@"D:\my-file\dbCountPsw1_MySqlConn.txt", mutiDBOperate.Connection);
            }
            else if (mutiDBOperate.DbType == DataBaseType.Oracle)
            {
                mutiDBOperate.Connection =
                    DifDBConnOfSecurity(@"D:\my-file\dbCountPsw1_OracleConn.txt", mutiDBOperate.Connection);
            }

            return mutiDBOperate;
        }

    }


    public enum DataBaseType
    {
        MySql = 0,
        SqlServer = 1,
        Sqlite = 2,
        Oracle = 3,
        PostgreSQL = 4,
        Dm = 5,
        Kdbndp = 6,
    }

    public class MutiDBOperate
    {
        /// <summary>
        /// 连接启用开关
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// 连接ID
        /// </summary>
        public string ConnId { get; set; }

        /// <summary>
        /// 从库执行级别，越大越先执行
        /// </summary>
        public int HitRate { get; set; }

        /// <summary>
        /// 连接字符串
        /// </summary>
        public string Connection { get; set; }

        /// <summary>
        /// 数据库类型
        /// </summary>
        public DataBaseType DbType { get; set; }

        /// <summary>
        /// 从库
        /// </summary>
        public List<MutiDBOperate> Slaves { get; set; }
    }
}