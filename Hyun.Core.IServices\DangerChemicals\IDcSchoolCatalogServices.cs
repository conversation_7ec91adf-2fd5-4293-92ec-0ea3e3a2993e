﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcSchoolCatalog接口方法
    ///</summary>
    public interface IDcSchoolCatalogServices : IBaseServices<DcSchoolCatalog>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<DcSchoolCatalog> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<DcSchoolCatalog>> Find(Expression<Func<DcSchoolCatalog, bool>> expression);
        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">DcSchoolCatalogParam实体参数</param>
        /// <returns></returns>
        Task<List<DcSchoolCatalog>> Find(DcSchoolCatalogParam param);
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcSchoolCatalogParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<DcSchoolCatalog>> GetPaged(DcSchoolCatalogParam param);
        /// <summary>
        /// 获取区县分类
        /// </summary>
        /// <param name="basecatalogid">基础分类设置</param>
        /// <returns></returns>
        Task<List<DcSchoolCatalog>> GetCountyCatalogList(long basecatalogid);
       //<used>0</used>
       Task<Result> InitData(long schoolId, long unitId, long userId);

       //<used>0</used>
       Task<Result> InsertUpdate(int id, int pid, string name, string brief, string unitsMeasurement, int nature, int isNeedBack, int statuz, int isCommonUse, int unitId, int userId);

       //<used>1</used>
       Task<List<DcSchoolCatalogDto>> PurchaseListByCatalog_Find(DcSchoolCatalogParam param);

       //<used>0</used>
       Task<int> UpdateIsCommonUseByIds(string ids, int isCommonUse, int unitId);

        #region 查询统计
        /// <summary>
        /// 单位危化品库统计列表(汇总) (原：V_dc_MaterialStatisticsList GetPaged)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcSchoolCatalogList>> GetStatisticsPaged(VDcSchoolCatalogListParam param);
        /// <summary>
        /// 危化品标准库一二级分类
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<List<VDcSchoolCatalogParentList>> FindParent(VDcSchoolCatalogParentListParam param);
        /// <summary>
        /// 危化品标准库一二级分类
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcSchoolCatalogParentList>> GetParentPaged(VDcSchoolCatalogParentListParam param);
        /// <summary>
        /// 危化品标准库一二级分类
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcSchoolCatalog>> GetTwoPaged(VDcSchoolCatalogParam param);
        #endregion
    }
}

