namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///101单位p_Unit
    ///</summary>
    [SugarTable("p_Unit", "WMHYUN_MSSQL_1")]
    public class PUnit : BaseEntity
    {

        public PUnit()
        {

        }

        /// <summary>
        ///区域Id
        /// </summary>
        public long AreaId { get; set; }

        /// <summary>
        ///父级单位Id
        /// </summary>
        public long PId { get; set; }

        /// <summary>
        ///性质类型：1市级、2区县、3单位、4企业
        /// </summary>
        public int UnitType { get; set; }

        /// <summary>
        ///行业类型：普教、高教、职教、机关、家庭
        /// </summary>
        public int IndustryId { get; set; }

        /// <summary>
        ///单位编号
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string Code { get; set; }

        /// <summary>
        ///名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        ///简称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Brief { get; set; }

        /// <summary>
        ///全拼
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string PinYin { get; set; }

        /// <summary>
        ///简拼
        /// </summary>
        [SugarColumn(Length = 15, IsNullable = true)]
        public string PinYinBrief { get; set; }

        /// <summary>
        ///法人
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string Legal { get; set; }

        /// <summary>
        ///地址
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Address { get; set; }

        /// <summary>
        ///邮编
        /// </summary>
        [SugarColumn(Length = 6, IsNullable = true)]
        public string ZipCode { get; set; }

        /// <summary>
        ///网址
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Url { get; set; }

        /// <summary>
        ///简介
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string Introduction { get; set; }

        /// <summary>
        ///Logo
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Logo { get; set; }

        /// <summary>
        ///VIP等级
        /// </summary>
        public int VipGrade { get; set; }

        /// <summary>
        ///组织机构代码
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string OrganizationCode { get; set; }

        /// <summary>
        ///登录界面图片
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string LoginPic { get; set; }

        /// <summary>
        ///短信服务手机号
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string Mobile { get; set; }

        /// <summary>
        ///联系人
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string ContactUser { get; set; }

        /// <summary>
        ///联系电话
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string Tel { get; set; }

        /// <summary>
        ///邮箱
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Email { get; set; }

        /// <summary>
        ///地理位置打点
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Position { get; set; }

        /// <summary>
        ///交通路线图
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string TrafficMap { get; set; }

        /// <summary>
        ///员工数
        /// </summary>
        public int EmployeeNum { get; set; }

        /// <summary>
        ///添加人
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///添加时间
        /// </summary>
        public DateTime RegTime { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        ///状态（0：待审核，1：正常，2：待提交审核）
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        ///来源类型（1：企业注册  2：程序自动添加  3：程序自动添加后企业又来平台注册）默认1，主要用于企业
        /// </summary>
        public int SourceType { get; set; }

        /// <summary>
        ///区域街道、镇名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string TownName { get; set; }

        /// <summary>
        ///第三方单位编码
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ThirdUnitId { get; set; }

        /// <summary>
        ///第三方单位名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ThirdUnitName { get; set; }

        /// <summary>
        ///主体性质（主要用于性质类型为单位的单位）对应字典表TypeCode为15000值
        /// </summary>
        public int SubjectNature { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        [SugarColumn(IsIgnore = true)]
        public int Mileage { get; set; }


        /// <summary>
        /// 班级数数，for 单位更新
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int ClassNum { get; set; }
        /// <summary>
        /// 在校生数，for 单位更新
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int StudentNum { get; set; }

        /// <summary>
        /// 教职工数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int TeacherNum { get; set; }

        /// <summary>
        /// 占地面积
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal FloorArea { get; set; }

        /// <summary>
        /// 建筑面积
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal BuildArea { get; set; }

        /// <summary>
        /// 学段
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int SchoolStage { get; set; }

        /// <summary>
        /// 学段名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrSchoolStage { get; set; }

        /// <summary>
        /// 单位管理员
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SchoolAdmin { get; set; }

        /// <summary>
        /// 单位管理员手机号码
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string AdminMobile { get; set; }

        /// <summary>
        /// 单位Guid
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SchoolGuid { get; set; }

        /// <summary>
        /// 单位性质(1：公办校，2：民办校，3：其他)
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int SchoolNature { get; set; }

        /// <summary>
        /// 街道镇Id，对应Area表Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long StreetTown { get; set; } = 0;



        /// <summary>
        /// 企业是否服务商
        /// 2017-11-09 update by jiangpeng
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsServiceProvider { get; set; } = false;
        /// <summary>
        /// 企业是否供应商
        /// 2017-11-09 update by jiangpeng
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsSupplier { get; set; } = false;

        /// <summary>
        /// 区县是否管理单位（0：否，1：是）默认0
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int IsCountyManager { get; set; } = 0;

        /// <summary>
        ///  认证状态（0：待认证  1：已认证）
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int AuthStatuz { get; set; } = 0;
    }

}

