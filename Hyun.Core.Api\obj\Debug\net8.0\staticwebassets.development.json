{"ContentRoots": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\"], "Root": {"Children": {"CorsPost.html": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "CorsPost.html"}, "Patterns": null}, "css": {"Children": {"site.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "css/site.css"}, "Patterns": null}, "style.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "css/style.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Download": {"Children": {"SchoolAddress.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/SchoolAddress.xlsx"}, "Patterns": null}, "SchoolUserThirdAllow.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/SchoolUserThirdAllow.xlsx"}, "Patterns": null}, "ThEquipmentCategory.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/ThEquipmentCategory.xlsx"}, "Patterns": null}, "Unit.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/Unit.xlsx"}, "Patterns": null}, "UnitCompany.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/UnitCompany.xlsx"}, "Patterns": null}, "UnitSchool.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/UnitSchool.xlsx"}, "Patterns": null}, "User.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/User.xlsx"}, "Patterns": null}, "UserMine.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/UserMine.xlsx"}, "Patterns": null}, "UserThirdAllow.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/UserThirdAllow.xlsx"}, "Patterns": null}, "关于征订校服的征求家长意见书.docx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/关于征订校服的征求家长意见书.docx"}, "Patterns": null}, "学生导入信息.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/学生导入信息.xlsx"}, "Patterns": null}, "班级导入信息.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Download/班级导入信息.xlsx"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "HyunCore.Data.excel": {"Children": {"Department.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.excel/Department.xlsx"}, "Patterns": null}, "Modules.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.excel/Modules.xlsx"}, "Patterns": null}, "Permission.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.excel/Permission.xlsx"}, "Patterns": null}, "Role.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.excel/Role.xlsx"}, "Patterns": null}, "RoleModulePermission.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.excel/RoleModulePermission.xlsx"}, "Patterns": null}, "SysUserInfo.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.excel/SysUserInfo.xlsx"}, "Patterns": null}, "UserRole.xlsx": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.excel/UserRole.xlsx"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "HyunCore.Data.json": {"Children": {"BlogArticle.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/BlogArticle.tsv"}, "Patterns": null}, "Department.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/Department.tsv"}, "Patterns": null}, "Modules.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/Modules.tsv"}, "Patterns": null}, "Permission.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/Permission.tsv"}, "Patterns": null}, "Role.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/Role.tsv"}, "Patterns": null}, "RoleModulePermission.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/RoleModulePermission.tsv"}, "Patterns": null}, "sysUserInfo.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/sysUserInfo.tsv"}, "Patterns": null}, "TasksQz.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/TasksQz.tsv"}, "Patterns": null}, "Topic.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/Topic.tsv"}, "Patterns": null}, "TopicDetail.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/TopicDetail.tsv"}, "Patterns": null}, "UserRole.tsv": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "HyunCore.Data.json/UserRole.tsv"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "index.html": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "index.html"}, "Patterns": null}, "JMeterTest.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "JMeterTest.png"}, "Patterns": null}, "js": {"Children": {"anime.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/anime.min.js"}, "Patterns": null}, "jquery-3.3.1.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/jquery-3.3.1.min.js"}, "Patterns": null}, "site.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/site.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "lib": {"Children": {"bootstrap": {"Children": {"dist": {"Children": {"css": {"Children": {"bootstrap-grid.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.css"}, "Patterns": null}, "bootstrap-grid.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, "Patterns": null}, "bootstrap-grid.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, "Patterns": null}, "bootstrap-grid.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, "Patterns": null}, "bootstrap-grid.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, "Patterns": null}, "bootstrap-grid.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, "Patterns": null}, "bootstrap-grid.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, "Patterns": null}, "bootstrap-grid.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, "Patterns": null}, "bootstrap-reboot.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, "Patterns": null}, "bootstrap-reboot.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, "Patterns": null}, "bootstrap-reboot.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, "Patterns": null}, "bootstrap-reboot.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, "Patterns": null}, "bootstrap-reboot.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, "Patterns": null}, "bootstrap-reboot.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, "Patterns": null}, "bootstrap-reboot.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, "Patterns": null}, "bootstrap-reboot.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, "Patterns": null}, "bootstrap-utilities.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, "Patterns": null}, "bootstrap-utilities.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, "Patterns": null}, "bootstrap-utilities.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, "Patterns": null}, "bootstrap-utilities.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, "Patterns": null}, "bootstrap-utilities.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, "Patterns": null}, "bootstrap-utilities.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, "Patterns": null}, "bootstrap-utilities.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, "Patterns": null}, "bootstrap-utilities.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, "Patterns": null}, "bootstrap.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.css"}, "Patterns": null}, "bootstrap.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.css.map"}, "Patterns": null}, "bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, "Patterns": null}, "bootstrap.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, "Patterns": null}, "bootstrap.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, "Patterns": null}, "bootstrap.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, "Patterns": null}, "bootstrap.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {"bootstrap.bundle.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, "Patterns": null}, "bootstrap.bundle.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, "Patterns": null}, "bootstrap.bundle.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, "Patterns": null}, "bootstrap.bundle.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, "Patterns": null}, "bootstrap.esm.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.js"}, "Patterns": null}, "bootstrap.esm.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, "Patterns": null}, "bootstrap.esm.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, "Patterns": null}, "bootstrap.esm.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, "Patterns": null}, "bootstrap.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.js"}, "Patterns": null}, "bootstrap.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.js.map"}, "Patterns": null}, "bootstrap.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.min.js"}, "Patterns": null}, "bootstrap.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "LICENSE": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/LICENSE"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "logo.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "logo.png"}, "Patterns": null}, "logo": {"Children": {"favicon-32x32.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "logo/favicon-32x32.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Resource": {"Children": {"Export": {"Children": {"Excel": {"Children": {"预算清单.xls": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Resource/Export/Excel/预算清单.xls"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "swg-login.html": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "swg-login.html"}, "Patterns": null}, "uploadfile": {"Children": {"101001": {"Children": {"20241218": {"Children": {"2412183a79082c364e45fe8d3a94764114afe3.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/101001/20241218/2412183a79082c364e45fe8d3a94764114afe3.png"}, "Patterns": null}, "24121873205bc2bf804e0da229dfc805527d8d.jpeg": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/101001/20241218/24121873205bc2bf804e0da229dfc805527d8d.jpeg"}, "Patterns": null}, "2412189e78b12084924d4681c65e81c7b1e001.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/101001/20241218/2412189e78b12084924d4681c65e81c7b1e001.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "104001": {"Children": {"20250506": {"Children": {"250506254352c38b6f4d9a9c84e2d965a2ee66.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/104001/20250506/250506254352c38b6f4d9a9c84e2d965a2ee66.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "600001": {"Children": {"20250805": {"Children": {"2508051c8c56052f414adbbb7d100e162b5367.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/600001/20250805/2508051c8c56052f414adbbb7d100e162b5367.png"}, "Patterns": null}, "25080527e597a48e54481aa6d3ae2b4d554d9c.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/600001/20250805/25080527e597a48e54481aa6d3ae2b4d554d9c.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "600002": {"Children": {"20250805": {"Children": {"250805207f0c42701c49a281162398af8ce25c.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/600002/20250805/250805207f0c42701c49a281162398af8ce25c.png"}, "Patterns": null}, "2508052db62f35d6c749e6a07ecbb5b4865268.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploadfile/600002/20250805/2508052db62f35d6c749e6a07ecbb5b4865268.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "web.config": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "web.config"}, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 0, "Pattern": "**", "Depth": 0}]}}