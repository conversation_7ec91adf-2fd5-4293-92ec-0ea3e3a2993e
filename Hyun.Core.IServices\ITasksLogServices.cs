﻿
using Hyun.Core.IServices.BASE;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.SearchModels.Tasks;
using System;
using System.Threading.Tasks;

namespace Hyun.Core.IServices
{	
	/// <summary>
	/// ITasksLogServices
	/// </summary>	
    public interface ITasksLogServices :IBaseServices<TasksLog>
	{
		public Task<PageModel<TasksLog>> GetTaskLogs(TasksLogParam param);
        public Task<object> GetTaskOverview(long jobId, DateTime? runTime, DateTime? endTime, string type);
    }
}
                    