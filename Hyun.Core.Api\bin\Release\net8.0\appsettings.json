{
    "urls": "http://*:9291", //web服务端口，如果用IIS部署，把这个去掉
    "Serilog": {
        "MinimumLevel": {
            "Default": "Debug",
            "Override": {
                "Microsoft": "Information",
                "Microsoft.AspNetCore": "Warning",
                "System": "Warning",
                "System.Net.Http.HttpClient": "Warning",
                "Hangfire": "Information",
                "Magicodes": "Warning",
                "DotNetCore.CAP": "Information",
                "Savorboard.CAP": "Information",
                "Quartz": "Information"
            }
        }
    },
    "AllowedHosts": "*",
    "Redis": {
        "Enable": false,
        "ConnectionString": "127.0.0.1:6379",
        "InstanceName": "" //前缀
    },
    "AuthCenter": {
        "Authority": "https://localhost:7280/"
    },
    "RabbitMQ": {
        "Enabled": true,
        "Connection": "101xxxx57",
        "UserName": "xxxx",
        "Password": "xxxxx",
        "Port": "5672",
        "RetryCount": 2
    },
    "Kafka": {
        "Enabled": false,
        "Servers": "localhost:9092",
        "Topic": "blog",
        "GroupId": "blog-consumer",
        "NumPartitions": 3 //主题分区数量
    },
    "EventBus": {
        "Enabled": false,
        "SubscriptionClientName": "Hyun.Core"
    },
    "AppSettings": {
        "CachingAOP": {
            "Enabled": true
        },
        "LogToDb": true,
        "LogAOP": {
            "Enabled": true,
            "LogToFile": {
                "Enabled": true
            },
            "LogToDB": {
                "Enabled": true
            }
        },
        "TranAOP": {
            "Enabled": true
        },
        "UserAuditAOP": {
            "Enabled": false
        },
        "SqlAOP": {
            "Enabled": true,
            "LogToFile": {
                "Enabled": true
            },
            "LogToDB": {
                "Enabled": true
            },
            "LogToConsole": {
                "Enabled": true
            }
        },
        "Date": "2018-08-28",
        "SeedDBEnabled": false, //只生成表结构
        "SeedDBDataEnabled": false, //生成表,并初始化数据
        "Author": "Hyun.Core",
        "SvcName": "", // /svc/blog
        "UseLoadTest": false
    },

    //优化DB配置、不会再区分单库多库
    //MainDb：标识当前项目的主库，所对应的连接字符串的Enabled必须为true
    //Log:标识日志库，所对应的连接字符串的Enabled必须为true
    //从库只需配置Slaves数组,要求数据库类型一致!，比如都是SqlServer
    //
    //新增,故障转移方案
    //如果主库挂了,会自动切换到备用连接(比如说主库+备用库)
    //备用连接的ConnId配置为主库的ConnId+数字即可,比如主库的ConnId为Main,那么备用连接的ConnId为Mian1
    //主库、备用库无需数据库类型一致!
    //备用库不会有程序维护,需要手动维护
    //"MainDB": "WM****_DM", //当前项目的主库，所对应的连接字符串的Enabled必须为true
    "MainDB": "WM****_MSSQL_1", //当前项目的主库，所对应的连接字符串的Enabled必须为true
    "DBS": [
        /*
      对应下边的 DBType
      MySql = 0,
      SqlServer = 1,
      Sqlite = 2,
      Oracle = 3,
      PostgreSQL = 4,
      Dm = 5,//达梦
      Kdbndp = 6,//人大金仓
    */
        {
            "ConnId": "Main",
            "DBType": 2,
            "Enabled": false,
            "Connection": "WMBlog.db", //sqlite只写数据库名就行
            "Slaves": [
                {
                    "HitRate": 0, // 值越大，优先级越高 0不使用
                    "Connection": "WMBlog2.db"
                }
            ]
        },
        {
            "ConnId": "Main2",
            "DBType": 2,
            "Enabled": false,
            "Connection": "WMBlog3.db", //sqlite只写数据库名就行
            "Slaves": [
                {
                    "HitRate": 0, // 值越大，优先级越高 0不使用
                    "Connection": "WMBlog4.db"
                }
            ]
        },
        //{
        //  "ConnId": "Log", //日志库连接固定名称，不要改,其他的可以改
        //  "DBType": 2,
        //  "Enabled": true,
        //  "HitRate": 50,
        //  "Connection": "WMBlogLog.db" //sqlite只写数据库名就行
        //},
        {
            "ConnId": "Log", //日志库连接固定名称，不要改,其他的可以改
            "DBType": 1,
            "Enabled": true,
            "HitRate": 50,
            "Connection": "Data Source=***********;Initial Catalog=HyunCoreLog;User ID=****;Password=****;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False",
            "ProviderName": "System.Data.SqlClient"
        },
      {
        "ConnId": "WM****_MSSQL_1",
        "DBType": 1,
        "Enabled": true,
        "Connection": "Data Source=***********;Initial Catalog=HyunXfCore;User ID=****;Password=****;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False",
        "ProviderName": "System.Data.SqlClient"
      },
        {
            "ConnId": "WM****_MSSQL_2",
            "DBType": 1,
            "Enabled": false,
            "Connection": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=WM****_MSSQL_2;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False",
            "ProviderName": "System.Data.SqlClient"
        },
        {
            "ConnId": "WM****_MYSQL",
            "DBType": 0,
            "Enabled": false,
            "Connection": "server=localhost;Database=blog;Uid=root;Pwd=root;Port=3306;Allow User Variables=True;"
        },
        {
            "ConnId": "WM****_MYSQL_2",
            "DBType": 0,
            "Enabled": false,
            "Connection": "server=localhost;Database=blogcore001;Uid=root;Pwd=root;Port=3306;Allow User Variables=True;"
        },
        {
            "ConnId": "WM****_ORACLE",
            "DBType": 3,
            "Enabled": false,
            "Connection": "Data Source=127.0.0.1/ops;User ID=OPS;Password=******;Persist Security Info=True;Connection Timeout=60;"
        },
        {
            "ConnId": "WM****_DM",
            "DBType": 5,
            "Enabled": false,
            "Connection": "Server=***********:5236;User Id=****;PWD=****;SCHEMA=JunfeiYanfa;"
        },
        {
            "ConnId": "WM****_KDBNDP",
            "DBType": 6,
            "Enabled": false,
            "Connection": "Server=127.0.0.1;Port=54321;UID=SYSTEM;PWD=system;database=SQLSUGAR4XTEST1;"
        }
    ],
    "SnowIdOptions": {
        "WorkerId": 2 //1~64 ushort 类型
    },
  "Audience": {
    "Secret": "sdfsdfsrty45634kkhllghtdgdfss345t678fs", //不要太短，16位+
    "SecretFile": "C:\\my-file\\Hyun.Core.audience.secret.txt", //安全。内容就是Secret
    //"SecretFile": "/etc/****/core/audience.secret.txt", // Linux
    "Issuer": "Hyun.Core", //这个值一定要在自己的项目里修改！！
    "Audience": "Hyun", //这个值一定要在自己的项目里修改！！
    "Expiration": 7200 //JWT过期时间（秒），2小时 = 7200秒
  },
    "Mongo": {
        "ConnectionString": "mongodb://nosql.data",
        "Database": "HyunCoreDb"
    },
    "Startup": {
        "Domain": "http://localhost:9291",
        "Cors": {
            "PolicyName": "CorsIpAccess", //策略名称
            "EnableAllIPs": false, //当为true时，开放所有IP均可访问。
            // 支持多个域名端口，注意端口号后不要带/斜杆：比如localhost:8000/，是错的
            // 注意，http://127.0.0.1:1818 和 http://localhost:1818 是不一样的
            "IPs": "http://localhost:5200,http://localhost:5173,http://127.0.0.1:5173,http://localhost:9291,http://127.0.0.1:9291,http://localhost:9292,http://127.0.0.1:9292,http://localhost:9293,http://localhost:8080,http://localhost:8081"
        },
        "AppConfigAlert": {
            "Enabled": true
        },
        "ApiName": "Hyun.Core",
        "IdentityServer4": {
            "Enabled": false, // 这里默认是false，表示使用jwt，如果设置为true，则表示系统使用Ids4模式
            "AuthorizationUrl": "http://localhost:5004", // 认证中心域名
            "ApiName": "Hyun.Core.api" // 资源服务器
        },
        "Authing": {
            "Enabled": false,
            "Issuer": "https://uldr24esx31h-demo.authing.cn/oidc",
            "Audience": "63d51c4205c2849803be5178",
            "JwksUri": "https://uldr24esx31h-demo.authing.cn/oidc/.well-known/jwks.json"
        },
        "RedisMq": {
            "Enabled": false //redis 消息队列
        },
        "MiniProfiler": {
            "Enabled": false //性能分析开启
        },
        "Nacos": {
            "Enabled": false //Nacos注册中心
        }
    },
    "Middleware": {
        "RequestResponseLog": {
            "Enabled": false,
            "LogToFile": {
                "Enabled": false
            },
            "LogToDB": {
                "Enabled": false
            }
        },
        "IPLog": {
            "Enabled": false,
            "LogToFile": {
                "Enabled": false
            },
            "LogToDB": {
                "Enabled": false
            }
        },
        "RecordAccessLogs": {
            "Enabled": false,
            "LogToFile": {
                "Enabled": false
            },
            "LogToDB": {
                "Enabled": false
            },
            "IgnoreApis": "/api/permission/getnavigationbar,/api/monitor/getids4users,/api/monitor/getaccesslogs,/api/monitor/server,/api/monitor/getactiveusers,/api/monitor/server,"
        },
        "SignalR": {
            "Enabled": false
        },
        "SignalRSendLog": {
            "Enabled": false
        },
        "QuartzNetJob": {
            "Enabled": false
        },
        "Consul": {
            "Enabled": false
        },
        "IpRateLimit": {
            "Enabled": false
        },
        "EncryptionResponse": {
            "Enabled": false,
            "AllApis": false,
            "LimitApis": [
                "/api/Login/GetJwtTokenSecret"
            ]
        },
        "EncryptionRequest": {
            "Enabled": false,
            "AllApis": false,
            "LimitApis": [
                "/api/Login/GetJwtTokenSecret"
            ]
        }
    },
    "IpRateLimiting": {
        "EnableEndpointRateLimiting": false, //False: globally executed, true: executed for each
        "StackBlockedRequests": false, //False: Number of rejections should be recorded on another counter
        "RealIpHeader": "X-Real-IP",
        "ClientIdHeader": "X-ClientId",
        "IpWhitelist": [], //白名单
        "EndpointWhitelist": [ "get:/api/xxx", "*:/api/yyy" ],
        "ClientWhitelist": [ "dev-client-1", "dev-client-2" ],
        "QuotaExceededResponse": {
            "Content": "{{\"status\":429,\"msg\":\"访问过于频繁，请稍后重试\",\"success\":false}}",
            "ContentType": "application/json",
            "StatusCode": 429
        },
        "HttpStatusCode": 429, //返回状态码
        "GeneralRules": [ //api规则,结尾一定要带*
            {
                "Endpoint": "*:/api/blog*",
                "Period": "1m",
                "Limit": 20
            },
            {
                "Endpoint": "*/api/*",
                "Period": "1s",
                "Limit": 3
            },
            {
                "Endpoint": "*/api/*",
                "Period": "1m",
                "Limit": 30
            },
            {
                "Endpoint": "*/api/*",
                "Period": "12h",
                "Limit": 500
            }
        ]
    },
    "ConsulSetting": {
        "ServiceName": "HyunCoreService",
        "ServiceIP": "localhost",
        "ServicePort": "9291",
        "ServiceHealthCheck": "/healthcheck",
        "ConsulAddress": "http://localhost:8500"
    },
    "PayInfo": { //建行聚合支付信息
        "MERCHANTID": "", //商户号
        "POSID": "", //柜台号
        "BRANCHID": "", //分行号
        "pubKey": "", //公钥
        "USER_ID": "", //操作员号
        "PASSWORD": "", //密码
        "OutAddress": "http://127.0.0.1:12345" //外联地址
    },
    "nacos": {
        "ServerAddresses": [ "http://localhost:8848" ], // nacos 连接地址
        "DefaultTimeOut": 15000, // 默认超时时间
        "Namespace": "public", // 命名空间
        "ListenInterval": 10000, // 监听的频率
        "ServiceName": "Hyun.Core.Api", // 服务名
        "Port": "9291", // 服务端口号
        "RegisterEnabled": true // 是否直接注册nacos
    },
    "LogFiedOutPutConfigs": {
        "tcpAddressHost": "", // 输出elk的tcp连接地址
        "tcpAddressPort": 0, // 输出elk的tcp端口号
        "ConfigsInfo": [ // 配置的输出elk节点内容 常用语动态标识
            {
                "FiedName": "applicationName",
                "FiedValue": "Hyun.Core.Api"
            }
        ]
    },
    "Seq": {
        "Enabled": true,
        "Address": "http://localhost:5341/",
        "ApiKey": ""
    }
}