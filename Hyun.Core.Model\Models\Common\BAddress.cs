namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///wmblog_mssql_1
    ///</summary>
    [SugarTable("b_Address","wmblog_mssql_1")]
    public class BAddress : BaseEntity
    {

        public BAddress()
        {
            children = new List<BAddress>();
        }

        /// <summary>
        /// 子集
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<BAddress> children { get; set; }

        /// <summary>
        ///单位Id
        /// </summary>
        public long UnitId { get; set; }

           /// <summary>
           ///父Id
          /// </summary>
          public long Pid { get; set; }

           /// <summary>
           ///名称
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///编号
          /// </summary>
          [SugarColumn(Length = 15,IsNullable = true)]
          public string Code { get; set; }

           /// <summary>
           ///详细地址（1:A区|10:教学楼|101:会议室）
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Address { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///深度
          /// </summary>
          public int Depth { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          public int Sort { get; set; }

           /// <summary>
           ///路径
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Path { get; set; }

           /// <summary>
           ///地址属性（0：用户；1：系统；）
          /// </summary>
          public int AddressType { get; set; }

           /// <summary>
           ///状态(-1:已删除)
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///部门Id
          /// </summary>
          public long DepartmentId { get; set; }

           /// <summary>
           ///管理人Id
          /// </summary>
          public long ManagerUserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

