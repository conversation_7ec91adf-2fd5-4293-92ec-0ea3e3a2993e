﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hyun.Core.Model
{

    /// <summary>
    /// 性质类型：0系统超管 1市级、2区县、3单位、4企业
    /// </summary>
    /// <remarks>
    /// 功能说明：性质类型
    /// 作   者： 
    /// 创建日期： 
    /// 修改说明： 2019-12-25 by lss 添加 0系统超管
    /// </remarks>
    public enum UnitTypes
    {
        /// <summary>
        /// 0: 系统超管
        /// </summary>
        System = 0,

        /// <summary>
        /// 1: 市级
        /// </summary>
        City = 1,

        /// <summary>
        /// 2: 区县
        /// </summary>
        Couty = 2,

        /// <summary>
        /// 3: 单位
        /// </summary>
        School = 3,

        /// <summary>
        /// 4: 企业
        /// </summary>
        Company = 4,
        /// <summary>
        /// 5: 家长
        /// </summary>
        Parent = 5
    }

    public enum IndustryTypes
    {
        /// <summary>
        /// 普教
        /// </summary>
        GeneralEducation = 1,

        /// <summary>
        /// 高教
        /// </summary>
        HigherEducation = 2,

        /// <summary>
        /// 职教
        /// </summary>
        VocationalEducation = 3,

        /// <summary>
        /// 机关
        /// </summary>
        Organ = 4,

        /// <summary>
        /// 家庭
        /// </summary>
        Family = 5
    }


    /// <summary>
    /// 附件页面枚举（危化品使用是个位数，校服这里使用三位数）
    /// </summary>
    public enum ModuleTypeEnum
    {
        [Description("危化品-治理分类填报表")]
        Report = 1,
        [Description("危化品-治理申报分类明细表(历史)")]
        TrainPhoto = 2,
        [Description("危化品-问题隐患整改")]
        Rectification = 3,
        [Description("危化品-现场检查表")]
        Inspection = 4,

        /// <summary>
        /// 危化品-制度与队伍建设
        /// </summary>
        [Description("危化品-制度与队伍建设")]
        DcTeamBuild = 5,
        /// <summary>
        /// 危化品-培训与安全教育
        /// </summary>
        [Description("危化品-培训与安全教育")]
        DcTrainSafe = 6,
        /// <summary>
        /// 危化品-应急预案与演练
        /// </summary>
        [Description("危化品-应急预案与演练")]
        DcEmergencyDrill = 7,
        /// <summary>
        /// 危化品-MSDS
        /// </summary>
        [Description("危化品-MSDS")]
        DcMSDS = 8,
        /// <summary>
        /// 危化品-工作指导
        /// </summary>
        [Description("危化品-工作指导")]
        DcWorkGuide = 9,

        /// <summary>
        /// 危化品管理
        /// </summary>
        [Description("危化品管理")]
        DcManager = 10,

        /// <summary>
        /// 危化品-采购入库审核
        /// </summary>
        [Description("危化品-采购入库审核")]
        DcPurchaseStorageAudit = 11,

        /// <summary>
        /// 危化品-处置填报
        /// </summary>
        [Description("危化品-处置填报")]
        DcDisposeInput = 12,

        /// <summary>
        /// 校服选用创建
        /// </summary>
        [Description("校服选用")]
        SchemeCreate = 101,

        /// <summary>
        /// 校服采购创建
        /// </summary>
        [Description("校服采购")]
        PurchaseCreate = 102,

        /// <summary>
        /// 校服录入页面
        /// </summary>
        [Description("校服录入")]
        Create = 103,

        /// <summary>
        /// 选用组织页面
        /// </summary>
        [Description("选用组织")]
        OrganizationCreate = 104,

        /// <summary>
        /// 校服资助
        /// </summary>
        [Description("校服资助")]
        Sponsor = 105,

        /// <summary>
        /// 企业申请认证
        /// </summary>
        [Description("企业申请认证")]
        CompanyAuth = 106,

        /// <summary>
        /// 单位申请认证
        /// </summary>
        [Description("单位申请认证")]
        SchoolAuth = 107,

        /// <summary>
        /// 资讯信息
        /// </summary>
        [Description("资讯信息")]
        Atricle = 201,

        /// <summary>
        /// 地点管理
        /// </summary>
        [Description("地点管理")]
        Address = 401,
        /// <summary>
        /// 工作流
        /// </summary>
        [Description("工作流")]
        Workflow = 501,

        /// <summary>
        /// 认证中心
        /// </summary>
        [Description("认证中心")]
        AuthCenter = 601,
    }

    /// <summary>
    /// 附件值 
    /// </summary>
    public enum FileCategory
    {
        [Description("培训/教育内容")]
        TrainContent = 2960,
        [Description("培训/教育现场照片")]
        TrainPhoto = 2961,
        [Description("事故应急预案内容")]
        AccidentContent = 2962,
        [Description("演练现场照片")]
        DrillPhoto = 2963,
        [Description("MSDS")]
        MSDS = 2964,
        [Description("危化品治理分类填报表附件")]
        GovernItemReport = 2972,
        [Description("危化品治理整改表附件")]
        GovernItemRectify = 2973,
        [Description("危化品治理分类填报表附件")]
        GovernDeclareDetail = 2974,
        [Description("危化品治理任务单位表附件")]
        GovernTaskUnit = 2975,
        [Description("危化品工作指导")]
        DcWorkGuide = 2976,
        /// <summary>
        /// 危化品公安报备文件（危化品使用298几）
        /// </summary>
        [Description("危化品公安报备文件")]
        DcGongAnFile =2981,


        [Description("房屋基本信息附件")]
        RoomBasicInfo = 5100,
        [Description("房屋安全排查附件")]
        RoomCheck = 5200,
        [Description("房屋安全检测")]
        RoomDetect = 5300,
        [Description("绿化树木信息")]
        PreciousTrees = 5400,
        [Description("安全检查")]
        SrDangerCheck = 6100,
        [Description("安全隐患整改")]
        SrDangerRectify = 6300,
        [Description("安全专项发文复印件")]
        SrDangerSpecialCopy = 6500,
        [Description("检查报告")]
        SrDangerReport = 6600,

        [Description("安全排查培训/教育内容")]
        SafeTrainContent = 3060,
        [Description("安全排查培训/教育现场照片")]
        SafeTrainPhoto = 3061,

        [Description("安全排查事故应急预案内容")]
        SafeAccidentContent = 3062,
        [Description("安全排查演练现场照片")]
        SafeDrillPhoto = 3063,

        [Description("场景图片")]
        ScenePhoto = 4001,

        [Description("资产调拨材料")]
        AllocationData = 2100,


        [Description("合同扫描件")]
        ContractScan = 7100,

        [Description("中标通知书")]
        BidderNotice = 7210,

        [Description("招标文件")]
        TenderDocument = 7220,

        [Description("投标文件")]
        BidDocument = 7230,

        [Description("项目变更文件")]
        ChangeDocument = 7310,

        [Description("项目审计报告")]
        AuditReport = 7320,

        [Description("发票扫描件")]
        InvoiceScan = 8100,


        /// <summary>
        /// 镇江方案审核，区县方案审核
        /// </summary>
        [Description("辅助材料")]
        Auxiliary = 1100,
        /// <summary>
        /// 金陵中学，项目审计报告
        /// </summary>
        [Description("项目审计报告")]
        ProjectAuditReportJl = 1201,
        /// <summary>
        /// 金陵中学 经费报销审批单扫描件
        /// </summary>
        [Description("经费报销审批单扫描件")]
        FundsAuditFormJl = 1202,
    }

    /// <summary>
    ///危化品采购审批状态
    /// </summary>
    public enum DcPurchaseApprovalStatuz
    {
        [Description("已删除")]
        Deleted = -1,

        [Description("已添加")]
        Save = 0,

        [Description("等待主管审核")]
        WaitAudit = 10,

        [Description("主管审核退回")]
        AuditNoPass = 11,

        [Description("等待领导审批")]
        WaitApproval = 20,

        [Description("领导审批退回")]
        ApprovalNoPass = 21,

        [Description("审批结束")]
        End = 100,
    }

    public enum GradeEnum
    {
        /// <summary>
        /// 一年级
        /// </summary>
        [Description("一年级")]
        Grade1 = 1,
        /// <summary>
        /// 二年级
        /// </summary>
        [Description("二年级")]
        Grade2 = 2,
        /// <summary>
        /// 三年级
        /// </summary>
        [Description("三年级")]
        Grade3 = 3,
        /// <summary>
        /// 四年级
        /// </summary>
        [Description("四年级")]
        Grade4 = 4,
        /// <summary>
        /// 五年级
        /// </summary>
        [Description("五年级")]
        Grade5 = 5,
        /// <summary>
        /// 六年级
        /// </summary>
        [Description("六年级")]
        Grade6 = 6,
        /// <summary>
        /// 七年级
        /// </summary>
        [Description("七年级")]
        Grade7 = 7,
        /// <summary>
        /// 八年级
        /// </summary>
        [Description("八年级")]
        Grade8 = 8,
        /// <summary>
        /// 九年级
        /// </summary>
        [Description("九年级")]
        Grade9 = 9,
        /// <summary>
        /// 高一
        /// </summary>
        [Description("高一")]
        Grade10 = 10,
        /// <summary>
        /// 高二
        /// </summary>
        [Description("高二")]
        Grade11 = 11,
        /// <summary>
        /// 高三
        /// </summary>
        [Description("高三")]
        Grade12 = 12,
    }

    /// <summary>
    /// 状态是否（1：是  2：否）
    /// </summary>
    public enum StatuzIsEnum
    {
        /// <summary>
        /// 是
        /// </summary>
        [Description("是")]
        Ok = 1,
        /// <summary>
        /// 否
        /// </summary>
        [Description("否")]
        Not = 2,
    }

    /// <summary>
    /// 状态启用禁用（1：启用  2：禁用）
    /// </summary>
    public enum StatuzEnum
    {
        /// <summary>
        /// 启用
        /// </summary>
        [Description("启用")]
        Enable = 1,
        /// <summary>
        /// 禁用
        /// </summary>
        [Description("禁用")]
        Disable = 2,
    }

    /// <summary>
    /// 字典表TypeCode 枚举
    /// </summary>
    public enum DictionaryTypeCodeEnum
    {
        /// <summary>
        /// 校服单位
        /// </summary>
        [Description("校服单位")]
        UniformUnitName = 500000,
        /// <summary>
        /// 采购组织形式
        /// </summary>
        [Description("采购组织形式")]
        OrganizationForm = 501000,
        /// <summary>
        /// 采购要求
        /// </summary>
        [Description("采购要求")]
        PurchaseDemand = 502000,

        /// <summary>
        /// 学段
        /// </summary>
        [Description("学段")]
        SchoolStage = 100000,
    }
}
