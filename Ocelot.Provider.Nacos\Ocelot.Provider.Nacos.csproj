﻿<Project Sdk="Microsoft.NET.Sdk">
	
  <Import Project="..\build\common.targets" />
  <PropertyGroup>
    <Copyright>softlgl</Copyright>
    <Owners>softlgl</Owners>
    <PackageProjectUrl>https://github.com/softlgl/Ocelot.Provider.Nacos</PackageProjectUrl>
    <Title>Ocelot.Provider.Nacos</Title>
    <Description>Repo for Nacos integration with Ocelot</Description>
    <Version>1.2.1</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="nacos-sdk-csharp" Version="1.3.7" />
    <PackageReference Include="Ocelot" Version="24.0.1" />   
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="EasyCaching.InMemory" Version="1.9.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
  </ItemGroup>
</Project>
