﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///BAttachment接口方法
    ///</summary>
    public interface IBAttachmentServices : IBaseServices<BAttachment>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<BAttachment> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<BAttachment>> Find(Expression<Func<BAttachment, bool>> expression);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">BAttachmentParam实体参数</param>
        /// <returns></returns>
        Task<List<BAttachment>> Find(BAttachmentParam param);
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">BAttachmentParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<BAttachment>> GetPaged(BAttachmentParam param);
        /// <summary>
        /// 实体转换
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        BAttachment GetModel(BAttachmentData model);
       //<used>0</used>
       Task<List<BAttachment>> Insert(List<BAttachment> entityCollection);

       //<used>0</used>
       Task<List<BAttachment>> InsertUpdate(string title, string path, int width, int height, int docType, int isDefault, string remark, int userId, int unitId, int fileCategory, int isDelete, string ext);

       //<used>0</used>
       Task<List<BAttachment>> Delete(List<BAttachment> entityCollection);

    }
}

