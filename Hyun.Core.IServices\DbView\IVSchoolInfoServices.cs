﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///VSchoolInfo接口方法
    ///</summary>
    public interface IVSchoolInfoServices : IBaseServices<VSchoolInfo>
    {

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">VSchoolInfoParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<VSchoolInfo>> GetPaged(VSchoolInfoParam param);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VSchoolInfo>> GetSchoolDetail(VSchoolInfoParam param);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<List<VSchoolInfo>> GetById(long id);

    }
}

