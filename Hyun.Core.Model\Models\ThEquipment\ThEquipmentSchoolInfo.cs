﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位装备信息情况表
    ///</summary>
    [SugarTable("th_EquipmentSchoolInfo","单位装备信息情况表")]
    public class ThEquipmentSchoolInfo : BaseEntity
    {

          public ThEquipmentSchoolInfo()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///装备分类表Id
          /// </summary>
          public long EquipmentCategoryId { get; set; }

           /// <summary>
           ///配备标准数量
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? StandardNum { get; set; }

           /// <summary>
           ///装备存量
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? StockNum { get; set; }

           /// <summary>
           ///装备差额
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? DifferenceNum { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

