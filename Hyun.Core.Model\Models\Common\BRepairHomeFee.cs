namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///上门费管理
    ///</summary>
    [SugarTable("b_RepairHomeFee","上门费管理")]
    public class BRepairHomeFee : BaseEntity
    {

          public BRepairHomeFee()
          {

          }

           /// <summary>
           ///添加该项目用户Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///制定报价项目的单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///企业Id，用来界定单位企业之间点对点的上门费限价，0表示所有企业到该单位使用统一价格
          /// </summary>
          public long CompanyId { get; set; }

           /// <summary>
           ///往返里程数
          /// </summary>
          public int Mileage { get; set; }

        /// <summary>
        ///单价
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Price { get; set; }

        /// <summary>
        ///最高限价
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal CeilingPrice { get; set; }

           /// <summary>
           ///记录时间
          /// </summary>
          public DateTime RegTime { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

