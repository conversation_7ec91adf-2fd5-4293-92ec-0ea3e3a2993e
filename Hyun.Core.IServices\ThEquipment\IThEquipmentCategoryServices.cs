﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///ThEquipmentCategory接口方法
    ///</summary>
    public interface IThEquipmentCategoryServices : IBaseServices<ThEquipmentCategory>
    {
        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="o">List<ThEquipmentCategory>对象</param>
        /// <returns></returns>
        Task<Result> BatchAdd(List<ThEquipmentCategory> list);

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<ThEquipmentCategory> GetById(long id);
        /// <summary>
        /// 获取分类
        /// </summary>
        /// <param name="unitid"></param>
        /// <returns></returns>
        Task<List<ThEquipmentCategoryDto>> GetCategory(long unitid);
        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<ThEquipmentCategory>> Find(Expression<Func<ThEquipmentCategory, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">ThEquipmentCategoryParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<ThEquipmentCategory>> GetPaged(ThEquipmentCategoryParam param);
        /// <summary>
        /// 单位获取信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<ThEquipmentSchoolInfoDto>> GetPagedBySchool(ThEquipmentCategoryParam param);
    }
}

