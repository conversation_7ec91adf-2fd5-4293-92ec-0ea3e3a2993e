﻿namespace Hyun.Core.Model
{

    ///<summary>
    ///招标结果
    ///</summary>
    public class XUniformBiddingDto : BaseEntity
    {

        public XUniformBiddingDto()
        {

        }

        /// <summary>
        ///学校Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///区县Id
        /// </summary>
        public long CountyId { get; set; }

        /// <summary>
        ///校服采购表Id
        /// </summary>
        public long? UniformBuyId { get; set; }

        /// <summary>
        ///是否备查
        /// </summary>
        public int? IsFiling { get; set; }

        /// <summary>
        ///采购有效年限
        /// </summary>
        public int? ValidityPeriod { get; set; }

        /// <summary>
        ///采购方式（对应字典表DicValue值）
        /// </summary>
        public int Method { get; set; } = 0;

        /// <summary>
        ///开标结果公开（1：已公开 2：未公开）
        /// </summary>
        public int BidResultPublic { get; set; } = 0;

        /// <summary>
        ///开标日期
        /// </summary>
        public DateTime? BidDate { get; set; }

        /// <summary>
        ///公开日期
        /// </summary>
        public DateTime? PublicDate { get; set; }

        /// <summary>
        ///公开媒体名称
        /// </summary>
        public string PublicMediaName { get; set; }

        /// <summary>
        ///委托代理机构
        /// </summary>
        public string EntrustingAgency { get; set; }

        /// <summary>
        ///代理机构联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        ///代理机构联系电话
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        ///招标结果状态（0：待备案  10：待审核  100：已备案）
        /// </summary>
        public int? BiddingStatuz { get; set; }

        /// <summary>
        /// 备案说明
        /// </summary>
        public string FilingExplanation { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 采购有效年限名称
        /// </summary>
        public string ValidityPeriodName { get; set; }

        /// <summary>
        /// 采购方式名称
        /// </summary>
        public string MethodName { get; set; }

        /// <summary>
        /// 年度
        /// </summary>
        public int PlanYear { get; set; }

        /// <summary>
        /// 采购批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string BiddingStatuzName { get; set; }


        /// <summary>
        /// 附件Id集合
        /// </summary>
        public List<long> ListAttachmentId { get; set; } = new List<long>();

        /// <summary>
        /// 按钮类型（0：保存，1：提交）
        /// </summary>
        public int ButtonType { get; set; } = 0;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 附件Id
        /// </summary>
        public long AttachmentId { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string AreaName { get; set; }
    }


}

