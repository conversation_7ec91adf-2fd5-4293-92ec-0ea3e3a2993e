namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///校服班级表
    ///</summary>
    [SugarTable("X_UniformClass","班级表")]
    public class XUniformClass : BaseEntity
    {

          public XUniformClass()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///学段
          /// </summary>
          public long SchoolStage { get; set; }

           /// <summary>
           ///入学年份
          /// </summary>
          public int StartYear { get; set; }

           /// <summary>
           ///年级名称
          /// </summary>
          [SugarColumn(Length = 31)]
          public string GradeName { get; set; }

           /// <summary>
           ///年级
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? GradeId { get; set; }

           /// <summary>
           ///班级
          /// </summary>
          [SugarColumn(Length = 31)]
          public string ClassName { get; set; }

           /// <summary>
           ///班主任Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? TeacherUserId { get; set; }

           /// <summary>
           ///班主任
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string TeacherName { get; set; }

           /// <summary>
           ///手机
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string TeacherMobile { get; set; }

           /// <summary>
           ///学生数
          /// </summary>
          public int StudentNum { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

