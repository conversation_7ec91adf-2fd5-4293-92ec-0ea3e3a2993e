﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models;
using Hyun.Core.IServices;
using Hyun.Core.Model.SearchModels.Common;
using NetTaste;
using Hyun.Old.Util;
using DPE.Core.Common.Helper;
using System.Collections.Generic;
using NPOI.Util;
namespace Hyun.Core.Api
{

    /// <summary>
    /// 学生管理-班主任事务
    /// </summary>
    [Route("api/hyun/pstudent")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PStudentController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IPStudentServices ipstudentservicesManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IPClassInfoServices classinfoservicesManager;

        public PStudentController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IPStudentServices _ipstudentservicesManager, IBDictionaryServices _dictionaryManager, IPClassInfoServices _classinfoservicesManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ipstudentservicesManager = _ipstudentservicesManager;
            dictionaryManager = _dictionaryManager;
            classinfoservicesManager = _classinfoservicesManager;
        }

        /// <summary>
        /// 单位管理员新增学生信息
        /// </summary>
        /// <param name="obj">PStudentDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("add")]
        public async Task<Result> PStudentAdd([FromBody] PStudentDto obj)
        {
            Result r = new Result();
            PStudent o = mapper.Map<PStudent>(obj);
            o.SchoolId = user.UnitId;
            r = await ipstudentservicesManager.InsertUpdate(o);
            return r;
        }

        /// <summary>
        /// 修改学生信息
        /// </summary>
        /// <param name="obj">PStudentDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("edit")]
        public async Task<Result> PStudentEdit([FromBody] PStudentDto obj)
        {
            Result r = new Result();
            PStudent o = mapper.Map<PStudent>(obj);
            o.SchoolId = user.UnitId;
            r = await ipstudentservicesManager.InsertUpdate(o);
            return r;
        }

        /// <summary>
        /// 根据Id查询学生信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbyeditid")]
        public async Task<Result> PStudentByEditId(long id)
        {
            Result r = new Result();
            PStudent m = await ipstudentservicesManager.QueryById(id);
            if (m != null && m.SchoolId == user.UnitId)
            {
                m.IDCard = Hyun.Core.Common.Helper.Base64.DecodeBase64(m.IDCard);
                r.data.rows = mapper.Map<PStudentDto>(m);
            }

            //if (isFirst)
            //{
            //    //获取学段
            //    List<BDictionary> listStage = await dictionaryManager.GetByTypeCode("100000");
            //    List<dropdownModel> listStageDropdown = listStage.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();
            //    //获取年级
            //    List<BDictionaryDto> listGrade = await dictionaryManager.GetGradeInfo();
            //    List<dropdownModel> listGradeDropdown = listStage.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();
            //    r.data.other = new { StageList = listStageDropdown, GradeList = listGradeDropdown };
            //}

            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }


        /// <summary>
        /// 根据Id删除数据【真删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("deletebyid")]
        public async Task<Result> PStudentDeleteById(long id)
        {
            Result r = new Result();
            r = await ipstudentservicesManager.DeleteById(id);
            return r;
        }

        /// <summary>
        /// 区县、市级查询列表
        /// </summary>
        /// <param name="param">PStudentParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<PStudentDto>>> PStudentGetPaged([FromBody] PStudentParam param)
        {
            if (user.UnitTypeId == 3)
            {
                param.unitId = user.UnitId;
            }
            var msgdata = new Result<List<PStudentDto>>();
            string downLoadFile = $"/Download/学生导入信息.xlsx";
            PageModel<PStudentDto> pg = await ipstudentservicesManager.GetPaged(param);
            if (param.isFirst)
            {
                //获取学段
                List<BDictionary> listStage = await dictionaryManager.GetByTypeCode("100000");
                List<dropdownModel> listStageDropdown = listStage.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();
                
                ////获取年级
                //List<BDictionaryDto> listGrade = await dictionaryManager.GetGradeInfo();
                //List<dropdownModel> listGradeDropdown = listStage.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                //获取班级
                List<BDictionary> listClass = await dictionaryManager.GetByTypeCode("300000");
                List<dropdownModel> listClassDropdown = listClass.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

               

                msgdata = baseSucc(pg.data, pg.dataCount,
                    $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                    new { StageList = listStageDropdown, ClassList = listClassDropdown, filePath = downLoadFile });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount,
                    $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { filePath = downLoadFile });
            }
            return msgdata;
        }


        /// <summary>
        /// 单位管理员导入班级学生信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("import")]
        public async Task<Result> PStudentImport([FromBody] ImportParam param)
        {
            Result r = new Result();
            List<PStudentDto> list = new ExcelHelper<PStudentDto>().ImportFromExcel(env.ContentRootPath, param.FilePath);
            r = await ipstudentservicesManager.ImportStudentInfo(list, param.UniformClassId);
            return r;
        }

        /// <summary>
        /// 单位导出学生信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportschoolstu")]
        public async Task<Result> ExportSchoolStudent([FromBody] PStudentParam param)
        {
            Result r = new Result();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<PStudentDto> pg = await ipstudentservicesManager.GetPaged(param);
            if (pg.dataCount > 0)
            {
                string execlName = $"{user.UnitName}-{DateTime.Now.Year}年学生信息";
                string file = new ExcelHelper<PStudentDto>().ExportToExcel(env.WebRootPath, execlName + ".xls", execlName,
                                                                                          pg.data.ToList(),
                                                                                          new string[] { "GradeName", "ClassName", "StudentNo", "StudentName", "Sex", "IDCard6" });
                //r.data.rows = Path.Combine(ApplicationConfig.CurrentServerUrl, file);
                r.flag = 1;
                r.msg = "导出成功";
                r.data.headers = "学生信息";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }


        #region 班主任事务
        /// <summary>
        /// 班主任学生信息列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpagedbyteacher")]
        public async Task<Result<List<PStudentDto>>> GetPagedByTeacher([FromBody] PStudentParam param)
        {
            var msgdata = new Result<List<PStudentDto>>();
            PageModel<PStudentDto> pg = await ipstudentservicesManager.GetStudentPagedByTeacher(param);
            string headMsg = "";
            string downLoadFile = $"/Download/学生导入信息.xlsx";
            if (pg.dataCount > 0)
            {
                headMsg = $"{pg.data[0].GradeName},{pg.data[0].ClassName}";
            }
            if (param.isFirst && pg.dataCount> 0)
            {
                msgdata = baseSucc(pg.data, pg.dataCount,
                 $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                 new { headMsg = headMsg,filePath = downLoadFile });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",new { filePath = downLoadFile });
            }
            return msgdata;
        }

        /// <summary>
        /// 班主任添加学生信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("teacheraddstu")]
        public async Task<Result<string>> TeacherStudentAdd([FromBody] PStudentModel o)
        {
            var result = await ipstudentservicesManager.TeacherStudentInsertUpdate(o);
            return result;
        }

        /// <summary>
        /// 班主任修改学生信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("teachereditstu")]
        public async Task<Result<string>> TeacherStudentEdit([FromBody] PStudentModel o)
        {
            var result = await ipstudentservicesManager.TeacherStudentInsertUpdate(o);
            return result;
        }

        /// <summary>
        /// 班主任删除学生信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("teacherdelstu")]
        public async Task<Result<string>> TeacherStudentDeleteById(long id)
        {
            var result = await ipstudentservicesManager.TeacherDelStudent(id);
            return result;
        }

        /// <summary>
        /// 班主任导入学生信息
        /// </summary>
        /// <param name="fileList"></param>
        /// <returns></returns>x
        [HttpPost]
        [Route("teacherimportstu")]
        public async Task<Result> TeacherImportStudent(IFormCollection fileList)
        {
            Result r = await FileHelper.UploadFile(env.ContentRootPath, (int)UploadFileType.Import, fileList.Files);
            if (r.flag == 1)
            {
                List<PStudentModel> list = new ExcelHelper<PStudentModel>().ImportFromExcel(env.ContentRootPath, r.data.rows.ToString());
                r = await ipstudentservicesManager.TeacherImportStudentInfo(list);
            }
            return r;
        }

        /// <summary>
        /// 班主任导出学生信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportteacherstu")]
        public async Task<IActionResult> ExportTeacherStudent([FromBody] PStudentParam param)
        {
            //Result r = new Result();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<PStudentDto> pg = await ipstudentservicesManager.GetStudentPagedByTeacher(param);
            //if (pg.dataCount > 0)
            //{
            //    //string execlName = $"{DateTime.Now.Year}年{pg.data[0].GradeName}({pg.data[0].ClassName})学生信息";
            //    //string file = new ExcelHelper<PStudentDto>().ExportToExcel(env.WebRootPath, execlName + ".xls", execlName,
            //    //                                                                          pg.data.ToList(),
            //    //                                                                          new string[] { "StudentNo", "StudentName", "Sex", "IDCard6" });
            //    //r.data.rows = file;
            //    //r.flag = 1;
            //    //r.msg = "导出成功";
            //    //r.data.headers = "学生信息";

            //    var excelBytes = await new ExcelHelper<PStudentDto>().ExportExecl(pg.data.ToList(), "学生信息", new string[] { "StudentNo", "StudentName", "Sex", "IDCard6" });
            //    var file = File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "学生信息.xlsx");
            //    r.data.rows = file;
            //    r.flag = 1;
            //    r.msg = "导出成功";
            //}
            //else
            //{
            //    r.flag = 0;
            //    r.msg = "无数据导出";
            //}
            //return r;
            var excelBytes = await new ExcelHelper<PStudentDto>().ExportExecl(pg.data.ToList(), "学生信息", new string[] { "StudentNo", "StudentName", "Sex", "IDCard6" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "学生信息.xlsx");
        }
        #endregion


    }
}
