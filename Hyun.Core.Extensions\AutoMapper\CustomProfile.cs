﻿using AutoMapper;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.ViewModels;
using Hyun.Core.Model.Models.Uniform;

namespace Hyun.Core.AutoMapper
{
    public class CustomProfile : Profile
    {
        /// <summary>
        /// 配置构造函数，用来创建关系映射
        /// </summary>
        public CustomProfile()
        {
            CreateMap<AAddressPlaceUserDto, AAddressPlaceUser>();
            CreateMap<AAddressPlaceUser, AAddressPlaceUserDto>();
            CreateMap<AAssetsEquipmentAddressDto, AAssetsEquipmentAddress>();
            CreateMap<AAssetsEquipmentAddress, AAssetsEquipmentAddressDto>();
            CreateMap<AAssetsEquipmentBorrowDto, AAssetsEquipmentBorrow>();
            CreateMap<AAssetsEquipmentBorrow, AAssetsEquipmentBorrowDto>();
            CreateMap<AAssetsEquipmentDetailDto, AAssetsEquipmentDetail>();
            CreateMap<AAssetsEquipmentDetail, AAssetsEquipmentDetailDto>();
            CreateMap<AAssetsEquipmentListDto, AAssetsEquipmentList>();
            CreateMap<AAssetsEquipmentList, AAssetsEquipmentListDto>();
            CreateMap<ApiAppSecretDto, ApiAppSecret>();
            CreateMap<ApiAppSecret, ApiAppSecretDto>();
            CreateMap<ApiKeyAreaDto, ApiKeyArea>();
            CreateMap<ApiKeyArea, ApiKeyAreaDto>();
            CreateMap<ApiSystemKeyDto, ApiSystemKey>();
            CreateMap<ApiSystemKey, ApiSystemKeyDto>();
            CreateMap<APlacePropertyDto, APlaceProperty>();
            CreateMap<APlaceProperty, APlacePropertyDto>();
            CreateMap<APlacePropertyStageDto, APlacePropertyStage>();
            CreateMap<APlacePropertyStage, APlacePropertyStageDto>();
            CreateMap<AppUserBindAppIdDto, AppUserBindAppId>();
            CreateMap<AppUserBindAppId, AppUserBindAppIdDto>();
            CreateMap<BAddressDto, BAddress>();
            CreateMap<BAddress, BAddressDto>();
            CreateMap<BAreaDictionaryDto, BAreaDictionary>();
            CreateMap<BAreaDictionary, BAreaDictionaryDto>();
            CreateMap<BAreaDto, BArea>();
            CreateMap<BArea, BAreaDto>();
            CreateMap<BAttachmentDto, BAttachment>();
            CreateMap<BAttachment, BAttachmentDto>();
            CreateMap<BAttachmentTemplateConfigDto, BAttachmentTemplateConfig>();
            CreateMap<BAttachmentTemplateConfig, BAttachmentTemplateConfigDto>();
            CreateMap<BCityRoutingDto, BCityRouting>();
            CreateMap<BCityRouting, BCityRoutingDto>();
            CreateMap<BConfigSetDto, BConfigSet>();
            CreateMap<BConfigSet, BConfigSetDto>();
            CreateMap<BCustomerDomainDto, BCustomerDomain>();
            CreateMap<BCustomerDomain, BCustomerDomainDto>();
            CreateMap<BDeviceAliasesDto, BDeviceAliases>();
            CreateMap<BDeviceAliases, BDeviceAliasesDto>();
            CreateMap<BDeviceBaseDto, BDeviceBase>();
            CreateMap<BDeviceBase, BDeviceBaseDto>();
            CreateMap<BDeviceCodeDto, BDeviceCode>();
            CreateMap<BDeviceCode, BDeviceCodeDto>();
            CreateMap<BDeviceCodeExtensionDto, BDeviceCodeExtension>();
            CreateMap<BDeviceCodeExtension, BDeviceCodeExtensionDto>();
            CreateMap<BDeviceCodeStandardDto, BDeviceCodeStandard>();
            CreateMap<BDeviceCodeStandard, BDeviceCodeStandardDto>();
            CreateMap<BDeviceScrapDto, BDeviceScrap>();
            CreateMap<BDeviceScrap, BDeviceScrapDto>();
            CreateMap<BDictionaryDto, BDictionary>();
            CreateMap<BDictionary, BDictionaryDto>();
            CreateMap<BIndustryDivisionDto, BIndustryDivision>();
            CreateMap<BIndustryDivision, BIndustryDivisionDto>();
            CreateMap<BIndustryDto, BIndustry>();
            CreateMap<BIndustry, BIndustryDto>();
            CreateMap<BIndustryGroupDto, BIndustryGroup>();
            CreateMap<BIndustryGroup, BIndustryGroupDto>();
            CreateMap<BLogDto, BLog>();
            CreateMap<BLog, BLogDto>();
            CreateMap<BMessageDto, BMessage>();
            CreateMap<BMessage, BMessageDto>();
            CreateMap<BMessageImageDto, BMessageImage>();
            CreateMap<BMessageImage, BMessageImageDto>();
            CreateMap<BMessageStateDto, BMessageState>();
            CreateMap<BMessageState, BMessageStateDto>();
            CreateMap<BModelOptionNameDto, BModelOptionName>();
            CreateMap<BModelOptionName, BModelOptionNameDto>();
            CreateMap<BModuleProcessConfigDto, BModuleProcessConfig>();
            CreateMap<BModuleProcessConfig, BModuleProcessConfigDto>();
            CreateMap<BModuleProcessDataDto, BModuleProcessData>();
            CreateMap<BModuleProcessData, BModuleProcessDataDto>();
            CreateMap<BModuleProcessStatuzConfigDto, BModuleProcessStatuzConfig>();
            CreateMap<BModuleProcessStatuzConfig, BModuleProcessStatuzConfigDto>();
            CreateMap<BOperateLogDto, BOperateLog>();
            CreateMap<BOperateLog, BOperateLogDto>();
            CreateMap<BPartsItemDto, BPartsItem>();
            CreateMap<BPartsItem, BPartsItemDto>();
            CreateMap<BPrintConfigDto, BPrintConfig>();
            CreateMap<BPrintConfig, BPrintConfigDto>();
            CreateMap<BPrintPageDto, BPrintPage>();
            CreateMap<BPrintPage, BPrintPageDto>();
            CreateMap<BPrintPositionDto, BPrintPosition>();
            CreateMap<BPrintPosition, BPrintPositionDto>();
            CreateMap<BPrintSettingDto, BPrintSetting>();
            CreateMap<BPrintSetting, BPrintSettingDto>();
            CreateMap<BRepairHomeFeeDto, BRepairHomeFee>();
            CreateMap<BRepairHomeFee, BRepairHomeFeeDto>();
            CreateMap<BSendMessageDto, BSendMessage>();
            CreateMap<BSendMessage, BSendMessageDto>();
            CreateMap<BServiceClassDto, BServiceClass>();
            CreateMap<BServiceClass, BServiceClassDto>();
            CreateMap<BServiceFeeDto, BServiceFee>();
            CreateMap<BServiceFee, BServiceFeeDto>();
            CreateMap<BServiceItemDto, BServiceItem>();
            CreateMap<BServiceItem, BServiceItemDto>();
            CreateMap<BServiceLibApplyDto, BServiceLibApply>();
            CreateMap<BServiceLibApply, BServiceLibApplyDto>();
            CreateMap<BServiceLibDto, BServiceLib>();
            CreateMap<BServiceLib, BServiceLibDto>();
            CreateMap<BSmsGroupDto, BSmsGroup>();
            CreateMap<BSmsGroup, BSmsGroupDto>();
            CreateMap<BSmsHistoryDto, BSmsHistory>();
            CreateMap<BSmsHistory, BSmsHistoryDto>();
            CreateMap<BSmsHistoryValidateDto, BSmsHistoryValidate>();
            CreateMap<BSmsHistoryValidate, BSmsHistoryValidateDto>();
            CreateMap<BSmsLogDto, BSmsLog>();
            CreateMap<BSmsLog, BSmsLogDto>();
            CreateMap<BSmsOutgoingDto, BSmsOutgoing>();
            CreateMap<BSmsOutgoing, BSmsOutgoingDto>();
            CreateMap<BSmsOutGroupDto, BSmsOutGroup>();
            CreateMap<BSmsOutGroup, BSmsOutGroupDto>();
            CreateMap<BSmsUserDto, BSmsUser>();
            CreateMap<BSmsUser, BSmsUserDto>();
            CreateMap<BStandardEntryAliasesDto, BStandardEntryAliases>();
            CreateMap<BStandardEntryAliases, BStandardEntryAliasesDto>();
            CreateMap<BStandardEntryDto, BStandardEntry>();
            CreateMap<BStandardEntry, BStandardEntryDto>();
            CreateMap<BUnitSettingDto, BUnitSetting>();
            CreateMap<BUnitSetting, BUnitSettingDto>();
            CreateMap<BUserActionLogDto, BUserActionLog>();
            CreateMap<BUserActionLog, BUserActionLogDto>();
            CreateMap<BUserActionStaticDto, BUserActionStatic>();
            CreateMap<BUserActionStatic, BUserActionStaticDto>();
            CreateMap<BWebSiteConfigDto, BWebSiteConfig>();
            CreateMap<BWebSiteConfig, BWebSiteConfigDto>();
            CreateMap<DArticleCategoryDto, DArticleCategory>();
            CreateMap<DArticleCategory, DArticleCategoryDto>();
            CreateMap<DArticleCommentDto, DArticleComment>();
            CreateMap<DArticleComment, DArticleCommentDto>();
            CreateMap<DArticleDto, DArticle>();
            CreateMap<DArticle, DArticleDto>();
            CreateMap<DArticleExtendDto, DArticleExtend>();
            CreateMap<DArticleExtend, DArticleExtendDto>();
            CreateMap<DArticleImageDto, DArticleImage>();
            CreateMap<DArticleImage, DArticleImageDto>();
            CreateMap<DArticleSubjectDto, DArticleSubject>();
            CreateMap<DArticleSubject, DArticleSubjectDto>();
            CreateMap<DcApplyApprovalDto, DcApplyApproval>();
            CreateMap<DcApplyApproval, DcApplyApprovalDto>();
            CreateMap<DcApplyConfirmDetailDto, DcApplyConfirmDetail>();
            CreateMap<DcApplyConfirmDetail, DcApplyConfirmDetailDto>();
            CreateMap<DcApplyConfirmDto, DcApplyConfirm>();
            CreateMap<DcApplyConfirm, DcApplyConfirmDto>();
            CreateMap<DcApplyDto, DcApply>();
            CreateMap<DcApply, DcApplyDto>();
            CreateMap<DcApplyGrantUserDto, DcApplyGrantUser>();
            CreateMap<DcApplyGrantUser, DcApplyGrantUserDto>();
            CreateMap<DcAuditConditionDto, DcAuditCondition>();
            CreateMap<DcAuditCondition, DcAuditConditionDto>();
            CreateMap<DcAuditGroupDto, DcAuditGroup>();
            CreateMap<DcAuditGroup, DcAuditGroupDto>();
            CreateMap<DcAuditMemberDto, DcAuditMember>();
            CreateMap<DcAuditMember, DcAuditMemberDto>();
            CreateMap<DcAuditUserDto, DcAuditUser>();
            CreateMap<DcAuditUser, DcAuditUserDto>();
            CreateMap<DcBackDto, DcBack>();
            CreateMap<DcBack, DcBackDto>();
            CreateMap<DcBaseBrandDto, DcBaseBrand>();
            CreateMap<DcBaseBrand, DcBaseBrandDto>();
            CreateMap<DcBaseCatalogDto, DcBaseCatalog>();
            CreateMap<DcBaseCatalog, DcBaseCatalogDto>();
            CreateMap<DcBaseFieldConfigDto, DcBaseFieldConfig>();
            CreateMap<DcBaseFieldConfig, DcBaseFieldConfigDto>();
            CreateMap<DcBaseModelExtensionDto, DcBaseModelExtension>();
            CreateMap<DcBaseModelExtension, DcBaseModelExtensionDto>();
            CreateMap<DcBaseWasteDto, DcBaseWaste>();
            CreateMap<DcBaseWaste, DcBaseWasteDto>();
            CreateMap<DcBrandStockDto, DcBrandStock>();
            CreateMap<DcBrandStock, DcBrandStockDto>();
            CreateMap<DcCompanyDto, DcCompany>();
            CreateMap<DcCompany, DcCompanyDto>();
            CreateMap<DcDepositAddressDto, DcDepositAddress>();
            CreateMap<DcDepositAddress, DcDepositAddressDto>();
            CreateMap<DcEmergencyPlanDto, DcEmergencyPlan>();
            CreateMap<DcEmergencyPlan, DcEmergencyPlanDto>();
            CreateMap<DcFinanceMonthStatisticDto, DcFinanceMonthStatistic>();
            CreateMap<DcFinanceMonthStatistic, DcFinanceMonthStatisticDto>();
            CreateMap<DcGovernDeclareDetailDto, DcGovernDeclareDetail>();
            CreateMap<DcGovernDeclareDetail, DcGovernDeclareDetailDto>();
            CreateMap<DcGovernDeclareSummaryDto, DcGovernDeclareSummary>();
            CreateMap<DcGovernDeclareSummary, DcGovernDeclareSummaryDto>();
            CreateMap<DcGovernDeclareUnitConfigDto, DcGovernDeclareUnitConfig>();
            CreateMap<DcGovernDeclareUnitConfig, DcGovernDeclareUnitConfigDto>();
            CreateMap<DcGovernItemDto, DcGovernItem>();
            CreateMap<DcGovernItem, DcGovernItemDto>();
            CreateMap<DcGovernItemReportDto, DcGovernItemReport>();
            CreateMap<DcGovernItemReport, DcGovernItemReportDto>();
            CreateMap<DcGovernRectifyDto, DcGovernRectify>();
            CreateMap<DcGovernRectify, DcGovernRectifyDto>();
            CreateMap<DcGovernSetDto, DcGovernSet>();
            CreateMap<DcGovernSet, DcGovernSetDto>();
            CreateMap<DcGovernTaskDto, DcGovernTask>();
            CreateMap<DcGovernTask, DcGovernTaskDto>();
            CreateMap<DcGovernTaskUnitDto, DcGovernTaskUnit>();
            CreateMap<DcGovernTaskUnit, DcGovernTaskUnitDto>();
            CreateMap<DcImportPurchaseListDto, DcImportPurchaseList>();
            CreateMap<DcImportPurchaseList, DcImportPurchaseListDto>();
            CreateMap<DcInventoryDto, DcInventory>();
            CreateMap<DcInventory, DcInventoryDto>();
            CreateMap<DcLackFeedbackDto, DcLackFeedback>();
            CreateMap<DcLackFeedback, DcLackFeedbackDto>();
            CreateMap<DcMaterialsNumAuditDto, DcMaterialsNumAudit>();
            CreateMap<DcMaterialsNumAudit, DcMaterialsNumAuditDto>();
            CreateMap<DcMessageDto, DcMessage>();
            CreateMap<DcMessage, DcMessageDto>();
            CreateMap<DcModelStockDto, DcModelStock>();
            CreateMap<DcModelStock, DcModelStockDto>();
            CreateMap<DcModelStockHistoryDto, DcModelStockHistory>();
            CreateMap<DcModelStockHistory, DcModelStockHistoryDto>();
            CreateMap<DcPurchaseApprovalDto, DcPurchaseApproval>();
            CreateMap<DcPurchaseApproval, DcPurchaseApprovalDto>();
            CreateMap<DcPurchaseListDto, DcPurchaseList>();
            CreateMap<DcPurchaseList, DcPurchaseListDto>();
            CreateMap<DcPurchaseOrderDto, DcPurchaseOrder>();
            CreateMap<DcPurchaseOrder, DcPurchaseOrderDto>();
            CreateMap<DcRevertDto, DcRevert>();
            CreateMap<DcRevert, DcRevertDto>();
            CreateMap<DcSchoolCatalogDto, DcSchoolCatalog>();
            CreateMap<DcSchoolCatalog, DcSchoolCatalogDto>();
            CreateMap<DcSchoolMaterialBackLogDto, DcSchoolMaterialBackLog>();
            CreateMap<DcSchoolMaterialBackLog, DcSchoolMaterialBackLogDto>();
            CreateMap<DcSchoolMaterialBrandDto, DcSchoolMaterialBrand>();
            CreateMap<DcSchoolMaterialBrand, DcSchoolMaterialBrandDto>();
            CreateMap<DcSchoolMaterialCodeDto, DcSchoolMaterialCode>();
            CreateMap<DcSchoolMaterialCode, DcSchoolMaterialCodeDto>();
            CreateMap<DcSchoolMaterialDto, DcSchoolMaterial>();
            CreateMap<DcSchoolMaterial, DcSchoolMaterialDto>();
            CreateMap<DcSchoolMaterialModelDto, DcSchoolMaterialModel>();
            CreateMap<DcSchoolMaterialModel, DcSchoolMaterialModelDto>();
            CreateMap<DcSchoolMaterialTempDto, DcSchoolMaterialTemp>();
            CreateMap<DcSchoolMaterialTemp, DcSchoolMaterialTempDto>();
            CreateMap<DcSchoolModelBrandDto, DcSchoolModelBrand>();
            CreateMap<DcSchoolModelBrand, DcSchoolModelBrandDto>();
            CreateMap<DcScrapDto, DcScrap>();
            CreateMap<DcScrap, DcScrapDto>();
            CreateMap<DcStockBookStatisticDto, DcStockBookStatistic>();
            CreateMap<DcStockBookStatistic, DcStockBookStatisticDto>();
            CreateMap<DcTrainSafeEducationDto, DcTrainSafeEducation>();
            CreateMap<DcTrainSafeEducation, DcTrainSafeEducationDto>();
            CreateMap<DcUnitLicenseInfoDto, DcUnitLicenseInfo>();
            CreateMap<DcUnitLicenseInfo, DcUnitLicenseInfoDto>();
            CreateMap<DcWasteDisposalDetailDto, DcWasteDisposalDetail>();
            CreateMap<DcWasteDisposalDetail, DcWasteDisposalDetailDto>();
            CreateMap<DcWasteDisposalDto, DcWasteDisposal>();
            CreateMap<DcWasteDisposal, DcWasteDisposalDto>();
            CreateMap<DcWasteRecordDto, DcWasteRecord>();
            CreateMap<DcWasteRecord, DcWasteRecordDto>();
            CreateMap<DFriendLinkCategoriesDto, DFriendLinkCategories>();
            CreateMap<DFriendLinkCategories, DFriendLinkCategoriesDto>();
            CreateMap<DFriendLinkDto, DFriendLink>();
            CreateMap<DFriendLink, DFriendLinkDto>();
            CreateMap<OCompanyRecommendDto, OCompanyRecommend>();
            CreateMap<OCompanyRecommend, OCompanyRecommendDto>();
            CreateMap<OPageFunctionDto, OPageFunction>();
            CreateMap<OPageFunction, OPageFunctionDto>();
            CreateMap<OPowerOpenDto, OPowerOpen>();
            CreateMap<OPowerOpen, OPowerOpenDto>();
            CreateMap<OPowerPageDto, OPowerPage>();
            CreateMap<OPowerPage, OPowerPageDto>();
            CreateMap<OPowerPermissionDto, OPowerPermission>();
            CreateMap<OPowerPermission, OPowerPermissionDto>();
            CreateMap<OPowerRoleDto, OPowerRole>();
            CreateMap<OPowerRole, OPowerRoleDto>();
            CreateMap<OPowerSetDto, OPowerSet>();
            CreateMap<OPowerSet, OPowerSetDto>();
            CreateMap<OPowerSetListDto, OPowerSetList>();
            CreateMap<OPowerSetList, OPowerSetListDto>();
            CreateMap<ORepairRecommendDto, ORepairRecommend>();
            CreateMap<ORepairRecommend, ORepairRecommendDto>();
            CreateMap<OSignatureDto, OSignature>();
            CreateMap<OSignature, OSignatureDto>();
            CreateMap<OTableCodeDto, OTableCode>();
            CreateMap<OTableCode, OTableCodeDto>();
            CreateMap<OUserThirdAllowDto, OUserThirdAllow>();
            CreateMap<OUserThirdAllow, OUserThirdAllowDto>();
            CreateMap<OUserThirdUserDto, OUserThirdUser>();
            CreateMap<OUserThirdUser, OUserThirdUserDto>();
            CreateMap<PAccountDto, SysUserInfo>();
            CreateMap<SysUserInfo, PAccountDto>();
            CreateMap<PCompanyExtensionDto, PCompanyExtension>();
            CreateMap<PCompanyExtension, PCompanyExtensionDto>();
            CreateMap<PCompanyImageDto, PCompanyImage>();
            CreateMap<PCompanyImage, PCompanyImageDto>();
            CreateMap<PDepartmentAuditConfigDto, PDepartmentAuditConfig>();
            CreateMap<PDepartmentAuditConfig, PDepartmentAuditConfigDto>();
            CreateMap<PDepartmentAuditUserDto, PDepartmentAuditUser>();
            CreateMap<PDepartmentAuditUser, PDepartmentAuditUserDto>();
            CreateMap<PDepartmentDto, PDepartment>();
            CreateMap<PDepartment, PDepartmentDto>();
            CreateMap<PDepartmentModuleDto, PDepartmentModule>();
            CreateMap<PDepartmentModule, PDepartmentModuleDto>();
            CreateMap<PRoleDefaultBarDto, PRoleDefaultBar>();
            CreateMap<PRoleDefaultBar, PRoleDefaultBarDto>();
            CreateMap<PRoleDto, PRole>();
            CreateMap<PRole, PRoleDto>();
            CreateMap<PSchoolExtensionDto, PSchoolExtension>();
            CreateMap<PSchoolExtension, PSchoolExtensionDto>();
            CreateMap<PSessionRemotelyDto, PSessionRemotely>();
            CreateMap<PSessionRemotely, PSessionRemotelyDto>();
            CreateMap<PUnitBankAccountDto, PUnitBankAccount>();
            CreateMap<PUnitBankAccount, PUnitBankAccountDto>();
            CreateMap<PUnitDto, PUnit>();
            CreateMap<PUnit, PUnitDto>();
            CreateMap<PUnitExchangeDto, PUnitExchange>();
            CreateMap<PUnitExchange, PUnitExchangeDto>();
            CreateMap<PUnitPermissionDto, PUnitPermission>();
            CreateMap<PUnitPermission, PUnitPermissionDto>();
            CreateMap<PUserBankAccountDto, PUserBankAccount>();
            CreateMap<PUserBankAccount, PUserBankAccountDto>();
            CreateMap<PUserDto, SysUserExtension>();
            CreateMap<SysUserExtension, PUserDto>();
            CreateMap<PUserInDepartDto, PUserInDepart>();
            CreateMap<PUserInDepart, PUserInDepartDto>();
            CreateMap<PUserInRoleDto, PUserInRole>();
            CreateMap<PUserInRole, PUserInRoleDto>();
            CreateMap<PUserMultAccountDto, PUserMultAccount>();
            CreateMap<PUserMultAccount, PUserMultAccountDto>();
            CreateMap<PUserTrackDto, PUserTrack>();
            CreateMap<PUserTrack, PUserTrackDto>();
            CreateMap<WxApiCallDto, WxApiCall>();
            CreateMap<WxApiCall, WxApiCallDto>();
            CreateMap<WxAppManageDto, WxAppManage>();
            CreateMap<WxAppManage, WxAppManageDto>();
            CreateMap<WxAppMessageDto, WxAppMessage>();
            CreateMap<WxAppMessage, WxAppMessageDto>();
            CreateMap<WxBindFromPcDto, WxBindFromPc>();
            CreateMap<WxBindFromPc, WxBindFromPcDto>();
            CreateMap<WxMenuDto, WxMenu>();
            CreateMap<WxMenu, WxMenuDto>();
            CreateMap<WxMenuPermissionDto, WxMenuPermission>();
            CreateMap<WxMenuPermission, WxMenuPermissionDto>();
            CreateMap<WxMessageLimitDto, WxMessageLimit>();
            CreateMap<WxMessageLimit, WxMessageLimitDto>();
            CreateMap<WxMessageSetDto, WxMessageSet>();
            CreateMap<WxMessageSet, WxMessageSetDto>();
            CreateMap<WxQrcodeLoginDto, WxQrcodeLogin>();
            CreateMap<WxQrcodeLogin, WxQrcodeLoginDto>();
            CreateMap<WxUserAttentionDto, WxUserAttention>();
            CreateMap<WxUserAttention, WxUserAttentionDto>();
            CreateMap<WxUserBindOpenidDto, WxUserBindOpenid>();
            CreateMap<WxUserBindOpenid, WxUserBindOpenidDto>();
            CreateMap<WxUserOptDto, WxUserOpt>();
            CreateMap<WxUserOpt, WxUserOptDto>();
            CreateMap<BlogArticle, BlogViewModels>();
            CreateMap<BlogViewModels, BlogArticle>();
            CreateMap<VUserDetail, SysUserExtension>();
            CreateMap<WfModuleDto, WfModule>();
            CreateMap<WfModule, WfModuleDto>();
            CreateMap<WfProcessDto, WfProcess>();
            CreateMap<WfProcess, WfProcessDto>();
            CreateMap<WfProcessFieldDto, WfProcessField>();
            CreateMap<WfProcessField, WfProcessFieldDto>();
            CreateMap<WfProcessNodeDto, WfProcessNode>();
            CreateMap<WfProcessNode, WfProcessNodeDto>();
            CreateMap<WfDictionaryDto, WfDictionary>();
            CreateMap<WfDictionary, WfDictionaryDto>();
            CreateMap<XUniformAuditLogDto, XUniformAuditLog>();
            CreateMap<XUniformAuditLog, XUniformAuditLogDto>();
            CreateMap<XUniformClassDto, XUniformClass>();
            CreateMap<XUniformClass, XUniformClassDto>();
            CreateMap<XUniformConfigDto, XUniformConfig>();
            CreateMap<XUniformConfig, XUniformConfigDto>();
            CreateMap<XUniformEvaluateDetailDto, XUniformEvaluateDetail>();
            CreateMap<XUniformEvaluateDetail, XUniformEvaluateDetailDto>();
            CreateMap<XUniformEvaluateDto, XUniformEvaluate>();
            CreateMap<XUniformEvaluate, XUniformEvaluateDto>();
            CreateMap<XUniformCompanyDto, XUniformCompany>();
            CreateMap<XUniformCompany, XUniformCompanyDto>();
            CreateMap<XUniformOrderDetailDto, XUniformOrderDetail>();
            CreateMap<XUniformOrderDetail, XUniformOrderDetailDto>();
            CreateMap<XUniformOrganizationDto, XUniformOrganization>();
            CreateMap<XUniformOrganization, XUniformOrganizationDto>();
            CreateMap<XUniformPurchaseGradeDto, XUniformPurchaseGrade>();
            CreateMap<XUniformPurchaseGrade, XUniformPurchaseGradeDto>();
            CreateMap<XUniformParentPurchaseDto, XUniformParentPurchase>();
            CreateMap<XUniformParentPurchase, XUniformParentPurchaseDto>();
            CreateMap<XUniformPurchaseDto, XUniformPurchase>();
            CreateMap<XUniformPurchase, XUniformPurchaseDto>();
            CreateMap<XUniformQrCodeDto, XUniformQrCode>();
            CreateMap<XUniformQrCode, XUniformQrCodeDto>();
            CreateMap<XUniformSchemeDto, XUniformScheme>();
            CreateMap<XUniformScheme, XUniformSchemeDto>();
            CreateMap<XUniformSchemeOpinionDto, XUniformSchemeOpinion>();
            CreateMap<XUniformSchemeOpinion, XUniformSchemeOpinionDto>();
            CreateMap<XUniformShelfDto, XUniformShelf>();
            CreateMap<XUniformShelf, XUniformShelfDto>();
            CreateMap<XUniformCompanySizeDto, XUniformCompanySize>();
            CreateMap<XUniformCompanySize, XUniformCompanySizeDto>();
            CreateMap<XUniformShelfSizeDto, XUniformShelfSize>();
            CreateMap<XUniformShelfSize, XUniformShelfSizeDto>();
            CreateMap<XUniformStudentDto, XUniformStudent>();
            CreateMap<XUniformStudent, XUniformStudentDto>();
            CreateMap<PSupplierSchoolAuditDto, PSupplierSchoolAudit>();
            CreateMap<PSupplierSchoolAudit, PSupplierSchoolAuditDto>();
            CreateMap<PClassInfoDto, PClassInfo>();
            CreateMap<PClassInfo, PClassInfoDto>();
            CreateMap<PStudentDto, PStudent>();
            CreateMap<PStudent, PStudentDto>();
            CreateMap<BAttachmentConfigDto, BAttachmentConfig>();
            CreateMap<BAttachmentConfig, BAttachmentConfigDto>();
            CreateMap<BAttachmentDataDto, BAttachmentData>();
            CreateMap<BAttachmentData, BAttachmentDataDto>();
            CreateMap<XUniformChangeDto, XUniformChange>();
            CreateMap<XUniformChange, XUniformChangeDto>();
            CreateMap<PParentStudentDto, PParentStudent>();
            CreateMap<PParentStudent, PParentStudentDto>();
            CreateMap<XUniformSponsorDto, XUniformSponsor>();
            CreateMap<XUniformSponsor, XUniformSponsorDto>();
            CreateMap<SysRoleDto, SysRole>();
            CreateMap<SysRole, SysRoleDto>();
            CreateMap<WfProjectListHistoryDto, WfProjectListHistory>();
            CreateMap<WfProjectListHistory, WfProjectListHistoryDto>();
            CreateMap<WfProjectListDto, WfProjectList>();
            CreateMap<WfProjectList, WfProjectListDto>();
            CreateMap<WfProjectListFieldSetDto, WfProjectListFieldSet>();
            CreateMap<WfProjectListFieldSet, WfProjectListFieldSetDto>();
            CreateMap<WfProcessConditionDto, WfProcessCondition>();
            CreateMap<WfProcessCondition, WfProcessConditionDto>();
            CreateMap<WfProcessNodeLinkDto, WfProcessNodeLink>();
            CreateMap<WfProcessNodeLink, WfProcessNodeLinkDto>();
            CreateMap<WfProcessStatuzDto, WfProcessStatuz>();
            CreateMap<WfProcessStatuz, WfProcessStatuzDto>();
            CreateMap<WfSourceFundDto, WfSourceFund>();
            CreateMap<WfSourceFund, WfSourceFundDto>();
            CreateMap<WfPageColumnConfigDto, WfPageColumnConfig>();
            CreateMap<WfPageColumnConfig, WfPageColumnConfigDto>();
            CreateMap<WfPageDefinitionDto, WfPageDefinition>();
            CreateMap<WfPageDefinition, WfPageDefinitionDto>();

            CreateMap<WfProjectDeclarationDetailDto, WfProjectDeclarationDetail>();
            CreateMap<WfProjectDeclarationDetail, WfProjectDeclarationDetailDto>();
            CreateMap<WfProjectDeclarationDto, WfProjectDeclaration>();
            CreateMap<WfProjectDeclaration, WfProjectDeclarationDto>();
            CreateMap<WfProjectAuditDto, WfProjectAudit>();
            CreateMap<WfProjectAudit, WfProjectAuditDto>();
            CreateMap<WfProcessReturnSetDto, WfProcessReturnSet>();
            CreateMap<WfProcessReturnSet, WfProcessReturnSetDto>();

            CreateMap<WfProjectApprovalNoDto, WfProjectApprovalNo>();
            CreateMap<WfProjectApprovalNo, WfProjectApprovalNoDto>();
            CreateMap<WfProjectListHistoryDto, WfProjectListHistory>();
            CreateMap<WfProjectListHistory, WfProjectListHistoryDto>();
            CreateMap<WfProjectListReviewDto, WfProjectListReview>();
            CreateMap<WfProjectListReview, WfProjectListReviewDto>();

            CreateMap<WfSourceFundSetDto, WfSourceFundSet>();
            CreateMap<WfSourceFundSet, WfSourceFundSetDto>();

            CreateMap<WfCodeGenerateSetDto, WfCodeGenerateSet>();
            CreateMap<WfCodeGenerateSet, WfCodeGenerateSetDto>();

            CreateMap<ThEquipmentCategoryDto, ThEquipmentCategory>();
            CreateMap<ThEquipmentCategory, ThEquipmentCategoryDto>();
            CreateMap<ThEquipmentCategoryStageDto, ThEquipmentCategoryStage>();
            CreateMap<ThEquipmentCategoryStage, ThEquipmentCategoryStageDto>();

            CreateMap<BMsgConfigDto, BMsgConfig>();
            CreateMap<BMsgConfig, BMsgConfigDto>();
            CreateMap<WfMsgConfigDto, WfMsgConfig>();
            CreateMap<WfMsgConfig, WfMsgConfigDto>();

            CreateMap<WfGroupProcessSetDto, WfGroupProcessSet>();
            CreateMap<WfGroupProcessSet, WfGroupProcessSetDto>();
            CreateMap<WfGroupUnitSetDto, WfGroupUnitSet>();
            CreateMap<WfGroupUnitSet, WfGroupUnitSetDto>();
            CreateMap<SysUserInfo, SysUserInfoDto>()
                .ForMember(a => a.uID, o => o.MapFrom(d => d.Id))
                .ForMember(a => a.RIDs, o => o.MapFrom(d => d.RIDs))
                 .ForMember(a => a.UserValidate, o => o.MapFrom(d => d.UserValidate))
                .ForMember(a => a.uStatus, o => o.MapFrom(d => d.Statuz))
                .ForMember(a => a.uUpdateTime, o => o.MapFrom(d => d.UpdateTime))
                .ForMember(a => a.uCreateTime, o => o.MapFrom(d => d.CreateTime))
                .ForMember(a => a.uErrorCount, o => o.MapFrom(d => d.ErrorCount))
                .ForMember(a => a.uLastErrTime, o => o.MapFrom(d => d.LastErrorTime))
                .ForMember(a => a.uLoginName, o => o.MapFrom(d => d.LoginName))
                .ForMember(a => a.uLoginPWD, o => o.MapFrom(d => d.LoginPWD))
                .ForMember(a => a.uRemark, o => o.MapFrom(d => d.Remark))
                //.ForMember(a => a.uRealName, o => o.MapFrom(d => d.RealName))
                .ForMember(a => a.name, o => o.MapFrom(d => d.RealName))
                .ForMember(a => a.tdIsDelete, o => o.MapFrom(d => d.IsDeleted))
                .ForMember(a => a.RoleNames, o => o.MapFrom(d => d.RoleNames));
            CreateMap<SysUserInfoDto, SysUserInfo>()
                .ForMember(a => a.Id, o => o.MapFrom(d => d.uID))
                .ForMember(a => a.RIDs, o => o.MapFrom(d => d.RIDs))
                   .ForMember(a => a.UserValidate, o => o.MapFrom(d => d.UserValidate))
                .ForMember(a => a.Statuz, o => o.MapFrom(d => d.uStatus))
                .ForMember(a => a.UpdateTime, o => o.MapFrom(d => d.uUpdateTime))
                .ForMember(a => a.CreateTime, o => o.MapFrom(d => d.uCreateTime))
                .ForMember(a => a.ErrorCount, o => o.MapFrom(d => d.uErrorCount))
                .ForMember(a => a.LastErrorTime, o => o.MapFrom(d => d.uLastErrTime))
                .ForMember(a => a.LoginName, o => o.MapFrom(d => d.uLoginName))
                .ForMember(a => a.LoginPWD, o => o.MapFrom(d => d.uLoginPWD))
                .ForMember(a => a.Remark, o => o.MapFrom(d => d.uRemark))
                //.ForMember(a => a.RealName, o => o.MapFrom(d => d.uRealName))
                .ForMember(a => a.RealName, o => o.MapFrom(d => d.name))
                .ForMember(a => a.IsDeleted, o => o.MapFrom(d => d.tdIsDelete))
                .ForMember(a => a.RoleNames, o => o.MapFrom(d => d.RoleNames));
        }
    }
}
