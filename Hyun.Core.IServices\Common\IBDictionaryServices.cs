﻿using Hyun.Core.Model.Model;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///BDictionary接口方法
    ///</summary>
    public interface IBDictionaryServices : IBaseServices<BDictionary>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<BDictionary> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<BDictionary>> Find(Expression<Func<BDictionary, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">BDictionaryParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<BDictionary>> GetPaged(BDictionaryParam param);

        //<used>0</used>
        Task<Result> InsertUpdate(string typeCode, string typeName, string dicName, string dicValue, string memo, long pid, long id, int sequence);

        Task<Result> UpdateStatuz(long id);

        //<used>0</used>
        Task<Result> DeleteByIds(string ids);

        //<used>0</used>
        Task<List<DictionaryModel>> SearchDictionaryList();

        //<used>0</used>
        Task<List<BDictionary>> GetByTypeCode(string typeCode);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<BDictionaryDto>> GetGradeInfo();

        /// <summary>
        /// 根据学段集合获取年级信息
        /// </summary>
        /// <param name="strPeriod"></param>
        /// <returns></returns>
        Task<List<BDictionaryDto>> GetGradeByPeriod(string strPeriod);
    }
}

