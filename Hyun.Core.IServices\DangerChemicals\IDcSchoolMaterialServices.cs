﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcSchoolMaterial接口方法
    ///</summary>
    public interface IDcSchoolMaterialServices : IBaseServices<DcSchoolMaterial>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<DcSchoolMaterial> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<DcSchoolMaterial>> Find(Expression<Func<DcSchoolMaterial, bool>> expression);
        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">DcSchoolMaterialParam实体参数</param>
        /// <returns></returns>
        Task<List<DcSchoolMaterial>> Find(DcSchoolMaterialParam param);
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcSchoolMaterialParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<DcSchoolMaterial>> GetPaged(DcSchoolMaterialParam param);
        /// <summary>
        /// 获取当前库使用年度集合
        /// </summary>
        /// <param name="unitid">当前单位</param>
        /// <param name="unittypeid">单位类型</param>
        /// <returns></returns>
        Task<List<int>> GetYearz(long unitid, int unittypeid);
        //<used>1</used>
        Task<Result> BatchAudit(List<long> ids, long userId, long unitId);

        //<used>1</used>
        Task<Result> DeleteById(long id, long userId, long unitId);

        //<used>1</used>
        Task<Result> SetIsMayUse(long id, int isMayUse, long unitId);

        //<used>1</used>
        Task<Result> InsertUpdate(DcSchoolMaterialDto model);

        //<used>1</used>
        Task<int> BatchUpdateAddress(string ids, long addressId, string cabinetAddress, long unitId);

        //<used>1</used>
        Task<PageModel<VDcCountyStockNumStatistics>> GetCityStockNumStatistics(DcSchoolMaterialParam param);

        //<used>0</used>
        Task<DataTable> GetStockbookPurchase(int schoolId, string month, string orderBy, int start, int pageLength, int total);
        //<used>1</used>
        Task<PageModel<VDcCountyStockNumStatistics>> GetStockNumStatistics(VDcCountyStockNumStatisticsParam param);
        //<used>0</used>
        Task<Result> SplitMaterialByThirdCode(long id, long unitId);
        //<used>1</used>
        Task<Result> ReturnLibrary(long id, int statuz, long unitid, long userid);
        //<used>1</used>
        Task<Result> DcInventory_Save(long id, int statuz, long unitid, long userid);
        //<used>1</used>
        Task<Result> DcScrap_Save(long id, int statuz, int wasetStatuz, long unitid, long userid);

        #region 查询统计报表
        /// <summary>
        /// 市级按危化品属性统计库存
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcCityClassifyStockStatistics>> GetCityClassifyStockPaged(VDcCityClassifyStockStatisticsParam param);
        /// <summary>
        /// 按危化品属性统计库存
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcClassifyStockNumStatistics>> GetClassifyStockNumPaged(VDcClassifyStockNumStatisticsParam param);
        /// <summary>
        /// 统计数量
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<int> GetClassifyStockNumByName(VDcClassifyStockNumStatisticsParam param);
        /// <summary>
        /// 库存数量统计报表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcCountyStockNumStatistics>> GetCountyStockNumPaged(VDcCountyStockNumStatisticsParam param);
        /// <summary>
        /// 单位危化品盘点记录列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcInventoryList>> GetInventoryPaged(VDcInventoryListParam param);
        /// <summary>
        /// 盘点、退货、报废审核表  (原：V_dc_MaterialsNumAudit GetPaged)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcMaterialsNumAudit>> GetMaterialsNumPaged(VDcMaterialsNumAuditParam param);
        /// <summary>
        /// 单位危化品库统计列表 (原：V_dc_MaterialStatisticsList GetPaged)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcMaterialStatisticsList>> GetStatisticsListPaged(VDcMaterialStatisticsListParam param);

        /// <summary>
        /// 单位危化品库列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<List<VDcSchoolMaterialList>> FindList(VDcSchoolMaterialListParam param);

        /// <summary>
        /// 单位危化品库列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcSchoolMaterialList>> GetListPaged(VDcSchoolMaterialListParam param);
        /// <summary>
        /// 单位退货、盘点、报废列表 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcSchoolMaterialOptList>> GetOptPaged(VDcSchoolMaterialOptListParam param);
        #endregion

        #region 台账

        /// <summary>
        /// 采购台账
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<DcSchoolMaterialDto>> GetStockbookPurchase(DcSchoolMaterialModelParam param);

        #endregion
    }
}

