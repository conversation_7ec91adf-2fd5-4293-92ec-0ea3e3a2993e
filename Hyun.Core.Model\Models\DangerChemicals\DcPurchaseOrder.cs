namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///物品采购申请单
    ///</summary>
    [SugarTable("dc_PurchaseOrder","物品采购申请单")]
    public class DcPurchaseOrder : BaseEntity
    {

          public DcPurchaseOrder()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///采购批次
          /// </summary>
          [SugarColumn(Length = 31)]
          public string BatchNo { get; set; }

        /// <summary>
        ///参考金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Amount { get; set; }

           /// <summary>
           ///申请时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///申请人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///审批时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? AuditDate { get; set; }

           /// <summary>
           ///审批状态
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///入库状态（0：未入库 ；1：入库中；2：已入库）
          /// </summary>
          public int InputStatuz { get; set; }

           /// <summary>
           ///公安审批文件（采购清单中包含需要公安审批的危化品时必传）
          /// </summary>
          [SugarColumn(Length = 2000,IsNullable = true)]
          public string SecurityApprovalFile { get; set; }

           /// <summary>
           ///公安报备文件（审核入库时，如果存在需报备文件，需上传）
          /// </summary>
          [SugarColumn(Length = 2000,IsNullable = true)]
          public string ReportFile { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

