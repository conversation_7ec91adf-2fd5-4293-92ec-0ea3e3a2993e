
Date：2025-07-29 10:06:05.514
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:02.605
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:06.807
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:06.798
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.595
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.599
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.595
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.595
LogLevel：Information
Message：------------------ 
 User:[null]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.811
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.811
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.974
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.007
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:11.974
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.014
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.041
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:超级管理员 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.049
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:超级管理员 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.055
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.055
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.075
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.075
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.099
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [CreateTime] > @CreateTime1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@CreateTime1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.099
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [CreateTime] > @CreateTime1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@CreateTime1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.100
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:06:12 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.100
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:06:12 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.114
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:06:12 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.114
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:06:12 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.233
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.233
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.242
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:12.242
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:17.252
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:超级管理员 [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/7/29 10:06:16 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:06:12 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:17.252
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:超级管理员 [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/7/29 10:06:16 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:06:12 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:17.514
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:17.647
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:17.715
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:2 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:168 [Type]:Int32    
[Name]:@LoginCount [Value]:953 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:06:17 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:592116605120645 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:17.793
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:23.140
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:23.195
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (0,9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:23.502
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000007008,100000000000070,100000000007046,100000000007013,100000000007015,100000000007016,100000000007028,100000000007029,100000000007031,100000000007035,100000000007017,100000000007018,100000000007019,100000000007033,100000000002016,100000000007022,100000000007034,100000000007023,100000000007024,100000000007025,100000000007038,100000000007042,100000000007043,100000000007044,796311544399925248,796312177270067200,796312847276576768,796372829267300352,797136060520861696,798916755765334016,799223303456690176,800012448240242688,811595703355707392,811595997955231744,811596413153579008,812342346623488000,812342705832071168,814068333472124928,814068545234145280,100000000004007,100000000004008,100000000007047,814851792939520000,824587920886206464,814855268880879616,827558792978763776,840600506052120576,840599930178375680,844980093162885120,844978402241810432,844957620178522112,844993843601346560,844994138360254464,844996768176279552,844996891459457024,852930755125317632,853563899415367680,853564152633888768)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:23.704
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst8) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst9) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst10) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst11) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst12) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst13) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst15  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted7 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted5 ) AND ( [PAU].[AuditUserId] = @AuditUserId6 ))  AND ( [PAU].[IsDeleted] = @IsDeleted7 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId6 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@MethodConst8 [Value]: [Type]:String    
[Name]:@MethodConst9 [Value]:0 [Type]:Int32    
[Name]:@MethodConst10 [Value]: [Type]:String    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:23.714
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , @constant4 AS [ProcessId] , @constant5 AS [ProcessName] , @constant6 AS [ProcessNodeId] , @constant7 AS [ProcessNodeName] , @constant8 AS [NodeType] , @constant9 AS [TreatHandle] , @constant10 AS [TreatHandleUrl] , @constant11 AS [StopHandle] , @constant12 AS [StopHandleUrl] , CAST([M].[Sort] AS BIGINT) AS [Sort] , @constant13 AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Module] [M] ON (( [PAU].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted0 )) AND ( [M].[IsDeleted] = @IsDeleted3 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted1 ) AND ( [PAU].[AuditUserId] = @AuditUserId2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@constant4 [Value]:0 [Type]:Int64    
[Name]:@constant5 [Value]: [Type]:String    
[Name]:@constant6 [Value]:0 [Type]:Int64    
[Name]:@constant7 [Value]: [Type]:String    
[Name]:@constant8 [Value]:3 [Type]:Int32    
[Name]:@constant9 [Value]: [Type]:String    
[Name]:@constant10 [Value]: [Type]:String    
[Name]:@constant11 [Value]: [Type]:String    
[Name]:@constant12 [Value]: [Type]:String    
[Name]:@constant13 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:23.968
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:23.995
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:24.028
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:24.100
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id] FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:24.118
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 50 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:28.825
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:28.834
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:39.968
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [m].[Id] AS [Id] , [m].[IsDeleted] AS [IsDeleted] , [m].[CreateTime] AS [CreateTime] , [m].[Name] AS [Name] , [m].[Logo] AS [Logo] , [m].[Sort] AS [Sort] , [m].[Statuz] AS [Statuz] , [u].[Name] AS [UnitName] , CAST([m].[Usage] AS NVARCHAR(MAX)) AS [StrUsage] , CAST([m].[UseUnitId] AS NVARCHAR(MAX)) AS [StrUseUnitId]  FROM [wf_Module] [m] Inner JOIN [p_Unit] [u] ON ( [m].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [m].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:39.978
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [m].[Id] AS [Id] , [m].[IsDeleted] AS [IsDeleted] , [m].[CreateTime] AS [CreateTime] , [m].[Name] AS [Name] , [m].[Logo] AS [Logo] , [m].[Sort] AS [Sort] , [m].[Statuz] AS [Statuz] , [u].[Name] AS [UnitName] , CAST([m].[Usage] AS NVARCHAR(MAX)) AS [StrUsage] , CAST([m].[UseUnitId] AS NVARCHAR(MAX)) AS [StrUseUnitId]  FROM [wf_Module] [m] Inner JOIN [p_Unit] [u] ON ( [m].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [m].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:40.057
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Statuz] = @Statuz1 )) AND ((( [UnitType] =@constant3) OR ( [UnitType] =@constant5)) OR ( [UnitType] =@constant7)))  AND ( [IsDeleted] = @IsDeleted8 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:1 [Type]:Int32    
[Name]:@constant5 [Value]:2 [Type]:Int32    
[Name]:@constant7 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:50.439
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:50.460
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:50.467
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:06:50.559
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( 1 = 1 ) AND( [IsDeleted] = @IsDeleted1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:09:09.072
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:09:09.089
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:09:09.093
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:09:09.118
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:09:09.124
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY ModuleId DESC) AS RowIndex  FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:09:09.221
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:09:09.262
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Statuz] = @Statuz1 )) AND ((( [UnitType] =@constant3) OR ( [UnitType] =@constant5)) OR ( [UnitType] =@constant7)))  AND ( [IsDeleted] = @IsDeleted8 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:1 [Type]:Int32    
[Name]:@constant5 [Value]:2 [Type]:Int32    
[Name]:@constant7 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:12.256
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:12.257
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:12.259
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:12.300
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:12.339
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:12.351
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:14:12 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:12.358
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:14:12 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.870
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.886
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.895
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:浦口教育局 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.899
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.905
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.911
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [CreateTime] > @CreateTime1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@CreateTime1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.916
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:15.921
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.427
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:浦口教育局 [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/7/29 10:14:15 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:14:15 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.440
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.446
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.450
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:2 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:76 [Type]:Int32    
[Name]:@LoginCount [Value]:278 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:14:16 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:592404813029509 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.467
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (20,20,251,252)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.699
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.731
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (20,251,252,271)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:16.955
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000001005,100000000001006,100000000000010,100000000000020,100000000002001,100000000002017,100000000000030,100000000003002,100000000000050,100000000007045,100000000000070,100000000007005,100000000007030,100000000007008,100000000007012,100000000007011,100000000007010,100000000007009,100000000007017,100000000007018,100000000007019,100000000007033,100000000002016,100000000007022,100000000002011,100000000002014,100000000002015,814917889516638208,844961288042844160,844962390851194880,844962725669900288,844963762673815552,844964012524310528,844964264702644224,844964418251919360,844964627275059200,844964778957869056,844964906741534720,844965103492141056,844965330131357696,844965468845379584,844965601356025856,844965719383740416,844965880776364032,844966005871480832,844966233722851328,844957453845008384,844961933143576576,844986554215567360,844986757417013248,844987139488747520,844986894205849600,844987320569434112,844987457018531840,844987604456706048,844987767002763264,844987948758732800,844988115859804160,844988224278368256,844988563278794752,844988671735107584,844988795462881280,844988925083652096,844989215463706624,844989331301994496,844993843601346560,844994285953617920,844994452098387968,844994601428193280,844996630775074816,844996440420782080,844996341519093760,844996115576131584,844994692171960320,844957620178522112,845654641256435712,852930331584499712,852931042762297344,852932243931271168,853563899415367680,853564152633888768,845256634027479040,845256778026323968,845256275557093376,863478789638721536)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:17.039
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst8) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst9) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst10) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst11) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst12) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst13) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst15  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted7 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted5 ) AND ( [PAU].[AuditUserId] = @AuditUserId6 ))  AND ( [PAU].[IsDeleted] = @IsDeleted7 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId6 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@MethodConst8 [Value]: [Type]:String    
[Name]:@MethodConst9 [Value]:0 [Type]:Int32    
[Name]:@MethodConst10 [Value]: [Type]:String    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:17.045
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , @constant4 AS [ProcessId] , @constant5 AS [ProcessName] , @constant6 AS [ProcessNodeId] , @constant7 AS [ProcessNodeName] , @constant8 AS [NodeType] , @constant9 AS [TreatHandle] , @constant10 AS [TreatHandleUrl] , @constant11 AS [StopHandle] , @constant12 AS [StopHandleUrl] , CAST([M].[Sort] AS BIGINT) AS [Sort] , @constant13 AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Module] [M] ON (( [PAU].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted0 )) AND ( [M].[IsDeleted] = @IsDeleted3 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted1 ) AND ( [PAU].[AuditUserId] = @AuditUserId2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@constant4 [Value]:0 [Type]:Int64    
[Name]:@constant5 [Value]: [Type]:String    
[Name]:@constant6 [Value]:0 [Type]:Int64    
[Name]:@constant7 [Value]: [Type]:String    
[Name]:@constant8 [Value]:3 [Type]:Int32    
[Name]:@constant9 [Value]: [Type]:String    
[Name]:@constant10 [Value]: [Type]:String    
[Name]:@constant11 [Value]: [Type]:String    
[Name]:@constant12 [Value]: [Type]:String    
[Name]:@constant13 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:17.206
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:17.215
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:17.220
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:19.032
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo] , [scheme].[PurchaseMethodIds] AS [PurchaseMethodIds] , [scheme].[PurchaseMethodNames] AS [PurchaseMethodNames] , [school].[Name] AS [SchoolName] , [school].[IsCountyManager] AS [IsCountyManager]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted2 )   WHERE ( [school].[PId] = @PId0 )  AND ( [scheme].[SolicitedStatuz] = @SolicitedStatuz1 )  AND ( [scheme].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@SolicitedStatuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:19.041
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY  CreateTime DESC ) AS RowIndex  FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo] , [scheme].[PurchaseMethodIds] AS [PurchaseMethodIds] , [scheme].[PurchaseMethodNames] AS [PurchaseMethodNames] , [school].[Name] AS [SchoolName] , [school].[IsCountyManager] AS [IsCountyManager]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted2 )   WHERE ( [school].[PId] = @PId0 )  AND ( [scheme].[SolicitedStatuz] = @SolicitedStatuz1 )  AND ( [scheme].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@SolicitedStatuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:19.138
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (((( [PId] = @PId0 ) AND ( [UnitType] =@constant2)) AND ( [IsDeleted] = @IsDeleted3 )) AND ( [Statuz] = @Statuz4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@constant2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:19.212
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[UnitId],[Title],[Explanation],[Sort],[Statuz],[ValueNum],[ValueDecimal],[Memo],[DicValue],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [x_UniformConfig]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Code] = CAST(@constant2 AS NVARCHAR(MAX)))) AND (( [UnitId] = @UnitId3 ) OR ( [UnitId] = @UnitId4 )))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@constant2 [Value]:1000 [Type]:Int32    
[Name]:@UnitId3 [Value]:*************** [Type]:Int64    
[Name]:@UnitId4 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:44.505
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:44.508
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:44.508
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:44.547
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:44.614
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:44.624
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:14:44 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:14:44.629
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:14:44 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.235
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.343
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.350
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:浦口教育局 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.353
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.359
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.367
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.371
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.383
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.904
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:浦口教育局 [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/7/29 10:15:26 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:15:26 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.912
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.925
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.929
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:2 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:76 [Type]:Int32    
[Name]:@LoginCount [Value]:279 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:15:26 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:592404813029509 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:26.941
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (20,20,251,252)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.117
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.157
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (20,251,252,271)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.365
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000001005,100000000001006,100000000000010,100000000000020,100000000002001,100000000002017,100000000000030,100000000003002,100000000000050,100000000007045,100000000000070,100000000007005,100000000007030,100000000007008,100000000007012,100000000007011,100000000007010,100000000007009,100000000007017,100000000007018,100000000007019,100000000007033,100000000002016,100000000007022,100000000002011,100000000002014,100000000002015,814917889516638208,844961288042844160,844962390851194880,844962725669900288,844963762673815552,844964012524310528,844964264702644224,844964418251919360,844964627275059200,844964778957869056,844964906741534720,844965103492141056,844965330131357696,844965468845379584,844965601356025856,844965719383740416,844965880776364032,844966005871480832,844966233722851328,844957453845008384,844961933143576576,844986554215567360,844986757417013248,844987139488747520,844986894205849600,844987320569434112,844987457018531840,844987604456706048,844987767002763264,844987948758732800,844988115859804160,844988224278368256,844988563278794752,844988671735107584,844988795462881280,844988925083652096,844989215463706624,844989331301994496,844993843601346560,844994285953617920,844994452098387968,844994601428193280,844996630775074816,844996440420782080,844996341519093760,844996115576131584,844994692171960320,844957620178522112,845654641256435712,852930331584499712,852931042762297344,852932243931271168,853563899415367680,853564152633888768,845256634027479040,845256778026323968,845256275557093376,863478789638721536)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.495
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst8) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst9) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst10) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst11) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst12) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst13) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst15  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted7 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted5 ) AND ( [PAU].[AuditUserId] = @AuditUserId6 ))  AND ( [PAU].[IsDeleted] = @IsDeleted7 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId6 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@MethodConst8 [Value]: [Type]:String    
[Name]:@MethodConst9 [Value]:0 [Type]:Int32    
[Name]:@MethodConst10 [Value]: [Type]:String    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.500
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , @constant4 AS [ProcessId] , @constant5 AS [ProcessName] , @constant6 AS [ProcessNodeId] , @constant7 AS [ProcessNodeName] , @constant8 AS [NodeType] , @constant9 AS [TreatHandle] , @constant10 AS [TreatHandleUrl] , @constant11 AS [StopHandle] , @constant12 AS [StopHandleUrl] , CAST([M].[Sort] AS BIGINT) AS [Sort] , @constant13 AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Module] [M] ON (( [PAU].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted0 )) AND ( [M].[IsDeleted] = @IsDeleted3 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted1 ) AND ( [PAU].[AuditUserId] = @AuditUserId2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@constant4 [Value]:0 [Type]:Int64    
[Name]:@constant5 [Value]: [Type]:String    
[Name]:@constant6 [Value]:0 [Type]:Int64    
[Name]:@constant7 [Value]: [Type]:String    
[Name]:@constant8 [Value]:3 [Type]:Int32    
[Name]:@constant9 [Value]: [Type]:String    
[Name]:@constant10 [Value]: [Type]:String    
[Name]:@constant11 [Value]: [Type]:String    
[Name]:@constant12 [Value]: [Type]:String    
[Name]:@constant13 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.615
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.635
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.648
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.665
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo] , [scheme].[PurchaseMethodIds] AS [PurchaseMethodIds] , [scheme].[PurchaseMethodNames] AS [PurchaseMethodNames] , [school].[Name] AS [SchoolName] , [school].[IsCountyManager] AS [IsCountyManager]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted2 )   WHERE ( [school].[PId] = @PId0 )  AND ( [scheme].[SolicitedStatuz] = @SolicitedStatuz1 )  AND ( [scheme].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@SolicitedStatuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.671
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY  CreateTime DESC ) AS RowIndex  FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo] , [scheme].[PurchaseMethodIds] AS [PurchaseMethodIds] , [scheme].[PurchaseMethodNames] AS [PurchaseMethodNames] , [school].[Name] AS [SchoolName] , [school].[IsCountyManager] AS [IsCountyManager]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted2 )   WHERE ( [school].[PId] = @PId0 )  AND ( [scheme].[SolicitedStatuz] = @SolicitedStatuz1 )  AND ( [scheme].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@SolicitedStatuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.731
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (((( [PId] = @PId0 ) AND ( [UnitType] =@constant2)) AND ( [IsDeleted] = @IsDeleted3 )) AND ( [Statuz] = @Statuz4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@constant2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:27.773
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[UnitId],[Title],[Explanation],[Sort],[Statuz],[ValueNum],[ValueDecimal],[Memo],[DicValue],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [x_UniformConfig]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Code] = CAST(@constant2 AS NVARCHAR(MAX)))) AND (( [UnitId] = @UnitId3 ) OR ( [UnitId] = @UnitId4 )))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@constant2 [Value]:1000 [Type]:Int32    
[Name]:@UnitId3 [Value]:*************** [Type]:Int64    
[Name]:@UnitId4 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:39.348
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:39.359
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:39.365
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:41.271
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PAU].[Id] AS [Id] , [PAU].[BusinessType] AS [BusinessType] , [PAU].[ProcessId] AS [ProcessId] , [PAU].[ProcessNodeId] AS [ProcessNodeId] , [PAU].[AuditUnitId] AS [AuditUnitId] , [PAU].[AuditUserId] AS [AuditUserId] , [UE].[Name] AS [AuditUserName]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [SysUserExtension] [UE] ON ( [PAU].[AuditUserId] = [UE].[Id] ) AND ( [UE].[IsDeleted] = @IsDeleted3 )   WHERE ((( [PAU].[AuditUnitId] = @AuditUnitId0 ) AND ( [PAU].[IsDeleted] = @IsDeleted1 )) AND ( [UE].[IsDeleted] = @IsDeleted2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@AuditUnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:41.285
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , [P].[IsOpen] AS [IsOpen]  FROM [wf_Module] [M] Inner JOIN [wf_Process] [P] ON ( [M].[Id] = [P].[ModuleId] ) AND ( [P].[IsDeleted] = @IsDeleted12 )   WHERE ((((( [M].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [M].[Statuz] = @Statuz2 )) AND ( [P].[Statuz] = @Statuz3 )) AND ((((( [P].[Usage] = @Usage4 ) AND ( [P].[UseUnitId] = @UseUnitId5 )) OR (( [P].[Usage] = @Usage6 ) AND ( [P].[UseUnitId] = @UseUnitId7 ))) OR (( [P].[Usage] = @Usage8 ) AND ( [P].[UseUnitId] = @UseUnitId9 ))) OR (( [P].[Usage] = @Usage10 ) AND ( [P].[UseUnitId] = @UseUnitId11 ))))  AND ( [M].[IsDeleted] = @IsDeleted12 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@Usage4 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId5 [Value]:*************** [Type]:Int64    
[Name]:@Usage6 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId7 [Value]:*************** [Type]:Int64    
[Name]:@Usage8 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId9 [Value]:592122710352005 [Type]:Int64    
[Name]:@Usage10 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId11 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted12 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:15:41.311
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PRS].[ModuleId] AS [ModuleId] , [M].[Name] AS [ModuleName] , [PRS].[ProcessId] AS [ProcessId] , [PRS].[ProcessNodeId] AS [ProcessNodeId] , [P].[ProcessName] AS [ProcessName] , [PN].[NodeShowName] AS [ProcessNodeName] , [PN].[RoleName] AS [RoleName] , @constant14 AS [AuditUserName] , ISNULL([PRS].[Sort],@MethodConst15) AS [Sort] , [P].[IsOpen] AS [IsOpen] , ISNULL([LEOS].[IsLook],@MethodConst16) AS [IsLook]  FROM [wf_ProcessReturnSet] [PRS] Inner JOIN [wf_Module] [M] ON ( [PRS].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted13 )  Inner JOIN [wf_Process] [P] ON ( [PRS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted13 )  Left JOIN [wf_ProcessNode] [PN] ON ( [PRS].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted13 )  Left JOIN [wf_LookEachOtherSet] [LEOS] ON ((( [LEOS].[ProcessId] = [P].[Id] ) AND ( [LEOS].[ProcessNodeId] = [PN].[Id] )) AND ( [LEOS].[UnitId] = @UnitId0 )) AND ( [LEOS].[IsDeleted] = @IsDeleted13 )   WHERE ((((( [PRS].[IsDeleted] = @IsDeleted1 ) AND ( [PN].[ProcessLevel] = @ProcessLevel2 )) AND ( [M].[Statuz] = @Statuz3 )) AND ( [P].[Statuz] = @Statuz4 )) AND ((((( [P].[Usage] = @Usage5 ) AND ( [P].[UseUnitId] = @UseUnitId6 )) OR (( [P].[Usage] = @Usage7 ) AND ( [P].[UseUnitId] = @UseUnitId8 ))) OR (( [P].[Usage] = @Usage9 ) AND ( [P].[UseUnitId] = @UseUnitId10 ))) OR (( [P].[Usage] = @Usage11 ) AND ( [P].[UseUnitId] = @UseUnitId12 ))))  AND ( [PRS].[IsDeleted] = @IsDeleted13 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@ProcessLevel2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@Usage5 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId6 [Value]:*************** [Type]:Int64    
[Name]:@Usage7 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId8 [Value]:*************** [Type]:Int64    
[Name]:@Usage9 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId10 [Value]:592122710352005 [Type]:Int64    
[Name]:@Usage11 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId12 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted13 [Value]:False [Type]:Boolean    
[Name]:@constant14 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
[Name]:@MethodConst16 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:17.458
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:17.458
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:17.494
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:17.509
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:17.557
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:17.571
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:22:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:17.576
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:22:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.678
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.694
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.702
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:超级管理员 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.705
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.710
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.717
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.721
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:28.725
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.240
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:超级管理员 [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/7/29 10:22:28 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:22:28 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.245
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.250
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.253
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:2 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:168 [Type]:Int32    
[Name]:@LoginCount [Value]:954 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:22:29 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:592116605120645 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.261
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.395
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.432
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (0,9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.517
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000007008,100000000000070,100000000007046,100000000007013,100000000007015,100000000007016,100000000007028,100000000007029,100000000007031,100000000007035,100000000007017,100000000007018,100000000007019,100000000007033,100000000002016,100000000007022,100000000007034,100000000007023,100000000007024,100000000007025,100000000007038,100000000007042,100000000007043,100000000007044,796311544399925248,796312177270067200,796312847276576768,796372829267300352,797136060520861696,798916755765334016,799223303456690176,800012448240242688,811595703355707392,811595997955231744,811596413153579008,812342346623488000,812342705832071168,814068333472124928,814068545234145280,100000000004007,100000000004008,100000000007047,814851792939520000,824587920886206464,814855268880879616,827558792978763776,840600506052120576,840599930178375680,844980093162885120,844978402241810432,844957620178522112,844993843601346560,844994138360254464,844996768176279552,844996891459457024,852930755125317632,853563899415367680,853564152633888768)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.542
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst8) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst9) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst10) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst11) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst12) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst13) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst15  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted7 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted5 ) AND ( [PAU].[AuditUserId] = @AuditUserId6 ))  AND ( [PAU].[IsDeleted] = @IsDeleted7 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId6 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@MethodConst8 [Value]: [Type]:String    
[Name]:@MethodConst9 [Value]:0 [Type]:Int32    
[Name]:@MethodConst10 [Value]: [Type]:String    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.546
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , @constant4 AS [ProcessId] , @constant5 AS [ProcessName] , @constant6 AS [ProcessNodeId] , @constant7 AS [ProcessNodeName] , @constant8 AS [NodeType] , @constant9 AS [TreatHandle] , @constant10 AS [TreatHandleUrl] , @constant11 AS [StopHandle] , @constant12 AS [StopHandleUrl] , CAST([M].[Sort] AS BIGINT) AS [Sort] , @constant13 AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Module] [M] ON (( [PAU].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted0 )) AND ( [M].[IsDeleted] = @IsDeleted3 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted1 ) AND ( [PAU].[AuditUserId] = @AuditUserId2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@constant4 [Value]:0 [Type]:Int64    
[Name]:@constant5 [Value]: [Type]:String    
[Name]:@constant6 [Value]:0 [Type]:Int64    
[Name]:@constant7 [Value]: [Type]:String    
[Name]:@constant8 [Value]:3 [Type]:Int32    
[Name]:@constant9 [Value]: [Type]:String    
[Name]:@constant10 [Value]: [Type]:String    
[Name]:@constant11 [Value]: [Type]:String    
[Name]:@constant12 [Value]: [Type]:String    
[Name]:@constant13 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.613
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.621
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.625
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.685
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id] FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:29.689
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 50 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:37.802
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:37.806
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:37.827
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:37.832
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:37.883
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:46.203
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:46.214
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:46.219
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:46.230
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:46.234
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:46.276
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:48.886
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:48.891
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:48.903
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:676352991015045 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:48.932
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:48.933
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:48.938
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:48.938
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:49.339
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:49.383
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:49.423
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:49.424
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:22:49.430
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Sequence asc) AS RowIndex  FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10000 
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:23:31.224
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:23:31.245
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:23:31.260
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:23:31.400
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:51000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:27.784
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:27.797
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:27.801
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:27.813
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:27.822
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:27.858
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:28.435
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:28.441
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:28.451
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:28.457
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY ModuleId DESC) AS RowIndex  FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:28.497
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:28.536
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Statuz] = @Statuz1 )) AND ((( [UnitType] =@constant3) OR ( [UnitType] =@constant5)) OR ( [UnitType] =@constant7)))  AND ( [IsDeleted] = @IsDeleted8 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:1 [Type]:Int32    
[Name]:@constant5 [Value]:2 [Type]:Int32    
[Name]:@constant7 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:44.459
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:44.468
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:44.471
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:44.487
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:701436251205765 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:44.530
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE ( [ModuleId] = @ModuleId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:44.608
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessNodeId],[FieldId],[ControlType],[ApiUrl],[FieldType],[FieldSource],[FieldCode],[FieldName],[ShowName],[HelpRemark],[HelpShowMode],[TypeBox],[IsRequired],[Statuz],[ValidType],[SqlCondition],[IsCondition],[Sort],[TypeCode],[TemplateUrl],[Width],[TitleStyle],[ContentStyle],[IsSort],[Pid],[ProjectListType],[ProjectListTitle],[ProjectListDesc],[SelfFundName],[IsInheritData],[DefaultValue],[ParentCode],[MasterControl],[Controlled],[IsAmountControl],[IsDetail],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessField]  WHERE (((( [ModuleId] = @ModuleId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [IsCondition] = @IsCondition2 )) AND ( [IsRequired] = @IsRequired3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:701431378894981 [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsCondition2 [Value]:1 [Type]:Int32    
[Name]:@IsRequired3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:45.474
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:45.480
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:45.494
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:45.499
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY ModuleId DESC) AS RowIndex  FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:45.537
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:45.574
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Statuz] = @Statuz1 )) AND ((( [UnitType] =@constant3) OR ( [UnitType] =@constant5)) OR ( [UnitType] =@constant7)))  AND ( [IsDeleted] = @IsDeleted8 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:1 [Type]:Int32    
[Name]:@constant5 [Value]:2 [Type]:Int32    
[Name]:@constant7 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:48.208
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:48.215
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:48.224
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:698593675456645 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:48.244
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE ( [ModuleId] = @ModuleId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:29:48.267
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessNodeId],[FieldId],[ControlType],[ApiUrl],[FieldType],[FieldSource],[FieldCode],[FieldName],[ShowName],[HelpRemark],[HelpShowMode],[TypeBox],[IsRequired],[Statuz],[ValidType],[SqlCondition],[IsCondition],[Sort],[TypeCode],[TemplateUrl],[Width],[TitleStyle],[ContentStyle],[IsSort],[Pid],[ProjectListType],[ProjectListTitle],[ProjectListDesc],[SelfFundName],[IsInheritData],[DefaultValue],[ParentCode],[MasterControl],[Controlled],[IsAmountControl],[IsDetail],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessField]  WHERE (((( [ModuleId] = @ModuleId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [IsCondition] = @IsCondition2 )) AND ( [IsRequired] = @IsRequired3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:698593460789381 [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsCondition2 [Value]:1 [Type]:Int32    
[Name]:@IsRequired3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.069
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.082
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.086
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.094
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:676352991015045 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.218
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.218
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.222
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.223
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.297
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.298
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.302
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Sequence asc) AS RowIndex  FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10000 
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.338
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:09.373
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:19.214
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:19.224
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:19.228
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:33:19.279
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:15000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:48.076
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:48.185
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:48.186
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:48.246
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:48.286
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:48.295
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:35:48 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:48.299
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/29 10:35:48 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:49.999
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.013
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.018
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:浦口教育局 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.022
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.026
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.031
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.035
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.039
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.543
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:浦口教育局 [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/7/29 10:35:50 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:35:50 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.550
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.555
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.559
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:2 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:76 [Type]:Int32    
[Name]:@LoginCount [Value]:280 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/29 10:35:50 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:592404813029509 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.569
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (20,20,251,252,271)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.730
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.768
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (20,251,252,271)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.923
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000001005,100000000001006,100000000000010,100000000000020,100000000002001,100000000002017,100000000000030,100000000003002,100000000000050,100000000007045,100000000000070,100000000007005,100000000007030,100000000007008,100000000007012,100000000007011,100000000007010,100000000007009,100000000007017,100000000007018,100000000007019,100000000007033,100000000002016,100000000007022,100000000002011,100000000002014,100000000002015,814917889516638208,844961288042844160,844962390851194880,844962725669900288,844963762673815552,844964012524310528,844964264702644224,844964418251919360,844964627275059200,844964778957869056,844964906741534720,844965103492141056,844965330131357696,844965468845379584,844965601356025856,844965719383740416,844965880776364032,844966005871480832,844966233722851328,844957453845008384,844961933143576576,844986554215567360,844986757417013248,844987139488747520,844986894205849600,844987320569434112,844987457018531840,844987604456706048,844987767002763264,844987948758732800,844988115859804160,844988224278368256,844988563278794752,844988671735107584,844988795462881280,844988925083652096,844989215463706624,844989331301994496,844991713553420288,844993843601346560,844994285953617920,844994452098387968,844994601428193280,844996630775074816,844996440420782080,844996341519093760,844996115576131584,844994692171960320,844957620178522112,845654641256435712,852930331584499712,852931042762297344,852932243931271168,853563899415367680,853564152633888768)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.985
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst8) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst9) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst10) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst11) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst12) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst13) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst15  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted7 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted5 ) AND ( [PAU].[AuditUserId] = @AuditUserId6 ))  AND ( [PAU].[IsDeleted] = @IsDeleted7 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId6 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@MethodConst8 [Value]: [Type]:String    
[Name]:@MethodConst9 [Value]:0 [Type]:Int32    
[Name]:@MethodConst10 [Value]: [Type]:String    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:50.990
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , @constant4 AS [ProcessId] , @constant5 AS [ProcessName] , @constant6 AS [ProcessNodeId] , @constant7 AS [ProcessNodeName] , @constant8 AS [NodeType] , @constant9 AS [TreatHandle] , @constant10 AS [TreatHandleUrl] , @constant11 AS [StopHandle] , @constant12 AS [StopHandleUrl] , CAST([M].[Sort] AS BIGINT) AS [Sort] , @constant13 AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Module] [M] ON (( [PAU].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted0 )) AND ( [M].[IsDeleted] = @IsDeleted3 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted1 ) AND ( [PAU].[AuditUserId] = @AuditUserId2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@constant4 [Value]:0 [Type]:Int64    
[Name]:@constant5 [Value]: [Type]:String    
[Name]:@constant6 [Value]:0 [Type]:Int64    
[Name]:@constant7 [Value]: [Type]:String    
[Name]:@constant8 [Value]:3 [Type]:Int32    
[Name]:@constant9 [Value]: [Type]:String    
[Name]:@constant10 [Value]: [Type]:String    
[Name]:@constant11 [Value]: [Type]:String    
[Name]:@constant12 [Value]: [Type]:String    
[Name]:@constant13 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:51.129
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:51.147
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:51.157
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:51.175
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo] , [scheme].[PurchaseMethodIds] AS [PurchaseMethodIds] , [scheme].[PurchaseMethodNames] AS [PurchaseMethodNames] , [school].[Name] AS [SchoolName] , [school].[IsCountyManager] AS [IsCountyManager]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted2 )   WHERE ( [school].[PId] = @PId0 )  AND ( [scheme].[SolicitedStatuz] = @SolicitedStatuz1 )  AND ( [scheme].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@SolicitedStatuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:51.179
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY  CreateTime DESC ) AS RowIndex  FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo] , [scheme].[PurchaseMethodIds] AS [PurchaseMethodIds] , [scheme].[PurchaseMethodNames] AS [PurchaseMethodNames] , [school].[Name] AS [SchoolName] , [school].[IsCountyManager] AS [IsCountyManager]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted2 )   WHERE ( [school].[PId] = @PId0 )  AND ( [scheme].[SolicitedStatuz] = @SolicitedStatuz1 )  AND ( [scheme].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@SolicitedStatuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:51.248
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (((( [PId] = @PId0 ) AND ( [UnitType] =@constant2)) AND ( [IsDeleted] = @IsDeleted3 )) AND ( [Statuz] = @Statuz4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@PId0 [Value]:*************** [Type]:Int64    
[Name]:@constant2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:51.326
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[UnitId],[Title],[Explanation],[Sort],[Statuz],[ValueNum],[ValueDecimal],[Memo],[DicValue],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [x_UniformConfig]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Code] = CAST(@constant2 AS NVARCHAR(MAX)))) AND (( [UnitId] = @UnitId3 ) OR ( [UnitId] = @UnitId4 )))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@constant2 [Value]:1000 [Type]:Int32    
[Name]:@UnitId3 [Value]:*************** [Type]:Int64    
[Name]:@UnitId4 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:57.096
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:57.102
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:57.111
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PAU].[Id] AS [Id] , [PAU].[BusinessType] AS [BusinessType] , [PAU].[ProcessId] AS [ProcessId] , [PAU].[ProcessNodeId] AS [ProcessNodeId] , [PAU].[AuditUnitId] AS [AuditUnitId] , [PAU].[AuditUserId] AS [AuditUserId] , [UE].[Name] AS [AuditUserName]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [SysUserExtension] [UE] ON ( [PAU].[AuditUserId] = [UE].[Id] ) AND ( [UE].[IsDeleted] = @IsDeleted3 )   WHERE ((( [PAU].[AuditUnitId] = @AuditUnitId0 ) AND ( [PAU].[IsDeleted] = @IsDeleted1 )) AND ( [UE].[IsDeleted] = @IsDeleted2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@AuditUnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:57.117
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , [P].[IsOpen] AS [IsOpen]  FROM [wf_Module] [M] Inner JOIN [wf_Process] [P] ON ( [M].[Id] = [P].[ModuleId] ) AND ( [P].[IsDeleted] = @IsDeleted12 )   WHERE ((((( [M].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [M].[Statuz] = @Statuz2 )) AND ( [P].[Statuz] = @Statuz3 )) AND ((((( [P].[Usage] = @Usage4 ) AND ( [P].[UseUnitId] = @UseUnitId5 )) OR (( [P].[Usage] = @Usage6 ) AND ( [P].[UseUnitId] = @UseUnitId7 ))) OR (( [P].[Usage] = @Usage8 ) AND ( [P].[UseUnitId] = @UseUnitId9 ))) OR (( [P].[Usage] = @Usage10 ) AND ( [P].[UseUnitId] = @UseUnitId11 ))))  AND ( [M].[IsDeleted] = @IsDeleted12 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@Usage4 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId5 [Value]:*************** [Type]:Int64    
[Name]:@Usage6 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId7 [Value]:*************** [Type]:Int64    
[Name]:@Usage8 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId9 [Value]:592122710352005 [Type]:Int64    
[Name]:@Usage10 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId11 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted12 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:35:57.123
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PRS].[ModuleId] AS [ModuleId] , [M].[Name] AS [ModuleName] , [PRS].[ProcessId] AS [ProcessId] , [PRS].[ProcessNodeId] AS [ProcessNodeId] , [P].[ProcessName] AS [ProcessName] , [PN].[NodeShowName] AS [ProcessNodeName] , [PN].[RoleName] AS [RoleName] , @constant14 AS [AuditUserName] , ISNULL([PRS].[Sort],@MethodConst15) AS [Sort] , [P].[IsOpen] AS [IsOpen] , ISNULL([LEOS].[IsLook],@MethodConst16) AS [IsLook]  FROM [wf_ProcessReturnSet] [PRS] Inner JOIN [wf_Module] [M] ON ( [PRS].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted13 )  Inner JOIN [wf_Process] [P] ON ( [PRS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted13 )  Left JOIN [wf_ProcessNode] [PN] ON ( [PRS].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted13 )  Left JOIN [wf_LookEachOtherSet] [LEOS] ON ((( [LEOS].[ProcessId] = [P].[Id] ) AND ( [LEOS].[ProcessNodeId] = [PN].[Id] )) AND ( [LEOS].[UnitId] = @UnitId0 )) AND ( [LEOS].[IsDeleted] = @IsDeleted13 )   WHERE ((((( [PRS].[IsDeleted] = @IsDeleted1 ) AND ( [PN].[ProcessLevel] = @ProcessLevel2 )) AND ( [M].[Statuz] = @Statuz3 )) AND ( [P].[Statuz] = @Statuz4 )) AND ((((( [P].[Usage] = @Usage5 ) AND ( [P].[UseUnitId] = @UseUnitId6 )) OR (( [P].[Usage] = @Usage7 ) AND ( [P].[UseUnitId] = @UseUnitId8 ))) OR (( [P].[Usage] = @Usage9 ) AND ( [P].[UseUnitId] = @UseUnitId10 ))) OR (( [P].[Usage] = @Usage11 ) AND ( [P].[UseUnitId] = @UseUnitId12 ))))  AND ( [PRS].[IsDeleted] = @IsDeleted13 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@ProcessLevel2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@Usage5 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId6 [Value]:*************** [Type]:Int64    
[Name]:@Usage7 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId8 [Value]:*************** [Type]:Int64    
[Name]:@Usage9 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId10 [Value]:592122710352005 [Type]:Int64    
[Name]:@Usage11 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId12 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted13 [Value]:False [Type]:Boolean    
[Name]:@constant14 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
[Name]:@MethodConst16 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.605
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.606
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.606
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.621
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.622
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.622
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.628
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.629
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:12.630
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:14.953
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Id],[Pid],[Name],[Path],[FullName],[Sort] FROM [V_Area]  WHERE (( 1 = 1 ) AND( [Pid] = @Pid1 ))) CountTable  
[Pars]:
[Name]:@Pid1 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:16.004
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE ( [TypeCode] = @TypeCode0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:19.707
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Id],[AreaId],[AreaName],[PId],[PName],[UnitType],[IndustryId],[IndustryName],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[AreaPid],[Sort],[SourceType],[ThirdUnitId],[ThirdUnitName] FROM [V_Unit]  WHERE ((( 1 = 1 ) AND(( [PId] = @PId1 ) AND ( [UnitType] <> @UnitType2 ))) AND ( [Statuz] >= @Statuz3 ))) CountTable  
[Pars]:
[Name]:@PId1 [Value]:*************** [Type]:Int64    
[Name]:@UnitType2 [Value]:4 [Type]:Int32    
[Name]:@Statuz3 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:36:19.719
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Id],[AreaId],[AreaName],[PId],[PName],[UnitType],[IndustryId],[IndustryName],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[AreaPid],[Sort],[SourceType],[ThirdUnitId],[ThirdUnitName],ROW_NUMBER() OVER(ORDER BY Sort ASC,Id ASC) AS RowIndex  FROM [V_Unit]  WHERE ((( 1 = 1 ) AND(( [PId] = @PId1 ) AND ( [UnitType] <> @UnitType2 ))) AND ( [Statuz] >= @Statuz3 ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@PId1 [Value]:*************** [Type]:Int64    
[Name]:@UnitType2 [Value]:4 [Type]:Int32    
[Name]:@Statuz3 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:38:07.268
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:38:07.323
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:38:07.326
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:38:07.337
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PAU].[Id] AS [Id] , [PAU].[BusinessType] AS [BusinessType] , [PAU].[ProcessId] AS [ProcessId] , [PAU].[ProcessNodeId] AS [ProcessNodeId] , [PAU].[AuditUnitId] AS [AuditUnitId] , [PAU].[AuditUserId] AS [AuditUserId] , [UE].[Name] AS [AuditUserName]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [SysUserExtension] [UE] ON ( [PAU].[AuditUserId] = [UE].[Id] ) AND ( [UE].[IsDeleted] = @IsDeleted3 )   WHERE ((( [PAU].[AuditUnitId] = @AuditUnitId0 ) AND ( [PAU].[IsDeleted] = @IsDeleted1 )) AND ( [UE].[IsDeleted] = @IsDeleted2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@AuditUnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:38:07.342
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , [P].[IsOpen] AS [IsOpen]  FROM [wf_Module] [M] Inner JOIN [wf_Process] [P] ON ( [M].[Id] = [P].[ModuleId] ) AND ( [P].[IsDeleted] = @IsDeleted12 )   WHERE ((((( [M].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [M].[Statuz] = @Statuz2 )) AND ( [P].[Statuz] = @Statuz3 )) AND ((((( [P].[Usage] = @Usage4 ) AND ( [P].[UseUnitId] = @UseUnitId5 )) OR (( [P].[Usage] = @Usage6 ) AND ( [P].[UseUnitId] = @UseUnitId7 ))) OR (( [P].[Usage] = @Usage8 ) AND ( [P].[UseUnitId] = @UseUnitId9 ))) OR (( [P].[Usage] = @Usage10 ) AND ( [P].[UseUnitId] = @UseUnitId11 ))))  AND ( [M].[IsDeleted] = @IsDeleted12 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@Usage4 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId5 [Value]:*************** [Type]:Int64    
[Name]:@Usage6 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId7 [Value]:*************** [Type]:Int64    
[Name]:@Usage8 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId9 [Value]:592122710352005 [Type]:Int64    
[Name]:@Usage10 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId11 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted12 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-29 10:38:07.348
LogLevel：Information
Message：------------------ 
 User:["浦口教育局"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PRS].[ModuleId] AS [ModuleId] , [M].[Name] AS [ModuleName] , [PRS].[ProcessId] AS [ProcessId] , [PRS].[ProcessNodeId] AS [ProcessNodeId] , [P].[ProcessName] AS [ProcessName] , [PN].[NodeShowName] AS [ProcessNodeName] , [PN].[RoleName] AS [RoleName] , @constant14 AS [AuditUserName] , ISNULL([PRS].[Sort],@MethodConst15) AS [Sort] , [P].[IsOpen] AS [IsOpen] , ISNULL([LEOS].[IsLook],@MethodConst16) AS [IsLook]  FROM [wf_ProcessReturnSet] [PRS] Inner JOIN [wf_Module] [M] ON ( [PRS].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted13 )  Inner JOIN [wf_Process] [P] ON ( [PRS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted13 )  Left JOIN [wf_ProcessNode] [PN] ON ( [PRS].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted13 )  Left JOIN [wf_LookEachOtherSet] [LEOS] ON ((( [LEOS].[ProcessId] = [P].[Id] ) AND ( [LEOS].[ProcessNodeId] = [PN].[Id] )) AND ( [LEOS].[UnitId] = @UnitId0 )) AND ( [LEOS].[IsDeleted] = @IsDeleted13 )   WHERE ((((( [PRS].[IsDeleted] = @IsDeleted1 ) AND ( [PN].[ProcessLevel] = @ProcessLevel2 )) AND ( [M].[Statuz] = @Statuz3 )) AND ( [P].[Statuz] = @Statuz4 )) AND ((((( [P].[Usage] = @Usage5 ) AND ( [P].[UseUnitId] = @UseUnitId6 )) OR (( [P].[Usage] = @Usage7 ) AND ( [P].[UseUnitId] = @UseUnitId8 ))) OR (( [P].[Usage] = @Usage9 ) AND ( [P].[UseUnitId] = @UseUnitId10 ))) OR (( [P].[Usage] = @Usage11 ) AND ( [P].[UseUnitId] = @UseUnitId12 ))))  AND ( [PRS].[IsDeleted] = @IsDeleted13 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@ProcessLevel2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@Usage5 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId6 [Value]:*************** [Type]:Int64    
[Name]:@Usage7 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId8 [Value]:*************** [Type]:Int64    
[Name]:@Usage9 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId10 [Value]:592122710352005 [Type]:Int64    
[Name]:@Usage11 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId12 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted13 [Value]:False [Type]:Boolean    
[Name]:@constant14 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
[Name]:@MethodConst16 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------