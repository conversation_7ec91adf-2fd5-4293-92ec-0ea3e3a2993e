﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcSchoolMaterialBackLog接口方法
    ///</summary>
    public interface IDcSchoolMaterialBackLogServices : IBaseServices<DcSchoolMaterialBackLog>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<DcSchoolMaterialBackLog> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<DcSchoolMaterialBackLog>> Find(Expression<Func<DcSchoolMaterialBackLog, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">DcSchoolMaterialBackLogParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<DcSchoolMaterialBackLog>> GetPaged(DcSchoolMaterialBackLogParam param);

       //<used>0</used>
       Task<List<DcSchoolMaterialBackLog>> Insert(List<DcSchoolMaterialBackLog> entityCollection);

        #region 查询统计

        /// <summary>
        /// 单位危化品退库记录 (原 V_dc_SchoolMaterialBackLog GetPaged)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcSchoolMaterialBackLog>> GetStatisticsPaged(VDcSchoolMaterialBackLogParam param);

        /// <summary>
        /// 采购分类采集
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcSchoolMaterial>> GetListPaged(VDcSchoolMaterialParam param);
        #endregion
    }
}

