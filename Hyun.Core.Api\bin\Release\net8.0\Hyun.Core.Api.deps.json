{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Hyun.Core.Api/1.0.0": {"dependencies": {"Hyun.Core.Extensions": "1.0.0", "MicroKnights.Log4NetAdoNetAppender": "2.2.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "SkyAPM.Agent.AspNetCore": "2.1.0", "System.Text.Encoding.CodePages": "8.0.0", "log4mongo-netcore": "3.2.0"}, "runtime": {"Hyun.Core.Api.dll": {}}}, "Antlr3.Runtime/3.5.1": {"dependencies": {"NETStandard.Library": "1.6.0"}, "runtime": {"lib/netstandard1.1/Antlr3.Runtime.dll": {"assemblyVersion": "3.5.0.2", "fileVersion": "3.5.1.0"}}}, "AspectCore.Extensions.Reflection/1.2.0": {"dependencies": {"System.Reflection.Emit.Lightweight": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/AspectCore.Extensions.Reflection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}, "AspNetCoreRateLimit/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/AspNetCoreRateLimit.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}}, "Autofac/6.5.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Autofac.dll": {"assemblyVersion": "6.5.0.0", "fileVersion": "6.5.0.0"}}}, "Autofac.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Autofac": "6.5.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net6.0/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autofac.Extras.DynamicProxy/7.1.0": {"dependencies": {"Autofac": "6.5.0", "Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}}, "AutoMapper/13.0.1": {"dependencies": {"Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net6.0/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.0"}}}, "AWSSDK.Core/**********": {"runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}}, "AWSSDK.SecurityToken/**********": {"dependencies": {"AWSSDK.Core": "**********"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.5", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "********", "fileVersion": "1.1100.424.31005"}}}, "BouncyCastle.Cryptography/2.3.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.1.17862"}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Com.Ctrip.Framework.Apollo/2.10.0": {"dependencies": {"System.Net.Http.Json": "6.0.0"}, "runtime": {"lib/netstandard2.1/Com.Ctrip.Framework.Apollo.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Com.Ctrip.Framework.Apollo.Configuration/2.10.2": {"dependencies": {"Com.Ctrip.Framework.Apollo": "2.10.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}, "runtime": {"lib/netstandard2.1/Com.Ctrip.Framework.Apollo.Configuration.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Confluent.Kafka/2.5.2": {"dependencies": {"librdkafka.redist": "2.5.0"}, "runtime": {"lib/net6.0/Confluent.Kafka.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Consul/********": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Consul.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DynamicExpresso.Core/2.3.3": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.0/DynamicExpresso.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EasyCaching.Core/1.9.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/EasyCaching.Core.dll": {"assemblyVersion": "1.9.2.0", "fileVersion": "1.9.2.0"}}}, "EasyCaching.InMemory/1.9.2": {"dependencies": {"EasyCaching.Core": "1.9.2"}, "runtime": {"lib/net6.0/EasyCaching.InMemory.dll": {"assemblyVersion": "1.9.2.0", "fileVersion": "1.9.2.0"}}}, "Enums.NET/4.0.1": {"runtime": {"lib/netcoreapp3.0/Enums.NET.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.1.0"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"runtime": {"lib/net8.0/ExtendedNumerics.BigDecimal.dll": {"assemblyVersion": "2025.1001.2.129", "fileVersion": "2025.1001.2.129"}}}, "FluentValidation/12.0.0": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "FluentValidation.DependencyInjectionExtensions/12.0.0": {"dependencies": {"FluentValidation": "12.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Google.Protobuf/3.21.5": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.21.5.0", "fileVersion": "3.21.5.0"}}}, "Grpc/2.37.0": {"dependencies": {"Grpc.Core": "2.46.3"}}, "Grpc.Core/2.46.3": {"dependencies": {"Grpc.Core.Api": "2.47.0", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/Grpc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.46.3.0"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libgrpc_csharp_ext.arm64.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libgrpc_csharp_ext.x64.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgrpc_csharp_ext.x64.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/grpc_csharp_ext.x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/grpc_csharp_ext.x86.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Grpc.Core.Api/2.47.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Client/2.47.0": {"dependencies": {"Grpc.Net.Common": "2.47.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}, "runtime": {"lib/net6.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Common/2.47.0": {"dependencies": {"Grpc.Core.Api": "2.47.0"}, "runtime": {"lib/net6.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "InitQ/********": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Hosting": "2.1.0", "Newtonsoft.Json": "13.0.3", "StackExchange.Redis": "2.8.0"}, "runtime": {"lib/net6.0/InitQ.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "IPAddressRange/6.2.0": {"runtime": {"lib/net8.0/IPAddressRange.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "librdkafka.redist/2.5.0": {"runtimeTargets": {"runtimes/linux-arm64/native/librdkafka.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/alpine-librdkafka.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/centos8-librdkafka.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/librdkafka.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/librdkafka.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/librdkafka.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libcrypto-3-x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/libcurl.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "8.4.0.0"}, "runtimes/win-x64/native/librdkafka.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/librdkafkacpp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libssl-3-x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/msvcp140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.29.30040.0"}, "runtimes/win-x64/native/vcruntime140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.29.30040.0"}, "runtimes/win-x64/native/zlib1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.3.0.0"}, "runtimes/win-x64/native/zstd.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.5.5.0"}, "runtimes/win-x86/native/libcrypto-3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/libcurl.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "8.4.0.0"}, "runtimes/win-x86/native/librdkafka.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/librdkafkacpp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libssl-3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/msvcp140.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.29.30040.0"}, "runtimes/win-x86/native/vcruntime140.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.29.30040.0"}, "runtimes/win-x86/native/zlib1.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.3.0.0"}, "runtimes/win-x86/native/zstd.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.5.5.0"}}}, "log4mongo-netcore/3.2.0": {"dependencies": {"MongoDB.Bson": "2.28.0", "MongoDB.Driver": "2.28.0", "MongoDB.Driver.Core": "2.28.0", "System.Configuration.ConfigurationManager": "8.0.0", "log4net": "2.0.17"}, "runtime": {"lib/netcoreapp2.2/Log4Mongo.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "log4net/2.0.17": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "2.0.17.0", "fileVersion": "2.0.17.0"}}}, "Magicodes.IE.Core/*******": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyModel": "8.0.1", "SixLabors.ImageSharp": "3.1.5", "System.ComponentModel.Annotations": "4.7.0"}, "runtime": {"lib/net8.0/Magicodes.IE.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "resources": {"lib/net8.0/zh-Hans/Magicodes.IE.Core.resources.dll": {"locale": "zh-Hans"}}}, "Magicodes.IE.EPPlus/*******": {"dependencies": {"Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SixLabors.ImageSharp": "3.1.5", "SkiaSharp": "2.88.6", "System.Security.Cryptography.Pkcs": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/Magicodes.IE.EPPlus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Magicodes.IE.Excel/*******": {"dependencies": {"DynamicExpresso.Core": "2.3.3", "Magicodes.IE.Core": "*******", "Magicodes.IE.EPPlus": "*******", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.6", "System.Linq.Dynamic.Core": "1.4.4"}, "runtime": {"lib/net8.0/Magicodes.IE.Excel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mapster/7.4.0": {"dependencies": {"Mapster.Core": "1.2.1"}, "runtime": {"lib/net7.0/Mapster.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.0"}}}, "Mapster.Core/1.2.1": {"runtime": {"lib/net7.0/Mapster.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MathNet.Numerics.Signed/5.0.0": {"runtime": {"lib/net6.0/MathNet.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroKnights.Log4NetAdoNetAppender/2.2.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Extensions.Configuration.Json": "8.0.0", "log4net": "2.0.17"}, "runtime": {"lib/netstandard2.1/MicroKnights.Log4NetAdoNetAppender.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.4": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.424.17014"}}}, "Microsoft.AspNetCore.Authorization/2.3.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.AspNetCore.Authorization.Policy/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.3.0", "Microsoft.AspNetCore.Authorization": "2.3.0"}}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.13": {"dependencies": {"Microsoft.Extensions.Features": "8.0.13", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6610"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.AspNetCore.Http/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.WebUtilities": "2.3.0", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Net.Http.Headers": "8.0.13"}}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Connections/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Authorization.Policy": "2.3.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.AspNetCore.Http.Connections.Common": "1.2.0", "Microsoft.AspNetCore.Routing": "2.3.0", "Microsoft.AspNetCore.WebSockets": "2.3.0", "Newtonsoft.Json": "13.0.3", "System.Net.WebSockets.WebSocketProtocol": "5.1.0"}}, "Microsoft.AspNetCore.Http.Connections.Common/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.13", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.6.0", "System.IO.Pipelines": "8.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Net.Http.Headers": "8.0.13", "System.Buffers": "4.6.0"}}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.AspNetCore.JsonPatch/8.0.19": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1925.37204"}}}, "Microsoft.AspNetCore.MiddlewareAnalysis/8.0.15": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.MiddlewareAnalysis.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.19": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.19", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1925.37204"}}}, "Microsoft.AspNetCore.Routing/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.3.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0"}}, "Microsoft.AspNetCore.SignalR/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Connections": "1.2.0", "Microsoft.AspNetCore.SignalR.Core": "1.2.0", "Microsoft.AspNetCore.WebSockets": "2.3.0", "System.IO.Pipelines": "8.0.0"}}, "Microsoft.AspNetCore.SignalR.Common/8.0.13": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.13", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6610"}}}, "Microsoft.AspNetCore.SignalR.Core/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "2.3.0", "Microsoft.AspNetCore.SignalR.Common": "8.0.13", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "System.IO.Pipelines": "8.0.0", "System.Reflection.Emit": "4.7.0", "System.Threading.Channels": "8.0.0"}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.2.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.13", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "8.0.0"}}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/8.0.13": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.13", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1325.6610"}}}, "Microsoft.AspNetCore.WebSockets/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.Extensions.Options": "8.0.2", "System.Net.WebSockets.WebSocketProtocol": "5.1.0"}}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"dependencies": {"Microsoft.Net.Http.Headers": "8.0.13", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Bcl.Memory/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Bcl.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.2.0.0"}}}, "Microsoft.Data.Sqlite/9.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Data.Sqlite.Core/9.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore/6.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.1", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "7.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.121.56712"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.1": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.121.56712"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.1": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.1", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.121.56712"}}}, "Microsoft.Extensions.AmbientMetadata.Application/8.10.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Caching.StackExchangeRedis/8.0.13": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "StackExchange.Redis": "2.8.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1325.6610"}}}, "Microsoft.Extensions.Compliance.Abstractions/8.10.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.ObjectPool": "8.0.11"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.4"}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/8.10.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.DependencyModel/8.0.1": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.DiagnosticAdapter/3.1.32": {"dependencies": {"System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netcoreapp2.0/Microsoft.Extensions.DiagnosticAdapter.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Diagnostics/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/8.10.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.Features/8.0.13": {"runtime": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6610"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting/2.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Http/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Http.Diagnostics/8.10.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "8.10.0", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry": "8.10.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.Http.Polly/8.0.13": {"dependencies": {"Microsoft.Extensions.Http": "8.0.1", "Polly": "8.4.1", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1325.6610"}}}, "Microsoft.Extensions.Http.Resilience/8.10.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Http.Diagnostics": "8.10.0", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Resilience": "8.10.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6609"}}}, "Microsoft.Extensions.Logging.Configuration/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.ObjectPool/8.0.11": {"runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Extensions.Resilience/8.10.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "8.10.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "8.10.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.Telemetry/8.10.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "8.10.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "8.10.0", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Telemetry.Abstractions": "8.10.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Extensions.Telemetry.Abstractions/8.10.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "8.10.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.4.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/8.4.0": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.Logging/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.Protocols/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.4.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/8.4.0": {"dependencies": {"Microsoft.Bcl.Memory": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.IdentityModel.Logging": "8.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Net.Http.Headers/8.0.13": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6610"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.14.0", "fileVersion": "1.6.14.0"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {}, "Microsoft.Win32.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MiniProfiler.AspNetCore/4.3.8": {"dependencies": {"MiniProfiler.Shared": "4.3.8"}, "runtime": {"lib/net6.0/MiniProfiler.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.3.8.8209"}}}, "MiniProfiler.AspNetCore.Mvc/4.3.8": {"dependencies": {"MiniProfiler.AspNetCore": "4.3.8"}, "runtime": {"lib/net6.0/MiniProfiler.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "4.3.8.8209"}}}, "MiniProfiler.Shared/4.3.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Newtonsoft.Json": "13.0.3", "System.ComponentModel.Primitives": "4.3.0", "System.Data.Common": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Diagnostics.StackTrace": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0"}, "runtime": {"lib/netstandard2.0/MiniProfiler.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "4.3.8.8209"}}}, "MongoDB.Bson/2.28.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.28.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "MongoDB.Bson": "2.28.0", "MongoDB.Driver.Core": "2.28.0", "MongoDB.Libmongocrypt": "1.11.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.28.0": {"dependencies": {"AWSSDK.SecurityToken": "**********", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "MongoDB.Bson": "2.28.0", "MongoDB.Libmongocrypt": "1.11.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.6.0", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Libmongocrypt/1.11.0": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.11.0.0", "fileVersion": "1.11.0.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net7.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.0"}}}, "nacos-sdk-csharp/1.3.7": {"dependencies": {"Google.Protobuf": "3.21.5", "Grpc.Core": "2.46.3", "Microsoft.Extensions.DependencyModel": "8.0.1", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Nacos.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "nacos-sdk-csharp-unofficial/0.8.5": {"dependencies": {"Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Newtonsoft.Json": "13.0.3"}}, "nacos-sdk-csharp-unofficial.AspNetCore/0.8.5": {"dependencies": {"EasyCaching.InMemory": "1.9.2", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "nacos-sdk-csharp-unofficial": "0.8.5"}}, "nacos-sdk-csharp-unofficial.Extensions.Configuration/0.8.5": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "Newtonsoft.Json": "13.0.3", "nacos-sdk-csharp-unofficial": "0.8.5"}, "runtime": {"lib/net5.0/Nacos.Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "nacos-sdk-csharp-unofficial.IniParser/0.8.5": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "nacos-sdk-csharp-unofficial": "0.8.5"}, "runtime": {"lib/net5.0/Nacos.IniParser.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "nacos-sdk-csharp.AspNetCore/1.3.7": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "nacos-sdk-csharp": "1.3.7"}, "runtime": {"lib/net6.0/Nacos.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NCalc.NetCore/1.0.1": {"dependencies": {"Antlr3.Runtime": "3.5.1"}, "runtime": {"lib/netstandard2.0/NCalc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NetDevPack.Security.JwtExtensions/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.4", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net8.0/NetDevPack.Security.JwtExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.0.1", "System.AppContext": "4.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.0.1", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.Sockets": "4.1.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.0.1", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Npgsql/5.0.18": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net5.0/Npgsql.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NPOI/2.7.1": {"dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "2.0.4", "SixLabors.ImageSharp": "3.1.5", "System.Configuration.ConfigurationManager": "8.0.0", "System.Security.Cryptography.Pkcs": "8.0.0", "System.Security.Cryptography.Xml": "6.0.1"}, "runtime": {"lib/net6.0/NPOI.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Ocelot/24.0.1": {"dependencies": {"FluentValidation": "12.0.0", "IPAddressRange": "6.2.0", "Microsoft.AspNetCore.MiddlewareAnalysis": "8.0.15", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.19", "Microsoft.Extensions.DiagnosticAdapter": "3.1.32"}, "runtime": {"lib/net8.0/Ocelot.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OpenIddict/6.2.0": {"dependencies": {"OpenIddict.Abstractions": "6.2.0", "OpenIddict.Client": "6.2.0", "OpenIddict.Client.SystemIntegration": "6.2.0", "OpenIddict.Client.SystemNetHttp": "6.2.0", "OpenIddict.Client.WebIntegration": "6.2.0", "OpenIddict.Core": "6.2.0", "OpenIddict.Server": "6.2.0", "OpenIddict.Validation": "6.2.0", "OpenIddict.Validation.ServerIntegration": "6.2.0", "OpenIddict.Validation.SystemNetHttp": "6.2.0"}}, "OpenIddict.Abstractions/6.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net8.0/OpenIddict.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.AspNetCore/6.2.0": {"dependencies": {"OpenIddict": "6.2.0", "OpenIddict.Client.AspNetCore": "6.2.0", "OpenIddict.Client.DataProtection": "6.2.0", "OpenIddict.Server.AspNetCore": "6.2.0", "OpenIddict.Server.DataProtection": "6.2.0", "OpenIddict.Validation.AspNetCore": "6.2.0", "OpenIddict.Validation.DataProtection": "6.2.0"}}, "OpenIddict.Client/6.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Client.AspNetCore/6.2.0": {"dependencies": {"OpenIddict.Client": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Client.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Client.DataProtection/6.2.0": {"dependencies": {"OpenIddict.Client": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Client.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Client.SystemIntegration/6.2.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Net.Http.Headers": "8.0.13", "OpenIddict.Client": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Client.SystemIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Client.SystemNetHttp/6.2.0": {"dependencies": {"Microsoft.Extensions.Http.Polly": "8.0.13", "Microsoft.Extensions.Http.Resilience": "8.10.0", "OpenIddict.Client": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Client.SystemNetHttp.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Client.WebIntegration/6.2.0": {"dependencies": {"OpenIddict.Client": "6.2.0", "OpenIddict.Client.SystemNetHttp": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Client.WebIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Core/6.2.0": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "OpenIddict.Abstractions": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Quartz/6.2.0": {"dependencies": {"OpenIddict.Core": "6.2.0", "Quartz.Extensions.DependencyInjection": "3.14.0"}, "runtime": {"lib/net8.0/OpenIddict.Quartz.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Server/6.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "OpenIddict.Abstractions": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Server.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Server.AspNetCore/6.2.0": {"dependencies": {"OpenIddict.Server": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Server.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Server.DataProtection/6.2.0": {"dependencies": {"OpenIddict.Server": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Server.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Validation/6.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Validation.AspNetCore/6.2.0": {"dependencies": {"OpenIddict.Validation": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Validation.DataProtection/6.2.0": {"dependencies": {"OpenIddict.Validation": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Validation.ServerIntegration/6.2.0": {"dependencies": {"OpenIddict.Server": "6.2.0", "OpenIddict.Validation": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.ServerIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "OpenIddict.Validation.SystemNetHttp/6.2.0": {"dependencies": {"Microsoft.Extensions.Http.Polly": "8.0.13", "Microsoft.Extensions.Http.Resilience": "8.10.0", "OpenIddict.Validation": "6.2.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.SystemNetHttp.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.25.17863"}}}, "Oracle.ManagedDataAccess.Core/3.21.100": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.1"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PinYinConverterCore/1.0.2": {"runtime": {"lib/netstandard2.0/PinYinConverterCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Polly/8.4.1": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net6.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.1.3582"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "8.4.1"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "protobuf-net/3.2.30": {"dependencies": {"protobuf-net.Core": "3.2.30"}, "runtime": {"lib/net6.0/protobuf-net.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.30.709"}}}, "protobuf-net.Core/3.2.30": {"dependencies": {"System.Collections.Immutable": "7.0.0"}, "runtime": {"lib/net6.0/protobuf-net.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.30.709"}}}, "Quartz/3.14.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3"}, "runtime": {"lib/net8.0/Quartz.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Quartz.Extensions.DependencyInjection/3.14.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Quartz": "3.14.0"}, "runtime": {"lib/net8.0/Quartz.Extensions.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Quartz.Extensions.Hosting/3.14.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Quartz.Extensions.DependencyInjection": "3.14.0"}, "runtime": {"lib/net8.0/Quartz.Extensions.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "RabbitMQ.Client/6.8.1": {"dependencies": {"System.Memory": "4.5.5", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "RestSharp/111.4.1": {"runtime": {"lib/net8.0/RestSharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "Scrutor/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.DependencyModel": "8.0.1"}, "runtime": {"lib/netcoreapp3.1/Scrutor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/4.0.1": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/8.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Serilog": "4.0.1", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.2", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Expressions/5.0.0": {"dependencies": {"Serilog": "4.0.1"}, "runtime": {"lib/net8.0/Serilog.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Serilog": "4.0.1", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Serilog": "4.0.1"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "4.0.1"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/8.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyModel": "8.0.1", "Serilog": "4.0.1"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Async/2.0.0": {"dependencies": {"Serilog": "4.0.1"}, "runtime": {"lib/net8.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.0.1"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "4.0.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.0.1"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.PeriodicBatching/5.0.0": {"dependencies": {"Serilog": "4.0.1"}, "runtime": {"lib/net8.0/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.RollingFile/3.3.0": {"dependencies": {"Serilog.Sinks.File": "6.0.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.3/Serilog.Sinks.RollingFile.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Seq/8.0.0": {"dependencies": {"Serilog": "4.0.1", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/2.0.4": {"runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/3.1.5": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp.Drawing/2.1.4": {"dependencies": {"SixLabors.Fonts": "2.0.4", "SixLabors.ImageSharp": "3.1.5"}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/2.88.6": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.6": {"dependencies": {"SkiaSharp": "2.88.6"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkyAPM.Abstractions/2.1.0": {"runtime": {"lib/netstandard2.0/SkyAPM.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Agent.AspNetCore/2.1.0": {"dependencies": {"SkyAPM.Agent.Hosting": "2.1.0", "SkyAPM.Diagnostics.AspNetCore": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Agent.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Agent.Hosting/2.1.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "2.1.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql": "2.1.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql": "2.1.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite": "2.1.0", "SkyAPM.Diagnostics.HttpClient": "2.1.0", "SkyAPM.Diagnostics.SqlClient": "2.1.0", "SkyAPM.Transport.Grpc": "2.1.0", "SkyAPM.Utilities.Configuration": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0", "SkyAPM.Utilities.Logging": "2.1.0", "SkyApm.Diagnostics.Grpc": "2.1.0", "SkyApm.Diagnostics.Grpc.Net.Client": "2.1.0", "SkyApm.Diagnostics.MSLogging": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Agent.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Core/2.1.0": {"dependencies": {"AspectCore.Extensions.Reflection": "1.2.0", "SkyAPM.Abstractions": "2.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory": "4.5.5", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.AspNetCore/2.1.0": {"dependencies": {"SkyAPM.Abstractions": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Diagnostics.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore/2.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.1", "Microsoft.EntityFrameworkCore.Relational": "6.0.1", "SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Diagnostics.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql/2.1.0": {"dependencies": {"SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql/2.1.0": {"dependencies": {"SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite/2.1.0": {"dependencies": {"SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyApm.Diagnostics.Grpc/2.1.0": {"dependencies": {"Grpc": "2.37.0", "SkyAPM.Abstractions": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyApm.Diagnostics.Grpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyApm.Diagnostics.Grpc.Net.Client/2.1.0": {"dependencies": {"Grpc.Core.Api": "2.47.0", "SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyApm.Diagnostics.Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.HttpClient/2.1.0": {"dependencies": {"SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Diagnostics.HttpClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyApm.Diagnostics.MSLogging/2.1.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "SkyAPM.Abstractions": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyApm.Diagnostics.MSLogging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.SqlClient/2.1.0": {"dependencies": {"SkyAPM.Abstractions": "2.1.0", "SkyAPM.Core": "2.1.0", "SkyAPM.Utilities.DependencyInjection": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Diagnostics.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Transport.Grpc/2.1.0": {"dependencies": {"SkyAPM.Abstractions": "2.1.0", "SkyAPM.Transport.Grpc.Protocol": "2.1.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Transport.Grpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Transport.Grpc.Protocol/2.1.0": {"dependencies": {"Google.Protobuf": "3.21.5", "Grpc.Net.Client": "2.47.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Transport.Grpc.Protocol.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.Configuration/2.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "SkyAPM.Abstractions": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Utilities.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.DependencyInjection/2.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "SkyAPM.Core": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Utilities.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.Logging/2.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Serilog.Extensions.Logging": "8.0.0", "Serilog.Sinks.RollingFile": "3.3.0", "SkyAPM.Abstractions": "2.1.0"}, "runtime": {"lib/net6.0/SkyAPM.Utilities.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Data.Sqlite": "9.0.0", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.3", "Npgsql": "5.0.18", "Oracle.ManagedDataAccess.Core": "3.21.100", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.8.0", "SqlSugarCore.Kdbndp": "9.3.7.428", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "SqlSugarCore.Dm/8.8.0": {"runtime": {"lib/netstandard2.1/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.33449", "fileVersion": "8.3.1.33449"}}}, "SqlSugarCore.Kdbndp/9.3.7.428": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "9.3.7.428", "fileVersion": "9.3.7.428"}}}, "StackExchange.Redis/2.8.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.0.27420"}}}, "Swashbuckle.AspNetCore/6.7.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.7.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.7.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.7.0"}}, "Swashbuckle.AspNetCore.Filters/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.OpenApi": "1.6.14", "Scrutor": "3.3.0", "Swashbuckle.AspNetCore.Filters.Abstractions": "8.0.2", "Swashbuckle.AspNetCore.SwaggerGen": "6.7.0"}, "runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Filters.Abstractions/8.0.2": {"runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Newtonsoft/6.7.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.19", "Swashbuckle.AspNetCore.SwaggerGen": "6.7.0"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Newtonsoft.dll": {"assemblyVersion": "*******", "fileVersion": "6.7.0.593"}}}, "Swashbuckle.AspNetCore.Swagger/6.7.0": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.7.0.593"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.7.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.7.0"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.7.0.593"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.7.0": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.7.0.593"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.6.0": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/7.0.0": {}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.ComponentModel.Annotations/4.7.0": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Configuration.ConfigurationManager/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Console/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.4.1", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tools/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}}, "System.DirectoryServices.Protocols/6.0.1": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Formats.Asn1/8.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.4.0", "Microsoft.IdentityModel.Tokens": "8.4.0"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Buffers": "4.6.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.0.1": {"dependencies": {"System.Buffers": "4.6.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Pipelines/8.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.4.4": {"runtime": {"lib/net8.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Net.Http.Json/6.0.0": {"dependencies": {"System.Text.Json": "8.0.4"}}, "System.Net.Primitives/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.0.11", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Net.WebSockets.WebSocketProtocol/5.1.0": {"runtime": {"lib/net6.0/System.Net.WebSockets.WebSocketProtocol.dll": {"assemblyVersion": "5.1.0.0", "fileVersion": "5.100.24.56208"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/1.4.1": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Immutable": "7.0.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.0.1": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Cng/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.OpenSsl/4.0.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Pkcs/8.0.0": {"dependencies": {"System.Formats.Asn1": "8.0.0"}}, "System.Security.Cryptography.Primitives/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.X509Certificates/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "8.0.0"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.4": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/8.0.0": {}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Timer/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.0.11": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.0.11": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.0.11"}}, "Yitter.IdGenerator/1.0.14": {"runtime": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Hyun.Core.Common/1.0.0": {"dependencies": {"Hyun.Core.Model": "1.0.0", "Hyun.Core.Serilog.Es": "1.0.0", "InitQ": "********", "Magicodes.IE.Excel": "*******", "Mapster": "7.4.0", "Mapster.Core": "1.2.1", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Configuration.Json": "8.0.0", "MiniProfiler.Shared": "4.3.8", "Ocelot.Provider.Nacos": "1.2.1", "PinYinConverterCore": "1.0.2", "RestSharp": "111.4.1", "Serilog": "4.0.1", "Serilog.AspNetCore": "8.0.2", "Serilog.Expressions": "5.0.0", "Serilog.Sinks.Async": "2.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "2.0.4", "SixLabors.ImageSharp": "3.1.5", "SixLabors.ImageSharp.Drawing": "2.1.4", "StackExchange.Redis": "2.8.0", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Linq.Dynamic.Core": "1.4.4", "Yitter.IdGenerator": "1.0.14", "log4net": "2.0.17"}, "runtime": {"Hyun.Core.Common.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.EventBus/1.0.0": {"dependencies": {"Autofac.Extensions.DependencyInjection": "8.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Confluent.Kafka": "2.5.2", "Hyun.Core.Common": "1.0.0", "Microsoft.Extensions.Logging": "8.0.1", "Newtonsoft.Json": "13.0.3", "Polly": "8.4.1", "RabbitMQ.Client": "6.8.1", "protobuf-net": "3.2.30"}, "runtime": {"Hyun.Core.EventBus.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.Extensions/1.0.0": {"dependencies": {"AspNetCoreRateLimit": "5.0.0", "Autofac.Extensions.DependencyInjection": "8.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Com.Ctrip.Framework.Apollo": "2.10.0", "Com.Ctrip.Framework.Apollo.Configuration": "2.10.2", "Consul": "********", "Hyun.Core.EventBus": "1.0.0", "Hyun.Core.Serilog": "1.0.0", "Hyun.Core.Services": "1.0.0", "Hyun.Core.Tasks": "1.0.0", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.19", "Microsoft.AspNetCore.SignalR": "1.2.0", "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": "8.0.13", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.13", "Microsoft.Extensions.Http.Polly": "8.0.13", "MiniProfiler.AspNetCore.Mvc": "4.3.8", "NetDevPack.Security.JwtExtensions": "8.0.0", "Newtonsoft.Json": "13.0.3", "OpenIddict.AspNetCore": "6.2.0", "OpenIddict.Quartz": "6.2.0", "OpenIddict.SqlSugar": "1.0.0", "Quartz.Extensions.Hosting": "3.14.0", "Serilog.Settings.Configuration": "8.0.2", "Serilog.Sinks.Seq": "8.0.0", "Swashbuckle.AspNetCore": "6.7.0", "Swashbuckle.AspNetCore.Filters": "8.0.2", "Swashbuckle.AspNetCore.Newtonsoft": "6.7.0", "Yitter.IdGenerator": "1.0.14", "nacos-sdk-csharp-unofficial": "0.8.5", "nacos-sdk-csharp-unofficial.AspNetCore": "0.8.5", "nacos-sdk-csharp-unofficial.Extensions.Configuration": "0.8.5", "nacos-sdk-csharp-unofficial.IniParser": "0.8.5", "nacos-sdk-csharp.AspNetCore": "1.3.7"}, "runtime": {"Hyun.Core.Extensions.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.IServices/1.0.0": {"dependencies": {"Hyun.Core.Common": "1.0.0", "Hyun.Core.Model": "1.0.0"}, "runtime": {"Hyun.Core.IServices.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.Model/1.0.0": {"dependencies": {"AutoMapper": "13.0.1", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "NPOI": "2.7.1", "OpenIddict.SqlSugar.Models": "1.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"Hyun.Core.Model.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.Repository/1.0.0": {"dependencies": {"Hyun.Core.Common": "1.0.0", "Hyun.Core.Model": "1.0.0", "MiniProfiler.AspNetCore.Mvc": "4.3.8", "MongoDB.Bson": "2.28.0", "MongoDB.Driver": "2.28.0", "MongoDB.Driver.Core": "2.28.0"}, "runtime": {"Hyun.Core.Repository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.Serilog/1.0.0": {"dependencies": {"Hyun.Core.Common": "1.0.0", "Mapster": "7.4.0", "Serilog.Sinks.PeriodicBatching": "5.0.0"}, "runtime": {"Hyun.Core.Serilog.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.Serilog.Es/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Serilog": "4.0.1"}, "runtime": {"Hyun.Core.Serilog.Es.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.Services/1.0.0": {"dependencies": {"Hyun.Core.IServices": "1.0.0", "Hyun.Core.Repository": "1.0.0", "NCalc.NetCore": "1.0.1"}, "runtime": {"Hyun.Core.Services.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Hyun.Core.Tasks/1.0.0": {"dependencies": {"Hyun.Core.Common": "1.0.0", "Hyun.Core.IServices": "1.0.0", "Hyun.Core.Repository": "1.0.0", "Quartz": "3.14.0"}, "runtime": {"Hyun.Core.Tasks.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Ocelot.Provider.Nacos/1.2.1": {"dependencies": {"EasyCaching.InMemory": "1.9.2", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Ocelot": "24.0.1", "nacos-sdk-csharp": "1.3.7"}, "runtime": {"Ocelot.Provider.Nacos.dll": {"assemblyVersion": "1.2.1", "fileVersion": "*******"}}}, "OpenIddict.SqlSugar/1.0.0": {"dependencies": {"OpenIddict.Core": "6.2.0", "OpenIddict.SqlSugar.Models": "1.0.0"}, "runtime": {"OpenIddict.SqlSugar.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "OpenIddict.SqlSugar.Models/1.0.0": {"dependencies": {"SqlSugarCore": "*********"}, "runtime": {"OpenIddict.SqlSugar.Models.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"Hyun.Core.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Antlr3.Runtime/3.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZwCEpLCLZXkjzf1lYEixp9hgVlYYk33ACHn6xjFyVhGRi2BpoYQez0qtatbetdylASWYTG+cGz0CGR4z66JM4w==", "path": "antlr3.runtime/3.5.1", "hashPath": "antlr3.runtime.3.5.1.nupkg.sha512"}, "AspectCore.Extensions.Reflection/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+9Ebk4YNMIueiJGhFbrfF8Zr9XY462lRaw6IgYgtfxSopoZSMRlksPnCIxWGoXBijuMeUjRbnULWaTwuZXZLgw==", "path": "aspectcore.extensions.reflection/1.2.0", "hashPath": "aspectcore.extensions.reflection.1.2.0.nupkg.sha512"}, "AspNetCoreRateLimit/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6fq9+o1maGADUmpK/PvcF0DtXW2+7bSkIL7MDIo/agbIHKN8XkMQF4oze60DO731WaQmHmK260hB30FwPzCmEg==", "path": "aspnetcoreratelimit/5.0.0", "hashPath": "aspnetcoreratelimit.5.0.0.nupkg.sha512"}, "Autofac/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-sm6HWWBDEoTPGXeyOchMygJ/7L89VxmVH72BFxakG88J34AVjA8RMi5jbv3PdpUtXI/d9ZLKOaxCmOSIo2K4aA==", "path": "autofac/6.5.0", "hashPath": "autofac.6.5.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nGrXNpQX2FiZpIBydK9cxZnnoqP/cUd3k/53uRERYEqLtWzKtE15R6L+j5q5ax5Rv/+3wAIkOaPePkahfqrwjg==", "path": "autofac.extensions.dependencyinjection/8.0.0", "hashPath": "autofac.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6Szv7A1LK/cTdeoyqS45zb/BC5vep8+86f6C1oh2UhZaYtiijlNfLWamp3lxe0uUQ33kFe1dDCjsvfwJWzLg==", "path": "autofac.extras.dynamicproxy/7.1.0", "hashPath": "autofac.extras.dynamicproxy.7.1.0.nupkg.sha512"}, "AutoMapper/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/Fx1SbJ16qS7dU4i604Sle+U9VLX+WSNVJggk6MupKVkYvvBm4XqYaeFuf67diHefHKHs50uQIS2YEDFhPCakQ==", "path": "automapper/13.0.1", "hashPath": "automapper.13.0.1.nupkg.sha512"}, "AWSSDK.Core/**********": {"type": "package", "serviceable": true, "sha512": "sha512-gnEgxBlk4PFEfdPE8Lkf4+D16MZFYSaW7/o6Wwe5e035QWUkTJX0Dn4LfTCdV5QSEL/fOFxu+yCAm55eIIBgog==", "path": "awssdk.core/**********", "hashPath": "awssdk.core.**********.nupkg.sha512"}, "AWSSDK.SecurityToken/**********": {"type": "package", "serviceable": true, "sha512": "sha512-dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "path": "awssdk.securitytoken/**********", "hashPath": "awssdk.securitytoken.**********.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "BouncyCastle.Cryptography/2.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-buwoISwecYke3CmgG1AQSg+sNZjJeIb93vTAtJiHZX35hP/teYMxsfg0NDXGUKjGx6BKBTNKc77O2M3vKvlXZQ==", "path": "bouncycastle.cryptography/2.3.1", "hashPath": "bouncycastle.cryptography.2.3.1.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "Com.Ctrip.Framework.Apollo/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-URMuO5zpe06xOd4HVq10pmWjxbxab7qevu6ZGLcRPh1xWjtHYw2xZf+ZO3gMkruNVdocNJz/M4SbV4N4B8Hoxg==", "path": "com.ctrip.framework.apollo/2.10.0", "hashPath": "com.ctrip.framework.apollo.2.10.0.nupkg.sha512"}, "Com.Ctrip.Framework.Apollo.Configuration/2.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-vYBi+ilN5RXQzayh/mfOJyHL82w06L00CpqGK1Yuz0P8RibbFJMb211OnLq9aYx2BXIA8ZPEbM4jpgu0B02vhQ==", "path": "com.ctrip.framework.apollo.configuration/2.10.2", "hashPath": "com.ctrip.framework.apollo.configuration.2.10.2.nupkg.sha512"}, "Confluent.Kafka/2.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-imjEYuPvCPPNwZa7PupDgzcEP0kGWjWrE+Uz5TW+kUjFEC64mGG6NnndDYgzt+a8xoaa6rsgYiKdemkUe1cVeg==", "path": "confluent.kafka/2.5.2", "hashPath": "confluent.kafka.2.5.2.nupkg.sha512"}, "Consul/********": {"type": "package", "serviceable": true, "sha512": "sha512-0+vCXo9I0sSJJfFafBN1VGtu/Ya1Sw26fmmGDX3TdYZRq7ScXjZC8tvMIcrxNpxwyWN+e7sxn/aZ9XiZHS4LCw==", "path": "consul/********", "hashPath": "consul.********.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DynamicExpresso.Core/2.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-p6GEP3BphaT9xa59VjpQeozkloXjcDmoL6aPXOInl5S5chWtB82H+GiirV3H1bP39ZeXX2e1UN0w7/pD1wCUlg==", "path": "dynamicexpresso.core/2.3.3", "hashPath": "dynamicexpresso.core.2.3.3.nupkg.sha512"}, "EasyCaching.Core/1.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-26gxpME2bJpOgF5H/ih3Xi48+GvpRHcPs0ifUwL3IUv3ya7G1rURxRz0LzuhiYQ3YtzpR5VgdK1PDX+tIrq69w==", "path": "easycaching.core/1.9.2", "hashPath": "easycaching.core.1.9.2.nupkg.sha512"}, "EasyCaching.InMemory/1.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-So06HEl/ap2TyPLfEpEISca/X+/xvYFBKj8D3Fg6RTEGHIDMjhCnkvuemIYzh7js43Af2xEVh33iCdgYbub3lg==", "path": "easycaching.inmemory/1.9.2", "hashPath": "easycaching.inmemory.1.9.2.nupkg.sha512"}, "Enums.NET/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "path": "enums.net/4.0.1", "hashPath": "enums.net.4.0.1.nupkg.sha512"}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "serviceable": true, "sha512": "sha512-+woGT1lsBtwkntOpx2EZbdbySv0aWPefE0vrfvclxVdbi4oa2bbtphFPWgMiQe+kRCPICbfFJwp6w1DuR7Ge2Q==", "path": "extendednumerics.bigdecimal/2025.1001.2.129", "hashPath": "extendednumerics.bigdecimal.2025.1001.2.129.nupkg.sha512"}, "FluentValidation/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8NVLxtMUXynRHJIX3Hn1ACovaqZIJASufXIIFkD0EUbcd5PmMsL1xUD5h548gCezJ5BzlITaR9CAMrGe29aWpA==", "path": "fluentvalidation/12.0.0", "hashPath": "fluentvalidation.12.0.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-B28fBRL1UjhGsBC8fwV6YB<PERSON><PERSON>+SiU1FxdD7l7p5dGPgRlVI7UnM+Lgzmg+unZtV1Zxzpaw96UY2MYfMaAd8cg==", "path": "fluentvalidation.dependencyinjectionextensions/12.0.0", "hashPath": "fluentvalidation.dependencyinjectionextensions.12.0.0.nupkg.sha512"}, "Google.Protobuf/3.21.5": {"type": "package", "serviceable": true, "sha512": "sha512-d7zwRm0edg2cxqRynMD31F8HUUyicBT0l5M74EIsU6GAaq3dxxaZ/PpSPlso010W9nIXoVVKUYZSsiOspAOO/A==", "path": "google.protobuf/3.21.5", "hashPath": "google.protobuf.3.21.5.nupkg.sha512"}, "Grpc/2.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hl4+qpEfTvKP95diLRZMi3nAaPp91myTuM8Z/lmC9XsbxUroMR4OM0HMKNC9rty2Gz+CHq4mq/E7CBqbcVdnPA==", "path": "grpc/2.37.0", "hashPath": "grpc.2.37.0.nupkg.sha512"}, "Grpc.Core/2.46.3": {"type": "package", "serviceable": true, "sha512": "sha512-lu7KP7BVWdvFXWbn/IFYHBQCpMohJwbLM1aL+nu2C0pY2CdS0idaUNAJNC2nZ60fXfqoxnSiERrIU6rC474oTw==", "path": "grpc.core/2.46.3", "hashPath": "grpc.core.2.46.3.nupkg.sha512"}, "Grpc.Core.Api/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-oZXapxH/2WHAVALghNauo+r/bp6zjgQ6r0v8FizLLQg0/j/FkK2u3WZ7cLOL9Y5H4oLg+wLclO8FSvNTQpNR5Q==", "path": "grpc.core.api/2.47.0", "hashPath": "grpc.core.api.2.47.0.nupkg.sha512"}, "Grpc.Net.Client/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-DLbUC3T8dMmhA2iqpaJo0X4g7fAi3FyVbDd0/jBXrlqc/bcyA1wPBzLx6mWOWoGUb5S89xL2svnsM8SfzzNa2Q==", "path": "grpc.net.client/2.47.0", "hashPath": "grpc.net.client.2.47.0.nupkg.sha512"}, "Grpc.Net.Common/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-Cv76h7noEN2s9cwIdEspOeDjeqcU6Nm34OmjSbRhD/FDBXFmG7rmSfJTPCEB1LYjZWfmf7uUH+nYcHR1I7oQqw==", "path": "grpc.net.common/2.47.0", "hashPath": "grpc.net.common.2.47.0.nupkg.sha512"}, "InitQ/********": {"type": "package", "serviceable": true, "sha512": "sha512-jqAujNOKYxIWiMbqrbcsN8hYeCOGiJuXk/Nf9W3dm+ei4XwkAR2l+YMFrBpeuU2Q8Mzeo81uZVMgxc3dok5FlA==", "path": "initq/********", "hashPath": "initq.********.nupkg.sha512"}, "IPAddressRange/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cqFHYpaouUdk/mRsEke3XKO2/iL+PQB/F5293MjxPa2CtwQZvV57DU+CMmOOLoAVCO6wYo43k8XV7BCB8k2YIQ==", "path": "ipaddressrange/6.2.0", "hashPath": "ipaddressrange.6.2.0.nupkg.sha512"}, "librdkafka.redist/2.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YQBronD1pdiYDMsz3DoVNLO8KVJpQ+svAzHqzE4CHHU9wAr+Uc5/s77gYpcodPQjpw7E2HvUJxrvhUd0l/E0Q==", "path": "librdkafka.redist/2.5.0", "hashPath": "librdkafka.redist.2.5.0.nupkg.sha512"}, "log4mongo-netcore/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qX6HVEQChL8EiTHFYT3R4/+HfVYsczPAThDt9xTedQpNMDSBzx+3ovfZsYLDYWkYGnt0VWgQ5Yhs4FGOna9MDA==", "path": "log4mongo-netcore/3.2.0", "hashPath": "log4mongo-netcore.3.2.0.nupkg.sha512"}, "log4net/2.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-qnnDf/ubJzwm2i1xH7nRMjEDoD+ctse7nZDqb+p7L1PvZc6ykpMoEesWr1/9hFqlsbII2v9e8yyQHJhoDQh7ZA==", "path": "log4net/2.0.17", "hashPath": "log4net.2.0.17.nupkg.sha512"}, "Magicodes.IE.Core/*******": {"type": "package", "serviceable": true, "sha512": "sha512-QokgLic4DJrqj5oDKHR+86+ZW1HfbJ4l/hzWUYIe3tPjPLsjH+dD7KvVBMiuZtvWCxwiUURF7j3rL/8Cpk+FqA==", "path": "magicodes.ie.core/*******", "hashPath": "magicodes.ie.core.*******.nupkg.sha512"}, "Magicodes.IE.EPPlus/*******": {"type": "package", "serviceable": true, "sha512": "sha512-9VSY9Gy+E9VyIn/iDsrAhrJx4Xea0gB7BdyEIHsYUYYLJKWE/QK2HYa6tTi8Torltu828wSwZTwYkl59+XJWew==", "path": "magicodes.ie.epplus/*******", "hashPath": "magicodes.ie.epplus.*******.nupkg.sha512"}, "Magicodes.IE.Excel/*******": {"type": "package", "serviceable": true, "sha512": "sha512-vfbicVe78YHlQ6RxWo62P5IDyRoJIRJkLMmB6U09TjJ6JDr/kVO9AOJRA+hHnrzXI6C75UQ3JOesHRtz4tTRTg==", "path": "magicodes.ie.excel/*******", "hashPath": "magicodes.ie.excel.*******.nupkg.sha512"}, "Mapster/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "path": "mapster/7.4.0", "hashPath": "mapster.7.4.0.nupkg.sha512"}, "Mapster.Core/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "path": "mapster.core/1.2.1", "hashPath": "mapster.core.1.2.1.nupkg.sha512"}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PSrHBVMf41SjbhlnpOMnoir8YgkyEJ6/nwxvjYpH+vJCexNcx2ms6zRww5yLVqLet1xLJgZ39swtKRTLhWdnAw==", "path": "mathnet.numerics.signed/5.0.0", "hashPath": "mathnet.numerics.signed.5.0.0.nupkg.sha512"}, "MicroKnights.Log4NetAdoNetAppender/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FryL/5pLWynP8znM5LTXd4M+DmD+Ftls5D8IlyMwCbH7MFWviLyNvGneJZFjmdXschal5lTUOB3Cs1FkEPs7tQ==", "path": "microknights.log4netadonetappender/2.2.0", "hashPath": "microknights.log4netadonetappender.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ve6uvLwKNRkfnO/QeN9M8eUJ49lCnWv/6/9p6iTEuiI6Rtsz+myaBAjdMzLuTViQY032xbTF5AdZF5BJzJJyXQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-tHnHRBgQyiVNZJ8PksNinLdGOsE8+tFFv3E9QEtmwO+iyTHRvg4bJ4X0XZG1u9KxXMTJuAdeIWKWYr2rTLEHqQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.4", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2/aBgLqBXva/+w8pzRNY8ET43Gi+dr1gv/7ySfbsh23lTK6IAgID5MGUEa1hreNIF+0XpW4tX7QwVe70+YvaPg==", "path": "microsoft.aspnetcore.authorization/2.3.0", "hashPath": "microsoft.aspnetcore.authorization.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-vn31uQ1dA1MIV2WNNDOOOm88V5KgR9esfi0LyQ6eVaGq2h0Yw+R29f5A6qUNJt+RccS3qkYayylAy9tP1wV+7Q==", "path": "microsoft.aspnetcore.authorization.policy/2.3.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-Ov8UviDb51fNXL7oQXaKPygQ3lSr0SflDGG5A2H63b98cLdfKNi7xSD0GtfCj4LIhctUZKBx7ViSVgDn3YFlTQ==", "path": "microsoft.aspnetcore.connections.abstractions/8.0.13", "hashPath": "microsoft.aspnetcore.connections.abstractions.8.0.13.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4ivq53W2k6Nj4eez9wc81ytfGj6HR1NaZJCpOrvghJo9zHuQF57PLhPoQH5ItyCpHXnrN/y7yJDUm+TGYzrx0w==", "path": "microsoft.aspnetcore.hosting.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-F5iHx7odAbFKBV1DNPDkFFcVmD5Tk7rk+tYm3LMQxHEFFdjlg5QcYb5XhHAefl5YaaPeG6ad+/ck8kSG3/D6kw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I9azEG2tZ4DDHAFgv+N38e6Yhttvf+QjE2j2UYyCACE7Swm5/0uoihCMWZ87oOZYeqiEFSxbsfpT71OYHe2tpw==", "path": "microsoft.aspnetcore.http/2.3.0", "hashPath": "microsoft.aspnetcore.http.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-39r9PPrjA6s0blyFv5qarckjNkaHRA5B+3b53ybuGGNTXEj1/DStQJ4NWjFL6QTRQpL9zt7nDyKxZdJOlcnq+Q==", "path": "microsoft.aspnetcore.http.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VYMCOLvdT0y3O9lk4jUuIs8+re7u5+i+ka6ZZ6fIzSJ94c/JeMnAOOg39EB2i4crPXvLoiSdzKWlNPJgTbCZ2g==", "path": "microsoft.aspnetcore.http.connections/1.2.0", "hashPath": "microsoft.aspnetcore.http.connections.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-yUA7eg6kv7Wbz5TCW4PqS5/kYE5VxUIEDvoxjw4p1RwS2LGm84F9fBtM0mD6wrRfiv1NUyJ7WBjn3PWd/ccO+w==", "path": "microsoft.aspnetcore.http.connections.common/1.2.0", "hashPath": "microsoft.aspnetcore.http.connections.common.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EY2u/wFF5jsYwGXXswfQWrSsFPmiXsniAlUWo3rv/MGYf99ZFsENDnZcQP6W3c/+xQmQXq0NauzQ7jyy+o1LDQ==", "path": "microsoft.aspnetcore.http.extensions/2.3.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-f10WUgcsKqrkmnz6gt8HeZ7kyKjYN30PO7cSic1lPtH7paPtnQqXPOveul/SIPI43PhRD4trttg4ywnrEmmJpA==", "path": "microsoft.aspnetcore.http.features/2.3.0", "hashPath": "microsoft.aspnetcore.http.features.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-jMg/zNhAdM4cbSZ7y2aIU2/VNJwyVmAQ5jmvCo5KYqkpCkQEuaCn4GDJRtaJbvCS4nH7VYVfJFu7xW2/yuqy1w==", "path": "microsoft.aspnetcore.jsonpatch/8.0.19", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.19.nupkg.sha512"}, "Microsoft.AspNetCore.MiddlewareAnalysis/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-xBIimXuQOWOzADTnZwTTLteD4lAnvXLulcxjAo+l1kaLUEIlZPEgC5vB/KmsVjbXvdF8xUM3LmjsCtMKg4tNew==", "path": "microsoft.aspnetcore.middlewareanalysis/8.0.15", "hashPath": "microsoft.aspnetcore.middlewareanalysis.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-M4IoDNgdcA1fJUKNPDQF63pF3EMf+qi4eC8CZ8NbIkaRMALPRBoJ2eK/y1HqAqEuHoSJ8MFB0R5tVSjp1ett7Q==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.19", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.19.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-no5/VC0CAQuT4PK4rp2K5fqwuSfzr2mdB6m1XNfWVhHnwzpRQzKAu9flChiT/JTLKwVI0Vq2MSmSW2OFMDCNXg==", "path": "microsoft.aspnetcore.routing/2.3.0", "hashPath": "microsoft.aspnetcore.routing.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZkFpUrSmp6TocxZLBEX3IBv5dPMbQuMs6L/BPl0WRfn32UVOtNYJQ0bLdh3cL9LMV0rmTW/5R0w8CBYxr0AOUw==", "path": "microsoft.aspnetcore.routing.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XoCcsOTdtBiXyOzUtpbCl0IaqMOYjnr+6dbDxvUCFn7NR6bu7CwrlQ3oQzkltTwDZH0b6VEUN9wZPOYvPHi+Lg==", "path": "microsoft.aspnetcore.signalr/1.2.0", "hashPath": "microsoft.aspnetcore.signalr.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-PR7/EHwtXZPjR9MHnfqj5/pMs9Ja5YSyn6epXagjP4r8coOog9BtWaZfLF+XgezqDIDy9m1D4LK7pW6VjunR0Q==", "path": "microsoft.aspnetcore.signalr.common/8.0.13", "hashPath": "microsoft.aspnetcore.signalr.common.8.0.13.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Core/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-eZTuMkSDw1uwjhLhJbMxgW2Cuyxfn0Kfqm8OBmqvuzE9Qc/VVzh8dGrAp2F9Pk7XKTDHmlhc5RTLcPPAZ5PSZw==", "path": "microsoft.aspnetcore.signalr.core/1.2.0", "hashPath": "microsoft.aspnetcore.signalr.core.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hNvZ7kQxp5Udqd/IFWViU35bUJvi4xnNzjkF28HRvrdrS7JNsIASTvMqArP6HLQUc3j6nlUOeShNhVmgI1wzHg==", "path": "microsoft.aspnetcore.signalr.protocols.json/1.2.0", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.1.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-LZbs1RTwS0sfKZcqVcdQlxFEZdqlAGRojkkyli3Q7yJUvw9DeiJC3UuPwS5WXc6ppEFLig50FvmjC0Caxz47zg==", "path": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson/8.0.13", "hashPath": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson.8.0.13.nupkg.sha512"}, "Microsoft.AspNetCore.WebSockets/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+T4zpnVPkIjvvkyhTH3WBJlTfqmTBRozvnMudAUDvcb4e+NrWf52q8BXh52rkCrBgX6Cudf6F/UhZwTowyBtKg==", "path": "microsoft.aspnetcore.websockets/2.3.0", "hashPath": "microsoft.aspnetcore.websockets.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-trbXdWzoAEUVd0PE2yTopkz4kjZaAIA7xUWekd5uBw+7xE8Do/YOVTeb9d9koPTlbtZT539aESJjSLSqD8eYrQ==", "path": "microsoft.aspnetcore.webutilities/2.3.0", "hashPath": "microsoft.aspnetcore.webutilities.2.3.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Bcl.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bTUtGfpGyJnohQzjdXbtc7MqNzkv7CWUSRz54+ucNm0i32rZiIU0VdVPHDBShOl1qhVKRjW8mnEBz3d2vH93tQ==", "path": "microsoft.bcl.memory/9.0.0", "hashPath": "microsoft.bcl.memory.9.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.Data.Sqlite/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lw6wthgXGx3r/U775k1UkUAWIn0kAT0wj4ZRq0WlhPx4WAOiBsIjgDKgWkXcNTGT0KfHiClkM+tyPVFDvxeObw==", "path": "microsoft.data.sqlite/9.0.0", "hashPath": "microsoft.data.sqlite.9.0.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cFfZjFL+tqzGYw9lB31EkV1IWF5xRQNk2k+MQd+Cf86Gl6zTeAoiZIFw5sRB1Z8OxpEC7nu+nTDsLSjieBAPTw==", "path": "microsoft.data.sqlite.core/9.0.0", "hashPath": "microsoft.data.sqlite.core.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UmZBVaRbOgtbYkp4x8W9j8HBd7ywzPRPj5StSiqFt/nsH6nOf1eQmHFfjIRb70cxsuC0NbkFBAAoUHFRCscCKA==", "path": "microsoft.entityframeworkcore/6.0.1", "hashPath": "microsoft.entityframeworkcore.6.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-JM55+h9Ln9MZAFikvmgrmDoIt4Zz9ZWZpNrmyi9QTVuJMzQQOEnJkISKp5Ebvpno6Jt00Gkrfmvu8K5mFPO+dA==", "path": "microsoft.entityframeworkcore.abstractions/6.0.1", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fp8VTLv1up7EGAa9Daf+IErpq63hiKtWeApoxgnkMjURdjkbVcwaA4AozwHjEqVVQugGqwroHes4HVp24cDDAw==", "path": "microsoft.entityframeworkcore.analyzers/6.0.1", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-abaCEEeeB0xlMYDD+m9kmhQh5aipJ5s3UbyQVBfMaZeCiZHXST0eWuJRX63E3e7K/mju5WEHiybV/hizng7q/w==", "path": "microsoft.entityframeworkcore.relational/6.0.1", "hashPath": "microsoft.entityframeworkcore.relational.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-uSLKFK7IKLsgc3rqryPDH6qIOsEIagG/XGiq8osbgIZQpBff5TA28+3XwX3JyUf/h3lzqmnbyGsfOZf1v66BAA==", "path": "microsoft.extensions.ambientmetadata.application/8.10.0", "hashPath": "microsoft.extensions.ambientmetadata.application.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.StackExchangeRedis/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-fIVAYp6PcwjAz0nzAFxd3s/eVwihKuR/P1t300ZES25oBRndi/Sk60YA+GBq1vPTvDSxmygI0YlGo2CZO4Tb0g==", "path": "microsoft.extensions.caching.stackexchangeredis/8.0.13", "hashPath": "microsoft.extensions.caching.stackexchangeredis.8.0.13.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-SlVnMqZjYh7409vddtcoQ1N2HEtEQUHbYPuOOJZ/zNSvbJT4x78BBHaWUTKNEfwdg+MEftRVL63BLB6CVdOCRg==", "path": "microsoft.extensions.compliance.abstractions/8.10.0", "hashPath": "microsoft.extensions.compliance.abstractions.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "path": "microsoft.extensions.configuration.binder/8.0.2", "hashPath": "microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DjYkzqvhiHCq38LW71PcIxXk6nhtV6VySP9yDcSO0goPl7YCU1VG1f2Wbgy58lkA10pWkjHCblZPUyboCB93ZA==", "path": "microsoft.extensions.configuration.environmentvariables/6.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-xN0s32RZHnMDr08/fu1qoC7BSEoTL1HlKQCffsNlNtMN5EJnTLEAHS/ES/ugP8l3x9uoaqi//RA7Rkj2LZpWDg==", "path": "microsoft.extensions.dependencyinjection.autoactivation/8.10.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5Ou6varcxLBzQ+Agfm0k0pnH7vrEITYlXMDuE6s7ZHlZHz6/G8XJ3iISZDr5rfwfge6RnXJ1+Wc479mMn52vjA==", "path": "microsoft.extensions.dependencymodel/8.0.1", "hashPath": "microsoft.extensions.dependencymodel.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DiagnosticAdapter/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-oDv3wt+Q5cmaSfOQ3Cdu6dF6sn/x5gzWdNpOq4ajBwCMWYBr6CchncDvB9pF83ORlbDuX32MsVLOPGPxW4Lx4g==", "path": "microsoft.extensions.diagnosticadapter/3.1.32", "hashPath": "microsoft.extensions.diagnosticadapter.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-doVPCUUCY7c6LhBsEfiy3W1bvS7Mi6LkfQMS8nlC22jZWNxBv8VO8bdfeyvpYFst6Kxqk7HBC6lytmEoBssvSQ==", "path": "microsoft.extensions.diagnostics/8.0.1", "hashPath": "microsoft.extensions.diagnostics.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.1", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-ajZg6WnvwSi8/gpNWQ7ruoEooOdpwNf/DR9Yck3b2AVrorPuJWJjWFpgfCAwph4DO4PPazB+6ifcWBHWm4QBZA==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/8.10.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.Features/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-tsfmy1JfRhbKT/uYWxWcR1yhFQXgVAxtvQDou2NkY6UhqkFFxjO7fkd0CWc6YsX/qqbW3VxhabhDRnMKKA72lQ==", "path": "microsoft.extensions.features/8.0.13", "hashPath": "microsoft.extensions.features.8.0.13.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nqOrLtBqpwRT006vdQ2Vp87uiuYztiZcZAndFqH91ZH4SQgr8wImCVQwzUgTxx1DSrpIW765+xrZTZqsoGtvqg==", "path": "microsoft.extensions.hosting/2.1.0", "hashPath": "microsoft.extensions.hosting.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "path": "microsoft.extensions.hosting.abstractions/8.0.1", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDYeKJUzh0qeg/AI+nSr3ffthmXYQTEb0nS9qRC7YhSbbuN4M4NPbaB77AJwtkTnCV9XZ7qYj3dkZaNcyl73EA==", "path": "microsoft.extensions.http/8.0.1", "hashPath": "microsoft.extensions.http.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-lLvjt7N4tUwXd1DJvsC7K12T1ZzmQpFBxFA6moi6A7SdJw5JLvhzXULsQUt06mxeu0tjkrR0neJJTsO8OZs4Uw==", "path": "microsoft.extensions.http.diagnostics/8.10.0", "hashPath": "microsoft.extensions.http.diagnostics.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-RntdzxuaTO0yjHTWN3+NQF6N8+qPezAloQ3VqYCyUBI7Hx6uorrPikz7x56onfllLanrf7RnKNYeLyjW3f4sPA==", "path": "microsoft.extensions.http.polly/8.0.13", "hashPath": "microsoft.extensions.http.polly.8.0.13.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-b+CnTcqlmg4vppGmxsLifY4gnMsE7XDhH1VRxpIn7NBksCoCqJn/2U+TTH9LjpNrAG6tfnGT8m4PP6WaDdPoMQ==", "path": "microsoft.extensions.http.resilience/8.10.0", "hashPath": "microsoft.extensions.http.resilience.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "path": "microsoft.extensions.logging.abstractions/8.0.3", "hashPath": "microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QWwTrsgOnJMmn+XUslm8D2H1n3PkP/u/v52FODtyBc/k4W9r3i2vcXXeeX/upnzllJYRRbrzVzT0OclfNJtBJA==", "path": "microsoft.extensions.logging.configuration/8.0.1", "hashPath": "microsoft.extensions.logging.configuration.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-6ApKcHNJigXBfZa6XlDQ8feJpq7SG1ogZXg6M4FiNzgd6irs3LUAzo0Pfn4F2ZI9liGnH1XIBR/OtSbZmJAV5w==", "path": "microsoft.extensions.objectpool/8.0.11", "hashPath": "microsoft.extensions.objectpool.8.0.11.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Resilience/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-2WsgG1zEPVtROZ+SHSAKiyuTREqFOOz/gPPDD/9BjfxYEpGa8E+QsD86UGeWIwnrplUxF6e7K388XkVDWBbSnQ==", "path": "microsoft.extensions.resilience/8.10.0", "hashPath": "microsoft.extensions.resilience.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-FcWCYW8W1O05w5tXtFRl4seCtTgV4ihTrCFgkbDUo3PIW2f5N8I1IGEE7KbdApxBFN0Oqf8FmVmtvjTncmlUBw==", "path": "microsoft.extensions.telemetry/8.10.0", "hashPath": "microsoft.extensions.telemetry.8.10.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-lnXq37FJZHJPbSJNEFoQVndG8cJvRbzeaUE9eoY1i6yfQUACHnEhuMnkZm7zPCkSqn0tylF0fITQn/RuDvt+0g==", "path": "microsoft.extensions.telemetry.abstractions/8.10.0", "hashPath": "microsoft.extensions.telemetry.abstractions.8.10.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-4dDpw7DJ2yx2vFM/w0sceXoByUhrU68eMdlXyzsPTWPtAfgCbkuMl7jfLBLegmgerbOzGNMm7zq5xwr4+7yTSg==", "path": "microsoft.identitymodel.abstractions/8.4.0", "hashPath": "microsoft.identitymodel.abstractions.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lyMsODXEMNvV0oTUehuz6wkohldFqQg5su/2Hdzr2xS1kdvilqyywkoVnpbJVb7zYr7TA+6rVCTRV/0f2uSBPQ==", "path": "microsoft.identitymodel.jsonwebtokens/8.4.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-vIKIBDMD6l2MeCwkdeh1XHSHNVz9C/qp9PaHMG6SF3OUSAh/2XMVj2zaVc2hs27QRfhh5xR3tvMSs8R96WS2IA==", "path": "microsoft.identitymodel.logging/8.4.0", "hashPath": "microsoft.identitymodel.logging.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cDvZZ8JYto6L1I64sWW17JGwYGuFZ5Qm+WZG+wLk0QHjtuSosujDVAc4nr/sx6+n88q1mdW93rGEl7TCniNp5Q==", "path": "microsoft.identitymodel.protocols/8.4.0", "hashPath": "microsoft.identitymodel.protocols.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-n/wBvhj7oq3rZyImUhkUv6bUjwPWK+jG1R3BsCfahLtwoOzIWSPLmzMQ61HCtd1BiCU43IdkX9M+cD1xMmE3xQ==", "path": "microsoft.identitymodel.tokens/8.4.0", "hashPath": "microsoft.identitymodel.tokens.8.4.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "path": "microsoft.io.recyclablememorystream/3.0.0", "hashPath": "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-wyz7YvzDjcGn+Y5FsKhSk7/2xbR0gOGWUwWN+x8V5ZBf/UODbt/6ZyEJyxLhCQU5Cgqw0k20LsfJ+9+bZZMG9A==", "path": "microsoft.net.http.headers/8.0.13", "hashPath": "microsoft.net.http.headers.8.0.13.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-8N<PERSON>HOE56YsY59HYY89akRMup8Ho+7Y3cADTGjajjWroXVU9RQai2nA6PfteB8AuzmRHZ5NZQB2BnWhQEul5g==", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.21.0", "hashPath": "microsoft.visualstudio.azure.containers.tools.targets.1.21.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA==", "path": "microsoft.win32.primitives/4.0.1", "hashPath": "microsoft.win32.primitives.4.0.1.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MiniProfiler.AspNetCore/4.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-dohMvXpjKDPv/edl7gwKhq80JBqRLLRSwVJB9bo0UYqsgEox7BZyYS/4vBty+UsZ59pYYYhMUpUKHVWLLj/PBw==", "path": "miniprofiler.aspnetcore/4.3.8", "hashPath": "miniprofiler.aspnetcore.4.3.8.nupkg.sha512"}, "MiniProfiler.AspNetCore.Mvc/4.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-aJ6Kkw2zMy36cKDWTjQYo/pJ6bhPBRA8z4NO8REe+xDhv8+fk58P526Bi52gnvsDp4jIVk5AQ8nQDgPUS/K+7A==", "path": "miniprofiler.aspnetcore.mvc/4.3.8", "hashPath": "miniprofiler.aspnetcore.mvc.4.3.8.nupkg.sha512"}, "MiniProfiler.Shared/4.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-SfXNX90fmDm373YAla0z06plTCj6YbByQJOm6G8/9kE6Hf4UALJxySyiMB9O4KYeTc6Ha1EFQDs6jLhio+bBFA==", "path": "miniprofiler.shared/4.3.8", "hashPath": "miniprofiler.shared.4.3.8.nupkg.sha512"}, "MongoDB.Bson/2.28.0": {"type": "package", "serviceable": true, "sha512": "sha512-yiWN3scVV0lYpFXn1CL4PB0BK3KrHFC44qw72+/cBIDS9+r9MQYfxFMxM+pAvnSssAZtQ7ZabCu7z3UOXqKy4w==", "path": "mongodb.bson/2.28.0", "hashPath": "mongodb.bson.2.28.0.nupkg.sha512"}, "MongoDB.Driver/2.28.0": {"type": "package", "serviceable": true, "sha512": "sha512-/aMeLbiqm8aqopZU7MlnJwQfc94VLoTRIK3xPSWqv6kDS1MV8tf1kcX2X3Ji71n2NspZ1jwHIpZ/AP5sQyBhXw==", "path": "mongodb.driver/2.28.0", "hashPath": "mongodb.driver.2.28.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.28.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>k5cjiN9FwBsTdJnLouOsKWjplr4D+8fs9urOhzBKZLygOsgIxTDLzpoHC3Mg4bJ1V/Mbz0/xXr0KqUXdmzqEg==", "path": "mongodb.driver.core/2.28.0", "hashPath": "mongodb.driver.core.2.28.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-x1Fm9b5/93k1l0FpbUxHrcFIm/6m/qXfmbQddtHILqltx2EVSkIAzzBMiCr4D/3IBXSEWzgXP73xFExIpXQ9lQ==", "path": "mongodb.libmongocrypt/1.11.0", "hashPath": "mongodb.libmongocrypt.1.11.0.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "nacos-sdk-csharp/1.3.7": {"type": "package", "serviceable": true, "sha512": "sha512-Zne/S+DXeuK6T8RpDyODa7mMFUUPa38eR7i/9uDUFAIAbJt7bzOjWmN67xi9NSE4Z8lLRhO6hfNjM3blLWyJYw==", "path": "nacos-sdk-csharp/1.3.7", "hashPath": "nacos-sdk-csharp.1.3.7.nupkg.sha512"}, "nacos-sdk-csharp-unofficial/0.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-P6XtcilVILlPK2CoMQMKm86ndV5ITs4rcKel5FA0Z/b/+XBMvDX+DwKn5FU6q7yrOmsG61dRTDZ9RTkeaZY0Zg==", "path": "nacos-sdk-csharp-unofficial/0.8.5", "hashPath": "nacos-sdk-csharp-unofficial.0.8.5.nupkg.sha512"}, "nacos-sdk-csharp-unofficial.AspNetCore/0.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-MRTYmIZKOYOr3KscNhtUuzPCifu/2ewzpqxs/fBIBDtUOKxIeYUk6R+IaKPv1kaYsKigXQNatEvZva289RhC+w==", "path": "nacos-sdk-csharp-unofficial.aspnetcore/0.8.5", "hashPath": "nacos-sdk-csharp-unofficial.aspnetcore.0.8.5.nupkg.sha512"}, "nacos-sdk-csharp-unofficial.Extensions.Configuration/0.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-4hlEcx0+1Ij0bIuZeS/EZjREoNEul3og1vbOk1Q3SV1mBxn9VJhXhkOJw+McKx4T9igf/SR2KiOyTOocpUb4ag==", "path": "nacos-sdk-csharp-unofficial.extensions.configuration/0.8.5", "hashPath": "nacos-sdk-csharp-unofficial.extensions.configuration.0.8.5.nupkg.sha512"}, "nacos-sdk-csharp-unofficial.IniParser/0.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-jjARgdOj1mlFPdRMBhD+xBNBTitvfEcFrY+qfAyELr7HTaPsxTj5eRY6IbG579yuxES5zKyKjvjiuv5iuhZ8Ow==", "path": "nacos-sdk-csharp-unofficial.iniparser/0.8.5", "hashPath": "nacos-sdk-csharp-unofficial.iniparser.0.8.5.nupkg.sha512"}, "nacos-sdk-csharp.AspNetCore/1.3.7": {"type": "package", "serviceable": true, "sha512": "sha512-q+Aw171+XpsZJzGx5uH4m3qd6CGlkoE7agrRKd+cl4EgEibBySE7RuMgBmwqtCaTF5aOFL6Vf9iZSVflTekOMw==", "path": "nacos-sdk-csharp.aspnetcore/1.3.7", "hashPath": "nacos-sdk-csharp.aspnetcore.1.3.7.nupkg.sha512"}, "NCalc.NetCore/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-D3++vfHvNxJaoExngCZHuqZeX8ktH5jx2Po/5NFM+MICXWaAmvvBLEl22CY7ec8YLYAacC2zenFFpmPjCxZllg==", "path": "ncalc.netcore/1.0.1", "hashPath": "ncalc.netcore.1.0.1.nupkg.sha512"}, "NetDevPack.Security.JwtExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ikynI90CXtCJRdlBz/RnTk8ZUT+mqRJTut0hwSf/Zg5wbUSXEEw69fJ+RoP8QSkt9/9AXd1N9RCIsaNfLJACNg==", "path": "netdevpack.security.jwtextensions/8.0.0", "hashPath": "netdevpack.security.jwtextensions.8.0.0.nupkg.sha512"}, "NETStandard.Library/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ypsCvIdCZ4IoYASJHt6tF2fMo7N30NLgV1EbmC+snO490OMl9FvVxmumw14rhReWU3j3g7BYudG6YCrchwHJlA==", "path": "netstandard.library/1.6.0", "hashPath": "netstandard.library.1.6.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Npgsql/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1u4iCPKL9wtPeSUzChIbgq/BlOhvf44o7xASacdpMY7z0PbqACKpNOF0fjEn9jDV/AJl/HtPY6zk5qasQ1URhw==", "path": "npgsql/5.0.18", "hashPath": "npgsql.5.0.18.nupkg.sha512"}, "NPOI/2.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ho29P+Kl6jye33X999o/qP9lqNDtpSQM/OnbdAcGhFBRpa30aqtMo+iNqa/EJFvZrQCo0z98drrCzCgGfjo3uA==", "path": "npoi/2.7.1", "hashPath": "npoi.2.7.1.nupkg.sha512"}, "Ocelot/24.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-f+5jdGU/bMSVjQeS/02EHn2afK8fyqGlswlrMOcDhQzHcA1pcMYvsFn9YKFFy4ynv2pyHmCue76Vs7KI9GuKOg==", "path": "ocelot/24.0.1", "hashPath": "ocelot.24.0.1.nupkg.sha512"}, "OpenIddict/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MToERl9MtM0dslmz+GzAHy3CExXL1Ujd4JKLittGPG/HZ1K+8lGKZcowAztWDhshjdTi3DStgjY8Ik4T8/Drmg==", "path": "openiddict/6.2.0", "hashPath": "openiddict.6.2.0.nupkg.sha512"}, "OpenIddict.Abstractions/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-5TXPUWUz5QfBwGm9i0mFhPp9xd667YQG4BoaiYa7NCSulna4FF264z/qXQ0SWCaZtTnBoFDDjA7tyLcnsbG06w==", "path": "openiddict.abstractions/6.2.0", "hashPath": "openiddict.abstractions.6.2.0.nupkg.sha512"}, "OpenIddict.AspNetCore/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-PhOynSGDCUt9Sic+s46zTRUo4A3j30WMuWHvLw0ruJG5s3NQAuZLpK+AsuIbCttNMcrLnCJE48nRj1BEkEVMCw==", "path": "openiddict.aspnetcore/6.2.0", "hashPath": "openiddict.aspnetcore.6.2.0.nupkg.sha512"}, "OpenIddict.Client/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-eCrFZ0YEhGAPdNLevvcWP4EUwzbkrHBJ7bZm5Vuuwv2n0RPu5sWpfKHBUhfBcha8V0aivkK8TrW0L5JHK4vEFg==", "path": "openiddict.client/6.2.0", "hashPath": "openiddict.client.6.2.0.nupkg.sha512"}, "OpenIddict.Client.AspNetCore/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-avXi3lPOAbyIamL3HpgEmep8qoJxn/1K0A20xenqf65EQMAX8rRH878igOX+eQGcgR+7mT7PHMvLmZcFUZ91EA==", "path": "openiddict.client.aspnetcore/6.2.0", "hashPath": "openiddict.client.aspnetcore.6.2.0.nupkg.sha512"}, "OpenIddict.Client.DataProtection/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-q7c4PbvWP1L0m4QOcDsvHwHfhUyzoiCecs8+dKsYjTDQP9ZRruFN6BZfNn815cUy5xmww0w3TYfRsnEbmbZdmQ==", "path": "openiddict.client.dataprotection/6.2.0", "hashPath": "openiddict.client.dataprotection.6.2.0.nupkg.sha512"}, "OpenIddict.Client.SystemIntegration/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-T10jI2ctKLUsG76REeuayZrqgZS/o72vRIk7i7m7iFFMcEbu9XnaqxcS8Q8b8Bmrd8Illvs8ilpr3LtaBDxMTw==", "path": "openiddict.client.systemintegration/6.2.0", "hashPath": "openiddict.client.systemintegration.6.2.0.nupkg.sha512"}, "OpenIddict.Client.SystemNetHttp/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-DpA+rND7RT8AhV4v0HC5e4HdU1hrFBACMlT0tr6ocWb6qa0u2OH70sCblEJrDDdLi11Nzfa1q2U0AWNcSnC1+Q==", "path": "openiddict.client.systemnethttp/6.2.0", "hashPath": "openiddict.client.systemnethttp.6.2.0.nupkg.sha512"}, "OpenIddict.Client.WebIntegration/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-kMMBiZqpe14whnWScZ0OCpbMYM1sgCsjCv+jzWPhadP9sHNTyYq3GzHrbHVx/V8MASgLP7RnyUXDQhAd4MPE6w==", "path": "openiddict.client.webintegration/6.2.0", "hashPath": "openiddict.client.webintegration.6.2.0.nupkg.sha512"}, "OpenIddict.Core/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-xW62xoahIQnpvnCl7ZM1onk7f96sVcDV9E+mA01qHSfHRQElJAGvb6znOOCckbED2laddXqMGMf1g2hZElMlvw==", "path": "openiddict.core/6.2.0", "hashPath": "openiddict.core.6.2.0.nupkg.sha512"}, "OpenIddict.Quartz/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-KopZEbjZCS5MqPAuQVsc14qmxR0YyabkLOMfr6mGEduyc4ShUZ1tqcSnuVOmmtyNZAt9d77LjxUKUHPTGvRPpg==", "path": "openiddict.quartz/6.2.0", "hashPath": "openiddict.quartz.6.2.0.nupkg.sha512"}, "OpenIddict.Server/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-96s4J7/h8RDVLCAd1ECndZBc96WB7qjaw1ci7I6YXvCUIVJ1TTJMQQVml8nEzJoljg8fONkteUSFXVzJx65l0w==", "path": "openiddict.server/6.2.0", "hashPath": "openiddict.server.6.2.0.nupkg.sha512"}, "OpenIddict.Server.AspNetCore/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Esqy0AibJJai1WZXHAkX4G9adHXeTm8SDTO7AGKnTCQmEILzlvhCSV6FAOQ/QFDMKWHescTir21dLBPRmKgumA==", "path": "openiddict.server.aspnetcore/6.2.0", "hashPath": "openiddict.server.aspnetcore.6.2.0.nupkg.sha512"}, "OpenIddict.Server.DataProtection/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-IJkus1HgXpa5ApdfD98vxgskxlcvCMShLSy3hD2WrEQcdXsPsKyfBwh3bKmxQsH3QZPGqtvCqBY9a+9V370c+A==", "path": "openiddict.server.dataprotection/6.2.0", "hashPath": "openiddict.server.dataprotection.6.2.0.nupkg.sha512"}, "OpenIddict.Validation/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nnMjnIEnpNeF3dTNy3HG22kJ5nUiy+U661L+4eltje1nUZ2vfnFn08u7uusG4D9l8xtkKjcleebzlMGQ8SB3HQ==", "path": "openiddict.validation/6.2.0", "hashPath": "openiddict.validation.6.2.0.nupkg.sha512"}, "OpenIddict.Validation.AspNetCore/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MlBgDp9e0+vMGX8H0ZDQFZqy6bdBvDQ0FIxUVKeKVmvdpCYvNl3MOaB+QkhsJCrohUmwph1lSJK6C+5ACBFeaA==", "path": "openiddict.validation.aspnetcore/6.2.0", "hashPath": "openiddict.validation.aspnetcore.6.2.0.nupkg.sha512"}, "OpenIddict.Validation.DataProtection/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-eamr5Qu8mxXYKSSoBY66pCE06zkeoao1D5kq8AkeiVSLYLL7Di1isaZlSgOfBDhYkxs30ctVOuX+TIw2wRYoYA==", "path": "openiddict.validation.dataprotection/6.2.0", "hashPath": "openiddict.validation.dataprotection.6.2.0.nupkg.sha512"}, "OpenIddict.Validation.ServerIntegration/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-70kG8X8kL/FmboBL1I56Dduqj1xDJcEuovkMcMjROgCEx40/0AMSy+1eY0FwjfH1IBUFcs4NckK0DpGRqPjrsg==", "path": "openiddict.validation.serverintegration/6.2.0", "hashPath": "openiddict.validation.serverintegration.6.2.0.nupkg.sha512"}, "OpenIddict.Validation.SystemNetHttp/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-E<PERSON>zZ3ef4HazuEaUYATXC6mlq2CrnbeJ7OCqHbZj0OvK5S66M7x301cRz3D2935MzGYzgYWLG/q0UoqqUVccPqA==", "path": "openiddict.validation.systemnethttp/6.2.0", "hashPath": "openiddict.validation.systemnethttp.6.2.0.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.100": {"type": "package", "serviceable": true, "sha512": "sha512-nsqyUE+v246WB0SOnR1u9lfZxYoNcdj1fRjTt7TOhCN0JurEc6+qu+mMe+dl1sySB2UpyWdfqHG1iSQJYaXEfA==", "path": "oracle.manageddataaccess.core/3.21.100", "hashPath": "oracle.manageddataaccess.core.3.21.100.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "PinYinConverterCore/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-GWGUL7HYyyTZeVltwgJ2sr8ER+Wyj1aR44KN1A5GS0iB3j/JqXDG62OHupz3iFczCQ4797IhHSSqOet7MvsWJQ==", "path": "pinyinconvertercore/1.0.2", "hashPath": "pinyinconvertercore.1.0.2.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Polly/8.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-kBxql53peR0bjxeEuuY114GD2rmC0tkUwE1xuKUlnd74ULEsGu3OwcLH56KkwxBPUbOysPa7stT9SJ6pKGTzlg==", "path": "polly/8.4.1", "hashPath": "polly.8.4.1.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "protobuf-net/3.2.30": {"type": "package", "serviceable": true, "sha512": "sha512-C/UTlmxEJHAHpqm8xQK1UyJKaIynVCSNG4mVrbLgnZ7ccH28nN49O8iMJvKEodTgVbnimvy+3mIiAdW6mATwnw==", "path": "protobuf-net/3.2.30", "hashPath": "protobuf-net.3.2.30.nupkg.sha512"}, "protobuf-net.Core/3.2.30": {"type": "package", "serviceable": true, "sha512": "sha512-v2ZxxYrz+X212ukSx+uqkLuPu414bvmSAnTyf+PBUKR9ENJxO4P/csorA/27456MCp1JNoMssDj/f91RDiwBfQ==", "path": "protobuf-net.core/3.2.30", "hashPath": "protobuf-net.core.3.2.30.nupkg.sha512"}, "Quartz/3.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8mY6S0FWbtuEFbStUEZAkkGpZLbSuTT0us1w0DUQqhi+vwg/7XXS36TeH3o34zZoqYGyXvk6jrKnW/p6kS8sg==", "path": "quartz/3.14.0", "hashPath": "quartz.3.14.0.nupkg.sha512"}, "Quartz.Extensions.DependencyInjection/3.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-CUY9ZaUsmnl8YxPcl3eXmg/1UVWv1NdyATKGaBjfxf8MYH36vgCoerv7aCSjM/oRbdiaBwNnPfNgHMzxUu1p1g==", "path": "quartz.extensions.dependencyinjection/3.14.0", "hashPath": "quartz.extensions.dependencyinjection.3.14.0.nupkg.sha512"}, "Quartz.Extensions.Hosting/3.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-kbxBND5Nift+qwOud8cjywoLvjcQX+aLt6R7qQcQVg8w84NUNjTsjzZ5zIod9vz7ricSvIaQXGVbhpm7X6yVQA==", "path": "quartz.extensions.hosting/3.14.0", "hashPath": "quartz.extensions.hosting.3.14.0.nupkg.sha512"}, "RabbitMQ.Client/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-jNsmGgmCNw2S/NzskeN2ijtGywtH4Sk/G6jWUTD5sY9SrC27Xz6BsLIiB8hdsfjeyWCa4j4GvCIGkpE8wrjU1Q==", "path": "rabbitmq.client/6.8.1", "hashPath": "rabbitmq.client.6.8.1.nupkg.sha512"}, "RestSharp/111.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-MhrBdwqYd+ezd3xD+c6eaAVcLJoWI+2TBH7rqX149nbYRkPJJfK/UbrgJVfKN9ZMehOFzo2PmKIZjS1ATnxEAQ==", "path": "restsharp/111.4.1", "hashPath": "restsharp.111.4.1.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Nh0UPZx2Vifh8r+J+H2jxifZUD3sBrmolgiFWJd2yiNrxO0xTa6bAw3YwRn1VOiSen/tUXMS31ttNItCZ6lKuA==", "path": "runtime.native.system.net.http/4.0.1", "hashPath": "runtime.native.system.net.http.4.0.1.nupkg.sha512"}, "runtime.native.System.Security.Cryptography/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2CQK0jmO6Eu7ZeMgD+LOFbNJSXHFVQbCJJkEyEwowh1SCgYnrn9W9RykMfpeeVGw7h4IBvYikzpGUlmZTUafJw==", "path": "runtime.native.system.security.cryptography/4.0.0", "hashPath": "runtime.native.system.security.cryptography.4.0.0.nupkg.sha512"}, "Scrutor/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BwqCnFzp2/Z+pq17iztxlIkR/ZANyPRR4PdE57WL1w/JW4AM/2imoxBWTL3+G+YXA46ce4s9OUgwWqTXYrtI8A==", "path": "scrutor/3.3.0", "hashPath": "scrutor.3.3.0.nupkg.sha512"}, "Serilog/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pzeDRXdpSLSsgBHpZcmpIDxqMy845Ab4s+dfnBg0sN9h8q/4Wo3vAoe0QCGPze1Q06EVtEPupS+UvLm8iXQmTQ==", "path": "serilog/4.0.1", "hashPath": "serilog.4.0.1.nupkg.sha512"}, "Serilog.AspNetCore/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-LNUd1bHsik2E7jSoCQFdeMGAWXjH7eUQ6c2pqm5vl+jGqvxdabYXxlrfaqApjtX5+BfAjW9jTA2EKmPwxknpIA==", "path": "serilog.aspnetcore/8.0.2", "hashPath": "serilog.aspnetcore.8.0.2.nupkg.sha512"}, "Serilog.Expressions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QhZjXtUcA2QfQRA60m+DfyIfidKsQV7HBstbYEDqzJKMbJH/KnKthkkjciRuYrmFE+scWv1JibC5LlXrdtOUmw==", "path": "serilog.expressions/5.0.0", "hashPath": "serilog.expressions.5.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hn8HCAmupon7N0to20EwGeNJ+L3iRzjGzAHIl8+8CCFlEkVedHvS6NMYMb0VPNMsDgDwOj4cPBPV6Fc2hb0/7w==", "path": "serilog.settings.configuration/8.0.2", "hashPath": "serilog.settings.configuration.8.0.2.nupkg.sha512"}, "Serilog.Sinks.Async/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-a+kTyUUxPAOZWKJiNbDqCPMiP0BWBWzObyTpRmGLrgCecgc/YJ+HqYGjsQoS6Sj9cRVXB9hH5O1mTZ0DiewG2w==", "path": "serilog.sinks.async/2.0.0", "hashPath": "serilog.sinks.async.2.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k57sDVgYitVdA5h9XSvy8lSlEts1ZzqlApHINUNV5WIuvnt6Z18LNynUQI6JYioKdqbUhkY6+KP844w7/awcOw==", "path": "serilog.sinks.periodicbatching/5.0.0", "hashPath": "serilog.sinks.periodicbatching.5.0.0.nupkg.sha512"}, "Serilog.Sinks.RollingFile/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2lT5X1r3GH4P0bRWJfhA7etGl8Q2Ipw9AACvtAHWRUSpYZ42NGVyHoVs2ALBZ/cAkkS+tA4jl80Zie144eLQPg==", "path": "serilog.sinks.rollingfile/3.3.0", "hashPath": "serilog.sinks.rollingfile.3.3.0.nupkg.sha512"}, "Serilog.Sinks.Seq/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z5ig56/qzjkX6Fj4U/9m1g8HQaQiYPMZS4Uevtjg1I+WWzoGSf5t/E+6JbMP/jbZYhU63bA5NJN5y0x+qqx2Bw==", "path": "serilog.sinks.seq/8.0.0", "hashPath": "serilog.sinks.seq.8.0.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "SixLabors.Fonts/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-4+NKz8W36injp98lmmM07ncp08HAK8c6FZz8vLoKxRPfJeEVWpBHlLYEbQa5rcqKKYqxUv/RVCrb8XcPhfMKUQ==", "path": "sixlabors.fonts/2.0.4", "hashPath": "sixlabors.fonts.2.0.4.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-lNtlq7dSI/QEbYey+A0xn48z5w4XHSffF8222cC4F4YwTXfEImuiBavQcWjr49LThT/pRmtWJRcqA/PlL+eJ6g==", "path": "sixlabors.imagesharp/3.1.5", "hashPath": "sixlabors.imagesharp.3.1.5.nupkg.sha512"}, "SixLabors.ImageSharp.Drawing/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-p4VwtAABggDUhUS5zXldCZHVGfjJl76+SrBHY4biNQ8+890igFK6RL87qIv9GqvEjMcYOar1sPkf2iMQ6uq9/g==", "path": "sixlabors.imagesharp.drawing/2.1.4", "hashPath": "sixlabors.imagesharp.drawing.2.1.4.nupkg.sha512"}, "SkiaSharp/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "path": "skiasharp/2.88.6", "hashPath": "skiasharp.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-ecgMclPor+X1wi2dZSVDo1sV2Dm8gwEKNRtS+qiE9qfnQzGHbYWlbTBWalnZBaIl3BLC21b1QO8gMgabhSAh+g==", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.6", "hashPath": "skiasharp.nativeassets.linux.nodependencies.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "path": "skiasharp.nativeassets.macos/2.88.6", "hashPath": "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "path": "skiasharp.nativeassets.win32/2.88.6", "hashPath": "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512"}, "SkyAPM.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KWbScefFLkfbIxQX8yAyTExwg2WHU6AZYCZstCAK2fgFQl3TQwfFjvEuKkIDuJB04ZE6XdnBXGkuA6fSSa5Vsw==", "path": "skyapm.abstractions/2.1.0", "hashPath": "skyapm.abstractions.2.1.0.nupkg.sha512"}, "SkyAPM.Agent.AspNetCore/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-dOrtVbRDJwu9ddPMYVh7IkgUe4Su9UQThDE5C9hVyE+BhnFm1Yl1f2h932xfkKQ49hK1Hz7GuREwg9scjiV3ZA==", "path": "skyapm.agent.aspnetcore/2.1.0", "hashPath": "skyapm.agent.aspnetcore.2.1.0.nupkg.sha512"}, "SkyAPM.Agent.Hosting/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lia9pimy0jbuQ6s3oaRhAZpPmSZ2ANXY3vwnhBLoMa3/KnPSqY2cQK/yW6nq2aPfLywZ43cv51A5p7cpk14NVw==", "path": "skyapm.agent.hosting/2.1.0", "hashPath": "skyapm.agent.hosting.2.1.0.nupkg.sha512"}, "SkyAPM.Core/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-dccln482YjgfFsV7OHMbIrlJ6gGoiZRxeIW1hGx+qgWt86RP8wc7pnIzNunjzpIS8q/9ua5izlg27M8ocWpL0Q==", "path": "skyapm.core/2.1.0", "hashPath": "skyapm.core.2.1.0.nupkg.sha512"}, "SkyAPM.Diagnostics.AspNetCore/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-iExq1vCvdNfX6l4c3krjqGV3F7sOPGWSBrBXBq5b6ghTo36XLhC/Pynzy+zOB463/4NP5I/Kq2LZW8WU0y8r2w==", "path": "skyapm.diagnostics.aspnetcore/2.1.0", "hashPath": "skyapm.diagnostics.aspnetcore.2.1.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-cQmexVuMOn2c4Nd2ze5e3xTthvQEjbnW/stV5lvV/70NbonaW8GJ5B1P1PK8ibIXUva2e/NBljdYjEvsaq/N8A==", "path": "skyapm.diagnostics.entityframeworkcore/2.1.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.2.1.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-AdC2Nlpbqay/N55wsOxjmM0hkfJ3stgJp5eh+wb4wmpFB/DWOjdMw5IeMg4HBC0o8KzsZTf+nsLkcdd/CA/vpw==", "path": "skyapm.diagnostics.entityframeworkcore.npgsql/2.1.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.npgsql.2.1.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tlwhG+tISQI6GiFvA8Kz4xOZo7cOAAmXHrQCHmn9LEBKyCz7buf1n6H82uM3iQrfCJyfjciQPFR3bLGL6FFPUQ==", "path": "skyapm.diagnostics.entityframeworkcore.pomelo.mysql/2.1.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.pomelo.mysql.2.1.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Zna1faORTP+zVKKAg3JAMDvG4gyfH+O98R8zs4rD2L9WrOMCx19o9KAnu98OTejiaSMZoKono6v0BsoUr4K14A==", "path": "skyapm.diagnostics.entityframeworkcore.sqlite/2.1.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.sqlite.2.1.0.nupkg.sha512"}, "SkyApm.Diagnostics.Grpc/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vomdFMBHVFEerRbbL3MsWXz39duRcJcBv+OSwR785n3CeL0wSeo28tAkvqT8f1Tq/W+vtz+v83KbUPh744g7KA==", "path": "skyapm.diagnostics.grpc/2.1.0", "hashPath": "skyapm.diagnostics.grpc.2.1.0.nupkg.sha512"}, "SkyApm.Diagnostics.Grpc.Net.Client/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-rR6m7zkojNBg01VoXSklfDJHlomlrOFTDSinMCAb45UciR8YFIXpO47BCAbxt4wngEIUlBrxFbeNzooRFW8GhA==", "path": "skyapm.diagnostics.grpc.net.client/2.1.0", "hashPath": "skyapm.diagnostics.grpc.net.client.2.1.0.nupkg.sha512"}, "SkyAPM.Diagnostics.HttpClient/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-zd<PERSON><PERSON>joynUoDtYB56qQl3H1KSRSElbOkGbhspJ84ER+QMfH9I4k6NEfBKxlobz/O0f7293qP7CORqimOLFg8dNw==", "path": "skyapm.diagnostics.httpclient/2.1.0", "hashPath": "skyapm.diagnostics.httpclient.2.1.0.nupkg.sha512"}, "SkyApm.Diagnostics.MSLogging/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-T0xa5ACwjpoBmxPIthMpeJndwsIbOdzr976vxSvr4xobuztZ75qc2WeR84QdaFbvnSwvFToZcx1th+LnKYB52g==", "path": "skyapm.diagnostics.mslogging/2.1.0", "hashPath": "skyapm.diagnostics.mslogging.2.1.0.nupkg.sha512"}, "SkyAPM.Diagnostics.SqlClient/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-a3hHRY8ZE5b3B1AQbXcUDa7qmSqbDHOuFzJeRYMC1KSv8MaY+tY0/SJvzRsfJrDCZb3t7a8seDKKVkWdWwpFvQ==", "path": "skyapm.diagnostics.sqlclient/2.1.0", "hashPath": "skyapm.diagnostics.sqlclient.2.1.0.nupkg.sha512"}, "SkyAPM.Transport.Grpc/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mXrRvprIppqd3+hJS4+LU6UxZaR4M78u2ilf6qb2fyNk2e6EEJ0yu+apwcAvbpF8DbBON/VkjZgqGMOhNQ/wqw==", "path": "skyapm.transport.grpc/2.1.0", "hashPath": "skyapm.transport.grpc.2.1.0.nupkg.sha512"}, "SkyAPM.Transport.Grpc.Protocol/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BeA3iXxrAM0pCkq8A9R73+/rLb5k9jS2HjCKZoVn8JdDEnstSe+UxJNsOH7dMigfb0ub2wmWp7GSriioSnVwPw==", "path": "skyapm.transport.grpc.protocol/2.1.0", "hashPath": "skyapm.transport.grpc.protocol.2.1.0.nupkg.sha512"}, "SkyAPM.Utilities.Configuration/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-yw/6IvhEumnxG4mh8BE6mfquFw3bNu3hl8PQwYvFBgJRushBrVv23zSkyBVWK34MGRfQpy7mQHaOH0iOPowQ2Q==", "path": "skyapm.utilities.configuration/2.1.0", "hashPath": "skyapm.utilities.configuration.2.1.0.nupkg.sha512"}, "SkyAPM.Utilities.DependencyInjection/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-m1d80HoMP/oit+X1/0cOHKLHCFHvYCdTbhGkpYnzBszB+ahD37P++1fcmDm1kO+8AbBxmjmzrhkyP+ASz5QCng==", "path": "skyapm.utilities.dependencyinjection/2.1.0", "hashPath": "skyapm.utilities.dependencyinjection.2.1.0.nupkg.sha512"}, "SkyAPM.Utilities.Logging/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-gd15f0FUR7VLHoAr3D/Ns2z/lbyw9qDRuzqhYcyiX0QZGQm/zUpCI6a1djsd7t6XfxmWw7ovfM9xQ/CT9HfMkA==", "path": "skyapm.utilities.logging/2.1.0", "hashPath": "skyapm.utilities.logging.2.1.0.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-h4wIN8i38Crj9jxjmhBzncMyLGHbWPR/NTuQAT4cgiODR7MzHB1wiObyUOtK1QGMhNG1W01GsB2QO6Z6fkOU7Q==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1Wh/JdK/UgaJe9m76/EKTrV0d18sBDvsgh7FCmRB8U0oJYA0pkFJWLchqi9CMYv1RJjc3f4tB0tFzKjlRV5rQ==", "path": "sqlsugarcore.dm/8.8.0", "hashPath": "sqlsugarcore.dm.8.8.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/9.3.7.428": {"type": "package", "serviceable": true, "sha512": "sha512-PPir32f/dH83D5AUvcdwFjbE0/8aEcceXY/aJuhAnVJEe2ibQi2gTpPqsqYdTsqXHoLl7lrml2tVrnZllYa0gQ==", "path": "sqlsugarcore.kdbndp/9.3.7.428", "hashPath": "sqlsugarcore.kdbndp.9.3.7.428.nupkg.sha512"}, "StackExchange.Redis/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-MjAJ0ejH8zLhtuN5+Z+/I07NmPGdVuGEvE2+4xONQoFwgl+7vbQ/A6jlUgH9UkZb4s9Mu9QDyBq1TkRqQcOgTQ==", "path": "stackexchange.redis/2.8.0", "hashPath": "stackexchange.redis.2.8.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-nrkZMoflCvTKj2yckJ1k/tQu53IRGHYkKp7ESiQETkAaUXzn05CYlCl/DY9kBBbFqefHWIwC/9VmYisD44jqlA==", "path": "swashbuckle.aspnetcore/6.7.0", "hashPath": "swashbuckle.aspnetcore.6.7.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-S4pR+265NwY9saAhK+3Jn2oL2Wfy0qvy9871b0KiEPW/iLPRgg2HrkrdMwC1wpjNkRXWGDFRRj+LkUD4lyuAaQ==", "path": "swashbuckle.aspnetcore.filters/8.0.2", "hashPath": "swashbuckle.aspnetcore.filters.8.0.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-RgQ1RbxmofrkBSrLkA/34DWwzz4rKHRwC4vHOvevru2CNqymUPn3kRhbIX29g4yvj2sCff1glf19djnyM9kuUQ==", "path": "swashbuckle.aspnetcore.filters.abstractions/8.0.2", "hashPath": "swashbuckle.aspnetcore.filters.abstractions.8.0.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.Newtonsoft/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y<PERSON>ttaigfjCKEJK3H9zexdcA6d/7gSveYnIXboWuyaCtnQfVT+RfXWCI+t4DYC64DSSI6VjZLM7NxPvbzXoWfIQ==", "path": "swashbuckle.aspnetcore.newtonsoft/6.7.0", "hashPath": "swashbuckle.aspnetcore.newtonsoft.6.7.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-qI1ntGtNnt1Nksi/oDNkmCULVrImHyLWodJhQzghGj9W6uKYMqVl8Y7M2oU8VbHTQZFImD2kYR9ay8LZkJwaqA==", "path": "swashbuckle.aspnetcore.swagger/6.7.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.7.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2eRUBYnMBWzyl0esJfcB4YHuG/boD6nmuDcKdRa+nYPU/57+kbZ3Ot4TGpRDTJcZ0BrWoQiYe5mWRy6whvTkQ==", "path": "swashbuckle.aspnetcore.swaggergen/6.7.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.7.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Oxyf8IcU28J0FlEVpBhywIAMRo1gvOERMyIZ56iOdyLJSkhz4MJRo4MDR++q+gh+R2f2+4KnATUvpU4wHweZJg==", "path": "swashbuckle.aspnetcore.swaggerui/6.7.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.7.0.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Buffers/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "path": "system.buffers/4.6.0", "hashPath": "system.buffers.4.6.0.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "path": "system.configuration.configurationmanager/8.0.0", "hashPath": "system.configuration.configurationmanager.8.0.0.nupkg.sha512"}, "System.Console/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qSKUSOIiYA/a0g5XXdxFcUFmv1hNICBD7QZ0QhGYVipPIhvpiydY8VZqr1thmCXvmn8aipMg64zuanB4eotK9A==", "path": "system.console/4.0.0", "hashPath": "system.console.4.0.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g==", "path": "system.diagnostics.tools/4.0.1", "hashPath": "system.diagnostics.tools.4.0.1.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ndUZlEkAMc1XzM0xGN++SsJrNhRkIHaKI8+te325vrUgoLT1ufWNI6KB8FFrL7NpRMHPrdxP99aF3fHbAPxW0A==", "path": "system.directoryservices.protocols/6.0.1", "hashPath": "system.directoryservices.protocols.6.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AJukBuLoe3QeAF+mfaRKQb2dgyrvt340iMBHYv+VdBzCUM06IxGlvl0o/uPOS7lHnXPN6u8fFRHSHudx5aTi8w==", "path": "system.formats.asn1/8.0.0", "hashPath": "system.formats.asn1.8.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L1c6IqeQ88vuzC1P81JeHmHA8mxq8a18NUBNXnIY/BVb+TCyAaGIFbhpZt60h9FJNmisymoQkHEFSE9Vslja1Q==", "path": "system.globalization.calendars/4.0.1", "hashPath": "system.globalization.calendars.4.0.1.nupkg.sha512"}, "System.Globalization.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KKo23iKeOaIg61SSXwjANN7QYDr/3op3OWGGzDzz7mypx0Za0fZSeG0l6cco8Ntp8YMYkIQcAqlk8yhm5/Uhcg==", "path": "system.globalization.extensions/4.0.1", "hashPath": "system.globalization.extensions.4.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hBQYJzfTbQURF10nLhd+az2NHxsU6MU7AB8RUf4IolBP5lOAm4Luho851xl+CqslmhI5ZH/el8BlngEk4lBkaQ==", "path": "system.io.compression.zipfile/4.0.1", "hashPath": "system.io.compression.zipfile.4.0.1.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-H7WMBX/WNdG38QLwvuD26EcsIjiP6YO4jy6BMjG47fjkjG9mMXfVZIgPxtBRGi5cBDB17AoEY8F05serSsam9Q==", "path": "system.linq.dynamic.core/1.4.4", "hashPath": "system.linq.dynamic.core.1.4.4.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULq9g3SOPVuupt+Y3U+A37coXzdNisB1neFCSKzBwo182u0RDddKJF8I5+HfyXqK6OhJPgeoAwWXrbiUXuRDsg==", "path": "system.net.http/4.1.0", "hashPath": "system.net.http.4.1.0.nupkg.sha512"}, "System.Net.Http.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GbIV4y344kGOKjshAKCIDCMUTTW/hyUC42wV0Y5SXEdIbaKBIHBUxZ2MOe4/ZiV2svUAGfQ0c8LGtUExpOI8tg==", "path": "system.net.http.json/6.0.0", "hashPath": "system.net.http.json.6.0.0.nupkg.sha512"}, "System.Net.Primitives/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-hVvfl4405DRjA2408luZekbPhplJK03j2Y2lSfMlny7GHXlkByw1iLnc9mgKW0GdQn73vvMcWrWewAhylXA4Nw==", "path": "system.net.primitives/4.0.11", "hashPath": "system.net.primitives.4.0.11.nupkg.sha512"}, "System.Net.Sockets/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-xAz0N3dAV/aR/9g8r0Y5oEqU1JRsz29F5EGb/WVHmX3jVSLqi2/92M5hTad2aNWovruXrJpJtgZ9fccPMG9uSw==", "path": "system.net.sockets/4.1.0", "hashPath": "system.net.sockets.4.1.0.nupkg.sha512"}, "System.Net.WebSockets.WebSocketProtocol/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-cVTT/Zw4JuUeX8H0tdWii0OMHsA5MY2PaFYOq/Hstw0jk479jZ+f8baCicWFNzJlCPWAe0uoNCELoB5eNmaMqA==", "path": "system.net.websockets.websocketprotocol/5.1.0", "hashPath": "system.net.websockets.websocketprotocol.5.1.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-tc2ZyJgweHCLci5oQGuhQn9TD0Ii9DReXkHtZm3aAGp8xe40rpRjiTbMXOtZU+fr0BOQ46goE9+qIqRGjR9wGg==", "path": "system.reflection.metadata/1.4.1", "hashPath": "system.reflection.metadata.1.4.1.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "path": "system.runtime.caching/8.0.0", "hashPath": "system.runtime.caching.8.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Numerics/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+XbKFuzdmLP3d1o9pdHu2nxjNr2OEPqGzKeegPLCUMM71a0t50A/rOcIRmGs9wR7a8KuHX6hYs/7/TymIGLNqg==", "path": "system.runtime.numerics/4.0.1", "hashPath": "system.runtime.numerics.4.0.1.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-8JQFxbLVdrtIOKMDN38Fn0GWnqYZw/oMlwOUG/qz1jqChvyZlnUmu+0s7wLx7JYua/nAXoESpHA3iw11QFWhXg==", "path": "system.security.cryptography.algorithms/4.2.0", "hashPath": "system.security.cryptography.algorithms.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cUJ2h+ZvONDe28Szw3st5dOHdjndhJzQ2WObDEXAWRPEQBtVItVoxbXM/OEsTthl3cNn2dk2k0I3y45igCQcLw==", "path": "system.security.cryptography.cng/4.2.0", "hashPath": "system.security.cryptography.cng.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/i1Usuo4PgAqgbPNC0NjbO3jPW//BoBlTpcWFD1EHVbidH21y4c1ap5bbEMSGAXjAShhMH4abi/K8fILrnu4BQ==", "path": "system.security.cryptography.csp/4.0.0", "hashPath": "system.security.cryptography.csp.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FbKgE5MbxSQMPcSVRgwM6bXN3GtyAh04NkV8E5zKCBE26X0vYW0UtTa2FIgkH33WVqBVxRgxljlVYumWtU+HcQ==", "path": "system.security.cryptography.encoding/4.0.0", "hashPath": "system.security.cryptography.encoding.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HUG/zNUJwEiLkoURDixzkzZdB5yGA5pQhDP93ArOpDPQMteURIGERRNzzoJlmTreLBWr5lkFSjjMSk8ySEpQMw==", "path": "system.security.cryptography.openssl/4.0.0", "hashPath": "system.security.cryptography.openssl.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULmp3xoOwNYjOYp4JZ2NK/6NdTgiN1GQXzVVN1njQ7LOZ0d0B9vyMnhyqbIi9Qw4JXj1JgCsitkTShboHRx7Eg==", "path": "system.security.cryptography.pkcs/8.0.0", "hashPath": "system.security.cryptography.pkcs.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wkd7QryWYjkQclX0bngpntW5HSlMzeJU24UaLJQ7YTfI8ydAVAaU2J+HXLLABOVJlKTVvAeL0Aj39VeTe7L+oA==", "path": "system.security.cryptography.primitives/4.0.0", "hashPath": "system.security.cryptography.primitives.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4HEfsQIKAhA1+ApNn729Gi09zh+lYWwyIuViihoMDWp1vQnEkL2ct7mAbhBlLYm+x/L4Rr/pyGge1lIY635e0w==", "path": "system.security.cryptography.x509certificates/4.1.0", "hashPath": "system.security.cryptography.x509certificates.4.1.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "path": "system.text.json/8.0.4", "hashPath": "system.text.json.8.0.4.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-saGfUV8uqVW6LeURiqxcGhZ24PzuRNaUBtbhVeuUAvky1naH395A/1nY0P2bWvrw/BreRtIB/EzTDkGBpqCwEw==", "path": "system.threading.timer/4.0.1", "hashPath": "system.threading.timer.4.0.1.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZIiLPsf67YZ9zgr31vzrFaYQqxRPX9cVHjtPSnmx4eN6lbS/yEyYNr2vs1doGDEscF0tjCZFsk9yUg1sC9e8tg==", "path": "system.xml.readerwriter/4.0.11", "hashPath": "system.xml.readerwriter.4.0.11.nupkg.sha512"}, "System.Xml.XDocument/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg==", "path": "system.xml.xdocument/4.0.11", "hashPath": "system.xml.xdocument.4.0.11.nupkg.sha512"}, "Yitter.IdGenerator/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4nOJ7Geq41vgNWX9E6/vkxRzFInACGpDp4Kad2mA2WIKhEwgPyE9FpulBAuEmDByrfHHz6mOII3IIeLJAh91g==", "path": "yitter.idgenerator/1.0.14", "hashPath": "yitter.idgenerator.1.0.14.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "Hyun.Core.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.EventBus/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.Extensions/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.IServices/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.Model/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.Repository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.Serilog/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.Serilog.Es/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hyun.Core.Tasks/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Ocelot.Provider.Nacos/1.2.1": {"type": "project", "serviceable": false, "sha512": ""}, "OpenIddict.SqlSugar/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "OpenIddict.SqlSugar.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}