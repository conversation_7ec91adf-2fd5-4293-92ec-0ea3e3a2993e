﻿namespace Hyun.Core.Model
{

    ///<summary>
    ///校服采购
    ///</summary>
    public class XUniformBuyDto : BaseEntity
    {

        public XUniformBuyDto()
        {

        }

        /// <summary>
        ///学校Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///区县Id
        /// </summary>
        public long CountyId { get; set; }

        /// <summary>
        ///年度
        /// </summary>
        public int PlanYear { get; set; }

        /// <summary>
        ///采购批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        ///校服数量
        /// </summary>
        public int Num { get; set; } = 0;

        /// <summary>
        ///预算金额(元)
        /// </summary>
        public decimal BudgetAmount { get; set; } = 0;

        /// <summary>
        ///是否需要招标（1：是 2：否）
        /// </summary>
        public int IsNeedBidding { get; set; } = 0;

        /// <summary>
        ///组织形式（对应字典表DicValue值）
        /// </summary>
        public int Organizational { get; set; } = 0;

        /// <summary>
        ///组织形式名称
        /// </summary>
        public string OrganizationalName { get; set; }

        /// <summary>
        ///招标公告公开（1：已公开 2：未公开）
        /// </summary>
        public int BidPublic { get; set; } = 0;

        /// <summary>
        ///采购方式（对应字典表DicValue值）
        /// </summary>
        public int Method { get; set; } = 0;

        /// <summary>
        ///采购方式名称
        /// </summary>
        public string MethodName { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string AreaName { get; set; }

        /// <summary>
        ///公开日期
        /// </summary>
        public DateTime? PublicDate { get; set; }

        /// <summary>
        ///公开媒体名称
        /// </summary>
        public string PublicMediaName { get; set; }

        /// <summary>
        ///是否备查
        /// </summary>
        public int? IsFiling { get; set; }

        /// <summary>
        ///采购申请状态（0：待备案  10：待审核  100：已备案）
        /// </summary>
        public int PurchaseStatuz { get; set; } = 0;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 备案说明
        /// </summary>
        public string FilingExplanation { get; set; }

        /// <summary>
        /// 采购需求编制日期
        /// </summary>
        public DateTime? PreparationDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 采购申请状态名称
        /// </summary>
        public string PurchaseStatuzName { get; set; }

        /// <summary>
        /// 选用表Id
        /// </summary>
        public long UniformSchemeId { get; set; }


        /// <summary>
        /// 附件Id集合
        /// </summary>
        public List<long> ListAttachmentId { get; set; }=new List<long>();

        /// <summary>
        /// 按钮类型（0：保存，1：提交）
        /// </summary>
        public int ButtonType { get; set; } = 0;


        /// <summary>
        /// 附件Id
        /// </summary>
        public long AttachmentId { get; set; }


    }

    /// <summary>
    /// 审核实体
    /// </summary>
    public class BuyAuditModel
    {
        /// <summary>
        /// 申请ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 1：通过，2：不通过
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        /// 原因
        /// </summary>
        public string FilingExplanation { get; set; }
    }

    /// <summary>
    /// 撤销、退回实体
    /// </summary>
    public class BuyRevokeModel
    {
        /// <summary>
        /// 申请ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 1：撤销，2：退回
        /// </summary>
        public int OptType { get; set; }

        /// <summary>
        /// 原因
        /// </summary>
        public string FilingExplanation { get; set; }
    }
}

