﻿using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models;
using Hyun.Old.Util;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.Util;
using System.Collections.Generic;
using System.Data;
using System.Linq.Expressions;
using System.Reflection.Metadata;
using System.Text;

namespace Hyun.Core.Api
{

    [Route("api/hyun/baddress")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class BAddressController : BaseApiController
    {
        StringBuilder result = new StringBuilder();
        StringBuilder sb = new StringBuilder();
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IVAddressListServices vAddressListManager;
        private readonly IBAddressServices addressManager;
        private readonly IVAddressPlacePropertyServices vAddressPlacePropertyManager;
        //private readonly IAAddressPlaceUserServices addressPlaceUserManager;
        private readonly IVAddressPlaceServices vAddressPlaceManager;
        private readonly IUser user;
        private readonly IBAttachmentDataServices attachmentDataManager;



        public BAddressController(IMapper _mapper, IWebHostEnvironment _env, IVAddressListServices _vAddressListManager, IBAddressServices _addressManager, IVAddressPlacePropertyServices _vAddressPlacePropertyManager, /*IAAddressPlaceUserServices _addressPlaceUserManager,*/ IVAddressPlaceServices _vAddressPlaceManager, IUser _user, IBAttachmentDataServices _attachmentDataManager)
        {
            mapper = _mapper;
            env = _env;
            vAddressListManager = _vAddressListManager;
            addressManager = _addressManager;
            vAddressPlacePropertyManager = _vAddressPlacePropertyManager;
            //addressPlaceUserManager = _addressPlaceUserManager;
            vAddressPlaceManager = _vAddressPlaceManager;
            user = _user;
            attachmentDataManager = _attachmentDataManager;
        }


        /// <summary>
        /// 获取地址信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("addressfind")]
        //<used>1</used>
        public async Task<Result> Address_Find([FromBody] VAddressListParam param)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            var expression = LinqExtensions.True<BAddress>();
            expression = expression.AndNew(f => f.IsDeleted == false && f.UnitId == user.UnitId && f.Statuz == 1);
            var listAddress = await addressManager.Query(expression);
            listAddress = listAddress.OrderBy(f => f.Sort).ToList();
            var treeData = BuildAddressTree(listAddress);
            r.data.rows = treeData;
            if (param.isFirst)
            {
                List<BAddress> list = await addressManager.Query(f => f.IsDeleted == false && f.Pid == 0 && f.Statuz == 1);
                r.data.other = new { listParentAddress = list };
            }
            return r;
        }

        


        /// <summary>
        /// 根据地址Id获取地址信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("addressgetbyid")]
        //<used>1</used>
        public async Task<Result> Address_GetById(long Id)
        {
            Result r = new Result();
            BAddress p =await addressManager.GetById(Id);
            r.flag = 1;
            r.msg = "";
            r.data.total = 1;
            r.data.rows = mapper.Map<BAddressDto>(p);

            return r;
        }

        /// <summary>
        /// 添加修改地址信息(单位)
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("addressinsertupdate")]
        //<used>1</used>
        public async Task<Result> Address_InsertUpdate([FromBody] BAddressDto o)
        {
            Result r = new Result();
            BAddress add = mapper.Map<BAddress>(o);
            r =await addressManager.InsertUpdate(o.ManagerUserId, o.DepartmentId, user.UnitId, o.Pid, o.Name, o.Code, o.Address, o.Memo, o.Depth, o.Sort, o.Path, o.Id);
            return r;
        }


        /// <summary>
        /// 删除地址信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("addressdelbatch")]
        //<used>1</used>
        public async Task<Result> Address_DelBatch(string ids)
        {
            Result r = new Result();
            r = await addressManager.DeleteByIds(ids, user.UnitId);
            return r;
        }


        /// <summary>
        /// 批量设置部门信息
        /// </summary>
        /// <param name="strAddress"></param>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("addressdepartmentuserbatchset")]
        //<used>1</used>
        public async Task<Result> AddressDepartment_UserBatchSet(string strAddress, long departmentId)
        {
            Result r = new Result();
            try
            {
                List<long> listId = strAddress.Split(',').Select(long.Parse).ToList();

                List<BAddress> listAddress = await addressManager.Find(f => f.UnitId == user.UnitId && f.Pid > 0 && listId.Contains(f.Id));
                if (listAddress.Count > 0)
                {
                    listAddress.ForEach(a => a.DepartmentId = departmentId);
                    await addressManager.Update(listAddress);
                    r.flag = 1;
                    r.msg = "批量设置部门成功";
                }
            }
            catch (Exception ex)
            {
                r.flag = 0;
                r.msg = "批量设置部门失败！失败原因：" + ex.Message;
            }
            return r;
        }


        /// <summary>
        /// 批量设置地址管理人
        /// </summary>
        /// <param name="strAddress"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("addressuserbatchset")]
        //<used>1</used>
        public async Task<Result> AddressUser_BatchSet(string strAddress, long userId)
        {
            Result r = new Result();
            try
            {
                List<long> listId = strAddress.Split(',').Select(long.Parse).ToList();

                List<BAddress> listAddress = await addressManager.Find(f => f.UnitId == user.UnitId && f.Pid > 0 && listId.Contains(f.Id));
                if (listAddress.Count > 0)
                {
                    listAddress.ForEach(a => a.ManagerUserId = userId);
                    await addressManager.Update(listAddress);
                    r.flag = 1;
                    r.msg = "批量设置部门成功";
                }
            }
            catch (Exception ex)
            {
                r.flag = 0;
                r.msg = "批量设置部门失败！失败原因：" + ex.Message;
            }
            return r;
        }

        /// <summary>
        /// 地址管理：地址导入
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("uploadaddressfile")]
        public async Task<Result> UploadAddressFile()
        {
            Result r = new Result();
            var files = await Request.ReadFormAsync();
            r = FileHelper.UploadExecl(env.WebRootPath, files);
            if (r.flag == 1)
            {
                DataTable sheet = r.data.rows as DataTable;
                BAttachmentData attach = r.data.other as BAttachmentData;
                await attachmentDataManager.Add(attach);

                //拷贝以前的装备平台导入代码
                string field = ApplicationConfig.ImportAddress;
                if (string.IsNullOrEmpty(field))
                {
                    r.flag = 0;
                    r.msg = "配置文件中未配置“hyun:Import.Address”";
                    return r;
                }
                r = ExeclHelp.ValidateFile(field, sheet);
                if (r.flag == 1)
                {
                    long outPid = 0;
                    string tempBuilding = "";
                    List<VAddress> vos = new List<VAddress>();//导入成功的信息
                    List<VAddress> pos = new List<VAddress>();//导入失败的信息
                                                                //整理数据
                    for (int i = 1; i < sheet.Rows.Count; i++)
                    {
                        DataRow dr = sheet.Rows[i];
                        VAddress vo = new VAddress();
                        //0   1    2   3   4   5 
                        // 楼宇名称|楼宇别名|楼宇备注|房间名称|房间别名|房间备注
                        string Building = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 0));
                        long id = 0;
                        string msg = "";

                        if (!string.IsNullOrEmpty(Building))
                        {
                            if (tempBuilding != Building)
                            {
                                List<BAddress> list =await addressManager.Find(f=>f.Depth == 0 && f.UnitId == user.UnitId && f.Name == Building);
                                if (list.Count > 0)
                                {
                                    outPid = list[0].Id;
                                    if (list[0].Statuz != 1)
                                    {
                                        list[0].Statuz = 1;
                                        await addressManager.Update(list[0]);
                                    }
                                }
                                else
                                {
                                    long addressId = 0;
                                    vo.Name = Building;
                                    vo.Code = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 1));
                                    vo.Memo = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 2));
                                    vo.Pid = 0;
                                    vo.Depth = 0;
                                    vo.Sort = 0;
                                    vo.Pid = 0;
                                    vo.Path = "";
                                    vo.UnitId = user.UnitId;
                                    r = await addressManager.InsertUpdate(0, 0, vo.UnitId, vo.Pid, vo.Name, vo.Code, vo.Address, vo.Memo, vo.Depth, vo.Sort, vo.Path, addressId);
                                    long.TryParse(r.data.rows.ToString(), out addressId);
                                    if (addressId > 0)
                                    {
                                        outPid = addressId;
                                    }
                                    else
                                    {
                                        r.flag = 0;
                                        msg = "楼宇添加失败";
                                        vo.Memo = msg;
                                        pos.Add(vo);
                                        continue;
                                    }
                                }
                                tempBuilding = Building;
                            }

                            vo.Pid = outPid;

                            string stallName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                            if (!string.IsNullOrEmpty(stallName))
                            {
                                vo.Name = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                                vo.Code = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 4));
                                vo.Memo = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 5));
                                vo.Path = "";
                                vo.UnitId = user.UnitId;
                                vo.Pid = outPid;
                                vo.Depth = 1;
                                vo.Sort = 0;
                                await addressManager.InsertUpdate(0, 0, vo.UnitId, vo.Pid, vo.Name, vo.Code, vo.Address, vo.Memo, vo.Depth, vo.Sort, vo.Path, id);
                                vo.Address = tempBuilding;
                            }
                            else
                            {
                                vo.Address = tempBuilding;
                                vo.Name = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                                vo.Code = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 4));
                                r.flag = 0;
                                msg = "场所(室)名称不能为空";
                                vo.Memo = msg;
                                pos.Add(vo);
                                continue;
                            }
                            //重复性校验
                            if (!string.IsNullOrEmpty(msg))
                            {
                                vo.Memo = msg;
                                pos.Add(vo);
                                continue;
                            }
                        }
                        else
                        {
                            vo.Name = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                            vo.Code = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 4));
                            r.flag = 0;
                            msg = "楼宇场馆不能为空";
                            vo.Memo = msg;
                            pos.Add(vo);
                            continue;
                        }
                        vos.Add(vo);
                    }
                    r.data.rows = vos;
                    r.data.footer = pos;
                    r.msg = $"信息导入成功 {vos.Count}条，导入失败 {pos.Count}条";
                }
            }
            return r;
        }
        /// <summary>
        /// 地址管理：地址导出（本单位）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("addressexport")]
        //<used>1</used>
        public async Task<Result> AddressExport([FromBody] VAddressListParam param)
        {
            Result r = new Result();
            List<BAddress> list = await addressManager.SearchAddressList(user.UnitId);
            if (list.Count > 0)
            {
                IWorkbook iwork = new HSSFWorkbook();
                ISheet isheet = iwork.CreateSheet("场所地点信息");
                IRow row = null;
                ICell cell = null;
                IFont font = iwork.CreateFont();
                font.IsBold = true;
                IFont font1 = iwork.CreateFont();
                IFont font2 = iwork.CreateFont();

                IFont font3 = iwork.CreateFont();
                font3.IsBold = true;
                font3.Color = IndexedColors.Red.Index;

                ICellStyle cellstyle = iwork.CreateCellStyle();
                cellstyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle.SetFont(font);
                cellstyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;

                ICellStyle cellRed = iwork.CreateCellStyle();
                cellRed.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellRed.SetFont(font3);
                cellRed.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellRed.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRed.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRed.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRed.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;


                ICellStyle cellstyle1 = iwork.CreateCellStyle();
                cellstyle1.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellstyle1.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle1.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;


                ICellStyle cellstyle2 = iwork.CreateCellStyle();
                cellstyle2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
                cellstyle2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle2.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;

                ICellStyle cellstyle0 = iwork.CreateCellStyle();
                cellstyle0.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle0.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle0.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle0.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle0.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle0.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;

                ICellStyle cellstyle3 = iwork.CreateCellStyle();
                cellstyle3.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                font1.IsBold = true;
                font1.FontHeightInPoints = 13;
                cellstyle3.SetFont(font1);
                cellstyle3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

                ICellStyle cellstyle4 = iwork.CreateCellStyle();
                cellstyle4.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
                font2.IsBold = false;
                font2.FontHeightInPoints = 9;
                cellstyle4.SetFont(font2);
                cellstyle4.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


                ICellStyle cellstyle5 = iwork.CreateCellStyle();
                cellstyle5.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle5.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                font1.IsBold = true;
                font1.FontHeightInPoints = 13;
                cellstyle5.SetFont(font1);

                int rowIndex = 0;
                row = isheet.CreateRow(rowIndex);
                row.HeightInPoints = 22;
                row.Height = 600;

                int cellIndex = 0;
                cell = row.CreateCell(cellIndex);
                cell.SetCellValue("楼宇场馆名称");
                isheet.SetColumnWidth(cellIndex, 6500);
                cell.CellStyle = cellstyle;
                cellIndex++;


                cell = row.CreateCell(cellIndex);
                cell.SetCellValue("场所(室)名称");
                isheet.SetColumnWidth(cellIndex, 6500);
                cell.CellStyle = cellstyle;
                cellIndex++;


                foreach (BAddress detail in list)
                {
                    rowIndex++;
                    row = isheet.CreateRow(rowIndex);
                    row.HeightInPoints = 22;
                    row.Height = 30 * 20;

                    cellIndex = 0;
                    cell = row.CreateCell(cellIndex);
                    cell.SetCellValue(detail.Address);
                    cell.CellStyle = cellstyle0;
                    cellIndex++;

                    cell = row.CreateCell(cellIndex);
                    cell.SetCellValue(detail.Name);
                    cell.CellStyle = cellstyle0;
                    cellIndex++;
                }

                string exlName = user.UnitName + "-场所地点信息";
                var fileInfo = FileHelper.GetSaveFileInfo(env.ContentRootPath, exlName);
                using (MemoryStream ms = new MemoryStream())
                {
                    //将工作簿的内容放到内存流中
                    iwork.Write(ms);
                    iwork.Close();
                    ms.Flush();
                    ms.Position = 0;
                    using (FileStream fs = new FileStream(fileInfo.filePath, FileMode.Create, FileAccess.Write))
                    {
                        byte[] data = ms.ToArray();
                        fs.Write(data, 0, data.Length);
                        fs.Flush();
                    }
                }
                r.flag = 1;
                r.data.rows = new { fileInfo.url, fileInfo.fileName };
                return r;

            }
            r.flag = 0;
            r.msg = "没有需要导出的数据！";
            return r;
        }



        #region 私有方法
        /// <summary>
        /// 地址树信息
        /// </summary>
        /// <param name="flatList"></param>
        /// <returns></returns>
        private List<BAddress> BuildAddressTree(List<BAddress> flatList)
        {
            var lookup = flatList.ToDictionary(n => n.Id);
            var rootNodes = new List<BAddress>();

            foreach (var node in flatList)
            {
                if (node.Pid == 0)
                {
                    // 这是根节点，没有父节点
                    rootNodes.Add(node);
                }
                else if (lookup.ContainsKey(node.Pid))
                {
                    // 将当前节点添加到其父节点的子节点列表中
                    lookup[node.Pid].children.Add(node);
                }
                // 如果PID在列表中不存在，则忽略该节点或进行其他处理
            }

            return rootNodes;
        }
        #endregion

    }
}
