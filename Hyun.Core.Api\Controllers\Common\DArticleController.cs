﻿using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Services;
using Hyun.Old.Util;
using Microsoft.AspNetCore.StaticFiles;

namespace Hyun.Core.Api
{

    [Route("api/hyun/darticle")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class DArticleController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly ISysUserExtensionServices sysUserExtensionManager;
        private readonly IPUnitServices unitManager;
        private readonly IBAreaServices areaManager;
        private readonly IDArticleServices articleManager;
        private readonly IDArticleImageServices articleImageManager;
        private readonly IDArticleExtendServices articleExtendManager;
        private readonly IDArticleCategoryServices articleCategoryManager;
        private readonly IVArticleServices vArticleManager;
        private readonly IUser user;
        private readonly IVCategoryArticleListServices vcategoryArticleListManager;
        //private readonly IVUserDetailServices vUserDetailManager;
        private readonly IVArticleCategoryLastStageServices varticleCategoryLastStageManager;
        private readonly IVArticleCategoryServices varticleCategoryManager;
    
        private readonly IBUserActionLogServices userActionLogManager;
        //private readonly IPUserLoginAgreementBjServices userLoginAgreementManager;
        public DArticleController(IMapper _mapper, IWebHostEnvironment _env, ISysUserExtensionServices _sysUserExtensionManager, IPUnitServices _unitManager, IBAreaServices _areaManager, IDArticleServices _articleManager, IDArticleImageServices _articleImageManager, IDArticleExtendServices _articleExtendManager, IDArticleCategoryServices _articleCategoryManager, IVArticleServices _vArticleManager, IUser _user, IVCategoryArticleListServices _vcategoryArticleListManager,
            IVArticleCategoryLastStageServices _varticleCategoryLastStageManager, IVArticleCategoryServices _varticleCategoryManager, IVUserDetailServices _vUserDetailManager, IBUserActionLogServices _userActionLogManager/*,IPUserLoginAgreementBjServices _userLoginAgreementManager*/)
        {
            mapper = _mapper;
            env = _env;
            sysUserExtensionManager = _sysUserExtensionManager;
            unitManager = _unitManager;
            areaManager = _areaManager;
            articleManager = _articleManager;
            articleImageManager = _articleImageManager;
            articleExtendManager = _articleExtendManager;
            articleCategoryManager = _articleCategoryManager;
            vArticleManager = _vArticleManager;
            user = _user;
            vcategoryArticleListManager = _vcategoryArticleListManager;           
            varticleCategoryLastStageManager = _varticleCategoryLastStageManager;
            varticleCategoryManager = _varticleCategoryManager;
            //vUserDetailManager = _vUserDetailManager;
            userActionLogManager = _userActionLogManager;
            //userLoginAgreementManager = _userLoginAgreementManager;
        }

        /// <summary>
        /// 获取资讯列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlefindperson")]
        //<used>0</used>
        public async Task<Result> Article_FindPerson([FromBody] DArticleParam param)
        {
            Result r = new Result();
            PageModel<DArticle> pg = await articleManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<DArticleDto>>(pg.data);
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        
        /// <summary>
        /// 根据id获取获取资讯详细信息 (查看专用) 
        /// </summary>
        /// <param name="Id">资讯编号</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlegetbyid")]
        //<used>1</used>
        public async Task<Result> Article_GetById(long Id)
        {
            Result r = new Result();
            if (user != null)
            {
                DArticle p = new DArticle();

                p = await articleManager.QueryById(Id);
                if (p != null)
                {
                    p.Hits = p.Hits + 1;
                    await articleManager.Update(p);
                }
                r.flag = 1;
                r.msg = "";
                r.data.total = 1;
                r.data.rows = mapper.Map<DArticleDto>(p); //p;
            }
            else
            {
                r.flag = 0;
                r.msg = "用户尚未登录";
            }
            return r;
        }

        /// <summary>
        /// 获取资讯以及发布人的信息
        /// </summary>
        /// <param name="Id">资讯id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlegetpersonbyid")]
        //<used>1</used>
        public async Task<Result> Article_GetPersonById(long Id)
        {
            Result r = new Result();

            DArticle p = new DArticle();
            int total = 1;
            p = await articleManager.GetById(Id);

            if (p != null)
            {
                r.flag = 1;
                if (user.AdministratorType != 0 && p.UnitId != user.UnitId)
                {
                    p = null;
                }
                else
                {
                    getPublishInfo(r, p);
                }
            }
            else
            {
                r.flag = 0;
            }

            r.data.total = total;
            r.data.rows = p;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 获取发布人信息
        /// </summary>
        /// <param name="r"></param>
        /// <param name="p"></param>
        private async void getPublishInfo(Result r, DArticle p)
        {
            string UnitName = "", CatalogName = "", UnitTel = "";

            var userInfo = await sysUserExtensionManager.QueryById(p.UserId);
            if (userInfo != null && userInfo.Id > 0)
            {              
                UnitTel = userInfo.Mobile;
                var unitInfo = await unitManager.QueryById(userInfo.UnitId);
                if (unitInfo != null && unitInfo.Id > 0)
                {
                    UnitName = unitInfo.Name;
                }                
            }
            var catalog = articleCategoryManager.GetById(p.Cid);
            if (catalog != null)
            {
                CatalogName = catalog.Result.Name;
            }
            r.data.footer = (new { UnitName = UnitName, UnitTel = UnitTel, CatalogName = CatalogName, CatalogId = p.Cid });
        }

        /// <summary>
        /// 资讯推荐,取消推荐 
        /// </summary>
        /// <param name="model">资讯实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlerecommend")]
        //<used>1</used>
        public async Task<Result> Article_Recommend([FromBody] DArticleDto model)
        {
            //int id, bool isRecommend
            Result r = new Result();
            if (!(user.IsSystemUser || user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin) || user.Roles.Contains(RoleTypes.SchoolAdmin) || user.Roles.Contains(RoleTypes.CompanyAdmin))) //是否是管理员（系统管理员、市级管理员、区县管理员、单位管理员、企业管理员）
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }

            DArticle p = new DArticle();
            p = await articleManager.GetById(model.Id);
            string strJson = string.Format("【id:{0},isRecommend:{1}】", model.Id, model.IsRecommend);
            if (p != null)
            {
                if (user.AdministratorType != 0 && p.UnitId != user.UnitId)
                {
                    r.flag = 0;
                    r.msg = "非法操作，您无权操作此功能";
                    return r;
                }
                p.IsRecommend = model.IsRecommend;
                if (await articleManager.Update(p))
                {
                    r.flag = 1;
                    r.msg = "操作成功";
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】资讯推荐{2}审核成功！", user.UnitName, user.UserName, model.Id), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
                else
                {
                    r.flag = 0;
                    r.msg = "操作成功";
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】资讯推荐{2}审核失败！", user.UnitName, user.UserName, model.Id), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "数据不存在，操作失败";
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】资讯推荐{2}审核失败！", user.UnitName, user.UserName, model.Id), ApplicationConfig.CommonLogFilePath + "Article\\");
            }

            return r;
        }

        /// <summary>
        /// 批量删除资讯
        /// </summary>
        /// <param name="ids">资讯Id，逗号分隔</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articledelbatch")]
        //<used>1</used>
        public async Task<Result> Article_DelBatch(string ids)
        {
            Result r = new Result();
            if (!(user.IsSystemUser || user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin) || user.Roles.Contains(RoleTypes.SchoolAdmin) || user.Roles.Contains(RoleTypes.CompanyAdmin))) //是否是管理员（系统管理员、市级管理员、区县管理员、单位管理员、企业管理员）
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }

            //删除增加判断，不是超管账号验证只有本单位才能删除本单位资讯
            if (user.AdministratorType != 0) //系统超管
            {
                var list = await articleManager.SearchArticleList(ids, user.UnitId);
                if (list.Count > 0)
                {
                    r.flag = 0;
                    r.msg = "非法操作，您无权删除其他单位资讯信息";
                    return r;
                }
            }

            if (ids.Length > 0)
            {
                var result = await articleManager.DeleteByIds(ids);
                if (result.flag == 1)
                {
                    r.flag = 1;
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】删除资讯ID【{2}】成功！", user.UnitName, user.UserName, ids), ApplicationConfig.CommonLogFilePath + "Article_DelBatch\\");
                }
                else
                {
                    r.flag = 0;
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】删除资讯ID【{2}】失败！", user.UnitName, user.UserName, ids), ApplicationConfig.CommonLogFilePath + "Article_DelBatch\\");
                }
            }
            r.msg = "";
            return r;
        }


        /// <summary>
        /// 获取资讯图片
        /// </summary>
        /// <param name="articleId">资讯id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articleimagegetbyarticleid")]
        //<used>1</used>
        public async Task<Result> ArticleImage_GetByArticleId(long articleId)
        {
            Result r = new Result();

            List<DArticleImage> list = new List<DArticleImage>();
            list = (await articleImageManager.GetByArticleId(articleId)).OrderBy(t => t.ShowOrder).ToList();
            r.flag = 1;
            r.msg = "";
            r.data.total = list.Count;
            r.data.rows = list.Select(t => new { t.Id, t.ImageSrc, t.ImageTitle, t.ImageDesc, t.ImageType, t.ShowOrder, t.ArticleId });
            return r;
        }

        /// <summary>
        /// 获取资讯扩展属性
        /// </summary>
        /// <param name="articleId">资讯id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articleextendgetbyarticleid")]
        //<used>1</used>
        public async Task<Result> ArticleExtend_GetByArticleId(long articleId)
        {
            Result r = new Result();
            List<DArticleExtend> list = new List<DArticleExtend>();
            list = (await articleExtendManager.GetByArticleId(articleId)).OrderBy(t => t.ShowOrder).ToList();
            r.flag = 1;
            r.msg = "";
            r.data.total = list.Count;
            r.data.rows = list.Select(t => new { t.Id, t.Code, t.Name, t.Info, t.ShowOrder });
            return r;
        }

        /// <summary>
        /// 查看资讯分类列表
        /// </summary>
        /// <param name="param">资讯分类查询实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("darticlecategorylistfind")]
        //<used>1</used>
        public async Task<Result> DArticleCategoryList_Find([FromBody] VArticleCategoryParam param)
        {
            Result r = new Result();
            var list = await varticleCategoryManager.GetPaged(param);
            if (list.data.Count > 0)
            {
                r.data.rows = list.data.Select(t => new { t.Id, t.Pid, t.Name, t.Depth, t.Path, t.Sort, t.Icon1, t.Icon2, t.ConfigCode, t.CateType, t.UserId, t.UnitTypeId, t.ParentName });
            }
            r.data.total = list.data.Count;
            r.flag = 1;
            r.msg = "查询成功。";

            return r;
        }

        /// <summary>
        /// 获取资讯分类实体对象
        /// </summary>
        /// <param name="id">分类Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("darticlecategorylistgetbyid")]
        //<used>1</used>
        public async Task<Result> DArticleCategoryList_GetById(long id)
        {
            Result r = new Result();
            var entity = await articleCategoryManager.GetById(id);
            if (entity != null)
            {
                r.msg = "获取成功。";
                r.flag = 1;
                r.data.rows = new { entity.Id, entity.Pid, entity.Name, entity.Sort, entity.Depth, entity.Path, entity.Icon1, entity.Icon2, entity.ConfigCode, entity.CateType, entity.UnitTypeId };
            }
            else
            {
                r.flag = 0;
                r.msg = "当前资讯分类已不存在。";
            }
            r.flag = 1;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 资讯分类 添加/修改
        /// </summary>
        /// <param name="model">分类对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("darticlecategorylistinsertupdate")]
        //<used>1</used>
        public async Task<Result> DArticleCategoryList_InsertUpdate([FromBody] DArticleCategoryDto model)
        {
            Result r = new Result();
            var o = mapper.Map<DArticleCategory>(model);
            //校验编码不能重复
            if (o.Name == null || o.Name.Length == 0)
            {
                r.flag = 0;
                r.msg = "请填写资讯分类名称。";
                return r;
            }
            if (o.Name.Length > 127)
            {
                r.flag = 0;
                r.msg = "资讯分类名称超长，请控制在120字符内";
                return r;
            }
            if (o.ConfigCode == null)
            {
                o.ConfigCode = "";
            }
            if (o.ConfigCode.Length > 15)
            {
                r.flag = 0;
                r.msg = "资讯分类编码超长，请控制在15字符内";
                return r;
            }
            DArticleCategoryParam acParam = new DArticleCategoryParam();
            acParam.Name = o.Name;
            if (o.ConfigCode != null && o.ConfigCode.Length > 0)
            {
                acParam.Name = o.Name;
                acParam.ConfigCodeOr = o.ConfigCode;
            }
            DArticleCategory entity = null;

            if (o.Id > 0)
            {
                entity = await articleCategoryManager.GetById(o.Id);
                if (entity == null)
                {
                    r.flag = 0;
                    r.msg = "修改失败，当前资讯分类已不存在。";
                    return r;
                }
                //编码一经录入，则不能修改，但是如果以前未录入，可以补录
                if (entity.ConfigCode != null && entity.ConfigCode.Length > 0)
                {
                    if (entity.ConfigCode != o.ConfigCode)
                    {
                        r.flag = 0;
                        r.msg = "编码一经录入，则不能修改。";
                        return r;
                    }
                }

                if (entity.Pid != o.Pid)
                {
                    r.flag = 0;
                    r.msg = "父级分类一经配置，则不能修改。";
                    return r;
                }

                acParam.Idnt = o.Id;
            }
            acParam.Pid = o.Pid;
            var list = await articleCategoryManager.GetPaged(acParam);
            if (list != null && list.data.Count > 0)
            {
                r.flag = 0;
                r.msg = "分类编码或者分类名称已存在，请修改。";
                return r;
            }

            //校验：如果父级存在单位，则子集必须是相同单位类型的。
            if (o.Pid > 0)
            {
                o.Depth = 1;//存在父级
                var entityParent = await articleCategoryManager.GetById(o.Pid.Value);
                if (entityParent != null)
                {
                    if (entityParent.UnitTypeId != o.UnitTypeId)
                    {
                        r.flag = 0;
                        r.msg = "你选择的单位类型和父级分类的单位类型不一致，请修改。";
                        return r;
                    }
                }
                else
                {
                    r.flag = 1;
                    r.msg = "你选择的父级分类不存在 ，请刷新重新选择。";
                    return r;
                }
            }

            if (o.Id > 0)
            {
                //更新
                entity.CateType = o.CateType;
                entity.Sort = o.Sort;
                entity.Name = o.Name;
                entity.Icon1 = o.Icon1 == null ? "" : o.Icon1;
                entity.Icon2 = o.Icon2 == null ? "" : o.Icon2;
                entity.ConfigCode = o.ConfigCode;
                entity.UnitTypeId = o.UnitTypeId;
                if (await articleCategoryManager.Update(entity))
                {
                    r.flag = 1;
                    r.msg = "资讯分类修改成功。";

                    return r;
                }
                else
                {
                    r.flag = 0;
                    r.msg = "资讯分类修改失败。";
                    return r;
                }
            }
            else
            {
                var saveResult = await articleCategoryManager.Add(new DArticleCategory()
                {
                    CateType = o.CateType,
                    Pid = o.Pid,
                    Depth = 0,
                    Name = o.Name,
                    Sort = o.Sort,
                    ConfigCode = o.ConfigCode,
                    Icon1 = o.Icon1 == null ? "" : o.Icon1,
                    Icon2 = o.Icon2 == null ? "" : o.Icon2,
                    UserId = user.UserId,
                    UnitTypeId = o.UnitTypeId
                });

                //新增
                if (saveResult > 0)
                {
                    r.flag = 1;
                    r.msg = "添加分类成功。";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "添加分类失败。";
                }
            }
            return r;
        }

        /// <summary>
        /// 资讯分类删除
        /// </summary>
        /// <param name="model">资讯分类实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("darticlecategorylistdelete")]
        //<used>1</used>
        public async Task<Result> DArticleCategoryList_Delete([FromBody] DArticleCategoryDto model)
        {
            Result r = new Result();
            var o = mapper.Map<DArticleCategoryDto>(model);
            if (!user.IsSystemUser) //是否是管理员（系统管理员、市级管理员、区县管理员、单位管理员、企业管理员）
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }
            var entity = await articleCategoryManager.GetById(o.Id);

            if (entity != null)
            {
                if (entity.UserId == user.UserId)
                {
                    //判断是否存在子集，存在子集则要先删除子集
                    var list = await articleCategoryManager.Find(t => t.Pid == entity.Id);
                    if (list == null || list.Count > 0)
                    {
                        r.flag = 1;
                        r.msg = "当前你要删除的分类存在子分类，请删除子分类，再删除该分类。。";
                        return r;
                    }
                    if (await articleCategoryManager.Delete(entity))
                    {
                        r.flag = 1;
                        r.msg = "资讯分类删除成功。";
                    }
                }
                else
                {
                    r.flag = 0;
                    r.msg = "该资讯分类不是自己添加的不可删除。";
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "当前资讯分类已不存在。";
            }

            r.flag = 1;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 获取资讯分类
        /// </summary>
        /// <param name="type">type:4 企业</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlecategorygetalllist")]
        //<used>1</used>
        public async Task<Result> Article_CategoryGetAllList(int type = 0)
        {
            Result r = new Result();
            if (user != null)
            {
                var list = (await varticleCategoryLastStageManager.Find(t => (user.UnitTypeId == UnitTypes.Company.ObjToInt() || type == 4) ? t.UnitTypeId == 4 : t.UnitTypeId > -1)).OrderBy(t => t.Sort).ToList();
                r.flag = 1;
                r.msg = "";
                r.data.total = list.Count;
                r.data.rows = list.Select(t => new { t.Id, t.Name });
            }
            else
            {
                r.flag = 0;
                r.msg = "用户尚未登录";
                r.data = null;
            }
            return r;
        }


        /// <summary>
        /// 获取待管理资讯列表
        /// </summary>
        /// <param name="param">查询实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlefindmanage")]
        //<used>1</used>
        public async Task<Result> Article_FindManage([FromBody] VArticleParam param)
        {
            Result r = new Result();
            PageModel<VArticle> pg = await vArticleManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data.Select(t => new { t.Id, t.Cid, t.Sid, t.Title, t.RegDate, t.BeginTime, t.EndTime, t.ImageUrl, t.Hits, t.CommentCount, t.AuditDate, t.ArticleType, t.Statuz, t.AttachDownCount, t.CateName, t.Sort, t.UserId, t.ArticleSort, t.UnitId, t.UnitPid, t.Attachment, t.UserName, t.IsRecommend, t.UnitType });
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";

            return r;
        }

      
        /// <summary>
        ///  资讯发布(状态:0:保存；1：区县审核通过；2：系统审核通过；3：区县审核不通过；4：系统审核不通过)       
        /// </summary>
        /// <param name="model">资讯对象</param>
        /// <users>
        /// 系统超管,校管理员
        /// </users>
        [HttpPost]
        [Route("articleinsertupdate20")]
        //<used>1</used>
        public async Task<Result> Article_InsertUpdate2_0([FromBody] DArticleDto model)
        {
            Result r = new Result();
            if (!(user.IsSystemUser || user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin) || user.Roles.Contains(RoleTypes.SchoolAdmin) || user.Roles.Contains(RoleTypes.CompanyAdmin))) //是否是管理员（系统管理员、市级管理员、区县管理员、单位管理员、企业管理员）
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }
            DArticle o = mapper.Map<DArticle>(model);
            long id = o.Id;
            long UnitPid = 0;

            DArticle p = new DArticle();
            if (o.Id > 0)
            {
                p = await articleManager.GetById(o.Id);
                if (user.AdministratorType != 0 && p.UnitId != user.UnitId)
                {
                    r.flag = 0;
                    r.msg = "非法操作，您无权操作此功能";
                    return r;
                }
            }

            var unit = await unitManager.QueryById((object)user.UnitId);
            if (unit != null)
            {
                UnitPid = unit.PId;
            }

            p.Statuz = 2;
            p.Title = o.Title;
            p.Remark = o.Remark;
            p.Cid = o.Cid;
            p.ExtImpleSchool = o.ExtImpleSchool;
            p.AreaId = o.AreaId;
            p.Attachment = o.Attachment;
            p.RegDate = DateTime.Now;
            p.Sid = o.Sid;
            p.Subtitle = o.Subtitle;
            p.ShortTitle = o.ShortTitle;
            p.ArticleType = o.ArticleType;
            p.HasImage = o.HasImage;
            p.ImageUrl = o.ImageUrl;
            p.ImgAlt = o.ImgAlt;
            p.Source = o.Source;
            p.Author = o.Author;
            p.Keywords = o.Keywords;
            p.Description = o.Description;
            p.IsRecommend = o.IsRecommend;
            p.AreaId = o.AreaId;
            p.UserId = user.UserId;
            p.Sort = o.Sort;
            p.IsHot = o.IsHot;
            p.IsFoc = o.IsFoc;
            p.Hits = o.Hits;
            p.CommentCount = o.CommentCount;
            p.AuditorId = o.AuditorId;
            p.AuditDate = DateTime.Now;
            p.BeginTime = DateTime.Now;
            p.EndTime = DateTime.Now;
            p.UnitId = user.UnitId;
            p.UnitPid = UnitPid;
            p.Attachment = o.Attachment;
            p.ExtProjectName = o.ExtProjectName;
            p.ExtConstDime = o.ExtConstDime;
            p.ExtImpleSchool = o.ExtImpleSchool;
            p.ExtProjectCost = o.ExtProjectCost;
            p.ExtConstrucCompany = o.ExtConstrucCompany;
            p.ExtBrandSupport = o.ExtBrandSupport;

            r = await articleManager.InsertUpdate(p);

            if (r.flag > 0)
            {
                if (o.Id > 0)
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改资讯【{2}】信息成功！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
                else
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】发布资讯【{2}】信息成功！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
            }
            else
            {
                if (o.Id > 0)
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改资讯【{2}】信息失败！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
                else
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】发布资讯【{2}】信息失败！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
            }
            return r;
        }

        /// <summary>
        /// 资讯保存(状态:0:保存；)
        /// </summary>
        /// <param name="model">资讯对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlesave20")]
        //<used>1</used>
        public async Task<Result> Article_Save2_0([FromBody] DArticleDto model)
        {
            Result r = new Result();
            if (!(user.IsSystemUser || user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin) || user.Roles.Contains(RoleTypes.SchoolAdmin) || user.Roles.Contains(RoleTypes.CompanyAdmin))) //是否是管理员（系统管理员、市级管理员、区县管理员、单位管理员、企业管理员）
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }
            DArticle o = mapper.Map<DArticle>(model);
            long UnitPid = 0;
            long id = o.Id;

            //禁用区县审核，所以状态初始化为1
            DArticle p = new DArticle();
            if (o.Id > 0)
            {
                p = await articleManager.GetById(o.Id);
                if (user.AdministratorType != 0 && p.UnitId != user.UnitId)
                {
                    r.flag = 0;
                    r.msg = "非法操作，您无权操作此功能";
                    return r;
                }
            }
            else
            {
                var unit = await unitManager.QueryById((object)user.UnitId);
                if (unit != null)
                {
                    UnitPid = unit.PId;
                }
            }

            p.Statuz = 0;
            p.Title = o.Title;
            p.Remark = o.Remark;
            p.Cid = o.Cid;
            p.ExtImpleSchool = o.ExtImpleSchool;
            p.Attachment = o.Attachment;
            p.ImageUrl = o.ImageUrl;
            p.RegDate = DateTime.Now;
            p.Sid = o.Sid;
            p.Subtitle = o.Subtitle;
            p.ShortTitle = o.ShortTitle;
            p.ArticleType = o.ArticleType;
            p.HasImage = o.HasImage;
            p.ImgAlt = o.ImgAlt;
            p.Source = o.Source;
            p.Author = o.Author;
            p.Keywords = o.Keywords;
            p.Description = o.Description;
            p.IsRecommend = o.IsRecommend;
            p.AreaId = o.AreaId;
            p.UserId = user.UserId;
            p.Sort = o.Sort;
            p.IsHot = o.IsHot;
            p.IsFoc = o.IsFoc;
            p.Hits = o.Hits;
            p.CommentCount = o.CommentCount;
            p.AuditorId = o.AuditorId;
            p.AuditDate = DateTime.Now;
            p.BeginTime = DateTime.Now;
            p.EndTime = DateTime.Now;
            p.UnitId = user.UnitId;
            p.UnitPid = UnitPid;
            p.Attachment = o.Attachment;
            p.ExtProjectName = o.ExtProjectName;
            p.ExtConstDime = o.ExtConstDime;
            p.ExtImpleSchool = o.ExtImpleSchool;
            p.ExtProjectCost = o.ExtProjectCost;
            p.ExtConstrucCompany = o.ExtConstrucCompany;
            p.ExtBrandSupport = o.ExtBrandSupport;

            r = await articleManager.InsertUpdate(p);

            if (r.flag > 0)
            {
                if (o.Id > 0)
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改资讯【{2}】信息成功！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
                else
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】新增资讯【{2}】信息成功！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
            }
            else
            {
                if (o.Id > 0)
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改资讯【{2}】信息失败！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
                else
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】新增资讯【{2}】信息失败！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
            }
            return r;
        }

        /// <summary>
        /// 企业资讯发布(状态:0:保存；1：区县审核通过；2：系统审核通过；3：区县审核不通过；4：系统审核不通过)
        /// </summary>
        /// <param name="o">资讯对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("companysubmitarticle")]
        //<used>1</used>
        public async Task<Result> Company_SubmitArticle([FromBody] DArticle o)
        {
            Result r = new Result();
            if (!(user.IsSystemUser || user.Roles.Contains(RoleTypes.CompanyAdmin))) //是否是管理员（系统管理员、企业管理员）
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }
            DArticle p = new DArticle();
            if (o.Id > 0)
            {
                p = await articleManager.GetById(o.Id);
                if (user.AdministratorType != 0 && p.UnitId != user.UnitId)
                {
                    r.flag = 0;
                    r.msg = "非法操作，您无权操作此功能";
                    return r;
                }
            }

            p.Statuz = 1;
            long id = o.Id;
            p.Title = o.Title;
            p.Remark = o.Remark;
            p.Cid = o.Cid;
            p.AreaId = o.AreaId;
            p.Attachment = o.Attachment;
            p.ImageUrl = o.ImageUrl;
            p.RegDate = DateTime.Now;
            p.ExtImpleSchool = o.ExtImpleSchool;

            r = await articleManager.InsertUpdate(p);
            if (r.flag > 0)
            {
                if (o.Id > 0)
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改资讯【{2}】信息成功！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
                else
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】提交资讯【{2}】信息成功！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
                }
            }
            else
            {
                if (o.Id > 0)
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改资讯【{2}】信息失败！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");

                }
                else
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】提交资讯【{2}】信息失败！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");

                }
            }

            return r;
        }

        /// <summary>
        ///  资讯审核（管理员审核，可以进行修改）  
        /// </summary>  
        /// <users>
        /// 系统超管
        /// </users>
        [HttpPost]
        [Route("articleaudit20")]
        //<used>1</used>
        public async Task<Result> Article_Audit2_0([FromBody] DArticle o)
        {
            Result r = new Result();

            if (!(user.IsSystemUser || user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin) || user.Roles.Contains(RoleTypes.SchoolAdmin) || user.Roles.Contains(RoleTypes.CompanyAdmin))) //是否是管理员（系统管理员、市级管理员、区县管理员、单位管理员、企业管理员）
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }

            DArticle p = new DArticle();
            p = await articleManager.GetById(o.Id);
            if (p != null)
            {
                p.ImageUrl = "";
                p.Statuz = o.Statuz;
                p.Description = o.Description;
                p.AuditorId = user.UserId;
                p.AuditDate = DateTime.Now;
                p.Cid = o.Cid;
                p.Title = o.Title;
                p.Remark = o.Remark;
                p.ImageUrl = o.ImageUrl;
                //p.BeginTime = o.BeginTime;
                //p.EndTime = o.EndTime;
                //p.AreaId = o.AreaId;
                p.Attachment = o.Attachment;
                //p.ExtProjectName = o.ExtProjectName;
                //p.ExtConstDime = o.ExtConstDime;
                p.ExtImpleSchool = o.ExtImpleSchool;
                //p.ExtProjectCost = o.ExtProjectCost;
                //p.ExtConstrucCompany = o.ExtConstrucCompany;
                //p.ExtBrandSupport = o.ExtBrandSupport;

                await articleManager.Update(p);

                r.flag = 1;
                r.msg = "审核结果提交成功";
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】，【{2}】审核通过！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
            }
            else
            {
                r.flag = 1;
                r.msg = "数据不存在，审核失败";
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】，【{2}】审核失败！", user.UnitName, user.UserName, p.Title), ApplicationConfig.CommonLogFilePath + "Article\\");
            }

            return r;
        }

        /// <summary>
        /// 根据id获取获取资讯详细信息 (查看专用)
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="isPreveiw"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("articlegetfullbyid")]
        //<used>1</used>
        public async Task<Result> Article_GetFullById(long Id, bool isPreveiw = false)
        {
            Result r = new Result();
            DArticle p = new DArticle();
            p = await articleManager.GetById(Id);
            if (p != null)
            {
                if (!isPreveiw)
                    p.Hits = p.Hits + 1;

                await articleManager.Update(p);

                string UnitName = "", CatalogName = "", UnitTel = "";
                var userInfo = await sysUserExtensionManager.QueryById(user.ID);
                if (userInfo != null && userInfo.Id > 0)
                {
                    UnitTel = userInfo.Mobile;
                    var unitInfo = await unitManager.QueryById(userInfo.UnitId);
                    if (unitInfo != null && unitInfo.Id > 0)
                    {
                        UnitName = unitInfo.Name;
                    }
                }
              
                var catalog = await articleCategoryManager.GetById(p.Cid);
                if (catalog != null)
                {
                    CatalogName = catalog.Name;
                }

                r.flag = 1;
                r.msg = "";
                r.data.total = 1;
                r.data.rows = p;
                r.data.footer = new { UnitName = UnitName, UnitTel = UnitTel, CatalogName = CatalogName, CatalogId = p.Cid };
            }
            else
            {
                r.flag = 0;
            }

            return r;
        }

        /// <summary>
        /// 资讯分类查询
        /// </summary>
        /// <param name="pid">父级Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getdarticlecategorylist")]
        //<used>1</used>
        public async Task<Result> GetDArticleCategoryList(long pid)
        {
            Result r = new Result();
            if (user != null)
            {
                var list = await varticleCategoryManager.GetPaged(new VArticleCategoryParam { Pid = pid, pageSize = int.MaxValue, pageIndex = 1, sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Sort", SortType = "DESC" }, new SortBaseModel { SortCode = "Id", SortType = "DESC" } } });
                if (list.data.Count > 0)
                {
                    r.data.rows = list.data.Select(t => new { t.Id, t.Pid, t.Name, t.Depth, t.Path, t.Sort, t.Icon1, t.Icon2, t.ConfigCode, t.CateType, t.UserId, t.UnitTypeId });
                }
                r.data.total = list.data.Count;
                r.flag = 1;
                r.msg = "查询成功。";
            }
            else
            {
                r.flag = 0;
                r.msg = "用户尚未登录";
            }
            return r;
        }


        /// <summary>
        /// 获取首页资讯列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("vcatalogarticlefind")]
        //<used>1</used>
        public async Task<Result> VCatalogArticle_Find([FromBody] VCategoryArticleListParam param)
        {
            Result r = new Result();
            if (user != null)
            {
                if (user.UnitTypeId == 4)
                {
                    param.IsAllowCompany = true;
                }
                var list = await vcategoryArticleListManager.GetPaged(param);
                r.flag = 1;
                r.data.total = list.dataCount;
                r.data.rows = list.data.Select(t => new { t.Id, t.Cid, t.Sid, t.Title, t.RegDate, t.BeginTime, t.EndTime, t.ImageUrl, t.Hits, t.CommentCount, t.AuditDate, t.ArticleType, t.Statuz, t.AttachDownCount, t.CateName, t.Sort, t.UserId, t.ArticleSort, t.UnitId, t.UnitPid, t.Attachment, t.UserName, t.IsAllowCompany, t.Icon1, t.Icon2, t.IsRecommend, t.UnitType }).ToList();
                r.msg = $"{param.pageIndex}-{param.pageSize}/{list.dataCount}";
                r.flag = 1;
            }
            else
            {
                r.flag = 0;
                r.msg = "用户尚未登录";
                r.data = null;
            }
            return r;
        }


        /// <summary>
        /// 资讯管理：资讯附件下载
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("articleattachmentdown")]
        //<used>1</used>
        public async Task<FileStreamResult> Article_AttachmentDown(string t = "", long id = 0)
        {
            Stream fileContents = new MemoryStream(0);
            if (t.Equals("article") && id > 0)
            {
                DArticle art = await articleManager.GetById(id);
                if (art != null && art.Attachment.Length > 0)
                {
                    ++art.AttachDownCount;
                    await articleManager.Update(art);
                    string strExt = art.Attachment.Substring(art.Attachment.LastIndexOf('.'));
                    string file = art.Attachment;
                    string filename = art.Title + strExt;

                    var webRootPath = env.ContentRootPath;
                    //var webRootPath = _webHostEnvironment.ContentRootPath;//>>>相当于HttpContext.Current.Server.MapPath("") 
                    //文件所在完整路径
                    var filePath = $"{webRootPath}/{file}";

                    if (System.IO.File.Exists(filePath)) //判断文件是否存在
                    {
                        //指定路径字符串的扩展名（包括句点“.”）
                        string fileExt = Path.GetExtension(file);
                        var stream = System.IO.File.OpenRead(filePath);

                        //获取文件的ContentType
                        var provider = new FileExtensionContentTypeProvider();
                        //文件扩展名到 MIME 内容类型的映射。
                        var mime = provider.Mappings[fileExt];
                        return File(stream, mime, filename);
                    }
                    else
                    {
                        return File(fileContents, "application/octet-stream", "");
                    }
                }
                else
                {
                    return File(fileContents, "application/octet-stream", "");
                }
            }
            else
            {
                return File(fileContents, "application/octet-stream", "");
            }
        }

        /// <summary>
        /// 判断用户是否弹出版本更新信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("isreadlogin")]
        //<used>1</used>
        public async Task<Result> IsReadLogin()
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "";
            //查询用户最近2次登录时间
            var listAgreement = await userActionLogManager.GetPaged(new BUserActionLogParam
            {
                Type = 1,
                Statuz = 1,
                UserId = user.UserId,
                pageIndex = 1,
                pageSize = 2,
                sortModel = new List<SortBaseModel>() { new SortBaseModel { SortCode = "RegTime", SortType = "DESC" } }
            });

            if (listAgreement.data.Count == 2)
            {
                DateTime dt = listAgreement.data[1].CreateTime;

                var listArticle = await articleManager.GetPaged(new DArticleParam
                {
                    Cid = 9999999,
                    Statuz = 2,
                    RegDate = dt,
                    pageIndex = 1,
                    pageSize = int.MaxValue,
                    sortModel = new List<SortBaseModel>() { new SortBaseModel { SortCode = "RegDate", SortType = "DESC" } }
                });

                ////判断是否点击已知晓
                //var list = await userLoginAgreementManager.GetPaged(new PUserLoginAgreementBjParam
                //{
                //    UseModule = 2,
                //    UserId = user.UserId,
                //    RegTime = dt,
                //    pageIndex = 1,
                //    pageSize = 1
                //});

                //if (listArticle.data.Count > 0 && list.data.Count == 0)
                //{
                //    r.flag = 0;
                //    r.msg = "提示信息";
                //    r.data.rows = listArticle.data.Select(t => new { t.Id, t.Title, t.Remark, t.RegDate });
                //}
            }

            return r;
        }

        ///// <summary>
        ///// 记录用户点击确认升级内容
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("adduserreadupgrade")]
        ////<used>1</used>
        //public async Task<Result> AddUserReadUpgrade()
        //{
        //    Result r = new Result();
        //    PUserLoginAgreementBj loginAgreenment = new PUserLoginAgreementBj();
        //    loginAgreenment.UserId = user.UserId;
        //    loginAgreenment.RegTime = DateTime.Now;
        //    loginAgreenment.UseModule = 2;
        //    var id = await userLoginAgreementManager.Add(loginAgreenment);
        //    if (id > 0) 
        //    {
        //        r.flag = 1;
        //        r.msg = "保存成功";
        //    }
        //    else
        //    {
        //        r.flag = 0;
        //        r.msg = "操作异常";
        //    }
        //    return r;
        //}
    }
}
