﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位分组设置表
    ///</summary>
    [SugarTable("wf_GroupUnitSet", "单位分组设置表")]
    public class WfGroupUnitSet : BaseEntity
    {

        public WfGroupUnitSet()
        {

        }

        /// <summary>
        ///流程Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessId { get; set; }

        /// <summary>
        ///单位Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? SchoolId { get; set; }

        /// <summary>
        ///分组Id（对应字典表中的Id）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? GroupId { get; set; }

        /// <summary>
        ///分组项Id（对应分组中每项的Id，也是字典表中）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? GroupItemId { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 分组项值
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string GroupItemName { get; set; }

        /// <summary>
        /// 2：字典表(B_Dictionary)下拉框组件，3：审批字典表(Wf_Dictionary)下拉框组件
        /// </summary>
        [SugarColumn(DefaultValue = "3")]
        public int TypeBox { get; set; }
    }


}

