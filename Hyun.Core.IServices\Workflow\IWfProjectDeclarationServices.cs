﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfProjectDeclaration接口方法
    ///</summary>
    public interface IWfProjectDeclarationServices : IBaseServices<WfProjectDeclaration>
    {

        /// <summary>
        /// 保存填报信息
        /// </summary>
        /// <param name="o"></param>
        /// <param name="typeButton">1：保存，2：提交</param>
        /// <returns></returns>
        Task<Result> FillingSave(FillingModel o, int typeButton);


        /// <summary>
        /// 删除填报信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result<string>> DelFillingInfo(long id);

        /// <summary>
        /// 填报详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result> FillingDetail(long id);

        /// <summary>
        /// 获取审核页面信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        Task<Result> FillingAudit(long id, long processNodeId);

        /// <summary>
        /// 审批
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result> ProcessApproval(FillingModel o);

        /// <summary>
        /// 撤销
        /// </summary>
        /// <param name="id"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        Task<Result<string>> Revoke(long id,long projectDeclarationId, long processNodeId);


        /// <summary>
        /// 流程审核暂存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> ProcessSave(FillingModel o);

        /// <summary>
        /// 获取下拉框数据信息
        /// </summary>
        /// <param name="processId"></param>
        /// <param name="processNodeId"></param>
        /// <param name="projectDeclarationId"></param>
        /// <returns></returns>
        Task<SelectDropDownModel> GetDropList(long processId, long processNodeId, long projectDeclarationId);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        Task<string> GetUserMobileInfo(WfProjectDeclaration obj, long processNodeId);
    }
}

