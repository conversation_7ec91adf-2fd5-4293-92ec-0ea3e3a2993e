﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models.Uniform;
using Hyun.Core.IServices.Uniform;
using Hyun.Core.Common.Extensions;
using System.Collections.Generic;
using NetTaste;
using Hyun.Core.Services.Uniform;
using Hyun.Core.Model;
using Hyun.Core.Services;
using Hyun.Core.Model.Models;
using Hyun.Old.Util;
using System.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;
namespace Hyun.Core.Api
{
    /// <summary>
    /// 校服采购-采购管理-校服调换-校服资助
    /// </summary>
    [Route("api/hyun/xuniformpurchase")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformPurchaseController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformPurchaseServices ixuniformpurchaseManager;
        private readonly IPUnitServices ipunitManager;
        private readonly IBAreaServices ibareaManager;
        private readonly IBAttachmentServices ibattachmentManager;
        private readonly IXUniformChangeServices uniformSwapDetailManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IXUniformShelfServices uniformshelfManager;
        private readonly IPSchoolExtensionServices schoolExtensionManager;
        private readonly IXUniformPurchaseGradeServices uniformpurchasegradeManager;
        private readonly IXUniformParentPurchaseServices uniformparentpurchaseManager;
        private readonly IPStudentServices studentManager;
        private readonly IPClassInfoServices classInfoManager;
        private readonly IXUniformSponsorServices uniformSponsorManager;
        public XUniformPurchaseController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IXUniformPurchaseServices _ixuniformpurchaseManager, IPUnitServices _ipunitManager, IBAreaServices _ibareaManager, IBAttachmentServices _ibattachmentManager, IXUniformChangeServices _uniformSwapDetailManager, IBDictionaryServices _dictionaryManager, IXUniformShelfServices _uniformshelfManager, IPSchoolExtensionServices _schoolExtensionManager, IXUniformPurchaseGradeServices _uniformpurchasegradeManager, IXUniformParentPurchaseServices _uniformparentpurchaseManager, IPStudentServices _studentManager, IPClassInfoServices _classInfoManager, IXUniformSponsorServices _uniformSponsorManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ixuniformpurchaseManager = _ixuniformpurchaseManager;
            ipunitManager = _ipunitManager;
            ibareaManager = _ibareaManager;
            ibattachmentManager = _ibattachmentManager;
            uniformSwapDetailManager = _uniformSwapDetailManager;
            dictionaryManager = _dictionaryManager;
            uniformshelfManager = _uniformshelfManager;
            schoolExtensionManager = _schoolExtensionManager;
            uniformpurchasegradeManager = _uniformpurchasegradeManager;
            uniformparentpurchaseManager = _uniformparentpurchaseManager;
            studentManager = _studentManager;
            classInfoManager = _classInfoManager;
            uniformSponsorManager = _uniformSponsorManager;
        }
        #region 获取查询数据 

        /// <summary>
        /// 校服采购-采购管理-查询分页列表
        /// </summary>
        /// <param name="param">XUniformPurchaseParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<XUniformPurchaseDto>>> GetPaged([FromBody] XUniformPurchaseParam param)
        {
            Result<List<XUniformPurchaseDto>> r = new Result<List<XUniformPurchaseDto>>();
            param.UnitType = user.UnitTypeId;
            // param.role = user.UnitId;"UserRoleIds: 30,370"
            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }
            if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
            {
                param.SchoolId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
            {
                param.CountyId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
            {
                param.CityId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.Parent.ObjToInt().ToString()))
            {
                //家长，根据学生查单位。
                param.userId = user.UserId;
            }
            else
            {
                return baseFailed<List<XUniformPurchaseDto>>("非法操作，你无权访问！");
            }
            PageModel<XUniformPurchaseDto> pg = await ixuniformpurchaseManager.GetPaged(param);
            List<BArea> listArea = null;
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
                {
                    r.data.rows = (from item in pg.data
                                   select new XUniformPurchaseDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SupplierId = item.SupplierId,
                                       PurchaseYear = item.PurchaseYear,
                                       PurchaseNo = item.PurchaseNo,
                                       IsContractRenewal = item.IsContractRenewal,
                                       ContractStartDate = item.ContractStartDate,
                                       ContractEndDate = item.ContractEndDate,
                                       FilingStatuz = item.FilingStatuz,
                                       IsFiling = item.IsFiling,
                                       IsRelatedUniform = item.IsRelatedUniform,
                                       IsCountyManager = item.IsCountyManager,
                                       FilingStatuzName = ((UniformFilingEnum)item.FilingStatuz).GetDescription(),
                                   }).ToList();
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    r.data.rows = (from item in pg.data
                                   select new XUniformPurchaseDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SupplierId = item.SupplierId,
                                       SchoolName = item.SchoolName,
                                       PurchaseYear = item.PurchaseYear,
                                       PurchaseNo = item.PurchaseNo,
                                       IsContractRenewal = item.IsContractRenewal,
                                       ContractStartDate = item.ContractStartDate,
                                       ContractEndDate = item.ContractEndDate,
                                       IsFiling=item.IsFiling,
                                       FilingStatuz = item.FilingStatuz,
                                       IsRelatedUniform = item.IsRelatedUniform,
                                       IsCountyManager = item.IsCountyManager,
                                       FilingStatuzName = ((UniformFilingEnum)item.FilingStatuz).GetDescription(),
                                   }).ToList();
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                    r.data.rows = (from item in pg.data
                                   select new XUniformPurchaseDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SupplierId = item.SupplierId,
                                       CountyName = GetAreaName(item.CountyAreaId, item.CountyName, listArea),
                                       SchoolName = item.SchoolName,
                                       PurchaseYear = item.PurchaseYear,
                                       PurchaseNo = item.FilingStatuz == UniformFilingEnum.SubmitNone.ObjToInt() ? "--" : item.PurchaseNo,
                                       IsContractRenewal = item.IsContractRenewal,
                                       ContractStartDate = item.ContractStartDate,
                                       ContractEndDate = item.ContractEndDate,
                                       FilingStatuz = item.FilingStatuz,
                                       IsRelatedUniform = item.IsRelatedUniform,
                                       IsCountyManager = item.IsCountyManager,
                                       FilingStatuzName = ((UniformFilingEnum)item.FilingStatuz).GetDescription(),
                                   }).ToList();
                }
            }
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                //是否续签合同
                List<dropdownModel> dropIsContractRenewal = new List<dropdownModel>();
                var listIsStatuz = EnumExtensions.EnumToList<StatuzIsEnum>();
                if (listIsStatuz != null)
                {
                    foreach (var item in listIsStatuz)
                    {
                        dropIsContractRenewal.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }
                r.data.other = new { IsRenewalStatuz = dropIsContractRenewal };
                //备案状态
                List<dropdownModel> dropFilingStatuz = new List<dropdownModel>();
                var listFilingStatuz = EnumExtensions.EnumToList<UniformFilingEnum>();
                if (listFilingStatuz != null)
                {
                    foreach (var item in listFilingStatuz)
                    {
                        dropFilingStatuz.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }

                if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
                {
                    r.data.other = new { StatuzIsRenewal = dropIsContractRenewal, StatuzFiling = dropFilingStatuz };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listSchool = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSchool != null)
                    {
                        foreach (var item in listSchool)
                        {
                            dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                    r.data.other = new { StatuzIsRenewal = dropIsContractRenewal, StatuzFiling = dropFilingStatuz, SchoolList = dropdownSchool };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listCounty = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        //获取所有区域Id
                        if (listArea == null)
                        {
                            listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                        }
                        foreach (var item in listCounty)
                        {
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = GetAreaName(item.AreaId, item.Name, listArea) });
                        }
                    }
                    r.data.other = new { StatuzIsRenewal = dropIsContractRenewal, StatuzFiling = dropFilingStatuz, CountyList = dropdownCounty, SchoolList = dropdownSchool };
                }
            }
            return r;
        }


        /// <summary>
        /// 校服采购-采购管理-根据Id获取对象
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("geteditbyid")]
        public async Task<Result<XUniformPurchaseDto>> GetById(long id)
        {
            Result<XUniformPurchaseDto> r = new Result<XUniformPurchaseDto>();
            r.flag = 1;
            r.msg = "查询成功！";
            IEnumerable<object> listAttachment = null;
            if (id > 0)
            {
                XUniformPurchase m = await ixuniformpurchaseManager.QueryById(id);
                if (m != null)
                {
                    r.flag = 1;
                    r.msg = "查询成功";
                    r.data.rows = mapper.Map<XUniformPurchaseDto>(m);


                    var list = await ibattachmentManager.Find(f => f.ObjectId == m.Id && f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.PurchaseCreate.ObjToInt());
                    if (list != null && list.Count > 0)
                    {
                        var listTemp = (from item in list
                                        select new
                                        {
                                            Id = item.Id,
                                            Title = item.Title,
                                            Path = item.Path,
                                            Width = item.Width,
                                            Height = item.Height,
                                            DocType = item.DocType,
                                            IsDefault = item.IsDefault,
                                            Remark = item.Remark,
                                            FileCategory = item.FileCategory,
                                            Ext = item.Ext,
                                        });
                        listAttachment = listTemp;
                    }

                }
            }
            //获取下拉列表数据
            //是否续签合同
            List<dropdownModel> dropIsContractRenewal = new List<dropdownModel>();
            var listIsStatuz = EnumExtensions.EnumToList<StatuzIsEnum>();
            if (listIsStatuz != null)
            {
                foreach (var item in listIsStatuz)
                {
                    dropIsContractRenewal.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                }
            }
            r.data.other = new { IsRenewalStatuz = dropIsContractRenewal };
            //供应商列表
            List<dropdownModel> dropdownSupplier = new List<dropdownModel>();
            var listSupplier = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
            if (listSupplier != null)
            {
                foreach (var item in listSupplier)
                {
                    dropdownSupplier.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                }
            }
            //采购组织形式
            List<dropdownModel> organizationForm = new List<dropdownModel>();
            var listDic = await dictionaryManager.Find(m => m.TypeCode == DictionaryTypeCodeEnum.OrganizationForm.ObjToInt().ToString() && m.Statuz == StatuzEnum.Enable.ObjToInt());
            var listOrganizationForm = EnumExtensions.EnumToList<XUniformOrganizationFormEnum>();
            if (listDic != null)
            {
                foreach (var item in listDic)
                {
                    organizationForm.Add(new dropdownModel() { value = item.DicValue, label = item.DicName });
                }
            }
            r.data.other = new { IsRenewalStatuz = dropIsContractRenewal, SupplierList = dropdownSupplier, OrganizationForm = organizationForm, AttachmentList = listAttachment };
            return r;
        }

        /// <summary>
        /// 校服采购-采购管理-根据Id获取采购详情
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbyid")]
        public async Task<Result<XUniformPurchaseDto>> GetByIdInfo(long id)
        {
            Result<XUniformPurchaseDto> r = new Result<XUniformPurchaseDto>();
            r.flag = 1;
            r.msg = "查询成功！";

            if (id > 0)
            {
                XUniformPurchase entity = await ixuniformpurchaseManager.QueryById(id);
                if (entity != null)
                {
                    r.flag = 1;
                    r.msg = "查询成功";
                    r.data.rows = mapper.Map<XUniformPurchaseDto>(entity);
                    var entityUnit = await ipunitManager.QueryById((object)entity.SchoolId);
                    if (entityUnit != null)
                    {
                        r.data.rows.SchoolName = entityUnit.Name;
                    }
                    //处理下拉赋值。
                    var listSupplier = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.Id == entity.SupplierId && f.IsDeleted == false && f.Statuz == 1);
                    if (listSupplier != null && listSupplier.Count > 0)
                    {
                        r.data.rows.SupplierName = listSupplier.FirstOrDefault().Name;
                    }
                    if (entity.OrganizationForm != null)
                    {
                        var listDic = await dictionaryManager.Find(m => m.TypeCode == DictionaryTypeCodeEnum.OrganizationForm.ObjToInt().ToString() && m.DicValue == entity.OrganizationForm.ToString() && m.Statuz == StatuzEnum.Enable.ObjToInt());
                        r.data.rows.OrganizationFormName = listDic.FirstOrDefault().DicName;
                    }

                    IEnumerable<object> listAttachment = null;
                    var list = await ibattachmentManager.Find(f => f.ObjectId == entity.Id && f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.PurchaseCreate.ObjToInt());
                    if (list != null && list.Count > 0)
                    {
                        var listTemp = (from item in list
                                        select new
                                        {
                                            Id = item.Id,
                                            Title = item.Title,
                                            Path = item.Path,
                                            Width = item.Width,
                                            Height = item.Height,
                                            DocType = item.DocType,
                                            IsDefault = item.IsDefault,
                                            Remark = item.Remark,
                                            FileCategory = item.FileCategory,
                                            Ext = item.Ext,
                                        });
                        listAttachment = listTemp;
                    }
                    r.data.other = new { AttachmentList = listAttachment };
                }
            }
            return r;
        }
        #endregion

        #region 提交保存（添加、修改、删除）

        /// <summary>
        /// 校服采购-采购管理-添加
        /// </summary>
        /// <param name="model">XUniformPurchaseDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveadd")]
        public async Task<Result<XUniformPurchaseDto>> Add([FromBody] XUniformPurchaseDto model)
        {
            model.SchoolId = user.UnitId;
            model.CountyId = user.UnitPId;
            model.CreateBy = user.UserName;
            model.CreateId = user.UserId;
            model.Id = 0;
            return await ixuniformpurchaseManager.InsertUpdate(model);
        }

        /// <summary>
        /// 校服采购-采购管理-修改
        /// </summary>
        /// <param name="model">XUniformPurchaseDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveedit")]
        public async Task<Result<XUniformPurchaseDto>> Edit([FromBody] XUniformPurchaseDto model)
        {
            model.SchoolId = user.UnitId;
            model.CountyId = user.UnitPId;
            model.CreateBy = user.UserName;
            model.CreateId = user.UserId;
            if (model.Id <= 0)
            {
                return baseFailed<XUniformPurchaseDto>("非法操作，请从页面点击操作。");
            }
            return await ixuniformpurchaseManager.InsertUpdate(model);
        }


        /// <summary>
        /// 校服采购-采购管理-提交
        /// </summary>
        /// <param name="model">XUniformPurchaseDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("savesubmit")]
        public async Task<Result> Submit([FromBody] XUniformPurchaseDto model)
        {
            Result r = new Result();
            model.SchoolId = user.UnitId;
            model.CountyId = user.UnitPId;
            model.CreateBy = user.UserName;
            model.CreateId = user.UserId;
            if (model.Id <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作，请从页面点击操作。";
                return r;
            }
            r = await ixuniformpurchaseManager.Submit(model);
            return r;
        }


        /// <summary>
        /// 根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("deletebyid")]
        public async Task<Result> DeleteById(long id)
        {
            Result r = new Result();
            XUniformPurchaseDto model = new XUniformPurchaseDto();
            model.Id = id;
            model.SchoolId = user.UnitId;
            model.CountyId = user.UnitPId;
            model.CreateBy = user.UserName;
            model.CreateId = user.UserId;
            r = await ixuniformpurchaseManager.FakeDeleteById(model);
            return r;
        }

        /// <summary>
        /// 校服采购-根据Id删除附件数据
        /// </summary>
        /// <param name="id">校服选用表Id</param>
        /// <param name="attid">附件Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("delattachmentbyid")]
        public async Task<Result> DeleteAttachmentById(long id, long attid)
        {
            Result r = new Result();
            var model = new XUniformPurchaseDto();
            model.Id = id;
            model.AttachmentId = attid;
            model.SchoolId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformpurchaseManager.UpdateAttachmentDelete(model);
            return r;
        }
        #endregion

        #region 备查、退回（区县）
        /// <summary>
        /// 校服采购-区县备查
        /// </summary>
        /// <param name="id">方案标识</param>
        /// <param name="statuz">状态 1：通过  2：退回</param>
        /// <param name="explanation">说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("confirmfiling")]
        public async Task<Result> FilingConfirm(long id, int statuz, string explanation = "")
        {
            Result r = new Result();
            var model = new XUniformPurchaseDto();
            model.Id = id;
            if (statuz == 1)
            {
                model.FilingStatuz = UniformFilingEnum.Filinged.ObjToInt();
            }
            else
            {
                model.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
            }
            model.FilingExplanation = explanation;
            model.CountyId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformpurchaseManager.FilingConfirm(model);
            return r;
        }


        /// <summary>
        /// 方案选用-区县退回
        /// </summary>
        /// <param name="id">方案标识</param>
        /// <param name="explanation">说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("filingbackout")]
        public async Task<Result> FilingBackout(long id, string explanation = "")
        {
            Result r = new Result();
            var model = new XUniformPurchaseDto();
            model.Id = id;
            model.FilingExplanation = explanation;
            model.CountyId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformpurchaseManager.FilingBackout(model);
            return r;
        }

        /// <summary>
        /// 方案选用-区县撤回（撤回到上一级）
        /// </summary>
        /// <param name="id">方案标识</param>
        /// <param name="explanation">说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("filingrevoked")]
        public async Task<Result> FilingRevoked(long id, string explanation = "")
        {
            Result r = new Result();
            var model = new XUniformPurchaseDto();
            model.Id = id;
            model.FilingExplanation = explanation;
            model.CountyId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformpurchaseManager.FilingRevoked(model);
            return r;
        }

        #endregion

        #region 获取采购批次
        [HttpGet]
        [Route("getpurchaseno")]
        public async Task<Result<List<dropdownModel>>> GetPurchaseNo()
        {
            Result<List<dropdownModel>> r = new Result<List<dropdownModel>>();
            var list = await ixuniformpurchaseManager.Find(n => n.SchoolId == user.UnitId
                            && n.IsDeleted == false
                            && n.ContractEndDate >= DateTime.Now.Date
                            );
            if (list != null && list.Count > 0)
            {
                r.data.total = list.Count;
                r.data.rows = list.Select(m => new dropdownModel { value = m.Id.ObjToString(), label = m.PurchaseNo,pid = m.OrderNum }).ToList();
            }
            r.flag = 1;
            r.msg = "查询成功。";
            return r;
        }
        #endregion

        #region 校服调换 zyf

        /// <summary>
        /// 调换发起列表
        /// </summary>
        /// <param name="param">
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("getswappaged")]
        public async Task<Result<List<XUniformPurchaseSwapDto>>> GetSwapPaged([FromBody] XUniformPurchaseParam param)
        {
            var msgdata = new Result<List<XUniformPurchaseSwapDto>>();
            param.SchoolId = user.UnitId;
            PageModel<XUniformPurchaseSwapDto> pg = await ixuniformpurchaseManager.GetSwapPaged(param);
            if (param.isFirst)
            {
                //状态
                var listSwapStatuz = EnumExtensions.EnumToList<UniformSwapEnum>();
                List<dropdownModel> listStatuz = listSwapStatuz.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();
                //年度前台处理
                //供应商
                var listCompany = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listSupplier = listCompany.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                msgdata = baseSucc(mapper.Map<List<XUniformPurchaseSwapDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listStatuz = listStatuz, listSupplier = listSupplier });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 校服调换（发起-修改）
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("launch")]
        public async Task<Result<string>> SwapLaunch([FromBody] XUniformPurchaseSwapModel obj)
        {
            obj.SchoolId = user.UnitId;
            var r = await ixuniformpurchaseManager.SwapLaunch(obj);
            return r;
        }

        /// <summary>
        /// 单位查看调换单
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getexchangeorder")]
        public async Task<Result<List<XUniformPurchaseSwapDto>>> GetExchangeOrder([FromBody] XUniformPurchaseParam param)
        {
            var msgdata = new Result<List<XUniformPurchaseSwapDto>>();
            param.SchoolId = user.UnitId;
            PageModel<XUniformPurchaseSwapDto> pg = await ixuniformpurchaseManager.GetSwapOrderPaged(param);
            if (param.isFirst)
            {
                //状态
                var listSwapStatuz = EnumExtensions.EnumToList<UniformSwapStatuzEnum>();
                List<dropdownModel> listStatuz = listSwapStatuz.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();
                //年度前台处理
                //供应商
                var listCompany = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listSupplier = listCompany.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                msgdata = baseSucc(mapper.Map<List<XUniformPurchaseSwapDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listStatuz = listStatuz, listSupplier = listSupplier });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 供应商管理调换单
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>

        [HttpPost]
        [Route("getmanagerorder")]
        public async Task<Result<List<XUniformPurchaseSwapDto>>> GetManagerOrder([FromBody] XUniformPurchaseParam param)
        {
            var msgdata = new Result<List<XUniformPurchaseSwapDto>>();
            param.CompanyId = user.UnitId;
            PageModel<XUniformPurchaseSwapDto> pg = await ixuniformpurchaseManager.GetSwapOrderPaged(param);
            if (param.isFirst)
            {
                //状态
                var listSwapStatuz = EnumExtensions.EnumToList<UniformSwapStatuzEnum>();
                List<dropdownModel> listStatuz = listSwapStatuz.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();
                //年度前台处理
                //供应商
                var pgSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listSchool = pgSchool.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                msgdata = baseSucc(mapper.Map<List<XUniformPurchaseSwapDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listStatuz = listStatuz, listSchool = listSchool });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 企业按汇总查看调换单
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getsummaryorder")]
        public async Task<Result<List<XUniformChangeDto>>> GetCompanySummaryPaged([FromBody] XUniformSwapDetailParam param)
        {
            var msgdata = new Result<List<XUniformChangeDto>>();
            param.SupplierId = user.UnitId;
            PageModel<XUniformChangeDto> pg = await uniformSwapDetailManager.GetCompanySummaryPaged(param);
            XUniformChangeDto changeSum = new XUniformChangeDto();
            changeSum.Uniformtype = "总计：";
            changeSum.Num = pg.data.Sum(f => f.Num);
            //string headMsg = $"{pg.data[0].SchoolName},,采购批次：{pg.data[0].PurchaseNo}";
            msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { footer = changeSum });
            return msgdata;
        }

        /// <summary>
        /// 企业导出汇总调换明细
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportsummaryorder")]
        public async Task<Result> ExportCompanySummary([FromBody] XUniformSwapDetailParam param)
        {
            Result r = new Result();
            param.SupplierId = user.UnitId;
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<XUniformChangeDto> pg = await uniformSwapDetailManager.GetCompanySummaryPaged(param);
            if (pg.dataCount > 0)
            {
                List<XUniformChangeDto> list = pg.data;
                //加上总计一栏
                list.Add(new XUniformChangeDto()
                {
                    Uniformtype = "总计：",
                    Num = list.Sum(f => f.Num)
                });
                //
                string execlName = $"{pg.data[0].SchoolName}-{pg.data[0].PurchaseNo}-调换明细";
                string file = new ExcelHelper<XUniformChangeDto>().ExportToExcel(env.WebRootPath, execlName + ".xls", execlName,
                                                                                          list,
                                                                                          new string[] { "Uniformtype", "ProductName", "Sex", "NewSizeDes", "Num", "UnitName" });
                r.data.rows = file;
                r.flag = 1;
                r.data.headers = "调换明细";
                r.msg = "导出成功";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }

        /// <summary>
        /// 单位企业根据采购Id查看调换明细(采购Id必传)【单位、企业使用】
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getswapdetail")]
        public async Task<Result<List<XUniformChangeDto>>> GetSwapDetail([FromBody] XUniformSwapDetailParam param)
        {
            var msgdata = new Result<List<XUniformChangeDto>>();
            if (user.UnitTypeId == 3)
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == 4)
            {
                param.SupplierId = user.UnitId;
            }
            else
            {
                param.SchoolId = 0;
            }
            param.SortType = 1;
            XUniformChangeDto changeSum = new XUniformChangeDto();
            changeSum.ClassName = "总计：";
            PageModel<XUniformChangeDto> pg = await uniformSwapDetailManager.GetPaged(param);
            string headMsg = "";
            if (pg.dataCount > 0)
            {
                headMsg = $"采购批次：{pg.data[0].PurchaseNo}";
                changeSum.Num = pg.data.Sum(f => f.Num);
            }
            if (param.isFirst)
            {
                //获取年级
                List<BDictionaryDto> listGrade = await dictionaryManager.GetGradeInfo();
                List<dropdownModel> listGradeDropdown = listGrade.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                //获取班级
                List<BDictionary> listClass = await dictionaryManager.GetByTypeCode("300000");
                List<dropdownModel> listClassDropdown = listClass.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                msgdata = baseSucc(pg.data, pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listGrade = listGradeDropdown, listClass = listClassDropdown, headMsg = headMsg, footer = changeSum });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { headMsg = headMsg, footer = changeSum });
            }
            return msgdata;
        }

        /// <summary>
        /// 单位企业导出调换明细
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportswapdetail")]
        public async Task<Result> ExportSwapDetail([FromBody] XUniformSwapDetailParam param)
        {
            Result r = new Result();
            if (user.UnitTypeId == 3)
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == 4)
            {
                param.SupplierId = user.UnitId;
            }
            else
            {
                param.SchoolId = 0;
            }
            param.SortType = 1;
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<XUniformChangeDto> pg = await uniformSwapDetailManager.GetPaged(param);
            if (pg.dataCount > 0)
            {
                List<XUniformChangeDto> list = pg.data;
                //加上总计一栏
                list.Add(new XUniformChangeDto()
                {
                    ClassName = "总计：",
                    Num = list.Sum(f => f.Num)
                });
                //
                string unitName = string.Empty;
                if (user.UnitTypeId == UnitTypeEnum.School.ObjToInt())
                {
                    unitName = user.UnitName + "-";
                }
                string execlName = $"{unitName}{pg.data[0].PurchaseNo}-调换明细";
                string file = new ExcelHelper<XUniformChangeDto>().ExportToExcel(env.WebRootPath, execlName + ".xls", execlName,
                                                                                          list,
                                                                                          new string[] { "GradeName", "ClassName", "StudentNo", "StudentName", "Sex", "Uniformtype", "ProductName", "OldSizeDes", "NewSizeDes", "Num", "UnitName", "IDCard6", "ParentMobile" });
                r.data.rows = file;
                r.flag = 1;
                r.msg = "导出成功";
                r.data.headers = "调换明细";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }


        /// <summary>
        /// 班主任校服调换列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getteacherorder")]
        public async Task<Result<List<XUniformPurchaseSwapDto>>> GetTeacherOrder([FromBody] XUniformPurchaseParam param)
        {
            var msgdata = new Result<List<XUniformPurchaseSwapDto>>();
            param.TeacherId = user.ID;
            param.SchoolId = user.UnitId;
            PageModel<XUniformPurchaseSwapDto> pg = await ixuniformpurchaseManager.GetSwapOrderByTeacherPaged(param);
            if (param.isFirst)
            {
                //状态
                var listSwapStatuz = EnumExtensions.EnumToList<UniformSwapStatuzEnum>();
                List<dropdownModel> listStatuz = listSwapStatuz.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();
                //年度前台处理
                //供应商
                var listCompany = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listSupplier = listCompany.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                msgdata = baseSucc(mapper.Map<List<XUniformPurchaseSwapDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listStatuz = listStatuz, listSupplier = listSupplier });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 班主任根据采购Id查看调换明细(采购Id必传)【班主任使用】
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getswapdetailbyteacher")]
        public async Task<Result<List<XUniformChangeDto>>> GetSwapDetailByTeacher([FromBody] XUniformSwapDetailParam param)
        {
            var msgdata = new Result<List<XUniformChangeDto>>();
            param.TeacherUserId = user.ID;
            param.SortType = 2;
            PageModel<XUniformChangeDto> pg = await uniformSwapDetailManager.GetPaged(param);
            XUniformChangeDto changeSum = new XUniformChangeDto();
            changeSum.StudentName = "总计：";
            if (pg.dataCount > 0)
            {
                changeSum.Num = pg.data.Sum(f => f.Num);

                string headMsg = $"{pg.data[0].GradeName},{pg.data[0].ClassName},采购批次：{pg.data[0].PurchaseNo}";
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { headMsg = headMsg, footer = changeSum });
            }
            return msgdata;
        }

        /// <summary>
        /// 班主任导出调换明细
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportbyteacher")]
        public async Task<Result> ExportSwapDetailByTeacher([FromBody] XUniformSwapDetailParam param)
        {
            Result r = new Result();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            param.TeacherUserId = user.ID;
            param.SortType = 2;
            param.SchoolId = -10000;
            PageModel<XUniformChangeDto> pg = await uniformSwapDetailManager.GetPaged(param);

            if (pg.dataCount > 0)
            {
                List<XUniformChangeDto> list = pg.data;
                //加上总计一栏
                list.Add(new XUniformChangeDto()
                {
                    SchoolName = "总计：",
                    Num = list.Sum(f => f.Num)
                });
                //
                string execlName = $"{pg.data[0].PurchaseNo}-{pg.data[0].GradeName}({pg.data[0].ClassName})-调换明细";
                string file = new ExcelHelper<XUniformChangeDto>().ExportToExcel(env.WebRootPath, execlName+".xls", execlName,
                                                                                          list,
                                                                                          new string[] { "StudentNo", "StudentName", "Sex", "Uniformtype", "ProductName", "OldSizeDes", "NewSizeDes", "Num", "UnitName", "IDCard6", "ParentMobile" });
                r.data.rows = file;
                r.flag = 1;
                r.msg = "导出成功";
                r.data.headers = "调换明细";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }


        /// <summary>
        /// 获取家长调换批次列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getchangelist")]
        public async Task<Result<List<XUniformChangeParentDto>>> GetChangeParentList()
        {
            var msgdata = new Result<List<XUniformChangeParentDto>>();
            List<XUniformChangeParentDto> list = await uniformSwapDetailManager.GetChangeParentList(user.ID);
            msgdata = baseSucc(list, list.Count, "查询成功");
            return msgdata;
        }

        /// <summary>
        /// 家长单条调换校服
        /// </summary>
        /// <param name="o">
        /// UniformChangeModel 实体
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("change")]
        public async Task<Result<string>> UniformChange([FromBody] UniformChangeModel o)
        {
            o.UserId = user.ID;
            var r = await uniformSwapDetailManager.UniformChange(o);
            return r;
        }

        /// <summary>
        /// 企业确认调换
        /// </summary>
        /// <param name="uniformPurchaseId">校服采购表Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("confirm")]
        public async Task<Result<string>> CompanyConfirm(long uniformPurchaseId)
        {
            var r = await uniformSwapDetailManager.CompanyConfirm(uniformPurchaseId, user.UnitId);
            return r;
        }
        #endregion

        #region 校服资助 zyf

        /// <summary>
        /// 单位校服资助列表显示
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getschoolsponsorpaged")]
        public async Task<Result<List<XuniformSponsorDto>>> GetSchoolSponsorPaged([FromBody] XUniformSponsorParam param)
        {
            param.unitId = user.UnitId;
            var msgdata = new Result<List<XuniformSponsorDto>>();
            PageModel<XuniformSponsorDto> pg = await uniformSponsorManager.GetSchoolSponsorPaged(param);
            if (param.isFirst)
            {
                var list = await ixuniformpurchaseManager.Query(f => f.OrderNum > 0 && f.SchoolId == user.UnitId && f.IsDeleted == false);
                List<dropdownModel> listPurchaseNo = list.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.PurchaseNo, pid = f.OrderNum }).ToList();
                msgdata = baseSucc(mapper.Map<List<XuniformSponsorDto>>(pg.data), pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { listPurchaseNo = listPurchaseNo});
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }


        /// <summary>
        /// 校服资助列表区县、市级
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getsponsorpaged")]
        public async Task<Result<List<XuniformSponsorDto>>> GetSponsorPaged([FromBody] XUniformPurchaseParam param)
        {
            var msgdata = new Result<List<XuniformSponsorDto>>();
            if (user.UnitTypeId == 1)
            {
                param.CityId = user.UnitId;
            }
            else if (user.UnitTypeId == 2)
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                param.SchoolId = 0;
            }
            PageModel<XuniformSponsorDto> pg = await ixuniformpurchaseManager.GetSponsorPaged(param);
            if (param.isFirst)
            {
                if (user.UnitTypeId == 2)
                {
                    List<PUnit> listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1 && f.PId == user.UnitId);
                    List<dropdownModel> listSchoolDrop = listSchool.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();
                    msgdata = baseSucc(mapper.Map<List<XuniformSponsorDto>>(pg.data), pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { listSchool = listSchoolDrop });
                }
                else if (user.UnitTypeId == 1)
                {
                    List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listCounty = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        //获取所有区域Id
                        var listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                        foreach (var item in listCounty)
                        {
                            string name = item.Name;
                            if (listArea != null && listArea.Where(m => m.Id == item.AreaId).Count() > 0)
                            {
                                name = listArea.Where(m => m.Id == item.AreaId).FirstOrDefault().Name;
                            }
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = name });
                        }
                    }
                    msgdata = baseSucc(mapper.Map<List<XuniformSponsorDto>>(pg.data), pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { listCounty = dropdownCounty, listSchool = dropdownSchool });
                }
                else
                {
                    msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
                }
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }


        /// <summary>
        /// 根据校服采购表Id获取校服资助表明细信息
        /// </summary>
        /// <param name="uniformPurchaseId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsponsordetailpaged")]
        public async Task<Result<List<XUniformSponsor>>> GetSponsorDetailPaged(long uniformPurchaseId)
        {
            var msgdata = new Result<List<XUniformSponsor>>();
            PageModel<XUniformSponsor> pg = new PageModel<XUniformSponsor>();
            pg.data = await uniformSponsorManager.GetPagedById(uniformPurchaseId);
            pg.dataCount=pg.data.Count;
            msgdata = baseSucc(pg.data, pg.dataCount);
            return msgdata;

        }

        /// <summary>
        /// 创建资助信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("sponsoradd")]
        public async Task<Result<string>> SponsorAdd([FromBody] XuniformSponsorModel o)
        {
            var r = await uniformSponsorManager.InsertUpdate(o);
            return r;
        }

        /// <summary>
        /// 修改资助信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("sponsoredit")]
        public async Task<Result<string>> SponsorEdit([FromBody] XuniformSponsorModel o)
        {
            var r = await uniformSponsorManager.InsertUpdate(o);
            return r;
        }


        /// <summary>
        /// 根据校服采购Id获取校服资助详情
        /// </summary>
        /// <param name="id">校服采购id</param>
        /// <param name="isFirst">是否第一次调用</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsponsorbyid")]
        public async Task<Result<XUniformSponsor>> GetSponsorById(long id, bool isFirst = false)
        {
            Result<XUniformSponsor> r = new Result<XUniformSponsor>();
            if (isFirst)
            {
                var list = await ixuniformpurchaseManager.Query(f => f.OrderNum > 0 && f.SchoolId == user.UnitId && f.IsDeleted == false && f.ContractEndDate >= DateTime.Now.Date);
                List<dropdownModel> listPurchaseNo = list.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.PurchaseNo, pid = f.OrderNum }).ToList();
                r.data.other = new { listPurchaseNo = listPurchaseNo };
            }
            else
            {
                XUniformSponsor obj = await uniformSponsorManager.GetById(id);
                r.data.rows = obj;
            }
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }

        /// <summary>
        /// 删除资助信息
        /// </summary>
        /// <param name="id">校服采购Id</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delsponsorbyid")]
        public async Task<Result<string>> DeleteSponsorById(long id)
        {
            var r = await uniformSponsorManager.FakeDeleteById(id);
            return r;
        }

        /// <summary>
        /// 删除校服资助附件
        /// </summary>
        /// <param name="id">附件Id</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delfilebyid")]
        public async Task<Result<string>> DeleteFileById(long id)
        {
            var r = await ixuniformpurchaseManager.SponsorFileDelete(id);
            return r;
        }
        #endregion

        #region 校服征订 2024-08-21 by lss
        /// <summary>
        /// 校服征订-生成征订单
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("geteditorderpaged")]
        public async Task<Result<List<XUniformPurchaseDto>>> GetEditOrderPaged([FromBody] XUniformPurchaseParam param)
        {
            Result<List<XUniformPurchaseDto>> r = new Result<List<XUniformPurchaseDto>>();
            param.UnitType = user.UnitTypeId;
            // param.role = user.UnitId;"UserRoleIds: 30,370"
            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }
            if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
            {
                param.SchoolId = user.UnitId;
            }
            else
            {
                return baseFailed<List<XUniformPurchaseDto>>("非法操作，你无权访问！");
            }
            param.IsRelatedUniform = 1;
            PageModel<XUniformPurchaseDto> pg = await ixuniformpurchaseManager.GetEditOrderPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            List<BArea> listArea = null;
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
                {
                    r.data.rows = (from item in pg.data
                                   select new XUniformPurchaseDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SupplierId = item.SupplierId,
                                       PurchaseYear = item.PurchaseYear,
                                       PurchaseNo = item.PurchaseNo,
                                       IsContractRenewal = item.IsContractRenewal,
                                       ContractStartDate = item.ContractStartDate,
                                       ContractEndDate = item.ContractEndDate,
                                       OrderNum = item.OrderNum,
                                       OrderedNum = item.OrderedNum,
                                       SubscriptionStatuz = item.SubscriptionStatuz,
                                       IsFiling = item.IsFiling,
                                       IsRelatedUniform = item.IsRelatedUniform,
                                       IsCountyManager = item.IsCountyManager,
                                       SubscriptionStatuzName = ((UniformSubscriptionStatuzEnum)item.SubscriptionStatuz).GetDescription(),
                                       SupplierName = item.SupplierName,
                                       SubscriptionDeadline = item.SubscriptionDeadline
                                   }).ToList();
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    r.data.rows = (from item in pg.data
                                   select new XUniformPurchaseDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SupplierId = item.SupplierId,
                                       SchoolName = item.SchoolName,
                                       PurchaseYear = item.PurchaseYear,
                                       PurchaseNo = item.PurchaseNo,
                                       IsContractRenewal = item.IsContractRenewal,
                                       ContractStartDate = item.ContractStartDate,
                                       ContractEndDate = item.ContractEndDate,
                                       OrderNum = item.OrderNum,
                                       OrderedNum = item.OrderedNum,
                                       SubscriptionStatuz = item.SubscriptionStatuz,
                                       IsFiling = item.IsFiling,
                                       IsRelatedUniform = item.IsRelatedUniform,
                                       IsCountyManager = item.IsCountyManager,
                                       SubscriptionStatuzName = ((UniformSubscriptionStatuzEnum)item.SubscriptionStatuz).GetDescription(),
                                       SupplierName = item.SupplierName,
                                       SubscriptionDeadline = item.SubscriptionDeadline
                                   }).ToList();
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    listArea = await ibareaManager.Find(f => f.Pid == user.AreaId);
                    r.data.rows = (from item in pg.data
                                   select new XUniformPurchaseDto
                                   {
                                       Id = item.Id,
                                       SchoolId = item.SchoolId,
                                       SupplierId = item.SupplierId,
                                       CountyName = GetAreaName(item.CountyAreaId, item.CountyName, listArea),
                                       SchoolName = item.SchoolName,
                                       PurchaseYear = item.PurchaseYear,
                                       PurchaseNo = item.FilingStatuz == UniformFilingEnum.SubmitNone.ObjToInt() ? "--" : item.PurchaseNo,
                                       IsContractRenewal = item.IsContractRenewal,
                                       ContractStartDate = item.ContractStartDate,
                                       ContractEndDate = item.ContractEndDate,
                                       OrderNum = item.OrderNum,
                                       OrderedNum = item.OrderedNum,
                                       SubscriptionStatuz = item.SubscriptionStatuz,
                                       IsFiling = item.IsFiling,
                                       IsRelatedUniform = item.IsRelatedUniform,
                                       IsCountyManager = item.IsCountyManager,
                                       SubscriptionStatuzName = ((UniformSubscriptionStatuzEnum)item.SubscriptionStatuz).GetDescription(),
                                       SupplierName = item.SupplierName,
                                       SubscriptionDeadline = item.SubscriptionDeadline
                                   }).ToList();
                }
            }
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                //是否续签合同
                List<dropdownModel> dropIsContractRenewal = new List<dropdownModel>();
                var listIsStatuz = EnumExtensions.EnumToList<StatuzIsEnum>();
                if (listIsStatuz != null)
                {
                    foreach (var item in listIsStatuz)
                    {
                        dropIsContractRenewal.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }

                //备案状态
                List<dropdownModel> dropStatuzFiling = new List<dropdownModel>();
                var listFilingStatuz = EnumExtensions.EnumToList<UniformFilingEnum>();
                if (listFilingStatuz != null)
                {
                    foreach (var item in listFilingStatuz)
                    {
                        dropStatuzFiling.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }


                if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
                {
                    //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                    List<dropdownModel> dropdownSupplier = new List<dropdownModel>();
                    var listSupplier = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSupplier != null)
                    {
                        foreach (var item in listSupplier)
                        {
                            dropdownSupplier.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }

                    r.data.other = new { SupplierList = dropdownSupplier };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listSchool = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSchool != null)
                    {
                        foreach (var item in listSchool)
                        {
                            dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                    r.data.other = new { StatuzIsRenewal = dropIsContractRenewal, StatuzFiling = dropStatuzFiling, SchoolList = dropdownSchool };

                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listCounty = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        //获取所有区域Id
                        if (listArea == null)
                        {
                            listArea = await ibareaManager.Find(f => f.Pid == user.AreaId);
                        }
                        foreach (var item in listCounty)
                        {
                            string name = item.Name;
                            if (listArea != null && listArea.Where(m => m.Id == item.AreaId).Count() > 0)
                            {
                                name = listArea.Where(m => m.Id == item.AreaId).FirstOrDefault().Name;
                            }
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = GetAreaName(item.AreaId, item.Name, listArea) });
                        }
                    }

                    r.data.other = new { StatuzIsRenewal = dropIsContractRenewal, StatuzFiling = dropStatuzFiling, CountyList = dropdownCounty, SchoolList = dropdownSchool };

                }
            }
            return r;
        }


        /// <summary>
        /// 校服征订-获取征订单信息
        /// </summary>
        /// <param name="id">采购id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getcreateorder")]
        public async Task<Result<List<XUniformShelfDto>>> GetCreateOrder(long id)
        {
            Result<List<XUniformShelfDto>> r = new Result<List<XUniformShelfDto>>();
            var entity = await ixuniformpurchaseManager.GetById(id);

            if (entity != null)
            {
                //获取采购单下的校服清单列表。
                var listUniformShelf = await uniformshelfManager.Find(a => a.UniformPurchaseId == entity.Id);
                if (listUniformShelf == null || listUniformShelf.Count == 0)
                {
                    r.flag = 1;
                    r.msg = "获取失败，没有需要生成的校服信息";
                    return r;
                }
                r.flag = 1;
                r.msg = "获取成功";
                r.data.rows = (from item in listUniformShelf
                               select new XUniformShelfDto()
                               {
                                   Id = item.Id,
                                   Uniformtype = item.Uniformtype,
                                   Name = item.Name,
                                   Brand = item.Brand,
                                   Sex = item.Sex,
                                   StandardNum = item.StandardNum,
                                   Price = item.Price,
                                   UnitName = item.UnitName,

                               }).ToList();
                List<XUniformPurchaseGrade> listPurchaseGrade = null;
                if (entity.SubscriptionStatuz == UniformSubscriptionStatuzEnum.Subscription.ObjToInt())
                {
                    listPurchaseGrade = await uniformpurchasegradeManager.Find(m => m.UniformPurchaseId == entity.Id && m.IsDeleted == false);
                }
                //获取年级集合
                List<dropdownModel> dropdownGrade = new List<dropdownModel>();
                var listGrade = await dictionaryManager.GetGradeInfo();
                //获取单位学段集合
                string Period = "";
                var listSchoolExt = await schoolExtensionManager.Find(m => m.UnitId == user.UnitId && m.IsDeleted == false);
                if (listSchoolExt != null && listSchoolExt.Count > 0)
                {
                    Period = listSchoolExt.FirstOrDefault().Period;
                }
                if (listGrade != null && listGrade.Count > 0)
                {
                    foreach (var item in listGrade)
                    {
                        if (!string.IsNullOrEmpty(Period))
                        {
                            if (Period.Contains(item.StageId.ToString()))
                            {
                                bool selected = false;
                                if (listPurchaseGrade != null && listPurchaseGrade.Count > 0)
                                {
                                    if (listPurchaseGrade.Where(m => m.GradeId.ToString() == item.DicValue).Count() > 0)
                                    {
                                        selected = true;
                                    }
                                }
                                dropdownGrade.Add(new dropdownModel() { value = item.DicValue, label = item.DicName, selected = selected });
                            }
                        }
                    }
                }
                r.data.other = new { GradeList = dropdownGrade, SubscriptionDeadline = entity.SubscriptionDeadline, PurchaseNo = entity.PurchaseNo };
            }
            return r;
        }

        /// <summary>
        /// 校服征订-保存征订单信息
        /// </summary>
        /// <param name="model">生成征订单信息{id:采购表Id ,GradeList选中年级Id集合}</param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveeditorder")]
        public async Task<Result> SaveEditOrder([FromBody] XUniformPurchaseDto model)
        {
            Result r = new Result();
            model.SchoolId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformpurchaseManager.SaveOrder(model);
            return r;
        }

        /// <summary>
        /// 校服征订-查看征订单
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getorderpaged")]
        public async Task<Result<List<XUniformPurchaseDto>>> GetOrderPaged([FromBody] XUniformPurchaseParam param)
        {
            Result<List<XUniformPurchaseDto>> r = new Result<List<XUniformPurchaseDto>>();
            param.UnitType = user.UnitTypeId;
            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }
            if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()) || param.UserRoleIdList.Contains(RoleTypes.ClassTeacher.ObjToInt().ToString()))
            {
                if (user.UnitId == 0)
                {
                    param.SchoolId = 1;//这里因为班主任未绑定班级，所有没有单位，所有给个默认值，因为判断室 > 0 .这个Id是long类型，所有给个1可以解决
                }
                else
                {
                    param.SchoolId = user.UnitId;
                }
         
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
            {
                param.CountyId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
            {
                param.CityId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CompanyAdmin.ObjToInt().ToString()))
            {
                param.unitId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.Parent.ObjToInt().ToString()))
            {
                //家长，根据学生查单位
                param.userId = user.UserId;
            }
            else
            {
                return baseFailed<List<XUniformPurchaseDto>>("非法操作，你无权访问！");
            }
            param.SubscriptionStatuzLg = UniformSubscriptionStatuzEnum.Subscription.ObjToInt();
            PageModel<XUniformPurchaseDto> pg = await ixuniformpurchaseManager.GetEditOrderPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            List<BArea> listArea = null;
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                r.data.total = pg.dataCount;
                r.data.rows = (from item in pg.data
                               select new XUniformPurchaseDto
                               {
                                   Id = item.Id,
                                   SchoolId = item.SchoolId,
                                   SupplierId = item.SupplierId,
                                   PurchaseYear = item.PurchaseYear,
                                   PurchaseNo = item.PurchaseNo,
                                   IsContractRenewal = item.IsContractRenewal,
                                   ContractStartDate = item.ContractStartDate,
                                   ContractEndDate = item.ContractEndDate,
                                   SubscriptionStatuz = item.SubscriptionDeadline >= DateTime.Now.Date ? item.SubscriptionStatuz : UniformSubscriptionStatuzEnum.Finish.ObjToInt(),
                                   OrderNum = item.OrderNum,
                                   OrderedNum = item.OrderedNum,
                                   IsFiling = item.IsFiling,
                                   IsRelatedUniform = item.IsRelatedUniform,
                                   IsCountyManager = item.IsCountyManager,
                                   SubscriptionStatuzName = item.SubscriptionDeadline >= DateTime.Now.Date ? ((UniformSubscriptionStatuzEnum)item.SubscriptionStatuz).GetDescription() : UniformSubscriptionStatuzEnum.Finish.GetDescription(),
                                   SupplierName = item.SupplierName,
                                   SchoolName = item.SchoolName,
                                   CountyName = GetAreaName(item.CountyAreaId, item.CountyName, listArea),
                                   CountyAreaId = item.CountyAreaId,
                                   SubscriptionDeadline = item.SubscriptionDeadline
                               }).ToList();
            }

            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                List<dropdownModel> dropdownSupplier = new List<dropdownModel>();
                if (!param.UserRoleIdList.Contains(RoleTypes.CompanyAdmin.ObjToInt().ToString()))
                {
                    var listSupplier = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSupplier != null)
                    {
                        foreach (var item in listSupplier)
                        {
                            dropdownSupplier.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                }

                //征订状态
                List<dropdownModel> dropStatuzSubscription = new List<dropdownModel>();
                var listSubscriptionStatuz = EnumExtensions.EnumToList<UniformSubscriptionStatuzEnum>();
                if (listSubscriptionStatuz != null)
                {
                    foreach (var item in listSubscriptionStatuz)
                    {
                        if (item.Value >= UniformSubscriptionStatuzEnum.Subscription.ObjToInt())
                        {
                            dropStatuzSubscription.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                        }
                    }
                }

                List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()) || param.UserRoleIdList.Contains(RoleTypes.CompanyAdmin.ObjToInt().ToString()))
                {
                    List<PUnit> listSchool = null;
                    if (param.UserRoleIdList.Contains(RoleTypes.CompanyAdmin.ObjToInt().ToString()))
                    {
                        listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    }
                    else
                    {
                        listSchool = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    }
                    if (listSchool != null)
                    {
                        foreach (var item in listSchool)
                        {
                            dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                }

                List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    var listCounty = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        if (listArea == null)
                        {
                            //获取所有区域Id
                            listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                        }
                        foreach (var item in listCounty)
                        {
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = GetAreaName(item.AreaId, item.Name, listArea) });
                        }
                    }
                }
                r.data.other = new { SupplierList = dropdownSupplier, SubscriptionStatuzList = dropStatuzSubscription };
                if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    r.data.other = new { SchoolList = dropdownSchool, CountyList = dropdownCounty, SupplierList = dropdownSupplier, SubscriptionStatuzList = dropStatuzSubscription };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    r.data.other = new { SchoolList = dropdownSchool, CountyList = dropdownCounty, SupplierList = dropdownSupplier, SubscriptionStatuzList = dropStatuzSubscription };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CompanyAdmin.ObjToInt().ToString()))
                {
                    r.data.other = new { SchoolList = dropdownSchool, SubscriptionStatuzList = dropStatuzSubscription };
                }
            }
            return r;
        }

        /// <summary>
        /// 获取区域名称
        /// </summary>
        /// <param name="countyAreaId">区县区域Id</param>
        /// <param name="name">区县单位名称，默认值</param>
        /// <param name="listArea">区域集合</param>
        /// <returns></returns>
        private string GetAreaName(long countyAreaId, string name, List<BArea> listArea)
        {
            if (listArea != null && listArea.Where(m => m.Id == countyAreaId).Count() > 0)
            {
                name = listArea.Where(m => m.Id == countyAreaId).FirstOrDefault().Name;
            }
            return name;
        }


        /// <summary>
        /// 校服征订-采购结果        
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getparentpaged")]
        public async Task<Result<List<XUniformPurchaseDto>>> GetParentPaged([FromBody] XUniformPurchaseParam param)
        {
            Result<List<XUniformPurchaseDto>> r = new Result<List<XUniformPurchaseDto>>();

            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }
            if (param.UserRoleIdList.Contains(RoleTypes.Parent.ObjToInt().ToString()))
            {
                //家长，根据学生查单位
                param.userId = user.UserId;
            }
            else
            {
                return baseFailed<List<XUniformPurchaseDto>>("非法操作，你无权访问！");
            }

            PageModel<XUniformPurchaseDto> pg = await ixuniformpurchaseManager.GetParentPaged(param);

            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
                r.data.total = pg.dataCount;

                r.data.rows = (from item in pg.data
                               select new XUniformPurchaseDto
                               {
                                   Id = item.Id,
                                   PurchaseNo = item.PurchaseNo,
                                   SchoolName = item.SchoolName,
                                   ContractStartDate = item.ContractStartDate,
                                   ContractStartDateStr = item.ContractStartDate == null ? "--" : ((DateTime)item.ContractStartDate).ToString("yyyy-MM-dd"),
                                   SubscriptionDeadline = item.SubscriptionDeadline,
                                   FilingStatuz = item.FilingStatuz,
                                   IsFiling = item.IsFiling,
                                   IsRelatedUniform = item.IsRelatedUniform,
                                   IsCountyManager = item.IsCountyManager,
                                   FilingStatuzName = GetFilingStatuzPublicityName(item),
                                   IsPublicity = item.IsPublicity
                               }).ToList();
            }
            r.flag = 1;
            r.msg = "获取成功！";
            return r;
        }


        /// <summary>
        /// 方案选用-获取当前校服选用公示状态名称
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private string GetFilingStatuzPublicityName(XUniformPurchaseDto item)
        {
            string name = "--";
            item.IsPublicity = 0;
            if (item.IsCountyManager == 0)
            {
                name = "等待公示";
                if (item.SubscriptionDeadline < DateTime.Now.Date)
                {
                    name = "公示结果";
                    item.IsPublicity = 1;
                }
            }
            else
            {
                name = "等待公示";
                if (item.FilingStatuz > UniformFilingEnum.SubmitNone.ObjToInt())
                {
                    name = "公示结果";
                    item.IsPublicity = 1;
                }
            }
            return name;
        }

        #endregion

        #region 班主任，查看征订单

        /// <summary>
        /// 校服征订-查看征订单(班主任)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getorderteacherpaged")]
        public async Task<Result<List<XUniformPurchaseDto>>> GetOrderTeacherPaged([FromBody] XUniformPurchaseParam param)
        {
            Result<List<XUniformPurchaseDto>> r = new Result<List<XUniformPurchaseDto>>();
            param.UnitType = user.UnitTypeId;
            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }
            param.userId = user.UserId;
            param.SubscriptionStatuzLg = UniformSubscriptionStatuzEnum.Subscription.ObjToInt();
            PageModel<XUniformPurchaseDto> pg = await ixuniformpurchaseManager.GetOrderTeacherPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {

                var listProduct = await uniformparentpurchaseManager.Find(m => m.IsDeleted == false && pg.data.Select(m => m.Id).Contains(m.UniformPurchaseId) && pg.data.Select(m => m.ClassInfoId).Contains(m.UniformClassId));
                r.data.total = pg.dataCount;
                r.data.rows = (from item in pg.data
                               select new XUniformPurchaseDto
                               {
                                   Id = item.Id,
                                   SchoolId = item.SchoolId,
                                   SupplierId = item.SupplierId,
                                   PurchaseYear = item.PurchaseYear,
                                   PurchaseNo = item.PurchaseNo,
                                   IsContractRenewal = item.IsContractRenewal,
                                   ContractStartDate = item.ContractStartDate,
                                   ContractEndDate = item.ContractEndDate,
                                   SubscriptionStatuz = item.SubscriptionDeadline <= DateTime.Now.Date ? item.SubscriptionStatuz : UniformSubscriptionStatuzEnum.Finish.ObjToInt(),
                                   OrderNum = item.OrderNum,
                                   OrderedNum = GetOrderNumed(listProduct, item.Id, item.ClassInfoId),
                                   IsFiling = item.IsFiling,
                                   IsRelatedUniform = item.IsRelatedUniform,
                                   IsCountyManager = item.IsCountyManager,
                                   SubscriptionStatuzName = item.SubscriptionDeadline <= DateTime.Now.Date ? ((UniformSubscriptionStatuzEnum)item.SubscriptionStatuz).GetDescription() : UniformSubscriptionStatuzEnum.Finish.GetDescription(),
                                   SupplierName = item.SupplierName,
                                   SchoolName = item.SchoolName,
                                   SubscriptionDeadline = item.SubscriptionDeadline,
                                   GradeName = item.GradeName,
                                   ClassName = item.ClassName,
                                   ClassInfoId = item.ClassInfoId
                               }).ToList();
            }

            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                List<dropdownModel> dropdownSupplier = new List<dropdownModel>();
                if (!param.UserRoleIdList.Contains(RoleTypes.CompanyAdmin.ObjToInt().ToString()))
                {
                    var listSupplier = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSupplier != null)
                    {
                        foreach (var item in listSupplier)
                        {
                            dropdownSupplier.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                }
                //征订状态
                List<dropdownModel> dropStatuzSubscription = new List<dropdownModel>();
                var listSubscriptionStatuz = EnumExtensions.EnumToList<UniformSubscriptionStatuzEnum>();
                if (listSubscriptionStatuz != null)
                {
                    foreach (var item in listSubscriptionStatuz)
                    {
                        if (item.Value >= UniformSubscriptionStatuzEnum.Subscription.ObjToInt())
                        {
                            dropStatuzSubscription.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                        }
                    }
                }

                r.data.other = new { SupplierList = dropdownSupplier, SubscriptionStatuzList = dropStatuzSubscription };
            }
            return r;
        }

        private int GetOrderNumed(List<XUniformParentPurchase> list, long purchaseid, long classinfoid)
        {
            int num = 0;
            if (list != null && list.Count > 0)
            {
                num = list.Where(n => n.UniformPurchaseId == purchaseid && n.UniformClassId == classinfoid).GroupBy(n => n.StudentId).Count();
            }
            return num;
        }



        #endregion
    }
}
