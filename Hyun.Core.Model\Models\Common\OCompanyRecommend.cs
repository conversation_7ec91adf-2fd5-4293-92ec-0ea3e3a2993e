namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///企业推荐模块o_CompanyRecommend
    ///</summary>
    [SugarTable("o_CompanyRecommend","企业推荐模块o_CompanyRecommend")]
    public class OCompanyRecommend : BaseEntity
    {

          public OCompanyRecommend()
          {

          }

           /// <summary>
           ///推荐人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///企业名称
          /// </summary>
          [SugarColumn(Length = 255)]
          public string Name { get; set; }

           /// <summary>
           ///企业联系人
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string LinkMan { get; set; }

           /// <summary>
           ///企业联系人电话
          /// </summary>
          [SugarColumn(Length = 63)]
          public string Tel { get; set; }

           /// <summary>
           ///企业类型[1：服务商，2：配件商]
          /// </summary>
          public int CompanyType { get; set; }

           /// <summary>
           ///推荐时间
          /// </summary>
          public DateTime RegTime { get; set; }

           /// <summary>
           ///状态(0：提交；1：区县审核通过；2区县审核不通；3：系统已经处理)
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? Statuz { get; set; }

           /// <summary>
           ///处理人
          /// </summary>
          public long OptId { get; set; }

           /// <summary>
           ///处理人备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///处理时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? OptTime { get; set; }

           /// <summary>
           ///法人
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string Legal { get; set; }

           /// <summary>
           ///短信服务手机号
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string Mobile { get; set; }

           /// <summary>
           ///地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Address { get; set; }

           /// <summary>
           ///联系电话
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string Tel2 { get; set; }

           /// <summary>
           ///简称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Brief { get; set; }

           /// <summary>
           ///网址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Url { get; set; }

           /// <summary>
           ///服务项目，逗号分隔
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string ServiceItemId { get; set; }

           /// <summary>
           ///服务范围（0：无；1：单位；2：区县）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? Range { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

