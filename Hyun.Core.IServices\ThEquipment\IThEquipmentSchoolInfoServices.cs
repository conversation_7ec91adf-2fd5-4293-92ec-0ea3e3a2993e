﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///ThEquipmentSchoolInfo接口方法
    ///</summary>
    public interface IThEquipmentSchoolInfoServices : IBaseServices<ThEquipmentSchoolInfo>
    {

       /// <summary>
       /// 新增修改
       /// </summary>
       /// <param name="o">ThEquipmentSchoolInfo对象</param>
       /// <returns></returns>
       Task<Result> InsertUpdate(ThEquipmentSchoolInfo o);

       /// <summary>
       /// 批量新增
       /// </summary>
       /// <param name="o">List<ThEquipmentSchoolInfo>对象</param>
       /// <returns></returns>
       Task<Result> BatchAdd(List<ThEquipmentSchoolInfo> list);

       /// <summary>
       /// 根据Id删除数据【假删除】
       /// </summary>
       /// <param name="id">Id值</param>
       /// <returns></returns>
       Task<Result> FakeDeleteById(long id);

       /// <summary>
       /// 根据Id集合批量删除数据【假删除】
       /// </summary>
       ///  <param name="ids">id集合逗号分隔</param>
       /// <returns></returns>
       Task<Result> FakeDeleteByIds(string ids);

       /// <summary>
       /// 根据Id删除数据【真删除】
       /// </summary>
       /// <param name="id">Id值</param>
       /// <returns></returns>
       Task<Result> DeleteById(long id);

       /// <summary>
       /// 根据Id集合批量删除数据【真删除】
       /// </summary>
       /// <param name="ids">id集合逗号分隔</param>
       /// <returns></returns>
       Task<Result> DeleteByIds(string ids);

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<ThEquipmentSchoolInfo> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<ThEquipmentSchoolInfo>> Find(Expression<Func<ThEquipmentSchoolInfo, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">ThEquipmentSchoolInfoParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<ThEquipmentSchoolInfo>> GetPaged(ThEquipmentSchoolInfoParam param);

    }
}

