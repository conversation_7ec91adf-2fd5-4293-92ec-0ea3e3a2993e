﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hyun.Core.Model.Model
{
    /// <summary>
    /// 下拉框型数据Model
    /// </summary>
    public class XaProcessPageCfgDataModel
    {
        /// <summary>
        /// id
        /// </summary>
        public int id { get; set; }

        /// <summary>
        /// text
        /// </summary>
        public string text { get; set; }

        /// <summary>
        /// pid
        /// </summary>
        public int pid { get; set; }

        /// <summary>
        /// pname
        /// </summary>
        public string pname { get; set; }

        /// <summary>
        /// value
        /// </summary>
        public string value { get; set; }
    }

    /// <summary>
    /// 待配置的字典项目
    /// </summary>
    public class XaDicClassInfo
    {
        /// <summary>
        /// 字典名称分类
        /// </summary>
        public string TypeName { get; set; }
        /// <summary>
        /// 字典分类编码
        /// </summary>
        public string TypeCode { get; set; }

        /// <summary>
        /// 使用单位Id
        /// </summary>
        public int UseUnitId { get; set; }

        /// <summary>
        /// 使用单位名称       
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// 流程Id
        /// </summary>
        public int ProcessNameId { get; set; }

        /// <summary>
        /// 流程名称
        /// </summary>
        public string ProcessName { get; set; }

    }

    public class XaProjectCfgExtend
    {
        /// <summary>
        /// 
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int ProcessDefinitionId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int ProjectApprovalId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int ProcessPageCfgId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string InfoValue { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string InfoText { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FieldCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FieldName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ShowName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int TypeBox { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int FieldSource { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public int ShowStatuz { get; set; }


    }

    public class GridFeild
    {
        /// <summary>
        /// 字段
        /// </summary>
        public string field { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public string title { get; set; }

        /// <summary>
        /// 排序int值
        /// </summary>
        public int IsSort { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public bool sortable { get; set; }

        /// <summary>
        /// 宽度
        /// </summary>
        public int width { get; set; }

        /// <summary>
        /// 标题对齐方式
        /// </summary>
        public string halign { get; set; }

        /// <summary>
        /// 内容对齐方式
        /// </summary>
        public string align { get; set; }
    }

    /// <summary>
    /// 表格统计
    /// </summary>
    public class GridTable
    {
        /// <summary>
        /// 总计数量
        /// </summary>
        public int RowCount { get; set; }

        /// <summary>
        /// 总计金额
        /// </summary>
        public double TotalMoney { get; set; }
    }

    public class XaGridFeild : GridFeild
    {
        /// <summary>
        /// 类型
        /// </summary>
        public int TypeBox { get; set; }
    }

    #region 型号、品牌配置列表返回实体类
    /// <summary>
    /// 型号、品牌配置列表返回实体类
    /// </summary>
    public class ModelBrandConfigListViewModel
    {
        /// <summary>
        /// 单位物品基础库Id
        /// </summary>
        public int SchoolCatalogId { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        public string SchoolCatalogName { get; set; }

        /// <summary>
        /// 型号基础库Id
        /// </summary>
        public int BaseId { get; set; }

        /// <summary>
        /// 型号自定义库名称
        /// </summary>
        public int ModelId { get; set; }

        /// <summary>
        /// 型号名称
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 品牌自定义库Id
        /// </summary>
        public int BrandId { get; set; }

        /// <summary>
        /// 品牌名称
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        /// 一级分类
        /// </summary>
        public int OneCatalogId { get; set; }

        /// <summary>
        /// 二级分类
        /// </summary>
        public int TwoCatalogId { get; set; }
    }
    #endregion

    /// <summary>
	/// 看板数据分析
	/// </summary>
	public class KbDcData
    {
        /// <summary>
        /// 年
        /// </summary>
        public int Y { get; set; }
        /// <summary>
        /// 月
        /// </summary>
        public int M { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int Num { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }
    }

    /// <summary>
    /// 看板数据分析
    /// </summary>
    public class KbDcSchoolData
    {

        /// <summary>
        /// 单位Id
        /// </summary>
        public int SchoolId { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int Num { get; set; }

    }

    /// <summary>
    /// 饼图数据
    /// </summary>
    public class KbDcPieData
    {
        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int value { get; set; }
    }

    /// <summary>
    /// 调用外部资产接口数据信息
    /// </summary>
    public class SchoolModel
    {
        /// <summary>
        /// 区县名称，如：
        /// </summary>
        public string countryName { get; set; }

        /// <summary>
        /// 单位Id
        /// </summary>
        public long schoolId { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string schoolName { get; set; }

        /// <summary>
        /// 单位Guid
        /// </summary>
        public string schoolGuid { get; set; }
    }

    /// <summary>
	/// 字典实体
	/// </summary>
	public class DictionaryModel
    {

        /// <summary>
        /// 一级分类名称
        /// </summary>
        public string OneName { get; set; }

        /// <summary>
        /// 二级分类名称
        /// </summary>
        public string TwoName { get; set; }

        /// <summary>
        /// 三级分类名称
        /// </summary>
        public string ThreeName { get; set; }

    }

    /// <summary>
    /// 配货发送短信危化品明细
    /// </summary>
    public class DcDistributionConfirm
    {
        /// <summary>
        /// 配货明细表Id
        /// </summary>
        public int ApplyConfirmDetailId { get; set; }

        /// <summary>
        /// 领用人Id
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 领用人手机号码
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 危化品名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 规格、属性
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 领用数量
        /// </summary>
        public decimal Num { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// 领用模式（1：单人领用，2：双人领用,双人密码，3：双人领用,单人密码）
        /// </summary>
        public int ApplyMode { get; set; }

        /// <summary>
        /// 同领用人
        /// </summary>
        public int WithUserId { get; set; }

        /// <summary>
        /// 同领用人手机号码
        /// </summary>
        public string WithMobile { get; set; }

        /// <summary>
        /// 申领批次
        /// </summary>
        public string BatchNo { get; set; }
    }

    /// <summary>
    /// 附件实体
    /// </summary>
    public class FileModel
    {
        /// <summary>
        /// 附件Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 对应项目Id
        /// </summary>
        public int ObjectId { get; set; }

        /// <summary>
        /// 附件标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 附件路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 附件扩展名
        /// </summary>
        public string Ext { get; set; }
    }

    /// <summary>
    /// 项目实体
    /// </summary>
    public class ProjectGLModel
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 企业名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 创建人单位
        /// </summary>
        public int UnitId { get; set; }

        /// <summary>
        /// 项目金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal ContractAmount { get; set; }

        /// <summary>
        /// 结算金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal ContractSettlementAmount { get; set; }

        /// <summary>
        /// 合同签订日期
        /// </summary>
        public string ContractDate { get; set; }
    }

    /// <summary>
	/// 小程序扫码登录
	/// </summary>
	public class wx_QrcodeLogin
    {
        /// <summary>
        /// 
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Token { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime RegDate { get; set; }
        /// <summary>
        /// 0：待登录；1：已登录；2：已失效；
        /// </summary>
        public int Statuz { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OpenId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? UserId { get; set; }
    }

    public class BConfigSetModel
    {
        public string ModuleCode { get; set; }

        public List<ConfigSetModel> List { get; set; }
    }

    public class ConfigSetModel
    {
        /// <summary>
        /// 类型编号
        /// </summary>
        public long ConfigSetId { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        public string ConfigValue { get; set; }
    }

    public class Login
    {
        private string name;
        private string pass;

        /// <summary>
        /// 帐号名
        /// </summary>
        public string Name { get { return name; } set { this.name = value; } }
        /// <summary>
        /// 密码
        /// </summary>
        public string Pass { get { return pass; } set { this.pass = value; } }


    }

    public class DcThirdMaterialIdGrant
    {
        /// <summary>
        /// 第三方物品Id
        /// </summary>
        public string ThirdMaterialId { get; set; }

        /// <summary>
        /// 第三方物品编码
        /// </summary>
        public string ThirdCode { get; set; }

        /// <summary>
        /// 发放数量
        /// </summary>
        public decimal Num { get; set; }

        /// <summary>
        /// 单位物品实体
        /// </summary>
        public DcSchoolMaterial dcSchoolMaterial { get; set; }
    }

    #region 危化品公安自动上报实体

    /// <summary>
    /// DangerChemicalsZbModel 的摘要说明
    /// </summary>
    public class DangerChemicalsZbModel<T>
    {
        /// <summary>
        /// code
        /// </summary>
        public int code { get; set; }

        /// <summary>
        /// info
        /// </summary>
        public string info { get; set; }

        /// <summary>
        /// 数据总数
        /// </summary>
        public int total { get; set; }

        /// <summary>
        /// 数据对象
        /// </summary>
        public T data { get; set; }
    }

    public class ZbDataString
    {
        public string data { get; set; }
    }

    public class ZbInResultData
    {
        public ZbInResult data { get; set; }
    }
    /// <summary>
    /// 购买入库接口返回结果
    /// </summary>
    public class ZbInResult
    {
        /// <summary>
        /// 出入库id
        /// </summary>
        public string CRK_ID { get; set; }

        /// <summary>
        /// 出入库物品id
        /// </summary>
        public List<string> CRKWP_ID { get; set; }
    }


    /// <summary>
    /// 单位资质信息集合
    /// </summary>
    public class ZbDataUnitQualificationList
    {
        public List<ZbDataUnitQualification> data { get; set; }
    }
    /// <summary>
    /// 单位资质信息实体
    /// </summary>
    public class ZbDataUnitQualification
    {
        /// <summary>
        /// 资质类型ID
        /// </summary>
        public string FJ_LEIXING_ID { get; set; }

        /// <summary>
        /// 资质类型名称
        /// </summary>
        public string FJ_LEIXING_NAME { get; set; }

        /// <summary>
        /// 资质编号，即资质证件号码
        /// </summary>
        public string ZZ_BIANHAO { get; set; }
    }

    /// <summary>
    /// 单位备案信息实体集合
    /// </summary>
    public class ZbDataUnitInfoList
    {
        public List<ZbDataUnitInfo> data { get; set; }
    }
    /// <summary>
    /// 单位备案信息实体
    /// </summary>
    public class ZbDataUnitInfo
    {
        /// <summary>
        /// 单位ID
        /// </summary>
        public string COMP_ID { get; set; }

        /// <summary>
        /// 单位管辖编码
        /// </summary>
        public string COMP_CODE { get; set; }

        /// <summary>
        /// 单位统一社会信用代码/公安机关机构代码
        /// </summary>
        public string COMP_TYSHXYDM { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string COMP_NAME { get; set; }

        /// <summary>
        /// 单位地址
        /// </summary>
        public string COMP_DIZHI { get; set; }

        /// <summary>
        /// 开户银行名称
        /// </summary>
        public string KAIHUYINGHANG { get; set; }

        /// <summary>
        /// 银行账户
        /// </summary>
        public string YINGHANGZHANGHU { get; set; }

        /// <summary>
        /// 流程状态：-1未创建流程、0流程审批中、1流程已结束
        /// </summary>
        public int FLOW_STATE { get; set; }

        /// <summary>
        /// 备案信息版本号，版本号大于0时表示备案完成
        /// </summary>
        public int VERSION { get; set; }

        /// <summary>
        /// 是否注销：0未注销、1已注销
        /// </summary>
        public int ZX_STATE { get; set; }

        /// <summary>
        /// 单位资质信息
        /// </summary>
        public List<ZbDataUnitQualification> SH_DANWEIBEIAN_FJ { get; set; }

        /// <summary>
        /// 单位备案物品信息
        /// </summary>
        public List<ZbUnitMaterial> SH_DANWEIBEIAN_WP { get; set; }
    }
    public class ZbUnitMaterial
    {
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public string UPDATE_DATE { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string CREATE_DATE { get; set; }

        /// <summary>
        /// 校验状态（与浙江生产安全子系统进行校验）：0未校验、1校验存在、2校验不存在
        /// </summary>
        public int CHECK_STATE { get; set; }

        /// <summary>
        /// 注销状态：0正常、1已注销
        /// </summary>
        public int ZX_STATE { get; set; }

        /// <summary>
        /// 锁定状态：0未锁定、1已锁定
        /// </summary>
        public int SD_STATE { get; set; }

        /// <summary>
        /// 物品年可购买量剩余
        /// </summary>
        public double YEAR_GOUMAI_SY { get; set; }

        /// <summary>
        /// 物品年可购买量，为0表示不限制，计量单位与系统物品表一致
        /// </summary>
        public double YEAR_GOUMAI { get; set; }

        /// <summary>
        /// 物品年消耗量，计量单位与系统物品表一致
        /// </summary>
        public double YEAR_XIAOHAO { get; set; }

        /// <summary>
        /// 是否涉及处置：0否、1是
        /// </summary>
        public int IS_CZ { get; set; }

        /// <summary>
        /// 是否涉及使用：0否、1是
        /// </summary>
        public int IS_SY { get; set; }

        /// <summary>
        /// 是否涉及运输：0否、1是
        /// </summary>
        public int IS_YS { get; set; }

        /// <summary>
        /// 是否涉及储存：0否、1是
        /// </summary>
        public int IS_CC { get; set; }

        /// <summary>
        /// 是否涉及销售：0否、1是
        /// </summary>
        public int IS_XS { get; set; }

        /// <summary>
        /// 是否涉及生产：0否、1是
        /// </summary>
        public int IS_SC { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string WP_JLDW { get; set; }

        /// <summary>
        /// 物品名称，与系统物品表保持一致
        /// </summary>
        public string WP_NAME { get; set; }

        /// <summary>
        /// 物品ID，与系统物品表保持一致
        /// </summary>
        public string WP_ID { get; set; }

        /// <summary>
        /// 单位ID
        /// </summary>
        public string COMP_ID { get; set; }

        /// <summary>
        /// 涉及物品主键ID
        /// </summary>
        public string COMPWP_ID { get; set; }
    }



    /// <summary>
    /// 储存场所集合
    /// </summary>
    public class ZbDataStoragePlaceList
    {
        public List<ZbDataStoragePlace> data { get; set; }
    }
    /// <summary>
    /// 储存场所实体
    /// </summary>
    public class ZbDataStoragePlace
    {
        /// <summary>
        /// 储存场所ID
        /// </summary>
        public string KF_ID { get; set; }

        /// <summary>
        /// 储存场所名称
        /// </summary>
        public string KF_NAME { get; set; }

        /// <summary>
        /// 流程状态：-1未创建流程、0流程审批中、1流程已结束
        /// </summary>
        public int FLOW_STATE { get; set; }

        /// <summary>
        /// 储存场所信息版本号，版本号大于0时表示备案完成
        /// </summary>
        public int VERSION { get; set; }

        /// <summary>
        /// 是否注销：0未注销、1已注销、-1连带注销
        /// </summary>
        public int ZX_STATE { get; set; }
    }

    /// <summary>
    /// 人员信息集合
    /// </summary>
    public class ZbDataUserList
    {
        public List<ZbDataUser> data { get; set; }
    }
    /// <summary>
    /// 人员信息实体
    /// </summary>
    public class ZbDataUser
    {
        /// <summary>
        /// 人员ID
        /// </summary>
        public string RY_ID { get; set; }

        /// <summary>
        /// 人员姓名
        /// </summary>
        public string RY_NAME { get; set; }

        /// <summary>
        /// 证件类型：01身份证、99其他证件[字典]
        /// </summary>
        public string RY_ZHENGJIAN_LX { get; set; }

        /// <summary>
        /// 证件号码
        /// </summary>
        public string RY_ZHENGJIANHAO { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string RY_MOBILE { get; set; }

        /// <summary>
        /// 是否注销：0未注销、1已注销、-1连带注销
        /// </summary>
        public int ZX_STATE { get; set; }

        /// <summary>
        /// 备案信息版本号，版本号大于0时表示备案完成
        /// </summary>
        public int VERSION { get; set; }

        /// <summary>
        /// 流程状态：-1未创建流程、0流程审批中、1流程已结束
        /// </summary>
        public int FLOW_STATE { get; set; }

        /// <summary>
        /// 是否保管员
        /// </summary>
        public int IS_BG { get; set; }
    }

    /// <summary>
    /// 标识码对应物品信息集合
    /// </summary>
    public class ZbMaterialList
    {
        public List<ZbMaterial> data { get; set; }
    }
    public class ZbMaterial
    {
        /// <summary>
        /// 物品Id
        /// </summary>
        public string wpId { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        public string wpName { get; set; }

        /// <summary>
        /// 物品计量单位
        /// </summary>
        public string wpDw { get; set; }

        /// <summary>
        /// 剩余数量
        /// </summary>
        public string wpSysl { get; set; }

        /// <summary>
        /// 销售单位id
        /// </summary>
        public string before_comp_id { get; set; }

        /// <summary>
        /// 销售单位名称
        /// </summary>
        public string before_comp_name { get; set; }

        /// <summary>
        /// 标识状态
        /// </summary>
        public int state { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string error { get; set; }
    }


    public class ZbInWarehouseDto
    {
        public ZbInWarehouse o { get; set; }

        public string schoolMaterialIds { get; set; }
    }
    /// <summary>
    /// 入库实体
    /// </summary>
    public class ZbInWarehouse
    {
        /// <summary>
        /// 系统登录用户id
        /// </summary>
        public string RY_ID { get; set; }

        /// <summary>
        /// 系统登录的用户名称
        /// </summary>
        public string RY_REAL_NAME { get; set; }

        /// <summary>
        /// 单位ID
        /// </summary>
        public string COMP_ID { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string COMP_NAME { get; set; }

        /// <summary>
        /// 单位管辖编码16位
        /// </summary>
        public string COMP_CODE { get; set; }

        /// <summary>
        /// 单位地址
        /// </summary>
        public string COMP_DIZHI { get; set; }

        /// <summary>
        /// 单位许可证种类（危险化学品经营资质证明分类代码）
        /// </summary>
        public string COMP_XKZZL { get; set; }

        /// <summary>
        /// 单位许可证编号（危险化学品经营资质证明编号）
        /// </summary>
        public string COMP_XKZBH { get; set; }

        /// <summary>
        /// 单位开户银行
        /// </summary>
        public string COMP_YINHANG { get; set; }

        /// <summary>
        /// 单位银行账户
        /// </summary>
        public string COMP_YHZH { get; set; }

        /// <summary>
        /// 储存场所Id
        /// </summary>
        public string KF_ID { get; set; }

        /// <summary>
        /// 储存场所名称
        /// </summary>
        public string KF_NAME { get; set; }

        /// <summary>
        /// 出入库时间
        /// </summary>
        public string CRK_TIME { get; set; }

        /// <summary>
        /// 经办人Id
        /// </summary>
        public string JBR_ID { get; set; }

        /// <summary>
        /// 经办人姓名
        /// </summary>
        public string JBR_NAME { get; set; }

        /// <summary>
        /// 经办人证件类型（04:护照,03:台湾居民来往大陆通行证,02:港澳居民来往大陆通行证,01:公民身份证,99:其他证件）
        /// </summary>
        public string JBR_ZHENGJIAN_LX { get; set; }

        /// <summary>
        /// 经办人证件号码
        /// </summary>
        public string JBR_ZHENGJIANHAO { get; set; }

        /// <summary>
        /// 经办人手机
        /// </summary>
        public string JBR_MOBILE { get; set; }

        /// <summary>
        /// 对方单位ID
        /// </summary>
        public string DF_COMP_ID { get; set; }

        /// <summary>
        /// 对方单位名称
        /// </summary>
        public string DF_COMP_NAME { get; set; }

        /// <summary>
        /// 对方单位许可证种类（危险化学品经营资质证明分类代码）
        /// </summary>
        public string DF_COMP_XKZZL { get; set; }

        /// <summary>
        /// 对方单位许可证编号（危险化学品经营资质证明编号）
        /// </summary>
        public string DF_COMP_XKZBH { get; set; }

        /// <summary>
        /// 对方单位地址
        /// </summary>
        public string DF_COMP_DIZHI { get; set; }

        /// <summary>
        /// 对方单位开户银行
        /// </summary>
        public string DF_COMP_YINHANG { get; set; }

        /// <summary>
        /// 对方单位银行账户
        /// </summary>
        public string DF_COMP_YHZH { get; set; }

        /// <summary>
        /// 对方经办人ID
        /// </summary>
        public string DF_JBR_ID { get; set; }

        /// <summary>
        /// 对方经办人姓名
        /// </summary>
        public string DF_JBR_NAME { get; set; }

        /// <summary>
        /// 对方经办人证件类型（04:护照,03:台湾居民来往大陆通行证,02:港澳居民来往大陆通行证,01:公民身份证,99:其他证件）
        /// </summary>
        public string DF_JBR_ZHENGJIAN_LX { get; set; }

        /// <summary>
        /// 对方经办人证件号码
        /// </summary>
        public string DF_JBR_ZHENGJIANHAO { get; set; }

        /// <summary>
        /// 对方经办人手机
        /// </summary>
        public string DF_JBR_MOBILE { get; set; }

        /// <summary>
        /// 出入库物品
        /// </summary>
        public List<ZbInWarehourseMaterial> CRK_WP { get; set; }
    }

    public class ZbInWarehourseMaterial
    {
        /// <summary>
        /// 物品id
        /// </summary>
        public string WP_ID { get; set; }

        /// <summary>
        /// 物品数量
        /// </summary>
        public string WP_SL { get; set; }

        /// <summary>
        /// 物品计量单位固体单位kg，液体单位L
        /// </summary>
        public string WP_JLDW { get; set; }

        /// <summary>
        /// 物品标识
        /// </summary>
        public List<string> BS_NOS { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        public string WP_NAME { get; set; }
    }

    /// <summary>
    /// 获取化学品库
    /// </summary>
    public class ZbGetMaterialList
    {
        public List<ZbGetMaterial> data { get; set; }
    }
    public class ZbGetMaterial
    {
        public string WP_ID { get; set; }

        public string WP_NAME { get; set; }

        public string WP_BIEMING { get; set; }

        public string WP_XINGTAI { get; set; }

        public int DEL { get; set; }

        public int NEED_BIAOSHI { get; set; }
    }


    /// <summary>
    /// 添加合法用途说明
    /// </summary>
    public class ZbAddLegalUse
    {
        /// <summary>
        /// 系统登录用户id
        /// </summary>
        public string RY_ID { get; set; }

        /// <summary>
        /// 系统登录的用户名称
        /// </summary>
        public string RY_REAL_NAME { get; set; }

        /// <summary>
        /// 单位ID
        /// </summary>
        public string COMP_ID { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string COMP_NAME { get; set; }

        /// <summary>
        /// 单位管辖编码16位
        /// </summary>
        public string COMP_CODE { get; set; }

        /// <summary>
        /// 单位银行账号
        /// </summary>
        public string COMP_YHZH { get; set; }

        /// <summary>
        /// 单位地址
        /// </summary>
        public string COMP_DIZHI { get; set; }

        /// <summary>
        /// 销售单位id
        /// </summary>
        public string XS_COMP_ID { get; set; }

        /// <summary>
        /// 销售单位名称
        /// </summary>
        public string XS_COMP_NAME { get; set; }

        /// <summary>
        /// 销售单位编码
        /// </summary>
        public string XS_COMP_CODE { get; set; }

        /// <summary>
        /// 经办人id
        /// </summary>
        public string JBR_ID { get; set; }

        /// <summary>
        /// 经办人姓名
        /// </summary>
        public string JBR_NAME { get; set; }

        /// <summary>
        /// 经办人证件类型
        /// </summary>
        public string JBR_ZHENGJIAN_LX { get; set; }

        /// <summary>
        /// 经办人证件号
        /// </summary>
        public string JBR_ZHENGJIAN_HAO { get; set; }

        /// <summary>
        /// 经办人电话
        /// </summary>
        public string JBR_MOBILE { get; set; }

        /// <summary>
        /// 详细物品
        /// </summary>
        public List<LegalUseMaterial> HFYT_WP { get; set; }
    }
    public class LegalUseMaterial
    {
        /// <summary>
        /// 物品id
        /// </summary>
        public string WP_ID { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        public string WP_NAME { get; set; }

        /// <summary>
        /// 物品数量
        /// </summary>
        public string WP_NUM { get; set; }

        /// <summary>
        /// 物品计量单位
        /// </summary>
        public string WP_JLDW { get; set; }

        /// <summary>
        /// 合法用途说明
        /// </summary>
        public string WP_YONGTU { get; set; }
    }

    /// <summary>
    /// 添加合法用途说明结果
    /// </summary>
    public class ZbLegalResult
    {
        public ZbLegalResultDetail data { get; set; }
    }
    public class ZbLegalResultDetail
    {
        public string HFYT_ID { get; set; }

        public List<string> HFYTWP_ID { get; set; }
    }
    #endregion


    /// <summary>
    /// 废弃物处置申请
    /// </summary>
    public class DcWasteDisposalSave
    {
        /// <summary>
        /// 废弃物基础分类库Id
        /// </summary>
        public long BaseWasteId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Num { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }

    /// <summary>
    /// 废弃物记录添加
    /// </summary>
    public class DcWasteRecordModel : DcWasteDisposalSave
    {
        /// <summary>
        /// 关联表Id（类型1时dc_ApplyConfirmDetail表Id）
        /// </summary>
        public long ObjectId { get; set; }
    }
    /// <summary>
    /// 废弃物保存
    /// </summary>
    public class DcWasteDisposalSaveModel
    {
        /// <summary>
        /// 保存实体信息
        /// </summary>
        public List<DcWasteDisposalSave> o { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public int type { get; set; }
        /// <summary>
        /// Id集合
        /// </summary>
        public string ids { get; set; }
    }

    public class LvApplyAdjust
    {
        /// <summary>
        /// 申领Id
        /// </summary>
        public long ApplyId { get; set; }

        /// <summary>
        /// 单位物品表Id
        /// </summary>
        public long SchoolMaterialId { get; set; }

        /// <summary>
        /// 领用数量
        /// </summary>
        public decimal Num { get; set; }
    }

    public class WebSiteDataTable
    {
        public long titleId { get; set; }
        public string webTitle { get; set; }
        public string titleMemo { get; set; }
        public long logoId { get; set; }
        public string logoMemo { get; set; }

        public string logo { get; set; }

        public string OutsideLog { get; set; }

    }
}
