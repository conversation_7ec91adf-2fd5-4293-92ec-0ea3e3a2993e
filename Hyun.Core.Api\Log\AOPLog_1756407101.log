--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:40 937
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： d => ((d.AppType == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_1).CS$<>8__locals1.appType, Nullable`1)) AndAlso value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_1).pids.Contains(d.Id)) 
【携带的参数JSON】：  
【响应时间】：22ms
【执行完成时间】：2025-08-28 06:51:40 959
【执行完成结果】：[{"Code":"@url","Name":"校服选用","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1100000,"Icon":"cdxiaofuxuanyong","Description":"市级管理员、区县管理员、学校管理员、学校班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000010},{"Code":"@url","Name":"校服采购","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1300000,"Icon":"cdxiaofucaigou","Description":"学校班主任、学校管理员、区县管理员、市级管理员、企业管理员（供应商）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000020},{"Code":"@url","Name":"选用组织","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1200000,"Icon":"cdxuanyongzuzhi","Description":"学校管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000030},{"Code":"@url","Name":"校服调换","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1500000,"Icon":"cdxiaofudiaohuan","Description":"学校管理员、企业管理员（供应商）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000040},{"Code":"@url","Name":"校服资助","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1600000,"Icon":"cdxiaofuzizhu","Description":"学校管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000050},{"Code":"@url","Name":"校服评价","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1700000,"Icon":"cdxiaofupingjia","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000060},{"Code":"@url","Name":"平台设置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5600000,"Icon":"cdpingtaishezhi","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":***************},{"Code":"selection/project/list","Name":"选用方案","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1101000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001001},{"Code":"selection/project/edit","Name":"创建/修改方案","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1105000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001002},{"Code":"selection/solicit/list","Name":"征求结果","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1106000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001003},{"Code":"selection/solicit/opiniondetail","Name":"意见详情","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1107000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001004},{"Code":"purchase/management/list","Name":"采购管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1301000,"Icon":"","Description":"市级管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002001},{"Code":"purchase/management/edit","Name":"新增/修改采购","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1303000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002002},{"Code":"@url","Name":"校服审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1312000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002008},{"Code":"purchase/audit/auditlist","Name":"待审核校服","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1312001,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002008,"Mid":0,"PidArr":null,"Id":100000000002009},{"Code":"purchase/audit/auditedlist","Name":"已审核校服","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1312002,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002008,"Mid":0,"PidArr":null,"Id":100000000002010},{"Code":"@url","Name":"校服征订","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1400000,"Icon":"cdxiaofuzhengding","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000002011},{"Code":"purchase/solicitsubscrip/orderlist","Name":"征订单管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1401000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002012},{"Code":"purchase/solicitsubscrip/orderedit","Name":"生成/修改征订单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1402000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002013},{"Code":"purchase/solicitsubscrip/orderexamine","Name":"查看征订单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1403000,"Icon":"","Description":"学校管理员、区县管理员、市级管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002014},{"Code":"purchase/solicitsubscrip/orderdetail","Name":"征订单明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1404000,"Icon":"","Description":"学校管理员、区县管理员、市级管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002015},{"Code":"purchase/management/detail","Name":"采购管理查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1308000,"Icon":"","Description":"学校管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002017},{"Code":"organization/organize/editlist","Name":"组建选用组织","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1201000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000030,"Mid":0,"PidArr":null,"Id":100000000003001},{"Code":"exchange/launch/list","Name":"调换管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1501000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000040,"Mid":0,"PidArr":null,"Id":100000000004001},{"Code":"user/teacheraffair/launch","Name":"校服调换","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2203000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000004002},{"Code":"exchange/swaporder/list","Name":"查看调换单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1505000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000040,"Mid":0,"PidArr":null,"Id":100000000004003},{"Code":"exchange/swaporder/detail","Name":"调换明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1507000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000040,"Mid":0,"PidArr":null,"Id":100000000004004},{"Code":"subsidize/subsidize/list","Name":"资助信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1601000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000050,"Mid":0,"PidArr":null,"Id":100000000005001},{"Code":"evaluate/list","Name":"评价管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1701000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006001},{"Code":"evaluate/edit","Name":"创建/修改评价","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1703000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006002},{"Code":"evaluate/examinelist","Name":"查看评价","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1704000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006003},{"Code":"evaluate/examinedetail","Name":"评价明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1705000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006004},{"Code":"@url","Name":"班主任事务","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2200000,"Icon":"cdbanzhurenshiwu","Description":"学校班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000007001},{"Code":"user/teacheraffair/studentmanage","Name":"学生管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2201000,"Icon":"","Description":"学校班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007002},{"Code":"user/backset/unit","Name":"单位信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2307001,"Icon":"","Description":"（校服）学校管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":***************},{"Code":"user/backset/useraccount","Name":"用户账号","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2309000,"Icon":"","Description":"市级管理员、区县管理员、学校管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":***************},{"Code":"user/backset/gradeclass","Name":"年级班级","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2310000,"Icon":"","Description":"（校服）学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":100000000007006},{"Code":"user/backset/student","Name":"学生名单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2311000,"Icon":"","Description":"（校服）学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":100000000007007},{"Code":"user/teacheraffair/solicitsubscrip","Name":"校服征订","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2202000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007014},{"Code":"@url","Name":"个人中心","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2388000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":100000000007017},{"Code":"user/my/useredit","Name":"个人信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2328001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007017,"Mid":0,"PidArr":null,"Id":100000000007018},{"Code":"user/my/changepass","Name":"修改密码","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2328002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007017,"Mid":0,"PidArr":null,"Id":100000000007019},{"Code":"user/teacheraffair/orderdetail","Name":"征订单明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2204000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007020},{"Code":"user/teacheraffair/launchdetail","Name":"调换明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2205000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007021},{"Code":"approvalconfiguration/pages/node/formedit","Name":"填报修改","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814852328300482560},{"Code":"approvalconfiguration/pages/node/detail","Name":"查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814852564905365504},{"Code":"approvalconfiguration/pages/node/examine","Name":"审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814852943126728704},{"Code":"approvalconfiguration/pages/node/processedlist","Name":"已处理","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814853692145537024},{"Code":"approvalconfiguration/pages/node/pendinglist","Name":"待处理","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814853925042655232},{"Code":"approvalconfiguration/pages/statistics/list","Name":"资金库","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814855159220801536},{"Code":"approvalconfiguration/workflow/node/conditionset","Name":"条件控制","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":796311544399925248,"Mid":0,"PidArr":null,"Id":814855268880879616},{"Code":"approvalconfiguration/pages/treasury/list","Name":"查询统计","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8009,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814855791944142848},{"Code":"approvalconfiguration/pages/treasury/detail","Name":"查询统计查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8011,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814855881240875008},{"Code":"approvalconfiguration/pages/node/projectlist","Name":"项目清单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":11101,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814916365319147520},{"Code":"approvalconfiguration/business/permission","Name":"业务授权","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2324000,"Icon":"","Description":"（非校服）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":814917889516638208},{"Code":"@url","Name":"工作流填报","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2500000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":815152216284336128},{"Code":"@url","Name":"节点","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":11001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152216284336128,"Mid":0,"PidArr":null,"Id":815152703427579904},{"Code":"dangerchemicals/daily/governdeclarelist@t=1","Name":"周申报列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512156001800192},{"Code":"dangerchemicals/daily/governitemreport@t=2","Name":"月排查申报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512500404490240},{"Code":"dangerchemicals/daily/governdeclarelist@t=2","Name":"月申报列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512620718100480},{"Code":"dangerchemicals/daily/dcgovernrectifylist","Name":"问题隐患清单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512817057665024},{"Code":"approvalconfiguration/pages/treasury/listitem","Name":"查询统计列表","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":827558788750905344},{"Code":"approvalconfiguration/pages/node/projectexaminelist","Name":"项目清单审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":12104,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":827558788880928768},{"Code":"approvalconfiguration/pages/node/projectdetail","Name":"项目清单查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":12105,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":827558790864834560},{"Code":"@url","Name":"安全排查","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957453845008384,"Mid":0,"PidArr":null,"Id":827558795155607552},{"Code":"dangerchemicals/daily/governitemreport@t=1","Name":"周排查申报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":827558797206622208},{"Code":"@URL","Name":"应用管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5700000,"Icon":"cdyingyongguanli","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":840599930178375680},{"Code":"user/application/list","Name":"应用列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5701000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":840599930178375680,"Mid":0,"PidArr":null,"Id":840600506052120576},{"Code":"user/application/add","Name":"应用添加修改","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5702000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":840599930178375680,"Mid":0,"PidArr":null,"Id":842694325652426752},{"Code":"@url","Name":"危化品治理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2600000,"Icon":"cdweihuapinzhili","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":844957453845008384},{"Code":"@url","Name":"危化品管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2800000,"Icon":"cdweihuapinguanli","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":844957620178522112},{"Code":"dangerchemicals/daily/governdetail","Name":"申报查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":10008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":844961710316982272},{"Code":"@url","Name":"危化品领用","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2802000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844966541932892160},{"Code":"dangerchemicals/apply/carlist","Name":"领用车","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2802003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844972844101144576},{"Code":"dangerchemicals/apply/detailview","Name":"领用查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2802004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844972973948407808},{"Code":"dangerchemicals/apply/filleasy","Name":"领用填报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2802006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844973591006023680},{"Code":"dangerchemicals/applyed/listeasy","Name":"已领用物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2802007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844973895587991552},{"Code":"@url","Name":"领用审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2803000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844974175394205696},{"Code":"dangerchemicals/apply/auditlist@p=20","Name":"待审核物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2803002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974175394205696,"Mid":0,"PidArr":null,"Id":844974402880671744},{"Code":"dangerchemicals/apply/auditedlist@p=20","Name":"已审核物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2803003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974175394205696,"Mid":0,"PidArr":null,"Id":844974534216912896},{"Code":"dangerchemicals/apply/audit","Name":"领用审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2803004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974175394205696,"Mid":0,"PidArr":null,"Id":844974696779747328},{"Code":"@url","Name":"危化品配发","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844974828485087232},{"Code":"dangerchemicals/apply/auditlist@p=30","Name":"待配货物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975023654440960},{"Code":"dangerchemicals/apply/confirm","Name":"仓库配货","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2804003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975297731235840},{"Code":"dangerchemicals/apply/givelist","Name":"待发放物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975449124638720},{"Code":"dangerchemicals/apply/givedlist@t=1","Name":"已发放物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975635729223680},{"Code":"dangerchemicals/apply/print","Name":"打印领用单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2804006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975728242987008},{"Code":"@url","Name":"危化品采购","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844975797407059968},{"Code":"dangerchemicals/purchase/fill","Name":"采购填报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976309716127744},{"Code":"dangerchemicals/purchase/list","Name":"采购车","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2805003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976490087976960},{"Code":"dangerchemicals/purchase/orderlist","Name":"已填报列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976660540297216},{"Code":"dangerchemicals/purchase/endlist","Name":"采购计划","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976914006282240},{"Code":"dangerchemicals/purchase/detaillist","Name":"物品明细列表","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2805007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844977126699438080},{"Code":"@url","Name":"采购审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2806000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844977251022802944},{"Code":"dangerchemicals/purchase/auditlist@p=10","Name":"待审核列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2806002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977251022802944,"Mid":0,"PidArr":null,"Id":844977462650605568},{"Code":"dangerchemicals/purchase/auditedlist@p=10","Name":"已审核列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2806003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977251022802944,"Mid":0,"PidArr":null,"Id":844977581781422080},{"Code":"@url","Name":"采购审批","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2807000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844977790431268864},{"Code":"dangerchemicals/purchase/auditlist@p=20","Name":"待审批列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2807002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844977922153385984},{"Code":"dangerchemicals/purchase/auditedlist@p=20","Name":"已审批列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2807003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844978006509228032},{"Code":"dangerchemicals/purchase/audit","Name":"采购审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2807004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844978207466721280},{"Code":"dangerchemicals/purchase/detailview","Name":"采购查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2807005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844978326723366912},{"Code":"@url","Name":"危化品入库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844978402241810432},{"Code":"dangerchemicals/input/byplanlist","Name":"按计划入库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844978933500743680},{"Code":"dangerchemicals/school/itemstorage","Name":"存量录入","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979146500083712},{"Code":"dangerchemicals/school/materialstoragelog","Name":"入库记录","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979303874564096},{"Code":"dangerchemicals/school/purchasestorageauditlist","Name":"采购入库审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979454731096064},{"Code":"dangerchemicals/input/materialbyplan","Name":"计划入库","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2808007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979648616992768},{"Code":"dangerchemicals/school/storagelogprint","Name":"打印入库单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2808008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979828049317888},{"Code":"@url","Name":"危化品库管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844980359450857472},{"Code":"dangerchemicals/school/materialcataloglist","Name":"危化品存量库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844980598295498752},{"Code":"dangerchemicals/apply/granteasy","Name":"领用发放","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844980852977831936},{"Code":"dangerchemicals/apply/backlisteasy","Name":"领用退回","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981082699862016},{"Code":"dangerchemicals/apply/givedlist@t=2","Name":"已领用物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981392927363072},{"Code":"dangerchemicals/material/backlist","Name":"领用退回","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981743516651520},{"Code":"dangerchemicals/school/materialbacklist","Name":"采购退货","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981921363529728},{"Code":"dangerchemicals/school/materialbacklog","Name":"已退货清单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844982125559025664},{"Code":"dangerchemicals/school/materialbacklogprint","Name":"打印退货单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2809010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844982262398193664},{"Code":"dangerchemicals/scrap/list","Name":"危化品报废","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809015,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983343421329408},{"Code":"dangerchemicals/scraped/list","Name":"已报废清单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809016,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983416607739904},{"Code":"dangerchemicals/materials/numaudit@t=3","Name":"退货审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809017,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983686142103552},{"Code":"dangerchemicals/materials/numaudit@t=2","Name":"报废审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809019,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983906800242688},{"Code":"dangerchemicals/apply/print","Name":"打印领用单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2809020,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844984091039240192},{"Code":"@url","Name":"危废物处置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844984272614854656},{"Code":"dangerchemicals/waste/list","Name":"危废物存量库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984515939012608},{"Code":"dangerchemicals/waste/disposallisted","Name":"已处置危废物","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984636638498816},{"Code":"dangerchemicals/waste/reportlist","Name":"待处置信息填报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984773846765568},{"Code":"dangerchemicals/waste/detaillist","Name":"危废物明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2810005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984942273236992},{"Code":"dangerchemicals/waste/auditlist","Name":"处置审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844985090730627072},{"Code":"dangerchemicals/waste/recordlist","Name":"危废物详情","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2810007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844985239972352000},{"Code":"@url","Name":"安全保障要求","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844985309048344576},{"Code":"dangerchemicals/system/teambuild","Name":"制度与队伍建设","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844985864302891008},{"Code":"dangerchemicals/train/safeeducationlist","Name":"培训与安全教育","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986081353928704},{"Code":"dangerchemicals/emergency/planlist","Name":"演练与应急预案","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986204750352384},{"Code":"dangerchemicals/msds/list","Name":"查看MSDS","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986319925940224},{"Code":"dangerchemicals/word/guidelist","Name":"工作指导","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986470987993088},{"Code":"@url","Name":"查询统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844988224278368256},{"Code":"dangerchemicals/purchase/yearnumstatistics","Name":"采购数量统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844988795462881280},{"Code":"dangerchemicals/apply/yearnumstatistics","Name":"领用数量统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844988925083652096},{"Code":"dangerchemicals/apply/usernumstatistics","Name":"按领用人统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844989069602590720},{"Code":"@url","Name":"配置信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844992020215762944},{"Code":"dangerchemicals/school/materialmodelset","Name":"危化品信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992359836946432},{"Code":"dangerchemicals/company/list","Name":"供应商信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992477537505280},{"Code":"dangerchemicals/apply/grantuserset@t=1","Name":"领用人配置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992873186201600},{"Code":"dangerchemicals/apply/grantuserset@t=2","Name":"发放人配置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992931449278464},{"Code":"dangerchemicals/base/configset@m=9&opt=0&t=领用参数配置","Name":"领用参数配置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844993347960442880},{"Code":"dangerchemicals/deposit/addresslist","Name":"存放地点设置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844993496422027264},{"Code":"@url","Name":"台账打印","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844997017473126400},{"Code":"dangerchemicals/standbook/purchase","Name":"采购台账","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997280166580224},{"Code":"dangerchemicals/standbook/purchaseprint","Name":"采购台账打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997405890842624},{"Code":"dangerchemicals/standbook/apply","Name":"领用台账","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997569112182784},{"Code":"dangerchemicals/standbook/applyprint","Name":"领用台账打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997754236178432},{"Code":"dangerchemicals/standbook/stock","Name":"存量台账","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997884142161920},{"Code":"dangerchemicals/standbook/stockprint","Name":"存量台账打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997950672211968},{"Code":"dangerchemicals/standbook/waste","Name":"危废物统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844998050886717440},{"Code":"dangerchemicals/standbook/wasteprint","Name":"危废物统计打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816009,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844998119732023296},{"Code":"dangerchemicals/school/itemstorageauditlist","Name":"存量入库审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":845255096391438336},{"Code":"user/department/usermanager","Name":"部门用户列表","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2308400,"Icon":"","Description":"非校服（学校）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":852931579138281472},{"Code":"user/department/manager","Name":"部门管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2308330,"Icon":"","Description":"非校服（学校）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":853564351586504704},{"Code":"dangerchemicals/school/modelbranddisablelist","Name":"规格品牌配置","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2814009,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":855107575388049408},{"Code":"dangerchemicals/school/modelbrand","Name":"规格品牌配置","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2814010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":855107866284003328},{"Code":"approvalconfiguration/business/classification","Name":"分级授权","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2324100,"Icon":"","Description":"（非校服）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":870714919371149312},{"Code":"@url","Name":"装备登记","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":3000000,"Icon":"cdzhuangbeidengji","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":871030740077252608},{"Code":"thequipment/record/list","Name":"装备登记","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":3002000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":871030740077252608,"Mid":0,"PidArr":null,"Id":872554852792668160},{"Code":"user/backset/newunit","Name":"单位信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2306000,"Icon":"","Description":"（非校服）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T17:03:31","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-28T17:03:31","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":875322682860638208}]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 117
【当前操作用户】：yucai 
【当前执行方法】：GetModuleMenuList 
【携带的参数有】：  
【携带的参数JSON】：  
【响应时间】：28ms
【执行完成时间】：2025-08-28 06:51:41 146
【执行完成结果】：[{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":658679471378565,"ProcessNodeName":"学校填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":658679471378565,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":658681824243845,"ProcessNodeName":"教导主任审核","NodeType":2,"TreatHandle":"待审核","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审核","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":658681824243845,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":658682131247237,"ProcessNodeName":"校长审核","NodeType":2,"TreatHandle":"待审核","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审核","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":658682131247237,"ModuleSort":0},{"ModuleId":658679134371973,"ModuleName":"采购审批","Logo":"el-icon-ice-tea","ProcessId":670258683539589,"ProcessName":"测试流程","ProcessNodeId":661475211567237,"ProcessNodeName":"填报01","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":661475211567237,"ModuleSort":0},{"ModuleId":658679134371973,"ModuleName":"采购审批","Logo":"el-icon-ice-tea","ProcessId":670258683539589,"ProcessName":"测试流程","ProcessNodeId":661475211567237,"ProcessNodeName":"填报01","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":661475211567237,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667839088402565,"ProcessNodeName":"需求审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":667839088402565,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667839181226117,"ProcessNodeName":"财务审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":667839181226117,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":676389824655493,"ProcessName":"两个起始节点——测试","ProcessNodeId":676389425160325,"ProcessNodeName":"两个起始节点-学校填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":676389425160325,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":676389824655493,"ProcessName":"两个起始节点——测试","ProcessNodeId":676389492240517,"ProcessNodeName":"两个起始节点-学校审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":676389492240517,"ModuleSort":0},{"ModuleId":702534294601861,"ModuleName":"资金库测试","Logo":"el-icon-lollipop","ProcessId":702534674399365,"ProcessName":"资金库测试流程","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":712848436027525,"ProcessNodeName":"测试开始节点","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":0,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":1},{"ModuleId":674228238864517,"ModuleName":"多人审批","Logo":"el-icon-lollipop","ProcessId":674230575259781,"ProcessName":"多人审批项目","ProcessNodeId":674228986634373,"ProcessNodeName":"多人学校审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":674228986634373,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":701431378894981,"ModuleName":"暂存模块","Logo":"el-icon-lollipop","ProcessId":701436251205765,"ProcessName":"测试流程-暂存","ProcessNodeId":701431612588165,"ProcessNodeName":"学校填报-暂存","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":701431612588165,"ModuleSort":1},{"ModuleId":701431378894981,"ModuleName":"暂存模块","Logo":"el-icon-lollipop","ProcessId":701436251205765,"ProcessName":"测试流程-暂存","ProcessNodeId":701431842508933,"ProcessNodeName":"学校审核-暂存","NodeType":2,"TreatHandle":"待审核列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审核列表","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":701431842508933,"ModuleSort":1},{"ModuleId":701431378894981,"ModuleName":"暂存模块","Logo":"el-icon-lollipop","ProcessId":701436251205765,"ProcessName":"测试流程-暂存","ProcessNodeId":701432050229381,"ProcessNodeName":"学校审批-暂存","NodeType":2,"TreatHandle":"待审批列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审批列表","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":701432050229381,"ModuleSort":1},{"ModuleId":702534294601861,"ModuleName":"资金库测试","Logo":"el-icon-lollipop","ProcessId":702534674399365,"ProcessName":"资金库测试流程","ProcessNodeId":702534414794885,"ProcessNodeName":"学校填报-资金库测试","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":702534414794885,"ModuleSort":1},{"ModuleId":702534294601861,"ModuleName":"资金库测试","Logo":"el-icon-lollipop","ProcessId":702534674399365,"ProcessName":"资金库测试流程","ProcessNodeId":702534572294277,"ProcessNodeName":"学校审批-资金库测试","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":702534572294277,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":704429713551493,"ProcessName":"项目审批测试流程1","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":704429713551493,"ProcessName":"项目审批测试流程1","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":705091701473413,"ProcessNodeName":"项目填报-指定审核人","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":705091701473413,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":705091701473413,"ProcessNodeName":"项目填报-指定审核人","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":705091701473413,"ModuleSort":1},{"ModuleId":698593460789381,"ModuleName":"一个节点模块","Logo":"el-icon-milk-tea","ProcessId":698593675456645,"ProcessName":"一个节点流程","ProcessNodeId":698593592631429,"ProcessNodeName":"学校填报-一个节点","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":698593592631429,"ModuleSort":10}]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 229
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => (f.IsDeleted == False) 
【携带的参数JSON】：  
【响应时间】：22ms
【执行完成时间】：2025-08-28 06:51:41 251
【执行完成结果】：[{"ProcessId":658689418428549,"ProcessNodeId":658679471378565,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.593","Version":0,"Id":658689964335237},{"ProcessId":658689418428549,"ProcessNodeId":658679471378565,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339333},{"ProcessId":658689418428549,"ProcessNodeId":658681824243845,"Statuz":2,"StatuzDesc":"等待教导主任审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339334},{"ProcessId":658689418428549,"ProcessNodeId":658681824243845,"Statuz":5,"StatuzDesc":"教导主任审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339335},{"ProcessId":658689418428549,"ProcessNodeId":658682131247237,"Statuz":6,"StatuzDesc":"等待校长审核","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339336},{"ProcessId":658689418428549,"ProcessNodeId":658682131247237,"Statuz":9,"StatuzDesc":"校长审核退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339337},{"ProcessId":658689418428549,"ProcessNodeId":658682545234053,"Statuz":10,"StatuzDesc":"等待区县审批","WaitOrBack":1,"IsEnable":1,"Sort":10,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339338},{"ProcessId":658689418428549,"ProcessNodeId":658682545234053,"Statuz":13,"StatuzDesc":"区县审批退回","WaitOrBack":2,"IsEnable":1,"Sort":13,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339339},{"ProcessId":661475468013701,"ProcessNodeId":661475211567237,"Statuz":0,"StatuzDesc":"等待采购审批01","WaitOrBack":1,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.453","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893701},{"ProcessId":661475468013701,"ProcessNodeId":661475211567237,"Statuz":3,"StatuzDesc":"采购审批01退回","WaitOrBack":2,"IsEnable":1,"Sort":3,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.457","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893702},{"ProcessId":661475468013701,"ProcessNodeId":661478208483461,"Statuz":4,"StatuzDesc":"等待审批02","WaitOrBack":1,"IsEnable":1,"Sort":4,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.457","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893703},{"ProcessId":661475468013701,"ProcessNodeId":661478208483461,"Statuz":7,"StatuzDesc":"审批02退回","WaitOrBack":2,"IsEnable":1,"Sort":7,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.457","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893704},{"ProcessId":667848427954309,"ProcessNodeId":667815747584133,"Statuz":0,"StatuzDesc":"等待需求填报","WaitOrBack":1,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.883","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121226885},{"ProcessId":667848427954309,"ProcessNodeId":667815747584133,"Statuz":3,"StatuzDesc":"需求填报退回","WaitOrBack":2,"IsEnable":1,"Sort":3,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230981},{"ProcessId":667848427954309,"ProcessNodeId":667839088402565,"Statuz":4,"StatuzDesc":"等待需求审核","WaitOrBack":1,"IsEnable":1,"Sort":4,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230982},{"ProcessId":667848427954309,"ProcessNodeId":667839088402565,"Statuz":7,"StatuzDesc":"需求审核退回","WaitOrBack":2,"IsEnable":1,"Sort":7,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230983},{"ProcessId":667848427954309,"ProcessNodeId":667839181226117,"Statuz":8,"StatuzDesc":"等待财务审核","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230984},{"ProcessId":667848427954309,"ProcessNodeId":667839181226117,"Statuz":11,"StatuzDesc":"财务审核退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230985},{"ProcessId":667848427954309,"ProcessNodeId":667839300063365,"Statuz":12,"StatuzDesc":"等待教育局审核","WaitOrBack":1,"IsEnable":1,"Sort":12,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230986},{"ProcessId":667848427954309,"ProcessNodeId":667839300063365,"Statuz":15,"StatuzDesc":"教育局审核退回","WaitOrBack":2,"IsEnable":1,"Sort":15,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230987},{"ProcessId":670258683539589,"ProcessNodeId":661475211567237,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581957},{"ProcessId":670258683539589,"ProcessNodeId":661475211567237,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581958},{"ProcessId":670258683539589,"ProcessNodeId":661478208483461,"Statuz":2,"StatuzDesc":"等待审批02","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581959},{"ProcessId":670258683539589,"ProcessNodeId":661478208483461,"Statuz":5,"StatuzDesc":"审批02退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581960},{"ProcessId":674230575259781,"ProcessNodeId":674228817981573,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642117},{"ProcessId":674230575259781,"ProcessNodeId":674228817981573,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642118},{"ProcessId":674230575259781,"ProcessNodeId":674228986634373,"Statuz":2,"StatuzDesc":"等待多人学校审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642119},{"ProcessId":674230575259781,"ProcessNodeId":674228986634373,"Statuz":5,"StatuzDesc":"多人学校审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642120},{"ProcessId":674230575259781,"ProcessNodeId":674229194723461,"Statuz":6,"StatuzDesc":"等待区县多用户审核","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642121},{"ProcessId":674230575259781,"ProcessNodeId":674229194723461,"Statuz":9,"StatuzDesc":"区县多用户审核退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642122},{"ProcessId":675274736050309,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.013","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242911365},{"ProcessId":675274736050309,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915461},{"ProcessId":675274736050309,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915462},{"ProcessId":675274736050309,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915463},{"ProcessId":675274736050309,"ProcessNodeId":675249600512133,"Statuz":6,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915464},{"ProcessId":675274736050309,"ProcessNodeId":675249600512133,"Statuz":9,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915465},{"ProcessId":675276588191877,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163013},{"ProcessId":675276588191877,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163014},{"ProcessId":675276588191877,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163015},{"ProcessId":675276588191877,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163016},{"ProcessId":675276588191877,"ProcessNodeId":675249600512133,"Statuz":6,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163017},{"ProcessId":675276588191877,"ProcessNodeId":675249600512133,"Statuz":9,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163018},{"ProcessId":675286298050693,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157509},{"ProcessId":675286298050693,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157510},{"ProcessId":675286298050693,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157511},{"ProcessId":675286298050693,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157512},{"ProcessId":675286298050693,"ProcessNodeId":675249600512133,"Statuz":6,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157513},{"ProcessId":675286298050693,"ProcessNodeId":675249600512133,"Statuz":9,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.707","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157514},{"ProcessId":675315597021317,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.46","Version":0,"Id":675315746574469},{"ProcessId":675315597021317,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578565},{"ProcessId":675315597021317,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578566},{"ProcessId":675315597021317,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578567},{"ProcessId":675315597021317,"ProcessNodeId":675315433488517,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578568},{"ProcessId":675315597021317,"ProcessNodeId":675315433488517,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578569},{"ProcessId":675315597021317,"ProcessNodeId":675249600512133,"Statuz":8,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578570},{"ProcessId":675315597021317,"ProcessNodeId":675249600512133,"Statuz":11,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578571},{"ProcessId":675315597021317,"ProcessNodeId":675315513630853,"Statuz":12,"StatuzDesc":"等待区县审批","WaitOrBack":1,"IsEnable":1,"Sort":12,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578572},{"ProcessId":675315597021317,"ProcessNodeId":675315513630853,"Statuz":15,"StatuzDesc":"区县审批退回","WaitOrBack":2,"IsEnable":1,"Sort":15,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578573},{"ProcessId":676375478214789,"ProcessNodeId":676375207424133,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.037","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129861},{"ProcessId":676375478214789,"ProcessNodeId":676375207424133,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129862},{"ProcessId":676375478214789,"ProcessNodeId":676375283605637,"Statuz":2,"StatuzDesc":"等待A测试审批1","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129863},{"ProcessId":676375478214789,"ProcessNodeId":676375283605637,"Statuz":5,"StatuzDesc":"A测试审批1退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129864},{"ProcessId":676375478214789,"ProcessNodeId":676375359160453,"Statuz":6,"StatuzDesc":"等待A测试审批3","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129865},{"ProcessId":676375478214789,"ProcessNodeId":676375359160453,"Statuz":9,"StatuzDesc":"A测试审批3退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129866},{"ProcessId":676375854522501,"ProcessNodeId":676375808934021,"Statuz":0,"StatuzDesc":"等待A测试需求1","WaitOrBack":1,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937221},{"ProcessId":676375854522501,"ProcessNodeId":676375808934021,"Statuz":3,"StatuzDesc":"A测试需求1退回","WaitOrBack":2,"IsEnable":1,"Sort":3,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937222},{"ProcessId":676375854522501,"ProcessNodeId":676375283605637,"Statuz":4,"StatuzDesc":"等待A测试审批1","WaitOrBack":1,"IsEnable":1,"Sort":4,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937223},{"ProcessId":676375854522501,"ProcessNodeId":676375283605637,"Statuz":7,"StatuzDesc":"A测试审批1退回","WaitOrBack":2,"IsEnable":1,"Sort":7,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937224},{"ProcessId":676375854522501,"ProcessNodeId":676375359160453,"Statuz":8,"StatuzDesc":"等待A测试审批3","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937225},{"ProcessId":676375854522501,"ProcessNodeId":676375359160453,"Statuz":11,"StatuzDesc":"A测试审批3退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937226},{"ProcessId":676389824655493,"ProcessNodeId":676389425160325,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733637},{"ProcessId":676389824655493,"ProcessNodeId":676389425160325,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733638},{"ProcessId":676389824655493,"ProcessNodeId":676389492240517,"Statuz":2,"StatuzDesc":"等待两个起始节点-学校审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733639},{"ProcessId":676389824655493,"ProcessNodeId":676389492240517,"Statuz":5,"StatuzDesc":"两个起始节点-学校审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733640},{"ProcessId":676389824655493,"ProcessNodeId":676389582729349,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733641},{"ProcessId":676389824655493,"ProcessNodeId":676389582729349,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733642},{"ProcessId":676389824655493,"ProcessNodeId":676389640851589,"Statuz":8,"StatuzDesc":"等待两个起始节点-区县审批","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.497","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733643},{"ProcessId":676389824655493,"ProcessNodeId":676389640851589,"Statuz":11,"StatuzDesc":"两个起始节点-区县审批退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.497","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733644},{"ProcessId":701436251205765,"ProcessNodeId":701431612588165,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.983","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.98","Version":0,"Id":701436342112389},{"ProcessId":701436251205765,"ProcessNodeId":701431612588165,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116485},{"ProcessId":701436251205765,"ProcessNodeId":701431842508933,"Statuz":2,"StatuzDesc":"等待学校审核-暂存","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116486},{"ProcessId":701436251205765,"ProcessNodeId":701431842508933,"Statuz":5,"StatuzDesc":"学校审核-暂存退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116487},{"ProcessId":701436251205765,"ProcessNodeId":701432050229381,"Statuz":6,"StatuzDesc":"等待学校审批-暂存","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116488},{"ProcessId":701436251205765,"ProcessNodeId":701432050229381,"Statuz":9,"StatuzDesc":"学校审批-暂存退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116489},{"ProcessId":702534674399365,"ProcessNodeId":702534414794885,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.64","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728167557},{"ProcessId":702534674399365,"ProcessNodeId":702534414794885,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.643","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728171653},{"ProcessId":702534674399365,"ProcessNodeId":702534572294277,"Statuz":2,"StatuzDesc":"等待学校审批-资金库测试","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.643","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728171654},{"ProcessId":702534674399365,"ProcessNodeId":702534572294277,"Statuz":5,"StatuzDesc":"学校审批-资金库测试退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.643","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728171655},{"ProcessId":703914263199877,"ProcessNodeId":703912581980293,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364229},{"ProcessId":703914263199877,"ProcessNodeId":703912581980293,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364230},{"ProcessId":703914263199877,"ProcessNodeId":703913893118085,"Statuz":2,"StatuzDesc":"等待教育局审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364231},{"ProcessId":703914263199877,"ProcessNodeId":703913893118085,"Statuz":5,"StatuzDesc":"教育局审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364232},{"ProcessId":704429713551493,"ProcessNodeId":703912581980293,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.26","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937619077},{"ProcessId":704429713551493,"ProcessNodeId":703912581980293,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623173},{"ProcessId":704429713551493,"ProcessNodeId":704428996944005,"Statuz":2,"StatuzDesc":"等待学校审批","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623174},{"ProcessId":704429713551493,"ProcessNodeId":704428996944005,"Statuz":5,"StatuzDesc":"学校审批退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623175},{"ProcessId":704429713551493,"ProcessNodeId":703913893118085,"Statuz":6,"StatuzDesc":"等待教育局审核","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623176},{"ProcessId":704429713551493,"ProcessNodeId":703913893118085,"Statuz":9,"StatuzDesc":"教育局审核退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623177},{"ProcessId":705106779930757,"ProcessNodeId":705091701473413,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.653","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272709},{"ProcessId":705106779930757,"ProcessNodeId":705091701473413,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.653","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272710},{"ProcessId":705106779930757,"ProcessNodeId":705091859951749,"Statuz":2,"StatuzDesc":"等待单位审核-指定审核人","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272711},{"ProcessId":705106779930757,"ProcessNodeId":705091859951749,"Statuz":5,"StatuzDesc":"单位审核-指定审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272712},{"ProcessId":705106779930757,"ProcessNodeId":705092139769989,"Statuz":6,"StatuzDesc":"等待财务处审核-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272713},{"ProcessId":705106779930757,"ProcessNodeId":705092139769989,"Statuz":9,"StatuzDesc":"财务处审核-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272714},{"ProcessId":705106779930757,"ProcessNodeId":705092317196421,"Statuz":10,"StatuzDesc":"等待教导主任审核-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":10,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272715},{"ProcessId":705106779930757,"ProcessNodeId":705092317196421,"Statuz":13,"StatuzDesc":"教导主任审核-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":13,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272716},{"ProcessId":705106779930757,"ProcessNodeId":705092469321861,"Statuz":14,"StatuzDesc":"等待校长审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":14,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272717},{"ProcessId":705106779930757,"ProcessNodeId":705092469321861,"Statuz":17,"StatuzDesc":"校长审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":17,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272718},{"ProcessId":705106779930757,"ProcessNodeId":705092789448837,"Statuz":18,"StatuzDesc":"等待区级领导审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":18,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272719},{"ProcessId":705106779930757,"ProcessNodeId":705092789448837,"Statuz":21,"StatuzDesc":"区级领导审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":21,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272720},{"ProcessId":705106779930757,"ProcessNodeId":705093020311685,"Statuz":22,"StatuzDesc":"等待区级财务科审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":22,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272721},{"ProcessId":705106779930757,"ProcessNodeId":705093020311685,"Statuz":25,"StatuzDesc":"区级财务科审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":25,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272722},{"ProcessId":710330317377669,"ProcessNodeId":705091701473413,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262469},{"ProcessId":710330317377669,"ProcessNodeId":705091701473413,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262470},{"ProcessId":710330317377669,"ProcessNodeId":705091859951749,"Statuz":2,"StatuzDesc":"等待单位审核-指定审核人","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262471},{"ProcessId":710330317377669,"ProcessNodeId":705091859951749,"Statuz":5,"StatuzDesc":"单位审核-指定审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262472},{"ProcessId":710330317377669,"ProcessNodeId":705092317196421,"Statuz":6,"StatuzDesc":"等待教导主任审核-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262473},{"ProcessId":710330317377669,"ProcessNodeId":705092317196421,"Statuz":9,"StatuzDesc":"教导主任审核-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262474},{"ProcessId":710330317377669,"ProcessNodeId":705092789448837,"Statuz":10,"StatuzDesc":"等待区级领导审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":10,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262475},{"ProcessId":710330317377669,"ProcessNodeId":705092789448837,"Statuz":13,"StatuzDesc":"区级领导审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":13,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262476}]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 403
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 413
【执行完成结果】：[{"BusinessType":3,"ProcessId":667811814981765,"ProcessNodeId":30000,"AuditUnitId":592123124052101,"AuditUserId":592123693924485,"Remark":"","Enabled":true,"ModuleId":667811814981765,"GroupValue":0,"TypeBox":3,"IsDeleted":false,"CreateId":592123693924485,"CreateBy":"yucai","CreateTime":"2025-04-21T13:52:15.203","ModifyId":592123693924485,"ModifyBy":"yucai","ModifyTime":"2025-04-21T13:52:15.2","Version":0,"Id":667850887905413}]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 453
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((f.Statuz == 1) AndAlso (f.ModuleId == value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId)) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 463
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 505
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 516
【执行完成结果】：[{"BusinessType":3,"ProcessId":658679024259205,"ProcessNodeId":30000,"AuditUnitId":592123124052101,"AuditUserId":592123693924485,"Remark":"","Enabled":true,"ModuleId":658679024259205,"GroupValue":0,"TypeBox":3,"IsDeleted":false,"CreateId":592123693924485,"CreateBy":"yucai","CreateTime":"2025-04-25T14:34:41.43","ModifyId":592123693924485,"ModifyBy":"yucai","ModifyTime":"2025-04-25T14:34:41.427","Version":0,"Id":669276894855301}]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 556
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((f.Statuz == 1) AndAlso (f.ModuleId == value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId)) 
【携带的参数JSON】：  
【响应时间】：9ms
【执行完成时间】：2025-08-28 06:51:41 565
【执行完成结果】：[{"ModuleId":658679024259205,"Name":"简单流程项目","ShowName":"项目列表","ProcessIds":"658689418428549,661475468013701","Unittype":1,"SourceData":4,"UseType":3,"PageType":0,"PageSize":100,"TotalName":"总计：","TotalCounmn":"SchoolName","UseUnitId":592122710352005,"Logo":null,"Statuz":1,"DefaultSort":"PlanYear","DefaultWhere":"Statuz = 2","PkId":"","SortType":2,"UseUnitField":"SchoolId","UseUserField":"","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"ProjectList","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-02T13:30:38.25","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-21T18:11:11.567","Version":0,"Id":661121584091269},{"ModuleId":658679024259205,"Name":"das","ShowName":"asd","ProcessIds":"658689418428549","Unittype":1,"SourceData":4,"UseType":1,"PageType":0,"PageSize":20,"TotalName":"dasd","TotalCounmn":"SchoolName","UseUnitId":592122710352005,"Logo":null,"Statuz":1,"DefaultSort":"fsd","DefaultWhere":"","PkId":"","SortType":1,"UseUnitField":"SchoolId","UseUserField":"UserId","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"ProjectList","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-02T15:55:42.03","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-24T13:44:54.243","Version":0,"Id":661157234729093},{"ModuleId":658679024259205,"Name":"测试项目清单1A","ShowName":"测试项目清单","ProcessIds":"658689418428549,661475468013701","Unittype":3,"SourceData":4,"UseType":1,"PageType":0,"PageSize":0,"TotalName":"总计：","TotalCounmn":"Name","UseUnitId":592122937471109,"Logo":null,"Statuz":1,"DefaultSort":"ModifyTime","DefaultWhere":null,"PkId":"","SortType":2,"UseUnitField":"SchoolId","UseUserField":"","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"ProjectList","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-08T15:57:07.69","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-19T13:53:25.74","Version":0,"Id":663280952029317},{"ModuleId":658679024259205,"Name":"测试基础信息显示","ShowName":"测试基础信息显示","ProcessIds":"658689418428549","Unittype":1,"SourceData":1,"UseType":0,"PageType":0,"PageSize":0,"TotalName":null,"TotalCounmn":null,"UseUnitId":592122710352005,"Logo":null,"Statuz":1,"DefaultSort":null,"DefaultWhere":null,"PkId":"","SortType":0,"UseUnitField":"","UseUserField":"","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-18T18:19:04.117","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-21T18:07:01.363","Version":0,"Id":699059169710213}]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 610
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 620
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 660
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 671
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 712
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 723
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 764
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 775
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 815
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：11ms
【执行完成时间】：2025-08-28 06:51:41 826
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 874
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 885
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 925
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 936
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:41|
【操作时间】：2025-08-28 06:51:41 976
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-28 06:51:41 987
【执行完成结果】：[]

--------------------------------
2025/8/28 18:51:42|
【操作时间】：2025-08-28 06:51:41 990
【当前操作用户】：yucai 
【当前执行方法】：IsExistGroupProcessSet 
【携带的参数有】：  
【携带的参数JSON】：  
【响应时间】：13ms
【执行完成时间】：2025-08-28 06:51:42 004
【执行完成结果】：true

--------------------------------
2025/8/28 18:51:42|
【操作时间】：2025-08-28 06:51:42 006
【当前操作用户】：yucai 
【当前执行方法】：IsExistPermissionSet 
【携带的参数有】：  
【携带的参数JSON】：  
【响应时间】：26ms
【执行完成时间】：2025-08-28 06:51:42 033
【执行完成结果】：false

--------------------------------
2025/8/28 18:51:42|
【操作时间】：2025-08-28 06:51:42 729
【当前操作用户】：yucai 
【当前执行方法】：QueryById 
【携带的参数有】： 592123693924485, True 
【携带的参数JSON】：  
【响应时间】：9ms
【执行完成时间】：2025-08-28 06:51:42 738
【执行完成结果】：{"StaffNumber":null,"UnitId":592123124052101,"IdNumber":null,"Name":"育才小学","Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"UserId":0,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"VerifiedMobile":null,"UserType":1,"AdministratorType":2,"Wechat":null,"HeadPortrait":null,"Enabled":true,"UnitType":0,"UnitPId":0,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-09-19T14:17:30.2","ModifyId":592123595010181,"ModifyBy":"浦口教育局","ModifyTime":"2025-08-28T16:16:01.043","Version":0,"Id":592123693924485}

--------------------------------
2025/8/28 18:51:42|
【操作时间】：2025-08-28 06:51:42 743
【当前操作用户】：yucai 
【当前执行方法】：GetByUserId 
【携带的参数有】： 592123693924485 
【携带的参数JSON】：  
【响应时间】：14ms
【执行完成时间】：2025-08-28 06:51:42 757
【执行完成结果】：{"Id":592123693924485,"Name":"育才小学","AreaId":826,"UnitId":592123124052101,"UnitName":"浦口育才小学","UnitType":3,"UnitPId":592122937471109,"UnitPName":"南京市浦口教育局","AcctId":592123696050309,"AcctName":"yucai","RoleIds":null,"RoleNames":null,"StaffNumber":null,"IdNumber":null,"Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"NickName":null,"CreateId":0,"AcctStatuz":1,"UserType":1,"AdministratorType":2,"UserValidate":null,"IsThirdClient":null,"Pwd":null,"DepartmentIds":null,"StrRoleIds":null,"StrRoleNames":null,"HeadPortrait":null,"UnitStatus":1}

--------------------------------
2025/8/28 18:51:45|
【操作时间】：2025-08-28 06:51:45 556
【当前操作用户】：yucai 
【当前执行方法】：GetPaged 
【携带的参数有】： Hyun.Core.Model.XUniformSchemeParam 
【携带的参数JSON】：  
【响应时间】：166ms
【执行完成时间】：2025-08-28 06:51:45 723
【执行完成结果】：{"page":1,"pageCount":1,"dataCount":20,"PageSize":20,"data":[{"SchoolId":0,"SchemeYear":2024,"SchemeNo":"**********","SolicitedNum":20,"SolicitedDeadline":"2024-11-29T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":"2025-06-20T15:36:26.43","ReleaseTimeStr":null,"SolicitedStatuz":1,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p style=\"text-indent: 32pt; text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-size: 16px; font-family: 黑体;\">关于征订校服的征求家长意见书</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">尊敬的各位家长：</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">校服是校园文化的标志，是学生团体精神风貌的一种体现。您的孩子现正处于学习文化，掌握各种技能的大好时光，学生统一着装不仅能够提升我校师生的精神面貌，展现我校师生的整体风采，而且能消除学生间相互攀比的不良习气，培养学生的团队精神，增强集体荣誉感，杜绝学生穿奇装异服，形成正确的审美观；也有利于学校的教育管理及学生的安全管理，在开展校外活动，如参观、游行、校外实践活动时，便于教师的统一组织与管理，也可以提醒司机注意学生的安全。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">经学校研究决定，拟为学生统一校服，家长可自愿选择是否订购。 </span></p><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 微软雅黑;\">校服明细：</span></p><p style=\"text-indent: 24pt; text-align: justify;\"><br></p><table style=\"width: 100%;\"><tbody><tr><th colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">类别</th><th colSpan=\"1\" rowSpan=\"1\" width=\"180\">品 名</th><th colSpan=\"1\" rowSpan=\"1\" width=\"82\">单位</th><th colSpan=\"1\" rowSpan=\"1\" width=\"151\">预估单价（元）</th></tr><tr><td colspan=\"1\" rowspan=\"2\" width=\"120\" style=\"text-align: center;\">夏季运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">T恤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">休闲运动夏裤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">春秋运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">运动服</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"4\" width=\"120\" style=\"text-align: center;\">春秋礼服</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">西服上衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colSpan=\"1\" rowSpan=\"1\" width=\"151\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男裤/女裙</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">衬衫</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男领带/女领花</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">个</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">冬衣</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">摇粒绒内胆冲锋衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr></tbody></table><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\">本表为举例，请根据学校实际情况填写。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> </span></p><p style=\"text-indent: 350pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; XXX学校</span></p><p style=\"text-indent: 336pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20XX-XX-XX</span></p><p style=\"text-indent: 336pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp;</span></p><p><br></p><p><br></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2025-06-19T09:53:02.437","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2025-06-20T15:36:26.947","Version":0,"Id":688671870677125},{"SchoolId":0,"SchemeYear":2025,"SchemeNo":null,"SolicitedNum":23,"SolicitedDeadline":"2024-11-28T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":null,"ReleaseTimeStr":null,"SolicitedStatuz":0,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p style=\"text-indent: 32pt; text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-size: 16px; font-family: 黑体;\">关于征订校服的征求家长意见书</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">尊敬的各位家长：</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">校服是校园文化的标志，是学生团体精神风貌的一种体现。您的孩子现正处于学习文化，掌握各种技能的大好时光，学生统一着装不仅能够提升我校师生的精神面貌，展现我校师生的整体风采，而且能消除学生间相互攀比的不良习气，培养学生的团队精神，增强集体荣誉感，杜绝学生穿奇装异服，形成正确的审美观；也有利于学校的教育管理及学生的安全管理，在开展校外活动，如参观、游行、校外实践活动时，便于教师的统一组织与管理，也可以提醒司机注意学生的安全。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">经学校研究决定，拟为学生统一校服，家长可自愿选择是否订购。 </span></p><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 微软雅黑;\">校服明细：</span></p><table style=\"width: 100%;\"><tbody><tr><th colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">类别</th><th colSpan=\"1\" rowSpan=\"1\" width=\"180\">品 名</th><th colSpan=\"1\" rowSpan=\"1\" width=\"82\">单位</th><th colSpan=\"1\" rowSpan=\"1\" width=\"151\">预估单价（元）</th></tr><tr><td colspan=\"1\" rowspan=\"2\" width=\"120\" style=\"text-align: center;\">夏季运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">T恤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">休闲运动夏裤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">春秋运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">运动服</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"4\" width=\"120\" style=\"text-align: center;\">春秋礼服</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">西服上衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colSpan=\"1\" rowSpan=\"1\" width=\"151\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男裤/女裙</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">衬衫</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男领带/女领花</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">个</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">冬衣</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">摇粒绒内胆冲锋衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr></tbody></table><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\">本表为举例，请根据学校实际情况填写。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><br></p><p style=\"text-indent: 350px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; XXX学校</span></p><p style=\"text-indent: 336px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20XX-XX-XX</span></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2025-06-19T09:52:52.927","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2025-06-19T09:52:52.407","Version":0,"Id":688671831707781},{"SchoolId":0,"SchemeYear":2025,"SchemeNo":null,"SolicitedNum":333,"SolicitedDeadline":"2025-02-28T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":null,"ReleaseTimeStr":null,"SolicitedStatuz":0,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p><br></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2025-02-20T14:12:50.543","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2025-02-20T14:22:46.473","Version":0,"Id":646622285959301},{"SchoolId":0,"SchemeYear":2024,"SchemeNo":null,"SolicitedNum":201,"SolicitedDeadline":"2024-12-28T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":null,"ReleaseTimeStr":null,"SolicitedStatuz":0,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p><br></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2024-12-18T17:42:38.133","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2025-02-20T14:11:34.427","Version":0,"Id":624024605302917},{"SchoolId":0,"SchemeYear":2024,"SchemeNo":null,"SolicitedNum":101,"SolicitedDeadline":"2024-12-27T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":null,"ReleaseTimeStr":null,"SolicitedStatuz":0,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p><br></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2024-12-18T17:38:51.913","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2025-02-20T14:11:46.883","Version":0,"Id":624023678697605},{"SchoolId":0,"SchemeYear":2024,"SchemeNo":"**********","SolicitedNum":20,"SolicitedDeadline":"2024-12-28T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":"2024-12-17T10:31:02.923","ReleaseTimeStr":null,"SolicitedStatuz":1,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p><br></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2024-12-17T10:30:51.933","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2024-12-17T10:31:03.433","Version":0,"Id":623564599107717},{"SchoolId":0,"SchemeYear":2024,"SchemeNo":null,"SolicitedNum":66,"SolicitedDeadline":"2024-11-30T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":null,"ReleaseTimeStr":null,"SolicitedStatuz":0,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p style=\"text-indent: 32pt; text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-size: 16px; font-family: 黑体;\">关于征订校服的征求家长意见书</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">尊敬的各位家长：</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">校服是校园文化的标志，是学生团体精神风貌的一种体现。您的孩子现正处于学习文化，掌握各种技能的大好时光，学生统一着装不仅能够提升我校师生的精神面貌，展现我校师生的整体风采，而且能消除学生间相互攀比的不良习气，培养学生的团队精神，增强集体荣誉感，杜绝学生穿奇装异服，形成正确的审美观；也有利于学校的教育管理及学生的安全管理，在开展校外活动，如参观、游行、校外实践活动时，便于教师的统一组织与管理，也可以提醒司机注意学生的安全。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">经学校研究决定，拟为学生统一校服，家长可自愿选择是否订购。 </span></p><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 微软雅黑;\">校服明细：</span></p><table style=\"width: 100%;\"><tbody><tr><th colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">类别</th><th colSpan=\"1\" rowSpan=\"1\" width=\"180\">品 名</th><th colSpan=\"1\" rowSpan=\"1\" width=\"82\">单位</th><th colSpan=\"1\" rowSpan=\"1\" width=\"151\">预估单价（元）</th></tr><tr><td colspan=\"1\" rowspan=\"2\" width=\"120\" style=\"text-align: center;\">夏季运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">T恤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">休闲运动夏裤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">春秋运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">运动服</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"4\" width=\"120\" style=\"text-align: center;\">春秋礼服</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">西服上衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colSpan=\"1\" rowSpan=\"1\" width=\"151\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男裤/女裙</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">衬衫</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男领带/女领花</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">个</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">冬衣</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">摇粒绒内胆冲锋衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr></tbody></table><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\">本表为举例，请根据学校实际情况填写。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><br></p><p style=\"text-indent: 350px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; XXX学校</span></p><p style=\"text-indent: 336px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20XX-XX-XX</span></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2024-11-13T10:21:01.06","ModifyId":0,"ModifyBy":"","ModifyTime":"2024-11-13T10:21:01.06","Version":0,"Id":611529769316485},{"SchoolId":0,"SchemeYear":2024,"SchemeNo":null,"SolicitedNum":23,"SolicitedDeadline":"2024-11-30T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":null,"ReleaseTimeStr":null,"SolicitedStatuz":0,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p style=\"text-indent: 32pt; text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-size: 16px; font-family: 黑体;\">关于征订校服的征求家长意见书</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">尊敬的各位家长：</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">校服是校园文化的标志，是学生团体精神风貌的一种体现。您的孩子现正处于学习文化，掌握各种技能的大好时光，学生统一着装不仅能够提升我校师生的精神面貌，展现我校师生的整体风采，而且能消除学生间相互攀比的不良习气，培养学生的团队精神，增强集体荣誉感，杜绝学生穿奇装异服，形成正确的审美观；也有利于学校的教育管理及学生的安全管理，在开展校外活动，如参观、游行、校外实践活动时，便于教师的统一组织与管理，也可以提醒司机注意学生的安全。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">经学校研究决定，拟为学生统一校服，家长可自愿选择是否订购。 </span></p><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 微软雅黑;\">校服明细：</span></p><table style=\"width: 100%;\"><tbody><tr><th colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">类别</th><th colSpan=\"1\" rowSpan=\"1\" width=\"180\">品 名</th><th colSpan=\"1\" rowSpan=\"1\" width=\"82\">单位</th><th colSpan=\"1\" rowSpan=\"1\" width=\"151\">预估单价（元）</th></tr><tr><td colspan=\"1\" rowspan=\"2\" width=\"120\" style=\"text-align: center;\">夏季运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">T恤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">休闲运动夏裤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">春秋运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">运动服</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"4\" width=\"120\" style=\"text-align: center;\">春秋礼服</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">西服上衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colSpan=\"1\" rowSpan=\"1\" width=\"151\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男裤/女裙</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">衬衫</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男领带/女领花</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">个</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">冬衣</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">摇粒绒内胆冲锋衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr></tbody></table><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\">本表为举例，请根据学校实际情况填写。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><br></p><p style=\"text-indent: 350px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; XXX学校</span></p><p style=\"text-indent: 336px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20XX-XX-XX</span></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2024-11-13T10:18:27.347","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2024-11-13T10:20:00.75","Version":0,"Id":611529139757189},{"SchoolId":0,"SchemeYear":2025,"SchemeNo":"**********","SolicitedNum":23,"SolicitedDeadline":"2024-11-28T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":"2024-11-12T16:27:16.07","ReleaseTimeStr":null,"SolicitedStatuz":1,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p style=\"text-indent: 32pt; text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-size: 16px; font-family: 黑体;\">关于征订校服的征求家长意见书</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">尊敬的各位家长：</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">校服是校园文化的标志，是学生团体精神风貌的一种体现。您的孩子现正处于学习文化，掌握各种技能的大好时光，学生统一着装不仅能够提升我校师生的精神面貌，展现我校师生的整体风采，而且能消除学生间相互攀比的不良习气，培养学生的团队精神，增强集体荣誉感，杜绝学生穿奇装异服，形成正确的审美观；也有利于学校的教育管理及学生的安全管理，在开展校外活动，如参观、游行、校外实践活动时，便于教师的统一组织与管理，也可以提醒司机注意学生的安全。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">经学校研究决定，拟为学生统一校服，家长可自愿选择是否订购。 </span></p><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 微软雅黑;\">校服明细：</span></p><table style=\"width: 100%;\"><tbody><tr><th colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">类别</th><th colSpan=\"1\" rowSpan=\"1\" width=\"180\">品 名</th><th colSpan=\"1\" rowSpan=\"1\" width=\"82\">单位</th><th colSpan=\"1\" rowSpan=\"1\" width=\"151\">预估单价（元）</th></tr><tr><td colspan=\"1\" rowspan=\"2\" width=\"120\" style=\"text-align: center;\">夏季运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">T恤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">休闲运动夏裤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">春秋运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">运动服</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"4\" width=\"120\" style=\"text-align: center;\">春秋礼服</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">西服上衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colSpan=\"1\" rowSpan=\"1\" width=\"151\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男裤/女裙</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">衬衫</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男领带/女领花</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">个</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">冬衣</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">摇粒绒内胆冲锋衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr></tbody></table><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\">本表为举例，请根据学校实际情况填写。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><br></p><p style=\"text-indent: 350px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; XXX学校</span></p><p style=\"text-indent: 336px; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20XX-XX-XX</span></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2024-11-12T16:27:14.947","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2024-11-12T16:27:16.59","Version":0,"Id":611265880092805},{"SchoolId":0,"SchemeYear":2024,"SchemeNo":"**********","SolicitedNum":20,"SolicitedDeadline":"2024-11-29T00:00:00","SolicitedDeadlineStr":null,"ReleaseTime":"2024-11-12T16:11:48.23","ReleaseTimeStr":null,"SolicitedStatuz":1,"SolicitedStatuzName":null,"AgreeNum":0,"ResponseNum":0,"AgreeRate":0.0000,"PurchaseMethod":null,"PurchaseMethodName":null,"FilingStatuz":0,"FilingStatuzName":null,"FilingTime":null,"IsFiling":null,"FilingExplanation":null,"ParentOpinion":"<p style=\"text-indent: 32pt; text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-size: 16px; font-family: 黑体;\">关于征订校服的征求家长意见书</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">尊敬的各位家长：</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">校服是校园文化的标志，是学生团体精神风貌的一种体现。您的孩子现正处于学习文化，掌握各种技能的大好时光，学生统一着装不仅能够提升我校师生的精神面貌，展现我校师生的整体风采，而且能消除学生间相互攀比的不良习气，培养学生的团队精神，增强集体荣誉感，杜绝学生穿奇装异服，形成正确的审美观；也有利于学校的教育管理及学生的安全管理，在开展校外活动，如参观、游行、校外实践活动时，便于教师的统一组织与管理，也可以提醒司机注意学生的安全。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-size: 14px; font-family: 仿宋;\">经学校研究决定，拟为学生统一校服，家长可自愿选择是否订购。 </span></p><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 微软雅黑;\">校服明细：</span></p><p style=\"text-indent: 24pt; text-align: justify;\"><br></p><table style=\"width: 100%;\"><tbody><tr><th colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">类别</th><th colSpan=\"1\" rowSpan=\"1\" width=\"180\">品 名</th><th colSpan=\"1\" rowSpan=\"1\" width=\"82\">单位</th><th colSpan=\"1\" rowSpan=\"1\" width=\"151\">预估单价（元）</th></tr><tr><td colspan=\"1\" rowspan=\"2\" width=\"120\" style=\"text-align: center;\">夏季运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">T恤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">休闲运动夏裤</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">春秋运动系列</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">运动服</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"4\" width=\"120\" style=\"text-align: center;\">春秋礼服</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">西服上衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colSpan=\"1\" rowSpan=\"1\" width=\"151\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男裤/女裙</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">条</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">衬衫</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">件</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">男领带/女领花</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">个</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr><tr><td colspan=\"1\" rowspan=\"1\" width=\"120\" style=\"text-align: center;\">冬衣</td><td colspan=\"1\" rowspan=\"1\" width=\"180\" style=\"text-align: center;\">摇粒绒内胆冲锋衣</td><td colspan=\"1\" rowspan=\"1\" width=\"82\" style=\"text-align: center;\">套</td><td colspan=\"1\" rowspan=\"1\" width=\"151\" style=\"text-align: center;\"></td></tr></tbody></table><p style=\"text-indent: 24pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\">本表为举例，请根据学校实际情况填写。</span></p><p style=\"text-indent: 28pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> </span></p><p style=\"text-indent: 350pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; XXX学校</span></p><p style=\"text-indent: 336pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20XX-XX-XX</span></p><p style=\"text-indent: 336pt; text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 仿宋;\"> &nbsp;</span></p><p><br></p><p><br></p>","Memo":null,"SchoolName":null,"CountyId":0,"CountyName":null,"CountyAreaId":0,"PurchaseMethodIds":null,"PurchaseMethodNames":null,"Enabled":true,"OptStatuz":0,"AttachmentIdList":null,"AttachmentId":0,"IsSolicitedEnd":0,"IsCountyManager":0,"IsPublicity":0,"IsDeleted":false,"CreateId":0,"CreateBy":"yucai","CreateTime":"2024-11-12T16:11:47.09","ModifyId":0,"ModifyBy":"yucai","ModifyTime":"2024-11-12T16:11:48.743","Version":0,"Id":611262079447173}],"Other":null,"Statistics":null}

--------------------------------
2025/8/28 18:51:57|
【操作时间】：2025-08-28 06:51:57 210
【当前操作用户】： 
【当前执行方法】：GetSchoolNumListNum 
【携带的参数有】： 2, 5 
【携带的参数JSON】：  
【响应时间】：122ms
【执行完成时间】：2025-08-28 06:51:57 332
【执行完成结果】：{"page":1,"pageCount":0,"dataCount":0,"PageSize":20,"data":[],"Other":null,"Statistics":null}

--------------------------------
2025/8/28 18:51:57|
【操作时间】：2025-08-28 06:51:57 210
【当前操作用户】： 
【当前执行方法】：GetByModule 
【携带的参数有】： 8002, 0, 0, 0, 0 
【携带的参数JSON】：  
【响应时间】：128ms
【执行完成时间】：2025-08-28 06:51:57 338
【执行完成结果】：[{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面标题","TypeCode":"8002_DLYM_BJ","ConfigType":0,"UnitType":0,"ConfigValue":"中小学校服管理与备案平台 ","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.793","ValueType":2,"ComboValues":"","Remark":"登录页面标题名称","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.283","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.793","Version":0,"Id":235},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面主图片","TypeCode":"8002_DLYM_ZTP","ConfigType":0,"UnitType":0,"ConfigValue":"/images/loginv10/login_bg.jpg","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.823","ValueType":8,"ComboValues":"","Remark":"登录页面图片","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.823","Version":0,"Id":236},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面背景","TypeCode":"8002_DLYM_BJTP","ConfigType":0,"UnitType":0,"ConfigValue":"/images/loginv10/main_bg.jpg","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.863","ValueType":8,"ComboValues":"","Remark":"登录页面背景","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.863","Version":0,"Id":237},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面Logo","TypeCode":"8002_DLYM_LOGO","ConfigType":0,"UnitType":0,"ConfigValue":"NULL","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.893","ValueType":8,"ComboValues":"","Remark":"登录页面Logo，没有输入 NULL","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.893","Version":0,"Id":238},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"ICP备案","TypeCode":"8002_DLYM_ICPBA","ConfigType":0,"UnitType":0,"ConfigValue":"苏ICP备10041657号-2","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.92","ValueType":2,"ComboValues":"","Remark":"ICP备案，没有输入 NULL","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.92","Version":0,"Id":239},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"ICP备案地址","TypeCode":"8002_DLYM_ICPBADZ","ConfigType":0,"UnitType":0,"ConfigValue":"https://beian.miit.gov.cn","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.94","ValueType":2,"ComboValues":"","Remark":"ICP备案地址，一般不用修改","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.94","Version":0,"Id":240},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"公安备案","TypeCode":"8002_DLYM_GABA","ConfigType":0,"UnitType":0,"ConfigValue":"苏ICP备10041657号-2","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.947","ValueType":2,"ComboValues":"","Remark":"公安备案，没有输入 NULL","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.947","Version":0,"Id":241},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"公安备案地址","TypeCode":"8002_DLYM_GABADZ","ConfigType":0,"UnitType":0,"ConfigValue":"https://beian.mps.gov.cn/#/query/webSearch","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.957","ValueType":2,"ComboValues":"","Remark":"公安备案地址，一般不用修改","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.957","Version":0,"Id":242},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"技术支持","TypeCode":"8002_DLYM_JSZC","ConfigType":0,"UnitType":0,"ConfigValue":"南京骏飞科技有限公司001","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.97","ValueType":2,"ComboValues":"","Remark":"技术支持","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.97","Version":0,"Id":243},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"技术支持地址","TypeCode":"8002_DLYM_JSZCDZ","ConfigType":0,"UnitType":0,"ConfigValue":"http://www.njjfkj.com","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-22T10:22:08.973","ValueType":2,"ComboValues":"","Remark":"技术支持地址，一般不用修改","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-22T10:22:08.973","Version":0,"Id":244}]

--------------------------------
2025/8/28 18:51:57|
【操作时间】：2025-08-28 06:51:57 214
【当前操作用户】： 
【当前执行方法】：GetXfArticlePaged 
【携带的参数有】： Hyun.Core.Model.XfArticleParam 
【携带的参数JSON】：  
【响应时间】：139ms
【执行完成时间】：2025-08-28 06:51:57 354
【执行完成结果】：{"page":1,"pageCount":1,"dataCount":3,"PageSize":20,"data":[{"Cid":599139195162757,"Sid":0,"Title":"轮播图片001标题001","Subtitle":null,"ShortTitle":null,"Remark":"<p><br></p>","ArticleType":0,"HasImage":false,"ImageUrl":"http://localhost:9291/uploadfile/201001/20250227/250227a3f04678360b48ea8938199659cb76e3.jpg","ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2024-10-16T13:35:28.967","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2024-10-16T13:33:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"轮播图片001","CateType":3,"Code":1009,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T18:51:57.3542369+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-27T10:53:36.37","Version":0,"Id":601668515696773},{"Cid":599139195162757,"Sid":0,"Title":"轮播图片001标题002","Subtitle":null,"ShortTitle":null,"Remark":"<p><br></p>","ArticleType":0,"HasImage":false,"ImageUrl":"http://localhost:9291/uploadfile/201001/20250227/250227a3279f6054c24f008423f224fc066523.jpg","ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2024-10-16T13:36:30.277","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2024-10-16T13:36:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"轮播图片001","CateType":3,"Code":1009,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T18:51:57.3542484+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-27T10:53:25.71","Version":0,"Id":601668766806149},{"Cid":599139195162757,"Sid":0,"Title":"轮播图片001标题003","Subtitle":null,"ShortTitle":null,"Remark":"<p><br></p>","ArticleType":0,"HasImage":false,"ImageUrl":"http://localhost:9291/uploadfile/201001/20250227/250227deaf1aa762f4443fbffb1490a582bdd4.jpg","ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2024-10-16T13:36:56.683","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2024-10-16T13:36:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"轮播图片001","CateType":3,"Code":1009,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T18:51:57.3542539+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-27T10:53:16.77","Version":0,"Id":***************}],"Other":null,"Statistics":null}

--------------------------------
2025/8/28 18:51:57|
【操作时间】：2025-08-28 06:51:57 317
【当前操作用户】： 
【当前执行方法】：Query 
【携带的参数有】： f => (((f.Pid == Convert(Convert(0, Int64), Nullable`1)) AndAlso (f.CateType != Convert(3, Nullable`1))) AndAlso (f.IsShowBottom == 1)) 
【携带的参数JSON】：  
【响应时间】：23ms
【执行完成时间】：2025-08-28 06:51:57 341
【执行完成结果】：[{"Pid":0,"Name":"网站信息002","Depth":0,"Path":"598857827360901","Sort":2,"Icon1":"","Icon2":"","ConfigCode":"100201","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":100201,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-08T14:58:45.757","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-21T14:20:41.37","Version":0,"Id":598857827360901},{"Pid":0,"Name":"网站信息001","Depth":0,"Path":"599229278314629","Sort":1,"Icon1":"","Icon2":"","ConfigCode":"1001","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1001,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-09T16:10:12.03","ModifyId":0,"ModifyBy":"","ModifyTime":"2024-10-09T16:10:12.03","Version":0,"Id":599229278314629},{"Pid":0,"Name":"平台客服","Depth":0,"Path":"603449047175301","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1002","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1002,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-21T14:20:29.03","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:11.597","Version":0,"Id":603449047175301},{"Pid":0,"Name":"关于我们","Depth":0,"Path":"604204555731077","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1003","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1003,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-23T17:34:39.353","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:15.03","Version":0,"Id":604204555731077},{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}]

--------------------------------
2025/8/28 18:51:59|
【操作时间】：2025-08-28 06:51:59 206
【当前操作用户】： 
【当前执行方法】：Query 
【携带的参数有】： f => (f.Code == value(Hyun.Core.Api.XUniformArticleController+<>c__DisplayClass20_0).code) 
【携带的参数JSON】：  
【响应时间】：8ms
【执行完成时间】：2025-08-28 06:51:59 215
【执行完成结果】：[{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}]

--------------------------------
2025/8/28 18:51:59|
【操作时间】：2025-08-28 06:51:59 217
【当前操作用户】： 
【当前执行方法】：QueryById 
【携带的参数有】： 644092336705669 
【携带的参数JSON】：  
【响应时间】：9ms
【执行完成时间】：2025-08-28 06:51:59 227
【执行完成结果】：{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}

--------------------------------
2025/8/28 18:51:59|
【操作时间】：2025-08-28 06:51:59 260
【当前操作用户】： 
【当前执行方法】：Query 
【携带的参数有】： f => ((f.Pid == Convert(Convert(0, Int64), Nullable`1)) AndAlso (f.CateType == value(Hyun.Core.Api.XUniformArticleController+<>c__DisplayClass20_1).obj.CateType)) 
【携带的参数JSON】：  
【响应时间】：6ms
【执行完成时间】：2025-08-28 06:51:59 267
【执行完成结果】：[{"Pid":0,"Name":"网站信息002","Depth":0,"Path":"598857827360901","Sort":2,"Icon1":"","Icon2":"","ConfigCode":"100201","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":100201,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-08T14:58:45.757","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-21T14:20:41.37","Version":0,"Id":598857827360901},{"Pid":0,"Name":"网站信息003","Depth":0,"Path":"598858178769029","Sort":3,"Icon1":"","Icon2":"","ConfigCode":"100301","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":100301,"IsShowBottom":0,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-08T15:00:11.56","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-23T17:34:22.727","Version":0,"Id":598858178769029},{"Pid":0,"Name":"网站信息004","Depth":0,"Path":"599132688167045","Sort":4,"Icon1":"","Icon2":"","ConfigCode":"1004","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1004,"IsShowBottom":0,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-09T09:37:10.453","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-09T16:15:50.553","Version":0,"Id":599132688167045},{"Pid":0,"Name":"网站信息001","Depth":0,"Path":"599229278314629","Sort":1,"Icon1":"","Icon2":"","ConfigCode":"1001","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1001,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-09T16:10:12.03","ModifyId":0,"ModifyBy":"","ModifyTime":"2024-10-09T16:10:12.03","Version":0,"Id":599229278314629},{"Pid":0,"Name":"平台客服","Depth":0,"Path":"603449047175301","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1002","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1002,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-21T14:20:29.03","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:11.597","Version":0,"Id":603449047175301},{"Pid":0,"Name":"关于我们","Depth":0,"Path":"604204555731077","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1003","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1003,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-23T17:34:39.353","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:15.03","Version":0,"Id":604204555731077},{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}]

--------------------------------
2025/8/28 18:51:59|
【操作时间】：2025-08-28 06:51:59 272
【当前操作用户】： 
【当前执行方法】：GetXfArticlePaged 
【携带的参数有】： Hyun.Core.Model.XfArticleParam 
【携带的参数JSON】：  
【响应时间】：15ms
【执行完成时间】：2025-08-28 06:51:59 287
【执行完成结果】：{"page":1,"pageCount":1,"dataCount":2,"PageSize":20,"data":[{"Cid":644092336705669,"Sid":0,"Title":"用户协议","Subtitle":null,"ShortTitle":null,"Remark":"<h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">一、用户注册须知</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">1. 注册信息</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户注册时应提供真实、准确、完整、有效的个人信息，包括但不限于姓名、身份证号、手机号、电子邮箱等。这些信息将用于实名认证、密码找回、账号安全验证等后续服务。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应确保所填写的信息为本人所有，并承诺不冒用他人信息进行注册。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">• 如信息发生变化，用户应及时更新个人信息，以确保账号的安全性和有效性。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">2. 账号安全</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应妥善保管自己的账号和密码，不得将账号转让或借予他人使用。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应定期更换密码，并避免使用过于简单或容易被猜测的密码。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如因保管疏忽导致账号被盗用，用户将自行承担责任。同时，用户应对以其账号在平台上进行的所有活动负责。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">3. 合法合规</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户不得使用平台发送或传播任何违法、虚假、骚扰性、侮辱性、恐吓性、伤害性、破坏性、挑衅性、淫秽色情性等内容的信息。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应遵守国家法律法规和平台规定，不得从事任何违法活动或损害平台利益的行为。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">4. 禁止行为</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户不得采用测试、欺骗等非法手段盗取其他用户的账号，也不得利用平台服务从事任何侵犯他人权益的活动。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户不得恶意注册多个账号进行刷单、刷评论等不正当行为。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">二、用户登录须知</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">1. 登录方式</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户可以使用注册时填写的用户名、手机号或身份证号等作为登录名，结合密码进行登录。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•部分平台还支持通过第三方账号（如微信、支付宝等）进行快捷登录。用户在使用第三方账号登录时，应确保第三方账号的安全性和有效性。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">2. 验证码验证</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•为了保障账号安全，平台在登录时可能会要求用户输入短信验证码或进行其他形式的身份验证。用户应积极配合完成验证过程。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如用户未收到验证码或验证码无法正常使用，应及时联系平台客服进行处理。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">3. 异地登录监测</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">平台可能会建立异地登录监测机制。当用户在常用地点之外的地方登录时，系统可能会发送短信或邮件提醒用户。用户应及时关注并确认登录情况，以确保账号安全。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">4. 登录异常处理</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如用户遇到登录问题（如忘记密码、账号被盗等），应及时联系平台客服进行处理。平台将根据实际情况提供相应的帮助和支持。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应妥善保管好与账号相关的所有信息（如验证码、密码等），避免泄露给第三方或在不安全的网络环境下进行登录操作。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">三、其他注意事项</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">1. 隐私保护</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•平台将严格遵守相关法律法规，保护用户的个人隐私安全。用户在使用平台服务时，应同意并遵守平台的隐私政策。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•平台将采取必要的技术措施和管理措施，确保用户个人信息的安全性和保密性。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">2. 版权保护</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•平台上的所有内容（包括但不限于文字、图片、音频、视频等）均受版权保护。用户不得擅自复制、传播或用于其他商业用途。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如用户需要使用平台上的内容，应事先获得版权所有者的授权或同意。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">3. 服务费用</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•部分平台提供的服务可能需要支付一定的费用。用户在使用收费服务前，应仔细阅读服务条款并确认支付相关费用。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应确保支付信息的准确性和安全性，避免因支付问题导致的经济损失或账号安全问题。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">4. 用户协议</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户在注册和登录时，应仔细阅读并同意平台的用户协议。该协议是用户与平台之间的重要约定，具有法律效力。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应遵守用户协议中的各项规定和条款，如有违反将承担相应的法律责任和后果。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">综上所述，用户在注册和登录网站平台时，应遵守相关法律法规和平台规定，确保个人信息安全和合法权益不受侵害。同时，用户也应积极维护平台秩序和良好氛围，共同营造一个健康、和谐的网络环境。</span></h5><h5 style=\"text-align: start;\"></h5>","ArticleType":0,"HasImage":false,"ImageUrl":null,"ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2025-02-13T10:39:44.877","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2025-02-13T10:39:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"用户条款","CateType":1,"Code":1010,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T18:51:59.2870295+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-19T16:54:15.973","Version":0,"Id":644092655071365},{"Cid":644092336705669,"Sid":0,"Title":"隐私协议","Subtitle":null,"ShortTitle":null,"Remark":"<p><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">您在使用百度公司提供的各项服务之前，请您务必审慎阅读、充分理解本协议各条款内容，特别是以粗体标注的部分，包括但不限于免除或者限制责任的条款。如您不同意本服务协议及/或随时对其的修改，您可以主动停止使用百度公司提供的服务；您一旦使用百度公司提供的服务，即视为您已了解并完全同意本服务协议各项内容，包括百度公司对服务协议随时所做的任何修改，并成为我们的用户。</span></p>","ArticleType":0,"HasImage":false,"ImageUrl":null,"ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2025-02-13T10:39:17.827","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2025-02-13T10:39:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"用户条款","CateType":1,"Code":1010,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-28T18:51:59.2870499+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-13T10:39:17.827","Version":0,"Id":644092544282757}],"Other":null,"Statistics":null}

--------------------------------
2025/8/29 15:26:20|
【操作时间】：2025-08-29 03:26:19 504
【当前操作用户】： 
【当前执行方法】：GetSchoolNumListNum 
【携带的参数有】： 2, 5 
【携带的参数JSON】：  
【响应时间】：852ms
【执行完成时间】：2025-08-29 03:26:20 356
【执行完成结果】：{"page":1,"pageCount":0,"dataCount":0,"PageSize":20,"data":[],"Other":null,"Statistics":null}

--------------------------------
2025/8/29 15:26:25|
【操作时间】：2025-08-29 03:26:25 406
【当前操作用户】： 
【当前执行方法】：GetByModule 
【携带的参数有】： 8002, 0, 0, 0, 0 
【携带的参数JSON】：  
【响应时间】：318ms
【执行完成时间】：2025-08-29 03:26:25 725
【执行完成结果】：[{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面标题","TypeCode":"8002_DLYM_BJ","ConfigType":0,"UnitType":0,"ConfigValue":"中小学校服管理与备案平台 11","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.45","ValueType":2,"ComboValues":"","Remark":"登录页面标题名称","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.283","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.45","Version":0,"Id":235},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面主图片","TypeCode":"8002_DLYM_ZTP","ConfigType":0,"UnitType":0,"ConfigValue":"/images/loginv10/login_bg.jpg","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.463","ValueType":8,"ComboValues":"","Remark":"登录页面图片","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.463","Version":0,"Id":236},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面背景","TypeCode":"8002_DLYM_BJTP","ConfigType":0,"UnitType":0,"ConfigValue":"/images/loginv10/main_bg.jpg","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.47","ValueType":8,"ComboValues":"","Remark":"登录页面背景","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.47","Version":0,"Id":237},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"登录页面Logo","TypeCode":"8002_DLYM_LOGO","ConfigType":0,"UnitType":0,"ConfigValue":"NULL","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.477","ValueType":8,"ComboValues":"","Remark":"登录页面Logo，没有输入 NULL","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.477","Version":0,"Id":238},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"ICP备案","TypeCode":"8002_DLYM_ICPBA","ConfigType":0,"UnitType":0,"ConfigValue":"苏ICP备10041657号-2","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.487","ValueType":2,"ComboValues":"","Remark":"ICP备案，没有输入 NULL","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.487","Version":0,"Id":239},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"ICP备案地址","TypeCode":"8002_DLYM_ICPBADZ","ConfigType":0,"UnitType":0,"ConfigValue":"https://beian.miit.gov.cn","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.493","ValueType":2,"ComboValues":"","Remark":"ICP备案地址，一般不用修改","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.493","Version":0,"Id":240},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"公安备案","TypeCode":"8002_DLYM_GABA","ConfigType":0,"UnitType":0,"ConfigValue":"苏ICP备10041657号-2","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.5","ValueType":2,"ComboValues":"","Remark":"公安备案，没有输入 NULL","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.5","Version":0,"Id":241},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"公安备案地址","TypeCode":"8002_DLYM_GABADZ","ConfigType":0,"UnitType":0,"ConfigValue":"https://beian.mps.gov.cn/#/query/webSearch","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.507","ValueType":2,"ComboValues":"","Remark":"公安备案地址，一般不用修改","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.507","Version":0,"Id":242},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"技术支持","TypeCode":"8002_DLYM_JSZC","ConfigType":0,"UnitType":0,"ConfigValue":"南京骏飞科技有限公司001","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.51","ValueType":2,"ComboValues":"","Remark":"技术支持","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.51","Version":0,"Id":243},{"ModuleName":"登录页面","ModuleCode":"8002","TypeName":"技术支持地址","TypeCode":"8002_DLYM_JSZCDZ","ConfigType":0,"UnitType":0,"ConfigValue":"http://www.njjfkj.com","Statuz":1,"UnitId":0,"UserId":0,"RegDate":"2025-08-29T10:01:57.52","ValueType":2,"ComboValues":"","Remark":"技术支持地址，一般不用修改","Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2024-10-21T16:54:08.287","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-29T10:01:57.52","Version":0,"Id":244}]

--------------------------------
2025/8/29 15:26:31|
【操作时间】：2025-08-29 03:26:31 283
【当前操作用户】： 
【当前执行方法】：SearchLoginList 
【携带的参数有】： ::ffff:127.0.0.1 
【携带的参数JSON】：  
【响应时间】：280ms
【执行完成时间】：2025-08-29 03:26:31 563
【执行完成结果】：[]

--------------------------------
2025/8/29 15:26:32|
【操作时间】：2025-08-29 03:26:31 578
【当前操作用户】： 
【当前执行方法】：Login 
【携带的参数有】： admin, e10adc3949ba59abbe56e057f20f883e, ::ffff:127.0.0.1 
【携带的参数JSON】：  
【响应时间】：212ms
【执行完成时间】：2025-08-29 03:26:32 791
【执行完成结果】：{"flag":2,"msg":"登录失败。可能原因：用户名或密码错误。","data":{"total":0,"headers":null,"rows":null,"footer":null,"other":null,"other1":null,"other2":null,"other3":null},"userId":0,"unitId":0,"Id":0}

--------------------------------
2025/8/29 15:26:33|
【操作时间】：2025-08-29 03:26:33 245
【当前操作用户】： 
【当前执行方法】：Query 
【携带的参数有】： f => (f.Code == value(Hyun.Core.Api.XUniformArticleController+<>c__DisplayClass20_0).code) 
【携带的参数JSON】：  
【响应时间】：59ms
【执行完成时间】：2025-08-29 03:26:33 305
【执行完成结果】：[{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}]

--------------------------------
2025/8/29 15:26:33|
【操作时间】：2025-08-29 03:26:33 245
【当前操作用户】： 
【当前执行方法】：Query 
【携带的参数有】： f => (((f.Pid == Convert(Convert(0, Int64), Nullable`1)) AndAlso (f.CateType != Convert(3, Nullable`1))) AndAlso (f.IsShowBottom == 1)) 
【携带的参数JSON】：  
【响应时间】：74ms
【执行完成时间】：2025-08-29 03:26:33 320
【执行完成结果】：[{"Pid":0,"Name":"网站信息002","Depth":0,"Path":"598857827360901","Sort":2,"Icon1":"","Icon2":"","ConfigCode":"100201","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":100201,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-08T14:58:45.757","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-21T14:20:41.37","Version":0,"Id":598857827360901},{"Pid":0,"Name":"网站信息001","Depth":0,"Path":"599229278314629","Sort":1,"Icon1":"","Icon2":"","ConfigCode":"1001","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1001,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-09T16:10:12.03","ModifyId":0,"ModifyBy":"","ModifyTime":"2024-10-09T16:10:12.03","Version":0,"Id":599229278314629},{"Pid":0,"Name":"平台客服","Depth":0,"Path":"603449047175301","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1002","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1002,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-21T14:20:29.03","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:11.597","Version":0,"Id":603449047175301},{"Pid":0,"Name":"关于我们","Depth":0,"Path":"604204555731077","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1003","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1003,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-23T17:34:39.353","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:15.03","Version":0,"Id":604204555731077},{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}]

--------------------------------
2025/8/29 15:26:33|
【操作时间】：2025-08-29 03:26:33 322
【当前操作用户】： 
【当前执行方法】：QueryById 
【携带的参数有】： 644092336705669 
【携带的参数JSON】：  
【响应时间】：24ms
【执行完成时间】：2025-08-29 03:26:33 346
【执行完成结果】：{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}

--------------------------------
2025/8/29 15:26:33|
【操作时间】：2025-08-29 03:26:33 378
【当前操作用户】： 
【当前执行方法】：Query 
【携带的参数有】： f => ((f.Pid == Convert(Convert(0, Int64), Nullable`1)) AndAlso (f.CateType == value(Hyun.Core.Api.XUniformArticleController+<>c__DisplayClass20_1).obj.CateType)) 
【携带的参数JSON】：  
【响应时间】：16ms
【执行完成时间】：2025-08-29 03:26:33 395
【执行完成结果】：[{"Pid":0,"Name":"网站信息002","Depth":0,"Path":"598857827360901","Sort":2,"Icon1":"","Icon2":"","ConfigCode":"100201","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":100201,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-08T14:58:45.757","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-21T14:20:41.37","Version":0,"Id":598857827360901},{"Pid":0,"Name":"网站信息003","Depth":0,"Path":"598858178769029","Sort":3,"Icon1":"","Icon2":"","ConfigCode":"100301","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":100301,"IsShowBottom":0,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-08T15:00:11.56","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-23T17:34:22.727","Version":0,"Id":598858178769029},{"Pid":0,"Name":"网站信息004","Depth":0,"Path":"599132688167045","Sort":4,"Icon1":"","Icon2":"","ConfigCode":"1004","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1004,"IsShowBottom":0,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-09T09:37:10.453","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-10-09T16:15:50.553","Version":0,"Id":599132688167045},{"Pid":0,"Name":"网站信息001","Depth":0,"Path":"599229278314629","Sort":1,"Icon1":"","Icon2":"","ConfigCode":"1001","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1001,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-09T16:10:12.03","ModifyId":0,"ModifyBy":"","ModifyTime":"2024-10-09T16:10:12.03","Version":0,"Id":599229278314629},{"Pid":0,"Name":"平台客服","Depth":0,"Path":"603449047175301","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1002","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1002,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-21T14:20:29.03","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:11.597","Version":0,"Id":603449047175301},{"Pid":0,"Name":"关于我们","Depth":0,"Path":"604204555731077","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1003","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1003,"IsShowBottom":1,"IsMany":2,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-10-23T17:34:39.353","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2024-11-13T10:07:15.03","Version":0,"Id":604204555731077},{"Pid":0,"Name":"用户条款","Depth":0,"Path":"644092336705669","Sort":0,"Icon1":"","Icon2":"","ConfigCode":"1010","CateType":1,"UserId":100000000000000,"UnitTypeId":0,"Code":1010,"IsShowBottom":1,"IsMany":1,"Enabled":true,"CateTypeName":null,"PName":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-02-13T10:38:27.15","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-02-13T10:38:27.147","Version":0,"Id":644092336705669}]

--------------------------------
2025/8/29 15:26:33|
【操作时间】：2025-08-29 03:26:33 404
【当前操作用户】： 
【当前执行方法】：GetXfArticlePaged 
【携带的参数有】： Hyun.Core.Model.XfArticleParam 
【携带的参数JSON】：  
【响应时间】：18ms
【执行完成时间】：2025-08-29 03:26:33 423
【执行完成结果】：{"page":1,"pageCount":1,"dataCount":2,"PageSize":20,"data":[{"Cid":644092336705669,"Sid":0,"Title":"用户协议","Subtitle":null,"ShortTitle":null,"Remark":"<h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">一、用户注册须知</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">1. 注册信息</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户注册时应提供真实、准确、完整、有效的个人信息，包括但不限于姓名、身份证号、手机号、电子邮箱等。这些信息将用于实名认证、密码找回、账号安全验证等后续服务。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应确保所填写的信息为本人所有，并承诺不冒用他人信息进行注册。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">• 如信息发生变化，用户应及时更新个人信息，以确保账号的安全性和有效性。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">2. 账号安全</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应妥善保管自己的账号和密码，不得将账号转让或借予他人使用。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应定期更换密码，并避免使用过于简单或容易被猜测的密码。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如因保管疏忽导致账号被盗用，用户将自行承担责任。同时，用户应对以其账号在平台上进行的所有活动负责。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">3. 合法合规</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户不得使用平台发送或传播任何违法、虚假、骚扰性、侮辱性、恐吓性、伤害性、破坏性、挑衅性、淫秽色情性等内容的信息。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应遵守国家法律法规和平台规定，不得从事任何违法活动或损害平台利益的行为。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">4. 禁止行为</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户不得采用测试、欺骗等非法手段盗取其他用户的账号，也不得利用平台服务从事任何侵犯他人权益的活动。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户不得恶意注册多个账号进行刷单、刷评论等不正当行为。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">二、用户登录须知</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">1. 登录方式</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户可以使用注册时填写的用户名、手机号或身份证号等作为登录名，结合密码进行登录。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•部分平台还支持通过第三方账号（如微信、支付宝等）进行快捷登录。用户在使用第三方账号登录时，应确保第三方账号的安全性和有效性。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">2. 验证码验证</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•为了保障账号安全，平台在登录时可能会要求用户输入短信验证码或进行其他形式的身份验证。用户应积极配合完成验证过程。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如用户未收到验证码或验证码无法正常使用，应及时联系平台客服进行处理。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">3. 异地登录监测</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">平台可能会建立异地登录监测机制。当用户在常用地点之外的地方登录时，系统可能会发送短信或邮件提醒用户。用户应及时关注并确认登录情况，以确保账号安全。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">4. 登录异常处理</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如用户遇到登录问题（如忘记密码、账号被盗等），应及时联系平台客服进行处理。平台将根据实际情况提供相应的帮助和支持。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应妥善保管好与账号相关的所有信息（如验证码、密码等），避免泄露给第三方或在不安全的网络环境下进行登录操作。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">三、其他注意事项</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">1. 隐私保护</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•平台将严格遵守相关法律法规，保护用户的个人隐私安全。用户在使用平台服务时，应同意并遵守平台的隐私政策。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•平台将采取必要的技术措施和管理措施，确保用户个人信息的安全性和保密性。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">2. 版权保护</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•平台上的所有内容（包括但不限于文字、图片、音频、视频等）均受版权保护。用户不得擅自复制、传播或用于其他商业用途。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•如用户需要使用平台上的内容，应事先获得版权所有者的授权或同意。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">3. 服务费用</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•部分平台提供的服务可能需要支付一定的费用。用户在使用收费服务前，应仔细阅读服务条款并确认支付相关费用。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应确保支付信息的准确性和安全性，避免因支付问题导致的经济损失或账号安全问题。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">4. 用户协议</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户在注册和登录时，应仔细阅读并同意平台的用户协议。该协议是用户与平台之间的重要约定，具有法律效力。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">•用户应遵守用户协议中的各项规定和条款，如有违反将承担相应的法律责任和后果。</span></h5><h5 style=\"text-align: start;\"><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">综上所述，用户在注册和登录网站平台时，应遵守相关法律法规和平台规定，确保个人信息安全和合法权益不受侵害。同时，用户也应积极维护平台秩序和良好氛围，共同营造一个健康、和谐的网络环境。</span></h5><h5 style=\"text-align: start;\"></h5>","ArticleType":0,"HasImage":false,"ImageUrl":null,"ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2025-02-13T10:39:44.877","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2025-02-13T10:39:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"用户条款","CateType":1,"Code":1010,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T15:26:33.4231688+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-19T16:54:15.973","Version":0,"Id":644092655071365},{"Cid":644092336705669,"Sid":0,"Title":"隐私协议","Subtitle":null,"ShortTitle":null,"Remark":"<p><span style=\"color: rgb(51, 51, 51); font-size: 12px;\">您在使用百度公司提供的各项服务之前，请您务必审慎阅读、充分理解本协议各条款内容，特别是以粗体标注的部分，包括但不限于免除或者限制责任的条款。如您不同意本服务协议及/或随时对其的修改，您可以主动停止使用百度公司提供的服务；您一旦使用百度公司提供的服务，即视为您已了解并完全同意本服务协议各项内容，包括百度公司对服务协议随时所做的任何修改，并成为我们的用户。</span></p>","ArticleType":0,"HasImage":false,"ImageUrl":null,"ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2025-02-13T10:39:17.827","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2025-02-13T10:39:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"用户条款","CateType":1,"Code":1010,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T15:26:33.4232112+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-13T10:39:17.827","Version":0,"Id":644092544282757}],"Other":null,"Statistics":null}

--------------------------------
2025/8/29 15:26:33|
【操作时间】：2025-08-29 03:26:33 079
【当前操作用户】： 
【当前执行方法】：GetXfArticlePaged 
【携带的参数有】： Hyun.Core.Model.XfArticleParam 
【携带的参数JSON】：  
【响应时间】：333ms
【执行完成时间】：2025-08-29 03:26:33 412
【执行完成结果】：{"page":1,"pageCount":1,"dataCount":3,"PageSize":20,"data":[{"Cid":599139195162757,"Sid":0,"Title":"轮播图片001标题001","Subtitle":null,"ShortTitle":null,"Remark":"<p><br></p>","ArticleType":0,"HasImage":false,"ImageUrl":"http://localhost:9291/uploadfile/201001/20250227/250227a3f04678360b48ea8938199659cb76e3.jpg","ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2024-10-16T13:35:28.967","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2024-10-16T13:33:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"轮播图片001","CateType":3,"Code":1009,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T15:26:33.4108299+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-27T10:53:36.37","Version":0,"Id":601668515696773},{"Cid":599139195162757,"Sid":0,"Title":"轮播图片001标题002","Subtitle":null,"ShortTitle":null,"Remark":"<p><br></p>","ArticleType":0,"HasImage":false,"ImageUrl":"http://localhost:9291/uploadfile/201001/20250227/250227a3279f6054c24f008423f224fc066523.jpg","ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2024-10-16T13:36:30.277","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2024-10-16T13:36:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"轮播图片001","CateType":3,"Code":1009,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T15:26:33.4116077+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-27T10:53:25.71","Version":0,"Id":601668766806149},{"Cid":599139195162757,"Sid":0,"Title":"轮播图片001标题003","Subtitle":null,"ShortTitle":null,"Remark":"<p><br></p>","ArticleType":0,"HasImage":false,"ImageUrl":"http://localhost:9291/uploadfile/201001/20250227/250227deaf1aa762f4443fbffb1490a582bdd4.jpg","ImgAlt":null,"Attachment":null,"Source":"","Author":"","Keywords":null,"IsRecommend":false,"AreaId":0,"IsAllowCompany":false,"UserId":100000000000000,"Sort":0,"RegDate":"2024-10-16T13:36:56.683","IsHot":false,"IsFoc":false,"Statuz":2,"Hits":0,"AttachDownCount":0,"CommentCount":0,"AuditorId":0,"AuditDate":"0001-01-01T00:00:00","Description":null,"BeginTime":"2024-10-16T13:36:00","EndTime":"0001-01-01T00:00:00","UnitId":0,"UnitPid":0,"ExtProjectName":null,"ExtConstDime":null,"ExtImpleSchool":null,"ExtProjectCost":null,"ExtConstrucCompany":null,"ExtBrandSupport":null,"Enabled":true,"CategoryName":"轮播图片001","CateType":3,"Code":1009,"IsMany":1,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T15:26:33.4116272+08:00","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-02-27T10:53:16.77","Version":0,"Id":***************}],"Other":null,"Statistics":null}

--------------------------------
2025/8/29 15:26:56|
【操作时间】：2025-08-29 03:26:56 428
【当前操作用户】： 
【当前执行方法】：SearchLoginList 
【携带的参数有】： ::ffff:127.0.0.1 
【携带的参数JSON】：  
【响应时间】：20ms
【执行完成时间】：2025-08-29 03:26:56 448
【执行完成结果】：[{"UserId":0,"AccountName":"admin","UserIp":"::ffff:127.0.0.1","Type":5,"Statuz":0,"Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T15:26:32.27","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T15:26:31.757","Version":0,"Id":***************}]

--------------------------------
2025/8/29 15:26:56|
【操作时间】：2025-08-29 03:26:56 549
【当前操作用户】： 
【当前执行方法】：SearchLoginList 
【携带的参数有】： ::ffff:127.0.0.1 
【携带的参数JSON】：  
【响应时间】：25ms
【执行完成时间】：2025-08-29 03:26:56 574
【执行完成结果】：[{"UserId":0,"AccountName":"admin","UserIp":"::ffff:127.0.0.1","Type":5,"Statuz":0,"Enabled":true,"IsDeleted":false,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T15:26:32.27","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T15:26:31.757","Version":0,"Id":***************}]

