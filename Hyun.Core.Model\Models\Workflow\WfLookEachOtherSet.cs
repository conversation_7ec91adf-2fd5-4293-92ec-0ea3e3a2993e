﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批节点是否允许互看配置
    ///</summary>
    [SugarTable("wf_LookEachOtherSet","审批节点是否允许互看配置")]
    public class WfLookEachOtherSet : BaseEntity
    {

          public WfLookEachOtherSet()
          {

          }

           /// <summary>
           ///模块Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long ModuleId { get; set; }

           /// <summary>
           ///流程配置表Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long ProcessId { get; set; }

           /// <summary>
           ///流程节点表Id，项目库节点Id 20000、数据报表节点Id 30000
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long ProcessNodeId { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long UnitId { get; set; }

           /// <summary>
           ///是否允许互看（1：是 ，2：否）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int IsLook { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

