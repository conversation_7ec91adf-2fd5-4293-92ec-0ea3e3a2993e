﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfMsgConfig接口方法
    ///</summary>
    public interface IWfMsgConfigServices : IBaseServices<WfMsgConfig>
    {

        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="o">WfMsgConfig对象</param>
        /// <returns></returns>
        Task<Result<string>> Add(WfProcessMsgConfigModel o);

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        Task<Result<string>> Edit(WfMsgConfigDto m);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">WfMsgConfigParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<WfMsgConfigDto>> GetPaged(WfMsgConfigParam param);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="porcessId"></param>
        /// <returns></returns>
        Task<List<dropdownModel>> GetProcessNode(long porcessId);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        Task<Result<string>> BatchSetStatuz(MsgConfigStatuzModel m);

    }
}

