﻿using Confluent.Kafka;
using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Model;
using Hyun.Old.Util;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.HPSF;
using NPOI.Util;
using System.Collections.Generic;
using System.Data;
using System.Reflection.Metadata;

namespace Hyun.Core.Api
{

    [Route("api/hyun/dcdangerchemicalsapply")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class DcDangerChemicalsApplyController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IDcApplyConfirmDetailServices dcApplyConfirmDetailManager;
        private readonly IDcWasteRecordServices dcWasteRecordManager;
        private readonly IDcApplyServices dcApplyManager;
        private readonly IDcBrandStockServices dcBrandStockManager;
        private readonly IDcModelStockServices dcModelStockManager;
        private readonly IDcSchoolMaterialServices dcSchoolMaterialManager;
        private readonly IDcApplyApprovalServices dcApplyApprovalManager;
        private readonly IDcApplyConfirmServices dcApplyConfirmManager;
        private readonly ISysUserExtensionServices userManager;
        private readonly IDcMessageServices dcMessageManager;
        private readonly IBConfigSetServices configSetManager;
        private readonly IUser user;


        public DcDangerChemicalsApplyController(IMapper _mapper, IWebHostEnvironment _env, IDcApplyConfirmDetailServices _dcApplyConfirmDetailManager, IDcWasteRecordServices _dcWasteRecordManager, IDcApplyServices _dcApplyManager, IDcBrandStockServices _dcBrandStockManager, IDcModelStockServices _dcModelStockManager, IDcSchoolMaterialServices _dcSchoolMaterialManager, IDcApplyApprovalServices _dcApplyApprovalManager, IDcApplyConfirmServices _dcApplyConfirmManager, ISysUserExtensionServices _userManager, IDcMessageServices _dcMessageManager, IBConfigSetServices _configSetManager, IUser _user)
        {
            mapper = _mapper;
            env = _env;
            dcApplyConfirmDetailManager = _dcApplyConfirmDetailManager;
            dcWasteRecordManager = _dcWasteRecordManager;
            dcApplyManager = _dcApplyManager;
            dcBrandStockManager = _dcBrandStockManager;
            dcModelStockManager = _dcModelStockManager;
            dcSchoolMaterialManager = _dcSchoolMaterialManager;
            dcApplyApprovalManager = _dcApplyApprovalManager;
            dcApplyConfirmManager = _dcApplyConfirmManager;
            userManager = _userManager;
            dcMessageManager = _dcMessageManager;
            configSetManager = _configSetManager;
            user = _user;
        }

        /// <summary>
        /// 危化品管理：已填报列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplylistfind")]
        public async Task<Result<PageModel<VDcApply>>> DcApplyList_Find([FromBody] VDcApplyParam param)
        {
            param.ApplyStatuzgt = 0;
            param.unitId = user.UnitId;
            param.userId = user.ID;
            PageModel<VDcApply> pg = await dcApplyManager.GetApplyedPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：申请确认
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyafterconfirm")]
        public async Task<Result<string>> DcApplyAfterConfirm([FromBody] ApplyAfterConfirmModel o)
        {
            return await dcApplyConfirmDetailManager.AfterConfirm(o.id, o.surplusStatuz, o.wasetStatuz, o.backNum, user.UnitId, user.ID, o.wasteList);
        }

        /// <summary>
        /// 危化品管理：更新退回数量
        /// </summary>
        /// <param name="id"></param>
        /// <param name="backNum"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyupdatebacknum")]
        public async Task<Result<string>> DcApplyUpdateBackNum(long id, decimal backNum)
        {
            return await dcApplyConfirmDetailManager.UpdateBackNum(id, backNum, user.UnitId, user.ID);
        }


        /// <summary>
        /// 危化品管理：领用待审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplycarlistfind")]
        public async Task<Result<PageModel<VDcApplyAudit>>> DcApplyCarList_Find([FromBody] VDcApplyAuditParam param)
        {
            Result r = new Result();
            param.SchoolId = user.UnitId;
            param.UserId = user.ID;
            PageModel<VDcApplyAudit> pg = await dcApplyManager.GetAuditPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：根据Id查询申领数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyfindbyid")]
        public async Task<Result<DcApplyDto>> DcApply_FindById(long id)
        {
            DcApply apply = await dcApplyManager.QueryById(id);
            return baseSucc(mapper.Map<DcApplyDto>(apply), 1, "查询成功");
        }

        /// <summary>
        /// 危化品管理：申领新增修改
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyinsertupdate")]
        public async Task<Result<string>> DcApply_InsertUpdate([FromBody] DcApplyDto o)
        {
            return await dcApplyManager.InsertUpdate(o.Id, o.SchoolCatalogId, o.SchoolMaterialModelId, o.SchoolMaterialBrandId, o.Num, o.Remark, o.UseTime, 0, o.WithUserId, user.UnitId, user.ID);
        }

        /// <summary>
        /// 危化品管理：申请添加
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyadd")]
        public async Task<Result<string>> DcApplyAdd([FromBody] DcApplyDto o)
        {
            return await dcApplyManager.InsertUpdate(o.Id, o.SchoolCatalogId, o.SchoolMaterialModelId, o.SchoolMaterialBrandId, o.Num, o.Remark, o.UseTime, 0, o.WithUserId, user.UnitId, user.ID);
        }

        /// <summary>
        /// 危化品管理：申请批量添加
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplybatchadd")]
        public async Task<Result<string>> DcApplyBatchAdd([FromBody] List<DcApplyDto> o)
        {
            string msg = "";
            int successNum = 0; //执行成功数量
            foreach (var m in o)
            {
                Result<string> r = await dcApplyManager.InsertUpdate(m.Id, m.SchoolCatalogId, m.SchoolMaterialModelId, m.SchoolMaterialBrandId, m.Num, m.Remark, m.UseTime, 0, m.WithUserId, user.UnitId, user.ID);
                if (r.flag == 1)
                {
                    successNum += 1;
                }
            }
            if (o.Count == successNum)
            {
                msg = "添加成功。";
                return Result<string>.Success(msg);
            }
            else
            {
                msg = string.Format("共添加{0}条数据，成功{1}条，失败{2}条。失败可能原因：库存数量不足。", o.Count, successNum, (o.Count - successNum));
                return Result<string>.Fail(msg);
            }
        }

        /// <summary>
        /// 危化品管理：领用申请
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyapply")]
        public async Task<Result<string>> DcApply_Apply([FromBody] DcApplyDto o)
        {
            if (o.EmployerType == 1)
            {
                o.EmployerId = user.ID;
            }
            Result<string> r = await dcApplyManager.InsertUpdate(o.Id, o.SchoolCatalogId, o.SchoolMaterialModelId, o.SchoolMaterialBrandId, o.Num, o.Remark, o.UseTime, 0, o.WithUserId, user.UnitId, user.ID);
            if (r.flag == 1)
            {
                r = await dcApplyManager.Apply(o.Id, user.UnitId, user.ID);
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：批量领用申请
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="useTime"></param>
        /// <param name="withUserId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplybatchapply")]
        public async Task<Result> DcApply_BatchApply(string ids, DateTime useTime, long withUserId)
        {
            Result r = new Result();
            if (useTime < DateTime.Now)
            {
                r.flag = 0;
                r.msg = "使用时间不得早于当前时间。";
                return r;
            }
            r = await dcApplyManager.IsApplyMsg(ids, user.UnitId, user.ID);
            if (r.flag == 1)
            {
                r = await dcApplyManager.BatchApply(ids, useTime, withUserId, user.UnitId, user.ID);
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：批量领用申请
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplydirectapply")]
        public async Task<Result<string>> DcApply_DirectApply([FromBody] DcApplyDto o)
        {
            if (o.UseTime < DateTime.Now)
            {
                return Result<string>.Fail("使用时间不得早于当前时间");
            }
            return await dcApplyManager.InsertUpdate(o.Id, o.SchoolCatalogId, o.SchoolMaterialModelId, o.SchoolMaterialBrandId, o.Num, o.Remark, o.UseTime, 1, o.WithUserId, user.UnitId, user.ID);
        }

        /// <summary>
        /// 危化品管理：根据Id申领删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplydelete")]
        public async Task<Result<string>> DcApply_Delete(long id)
        {
            return await dcApplyManager.DeleteConfirm(id, user.UnitId, user.ID);
        }

        /// <summary>
        ///  危化品管理：申请查看
        /// </summary>
        /// <param name="id"></param>
        /// <param name="viewType"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyview")]
        public async Task<Result<VDcApplyAudit>> DcApply_View(long id,int viewType=0)
        {
            Result<VDcApplyAudit> r = new Result<VDcApplyAudit>();
            if(viewType == 0)
            {
                 r = await dcApplyConfirmDetailManager.ApplyView(id, user.UnitId, user.ID);
            }
            else
            {
                 r = await dcApplyConfirmDetailManager.ApplyDetailView(id, viewType);
            }
            return baseSucc(r.data.rows, 1, r.msg, r.data.other);
        }

        /// <summary>
        /// 危化品管理：获取库存数量
        /// </summary>
        /// <param name="schoolCatalogId"></param>
        /// <param name="schoolMaterialModelId"></param>
        /// <param name="schoolMaterialBrandId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplygetstocknum")]
        public async Task<Result> DcApply_GetStockNum(long schoolCatalogId, long schoolMaterialModelId, long schoolMaterialBrandId)
        {
            Result r = new Result();
            decimal stockNum = await dcApplyManager.GetStockNum(user.UnitId, schoolCatalogId, schoolMaterialModelId, schoolMaterialBrandId);
            r.flag = 1;
            r.msg = "查询成功。";
            r.data.rows = stockNum.ToString("G0");
            return r;
        }

        /// <summary>
        /// 危化品管理：领用审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyauditgrouplistfind")]
        public async Task<Result<PageModel<VDcApplyAuditGroupList>>> DcApplyAuditGroupList_Find([FromBody] VDcApplyAuditGroupListParam param)
        {
            PageModel<VDcApplyAuditGroupList> pg = await dcApplyManager.GetAuditGroupPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：通用领用待审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyauditlistfind")]
        public async Task<Result<PageModel<VDcApplyAudit>>> DcApplyAuditList_Find([FromBody] VDcApplyAuditParam param)
        {
            param.SchoolId = user.UnitId;
            PageModel<VDcApplyAudit> pg = await dcApplyManager.GetAuditPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：领用通用已审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyauditedlistfind")]
        public async Task<Result<PageModel<VDcApplyAudited>>> DcApplyAuditedList_Find([FromBody] VDcApplyAuditedParam param)
        {
            Result r = new Result();
            PageModel<VDcApplyAudited> pg = await dcApplyManager.GetAuditedPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：领用审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyaudit")]
        public async Task<Result<string>> DcApplyAudit([FromBody] ApplyAuditModel o)
        {
            return await dcApplyManager.ApplyApprovalInsertUpdate(o.id, o.processNumber, o.statuz, o.remark, user.ID, user.UnitId, o.isWithdraw);
        }

        /// <summary>
        /// 危化品管理：领用批量审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplybatchaudit")]
        public async Task<Result<string>> DcApplyBatchAudit([FromBody] ApplyBatchAuditModel o)
        {
            return await dcApplyManager.BatchSubmit(o.ids, o.statuz, o.remark, o.processNumber, o.currentStatuz, o.isWithdraw, user.ID, user.UnitId);
        }

        /// <summary>
        /// 危化品管理：根据Id获取领用审批信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplynopassreasonget")]
        public async Task<Result> DcApplyNoPassReasonGet(long id)
        {
            Result r = new Result();
            List<DcApplyApproval> listApproval = await dcApplyApprovalManager.Query(f => f.ApplyId == id && f.IsShow == 1);
            if (listApproval.Count > 0)
            {
                DcApplyApproval m = listApproval.OrderByDescending(f => f.Id).FirstOrDefault();
                r.flag = 1;
                r.msg = "查询成功。";
                r.data.rows = mapper.Map<DcApplyApprovalDto>(m);
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：获取单位物品库列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyschoolmaterialfind")]
        public async Task<Result<PageModel<DcSchoolMaterial>>> DcApplySchoolMaterial_Find([FromBody] DcSchoolMaterialParam param)
        {
            Result r = new Result();
            PageModel<DcSchoolMaterial> pg = await dcSchoolMaterialManager.GetPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：领用配货
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyadjust")]
        public async Task<Result> DcApplyAdjust([FromBody] List<LvApplyAdjust> o)
        {
            Result r = new Result();
            foreach (var item in o)
            {
                r = await dcApplyManager.Adjust(item.ApplyId, item.SchoolMaterialId, item.Num, user.ID, user.UnitId);
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：根据Id删除申领配货信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyconfirmdetaildelete")]
        public async Task<Result<string>> DcApplyConfirmDetail_Delete(long id)
        {
            return await dcApplyConfirmDetailManager.DeleteApplyConfirm(id,EnumExtensions.ToEnumInt(DcApplyApprovalStatuz.WaitConfirm), user.ID, user.UnitId);
        }

        /// <summary>
        /// 危化品管理：领用配货数据列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyadjustfind")]
        public async Task<Result<PageModel<VDcApplyAdjust>>> DcApplyAdjust_Find([FromBody] VDcApplyAdjustParam param)
        {
            PageModel<VDcApplyAdjust> pg = await dcApplyManager.GetAdjustPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：领用确认
        /// </summary>
        /// <param name="id"></param>
        /// <param name="num"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyconfirm")]
        public async Task<Result> DcApply_Confirm(long id, decimal num, string remark)
        {
            Result r = new Result();
            r = await dcApplyManager.Confirm(id, num, EnumExtensions.ToEnumInt(DcApplyApprovalStatuz.WaitReceive), remark, EnumExtensions.ToEnumInt(DcApplyApprovalStatuz.WaitConfirm), 0, user.ID, user.UnitId);
            if (r.flag == 1)
            {
                List<DcApplyConfirmDetail> list = await dcApplyConfirmDetailManager.GetApplyConfirmDetail(id, user.UnitId);
                if (list.Count > 0)
                {
                    var apply = await dcApplyManager.GetById(id);
                    if (apply == null) return r;
                    var user = await userManager.GetById(apply.UserId);
                    if (user == null) return r;
                    string withMobile = "";
                    if (apply.ApplyMode == 2) //1：单人领用，2：双人领用,双人密码，3：双人领用,单人密码）
                    {
                        var withUser = await userManager.GetById(apply.WithUserId);
                        withMobile = withUser != null ? withUser.Mobile : "";
                    }
                    string mobile = user.Mobile;
                    string strMessage = ApplicationConfig.SeneMessageEventDcDangerChemicalsDistributeCode; //短信内容
                    string withMessage = ApplicationConfig.SeneMessageEventDcDangerChemicalsDistributeCode; //同领用人，短信内容
                    var messageList = await dcApplyConfirmDetailManager.GetMessage(apply.BatchNo, user.UnitId);
                    var applyMessageList = messageList.Where(f => f.MessageType == 1).ToList();
                    var withMessageList = messageList.Where(f => f.MessageType == 2).ToList();
                    string code = CreateRandomNumCode(8);
                    foreach (var model in list)
                    {
                        DcMessage dcMessage = new DcMessage();
                        dcMessage.TakeBatchNo = model.Id.ToString();
                        dcMessage.ReceiveUserId = apply.UserId;
                        dcMessage.Mobile = mobile;
                        dcMessage.CheckCode = applyMessageList.Count > 0 ? applyMessageList.FirstOrDefault().CheckCode : code.Substring(0, 4);
                        dcMessage.UserId = user.Id;
                        dcMessage.UnitId = user.UnitId;
                        dcMessage.RegDate = DateTime.Now;
                        dcMessage.MessageType = 1;
                        long messageManagerId = await dcMessageManager.Add(dcMessage);
                        if (messageManagerId > 0 && applyMessageList.Count == 0)
                        {
                            //发送验证码 
                            strMessage = string.Format(strMessage, dcMessage.CheckCode, apply.BatchNo);
                        }

                        if (!string.IsNullOrEmpty(withMobile))
                        {
                            dcMessage = new DcMessage();
                            dcMessage.TakeBatchNo = model.Id.ToString();
                            dcMessage.ReceiveUserId = apply.WithUserId;
                            dcMessage.Mobile = withMobile;
                            dcMessage.CheckCode = withMessageList.Count > 0 ? withMessageList.FirstOrDefault().CheckCode : code.Substring(4, 4);
                            dcMessage.UserId = user.Id;
                            dcMessage.UnitId = user.UnitId;
                            dcMessage.RegDate = DateTime.Now;
                            dcMessage.MessageType = 2;
                            long mId = await dcMessageManager.Add(dcMessage);
                            if (mId > 0 && withMessageList.Count == 0)
                            {
                                withMessage = string.Format(withMessage, dcMessage.CheckCode, apply.BatchNo);
                            }
                        }
                    }

                    if (!strMessage.Equals(ApplicationConfig.SeneMessageEventDcDangerChemicalsDistributeCode))
                    {
                        //调用发送短信报错
                        //await SendMessage.SendToMobile(mobile, strMessage);
                    }
                    if (!withMessage.Equals(ApplicationConfig.SeneMessageEventDcDangerChemicalsDistributeCode))
                    {
                        //调用发送短信报错
                        //await SendMessage.SendToMobile(withMobile, withMessage);
                    }
                }
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：批量领用确认
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplybatchconfirm")]
        public async Task<Result> DcApply_BatchConfirm(string ids, string remark)
        {
            Result r = new Result();
            r = await dcApplyManager.BatchConfirm(ids, EnumExtensions.ToEnumInt(DcApplyApprovalStatuz.WaitReceive), remark, EnumExtensions.ToEnumInt(DcApplyApprovalStatuz.WaitConfirm), 0, user.ID, user.UnitId);
            return r;
        }

        /// <summary>
        /// 危化品管理：获取领用清单打印列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyprintlistfind")]
        public async Task<Result<PageModel<VDcApplyConfirmDetailPrintList>>> DcApplyPrintList_Find([FromBody] VDcApplyConfirmDetailPrintListParam param)
        {
            param.SchoolId = user.UnitId;
            PageModel<VDcApplyConfirmDetailPrintList> pg = await dcApplyManager.GetConfirmDetailPrintPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：查询当前领用状态的审核人
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplylookaudituser")]

        public async Task<Result<List<DcUserInfo>>> DcApply_LookAuditUser(long id)
        {
            var r = await dcApplyManager.SearchAuditUser(id, user.UnitId, user.ID);
            return r;
        }

        /// <summary>
        /// 危化品管理：批量撤回已填报危化品信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyrevoke")]
        public async Task<Result<string>> DcApply_Revoke(string ids)
        {
            return await dcApplyManager.Revoke(ids, user.UnitId, user.ID);
        }

        /// <summary>
        /// 危化品管理：领用填报列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyfilllistfind")]
        public async Task<Result<PageModel<VDcApplyFillList>>> DcApplyFillList_Find([FromBody] VDcApplyFillListParam param)
        {
            param.SchoolId = user.UnitId;
            PageModel<VDcApplyFillList> pg = await dcApplyManager.GetFillPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：获取前6条数据信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyoftenuselistfind")]
        public async Task<Result> DcApplyOftenUseList_Find([FromBody] DcApplyParam param)
        {
            Result r = new Result();
            param.SchoolId = user.UnitId;
            param.UserId = user.ID;
            DataTable dt = await dcApplyManager.DcApplyOftenUseList(param);
            r.flag = 1;
            r.msg = "查询成功。";
            r.data.total = dt.Rows.Count;
            r.data.rows = dt;
            return r;
        }

        /// <summary>
        /// 危化品管理：短信发送
        /// </summary>
        /// <param name="id"></param>
        /// <param name="mobile"></param>
        /// <param name="messageType"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyconfirmdetailresendcode")]
        public async Task<Result> DcApplyConfirmDetail_ResendCode(long id, string mobile, int messageType)
        {
            Result r = new Result();
            r = await dcApplyManager.ResendCode(id, mobile, messageType);
            if(r.flag == 1)
            {
                await SendMessage.SendToMobile(mobile, r.data.rows.ToString());
                r.msg = "发放领用码成功。";
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：领用发放
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyconfirmdetailcollar")]
        public async Task<Result<string>> DcApplyConfirmDetail_Collar([FromBody] CollarModel o)
        {
            return await dcApplyManager.Collar(o.id, o.checkCode, o.withCode, o.withUserId, o.num, user.ID, user.UnitId);
        }

        /// <summary>
        /// 危化品管理：单位领用列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyfind")]
        public async Task<Result<PageModel<VDcMaterialApplyDetailList>>> DcApply_Find([FromBody] VDcMaterialApplyDetailListParam param)
        {
            Result r = new Result();
            param.SchoolId = user.UnitId;
            PageModel<VDcMaterialApplyDetailList> pg = await dcApplyConfirmDetailManager.GetStatisticsPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 导出领用台账
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportdcapplyfind")]
        public async Task<IActionResult> ExportDcApply_Find([FromBody] VDcMaterialApplyDetailListParam param)
        {
            Result r = new Result();
            param.SchoolId = user.UnitId;
            param.pageSize = int.MaxValue;
            PageModel<VDcMaterialApplyDetailList> pg = await dcApplyConfirmDetailManager.GetStatisticsPaged(param);
            foreach(VDcMaterialApplyDetailList f in pg.data.ToList())
            {
                f.LastNum = f.Num - f.BackNum;
            }
            var excelBytes = await new ExcelHelper<VDcMaterialApplyDetailList>().ExportExecl(pg.data.ToList(), "领用台账", new string[] { "Name", "Model", "UnitName", "Num", "BackNum", "LastNum", "Remark", "GrantDate", "GrantUserName", "UserName" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "领用台账.xlsx");
        }

        /// <summary>
        /// 危化品管理：根据Id查询领用详情信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyconfirmdetailgetbyid")]
        public async Task<Result> DcApplyConfirmDetail_GetById(long id)
        {
            Result r = new Result();
            var obj = await dcApplyConfirmDetailManager.QueryById(id);
            if (obj != null)
            {
                r.flag = 1;
                r.msg = "查询成功。";
                r.data.rows = obj;
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：危化品退回
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isMayUse"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialrevertbackconfirm")]
        public async Task<Result<string>> DcMaterialRevert_BackConfirm(long id, int isMayUse)
        {
            return await dcApplyManager.BackConfirm(id, isMayUse, 0, "", user.ID, user.UnitId);
        }

        /// <summary>
        /// 危化品管理：单位危化品退回列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialbackfind")]
        public async Task<Result<PageModel<VDcMaterialBackList>>> DcMaterialBack_Find([FromBody] VDcMaterialBackListParam param)
        {
            param.SchoolId = user.UnitId;
            PageModel<VDcMaterialBackList> pg = await dcApplyConfirmDetailManager.GetMaterialBackPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 危化品管理：简易申领列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyeasylistfind")]
        
        public async Task<Result<PageModel<DcApply>>> DcApplyEasyList_Find([FromBody] DcApplyParam param)
        {
            param.SchoolId = user.UnitId;
            PageModel<DcApply> pg = await dcApplyManager.DcApplyEasyList_Find(param);
            return baseSucc(pg, pg.dataCount);
        }


        /// <summary>
        /// 危化品管理：简易领用
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyeasyapply")]
        public async Task<Result> DcApply_EasyApply([FromBody] DcApplyDto o)
        {
            Result r = new Result();
            if (o.Num <= 0)
            {
                r.flag = 0;
                r.msg = "领用数量必须大于0。";
                return r;
            }
            if (o.UseTime.Value < DateTime.Now.AddDays(-1))
            {
                r.flag = 0;
                r.msg = "使用时间必须大于当前时间。";
                return r;
            }
            r = await dcApplyManager.EasyApply(o.SchoolCatalogId, o.SchoolMaterialModelId, o.SchoolMaterialBrandId, o.Num, o.Remark, o.UseTime.Value, o.WithUserId, user.UnitId, user.ID, o.IsNeedSendMessage);
            //发送短信
            if (r.flag == 1 && o.IsNeedSendMessage == 1)
            {
                string message = string.Format(ApplicationConfig.SendMessageEventDcApplyMsg, user.UserName);
                string sendR = await SendMessage.SendToMobile(r.data.footer.ToString(), message);
            }
            return r;
        }

        /// <summary>
        /// 危化品管理：简易退回
        /// </summary>
        /// <param name="applyConfirmDetailId"></param>
        /// <param name="backNum"></param>
        /// <param name="isMayUse"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyeasyback")]
        public async Task<Result<string>> DcApply_EasyBack(long applyConfirmDetailId, decimal backNum, int isMayUse)
        {
            return await dcApplyManager.EasyBack(applyConfirmDetailId, backNum, isMayUse, user.UnitId, user.ID);
        }

        /// <summary>
        /// 危化品管理：简易发放
        /// </summary>
        /// <param name="id"></param>
        /// <param name="num"></param>
        /// <param name="isNeedSendMessage"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyeasygrant")]
        public async Task<Result> DcApply_EasyGrant(long id, decimal num, int isNeedSendMessage)
        {
            Result r = new Result();
            if (num <= 0)
            {
                r.flag = 0;
                r.msg = "数量不能小于等于0";
                return r;
            }
            r = await dcApplyManager.EasyGrant(id, num, isNeedSendMessage);
            if(r.flag == 1 && isNeedSendMessage == 1)
            {
                string message = ApplicationConfig.SendMessageEventDcGrantMsg;
                await SendMessage.SendToMobile(r.data.rows.ToString(), message);
            }
            return r;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="codeCount"></param>
        /// <returns></returns>
        private string CreateRandomNumCode(int codeCount)
        {
            string allChar = "0,1,2,3,4,5,6,7,8,9";
            string[] allCharArray = allChar.Split(',');
            string randomCode = "";
            int temp = -1;

            Random rand = new Random();
            for (int i = 0; i < codeCount; i++)
            {
                if (temp != -1)
                {
                    rand = new Random(i * temp * ((int)DateTime.Now.Ticks));
                }
                int t = rand.Next(10);
                randomCode += allCharArray[t];
            }
            return randomCode;
        }
    }
}
