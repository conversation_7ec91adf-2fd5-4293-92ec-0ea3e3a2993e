﻿using Hyun.Core.Common.Extensions;
using Hyun.Core.Model.Helper;
using Hyun.Core.Model.Models;
using log4net;
using NetTaste;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Hyun.Core.Common.Helper
{
    /// <summary>
    /// List导出到Excel文件
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ExcelHelper<T> where T : new()
    {
        #region List导出到Excel文件
        /// <summary>
        /// List导出到Excel文件
        /// </summary>
        /// <param name="sFileName"></param>
        /// <param name="sHeaderText"></param>
        /// <param name="list"></param>
        public string ExportToExcel(string rootPath,string sFileName, string sHeaderText, List<T> list, string[] columns, string remarkText = "")
        {
            sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), sFileName);
            string sRoot = rootPath;
            string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
            string sDirectory = Path.Combine(sRoot, partDirectory);
            string sFilePath = Path.Combine(sDirectory, sFileName);
            if (!Directory.Exists(sDirectory))
            {
                Directory.CreateDirectory(sDirectory);
            }
            if (remarkText.IsNullOrEmpty())
            {
                using (MemoryStream ms = CreateExportMemoryStream(list, sHeaderText, columns))
                {
                    using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                    {
                        byte[] data = ms.ToArray();
                        fs.Write(data, 0, data.Length);
                        fs.Flush();
                    }
                }
            }
            else
            {
                using (MemoryStream ms = CreateExportMemoryStream(list, remarkText, sHeaderText, columns))
                {
                    using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                    {
                        byte[] data = ms.ToArray();
                        fs.Write(data, 0, data.Length);
                        fs.Flush();
                    }
                }
            }

            return partDirectory + Path.DirectorySeparatorChar + sFileName;
        }

        /// <summary>  
        /// List导出到Excel的MemoryStream  
        /// </summary>  
        /// <param name="list">数据源</param>  
        /// <param name="sHeaderText">表头文本</param>  
        /// <param name="columns">需要导出的属性</param>  
        private MemoryStream CreateExportMemoryStream(List<T> list, string remark, string sHeaderText, string[] columns)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            Type type = typeof(T);
            PropertyInfo[] properties = ReflectionHelper.GetProperties(type, columns);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            //单元格填充循环外设定单元格格式，避免4000行异常

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);

            #region 取得每列的列宽（最大宽度）
            int[] arrColWidth = new int[properties.Length];
            for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
            {
                //GBK对应的code page是CP936
                arrColWidth[columnIndex] = properties[columnIndex].Name.Length;
            }
            #endregion
            for (int rowIndex = 0; rowIndex < list.Count; rowIndex++)
            {
                #region 新建表，填充表头，填充列头，样式
                if (rowIndex == 65535 || rowIndex == 0)
                {
                    if (rowIndex != 0)
                    {
                        sheet = workbook.CreateSheet();
                    }

                    #region 表头以上注释说明
                    {
                        IRow headerRow = sheet.CreateRow(0);
                        headerRow.HeightInPoints = 35;
                        headerRow.CreateCell(0).SetCellValue(remark);

                        ICellStyle headStyle = workbook.CreateCellStyle();
                        headStyle.Alignment = HorizontalAlignment.Left;
                        headStyle.VerticalAlignment = VerticalAlignment.Center;
                        IFont font = workbook.CreateFont();
                        font.FontHeightInPoints = 10;
                        font.IsBold = true;
                        font.FontName = "宋体";
                        font.Color = HSSFColor.OliveGreen.Red.Index;
                        headStyle.SetFont(font);

                        headerRow.GetCell(0).CellStyle = headStyle;

                        sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, properties.Length - 1));
                    }
                    #endregion

                    #region 表头及样式
                    {
                        IRow headerRow = sheet.CreateRow(1);
                        headerRow.HeightInPoints = 35;
                        headerRow.CreateCell(0).SetCellValue(sHeaderText);


                        ICellStyle headStyle = workbook.CreateCellStyle();
                        headStyle.Alignment = HorizontalAlignment.Center;
                        headStyle.VerticalAlignment = VerticalAlignment.Center;
                        IFont font = workbook.CreateFont();
                        font.FontHeightInPoints = 20;
                        font.IsBold = true;
                        font.FontName = "宋体";
                        headStyle.SetFont(font);

                        headerRow.GetCell(0).CellStyle = headStyle;

                        sheet.AddMergedRegion(new CellRangeAddress(1, 1, 0, properties.Length - 1));
                    }
                    #endregion

                    #region 列头及样式
                    {
                        IRow headerRow = sheet.CreateRow(2);
                        headerRow.HeightInPoints = 25;
                        ICellStyle headStyle = workbook.CreateCellStyle();
                        headStyle.Alignment = HorizontalAlignment.Center;
                        headStyle.VerticalAlignment = VerticalAlignment.Center;
                        headStyle.BorderTop = BorderStyle.Thin;
                        headStyle.BorderLeft = BorderStyle.Thin;
                        headStyle.BorderRight = BorderStyle.Thin;
                        headStyle.BorderBottom = BorderStyle.Thin;
                        IFont font = workbook.CreateFont();
                        font.FontHeightInPoints = 10;
                        font.IsBold = true;
                        font.FontName = "宋体";
                        headStyle.SetFont(font);

                        for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
                        {
                            // 类属性如果有Description就用Description当做列名
                            DescriptionAttribute customAttribute = (DescriptionAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(DescriptionAttribute));
                            string description = properties[columnIndex].Name;
                            if (customAttribute != null)
                            {
                                description = customAttribute.Description;
                            }
                            headerRow.CreateCell(columnIndex).SetCellValue(description);
                            headerRow.GetCell(columnIndex).CellStyle = headStyle;
                            //根据表头设置列宽  
                            sheet.SetColumnWidth(columnIndex, (arrColWidth[columnIndex] + 1) * 256);
                            //根据自定义属性设置列宽
                            ExportExcelAttribute excelAttribute = (ExportExcelAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(ExportExcelAttribute));
                            if (excelAttribute != null)
                            {
                                if (excelAttribute.Width > 0)
                                {
                                    sheet.SetColumnWidth(columnIndex, excelAttribute.Width * 256);
                                }
                            }
                        }
                    }
                    #endregion
                }
                #endregion

                #region 填充内容
                IRow dataRow = sheet.CreateRow(rowIndex + 3); // 前面3行已被占用
                dataRow.HeightInPoints = 20;
                for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
                {
                    string drValue = properties[columnIndex].GetValue(list[rowIndex], null).ObjToString();

                    NPOI.SS.UserModel.ICell newCell = dataRow.CreateCell(columnIndex);
                    newCell.CellStyle = contentLeftStyle;

                    //根据单元格内容设定列宽
                    int cellValueLength = System.Text.Encoding.UTF8.GetBytes(drValue).Length + 1;
                    int length = (cellValueLength > 255 ? 255 : cellValueLength) * 256;
                    if (sheet.GetColumnWidth(columnIndex) < length && !drValue.IsEmpty())
                    {
                        sheet.SetColumnWidth(columnIndex, length);
                    }

                    //根据自定义属性设置排列方向
                    ExportExcelAttribute excelAttribute = (ExportExcelAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(ExportExcelAttribute));
                    if (excelAttribute != null)
                    {
                        switch (excelAttribute.Alignment)
                        {
                            case HorizontalAlignment.Left:
                                newCell.CellStyle = contentLeftStyle;
                                break;
                            case HorizontalAlignment.Center:
                                newCell.CellStyle = contentCenterStyle;
                                break;
                            case HorizontalAlignment.Right:
                                newCell.CellStyle = contentRightStyle;
                                break;
                            default:
                                newCell.CellStyle = contentLeftStyle;
                                break;
                        }
                        if (excelAttribute.Width > 0)
                        {
                            sheet.SetColumnWidth(columnIndex, excelAttribute.Width * 256);
                        }
                    }

                    if (drValue.IsEmpty())
                    {
                        newCell.SetCellValue("");
                        continue;
                    }
                    switch (properties[columnIndex].PropertyType.ToString())
                    {
                        case "System.String":
                            newCell.SetCellValue(drValue);
                            break;

                        case "System.DateTime":
                        case "System.Nullable`1[System.DateTime]":
                            newCell.SetCellValue(drValue.ObjToDateTime().ToString("yyyy-MM-dd"));
                            break;

                        case "System.Boolean":
                        case "System.Nullable`1[System.Boolean]":
                            newCell.SetCellValue(drValue.ObjToBool());
                            break;

                        case "System.Byte":
                        case "System.Nullable`1[System.Byte]":
                        case "System.Int16":
                        case "System.Nullable`1[System.Int16]":
                        case "System.Int32":
                        case "System.Nullable`1[System.Int32]":
                            newCell.SetCellValue(drValue.ObjToInt());
                            break;

                        case "System.Int64":
                        case "System.Nullable`1[System.Int64]":
                            newCell.SetCellValue(drValue.ObjToString());
                            break;

                        case "System.Double":
                        case "System.Nullable`1[System.Double]":
                            newCell.SetCellValue(drValue.ObjToDouble());
                            break;

                        case "System.Single":
                        case "System.Nullable`1[System.Single]":
                            newCell.SetCellValue(drValue.ObjToDouble());
                            break;

                        case "System.Decimal":
                        case "System.Nullable`1[System.Decimal]":
                            newCell.SetCellValue(drValue.ObjToDouble());
                            break;

                        case "System.DBNull":
                            newCell.SetCellValue(string.Empty);
                            break;

                        default:
                            newCell.SetCellValue(string.Empty);
                            break;
                    }
                }
                #endregion
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        /// <summary>  
        /// List导出到Excel的MemoryStream  
        /// </summary>  
        /// <param name="list">数据源</param>  
        /// <param name="sHeaderText">表头文本</param>  
        /// <param name="columns">需要导出的属性</param>  
        private MemoryStream CreateExportMemoryStream(List<T> list, string sHeaderText, string[] columns)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            Type type = typeof(T);
            PropertyInfo[] properties = ReflectionHelper.GetProperties(type, columns);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            //单元格填充循环外设定单元格格式，避免4000行异常

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);

            #region 取得每列的列宽（最大宽度）
            int[] arrColWidth = new int[properties.Length];
            for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
            {
                //GBK对应的code page是CP936
                arrColWidth[columnIndex] = properties[columnIndex].Name.Length;
            }
            #endregion
            for (int rowIndex = 0; rowIndex < list.Count; rowIndex++)
            {
                #region 新建表，填充表头，填充列头，样式
                if (rowIndex == 65535 || rowIndex == 0)
                {
                    if (rowIndex != 0)
                    {
                        sheet = workbook.CreateSheet();
                    }

                    #region 表头及样式
                    {
                        IRow headerRow = sheet.CreateRow(0);
                        headerRow.HeightInPoints = 35;
                        headerRow.CreateCell(0).SetCellValue(sHeaderText);

                        ICellStyle headStyle = workbook.CreateCellStyle();
                        headStyle.Alignment = HorizontalAlignment.Center;
                        headStyle.VerticalAlignment = VerticalAlignment.Center;
                        IFont font = workbook.CreateFont();
                        font.FontHeightInPoints = 20;
                        font.IsBold = true;
                        font.FontName = "宋体";
                        headStyle.SetFont(font);

                        headerRow.GetCell(0).CellStyle = headStyle;

                        sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, properties.Length - 1));
                    }
                    #endregion

                    #region 列头及样式
                    {
                        IRow headerRow = sheet.CreateRow(1);
                        headerRow.HeightInPoints = 25;
                        ICellStyle headStyle = workbook.CreateCellStyle();
                        headStyle.Alignment = HorizontalAlignment.Center;
                        headStyle.VerticalAlignment = VerticalAlignment.Center;
                        headStyle.BorderTop = BorderStyle.Thin;
                        headStyle.BorderLeft = BorderStyle.Thin;
                        headStyle.BorderRight = BorderStyle.Thin;
                        headStyle.BorderBottom = BorderStyle.Thin;
                        IFont font = workbook.CreateFont();
                        font.FontHeightInPoints = 10;
                        font.IsBold = true;
                        font.FontName = "宋体";
                        headStyle.SetFont(font);

                        for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
                        {
                            // 类属性如果有Description就用Description当做列名
                            DescriptionAttribute customAttribute = (DescriptionAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(DescriptionAttribute));
                            string description = properties[columnIndex].Name;
                            if (customAttribute != null)
                            {
                                description = customAttribute.Description;
                            }
                            headerRow.CreateCell(columnIndex).SetCellValue(description);
                            headerRow.GetCell(columnIndex).CellStyle = headStyle;
                            //根据表头设置列宽  
                            sheet.SetColumnWidth(columnIndex, (arrColWidth[columnIndex] + 1) * 256);
                            //根据自定义属性设置列宽
                            ExportExcelAttribute excelAttribute = (ExportExcelAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(ExportExcelAttribute));
                            if (excelAttribute != null)
                            {
                                if (excelAttribute.Width > 0)
                                {
                                    sheet.SetColumnWidth(columnIndex, excelAttribute.Width * 256);
                                }
                            }
                        }
                    }
                    #endregion
                }
                #endregion

                #region 填充内容
                IRow dataRow = sheet.CreateRow(rowIndex + 2); // 前面2行已被占用
                dataRow.HeightInPoints = 20;
                for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
                {
                    string drValue = properties[columnIndex].GetValue(list[rowIndex], null).ObjToString();

                    ICell newCell = dataRow.CreateCell(columnIndex);
                    newCell.CellStyle = contentLeftStyle;

                    //根据单元格内容设定列宽
                    int cellValueLength = System.Text.Encoding.UTF8.GetBytes(drValue).Length + 1;
                    int length = (cellValueLength > 255 ? 255 : cellValueLength) * 256;
                    if (sheet.GetColumnWidth(columnIndex) < length && !drValue.IsEmpty())
                    {
                        sheet.SetColumnWidth(columnIndex, length);
                    }

                    //根据自定义属性设置排列方向
                    ExportExcelAttribute excelAttribute = (ExportExcelAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(ExportExcelAttribute));
                    if (excelAttribute != null)
                    {
                        switch (excelAttribute.Alignment)
                        {
                            case HorizontalAlignment.Left:
                                newCell.CellStyle = contentLeftStyle;
                                break;
                            case HorizontalAlignment.Center:
                                newCell.CellStyle = contentCenterStyle;
                                break;
                            case HorizontalAlignment.Right:
                                newCell.CellStyle = contentRightStyle;
                                break;
                            default:
                                newCell.CellStyle = contentLeftStyle;
                                break;
                        }
                        if (excelAttribute.Width > 0)
                        {
                            sheet.SetColumnWidth(columnIndex, excelAttribute.Width * 256);
                        }
                    }

                    if (drValue.IsEmpty())
                    {
                        newCell.SetCellValue("");
                        continue;
                    }
                    switch (properties[columnIndex].PropertyType.ToString())
                    {
                        case "System.String":
                            newCell.CellStyle.WrapText = true;
                            newCell.SetCellValue(drValue);
                            break;

                        case "System.DateTime":
                        case "System.Nullable`1[System.DateTime]":
                            newCell.SetCellValue(drValue.ObjToDateTime().ToString("yyyy-MM-dd"));
                            break;

                        case "System.Boolean":
                        case "System.Nullable`1[System.Boolean]":
                            newCell.SetCellValue(drValue.ObjToBool());
                            break;

                        case "System.Byte":
                        case "System.Nullable`1[System.Byte]":
                        case "System.Int16":
                        case "System.Nullable`1[System.Int16]":
                        case "System.Int32":
                        case "System.Nullable`1[System.Int32]":
                            newCell.SetCellValue(drValue.ObjToInt());
                            break;

                        case "System.Int64":
                        case "System.Nullable`1[System.Int64]":
                            newCell.SetCellValue(drValue.ObjToString());
                            break;

                        case "System.Double":
                        case "System.Nullable`1[System.Double]":
                            newCell.SetCellValue(drValue.ObjToDouble());
                            break;

                        case "System.Single":
                        case "System.Nullable`1[System.Single]":
                            newCell.SetCellValue(drValue.ObjToDouble());
                            break;

                        case "System.Decimal":
                        case "System.Nullable`1[System.Decimal]":
                            newCell.SetCellValue(drValue.ObjToDouble());
                            break;

                        case "System.DBNull":
                            newCell.SetCellValue(string.Empty);
                            break;

                        default:
                            newCell.SetCellValue(string.Empty);
                            break;
                    }
                }
                #endregion
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        /// <summary>
        /// 下载
        /// </summary>
        /// <param name="sFileName"></param>
        /// <param name="sHeaderText"></param>
        /// <param name="listExeclModel"></param>
        /// <returns></returns>
        public string ExportToComboxExcel(string rootPath,string sFileName, string sHeaderText, List<ExeclDownLoadModel> listExeclModel)
        {
            sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), sFileName);
            string sRoot = rootPath;
            string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
            string sDirectory = Path.Combine(sRoot, partDirectory);
            string sFilePath = Path.Combine(sDirectory, sFileName);
            if (!Directory.Exists(sDirectory))
            {
                Directory.CreateDirectory(sDirectory);
            }
            using (MemoryStream ms = CreateComboxExecl(sHeaderText, listExeclModel))
            {
                using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                {
                    byte[] data = ms.ToArray();
                    fs.Write(data, 0, data.Length);
                    fs.Flush();
                }
            }
            return partDirectory + Path.DirectorySeparatorChar + sFileName;
        }

        /// <summary>
        /// 下载带下拉框数据
        /// </summary>
        /// <param name="sHeaderText"></param>
        /// <param name="listExeclModel"></param>
        /// <returns></returns>
        private MemoryStream CreateComboxExecl(string sHeaderText, List<ExeclDownLoadModel> listExeclModel)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            Type type = typeof(T);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            //单元格填充循环外设定单元格格式，避免4000行异常

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);


            IRow headerRow = null;
            int columnIndex = 0;

            #region 设置表头
            headerRow = sheet.CreateRow(0);
            headerRow.HeightInPoints = 35;
            headerRow.CreateCell(0).SetCellValue(sHeaderText);
            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            IFont font = workbook.CreateFont();
            font.FontHeightInPoints = 20;
            font.IsBold = true;
            font.FontName = "宋体";
            headStyle.SetFont(font);
            headerRow.GetCell(0).CellStyle = headStyle;
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, listExeclModel.Count - 1));
            #endregion


            #region 设置列头
            headerRow = sheet.CreateRow(1);
            headerRow.HeightInPoints = 25;
            ICellStyle headStyle1 = workbook.CreateCellStyle();
            headStyle1.Alignment = HorizontalAlignment.Center;
            headStyle1.VerticalAlignment = VerticalAlignment.Center;
            headStyle1.BorderTop = BorderStyle.Thin;
            headStyle1.BorderLeft = BorderStyle.Thin;
            headStyle1.BorderRight = BorderStyle.Thin;
            headStyle1.BorderBottom = BorderStyle.Thin;
            IFont font1 = workbook.CreateFont();
            font1.FontHeightInPoints = 10;
            font1.IsBold = true;
            font1.FontName = "宋体";
            headStyle1.SetFont(font1);
            foreach (ExeclDownLoadModel m in listExeclModel)
            {
                NPOI.SS.UserModel.ICell newCell = headerRow.CreateCell(columnIndex);
                headerRow.CreateCell(columnIndex).SetCellValue(m.ColumnName);
                headerRow.GetCell(columnIndex).CellStyle = headStyle1;
                //根据表头设置列宽  
                sheet.SetColumnWidth(columnIndex, m.ColumnWidth);
                columnIndex++;
            }
            #endregion


            for (int rowIndex = 0; rowIndex <= 1000; rowIndex++)
            {
                #region 填充内容
                IRow dataRow = sheet.CreateRow(rowIndex + 2); // 前面2行已被占用
                dataRow.HeightInPoints = 20;
                columnIndex = 0;
                foreach (ExeclDownLoadModel model in listExeclModel)
                {
                    NPOI.SS.UserModel.ICell newCell = dataRow.CreateCell(columnIndex);
                    newCell.CellStyle = contentLeftStyle;
                    //根据自定义属性设置排列方向
                    newCell.CellStyle = contentCenterStyle;
                    if (!model.IsDropdown)
                    {
                        newCell.SetCellValue("");
                    }
                    else
                    {
                        SetCellDropdownList(sheet, columnIndex, columnIndex, model.DropdownData);
                    }
                    columnIndex++;
                }
                #endregion
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        /// <summary>
        /// 设置下拉框
        /// </summary>
        /// <param name="sheet"></param>
        /// <param name="firstcol"></param>
        /// <param name="lastcol"></param>
        /// <param name="vals"></param>
        private void SetCellDropdownList(ISheet sheet, int firstcol, int lastcol, string[] vals)
        {
            //设置生成下拉框的行和列
            var cellRegions = new CellRangeAddressList(1, 65535, firstcol, lastcol);

            //设置 下拉框内容
            DVConstraint constraint = DVConstraint.CreateExplicitListConstraint(vals);

            //绑定下拉框和作用区域，并设置错误提示信息
            HSSFDataValidation dataValidate = new HSSFDataValidation(cellRegions, constraint);
            dataValidate.CreateErrorBox("输入不合法", "请输入或选择下拉列表中的值。");
            dataValidate.ShowPromptBox = true;

            sheet.AddValidationData(dataValidate);
        }
        #endregion

        #region Excel导入
        /// <summary>
        /// Excel导入
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="columnNameIndex">列名，索引，默认：1 为列的名称，表格会根据列名查到字段赋值</param>
        /// <returns></returns>
        public List<T> ImportFromExcel(string rootPath, string filePath, int columnNameIndex = 1)
        {
            //errorMsg
            string absoluteFilePath = rootPath + filePath.Replace(Path.AltDirectorySeparatorChar, Path.DirectorySeparatorChar);
            List<T> list = new List<T>();
            HSSFWorkbook hssfWorkbook = null;
            XSSFWorkbook xssWorkbook = null;
            ISheet sheet = null;
            using (FileStream file = new FileStream(absoluteFilePath, FileMode.Open, FileAccess.Read))
            {
                switch (Path.GetExtension(filePath))
                {
                    case ".xls":
                        hssfWorkbook = new HSSFWorkbook(file);
                        sheet = hssfWorkbook.GetSheetAt(0);
                        break;

                    case ".xlsx":
                        xssWorkbook = new XSSFWorkbook(file);
                        sheet = xssWorkbook.GetSheetAt(0);
                        break;

                    default:
                        throw new Exception("不支持的文件格式");
                }
            }
            IRow columnRow = sheet.GetRow(columnNameIndex); // 第二行为字段名

            if (columnRow == null) return list;

            try
            {
                Dictionary<int, PropertyInfo> mapPropertyInfoDict = new Dictionary<int, PropertyInfo>();
                for (int j = 0; j < columnRow.LastCellNum; j++)
                {
                    NPOI.SS.UserModel.ICell cell = columnRow.GetCell(j);
                    PropertyInfo propertyInfo = MapPropertyInfo(cell.ObjToString());
                    if (propertyInfo != null)
                    {
                        mapPropertyInfoDict.Add(j, propertyInfo);
                    }
                }

                for (int i = (sheet.FirstRowNum + 1 + columnNameIndex); i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null)
                    {
                        break;
                    }
                    T entity = new T();
                    bool isEmptyRow = true; //是否空行
                    for (int j = row.FirstCellNum; j < columnRow.LastCellNum; j++)
                    {
                        if (!row.GetCell(j).ObjToString().IsEmpty()) isEmptyRow = false;

                        if (mapPropertyInfoDict.ContainsKey(j))
                        {
                            if (row.GetCell(j) != null)
                            {
                                PropertyInfo propertyInfo = mapPropertyInfoDict[j];
                                switch (propertyInfo.PropertyType.ToString())
                                {
                                    case "System.DateTime":
                                    case "System.Nullable`1[System.DateTime]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDateTime());
                                        break;

                                    case "System.Boolean":
                                    case "System.Nullable`1[System.Boolean]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToBool());
                                        break;

                                    case "System.Byte":
                                    case "System.Nullable`1[System.Byte]":
                                        mapPropertyInfoDict[j].SetValue(entity, Byte.Parse(row.GetCell(j).ObjToString()));
                                        break;
                                    case "System.Int16":
                                    case "System.Nullable`1[System.Int16]":
                                        mapPropertyInfoDict[j].SetValue(entity, Int16.Parse(row.GetCell(j).ObjToString()));
                                        break;
                                    case "System.Int32":
                                    case "System.Nullable`1[System.Int32]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToInt());
                                        break;

                                    case "System.Int64":
                                    case "System.Nullable`1[System.Int64]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToLong());
                                        break;

                                    case "System.Double":
                                    case "System.Nullable`1[System.Double]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDouble());
                                        break;

                                    case "System.Single":
                                    case "System.Nullable`1[System.Single]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDouble());
                                        break;

                                    case "System.Decimal":
                                    case "System.Nullable`1[System.Decimal]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDecimal());
                                        break;

                                    default:
                                    case "System.String":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString());
                                        break;
                                }
                            }
                        }
                    }

                    if (!isEmptyRow)
                        list.Add(entity);
                }
                hssfWorkbook?.Close();
                xssWorkbook?.Close();
            }
            catch
            {

            }
            return list;
        }

        /// <summary>
        /// 查找Excel列名对应的实体属性
        /// </summary>
        /// <param name="columnName"></param>
        /// <returns></returns>
        private PropertyInfo MapPropertyInfo(string columnName)
        {
            PropertyInfo[] propertyList = ReflectionHelper.GetProperties(typeof(T));
            PropertyInfo propertyInfo = propertyList.Where(p => p.Name == columnName).FirstOrDefault();
            if (propertyInfo != null)
            {
                return propertyInfo;
            }
            else
            {
                foreach (PropertyInfo tempPropertyInfo in propertyList)
                {
                    DescriptionAttribute[] attributes = (DescriptionAttribute[])tempPropertyInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
                    if (attributes.Length > 0)
                    {
                        if (attributes[0].Description == columnName)
                        {
                            return tempPropertyInfo;
                        }
                    }
                }
            }
            return null;
        }
        #endregion


        #region 审批配置项目清单Execl处理
        public List<T> ImportProjectListFromExcel(string rootPath, string filePath, int columnNameIndex = 1, Dictionary<string, string> listField=null)
        {
            //errorMsg
            string absoluteFilePath = rootPath + filePath.Replace(Path.AltDirectorySeparatorChar, Path.DirectorySeparatorChar);
            List<T> list = new List<T>();
            HSSFWorkbook hssfWorkbook = null;
            XSSFWorkbook xssWorkbook = null;
            ISheet sheet = null;
            using (FileStream file = new FileStream(absoluteFilePath, FileMode.Open, FileAccess.Read))
            {
                switch (Path.GetExtension(filePath))
                {
                    case ".xls":
                        hssfWorkbook = new HSSFWorkbook(file);
                        sheet = hssfWorkbook.GetSheetAt(0);
                        break;

                    case ".xlsx":
                        xssWorkbook = new XSSFWorkbook(file);
                        sheet = xssWorkbook.GetSheetAt(0);
                        break;

                    default:
                        throw new Exception("不支持的文件格式");
                }
            }
            IRow columnRow = sheet.GetRow(columnNameIndex); // 第二行为字段名

            if (columnRow == null) return list;

            try
            {
                string fieldName = string.Empty;
                Dictionary<int, PropertyInfo> mapPropertyInfoDict = new Dictionary<int, PropertyInfo>();
                for (int j = 0; j < columnRow.LastCellNum; j++)
                {
                    NPOI.SS.UserModel.ICell cell = columnRow.GetCell(j);
                    fieldName = cell.ObjToString().Replace("*", "");
                    var objDic = listField.Where(f => f.Key == fieldName).FirstOrDefault();
                    PropertyInfo propertyInfo = typeof(T).GetProperty(objDic.Value);
                    if (propertyInfo != null)
                    {
                        mapPropertyInfoDict.Add(j, propertyInfo);
                    }
                }

                for (int i = (sheet.FirstRowNum + 1 + columnNameIndex); i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null)
                    {
                        break;
                    }
                    T entity = new T();
                    bool isEmptyRow = true; //是否空行
                    for (int j = row.FirstCellNum; j < columnRow.LastCellNum; j++)
                    {
                        if (!row.GetCell(j).ObjToString().IsEmpty()) isEmptyRow = false;

                        if (mapPropertyInfoDict.ContainsKey(j))
                        {
                            if (row.GetCell(j) != null)
                            {
                                PropertyInfo propertyInfo = mapPropertyInfoDict[j];
                                switch (propertyInfo.PropertyType.ToString())
                                {
                                    case "System.DateTime":
                                    case "System.Nullable`1[System.DateTime]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDateTime());
                                        break;

                                    case "System.Boolean":
                                    case "System.Nullable`1[System.Boolean]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToBool());
                                        break;

                                    case "System.Byte":
                                    case "System.Nullable`1[System.Byte]":
                                        mapPropertyInfoDict[j].SetValue(entity, Byte.Parse(row.GetCell(j).ObjToString()));
                                        break;
                                    case "System.Int16":
                                    case "System.Nullable`1[System.Int16]":
                                        mapPropertyInfoDict[j].SetValue(entity, Int16.Parse(row.GetCell(j).ObjToString()));
                                        break;
                                    case "System.Int32":
                                    case "System.Nullable`1[System.Int32]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToInt());
                                        break;

                                    case "System.Int64":
                                    case "System.Nullable`1[System.Int64]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToLong());
                                        break;

                                    case "System.Double":
                                    case "System.Nullable`1[System.Double]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDouble());
                                        break;

                                    case "System.Single":
                                    case "System.Nullable`1[System.Single]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDouble());
                                        break;

                                    case "System.Decimal":
                                    case "System.Nullable`1[System.Decimal]":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString().ObjToDecimal());
                                        break;

                                    default:
                                    case "System.String":
                                        mapPropertyInfoDict[j].SetValue(entity, row.GetCell(j).ObjToString());
                                        break;
                                }
                            }
                        }
                    }

                    if (!isEmptyRow)
                        list.Add(entity);
                }
                hssfWorkbook?.Close();
                xssWorkbook?.Close();
            }
            catch
            {

            }
            return list;
        }

        #endregion




        #region 文件流形式导出

        /// <summary>
        /// 导出Execl
        /// </summary>
        /// <param name="list">list集合</param>
        /// <param name="sHeaderText">标题</param>
        /// <param name="columns">列</param>
        /// <returns></returns>
        public async Task<byte[]> ExportExecl(List<T> list, string sHeaderText, string[] columns)
        {
            return await Task.Run(() =>
            {
                HSSFWorkbook workbook = new HSSFWorkbook();
                ISheet sheet = workbook.CreateSheet();

                Type type = typeof(T);
                PropertyInfo[] properties = ReflectionHelper.GetProperties(type, columns);

                IFont contentFont = workbook.CreateFont();
                contentFont.FontName = "宋体";
                contentFont.FontHeightInPoints = 10;
                //单元格填充循环外设定单元格格式，避免4000行异常

                ICellStyle contentLeftStyle = workbook.CreateCellStyle();
                contentLeftStyle.Alignment = HorizontalAlignment.Left;
                contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
                contentLeftStyle.BorderTop = BorderStyle.Thin;
                contentLeftStyle.BorderLeft = BorderStyle.Thin;
                contentLeftStyle.BorderRight = BorderStyle.Thin;
                contentLeftStyle.BorderBottom = BorderStyle.Thin;
                contentLeftStyle.SetFont(contentFont);

                ICellStyle contentRightStyle = workbook.CreateCellStyle();
                contentRightStyle.Alignment = HorizontalAlignment.Right;
                contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
                contentRightStyle.BorderTop = BorderStyle.Thin;
                contentRightStyle.BorderLeft = BorderStyle.Thin;
                contentRightStyle.BorderRight = BorderStyle.Thin;
                contentRightStyle.BorderBottom = BorderStyle.Thin;
                contentRightStyle.SetFont(contentFont);

                ICellStyle contentCenterStyle = workbook.CreateCellStyle();
                contentCenterStyle.Alignment = HorizontalAlignment.Center;
                contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
                contentCenterStyle.BorderTop = BorderStyle.Thin;
                contentCenterStyle.BorderLeft = BorderStyle.Thin;
                contentCenterStyle.BorderRight = BorderStyle.Thin;
                contentCenterStyle.BorderBottom = BorderStyle.Thin;
                contentCenterStyle.SetFont(contentFont);

                #region 取得每列的列宽（最大宽度）
                int[] arrColWidth = new int[properties.Length];
                for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
                {
                    //GBK对应的code page是CP936
                    arrColWidth[columnIndex] = properties[columnIndex].Name.Length;
                }
                #endregion
                for (int rowIndex = 0; rowIndex < list.Count; rowIndex++)
                {
                    #region 新建表，填充表头，填充列头，样式
                    if (rowIndex == 65535 || rowIndex == 0)
                    {
                        if (rowIndex != 0)
                        {
                            sheet = workbook.CreateSheet();
                        }

                        #region 表头及样式
                        {
                            IRow headerRow = sheet.CreateRow(0);
                            headerRow.HeightInPoints = 35;
                            headerRow.CreateCell(0).SetCellValue(sHeaderText);

                            ICellStyle headStyle = workbook.CreateCellStyle();
                            headStyle.Alignment = HorizontalAlignment.Center;
                            headStyle.VerticalAlignment = VerticalAlignment.Center;
                            IFont font = workbook.CreateFont();
                            font.FontHeightInPoints = 20;
                            font.IsBold = true;
                            font.FontName = "宋体";
                            headStyle.SetFont(font);

                            headerRow.GetCell(0).CellStyle = headStyle;

                            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, properties.Length - 1));
                        }
                        #endregion

                        #region 列头及样式
                        {
                            IRow headerRow = sheet.CreateRow(1);
                            headerRow.HeightInPoints = 25;
                            ICellStyle headStyle = workbook.CreateCellStyle();
                            headStyle.Alignment = HorizontalAlignment.Center;
                            headStyle.VerticalAlignment = VerticalAlignment.Center;
                            headStyle.BorderTop = BorderStyle.Thin;
                            headStyle.BorderLeft = BorderStyle.Thin;
                            headStyle.BorderRight = BorderStyle.Thin;
                            headStyle.BorderBottom = BorderStyle.Thin;
                            IFont font = workbook.CreateFont();
                            font.FontHeightInPoints = 10;
                            font.IsBold = true;
                            font.FontName = "宋体";
                            headStyle.SetFont(font);

                            for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
                            {
                                // 类属性如果有Description就用Description当做列名
                                DescriptionAttribute customAttribute = (DescriptionAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(DescriptionAttribute));
                                string description = properties[columnIndex].Name;
                                if (customAttribute != null)
                                {
                                    description = customAttribute.Description;
                                }
                                headerRow.CreateCell(columnIndex).SetCellValue(description);
                                headerRow.GetCell(columnIndex).CellStyle = headStyle;
                                //根据表头设置列宽  
                                sheet.SetColumnWidth(columnIndex, (arrColWidth[columnIndex] + 1) * 256);
                                //根据自定义属性设置列宽
                                ExportExcelAttribute excelAttribute = (ExportExcelAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(ExportExcelAttribute));
                                if (excelAttribute != null)
                                {
                                    if (excelAttribute.Width > 0)
                                    {
                                        sheet.SetColumnWidth(columnIndex, excelAttribute.Width * 256);
                                    }
                                }
                            }
                        }
                        #endregion
                    }
                    #endregion

                    #region 填充内容
                    IRow dataRow = sheet.CreateRow(rowIndex + 2); // 前面2行已被占用
                    dataRow.HeightInPoints = 20;
                    for (int columnIndex = 0; columnIndex < properties.Length; columnIndex++)
                    {
                        string drValue = properties[columnIndex].GetValue(list[rowIndex], null).ObjToString();

                        ICell newCell = dataRow.CreateCell(columnIndex);
                        newCell.CellStyle = contentLeftStyle;

                        //根据单元格内容设定列宽
                        int cellValueLength = System.Text.Encoding.UTF8.GetBytes(drValue).Length + 1;
                        int length = (cellValueLength > 255 ? 255 : cellValueLength) * 256;
                        if (sheet.GetColumnWidth(columnIndex) < length && !drValue.IsEmpty())
                        {
                            sheet.SetColumnWidth(columnIndex, length);
                        }

                        //根据自定义属性设置排列方向
                        ExportExcelAttribute excelAttribute = (ExportExcelAttribute)Attribute.GetCustomAttribute(properties[columnIndex], typeof(ExportExcelAttribute));
                        if (excelAttribute != null)
                        {
                            switch (excelAttribute.Alignment)
                            {
                                case HorizontalAlignment.Left:
                                    newCell.CellStyle = contentLeftStyle;
                                    break;
                                case HorizontalAlignment.Center:
                                    newCell.CellStyle = contentCenterStyle;
                                    break;
                                case HorizontalAlignment.Right:
                                    newCell.CellStyle = contentRightStyle;
                                    break;
                                default:
                                    newCell.CellStyle = contentLeftStyle;
                                    break;
                            }
                            if (excelAttribute.Width > 0)
                            {
                                sheet.SetColumnWidth(columnIndex, excelAttribute.Width * 256);
                            }
                        }

                        if (drValue.IsEmpty())
                        {
                            newCell.SetCellValue("");
                            continue;
                        }
                        switch (properties[columnIndex].PropertyType.ToString())
                        {
                            case "System.String":
                                newCell.CellStyle.WrapText = true;
                                newCell.SetCellValue(drValue);
                                break;

                            case "System.DateTime":
                            case "System.Nullable`1[System.DateTime]":
                                newCell.SetCellValue(drValue.ObjToDateTime().ToString("yyyy-MM-dd"));
                                break;

                            case "System.Boolean":
                            case "System.Nullable`1[System.Boolean]":
                                newCell.SetCellValue(drValue.ObjToBool());
                                break;

                            case "System.Byte":
                            case "System.Nullable`1[System.Byte]":
                            case "System.Int16":
                            case "System.Nullable`1[System.Int16]":
                            case "System.Int32":
                            case "System.Nullable`1[System.Int32]":
                                newCell.SetCellValue(drValue.ObjToInt());
                                break;

                            case "System.Int64":
                            case "System.Nullable`1[System.Int64]":
                                newCell.SetCellValue(drValue.ObjToString());
                                break;

                            case "System.Double":
                            case "System.Nullable`1[System.Double]":
                                newCell.SetCellValue(drValue.ObjToDouble());
                                break;

                            case "System.Single":
                            case "System.Nullable`1[System.Single]":
                                newCell.SetCellValue(drValue.ObjToDouble());
                                break;

                            case "System.Decimal":
                            case "System.Nullable`1[System.Decimal]":
                                newCell.SetCellValue(drValue.ObjToDouble());
                                break;

                            case "System.DBNull":
                                newCell.SetCellValue(string.Empty);
                                break;

                            default:
                                newCell.SetCellValue(string.Empty);
                                break;
                        }
                    }
                    #endregion
                }

                // 写入内存流
                using var stream = new MemoryStream();
                workbook.Write(stream, true);
                return stream.ToArray();
            });
        }

        #endregion
    }
}
