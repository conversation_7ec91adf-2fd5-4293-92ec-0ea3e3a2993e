﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批节点人员设置
    ///</summary>
    [SugarTable("wf_ProjectAuditUser", "审批节点人员设置")]
    public class WfProjectAuditUser : BaseEntity
    {

        public WfProjectAuditUser()
        {

        }

        /// <summary>
        ///业务类型（1：节点  2：项目库  3：数据报表）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? BusinessType { get; set; }

        /// <summary>
        ///流程配置表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessId { get; set; }

        /// <summary>
        ///流程节点表Id，项目库节点Id 20000、数据报表节点Id 30000
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessNodeId { get; set; }

        /// <summary>
        ///审批人单位Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? AuditUnitId { get; set; }

        /// <summary>
        ///审批人Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? AuditUserId { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 模块Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ModuleId { get; set; }

        /// <summary>
        /// 分组字典值Id，对应审批字典表Id值，默认0
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long GroupValue { get; set; }

        /// <summary>
        /// 输入框类型，2：字典表(B_Dictionary)下拉框组件，3：审批字典表(Wf_Dictionary)下拉框组件
        /// </summary>
        [SugarColumn(DefaultValue = "3")]
        public long TypeBox { get; set; }
    }


}

