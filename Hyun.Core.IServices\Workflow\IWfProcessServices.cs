﻿using Microsoft.AspNetCore.Mvc;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfProcess接口方法
    ///</summary>
    public interface IWfProcessServices : IBaseServices<WfProcess>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<WfProcess> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<WfProcess>> Find(Expression<Func<WfProcess, bool>> expression);

        Task<Result<string>> SourceFundInputDataSet(SourceFundInputModel o);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">WfProcessParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<WfProcess>> GetPaged(WfProcessParam param);

        //<used>1</used>
        Task<Result<WfProcessDto>> InsertUpdate(WfProcessDto o);

        //<used>1</used>
        Task<Result<string>> DeleteById(long id);

        Task<Result<string>> ProcessSetSave(WfProcessSetConfigModel o);

        Task<Result<string>> DeleteProcessNodeLinkById(long id);

        Task<Result<string>> DeleteProcessNodeById(long id);

        /// <summary>
        /// 首页数据获取
        /// </summary>
        /// <returns></returns>
        Task<Result> GetHomePageData();
    }
}

