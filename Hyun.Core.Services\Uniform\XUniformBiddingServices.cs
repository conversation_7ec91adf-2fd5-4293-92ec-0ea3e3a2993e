﻿using AutoMapper;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.IServices.Uniform;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.Validator;
using NetTaste;
using System.Reflection;
namespace Hyun.Core.Services
{

    ///<summary>
    ///XUniformBidding方法
    ///</summary>
    public class XUniformBiddingServices : BaseServices<XUniformBidding>, IXUniformBiddingServices
    {

        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformPurchaseServices uniformPurchaseManager;
        public XUniformBiddingServices(IMapper _mapper, IUser _user, IXUniformPurchaseServices _uniformPurchaseManager)
        {
            mapper = _mapper;
            user = _user;
            uniformPurchaseManager = _uniformPurchaseManager;
        }
        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="o">XUniformBidding对象</param>
        /// <returns></returns>
        public async Task<Result<string>> InsertUpdate(XUniformBiddingDto m)
        {
            XUniformBidding o = mapper.Map<XUniformBidding>(m);

            #region 增加FluentValidation验证
            var validator = new XUniformBiddingValidator();
            var result = validator.Validate(o);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                return Result<string>.Fail(tipMsg);
            }
            #endregion

            string msg = "保存成功";

            XUniformBuy objBuy = await this.Db.Queryable<XUniformBuy>().Where(f => f.Id == o.UniformBuyId).FirstAsync();
            if (objBuy != null)
            {
                var obj = await this.Db.Queryable<XUniformBidding>().Where(f => f.UniformBuyId == o.UniformBuyId).FirstAsync();
                if (obj != null)
                {
                    o.SchoolId = obj.SchoolId;
                    o.CountyId = obj.CountyId;
                    o.BiddingStatuz = obj.BiddingStatuz;

                    await base.Update(o);
                }
                else
                {
                    o.Id = BaseDBConfig.GetYitterId();
                    o.SchoolId = user.UnitId;
                    o.CountyId = user.UnitPId;
                    o.IsFiling = 0;
                    o.UniformBuyId = o.UniformBuyId;
                    o.BiddingStatuz = UniformFilingEnum.SubmitNone.ToEnumInt();
                    await base.Add(o);
                }
            }

            //处理附件
            if (m.ListAttachmentId.Count > 0)
            {
                List<BAttachmentData> listAttData = await this.Db.Queryable<BAttachmentData>().Where(f => f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.PurchaseBid.ObjToInt() && f.UnitId == user.UnitId && m.ListAttachmentId.Contains(f.Id)).ToListAsync();
                List<BAttachment> listAddFile = new List<BAttachment>();
                foreach (BAttachmentData b in listAttData)
                {
                    listAddFile.Add(new BAttachment()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        MainId = 0,
                        ObjectId = o.Id,
                        ModuleType = ModuleTypeEnum.PurchaseBid.ObjToInt(),
                        Title = b.Title,
                        Path = b.Path,
                        Width = b.Width,
                        Height = b.Height,
                        DocType = 0,
                        IsDefault = 0,
                        Remark = "",
                        UserId = user.ID,
                        UnitId = user.UnitId,
                        FileCategory = b.FileCategory,
                        IsDelete = 0,
                        Ext = b.Ext,
                        AttachmentDataId = b.Id
                    });
                }

                if (listAddFile.Count > 0)
                {
                    await this.Db.Insertable<BAttachment>(listAddFile).ExecuteCommandAsync();
                }
            }

            //如果为提交
            if (m.ButtonType == 1)
            {
                o.BiddingStatuz = UniformFilingEnum.Filinged.ObjToInt();

                //验证附件必填
                string errorMsg = string.Empty;
                if (o.ValidityPeriod == 0)
                {
                    errorMsg += "采购有效年限不能为空\n";
                }
                if (o.Method == 0)
                {
                    errorMsg += "采购方式不能为空\n";
                }
                if (o.BidDate == null)
                {
                    errorMsg += "开标日期不能为空\n";
                }
                if (o.BidResultPublic == 0)
                {
                    errorMsg += "开标结果公开不能为空\n";
                }

                if (o.BidResultPublic == 1)
                {
                    if (o.PublicDate == null)
                    {
                        errorMsg += "公开日期不能为空\n";
                    }
                    if (string.IsNullOrEmpty(o.PublicMediaName))
                    {
                        errorMsg += "公开媒体名称不能为空\n";
                    }
                }

                List<BAttachment> listAttach = await this.Db.Queryable<BAttachment>().Where(f => f.IsDeleted == false && f.ObjectId == o.Id && f.ModuleType == ModuleTypeEnum.PurchaseBid.ToEnumInt() && f.UnitId == user.UnitId).ToListAsync();
                List<BAttachmentConfig> listConfig = await this.Db.Queryable<BAttachmentConfig>().Where(f => f.IsDeleted == false && f.IsFilled == 1 && f.ModuleType == ModuleTypeEnum.PurchaseBid.ToEnumInt()).ToListAsync();
                foreach (BAttachmentConfig config in listConfig)
                {
                    if (!listAttach.Exists(f => f.FileCategory == config.FileCategory))
                    {
                        errorMsg += $"“{config.Name}”未上传\n";
                    }
                }

                if (!string.IsNullOrEmpty(errorMsg))
                {
                    return Result<string>.Fail(errorMsg);
                }
                //

                //判断是否需要审核
                var entityConfigList = await this.Db.Queryable<XUniformConfig>().Where((config) => (config.UnitId == user.UnitPId || config.UnitId == 0) && config.Code == "2000" && config.DicValue == "209").ToListAsync();
                if (entityConfigList != null && entityConfigList.Count() > 0)
                {
                    var entityTempconfig = entityConfigList.Where(m => m.UnitId == user.UnitPId).FirstOrDefault();
                    if (entityTempconfig != null)
                    {
                        if (entityTempconfig.ValueNum == 1)
                        {
                            o.BiddingStatuz = UniformFilingEnum.Wait.ObjToInt();
                        }
                    }
                    else if (entityConfigList.FirstOrDefault().ValueNum == 1)
                    {
                        o.BiddingStatuz = UniformFilingEnum.Wait.ObjToInt();
                    }
                }

                await this.Db.Updateable<XUniformBidding>(o).ExecuteCommandAsync();
                msg = "提交成功";
            }

            return Result<string>.Success(msg);
        }

        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> Submit(long id)
        {
            var obj = await this.Db.Queryable<XUniformBidding>().Where(f => f.Id == id && f.SchoolId == user.UnitId).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.BiddingStatuz != UniformFilingEnum.SubmitNone.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可提交");
            }

            //验证附件必填
            string errorMsg = string.Empty;
            if (obj.ValidityPeriod == 0)
            {
                errorMsg += "采购有效年限不能为空\n";
            }
            if (obj.Method == 0)
            {
                errorMsg += "采购方式不能为空\n";
            }
            if (obj.BidDate == null)
            {
                errorMsg += "开标日期不能为空\n";
            }
            if (obj.BidResultPublic == 0)
            {
                errorMsg += "开标结果公开不能为空\n";
            }

            if(obj.BidResultPublic == 1)
            {
                if(obj.PublicDate == null)
                {
                    errorMsg += "公开日期不能为空\n";
                }
                if (string.IsNullOrEmpty(obj.PublicMediaName))
                {
                    errorMsg += "公开媒体名称不能为空\n";
                }
            }
  
            List<BAttachment> listAttach = await this.Db.Queryable<BAttachment>().Where(f => f.IsDeleted == false && f.ObjectId == id && f.ModuleType == ModuleTypeEnum.PurchaseBid.ToEnumInt() && f.UnitId == user.UnitId).ToListAsync();
            List<BAttachmentConfig> listConfig = await this.Db.Queryable<BAttachmentConfig>().Where(f => f.IsDeleted == false && f.IsFilled == 1 && f.ModuleType == ModuleTypeEnum.PurchaseBid.ToEnumInt()).ToListAsync();
            foreach (BAttachmentConfig config in listConfig)
            {
                if (!listAttach.Exists(f => f.FileCategory == config.FileCategory))
                {
                    errorMsg += $"“{config.Name}”未上传\n";
                }
            }

            if (!string.IsNullOrEmpty(errorMsg))
            {
                return Result<string>.Fail(errorMsg);
            }
            //
            obj.BiddingStatuz = UniformFilingEnum.Filinged.ObjToInt();
            //判断是否需要审核
            var entityConfigList = await this.Db.Queryable<XUniformConfig>().Where((config) => (config.UnitId == user.UnitPId || config.UnitId == 0) && config.Code == "2000" && config.DicValue == "202").ToListAsync();
            if (entityConfigList != null && entityConfigList.Count() > 0)
            {
                var entityTempconfig = entityConfigList.Where(m => m.UnitId == user.UnitPId).FirstOrDefault();
                if (entityTempconfig != null)
                {
                    if (entityTempconfig.ValueNum == 1)
                    {
                        obj.BiddingStatuz = UniformFilingEnum.Wait.ObjToInt();
                    }
                }
                else if (entityConfigList.FirstOrDefault().ValueNum == 1)
                {
                    obj.BiddingStatuz = UniformFilingEnum.Wait.ObjToInt();
                }
            }

            await this.Db.Updateable<XUniformBidding>(obj).ExecuteCommandAsync();

            return Result<string>.Success("提交成功");
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> DeleteById(long id)
        {
            var obj = await this.Db.Queryable<XUniformBidding>().Where(f => f.Id == id && f.SchoolId == user.UnitId).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.BiddingStatuz != UniformFilingEnum.SubmitNone.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可删除");
            }

            //判断是否被关联

            //

            await this.Db.Updateable<XUniformBidding>().SetColumns(f => new XUniformBidding() { IsDeleted = true }).Where(f => f.Id == id).ExecuteCommandAsync();

            return Result<string>.Success("删除成功");
        }

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> Audit(BuyAuditModel o)
        {
            var obj = await this.Db.Queryable<XUniformBidding>().Where(f => f.Id == o.Id).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.BiddingStatuz != UniformFilingEnum.Wait.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可审核");
            }
            var objUnit = await this.Db.Queryable<PUnit>().Where(f => f.Id == obj.SchoolId).FirstAsync();
            if (objUnit == null)
            {
                return Result<string>.Fail("当前数据单位信息不存在");
            }
            if (objUnit.PId != user.UnitId)
            {
                return Result<string>.Fail("非本单位的下属单位禁止操作");
            }
            if (o.Statuz == 2 && string.IsNullOrEmpty(o.FilingExplanation))
            {
                return Result<string>.Fail("审核不通过，必须填写原因");
            }

            if (o.Statuz == 1)
            {
                obj.IsFiling = 1;
                obj.BiddingStatuz = UniformFilingEnum.Filinged.ToEnumInt();
            }
            else
            {
                obj.BiddingStatuz = UniformFilingEnum.SubmitNone.ToEnumInt();
                obj.FilingExplanation = o.FilingExplanation;
            }
            await this.Db.Updateable<XUniformBidding>(obj).ExecuteCommandAsync();

            //插入记录表 UniformAuditLog
            var entityLog = new XUniformAuditLog();
            entityLog.Id = BaseDBConfig.GetYitterId();
            entityLog.UniformId = o.Id;
            entityLog.AuditTime = DateTime.Now;
            entityLog.AduitStatuz = obj.BiddingStatuz;
            entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Bidding.ObjToInt().ToString();
            entityLog.AuditExplain = $"审核：{o.FilingExplanation},结果：{o.Statuz}";
            entityLog.IsCurrent = 1;
            await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();

            return Result<string>.Success("审核成功");
        }

        /// <summary>
        /// 撤销、撤回
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> Revoke(BuyRevokeModel o)
        {
            var obj = await this.Db.Queryable<XUniformBidding>().Where(f => f.Id == o.Id).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.BiddingStatuz != UniformFilingEnum.Filinged.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可撤销");
            }
            var objUnit = await this.Db.Queryable<PUnit>().Where(f => f.Id == obj.SchoolId).FirstAsync();
            if (objUnit == null)
            {
                return Result<string>.Fail("当前数据单位信息不存在");
            }
            if (objUnit.PId != user.UnitId)
            {
                return Result<string>.Fail("非本单位的下属单位禁止操作");
            }
            string explain = string.Empty;
            if (o.OptType == 1)
            {
                obj.BiddingStatuz = UniformFilingEnum.Wait.ObjToInt();
                explain = $"撤销：{o.FilingExplanation}";
            }
            else
            {
                obj.BiddingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
            }

            obj.IsFiling = 0;
            obj.FilingExplanation = "";
            await this.Db.Updateable<XUniformBidding>(obj).ExecuteCommandAsync();

            //插入记录表 UniformAuditLog
            var entityLog = new XUniformAuditLog();
            entityLog.Id = BaseDBConfig.GetYitterId();
            entityLog.UniformId = o.Id;
            entityLog.AuditTime = DateTime.Now;
            entityLog.AduitStatuz = obj.BiddingStatuz;
            entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Bidding.ObjToInt().ToString();
            entityLog.AuditExplain = explain;
            entityLog.IsCurrent = 1;
            await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();

            return Result<string>.Success("操作成功");
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">XUniformBiddingParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<XUniformBidding>> GetPaged(XUniformBiddingParam param)
        {
            PageModel<XUniformBidding> pageList = new PageModel<XUniformBidding>();
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> totalCount = 0;

            var list = await this.Db.Queryable<XUniformBuy>()
               .InnerJoin<PUnit>((UB, US) => UB.SchoolId == US.Id && US.IsDeleted == false)
               .InnerJoin<PUnit>((UB, US, UC) => US.PId == UC.Id)
               .LeftJoin<BArea>((UB, US, UC, A) => UC.AreaId == A.Id)
               .LeftJoin<XUniformBidding>((UB, US, UC, A, B) => B.UniformBuyId == UB.Id && B.IsDeleted == false)
               .Where((UB, US, UC, A, B) => UB.IsDeleted == false && UB.IsNeedBidding == 1 && UB.PurchaseStatuz == 100)
               .WhereIF(user.UnitTypeId == 3, (UB, US, UC, A, B) => US.Id == user.UnitId)
               .WhereIF(user.UnitTypeId == 2, (UB, US, UC, A, B) => US.PId == user.UnitId)
               .WhereIF(user.UnitTypeId == 1, (UB, US, UC, A, B) => UC.PId == user.UnitId)
               .WhereIF(param.SchoolId > 0, (UB, US, UC, A, B) => UB.SchoolId == param.SchoolId)
               .WhereIF(param.CountyId > 0, (UB, US, UC, A, B) => UC.Id == param.CountyId)
               .WhereIF(param.ValidityPeriod > 0 , (UB, US, UC, A, B) => B.ValidityPeriod == param.ValidityPeriod)
               .Select((UB, US, UC, A, B) => new XUniformBidding()
               {
                   UniformBuyId = UB.Id,
                   Id = SqlFunc.IsNull(B.Id,0),
                   IsDeleted = UB.IsDeleted,
                   SchoolId = UB.SchoolId,
                   SchoolName = US.Name,
                   CountyId = UB.CountyId,
                   PlanYear = UB.PlanYear,
                   PurchaseNo = UB.PurchaseNo,
                   ValidityPeriod = SqlFunc.IsNull(B.ValidityPeriod, 0),
                   ValidityPeriodName = SqlFunc.IsNull(B.ValidityPeriodName, ""),
                   Method = SqlFunc.IsNull(B.Method, 0),
                   MethodName = SqlFunc.IsNull(B.MethodName, ""),
                   BiddingStatuz = SqlFunc.IsNull(B.BiddingStatuz, -1),
                   IsFiling = SqlFunc.IsNull(B.IsFiling, 0),
                   AreaName = A.Name,
                   BiddingStatuzName = SqlFunc.IsNull(SqlFunc.IF(B.BiddingStatuz == 10).Return("待审核").ElseIF(B.BiddingStatuz == 100).Return("已备案").End("待备案 "),"")
               })
               .MergeTable()
               .WhereIF(expression != null, expression)
               .OrderByIF(!string.IsNullOrEmpty(orderByFields), orderByFields)
               .ToPageListAsync(param.pageIndex, param.pageSize, totalCount); ;
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }


        public async Task<XUniformBidding> GetByBuyId(long id)
        {
            var obj = await this.Db.Queryable<XUniformBuy>()
             .LeftJoin<XUniformBidding>((UB, B) => B.UniformBuyId == UB.Id && B.IsDeleted == false)
             .Where((UB, B) => UB.Id == id)
             .Select((UB, B) => new XUniformBidding
             {
                 Id = SqlFunc.IsNull(B.Id, 0),
                 PlanYear = UB.PlanYear,
                 PurchaseNo = UB.PurchaseNo,
                 ValidityPeriod = SqlFunc.IsNull(B.ValidityPeriod, 0),
                 Method = SqlFunc.IsNull(B.Method, 0),
                 BidResultPublic = SqlFunc.IsNull(B.BidResultPublic, 0),
                 BidDate = B.BidDate,
                 PublicDate = B.PublicDate,
                 PublicMediaName = SqlFunc.IsNull(B.PublicMediaName, ""),
                 EntrustingAgency = SqlFunc.IsNull(B.EntrustingAgency, ""),
                 Contact = SqlFunc.IsNull(B.Contact, ""),
                 Mobile = SqlFunc.IsNull(B.Mobile, ""),
                 FilingExplanation = SqlFunc.IsNull(B.FilingExplanation, ""),
                 Remark = SqlFunc.IsNull(B.Remark, "")
             }).FirstAsync();
            return obj;
        }

        /// <summary>
        /// 校服招标-删除附件
        /// </summary>
        /// <param name="model">撤销信息</param>
        /// <returns></returns>
        public async Task<Result> UpdateAttachmentDelete(XUniformBiddingDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前校服招标数据已不存在，请刷新重新操作。";
                return r;
            }
            var listAddAttachment = new List<BAttachment>();
            var entityAttachment = await this.Db.Queryable<BAttachment>()
            .Where((att) => att.Id == model.AttachmentId && att.ObjectId == entity.Id && att.ModuleType == ModuleTypeEnum.PurchaseBid.ObjToInt() && att.IsDeleted == false).FirstAsync();

            if (entityAttachment == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前附件已不存在，请刷新重新操作。";
                return r;
            }
            var resultNo = await this.Db.Updateable<BAttachment>().SetColumns((att) => new BAttachment()
            {
                IsDeleted = true,
                ModifyTime = DateTime.Now,
                ModifyBy = model.CreateBy,
                ModifyId = model.CreateId
            })
            .Where((att) => att.Id == entityAttachment.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                r.flag = 1;
                r.msg = "删除成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "删除失败,请刷新重试，如无法解决请联系客服协助处理！";
            }
            return r;
        }


        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">XUniformBiddingParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<XUniformBidding, bool>> ListFilter(XUniformBiddingParam param)
        {
            var expression = LinqExtensions.True<XUniformBidding>();
            //if(user.UnitTypeId == 3)
            //{
            //    expression = expression.AndNew(f => f.SchoolId == user.UnitId);
            //}
            //else if(user.UnitTypeId == 2)
            //{
            //    expression = expression.AndNew(f => f.CountyId == user.UnitId);
            //}
            //else
            //{
            //    expression = expression.AndNew(f => f.SchoolId == 0);
            //}
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
                if (!string.IsNullOrEmpty(param.PurchaseNo))
                {
                    expression = expression.AndNew(f=>f.PurchaseNo.Contains(param.PurchaseNo));
                }
                if(param.PlanYear > 0)
                {
                    expression = expression.AndNew(f => f.PlanYear == param.PlanYear);
                }
                if(param.Method > 0)
                {
                    expression = expression.AndNew(f => f.Method == param.Method);
                }
                if(param.BiddingStatuz > -1)
                {
                    expression = expression.AndNew(f => f.BiddingStatuz == param.BiddingStatuz);
                }

            }
            return expression;
        }
        #endregion
    }
}

