﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Old.Util;
using log4net;
using NetTaste;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System.Collections.Generic;
using System.Data;
using System.Linq.Expressions;

namespace Hyun.Core.Api
{

    [Route("api/hyun/bdictionary")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class BDictionaryController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IBDictionaryServices dictionaryManager;
        //private readonly IMProjectConfigDetailServices projectConfigDetailManager;
        //private readonly IMProjectServices projectManager;
        //private readonly IMProjectConfigNodeServices projectConfigNodeManager;
        //private readonly IMProjectNodeDefinitionServices projectNodeDefinitionManager;
        private readonly IVAreaDictionaryServices vAreaDictionaryManager;
        private readonly IVSysStatServices vSysStatManager;
        private readonly IBAreaDictionaryServices areaDictionaryManager;
        private readonly IPUnitServices unitManager;
        private readonly IVXaDictionaryUnitServices vXaDictionaryUnitManager;
        private readonly IBAttachmentDataServices attachmentDataManager;
        private readonly IConfiguration configuration;
        //private readonly IAAssetsEquipmentListServices assetsEquipmentListManager;
        private readonly IUser user;

       /// <summary>
       /// 
       /// </summary>
       /// <param name="_mapper"></param>
       /// <param name="_env"></param>
       /// <param name="_dictionaryManager"></param>
       /// <param name="_projectConfigDetailManager"></param>
       /// <param name="_projectManager"></param>
       /// <param name="_projectConfigNodeManager"></param>
       /// <param name="_projectNodeDefinitionManager"></param>
       /// <param name="_vAreaDictionaryManager"></param>
       /// <param name="_vSysStatManager"></param>
       /// <param name="_areaDictionaryManager"></param>
       /// <param name="_unitManager"></param>
       /// <param name="_vXaDictionaryUnitManager"></param>
       /// <param name="_attachmentDataManager"></param>
       /// <param name="_configuration"></param>
       /// <param name="_assetsEquipmentListManager"></param>
       /// <param name="_user"></param>
        public BDictionaryController(IMapper _mapper, IWebHostEnvironment _env, IBDictionaryServices _dictionaryManager, /*IMProjectConfigDetailServices _projectConfigDetailManager, IMProjectServices _projectManager, IMProjectConfigNodeServices _projectConfigNodeManager, IMProjectNodeDefinitionServices _projectNodeDefinitionManager, */IVAreaDictionaryServices _vAreaDictionaryManager, IVSysStatServices _vSysStatManager, IBAreaDictionaryServices _areaDictionaryManager, IPUnitServices _unitManager, IVXaDictionaryUnitServices _vXaDictionaryUnitManager, IBAttachmentDataServices _attachmentDataManager, IConfiguration _configuration,/* IAAssetsEquipmentListServices _assetsEquipmentListManager,*/ IUser _user)
        {
            mapper = _mapper;
            env = _env;
            dictionaryManager = _dictionaryManager;
            //projectConfigDetailManager = _projectConfigDetailManager;
            //projectManager = _projectManager;
            //projectConfigNodeManager = _projectConfigNodeManager;
            //projectNodeDefinitionManager = _projectNodeDefinitionManager;
            vAreaDictionaryManager = _vAreaDictionaryManager;
            vSysStatManager = _vSysStatManager;
            areaDictionaryManager = _areaDictionaryManager;
            unitManager = _unitManager;
            vXaDictionaryUnitManager = _vXaDictionaryUnitManager;
            attachmentDataManager = _attachmentDataManager;
            configuration = _configuration;
            //assetsEquipmentListManager = _assetsEquipmentListManager;
            user = _user;
        }

        /// <summary>
        /// 获取字典列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dictionaryfind")]
        public async Task<Result> Dictionary_Find([FromBody] BDictionaryParam param)
        {
            Result r = new Result();
            PageModel<BDictionary> pg = await dictionaryManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<BDictionaryDto>>(pg.data);
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 根据Id查询字典信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dictionarygetbyid")]
        public async Task<Result> Dictionary_GetById(long id)
        {
            Result r = new Result();
            BDictionary b = await dictionaryManager.GetById(id);
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = mapper.Map<BDictionaryDto>(b);
            return r;
        }

        /// <summary>
        /// 新增修改字典值信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dictionaryinsertupdate")]
        public async Task<Result> Dictionary_InsertUpdate([FromBody] BDictionary o)
        {
            Result r = await dictionaryManager.InsertUpdate(o.TypeCode, o.TypeName, o.DicName, o.DicValue, o.Memo, o.Pid, o.Id, o.Sequence.Value);
            return r;
        }


        /// <summary>
        /// 字典值启用禁用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dictionarysetstatuz")]
        public async Task<Result> Dictionary_SetStatuz(long id)
        {
            Result r = await dictionaryManager.UpdateStatuz(id);
            return r;
        }

        /// <summary>
        /// 获取基础数据列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("areadictionaryfind")]
        //<used>1</used>
        public async Task<Result> AreaDictionary_Find([FromBody] VAreaDictionaryParam param)
        {
            Result r = new Result();
            PageModel<VAreaDictionary> pg = await vAreaDictionaryManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        /// <summary>
        /// 获取基础数据下拉框数据信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("gettypenamecombox")]
        //<used>1</used>
        public async Task<Result> GetTypeName_Combox()
        {
            Result r = new Result();
            List<BAreaDictionary> list =await areaDictionaryManager.Query();
            var listGrop = list.GroupBy(item => new {
                item.TypeCode,
                item.TypeName,
            }).Select(group => new BAreaDictionary
            {
                TypeCode = group.Key.TypeCode,
                TypeName = group.Key.TypeName,
            }).ToList();
            r.flag = 1;
            r.msg = "";
            r.data.rows = listGrop;
            return r;
        }


        /// <summary>
        /// 保存基础信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("areadictionarysave")]
        //<used>1</used>
        public async Task<Result> AreaDictionary_Save([FromBody] BAreaDictionary o)
        {
            Result r = new Result();
            bool isSystemAdmin = false;
            if (user.IsSystemUser)
            {
                isSystemAdmin = true;
            }
            r = await areaDictionaryManager.InsertUpdate(o.AreaId, o.TypeCode, o.TypeName, o.DicName, o.DicValue, o.Memo, o.EditWay, user.ID, o.Id, isSystemAdmin);
            return r;
        }

        /// <summary>
        /// 根据Id查询基础信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("areadictionarygetbyid")]
        //<used>1</used>
        public async Task<Result> AreaDictionary_GetById(long id)
        {
            Result r = new Result();
            List<VAreaDictionary> list = await vAreaDictionaryManager.Query(f => f.Id == id);
            if (list.Count > 0)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.total = list.Count;
                r.data.rows = list[0];
            }
            return r;
        }


        /// <summary>
        /// 根据字典编号获取字段信息
        /// </summary>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getdictionarycombox")]
        //<used>1</used>
        public async Task<Result> GetDictionary_Combox(string typeCode)
        {
            Result r = new Result();
            List <BDictionary> list= await dictionaryManager.GetByTypeCode(typeCode);
            list = list.OrderBy(f => f.Sequence).ToList();
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = mapper.Map<List<BDictionaryDto>>(list);
            return r;
        }


        /// <summary>
        /// 下载Execl
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("downloadexeclfile")]
        public async Task<Result> DownLoadExeclFile([FromBody] BDictionaryParam param)
        {
            Result r = new Result();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<BDictionary> pg = await dictionaryManager.GetPaged(param);

            IWorkbook iwork = new HSSFWorkbook();
            IFont font = iwork.CreateFont();
            font.FontHeightInPoints = 10;

            IFont font1 = iwork.CreateFont();
            font1.IsBold = true;
            font.FontHeightInPoints = 25;

            IFont font2 = iwork.CreateFont();
            font2.IsBold = true;
            font2.FontHeightInPoints = 10;

            ICellStyle cellstyle = iwork.CreateCellStyle();
            cellstyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellstyle.SetFont(font1);
            cellstyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellBlue = iwork.CreateCellStyle();
            cellBlue.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellBlue.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellBlue.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellBlue.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellBlue.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellBlue.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.SkyBlue.Index;
            cellBlue.FillPattern = FillPattern.SolidForeground;
            cellBlue.SetFont(font);
            cellBlue.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellRed = iwork.CreateCellStyle();
            cellRed.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRed.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRed.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRed.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRed.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellRed.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Red.Index;
            cellRed.FillPattern = FillPattern.SolidForeground;
            cellRed.SetFont(font);
            cellRed.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellLeft = iwork.CreateCellStyle();
            cellLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellLeft.SetFont(font);
            cellLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellRight = iwork.CreateCellStyle();
            cellRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellRight.SetFont(font);
            cellRight.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellCenter = iwork.CreateCellStyle();
            cellCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellCenter.SetFont(font);
            cellCenter.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNull = iwork.CreateCellStyle();
            cellNull.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellNull.SetFont(font1);
            cellNull.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNullLeft = iwork.CreateCellStyle();
            cellNullLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            font.FontHeightInPoints = 9;
            cellNullLeft.SetFont(font);
            cellNullLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellTitle = iwork.CreateCellStyle();
            cellTitle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle.SetFont(font2);
            cellTitle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellRightBold = iwork.CreateCellStyle();
            cellRightBold.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRightBold.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRightBold.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRightBold.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRightBold.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellRightBold.SetFont(font1);
            cellRightBold.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            //建立一个名为Sheet1的工作表
            NPOI.SS.UserModel.ISheet isheet = iwork.CreateSheet("字典管理");
            isheet.PrintSetup.PaperSize = 9;

            ICell cell = null;
            //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行
            NPOI.SS.Util.CellRangeAddress region = new NPOI.SS.Util.CellRangeAddress(0, 0, 0, 5);
            isheet.AddMergedRegion(region);


            NPOI.SS.UserModel.IRow rowTitle = isheet.CreateRow(0);
            rowTitle.HeightInPoints = 23;

            cell = rowTitle.CreateCell(0);
            cell.CellStyle = cellNull;
            cell.SetCellValue("字典管理");


            NPOI.SS.UserModel.IRow row1 = isheet.CreateRow(1);
            row1.HeightInPoints = 23;
            cell = row1.CreateCell(0);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("类别编码");
            isheet.SetColumnWidth(0, 4000);

            cell = row1.CreateCell(1);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("类别名称");
            isheet.SetColumnWidth(1, 9000);

            cell = row1.CreateCell(2);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("名称");
            isheet.SetColumnWidth(2, 9000);

            cell = row1.CreateCell(3);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("取值");
            isheet.SetColumnWidth(3, 6000);

            cell = row1.CreateCell(4);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("排序");
            isheet.SetColumnWidth(4, 3000);

            cell = row1.CreateCell(5);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("备注");
            isheet.SetColumnWidth(5, 9000);

            #region 打印重复表头
            //横向打印
            isheet.PrintSetup.Landscape = true;
            //缩放比：100% 不缩放
            isheet.PrintSetup.Scale = 100;
            //不缩放到一页
            isheet.FitToPage = false;
            //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
            isheet.RepeatingRows = new CellRangeAddress(1, 1, 0, int.MaxValue);
            #endregion

            if (pg.dataCount > 0)
            {
                List<BDictionary> list = pg.data;
                IRow rows = null;
                int y = 2;
                foreach (BDictionary b in list)
                {
                    rows = isheet.CreateRow(y);
                    rows.HeightInPoints = 20;
                    cell = rows.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(b.TypeCode);

                    cell = rows.CreateCell(1);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(b.TypeName);

                    cell = rows.CreateCell(2);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(b.DicName);


                    cell = rows.CreateCell(3);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(b.DicValue);

                    cell = rows.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(b.Sequence.ToString());

                    cell = rows.CreateCell(5);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(b.Memo);

                    y++;
                }

                var fileInfo = FileHelper.GetSaveFileInfo(env.ContentRootPath, "字典值管理");
                using (MemoryStream ms = new MemoryStream())
                {
                    iwork.Write(ms);
                    iwork.Close();
                    ms.Flush();
                    ms.Position = 0;
                    using (FileStream fs = new FileStream(fileInfo.filePath, FileMode.Create, FileAccess.Write))
                    {
                        byte[] data = ms.ToArray();
                        fs.Write(data, 0, data.Length);
                        fs.Flush();
                    }
                }
                r.data.rows = new
                {
                    url = fileInfo.url,
                    filename = fileInfo.fileName

                };
            }
            else
            {
                r.flag = 0;
                r.msg = "没有可导出的数据";
            }
            return r;
        }


        ///// <summary>
        ///// 查询列表
        ///// </summary>
        ///// <param name="param"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("teststockfind")]
        //public async Task<Result> TestStock_Find([FromBody] AAssetsEquipmentListParam param)
        //{
        //    var result = await Task.Run(() =>
        //    {
        //        Result r = new Result();
        //        //param.Name = "笔记本电脑";
        //        //param.DtBegin = DateTime.Parse("2023-01-01");
        //        DataTable dt = assetsEquipmentListManager.GetStockAssetsList(param);
        //        int pageCount = (int)Math.Ceiling((decimal)param.totalCount / param.pageSize);
        //        r.data.total = param.totalCount;
        //        r.flag = 1;
        //        r.data.rows = dt;
        //        r.msg = $"{param.pageIndex}-{param.pageSize}/{pageCount}";
        //        return r;
        //    });
        //    return result;
        //}
    }
}
