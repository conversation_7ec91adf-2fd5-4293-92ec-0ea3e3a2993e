namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///企业、单位认证待审核表
    ///</summary>
    [SugarTable("p_SupplierSchoolAudit", "企业、单位认证待审核表")]
    public class PSupplierSchoolAudit : BaseEntity
    {

        public PSupplierSchoolAudit()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        /// 父级Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long Pid { get; set; } = 0;

        /// <summary>
        ///企业名称
        /// </summary>
        [SugarColumn(Length = 255)]
        public string Name { get; set; }

        /// <summary>
        ///统一社会信用代码
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string SocialCreditCode { get; set; }

        /// <summary>
        ///营业执照
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string BusLicense { get; set; }

        /// <summary>
        ///省Id
        /// </summary>
        public long ProvinceId { get; set; } = 0;

        /// <summary>
        ///市Id
        /// </summary>
        public long CityId { get; set; } = 0;

        /// <summary>
        ///区Id
        /// </summary>
        public long CountyId { get; set; } = 0;

        /// <summary>
        ///地址
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Address { get; set; }

        /// <summary>
        ///电商网址
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Url { get; set; }

        /// <summary>
        ///公司简介
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string Introduction { get; set; }

        /// <summary>
        ///联系电话
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string Tel { get; set; }

        /// <summary>
        ///单位性质(1：公办  2：民办)
        /// </summary>
        public int Nature { get; set; } = 0;

        /// <summary>
        ///学段
        /// </summary>
        public int SchoolStage { get; set; } = 0;

        /// <summary>
        ///是否市直属(1：是，0:否)
        /// </summary>
        public bool BeLongUnit { get; set; } = false;

        /// <summary>
        ///认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)
        /// </summary>
        public int AuthStatuz { get; set; }

        /// <summary>
        /// 学段
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Period { get; set; }

        /// <summary>
        ///不通过原因
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Reason { get; set; }

        /// <summary>
        ///是否当前 0：否，1：是
        /// </summary>
        public bool IsCurrent { get; set; } = true;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 附件集合
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<BAttachment> ListAttachment { get; set; }


        /// <summary>
        /// 附件Id集合
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<long> ListId { get; set; }

        /// <summary>
        /// 单位类型
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int UnitType { get; set; }

        /// <summary>
        /// 状态（1：启用  2：禁用）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int Statuz { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int Sort { get; set; }
    }


    /// <summary>
    /// 运营商用户单位管理列表
    /// </summary>
    public class PUnitView : BaseEntityDto
    {
        /// <summary>
        /// 单位Id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        /// 单位类型
        /// </summary>
        public int UnitType { get; set; }

        /// <summary>
        /// 单位类型名称
        /// </summary>
        public string UnitTypeName { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 状态（1：启用  2：禁用）
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatuzName { get; set; }

        /// <summary>
        /// 审核状态名称
        /// </summary>
        public string AuditTypeName { get; set; }

        /// <summary>
        /// 排序值
        /// </summary>
        public int Sort { get; set; }
    }

}

