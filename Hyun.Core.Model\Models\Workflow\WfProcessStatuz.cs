﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批流程节点状态表
    ///</summary>
    [SugarTable("wf_ProcessStatuz", "审批流程节点状态表")]
    public class WfProcessStatuz : BaseEntity
    {

        public WfProcessStatuz()
        {

        }

        /// <summary>
        ///审批流程配置Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessId { get; set; }

        /// <summary>
        ///审批流程节点表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessNodeId { get; set; }

        /// <summary>
        ///状态值
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int Statuz { get; set; }

        /// <summary>
        ///状态描述
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string StatuzDesc { get; set; }

        /// <summary>
        ///等待或退回(1：等待  2：退回  3：单位立项中 4：审批结束)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? WaitOrBack { get; set; }

        /// <summary>
        ///启用禁用（1：启用  2：禁用）
        /// </summary>
        public int IsEnable { get; set; } = 1;

        /// <summary>
        ///排序值
        /// </summary>
        public int Sort { get; set; } = 0;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 节点名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string NodeName { get; set; }


        /// <summary>
        /// 节点显示名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string NodeShowName { get; set; }

        /// <summary>
        /// 等待或退回
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrWaitOrBack { get; set; }
    }


}

