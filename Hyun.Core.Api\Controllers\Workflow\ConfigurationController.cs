﻿using Autofac;
using Dm;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Helper;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.Models.Workflow;
using Hyun.Old.Util;
using MathNet.Numerics.Statistics.Mcmc;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using NCalc;
using NetTaste;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.SS.UserModel.Charts;
using NPOI.SS.Util;
using NPOI.XWPF.UserModel;
using OfficeOpenXml.FormulaParsing.ExpressionGraph;
using Org.BouncyCastle.Bcpg.OpenPgp;
using ProtoBuf.Meta;
using SkyWalking.NetworkProtocol.V3;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq.Expressions;
using System.Reflection;

namespace Hyun.Core.Api.Controllers.Workflow
{

    [Route("api/hyun/workflow")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ConfigurationController : BaseApiController
    {

        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IWfModuleServices moduleManager;
        private readonly IWfProcessServices processManager;
        private readonly IWfProcessNodeServices processNodeManager;
        private readonly IWfProcessFieldServices processFieldManager;
        private readonly IWfDictionaryServices wfDictionaryManager;
        private readonly IWfProjectListFieldSetServices wfProjectListFieldSetManager;
        private readonly IWfProjectListServices wfProjectListManager;
        private readonly IPUnitServices unitManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly ISysUserInfoServices sysUserInfoManager;
        private readonly IWfProcessStatuzServices processStatuzManager;
        private readonly ISysUserExtensionServices sysUserExtensionManager;
        private readonly IWfSourceFundServices sourceFundManager;
        private readonly IWfProcessNodeLinkServices processNodeLinkManager;
        private readonly IWfPageDefinitionServices pageDefinitionManager;
        private readonly IWfPageColumnConfigServices pageColumnConfigManager;
        private readonly IWfProjectAuditUserServices projectAuditUserManager;
        private readonly IWfProcessReturnSetServices processReturnSetManager;
        private readonly IWfFundFieldSetServices fundFieldSetManager;
        private readonly IWfSourceFundSetServices sourceFundSetManager;
        private readonly IWfFundProcessNodeSetServices fundProcessNodeSetManager;
        private readonly IWfLinkAgeHostServices linkAgeHostManager;
        private readonly IWfControlDetailServices controlDetailManager;
        private readonly IWfCodeGenerateSetServices codeGenerateSetManager;
        private readonly IWfGroupProcessSetServices groupSetManager;
        private readonly IWfGroupUnitSetServices groupUnitSetManager;
        private readonly IWfChildUnitDisableServices childUnitDisableManager;
        private readonly IBMsgConfigServices msgConfigManager;
        private readonly IWfMsgConfigServices wfMsgConfigManager;

        public ConfigurationController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IWfModuleServices _moduleManager, IWfProcessServices _processManager, IWfProcessNodeServices _processNodeManager, IWfProcessFieldServices _processFieldManager, IWfDictionaryServices _wfDictionaryManager, IWfProjectListFieldSetServices _wfProjectListFieldSetManager, IWfProjectListServices _wfProjectListManager, IPUnitServices _unitManager, IBDictionaryServices _dictionaryManager, ISysUserInfoServices _sysUserInfoManager, IWfProcessStatuzServices _processStatuzManager, ISysUserExtensionServices _sysUserExtensionManager, IWfSourceFundServices _sourceFundManager, IWfProcessNodeLinkServices _processNodeLinkManager, IWfPageDefinitionServices _pageDefinitionManager, IWfPageColumnConfigServices _pageColumnConfigManager, IWfProjectAuditUserServices _projectAuditUserManager, IWfProcessReturnSetServices _processReturnSetManager, IWfFundFieldSetServices _fundFieldSetManager, IWfSourceFundSetServices _sourceFundSetManager, IWfFundProcessNodeSetServices _fundProcessNodeSetManager, IWfLinkAgeHostServices _linkAgeHostManager, IWfControlDetailServices _controlDetailManager, IWfCodeGenerateSetServices _codeGenerateSetManager, IWfGroupProcessSetServices _groupSetManager, IWfGroupUnitSetServices _groupUnitSetManager, IWfChildUnitDisableServices _childUnitDisableManager, IBMsgConfigServices _msgConfigManager, IWfMsgConfigServices _wfMsgConfigManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            moduleManager = _moduleManager;
            processManager = _processManager;
            processNodeManager = _processNodeManager;
            processFieldManager = _processFieldManager;
            wfDictionaryManager = _wfDictionaryManager;
            wfProjectListFieldSetManager = _wfProjectListFieldSetManager;
            wfProjectListManager = _wfProjectListManager;
            unitManager = _unitManager;
            dictionaryManager = _dictionaryManager;
            sysUserInfoManager = _sysUserInfoManager;
            processStatuzManager = _processStatuzManager;
            sysUserExtensionManager = _sysUserExtensionManager;
            sourceFundManager = _sourceFundManager;
            processNodeLinkManager = _processNodeLinkManager;
            pageDefinitionManager = _pageDefinitionManager;
            pageColumnConfigManager = _pageColumnConfigManager;
            projectAuditUserManager = _projectAuditUserManager;
            processReturnSetManager = _processReturnSetManager;
            fundFieldSetManager = _fundFieldSetManager;
            sourceFundSetManager = _sourceFundSetManager;
            fundProcessNodeSetManager = _fundProcessNodeSetManager;
            linkAgeHostManager = _linkAgeHostManager;
            controlDetailManager = _controlDetailManager;
            codeGenerateSetManager = _codeGenerateSetManager;
            groupSetManager = _groupSetManager;
            groupUnitSetManager = _groupUnitSetManager;
            childUnitDisableManager = _childUnitDisableManager;
            msgConfigManager = _msgConfigManager;
            wfMsgConfigManager = _wfMsgConfigManager;
        }

        #region 模块管理
        /// <summary>
        /// 查询模块列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findmodulelist")]
        public async Task<Result<PageModel<WfModuleDto>>> FindModuleList([FromBody] WfModuleParam param)
        {
            PageModel<WfModule> data = await moduleManager.GetPaged(param);
            if (param.isFirst)
            {
                //使用方式
                var listEnumUsage = EnumExtensions.EnumToList<WfUsageEnum>();
                List<dropdownModel> listUsage = listEnumUsage.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //使用单位Id
                var listUnit = await unitManager.Query(f => f.IsDeleted == false && f.Statuz == 1 && (f.UnitType == UnitTypeEnum.City.ObjToInt() || f.UnitType == UnitTypeEnum.County.ObjToInt() || f.UnitType == UnitTypeEnum.School.ObjToInt()));
                List<dropdownModel> listUseUnitId = listUnit.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();
                return baseSucc(data.ConvertTo<WfModuleDto>(mapper), data.dataCount, "查询成功", new { listUsage = listUsage, listUseUnitId = listUseUnitId });
            }
            else
            {
                return baseSucc(data.ConvertTo<WfModuleDto>(mapper), data.dataCount);
            }
           
        }

        /// <summary>
        /// 新增修改模块信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleinsertupdate")]
        public async Task<Result<WfModuleDto>> ModuleInsertUpdate([FromBody] WfModuleDto o)
        {
            var saveResult = await moduleManager.InsertUpdate(o);
            return saveResult;
        }

        /// <summary>
        /// 根据Id获取模块信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("modulefindbyid")]
        public async Task<Result<WfModuleDto>> ModuleFindById(long id)
        {
            WfModule m = await moduleManager.QueryById(id);
            if (m != null)
            {
                m.StrUsage = m.Usage.ToString();
                m.StrUseUnitId = m.UseUnitId.ToString();
                return Result<WfModuleDto>.Success("查询成功。", mapper.Map<WfModuleDto>(m), 1);
            }
            else
            {
                return Result<WfModuleDto>.Fail("未查询到数据");
            }
        }

        /// <summary>
        /// 启用/禁用模块信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("modulesetstatuz")]
        public async Task<Result<string>> ModuleSetStatuz(long id)
        {
            WfModule m = await moduleManager.QueryById(id);
            if (m != null)
            {
                if (m.Statuz == 1)
                {
                    m.Statuz = 2;
                }
                else if (m.Statuz == 2)
                {
                    m.Statuz = 1;
                }

                await moduleManager.Update(m);
            }
            return baseSucc<string>("设置成功", 1);
        }
        #endregion

        #region 流程管理
        /// <summary>
        /// 流程管理：获取流程列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findprocesslist")]
        public async Task<Result<PageModel<WfProcessDto>>> FindProcessList([FromBody] WfProcessParam param)
        {
            PageModel<WfProcess> data = await processManager.GetPaged(param);
            if (param.isFirst)
            {
                //获取模块信息A
                //&& f.Statuz == 1
                var listUseModule = await moduleManager.Query(f => f.IsDeleted == false);
                List<dropdownModel> listModule = listUseModule.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                //使用方式
                var listEnumUsage = EnumExtensions.EnumToList<WfUsageEnum>();
                List<dropdownModel> listUsage = listEnumUsage.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //使用单位
                var listUseUnit = await unitManager.Query(f => f.IsDeleted == false && f.Statuz == 1 && (f.UnitType == UnitTypeEnum.City.ObjToInt() || f.UnitType == UnitTypeEnum.County.ObjToInt() || f.UnitType == UnitTypeEnum.School.ObjToInt()));
                List<dropdownModel> listUnit = listUseUnit.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                //写入第三方库类型
                var listUseInputDataType = EnumExtensions.EnumToList<WfInputDataType>();
                List<dropdownModel> listInputDataType = listUseInputDataType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //是否(用于是否显示历史附件、是否签名、是否控制金额)
                var listUseYesNo = EnumExtensions.EnumToList<YesNoEnum>();
                List<dropdownModel> listYesNo = listUseYesNo.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();


                //var listProcessField = await processFieldManager.Query(f => f.ModuleId == param.ModuleId && f.ControlType == "projectlist");
                //List<string> listStrFieldCode = listProcessField.Select(f => f.FieldCode).Distinct().ToList();
                //List<dropdownModel> listFieldCode = listStrFieldCode.Select(f => new dropdownModel { value = f.ToString(), label = f.ToString() }).ToList();
                

                return baseSucc(data.ConvertTo<WfProcessDto>(mapper), data.dataCount, "查询成功", new { listModule = listModule, listUsage = listUsage, listUnit = listUnit, listInputDataType = listInputDataType, listYesNo = listYesNo });
            }
            else
            {
                return baseSucc(data.ConvertTo<WfProcessDto>(mapper), data.dataCount);
            }
        }

        /// <summary>
        /// 流程管理：保存流程信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("processinsertupdate")]
        public async Task<Result<WfProcessDto>> ProcessInsertUpdate([FromBody] WfProcessDto o)
        {
            o.CreateUnitId = user.UnitId;
            var saveResult = await processManager.InsertUpdate(o);
            return saveResult;
        }

        /// <summary>
        /// 流程管理：根据Id获取流程信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("processfindbyid")]
        public async Task<Result> ProcessFindById(long id)
        {
            Result r = new Result();
            WfProcess m = await processManager.QueryById(id);
            if (m != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                var listProcessField = await processFieldManager.Query(f => f.ModuleId == m.ModuleId && f.ControlType == "projectlist");
                List<string> listStrFieldCode = listProcessField.Select(f => f.FieldCode).Distinct().ToList();
                List<dropdownModel> listFieldCode = listStrFieldCode.Select(f => new dropdownModel { value = f.ToString(), label = f.ToString() }).ToList();            
                List<WfProcessStatuz> listStatuz = await processStatuzManager.Query(f => f.ProcessId == id && (f.WaitOrBack == 1 || f.WaitOrBack == 4));
                List<dropdownModel> listWriteSourceFundStatuz = listStatuz.Select(f => new dropdownModel { value = f.Statuz.ToString(), label = f.StatuzDesc }).ToList();
                r.data.rows = mapper.Map<WfProcessDto>(m);
                r.data.other = new { listProjectListCode = listFieldCode, listWriteSourceFundStatuz = listWriteSourceFundStatuz };

                List<WfFundProcessNodeSetDto> list = await fundProcessNodeSetManager.GetFundSetListByProcessId(id);
                r.data.headers = list;
            }
            else
            {
                r.flag = 0;
                r.msg = "未查询到数据";
            }
            return r;
        }

        /// <summary>
        /// 流程管理：启用/禁用流程
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("processsetstatuz")]
        public async Task<Result<string>> ProcessSetStatuz(long id)
        {
            WfProcess m = await processManager.QueryById(id);
            if (m != null)
            {
                if (m.Statuz == 1)
                {
                    m.Statuz = 2;
                }
                else if (m.Statuz == 2)
                {
                    m.Statuz = 1;
                }

                await processManager.Update(m);
               
            }
            return baseSucc<string>("设置成功", 1);
        }


        /// <summary>
        /// 流程管理：删除流程信息（根据id）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("processdeletebyid")]
        public async Task<Result<string>> ProcessDeleteById(long id)
        {
            return await processManager.DeleteById(id);
        }


        /// <summary>
        /// 流程管理：根据Id设置流程信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("processsetbyid")]
        public async Task<Result> ProcessSetById(long id)
        {
            Result r = new Result();
            WfProcess m = await processManager.QueryById(id);
            if (m != null)
            {
                //根据ModuleId获取节点信息
                List<WfProcessNode> listNode = await processNodeManager.Query(f => f.ModuleId == m.ModuleId);
                //if (!string.IsNullOrEmpty(m.NodeConfig))
                //{
                //    List<NodeModel> listAddedNode = JsonHelper.JsonToObj<List<NodeModel>>(m.NodeConfig);
                //    List<long> listId = listAddedNode.Select(f => f.id).ToList();
                //    listNode = listNode.Where(f => !listId.Contains(f.Id)).ToList();
                //}

                var nodeConfig = m.NodeConfig;
                var lineConfig = m.LineConfig;

                //var listConditionEnum = EnumExtensions.EnumToList<WfConditionSymbol>();
                //List<dropdownModel> listConditionSymbol = listConditionEnum.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();
                List<dropdownModel> listConditionSymbol = new List<dropdownModel>();
                listConditionSymbol.Add(new dropdownModel() { value = "==", label = "等于(==)" });
                listConditionSymbol.Add(new dropdownModel() { value = "!=", label = "不等于(!=)" });
                listConditionSymbol.Add(new dropdownModel() { value = ">", label = "大于(>)" });
                listConditionSymbol.Add(new dropdownModel() { value = ">=", label = "大于等于(>=)" });
                listConditionSymbol.Add(new dropdownModel() { value = "<", label = "小于(<)" });
                listConditionSymbol.Add(new dropdownModel() { value = "<=", label = "小于等于(<=)" });
                listConditionSymbol.Add(new dropdownModel() { value = "#", label = "占位(#)" });
  

                //获取字段信息
                var listField = await processFieldManager.Query(f => f.ModuleId == m.ModuleId && f.Statuz == 1 && f.IsCondition == 1 && f.IsRequired == 1);
                var listGroupField = listField.GroupBy(f => new
                {
                    f.FieldCode,
                    f.ShowName,
                    f.TypeBox,
                    f.TypeCode,
                    f.ControlType
                })
                .Select(group => new dropdownModel
                {
                    pid = group.Key.TypeCode > 0 ? 1 : 0,
                    pname = $"{group.Key.TypeCode}_{group.Key.TypeBox}",
                    value = $"{group.Key.FieldCode}",
                    label = $"{group.Key.ShowName}({group.Key.FieldCode})"
                }).ToList();

                //增加主体性质
                listGroupField.Add(new dropdownModel()
                {
                    pid = 1,
                    pname = "15000_2",
                    value = "SubjectNature",
                    label = "主体性质(SubjectNature)"
                });

                m.NodeConfig = "";
                m.LineConfig = "";
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<WfProcessDto>(m);
                r.data.other = new { nodeConfig = nodeConfig, lineConfig = lineConfig, listNode = listNode, listConditionSymbol = listConditionSymbol, listGroupField = listGroupField };
            }
            return r;
        }

        /// <summary>
        /// 流程管理：根据Id获取流程节点关系信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("processnodelinkbyid")]
        public async Task<Result> ProcessNodeLinkById(long id)
        {
            Result r = new Result();
            WfProcessNodeLink nodeLink = await processNodeLinkManager.QueryById(id);
            if (nodeLink != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<WfProcessNodeLinkDto>(nodeLink);
                r.data.other = new { listConditionConfig = nodeLink.ConditionConfig };
            }
            return r;
        }

        /// <summary>
        /// 流程管理：提交流程信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("processsetsave")]
        public async Task<Result<string>> ProcessSetSave([FromBody] WfProcessSetConfigModel o)
        {
            return await processManager.ProcessSetSave(o);
        }

        /// <summary>
        /// 流程管理：根据Id删除节点连线
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("processlinkdeletebyid")]
        public async Task<Result<string>> ProcessLinkDeleteById(long id)
        {
            return await processManager.DeleteProcessNodeLinkById(id);
        }

        /// <summary>
        /// 流程管理：根据Id删除节点
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("processnodedeletebyid")]
        public async Task<Result<string>> ProcessNodeDeleteById(long id)
        {
            return await processManager.DeleteProcessNodeById(id);
        }
        #endregion

        #region 节点管理
        /// <summary>
        /// 流程管理：获取节点列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findnodelist")]
        public async Task<Result<PageModel<WfProcessNodeDto>>> FindNodeList([FromBody] WfProcessNodeParam param)
        {
            PageModel<WfProcessNode> data = await processNodeManager.GetPaged(param);
            if (param.isFirst)
            {
                //获取模块信息
                //&& f.Statuz == 1   禁用只针对前端给用户的，不涉及这边管理
                var listUseModule = await moduleManager.Query(f => f.IsDeleted == false);
                List<dropdownModel> listModuleId = listUseModule.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                //节点类型
                var listUseNodeType = EnumExtensions.EnumToList<WfNodeTypeType>();
                List<dropdownModel> listNodeType = listUseNodeType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //是否(是否为部门审批、是否审批清单、是否允许导出Excel、是否可撤回、提交时是否锁金额并验证清单总和)
                var listUseYesNo = EnumExtensions.EnumToList<YesNoEnum>();
                List<dropdownModel> listYesNo = listUseYesNo.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //流程级别
                var listUseProcessLevel = EnumExtensions.EnumToList<WfProcessLevel>();
                List<dropdownModel> listProcessLevel = listUseProcessLevel.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //审批方式
                var listUseAduitType = EnumExtensions.EnumToList<WfAduitType>();
                List<dropdownModel> listAduitType = listUseAduitType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //审核人员类型
                var listUseAduitUserType = EnumExtensions.EnumToList<WfAduitUserType>();
                List<dropdownModel> listAduitUserType = listUseAduitUserType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //指定审批人对象类型
                var listUseWfAuditObjectType = EnumExtensions.EnumToList<WfAuditObjectType>();
                List<dropdownModel> listWfAuditObjectType = listUseWfAuditObjectType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //指定人员数量
                var listUseWfDesigneeNum = EnumExtensions.EnumToList<WfDesigneeNum>();
                List<dropdownModel> listWfDesigneeNum = listUseWfDesigneeNum.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //审批人员方式
                var listUseApprovalMethod = EnumExtensions.EnumToList<WfApprovalMethod>();
                List<dropdownModel> listApprovalMethod = listUseApprovalMethod.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();


                return baseSucc(data.ConvertTo<WfProcessNodeDto>(mapper), data.dataCount, "查询成功", new { listModuleId = listModuleId, listNodeType = listNodeType, 
                    listIsDepartProcess = listYesNo, listProcessLevel = listProcessLevel, listAduitType = listAduitType, 
                    listIsAuditProjectList = listYesNo, listIsAllowExport = listYesNo, listIsWithdraw = listYesNo, 
                    listIsLockProjectAmount = listYesNo, listAduitUserType = listAduitUserType, 
                    listAuditObjectType = listWfAuditObjectType, listDesigneeNum = listWfDesigneeNum,
                    listBackIsSendMsg = listYesNo,
                    listNextIsSendMsg = listYesNo,
                    listApprovalMethod = listApprovalMethod,
                });
            }
            else
            {
                return baseSucc(data.ConvertTo<WfProcessNodeDto>(mapper), data.dataCount);
            }
            
        }

        /// <summary>
        /// 流程管理：保存节点信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("nodeinsertupdate")]
        public async Task<Result<WfProcessNodeDto>> NodeInsertUpdate([FromBody] WfProcessNodeDto o)
        {
            return await processNodeManager.InsertUpdate(o);

        }

        /// <summary>
        /// 流程管理：根据Id获取节点详情信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("nodefindbyid")]
        public async Task<Result> NodeFindById(long id)
        {
            Result r = new Result();
            WfProcessNode m = await processNodeManager.QueryById(id);
            if (m != null)
            {
                var listProcessNode = await processNodeManager.Query(f => f.ModuleId == m.ModuleId && f.Id != m.Id);
                List<dropdownModel> listNode = listProcessNode.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.NodeName }).ToList();

                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<WfProcessNodeDto>(m);
                r.data.headers = listNode;
            }
            else
            {
                r.flag = 1;
                r.msg = "未查询到数据";
            }
            return r;
        }

        /// <summary>
        /// 流程管理：根据Id获取节点Json数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("nodejsonfindbyid")]
        public async Task<Result> NodeJsonFindById(long id)
        {
            Result r = new Result();
            WfProcessNode m = await processNodeManager.QueryById(id);
            if (m != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = m.NodeConfig;
            }
            return r;
        }

        //

        /// <summary>
        /// 流程管理：删除节点信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("nodedeletebyid")]
        public async Task<Result<string>> NodeDeleteById(long id)
        {
            return await processNodeManager.DeleteById(id);
        }

        /// <summary>
        /// 流程管理：保存节点Json数据
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("nodeconfigsave")]
        public async Task<Result<string>> NodeConfigSave([FromBody] WfNodeConfigModel o)
        {
            return await processNodeManager.ConfigSave(o);
        }

        /// <summary>
        /// 流程管理：根据模块Id和流程节点Id获取该模块下所有节点数据
        /// 创建时流程节点Id传0，修改时节点Id传具体Id值
        /// </summary>
        /// <param name="moduleId">模块Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getnextprocessnode")]
        public async Task<Result> GetNextProcessNode(long moduleId,long processNodeId)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            var expression = LinqExtensions.True<WfProcessNode>();
            expression = expression.AndNew(f => f.ModuleId == moduleId);
            if(processNodeId > 0)
            {
                expression = expression.AndNew(f => f.Id != processNodeId);
            }
            var listProcessNode = await processNodeManager.Query(expression);
            List<dropdownModel> listNode = listProcessNode.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.NodeName }).ToList();
            r.data.rows = listNode;
            return r;
        }
        #endregion

        #region 流程状态列表
        /// <summary>
        /// 流程管理：获取流程状态列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findprocessstatuzlist")]
        public async Task<Result<PageModel<WfProcessStatuzDto>>> FindProcessStatuzList([FromBody] WfProcessStatuzParam param)
        {
            PageModel<WfProcessStatuz> data = await processStatuzManager.GetPaged(param);
            return baseSucc(data.ConvertTo<WfProcessStatuzDto>(mapper), data.dataCount);
        }

        /// <summary>
        /// 流程管理：修改状态值
        /// 只能修改排序值和状态描述
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("processstatuzedit")]
        public async Task<Result<string>> ProcessStatuzEdit([FromBody] WfProcessStatuzDto o)
        {
            return await processStatuzManager.EditProcessStatuz(o);
        }

        #endregion

        #region 字段管理
        /// <summary>
        /// 流程管理：字段配置列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findfieldlist")]
        //<used>1</used>
        public async Task<Result<PageModel<WfProcessFieldDto>>> FindFieldList([FromBody] WfProcessFieldParam param)
        {
            PageModel<WfProcessField> data = await processFieldManager.GetPaged(param);
            return baseSucc(data.ConvertTo<WfProcessFieldDto>(mapper), data.dataCount);
        }

        /// <summary>
        /// 流程管理：保存字段配置信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("fieldinsertupdate")]
        //<used>1</used>
        public async Task<Result> FieldInsertUpdate([FromBody] WfProcessFieldDto o)
        {
            Result r = new Result();
            WfProcessField m = mapper.Map<WfProcessField>(o);
            r = await processFieldManager.InsertUpdate(m);
            return r;
        }

        /// <summary>
        /// 流程管理：获取字段配置详情信息（根据id）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("fieldfindbyid")]
        //<used>1</used>
        public async Task<Result> FieldFindById(long id)
        {
            Result r = new Result();
            WfProcessField m = await processFieldManager.QueryById(id);
            if (m != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<WfProcessFieldDto>(m);
            }
            return r;
        }

        /// <summary>
        /// 流程管理：设置字段状态（启用、禁用）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("fieldsetstatuz")]
        //<used>1</used>
        public async Task<Result> FieldSetStatuz(long id)
        {
            Result r = new Result();
            WfProcessField m = await processFieldManager.QueryById(id);
            if (m != null)
            {
                if (m.Statuz == 1)
                {
                    m.Statuz = 2;
                }
                else if (m.Statuz == 2)
                {
                    m.Statuz = 1;
                }

                await processFieldManager.Update(m);
                r.flag = 1;
                r.msg = "设置成功";
            }
            return r;
        }



        /// <summary>
        /// 流程管理：删除字段配置信息（根据id）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("fielddeletebyid")]
        //<used>1</used>
        public async Task<Result> FieldDeleteById(long id)
        {
            Result r = new Result();
            r = await processFieldManager.DeleteById(id);
            return r;
        }

        #endregion

        #region 数据字典管理
        /// <summary>
        /// 流程管理：获取审批字典列表（wfdictionary）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("finddictionarylist")]
        public async Task<Result> FindDictionaryList([FromBody] WfDictionaryParam param)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            var expression = LinqExtensions.True<WfDictionary>();
            expression = expression.AndNew(f => f.IsDeleted == false);
            if (!string.IsNullOrEmpty(param.Key))
            {
                expression = expression.AndNew(f => f.ClassifyName.Contains(param.Key) || f.Name.Contains(param.Key));
            }
            var listDictionary = await wfDictionaryManager.Query(expression);
            listDictionary = listDictionary.OrderBy(f => f.Sort).ToList();
            var treeData = BuildDictionaryTree(listDictionary);
            r.data.rows = treeData;
            return r;
            //string od = "Id ASC";
            //if (param.sortModel != null && param.sortModel.Count > 0)
            //{
            //    od  = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            //}
            //var expression = LinqExtensions.True<WfDictionary>();
            //if (!string.IsNullOrEmpty(param.Key))
            //{
            //    expression = expression.AndNew(f=>f.ClassifyName.Contains(param.Key) || f.Name.Contains(param.Key));
            //}
            //param.pageSize = int.MaxValue;
            //PageModel<WfDictionary> data = await wfDictionaryManager.QueryPage(expression, param.pageIndex, param.pageSize, od);
            //if (param.isFirst)
            //{
            //    //获取类别名称
            //    List<WfDictionary> listDic = await wfDictionaryManager.Query(f => f.IsDeleted == false && f.DicType == 1);
            //    List<dropdownModel> listTypeCode = listDic.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

            //    return baseSucc(data.ConvertTo<WfDictionaryDto>(mapper), data.dataCount, "查询成功", new { listTypeCode = listTypeCode });
            //}
            //else
            //{
            //    return baseSucc(data.ConvertTo<WfDictionaryDto>(mapper), data.dataCount);
            //}
        }


        /// <summary>
        /// 流程管理：根据id获取字典信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("dictionaryfindbyid")]
        public async Task<Result<WfDictionaryDto>> DictionaryFindById(long id)
        {
            WfDictionary m = await wfDictionaryManager.QueryById(id);
            if (m != null)
            {
                return Result<WfDictionaryDto>.Success("查询成功。", mapper.Map<WfDictionaryDto>(m), 1);
            }
            else
            {
                return Result<WfDictionaryDto>.Fail("未查询到数据");
            }
        }

        /// <summary>
        /// 流程管理：新增修改字典信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dictionaryinsertupdate")]
        public async Task<Result<string>> DictionaryInsertUpdate([FromBody] WfDictionaryModel o)
        {
            return await wfDictionaryManager.InsertUpdate(o);
        }


        /// <summary>
        /// 流程管理：设置流程字典状态（启用、禁用）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("dictionarysetstatuz")]
        public async Task<Result<string>> DictionarySetStatuz(long id)
        {
            WfDictionary m = await wfDictionaryManager.QueryById(id);
            if (m != null)
            {
                if (m.Statuz == 1)
                {
                    m.Statuz = 2;
                }
                else if (m.Statuz == 2)
                {
                    m.Statuz = 1;
                }
                await wfDictionaryManager.Update(m);
                return Result<string>.Fail("设置成功");
            }
            else
            {
                return Result<string>.Fail("非法操作");
            }
        }

        #endregion

        #region 项目编号自动生成配置
        /// <summary>
        /// 流程管理：根据节点Id及编码获取编码配置信息
        /// </summary>
        /// <param name="processNodeId"></param>
        /// <param name="fieldCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getgeneratecodeset")]
        public async Task<Result> GetGenerateCodeSet(long processNodeId, string fieldCode)
        {
            return await codeGenerateSetManager.GetGenerateCodeSet(processNodeId, fieldCode);
        }

        /// <summary>
        /// 流程管理：保存编码配置信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsavegeneratecodeset")]
        public async Task<Result<string>> PostSaveGenerateCodeSet([FromBody] WfCodeGenerateSetDto o)
        {
            return await codeGenerateSetManager.SaveGenerateCodeSet(o);
        }
        #endregion

        #region 联动数据配置
        /// <summary>
        /// 流程管理：获取联动数据配置列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postlinkagehostlist")]
        public async Task<Result<PageModel<WfLinkAgeHostDto>>> PostLinkAgeHostList([FromBody] WfLinkAgeHostParam param)
        {
            PageModel<WfLinkAgeHostDto> data = await linkAgeHostManager.GetPageList(param);
            return baseSucc(data, data.dataCount);
        }

        /// <summary>
        /// 流程管理：根据审批联动主表Id获取联动父子联动数据及关联关系
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getlinkagehostdetaillist")]
        public async Task<Result> GetLinkAgeHostDetailList(long id)
        {
            Result r = await linkAgeHostManager.GetLinkAgeHostDetail(id);
            return r;
        }

        /// <summary>
        /// 流程管理：根据联动主表Id获取配置列表信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getdetailsetlist")]
        public async Task<Result> GetDetailSetList(long id)
        {
            Result r = await linkAgeHostManager.GetDetailSetList(id);
            return r;
        }

        /// <summary>
        /// 流程管理：根据联动主表Id父级Id获取选中子集数据信息
        /// </summary>
        /// <param name="id">点配置带过来的Id</param>
        /// <param name="parentId">选中项的Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getdetaibyparentid")]
        public async Task<Result> GetDetaiByParentId(long id,long parentId)
        {
            Result r = await linkAgeHostManager.GetDetailByParentId(id, parentId);
            return r;
        }

        /// <summary>
        /// 流程管理：保存联动关系数据信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsavelinkagedetail")]
        public async Task<Result<string>> PostSaveLinkAgeDetail([FromBody] LinkAgeDetailModel model)
        {
            return await linkAgeHostManager.SaveLinkAgeHostDetail(model);
        }

        #endregion

        #region 预算清单配置
        /// <summary>
        /// 流程审批：查询审批预算清单配置列表（后台）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("projectlistfieldsetlist")]
        public async Task<Result<PageModel<WfProjectListFieldSetDto>>> FindProjectListFieldSetList([FromBody] WfProjectListFieldSetParam param)
        {

            PageModel<WfProjectListFieldSet> data = new PageModel<WfProjectListFieldSet>();
            List<dropdownModel> listFieldCode = new List<dropdownModel>();

            List<dropdownModel> listTypeStyle = new List<dropdownModel>();
            listTypeStyle.Add(new dropdownModel() { value = "1", label = "文本框" });
            listTypeStyle.Add(new dropdownModel() { value = "2", label = "数字框" });
            listTypeStyle.Add(new dropdownModel() { value = "3", label = "下拉框" });
            listTypeStyle.Add(new dropdownModel() { value = "4", label = "多行文本框" });
            listTypeStyle.Add(new dropdownModel() { value = "5", label = "日期" });

            List<dropdownModel> listTitleStyle = new List<dropdownModel>();
            listTitleStyle.Add(new dropdownModel() { value = "left", label = "居左" });
            listTitleStyle.Add(new dropdownModel() { value = "center", label = "居中" });
            listTitleStyle.Add(new dropdownModel() { value = "right", label = "居右" });


            var listUseYesNo = EnumExtensions.EnumToList<YesNoEnum>();
            List<dropdownModel> listYesNo = listUseYesNo.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();


            //根据流程Id查询清单Code集合
            var objProcess = await processManager.QueryById(param.ProcessId);
            if (objProcess != null)
            {
                var listProcessField = await processFieldManager.Query(f => f.ModuleId == objProcess.ModuleId && f.ControlType == "projectlist");
                if (listProcessField.Count > 0)
                {
                    listFieldCode = listProcessField.Select(f => new dropdownModel { value = f.FieldCode, label = f.FieldName }).ToList();
                    if (string.IsNullOrEmpty(param.FieldCode))
                    {
                        param.FieldCode = listProcessField[0].FieldCode;
                    }
                    data = await wfProjectListFieldSetManager.GetPaged(param);

                    foreach (var item in data.data)
                    {
                        //1：文本框  2：数字框 3：下拉框 4：多行文本框 5：日期
                        switch (item.TypeStyle)
                        {
                            case 2:
                                item.StrTypeStyle = "数字框";
                                break;
                            case 3:
                                item.StrTypeStyle = "下拉框";
                                break;
                            case 4:
                                item.StrTypeStyle = "多行文本框";
                                break;
                            case 5:
                                item.StrTypeStyle = "日期";
                                break;
                            default:
                                item.StrTypeStyle = "文本框";
                                break;
                        }
                    }

                    return baseSucc(data.ConvertTo<WfProjectListFieldSetDto>(mapper), data.dataCount, "查询成功", new { listTypeStyle = listTypeStyle, listTitleStyle = listTitleStyle, listYesNo = listYesNo, listFieldCode = listFieldCode });
                }
                else
                {
                    return baseSucc(data.ConvertTo<WfProjectListFieldSetDto>(mapper), data.dataCount, "未配置项目清单信息", new { listTypeStyle = listTypeStyle, listTitleStyle = listTitleStyle, listYesNo = listYesNo, listFieldCode = listFieldCode });
                }
            }
            else
            {
                return baseSucc(data.ConvertTo<WfProjectListFieldSetDto>(mapper), data.dataCount, "流程Id不存在", new { listTypeStyle = listTypeStyle, listTitleStyle = listTitleStyle, listYesNo = listYesNo, listFieldCode = listFieldCode });
            }
        }

        /// <summary>
        /// 流程审批：根据Id查询审批预算清单配置数据（后台）
        /// </summary>
        /// <param name="id">预算清单配置Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("projectlistfieldsetbyid")]
        public async Task<Result<WfProjectListFieldSetDto>> ProjectListFieldSetById(long id)
        {
            WfProjectListFieldSet m = await wfProjectListFieldSetManager.QueryById(id);
            if (m != null)
            {
                return Result<WfProjectListFieldSetDto>.Success("查询成功。", mapper.Map<WfProjectListFieldSetDto>(m), 1);
            }
            else
            {
                return Result<WfProjectListFieldSetDto>.Fail("未查询到数据");
            }
        }

        /// <summary>
        /// 流程审批：保存审批预算清单配置（后台）
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("projectlistfieldsetsave")]
        public async Task<Result<WfProjectListFieldSetDto>> ProjectListFieldSetSave([FromBody] WfProjectListFieldSetDto o)
        {
            return await wfProjectListFieldSetManager.InsertUpdate(o);
        }

        /// <summary>
        /// 流程审批：根据模块Id获取项目清单Code集合
        /// </summary>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getfieldcodelistbymoduleid")]
        public async Task<Result> GetFieldCodeListByModuleId(long moduleId)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            var listProcessField = await processFieldManager.Query(f => f.ModuleId == moduleId && f.ControlType == "projectlist");
            if (listProcessField.Count > 0)
            {
                //List<string> listStrFieldCode = listProcessField.Select(f => f.FieldCode).Distinct().ToList();
                List<dropdownModel> listFieldCode = listProcessField.Select(f => new dropdownModel { value = f.FieldCode, label = f.FieldName }).ToList();
                r.data.rows = listFieldCode;
            }
            return r;
        }
        #endregion

        #region 前端节点数据源

        /// <summary>
        /// 流程审批：获取字典数据源信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getdatasource")]
        public async Task<Result> GetDataSource()
        {
            Result r = new Result();
            //获取所有审批字典表分类数据
            List<BDictionary> listDictionary = await dictionaryManager.Query(f => f.IsDeleted == false && f.TypeCode == "ZD000");
            List<dropdownModel> list = listDictionary.Select(f => new dropdownModel { value = $"{f.DicValue}_2", label = $"{f.DicName}({f.DicValue})" }).ToList();
            List<WfDictionary> listWfDictionary = await wfDictionaryManager.Query(f => f.IsDeleted == false && f.DicType == 1);
            List<dropdownModel> listF = listWfDictionary.Select(f => new dropdownModel { value = $"{f.Id}_3", label = $"{f.Name}" }).ToList();
            list.AddRange(listF);
            //获取联系人信息
            list.Add(new dropdownModel() { value = "80000_4", label = "联系人组件", pid = 4 });
            //项目年度
            list.Add(new dropdownModel() { value = "50000_5", label = "年度近三年", pid = 5 });
            list.Add(new dropdownModel() { value = "70000_7", label = "年度当年", pid = 7 });
            list.Add(new dropdownModel() { value = "80000_8", label = "年度下一年", pid = 8 });
            list.Add(new dropdownModel() { value = "90000_9", label = "单位名称", pid = 9 });

            //处理资金来源组件
            List<WfProcess> listProcess = await processManager.Query(f => f.IsOpen == 1 && f.Statuz == 1);
            foreach(WfProcess process in listProcess)
            {
                list.Add(new dropdownModel() { value = $"{process.Id}_6", label = $"资金来源组件_{process.ProcessName}", pid = 6 });
            }

            list.Add(new dropdownModel() { value = "100000_40", label = "指定审核人组件", pid = 10 });

            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = list;
            return r;
        }

        /// <summary>
        /// 流程审批：根据类型编码获取字典值数据信息
        /// </summary>
        /// <param name="code">编码</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getdatasourcebydicvalue")]
        public async Task<Result> GetDataSourceByDicValue(string code)
        {
            Result r = new Result();
            if (code.Contains("_"))
            {
                int dicType = 0;
                string[] strs= code.Split('_');
                string typeCode = strs[0];
                int.TryParse(strs[1], out dicType);

                switch (dicType)
                {
                    case 2:
                        List<BDictionary> listDictionary = await dictionaryManager.Query(f => f.IsDeleted == false && f.TypeCode == typeCode && f.Statuz == 1);
                        List<dropdownModel> listDic = listDictionary.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName, pid = 0, pname = f.TypeName, desp = f.Memo }).ToList();
                        r.data.rows = listDic;
                        break;
                    case 3:
                        List<WfDictionary> listWfDictionary = await wfDictionaryManager.Query(f => f.IsDeleted == false && f.ClassifyId.ToString() == typeCode && f.Statuz == 1);
                        List<dropdownModel> listTree = listWfDictionary.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name, pid = f.Depth == 1 ? 0 : f.Pid }).ToList();
                        var treeData = BuildDropDownModelTree(listTree);
                        r.data.rows = treeData;
                        break;
                    case 4:

                        break;
                    case 5:
                        List<dropdownModel> listYear = new List<dropdownModel> {
                                new dropdownModel {label = (DateTime.Now.Year - 1).ToString(),value = (DateTime.Now.Year - 1).ToString() },
                                new dropdownModel {label = (DateTime.Now.Year).ToString(),value = (DateTime.Now.Year).ToString() },
                                new dropdownModel {label = (DateTime.Now.Year + 1).ToString(),value = (DateTime.Now.Year + 1).ToString() }
                            };
                        r.data.rows = listYear;
                        break;
                    //资金来源组件
                    case 6:
                        //获取流程Id
                        long processId = 0;
                        long.TryParse(strs[0], out processId);
                        //List<WfPageColumnConfig> listPageColumnConfig = await pageColumnConfigManager.Query(f => f.ModeType == 3 && f.ProcessId == processId && f.ListFieldType == 1 && f.Statuz == 1 && !string.IsNullOrEmpty(f.FieldCode) && f.FieldType != 5);
                        List<WfPageColumnConfigDto> listPageColumnConfig = await pageColumnConfigManager.GetFundSourceColumn(processId);
                        r.data.rows = listPageColumnConfig;
                        break;
                    case 7:
                        List<dropdownModel> listCurrentYear = new List<dropdownModel> {
                                new dropdownModel {label = (DateTime.Now.Year).ToString(),value = (DateTime.Now.Year).ToString()}
                            };
                        r.data.rows = listCurrentYear;
                        break;
                    case 8:
                        List<dropdownModel> listNextYear = new List<dropdownModel> {
                                new dropdownModel {label = (DateTime.Now.Year + 1).ToString(),value = (DateTime.Now.Year + 1).ToString()}
                            };
                        r.data.rows = listNextYear;
                        break;
                    case 9:

                        break;
                }

                r.flag = 1;
                r.msg = "查询成功";
            }
            return r;
        }

        /// <summary>
        /// 根据参数查询页面显示数据
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("loaddatasource")]
        public async Task<Result> LoadDataSource([FromBody] DropListParam o)
        {
            Result r = new Result();
            WfProcessNode objNode = await processNodeManager.QueryById(o.ProcessNodeId);
            if (objNode != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = objNode.NodeConfig;

                //查询是否有下拉框配置值
                Dictionary<string, object> listOther = new Dictionary<string, object>();
                List<WfProcessField> listField = await processFieldManager.Query(f => f.ProcessNodeId == o.ProcessNodeId && f.IsDeleted == false && f.TypeBox > 0);
                foreach (WfProcessField field in listField)
                {
                    switch (field.TypeBox)
                    {
                        case 1:
                            List<BDictionary> listDictionary = await dictionaryManager.Query(f => f.IsDeleted == false && f.TypeCode == field.TypeCode.ToString() && f.Statuz == 1);
                            List<dropdownModel> listDic = listDictionary.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName, pid = 0, pname = f.TypeName, desp = f.Memo }).ToList();
                            listOther.Add(field.FieldId, listDic);
                            break;
                        case 2:
                            List<WfDictionary> listWfDictionary = await wfDictionaryManager.Query(f => f.IsDeleted == false && f.ClassifyId.ToString() == field.TypeCode.ToString() && f.Statuz == 1);
                            List<dropdownModel> listTree = listWfDictionary.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name, pid = f.Depth == 1 ? 0 : f.Pid }).ToList();
                            var treeData = BuildDropDownModelTree(listTree);
                            listOther.Add(field.FieldId, treeData);
                            break;
                        case 3:
                            if (o.ProjectDeclarationId == 0)
                            {
                                List<SysUserExtension> listUserExtension = await sysUserExtensionManager.Query(f => f.IsDeleted == false && f.UnitId == user.UnitId);
                                List<dropdownModel> listUser = listUserExtension.Select(f => new dropdownModel { value = f.UserId.ToString(), label = $"{f.Name}({f.Mobile})" }).ToList();
                                listOther.Add(field.FieldId, listUser);
                            }
                            break;
                        case 4:
                            List<dropdownModel> listYear = new List<dropdownModel> {
                                new dropdownModel {label = (DateTime.Now.Year - 1).ToString(),value = (DateTime.Now.Year - 1).ToString() },
                                new dropdownModel {label = (DateTime.Now.Year).ToString(),value = (DateTime.Now.Year).ToString() },
                                new dropdownModel {label = (DateTime.Now.Year + 1).ToString(),value = (DateTime.Now.Year + 1).ToString() }
                            };
                            listOther.Add(field.FieldId, listYear);
                            break;
                    }
                }

                r.data.other = listOther;

            }
            return r;
        }

        #endregion

        #region 设置资金来源

        /// <summary>
        /// 流程审批：查询资金来源配置列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("sourcefundsetlist")]
        public async Task<Result<PageModel<WfSourceFundSetDto>>> FindSourceFundSetList([FromBody] WfSourceFundSetParam param)
        {
            PageModel<WfSourceFundSet> data = await sourceFundSetManager.GetPaged(param);
            var pgSetData = data.ConvertTo<WfSourceFundSetDto>(mapper);
            foreach(var item in pgSetData.data)
            {
                //1：文本框  2：数字框 3：下拉框 4：多行文本框 5：日期
                switch (item.TypeStyle)
                {
                    case 2:
                        item.StrTypeStyle = "数字框";
                        break;
                    case 3:
                        item.StrTypeStyle = "下拉框";
                        break;
                    case 4:
                        item.StrTypeStyle = "多行文本框";
                        break;
                    case 5:
                        item.StrTypeStyle = "日期";
                        break;
                    default:
                        item.StrTypeStyle = "文本框";
                        break;
                }
            }

            List<dropdownModel> listTypeStyle = new List<dropdownModel>();
            listTypeStyle.Add(new dropdownModel() { value = "1", label = "文本框" });
            listTypeStyle.Add(new dropdownModel() { value = "2", label = "数字框" });
            listTypeStyle.Add(new dropdownModel() { value = "3", label = "下拉框" });
            listTypeStyle.Add(new dropdownModel() { value = "4", label = "多行文本框" });
            listTypeStyle.Add(new dropdownModel() { value = "5", label = "日期" });

            List<dropdownModel> listTitleStyle = new List<dropdownModel>();
            listTitleStyle.Add(new dropdownModel() { value = "left", label = "居左" });
            listTitleStyle.Add(new dropdownModel() { value = "center", label = "居中" });
            listTitleStyle.Add(new dropdownModel() { value = "right", label = "居右" });


            var listUseYesNo = EnumExtensions.EnumToList<YesNoEnum>();
            List<dropdownModel> listYesNo = listUseYesNo.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

            return baseSucc(pgSetData, data.dataCount, "查询成功", new { listTypeStyle = listTypeStyle, listTitleStyle = listTitleStyle, listYesNo = listYesNo });
        }

        /// <summary>
        /// 流程审批：根据Id查询资金来源配置信息
        /// </summary>
        /// <param name="id">资金来源配置Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("sourcefundbyid")]
        public async Task<Result<WfSourceFundSetDto>> SourceFundById(long id)
        {
            WfSourceFundSet m = await sourceFundSetManager.QueryById(id);
            if (m != null)
            {
                return Result<WfSourceFundSetDto>.Success("查询成功。", mapper.Map<WfSourceFundSetDto>(m), 1);
            }
            else
            {
                return Result<WfSourceFundSetDto>.Fail("未查询到数据");
            }
        }

        /// <summary>
        /// 流程审批：保存资金来源配置信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("sourcefundsetsave")]
        public async Task<Result<WfSourceFundSetDto>> SourceFundSetSave([FromBody] WfSourceFundSetDto o)
        {
            return await sourceFundSetManager.InsertUpdate(o);
        }

        /// <summary>
        /// 流程审批：获取字段选择列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("fieldselectlist")]
        public async Task<Result<PageModel<WfProcessFieldDto>>> FieldSelectList([FromBody] WfProcessFieldParam param)
        {
            PageModel<WfProcessField> data = await processFieldManager.GetSelectPage(param);
            return baseSucc(data.ConvertTo<WfProcessFieldDto>(mapper), data.dataCount);
        }

        /// <summary>
        /// 流程审批：获取资金来源显示列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("sourcefundlist")]
        public async Task<Result<PageModel<WfSourceFundDto>>> FindSourceFundList([FromBody] WfSourceFundParam param)
        {
            PageModel<WfSourceFundDto> data = await sourceFundManager.GetPaged(param);
            if (param.isFirst)
            {
                long processId = 0;
                if (param.ProcessId > 0)
                {
                    processId = param.ProcessId;
                }
                //获取显示列
                var listColumn = await sourceFundSetManager.Query(f => f.ProcessId == processId && f.IsShow == 1);
                var listSearch = listColumn.Where(f => f.SearchIsShow == 0).OrderBy(f => f.SearchSortValue).ToList();
                return baseSucc(data, data.dataCount, "查询成功", new { listColumn = listColumn, listSearch = listSearch });
            }
            else
            {
                return baseSucc(data, data.dataCount);
            }
        }
        #endregion

        #region 查询统计配置
        /// <summary>
        /// 流程管理：获取查询统计配置列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findpagedefinitionlist")]
        public async Task<Result<PageModel<WfPageDefinitionDto>>> FindPageDefinitionList([FromBody] WfPageDefinitionParam param)
        {
            PageModel<WfPageDefinition> data = await pageDefinitionManager.GetPaged(param);
            if (param.isFirst)
            {
                //获取模块信息
                var listUseModule = await moduleManager.Query(f => f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listModuleId = listUseModule.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                //查询类型
                var listUseSearchType = EnumExtensions.EnumToList<WfSearchType>();
                List<dropdownModel> listSearchType = listUseSearchType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //页面类型
                var listUsePageType = EnumExtensions.EnumToList<WfPageType>();
                List<dropdownModel> listPageType = listUsePageType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //使用单位类型
                var listUseUnittype = EnumExtensions.EnumToList<WfUnittype>();
                List<dropdownModel> listUnittype = listUseUnittype.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //适用范围
                var listUseUseType = EnumExtensions.EnumToList<WfUseType>();
                List<dropdownModel> listUseType = listUseUseType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //数据源
                var listUseSourceData = EnumExtensions.EnumToList<WfSourceData>();
                List<dropdownModel> listSourceData = listUseSourceData.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //获取流程信息
                var listUseProcess = await processManager.Query(f => f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listProcessIds = listUseProcess.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.ProcessName }).ToList();

                //使用单位
                var listUseUnit = await unitManager.Query(f => f.IsDeleted == false && f.Statuz == 1 && (f.UnitType == UnitTypeEnum.City.ObjToInt() || f.UnitType == UnitTypeEnum.County.ObjToInt() || f.UnitType == UnitTypeEnum.School.ObjToInt()));
                List<dropdownModel> listUseUnitId = listUseUnit.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                //总计显示位置
                var listUseTotalColumn = await pageColumnConfigManager.Query(f => f.IsDeleted == false && f.ModeType == 1 && f.FieldType != 5);
                List<dropdownModel> listTotalCounmn = listUseTotalColumn.GroupBy(f=>new { f.FieldCode,f.FieldName}) .Select(f => new dropdownModel { value = f.Key.FieldCode.ToString(), label = $"{f.Key.FieldName}({f.Key.FieldCode})" }).ToList();

                //使用单位字段名称
                List<dropdownModel> listUseUnitField = new List<dropdownModel>();
                listUseUnitField.Add(new dropdownModel() { label = "单位Id(SchoolId)", value = "SchoolId" });
                listUseUnitField.Add(new dropdownModel() { label = "区县Id(CountyId)", value = "CountyId" });
                listUseUnitField.Add(new dropdownModel() { label = "市级Id(CityId)", value = "CityId" });

                //使用用户字段名称

                List<dropdownModel> listUseUserField = new List<dropdownModel>();
                listUseUserField.Add(new dropdownModel() { label = "用户Id(UserId)", value = "UserId" });

                //排序规则
                var listUseSortType = EnumExtensions.EnumToList<WfSortType>();
                List<dropdownModel> listSortType = listUseSortType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //是否为子页面
                var listUseYesNo = EnumExtensions.EnumToList<YesNoEnum>();
                List<dropdownModel> listIsSubpage = listUseYesNo.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();


                return baseSucc(data.ConvertTo<WfPageDefinitionDto>(mapper), data.dataCount, "查询成功", new
                {
                    listModuleId = listModuleId,
                    listSearchType = listSearchType,
                    listPageType = listPageType,
                    listUnittype = listUnittype,
                    listUseType = listUseType,
                    listSourceData = listSourceData,
                    listProcessIds = listProcessIds,
                    listUseUnitId = listUseUnitId,
                    listTotalCounmn = listTotalCounmn,
                    listDefaultSort = listTotalCounmn,
                    listUseUnitField = listUseUnitField,
                    listUseUserField = listUseUserField,
                    listSortType = listSortType,
                    listIsSubpage = listIsSubpage,
                    listIsNavigation = listUseYesNo
                });
            }
            else
            {
                return baseSucc(data.ConvertTo<WfPageDefinitionDto>(mapper), data.dataCount);
            }
        }

        /// <summary>
        /// 流程管理：根据Id查询统计页面表配置信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getpagedefinitionbyid")]
        public async Task<Result> GetPageDefinitionById(long id)
        {
            Result r = new Result();
            WfPageDefinition pageDefinition = await pageDefinitionManager.QueryById(id);
            if (pageDefinition != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = pageDefinition;

                //获取模块信息
                var listUseModule = await moduleManager.Query(f => f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listModuleId = listUseModule.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                //查询类型
                var listUseSearchType = EnumExtensions.EnumToList<WfSearchType>();
                List<dropdownModel> listSearchType = listUseSearchType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //页面类型
                var listUsePageType = EnumExtensions.EnumToList<WfPageType>();
                List<dropdownModel> listPageType = listUsePageType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //使用单位类型
                var listUseUnittype = EnumExtensions.EnumToList<WfUnittype>();
                List<dropdownModel> listUnittype = listUseUnittype.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //适用范围
                var listUseUseType = EnumExtensions.EnumToList<WfUseType>();
                List<dropdownModel> listUseType = listUseUseType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //数据源
                var listUseSourceData = EnumExtensions.EnumToList<WfSourceData>();
                List<dropdownModel> listSourceData = listUseSourceData.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //获取流程信息
                var listUseProcess = await processManager.Query(f => f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listProcessIds = listUseProcess.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.ProcessName }).ToList();

                //获取使用单位
                int unitType = 0;
                PUnit unit = await unitManager.QueryById((object)pageDefinition.UseUnitId);
                if(unit != null && unit.UnitType == 3)
                {
                    unitType = 1;
                }
                var expression = LinqExtensions.True<PUnit>();
                if(unitType == 0)
                {
                    expression = expression.AndNew(f =>f.IsDeleted == false && f.Statuz == 1 && (f.UnitType == UnitTypeEnum.City.ObjToInt() || f.UnitType == UnitTypeEnum.County.ObjToInt()));
                }
                else
                {
                    expression = expression.AndNew(f => f.IsDeleted == false && f.Statuz == 1 && f.UnitType == UnitTypeEnum.School.ObjToInt());
                }

              
                //使用单位
                var listUseUnit = await unitManager.Query(f => f.IsDeleted == false && f.Statuz == 1 && (f.UnitType == UnitTypeEnum.City.ObjToInt() || f.UnitType == UnitTypeEnum.County.ObjToInt()));
                List<dropdownModel> listUseUnitId = listUseUnit.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                //总计显示位置
                var listUseTotalColumn = await pageColumnConfigManager.Query(f => f.IsDeleted == false && f.ModeType == 1 && f.FieldType != 5);
                List<dropdownModel> listTotalCounmn = listUseTotalColumn.Select(f => new dropdownModel { value = f.FieldCode.ToString(), label = $"{f.FieldName}({f.FieldCode})" }).ToList();

                //使用单位字段名称
                List<dropdownModel> listUseUnitField = new List<dropdownModel>();
                listUseUnitField.Add(new dropdownModel() { label = "单位Id(SchoolId)", value = "SchoolId" });
                listUseUnitField.Add(new dropdownModel() { label = "区县Id(CountyId)", value = "CountyId" });
                listUseUnitField.Add(new dropdownModel() { label = "市级Id(CityId)", value = "CityId" });

                //使用用户字段名称

                List<dropdownModel> listUseUserField = new List<dropdownModel>();
                listUseUserField.Add(new dropdownModel() { label = "用户填报Id(FillUserId)", value = "FillUserId" });
                listUseUserField.Add(new dropdownModel() { label = "用户审核Id(AuditUserId)", value = "AuditUserId" });

                //排序规则
                var listUseSortType = EnumExtensions.EnumToList<WfSortType>();
                List<dropdownModel> listSortType = listUseSortType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //是否为子页面
                var listUseYesNo = EnumExtensions.EnumToList<YesNoEnum>();
                List<dropdownModel> listIsSubpage = listUseYesNo.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();


                r.data.other = new
                {
                    listModuleId = listModuleId,
                    listSearchType = listSearchType,
                    listPageType = listPageType,
                    listUnittype = listUnittype,
                    listUseType = listUseType,
                    listSourceData = listSourceData,
                    listProcessIds = listProcessIds,
                    listUseUnitId = listUseUnitId,
                    listTotalCounmn = listTotalCounmn,
                    listDefaultSort = listTotalCounmn,
                    listUseUnitField = listUseUnitField,
                    listUseUserField = listUseUserField,
                    listSortType = listSortType,
                    listIsSubpage = listIsSubpage,
                    listIsNavigation = listUseYesNo
                };
            }
            return r;
        }

        /// <summary>
        /// 流程管理：根据Id删除查询统计配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delpagedefinitionbyid")]
        public async Task<Result<string>> DelPageDefinitionById(long id)
        {
            return await pageDefinitionManager.DeleteById(id);
        }

        /// <summary>
        /// 流程管理：保存查询统计页面
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("pagedefinitionsave")]
        public async Task<Result<WfPageDefinitionDto>> PageDefinitionSave([FromBody] WfPageDefinitionDto o)
        {
            return await pageDefinitionManager.InsertUpdate(o);
        }


        /// <summary>
        /// 流程管理：禁用启用及提交处理
        /// </summary>
        /// <param name="id">查询统计页面表Id</param>
        /// <param name="operateType">1：处理禁用启用,2：处理提交</param>
        /// <returns></returns>
        [HttpGet]
        [Route("pagedefinitionstatuzset")]
        public async Task<Result<string>> PageDefinitionStatuzSet(long id,int operateType)
        {
            WfPageDefinition obj = await pageDefinitionManager.QueryById(id);
            string tipMs = "提交";
            if(obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if(operateType == 1)
            {
                if(obj.Statuz == 1)
                {
                    tipMs = "禁用";
                    obj.Statuz = 2;
                }
                else if(obj.Statuz == 2)
                {
                    tipMs = "启用";
                    obj.Statuz = 1;
                }
            }
            else if(operateType == 2)
            {
                obj.Statuz = 1;
            }

            await pageDefinitionManager.Update(obj);
            return Result<string>.Success($"{tipMs}成功");
        }


        /// <summary>
        /// 流程管理节点列表设置：获取显示列列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findpagecolumnconfiglist")]
        public async Task<Result<PageModel<WfPageColumnConfigDto>>> FindPageColumnConfigList([FromBody] WfPageColumnConfigParam param)
        {

            PageModel<WfPageColumnConfigDto> pgData = await pageColumnConfigManager.GetPaged(param);
            if (param.isFirst)
            {
                //数据库类型
                var listUseFieldType = EnumExtensions.EnumToList<WfMenuType>();
                List<dropdownModel> listFieldType = listUseFieldType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //所属菜单
                var listUseMenuType = EnumExtensions.EnumToList<WfDataType>();
                List<dropdownModel> listMenuType = listUseMenuType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //对齐方式
                List<dropdownModel> listTitleStyle = new List<dropdownModel>() { };
                listTitleStyle.Add(new dropdownModel() { value = "center", label = "center" });
                listTitleStyle.Add(new dropdownModel() { value = "left", label = "left" });
                listTitleStyle.Add(new dropdownModel() { value = "right", label = "right" });

                return baseSucc(pgData, pgData.dataCount, "查询成功", new
                {
                    listUseMenuType = listUseMenuType,
                    listFieldType = listFieldType,
                    listTitleStyle = listTitleStyle,
                });
            }
            else
            {
                return baseSucc(pgData, pgData.dataCount);
            }
        }


        /// <summary>
        /// 流程管理节点列表设置：获取选择列列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findchoosecolumnpagelist")]
        public async Task<Result<PageModel<WfProcessFieldDto>>> FindChooseColumnPageList([FromBody] WfProcessFieldParam param)
        {
            var pgData = await processFieldManager.GetChooseColumnPage(param);
            if (param.isFirst)
            {
                //获取流程节点名称
                var listProcessNode = await processNodeManager.Query(f => f.ModuleId == param.ModuleId && f.IsDeleted == false);
                List<dropdownModel> listNode = listProcessNode.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.NodeName }).ToList();
                //listNode.Insert(0, new dropdownModel { value = "0", label = "节点名称" });

                return baseSucc(pgData, pgData.dataCount, "查询成功", new
                {
                    listNode = listNode
                });
            }
            else
            {
                return baseSucc(pgData, pgData.dataCount);
            }
        }


        /// <summary>
        /// 流程管理节点列表设置：选择列确认
        /// </summary>
        /// <param name="choseColumn">
        /// Id必传
        /// ModuleId必传
        /// ModeType必传：查询统计的传1；节点列设置传2
        /// MenuType必传：查询统计可以不传，用于节点，待处理传1，已处理传2
        /// FieldCode必传
        /// PageDefinitionId(查询统计必传)：节点列设置不需要传
        /// ProcessNodeId(节点列必传)：查询统计列设置不需要传
        /// ConfigType(查询统计必传)
        /// ListFieldType：（1：列  2：查询）
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("choosecolumnsave")]
        public async Task<Result<string>> ChooseColumnSave([FromBody] ChoseColumnModel choseColumn)
        {
            return await processFieldManager.SaveColumn(choseColumn);
        }

        /// <summary>
        /// 流程管理节点列表设置：列启用禁用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("listcolumnset")]
        public async Task<Result<string>> ListColumnSet(long id)
        {
            var obj = await pageColumnConfigManager.QueryById(id);
            if (obj != null)
            {
                string str = "禁用成功";
                if(obj.Statuz == 1)
                {
                    obj.Statuz = 0;
                }
                else
                {
                    obj.Statuz = 1;
                    str = "启用成功";
                }

                await pageColumnConfigManager.Update(obj);
                return Result<string>.Success(str);
            }
            else
            {
                return Result<string>.Fail("设置对象不存在!");
            }
        }

        /// <summary>
        /// 流程管理节点列表设置：根据Id获取列信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("listcolumngetbyid")]
        public async Task<Result> ListColumnGetById(long id)
        {
            Result r = new Result();
            var obj = await pageColumnConfigManager.QueryById(id);
            if (obj != null)
            {
                //数据库类型
                var listUseFieldType = EnumExtensions.EnumToList<WfMenuType>();
                List<dropdownModel> listFieldType = listUseFieldType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //所属菜单
                var listUseMenuType = EnumExtensions.EnumToList<WfDataType>();
                List<dropdownModel> listMenuType = listUseMenuType.Select(f => new dropdownModel { value = $"{f.Value.ToString()}", label = f.Description }).ToList();

                //对齐方式
                List<dropdownModel> listTitleStyle = new List<dropdownModel>() { };
                listTitleStyle.Add(new dropdownModel() { value = "center", label = "center" });
                listTitleStyle.Add(new dropdownModel() { value = "left", label = "left" });
                listTitleStyle.Add(new dropdownModel() { value = "right", label = "right" });

                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<WfPageColumnConfigDto>(obj);
                r.data.other = new
                {
                    listUseMenuType = listUseMenuType,
                    listFieldType = listFieldType,
                    listTitleStyle = listTitleStyle,
                };
            }
            return r;
        }

        /// <summary>
        /// 流程管理节点列表设置：新增修改列数据信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("listcolumninsertupdate")]
        public async Task<Result<string>> ListColumnInsertUpdate([FromBody] WfPageColumnConfigDto o)
        {
            List<string> listCode = new List<string>() { "edit", "detail", "examine", "del", "cancel" };
            WfPageColumnConfig obj = mapper.Map<WfPageColumnConfig>(o);
            if (o.Id > 0)
            {
                WfPageColumnConfig columnConfig = await pageColumnConfigManager.QueryById(o.Id);
                if (columnConfig != null)
                {
                    if(columnConfig.FieldType == 5)
                    {
                        if (!listCode.Contains(obj.FieldCode))
                        {
                            return Result<string>.Fail($"Code编码必须为“{string.Join(",", listCode)}”中的值");
                        }

                        columnConfig.FieldCode = obj.FieldCode;
                        columnConfig.MenuType = o.MenuType;
                        columnConfig.FieldName = o.FieldName;
                        columnConfig.CalculateFun = o.CalculateFun;
                        columnConfig.FieldDescription = o.FieldDescription;
                        columnConfig.Sort = o.Sort;
                        columnConfig.DateDisplay = o.DateDisplay;
                        columnConfig.ColumnFieldType = 5;
                        columnConfig.IsDateRange = o.IsDateRange;
                    }
                    else
                    {
                        if(o.FieldType == 5)
                        {
                            return Result<string>.Fail("不能改为列类型为按钮!");
                        }


                        columnConfig.MenuType= o.MenuType;
                        columnConfig.FieldType = o.FieldType;
                        columnConfig.FieldName = o.FieldName;
                        columnConfig.Width = o.Width;
                        columnConfig.ContentStyle = o.ContentStyle;
                        columnConfig.Sort = o.Sort;
                        columnConfig.IsNeedSum = o.IsNeedSum;
                        columnConfig.ListFieldType = o.ListFieldType;
                        columnConfig.ConditionWord = o.ConditionWord;
                        columnConfig.StatisticalMethod = o.StatisticalMethod;
                        columnConfig.DateDisplay = o.DateDisplay;
                        columnConfig.ColumnFieldType = o.ColumnFieldType;
                        columnConfig.IsDateRange = o.IsDateRange;
                    }
                    await pageColumnConfigManager.Update(columnConfig);

                    return Result<string>.Success("保存成功!");
                }
                else
                {
                    return Result<string>.Fail("查询的数据不存在!");
                }

            }
            else
            {

                if (!listCode.Contains(obj.FieldCode))
                {
                    return Result<string>.Fail($"Code编码必须为“{string.Join(",", listCode)}”中的值");
                }

                //添加只能添加按钮
                await pageColumnConfigManager.Add(new WfPageColumnConfig()
                {
                    Id = BaseDBConfig.GetYitterId(),
                    FieldType = 5,
                    ModuleId = obj.ModuleId,
                    ModeType = obj.ModeType,
                    MenuType = obj.MenuType,
                    PageDefinitionId = obj.PageDefinitionId,
                    ProcessNodeId = obj.ProcessNodeId,
                    FieldName = obj.FieldName,
                    FieldDescription = obj.FieldDescription,
                    FieldCode = obj.FieldCode,
                    TypeBox = 20,
                    Sort = obj.Sort,
                    Width = 100,
                    ContentStyle = "center",
                    JsFormat = "",
                    ConfigType = 1,
                    Statuz = 1,
                    TypeCode = 0,
                    IsNeedSum = 0,
                    CalculateFun = obj.CalculateFun,
                    ListFieldType = 1,
                    ConditionWord = "",
                    StatisticalMethod = 0,
                    DateDisplay = 0,
                    ColumnFieldType = 5
                });
                return Result<string>.Success("添加成功!");
            }
        }

        /// <summary>
        /// 流程管理节点列表设置：批量修改列数据信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("listcolumnbatchinsertupdate")]
        public async Task<Result<string>> ListColumnBatchUpdate([FromBody] List<WfPageColumnConfigDto> o)
        {
            return await processFieldManager.BatchUpdateListEdit(o);
        }


        /// <summary>
        /// 流程管理：根据页面列配置表Id获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getpagecolumnconfigbyid")]
        public async Task<Result> GetPageColumnConfigById(long id)
        {
            Result r = new Result();
            WfPageColumnConfig pageColumnConfig = await pageColumnConfigManager.QueryById(id);
            if (pageColumnConfig != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = pageColumnConfig;

                //绑定的功能
                var listUseOwnType = EnumExtensions.EnumToList<WfBindFuc>();
                List<dropdownModel> listOwnType = listUseOwnType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //数据库类型
                var listUseFieldType = EnumExtensions.EnumToList<WfDataType>();
                List<dropdownModel> listFieldType = listUseFieldType.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //对齐方式
                List<dropdownModel> listTitleStyle = new List<dropdownModel>() { };
                listTitleStyle.Add(new dropdownModel() { value = "center", label = "center" });
                listTitleStyle.Add(new dropdownModel() { value = "left", label = "left" });
                listTitleStyle.Add(new dropdownModel() { value = "right", label = "right" });

                //输入框类型
                var listUseTypeBox = EnumExtensions.EnumToList<WfTypeBox>();
                List<dropdownModel> listTypeBox = listUseTypeBox.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                r.data.other = new
                {
                    listOwnType = listOwnType,
                    listFieldType = listFieldType,
                    listTitleStyle = listTitleStyle,
                    listTypeBox = listTypeBox,
                };
            }
            return r;
        }

        /// <summary>
        /// 流程管理：新增修改查询统计页面
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("pagecolumnconfigsave")]
        public async Task<Result<WfPageColumnConfigDto>> PageColumnConfigSave([FromBody] WfPageColumnConfigDto o)
        {
            return await pageColumnConfigManager.InsertUpdate(o);
        }

        /// <summary>
        /// 流程管理：根据Id设置是否需要
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("setpagecolumnconfigbyid")]
        public async Task<Result<string>> SetPageColumnConfigById(long id)
        {
            var obj = await pageColumnConfigManager.QueryById(id);
            if (obj != null)
            {
                await pageColumnConfigManager.Update(obj);
                return Result<string>.Success("设置成功");
            }
            else
            {
                return Result<string>.Fail("未查询到数据,设置失败");
            }
        }

        /// <summary>
        ///  流程管理：同步字段配置设置值
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("pagecolumnconfigsync")]
        public async Task<Result<string>> PageColumnConfigSync([FromBody] WfSyncDataModel o)
        {
            return await pageColumnConfigManager.DataSync(o);
        }

        /// <summary>
        /// 流程管理：项目库字段管理选择列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findchoosefundpagelist")]
        public async Task<Result<PageModel<WfFundFieldSet>>> FindChooseFundPageList([FromBody] WfFundFieldSetParam param)
        {
            var pgData = await fundFieldSetManager.QueryPage(f => f.IsDeleted == false, param.pageIndex, param.pageSize, "Id ASC");
            return baseSucc(pgData, pgData.dataCount);
        }

        /// <summary>
        /// 流程管理：项目库字段管理字段选择确认
        /// </summary>
        /// <param name="choseColumn"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("choosefundsave")]
        public async Task<Result<string>> ChooseFundSave([FromBody] ChoseColumnModel choseColumn)
        {
            return await processFieldManager.SaveFund(choseColumn);
        }

        /// <summary>
        /// 流程管理：项目库字段绑定
        /// </summary>
        /// <param name="pageColumnConfigId"></param>
        /// <param name="processFieldId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("setsourcefundfield")]
        public async Task<Result<string>> SetSourceFundField(long pageColumnConfigId,long processFieldId)
        {
            return await processFieldManager.SaveSourceFundField(pageColumnConfigId, processFieldId);
        }

        /// <summary>
        /// 流程管理：修改流程资金来源信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("sourcefundinputdataset")]
        public async Task<Result<string>> SourceFundInputDataSet([FromBody] SourceFundInputModel o)
        {
            return await processManager.SourceFundInputDataSet(o);
        }


        /// <summary>
        /// 流程管理：根据查询统计页面表Id获取审批预算清单选择的列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findprojectcolumnpagelist")]
        public async Task<Result<PageModel<WfProjectListFieldSet>>> FindProjectColumnPageList([FromBody] WfProcessFieldParam param)
        {
            PageModel<WfProjectListFieldSet> pg = new PageModel<WfProjectListFieldSet>();
            var obj =await pageDefinitionManager.QueryById(param.PageDefinitionId);
            if (obj != null)
            {
                List<long> listProcessId = obj.ProcessIds.Split(',').Select(long.Parse).ToList();
                var expression = LinqExtensions.True<WfProjectListFieldSet>();
                expression = expression.AndNew(f => listProcessId.Contains(f.ProcessId) && f.FieldCode == obj.FieldCode && f.ConfigType == 1 && f.IsShow == 1 && f.IsDeleted == false);
                if (!string.IsNullOrEmpty(param.Key))
                {
                    expression = expression.AndNew(f => f.Title.Contains(param.Key) || f.FieldValue.Contains(param.Key));
                }
                pg = await wfProjectListFieldSetManager.QueryPage(expression, param.pageIndex, param.pageSize, "Sort ASC");
            }
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 流程管理：项目清单查询统计选择清单列功能
        /// 传参：ModuleId 必传,PageDefinitionId 必传,ListColumn:[{Id:11,FieldCode:"aa"},{Id:22,FieldCode:"ff"}]
        /// </summary>
        /// <param name="choseColumn"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("chooseprojectlistcolumnsave")]
        public async Task<Result<string>> ChooseProjectListColumnSave([FromBody] ChoseColumnModel choseColumn)
        {
            return await processFieldManager.SaveProjectListColumn(choseColumn);
        }
        #endregion

        #region 退回方式设置
        /// <summary>
        /// 流程管理：点击节点显示退回方式数据
        /// </summary>
        /// <param name="processId"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getprocessreturnset")]
        public async Task<Result> GetProcessReturnSet(long processId,long processNodeId)
        {
            Result r = new Result();
            //先根据流程Id、节点Id获取数据
            WfProcess objProcess = await processManager.QueryById(processId);
            if (objProcess != null)
            {
                r.flag = 1;
                List<WfProcessReturnSet> list = await processReturnSetManager.Query(f => f.ProcessId == processId && f.ProcessNodeId == processNodeId);
                if (list.Count == 0)
                {
                    WfProcessReturnSet returnSet = new WfProcessReturnSet();
                    returnSet.Id = BaseDBConfig.GetYitterId();
                    returnSet.ModuleId = objProcess.ModuleId;
                    returnSet.ProcessId = processId;
                    returnSet.ProcessNodeId = processNodeId;
                    returnSet.BackWay = 1;
                    returnSet.Sort = 1;
                    await processReturnSetManager.Add(returnSet);

                    r.data.rows = mapper.Map<WfProcessReturnSetDto>(returnSet);
                }
                else
                {
                    WfProcessReturnSet objSet = list.FirstOrDefault();
                    r.data.rows = mapper.Map<WfProcessReturnSetDto>(objSet);
                }

                //退回方式
                var listEnumBackWay = EnumExtensions.EnumToList<WfBackWay>();
                List<dropdownModel> listBackWay = listEnumBackWay.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                r.data.other = new { listBackWay = listBackWay };
            }
            return r;
        }

        /// <summary>
        /// 流程管理：保存退回方式
        /// </summary>
        /// <param name="o">
        /// 数据类型【{Id:13121212121,BackWay:3,Sort:5}】
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("setprocessreturn")]
        public async Task<Result<string>> SetProcessReturn([FromBody] WfProcessReturnSetDto o)
        {
            WfProcessReturnSet obj = await processReturnSetManager.QueryById(o.Id);
            if (obj != null)
            {
                obj.BackWay = o.BackWay;
                obj.Sort = o.Sort;
                await processReturnSetManager.Update(obj);
                return Result<string>.Success("保存成功!");
            }
            else
            {
                return Result<string>.Fail("数据存在!");
            }

        }
        #endregion

        #region 字段控制
        /// <summary>
        /// 流程管理：获取主控制列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postcontrollist")]
        public async Task<Result<PageModel<WfControlListDto>>> PostControlList([FromBody] WfControlDetailParam param)
        {
            PageModel<WfControlListDto> data = await controlDetailManager.GetPageList(param);
            return baseSucc(data, data.dataCount);
        }

        /// <summary>
        /// 流程管理：根据节点Id主控分组值获取设置控制列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postcontrolsetlist")]
        public async Task<Result> PostControlSetList([FromBody] WfControlDetailParam param)
        {
            return await controlDetailManager.GetControlSetList(param);
        }

        /// <summary>
        /// 流程管理：根据左侧点击获取右侧字段选中信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getsinglecontrolsetlist")]
        public async Task<Result> GetSingleControlSetList([FromBody] SingleControlModel model)
        {
            return await controlDetailManager.GetSingleSetList(model);
        }

        /// <summary>
        /// 流程管理：保存字段配置信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsavecontroldata")]
        public async Task<Result> PostSaveControlData([FromBody] ControlDetailModel model)
        {
            return await controlDetailManager.SaveControlData(model);
        }

        /// <summary>
        /// 流程管理：根据金额控制主表Id删除数据信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delamountcontrolbyid")]
        public async Task<Result<string>> DelAmountControlById(long id)
        {
            return await controlDetailManager.DelAmountControlData(id);
        }
        #endregion

        #region 分组管理设置

        /// <summary>
        /// 流程管理：分组查询列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getgroupsetlist")]
        public async Task<Result<PageModel<WfGroupProcessSetDto>>> GetGroupSetList([FromBody] WfGroupProcessSetParam param)
        {
            PageModel<WfGroupProcessSetDto> data = await groupSetManager.GetPaged(param);
            if (param.isFirst)
            {
                List<WfDictionary> listWfDictionary = await wfDictionaryManager.Query(f => f.IsDeleted == false && f.DicType == 1);
                List<dropdownModel> list = listWfDictionary.Select(f => new dropdownModel { value = $"{f.Id}", label = $"{f.Name}" }).ToList();
                return baseSucc(data, data.dataCount, "查询成功", new
                {
                    listDropDownList = list,
                });
            }
            else
            {
                return baseSucc(data, data.dataCount);
            }
        }

        /// <summary>
        /// 流程管理：添加修改分组信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("groupsetinsertupdate")]
        public async Task<Result<string>> GroupSetInsertUpdate([FromBody] WfGroupProcessSetModel o)
        {
            return await groupSetManager.InsertUpdate(o);
        }

        /// <summary>
        /// 流程管理：根据Id删除分组信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("groupsetdeletebyid")]
        public async Task<Result<string>> GroupSetDeleteById(long id)
        {
            return await groupSetManager.DeleteById(id);
        }

        /// <summary>
        /// 流程管理：单位分组设置列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getunitgroupsetlist")]
        public async Task<Result<PageModel<WfGroupUnitSetDto>>> GetUnitGroupSetList([FromBody] WfGroupUnitSetParam param)
        {
            if (param.isFirst)
            {
                //获取字典数据
                //List<BDictionary> listBasicDictionary = await dictionaryManager.Query(f => f.IsDeleted == false);
                //List<WfDictionary> listProcessDictionary = await wfDictionaryManager.Query(f => f.IsDeleted == false);
                //获取该单位下流程
                List<ProcessSetModel> listProcess = new List<ProcessSetModel>();
                List<WfProcess> listProcessAll = await processManager.Query(f => f.IsDeleted == false && (f.UseUnitId == user.UnitId || f.UseUnitId == user.UnitPId));
                List<WfGroupProcessSet> listProcessSet = await groupSetManager.Query(f => f.IsDeleted == false);
                foreach (WfProcess p in listProcessAll)
                {
                    var listSet = listProcessSet.Where(f => f.ProcessId == p.Id).ToList();
                    if (listSet.Count > 0)
                    {
                        ProcessSetModel porcessSetModel = new ProcessSetModel();
                        porcessSetModel.ProcessId = p.Id;
                        porcessSetModel.ProcessName = p.ProcessName;

                        List<ProcessGroupModel> listGroupModel = new List<ProcessGroupModel>();
                        foreach (WfGroupProcessSet pSet in listSet)
                        {
                            listGroupModel.Add(new ProcessGroupModel()
                            {
                                GroupId = pSet.TypeCode.Value,
                                GroupName = pSet.GroupName,
                                TypeBox = pSet.TypeBox
                            });
                        }
                        porcessSetModel.ListProcessGroup = listGroupModel;
                        listProcess.Add(porcessSetModel);
                    }
                }

                if (listProcess.Count > 0)
                {
                    param.ProcessId = listProcess[0].ProcessId;
                    param.GroupId = listProcess[0].ListProcessGroup[0].GroupId;
                    PageModel<WfGroupUnitSetDto> data = await groupSetManager.GetUnitGroupSetList(param);
                    return baseSucc(data, data.dataCount, "查询成功", new
                    {
                        listBasicGroupSet = listProcess
                    });
                }
                else
                {
                    PageModel<WfGroupUnitSetDto> data = new PageModel<WfGroupUnitSetDto>();
                    return baseSucc(data, data.dataCount);
                }
            }
            else
            {
                PageModel<WfGroupUnitSetDto> data = await groupSetManager.GetUnitGroupSetList(param);
                return baseSucc(data, data.dataCount);
            }
        }

        /// <summary>
        /// 流程管理：根据分组Id获取分组中每项的信息
        /// </summary>
        /// <param name="processId">流程Id</param>
        /// <param name="groupId">分类编码</param>
        /// <param name="typeBox">输入框类型（2:基础字典表，3：审批字典表）</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getgroupitemlist")]
        public async Task<Result> GetGroupItemList(long processId,long groupId,int typeBox)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            List<WfGroupProcessSet> listProcessSet = await groupSetManager.Query(f => f.ProcessId == processId && f.TypeCode == groupId && f.TypeBox == typeBox);
            if (listProcessSet.Count > 0)
            {
                WfGroupProcessSet obj = listProcessSet[0];
                List<dropdownModel> list = new List<dropdownModel>();
                if (obj.TypeBox == 2)
                {
                    List<BDictionary> listDictionary = await dictionaryManager.Query(f => f.IsDeleted == false && f.TypeCode == obj.TypeCode.ToString());
                    list = listDictionary.Select(f => new dropdownModel { value = $"{f.DicValue}", label = $"{f.DicName}({f.DicValue})", pid = 2 }).ToList();
                }
                else if (obj.TypeBox == 3)
                {
                    List<WfDictionary> listWfDictionary = await wfDictionaryManager.Query(f => f.IsDeleted == false && f.Pid == obj.TypeCode);
                    list = listWfDictionary.Select(f => new dropdownModel { value = $"{f.Id}", label = $"{f.Name}", pid = 3 }).ToList();
                }
                r.data.rows = list;
            }
            return r;
        }

        /// <summary>
        /// 流程管理：批量保存单位分组信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitgroupbatchset")]
        public async Task<Result<string>> UnitGroupBatchSet([FromBody] UnitGroupBatchModel o)
        {
            return await groupSetManager.BatchSetGroupUnit(o);
        }

        /// <summary>
        /// 流程管理：检测是否填写完成
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("groupitemcheck")]
        public async Task<Result> GroupItemCheck()
        {
            Result r = new Result();
            r.flag = 2;
            string msg = "";
            List<WfGroupUnitSet> listUnitSet = await groupUnitSetManager.Query(f => f.IsDeleted == false);
            //获取区县下所有单位信息
            int schoolCount = unitManager.Query(f => f.PId == user.UnitId && f.IsDeleted == false && f.Statuz == 1).Result.Count;
            List<ProcessSetModel> listProcess = new List<ProcessSetModel>();
            List<WfProcess> listProcessAll = await processManager.Query(f => f.IsDeleted == false && (f.UseUnitId == user.UnitId || f.UseUnitId == user.UnitPId));
            List<WfGroupProcessSet> listProcessSet = await groupSetManager.Query(f => f.IsDeleted == false);
            foreach (WfProcess p in listProcessAll)
            {
                var listSet = listProcessSet.Where(f => f.ProcessId == p.Id).ToList();
                if (listSet.Count > 0)
                {
                    ProcessSetModel porcessSetModel = new ProcessSetModel();
                    porcessSetModel.ProcessId = p.Id;
                    porcessSetModel.ProcessName = p.ProcessName;

                    List<ProcessGroupModel> listGroupModel = new List<ProcessGroupModel>();
                    foreach (WfGroupProcessSet pSet in listSet)
                    {
                        listGroupModel.Add(new ProcessGroupModel()
                        {
                            GroupId = pSet.TypeCode.Value,
                            GroupName = pSet.GroupName,
                            TypeBox = pSet.TypeBox
                        });
                    }
                    porcessSetModel.ListProcessGroup = listGroupModel;
                    listProcess.Add(porcessSetModel);
                }
            }

            if (listProcess.Count == 0)
            {
                r.flag = 0;
                r.msg = "无数据";
                return r;
            }
            //循环判断
            foreach(var p in listProcess)
            {
                foreach(var item in p.ListProcessGroup)
                {
                    int setCount = listUnitSet.Where(f => f.ProcessId == p.ProcessId && f.GroupId == item.GroupId && f.TypeBox == item.TypeBox).Count();
                    if(setCount != schoolCount)
                    {
                        msg += $"流程【“{p.ProcessName}”】-->分组【{item.GroupName}】未完成\n";
                    }
                }
            }
            if (string.IsNullOrEmpty(msg))
            {
                r.flag = 1;   
                msg = "已完成设置";
            }
            r.msg = msg;
            return r;
        }
        #endregion

        #region 单位权限控制

        /// <summary>
        /// 流程管理：获取单位控制数据信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getunitpermissioninfo")]
        public async Task<Result<PageModel<UnitInfo>>> GetUnitPermissionInfo([FromBody] WfChildUnitDisableParam param)
        {
            if (param.isFirst)
            {
                long processId = 0;
                var listProcess = await processManager.Query(f => f.IsDeleted == false && (f.UseUnitId == user.UnitId || f.UseUnitId == user.UnitPId) && f.Statuz == 1 && f.IsOpenControl == 1);
                listProcess = listProcess.OrderBy(f => f.PSort).ToList();
                if (listProcess.Count > 0)
                {
                    processId = listProcess[0].Id;
                }
                param.ProcessId = processId;
                //默认选中第一个
                PageModel<UnitInfo> data = await childUnitDisableManager.GetUnitPermissionInfo(param);
                return baseSucc(data, data.dataCount, "查询成功", new
                {
                    listProcess = listProcess,
                });
            }
            else
            {
                PageModel<UnitInfo> data = await childUnitDisableManager.GetUnitPermissionInfo(param);
                return baseSucc(data, data.dataCount);
            }
        }

        /// <summary>
        /// 流程管理：配置单位控制数据
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("setunitpermission")]
        public async Task<Result<string>> SetUnitPermission([FromBody] SetPermissionModel o)
        {
            return await childUnitDisableManager.SaveUnitPermission(o);
        }
        #endregion

        #region 消息配置
        /// <summary>
        /// 标准消息配置：消息配置列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmsgconfiglist")]
        public async Task<Result<PageModel<BMsgConfigDto>>> PostMsgConfigList([FromBody] BMsgConfigParam param)
        {
            PageModel<BMsgConfig> data = await msgConfigManager.GetPaged(param);
            return baseSucc(data.ConvertTo<BMsgConfigDto>(mapper), data.dataCount);
        }

        /// <summary>
        /// 标准消息配置：根据Id获取消息配置信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getmsgconfigbyid")]
        public async Task<Result> GetMsgConfigById(long id)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            BMsgConfig msgConfig = await msgConfigManager.QueryById(id);
            r.data.rows = mapper.Map<BMsgConfigDto>(msgConfig);
            return r;
        }

        /// <summary>
        /// 标准消息配置：新增修改消息配置
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmsgconfiginsertupdate")]
        public async Task<Result<string>> PostMsgConfigInsertUpdate([FromBody] BMsgConfigDto o)
        {
            return await msgConfigManager.InsertUpdate(o);
        }

        /// <summary>
        /// 标准消息配置：批量开启关闭
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmsgconfigsetsatuz")]
        public async Task<Result<string>> PostMsgConfigSetSatuz([FromBody] MsgConfigStatuzModel o)
        {
            return await msgConfigManager.BatchSetStatuz(o);
        }


        /// <summary>
        /// 审批消息配置：消息配置列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postprocessmsgconfiglist")]
        public async Task<Result<PageModel<WfMsgConfigDto>>> PostProcessMsgConfigList([FromBody] WfMsgConfigParam param)
        {
            PageModel<WfMsgConfigDto> data = await wfMsgConfigManager.GetPaged(param);
            if (param.isFirst)
            {
                List<dropdownModel> listProcessNode = await wfMsgConfigManager.GetProcessNode(param.ProcessId);

                List<dropdownModel> listMsgNo = new List<dropdownModel>();
                listMsgNo.Add(new dropdownModel() { value = "100100101", label = "100100101(提交)" });
                listMsgNo.Add(new dropdownModel() { value = "100100201", label = "100100201(转交下一步)" });
                listMsgNo.Add(new dropdownModel() { value = "100100202", label = "100100202(退回)" });

                return baseSucc(data, data.dataCount, "查询成功", new
                {
                    listProcessNode = listProcessNode,
                    listMsgNo = listMsgNo
                });
            }
            else
            {
                return baseSucc(data, data.dataCount);
            }
        }

        /// <summary>
        /// 审批消息配置：添加配置信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postprocessmsgconfigadd")]
        public async Task<Result<string>> PostProcessMsgConfigAdd([FromBody] WfProcessMsgConfigModel o)
        {
            return await wfMsgConfigManager.Add(o);
        }

        /// <summary>
        /// 审批消息配置：修改配置信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postprocessmsgconfigedit")]
        public async Task<Result<string>> PostProcessMsgConfigEdit([FromBody] WfMsgConfigDto o)
        {
            return await wfMsgConfigManager.Edit(o);
        }

        /// <summary>
        /// 审批消息配置：根据Id获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getprocessmsgconfigbyid")]
        public async Task<Result> GetProcessMsgConfigById(long id)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            WfMsgConfig msgConfig = await wfMsgConfigManager.QueryById(id);
            r.data.rows = mapper.Map<WfMsgConfigDto>(msgConfig);
            return r;
        }

        /// <summary>
        /// 标准消息配置：批量开启关闭
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postprocessmsgconfigsetsatuz")]
        public async Task<Result<string>> PostProcessMsgConfigSetSatuz([FromBody] MsgConfigStatuzModel o)
        {
            return await wfMsgConfigManager.BatchSetStatuz(o);
        }
        #endregion

        #region 私有方法

        /// <summary>
        /// 
        /// </summary>
        /// <param name="flatList"></param>
        /// <returns></returns>
        private List<WfDictionary> BuildDictionaryTree(List<WfDictionary> flatList)
        {
            var lookup = flatList.ToDictionary(n => n.Id);
            var rootNodes = new List<WfDictionary>();

            foreach (var node in flatList)
            {
                if (node.Pid == 0)
                {
                    // 这是根节点，没有父节点
                    rootNodes.Add(node);
                }
                else if (lookup.ContainsKey(node.Pid))
                {
                    // 将当前节点添加到其父节点的子节点列表中
                    lookup[node.Pid].children.Add(node);
                }
                // 如果PID在列表中不存在，则忽略该节点或进行其他处理
            }

            return rootNodes;
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="flatList"></param>
        /// <returns></returns>
        private List<dropdownModel> BuildDropDownModelTree(List<dropdownModel> flatList)
        {
            var lookup = flatList.ToDictionary(n => n.value);
            var rootNodes = new List<dropdownModel>();

            foreach (var node in flatList)
            {
                if (node.pid == 0)
                {
                    // 这是根节点，没有父节点
                    rootNodes.Add(node);
                }
                else if (lookup.ContainsKey(node.pid.ToString()))
                {
                    // 将当前节点添加到其父节点的子节点列表中
                    lookup[node.pid.ToString()].children.Add(node);
                }
                // 如果PID在列表中不存在，则忽略该节点或进行其他处理
            }

            return rootNodes;
        }
        #endregion

    }
}
