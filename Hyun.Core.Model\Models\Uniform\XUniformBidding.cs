﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///招标结果
    ///</summary>
    [SugarTable("x_UniformBidding", "招标结果")]
    public class XUniformBidding : BaseEntity
    {

        public XUniformBidding()
        {

        }

        /// <summary>
        ///学校Id
        /// </summary>
        public long SchoolId { get; set; }


        /// <summary>
        ///区县Id
        /// </summary>
        public long CountyId { get; set; }

        /// <summary>
        ///校服采购表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? UniformBuyId { get; set; }

        /// <summary>
        ///采购有效年限
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ValidityPeriod { get; set; }

        /// <summary>
        ///采购方式（对应字典表DicValue值）
        /// </summary>
        public int Method { get; set; } = 0;

        /// <summary>
        ///开标结果公开（1：已公开 2：未公开）
        /// </summary>
        public int BidResultPublic { get; set; } = 0;

        /// <summary>
        ///开标日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? BidDate { get; set; }

        /// <summary>
        ///公开日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? PublicDate { get; set; }

        /// <summary>
        ///公开媒体名称
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string PublicMediaName { get; set; }

        /// <summary>
        ///委托代理机构
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string EntrustingAgency { get; set; }

        /// <summary>
        ///是否备查
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? IsFiling { get; set; }

        /// <summary>
        ///代理机构联系人
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string Contact { get; set; }

        /// <summary>
        ///代理机构联系电话
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = true)]
        public string Mobile { get; set; }

        /// <summary>
        ///招标结果状态（0：待备案  10：待审核  100：已备案）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? BiddingStatuz { get; set; }

        /// <summary>
        /// 备案说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string FilingExplanation { get; set; }

        /// <summary>
        /// 采购有效年限名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string ValidityPeriodName { get; set; }

        /// <summary>
        /// 采购方式名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string MethodName { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SchoolName { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string AreaName { get; set; }

        /// <summary>
        /// 年度
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int PlanYear { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string BiddingStatuzName { get; set; }

        /// <summary>
        /// 采购批次
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PurchaseNo { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Remark { get; set; }
    }


}

