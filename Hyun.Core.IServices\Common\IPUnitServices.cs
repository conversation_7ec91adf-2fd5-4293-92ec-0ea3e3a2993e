﻿using Hyun.Core.Model.Model;
using Microsoft.AspNetCore.Mvc;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///PUnit接口方法
    ///</summary>
    public interface IPUnitServices : IBaseServices<PUnit>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        //Task<PUnit> QueryById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<PUnit>> Find(Expression<Func<PUnit, bool>> expression);
        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">PUnitParam实体参数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<List<PUnit>> Find(PUnitParam param);
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">PUnitParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<PUnit>> GetPaged(PUnitParam param);

        //<used>0</used>
        Task<List<PUnit>> GetByUnitType(int unitType);

        //<used>0</used>
        Task<DataTable> GetSchool(int countyId);

        //<used>0</used>
        Task<Result> Active(int id);

        //<used>0</used>
        Task<Result> InsertUpdateJj(int areaId, string townName, int pid, int unitType, int industryId, string code, string name, string brief, string pinYin, string pinYinBrief, string legal, string address, string zipCode, string url, string introduction, string logo, int vipGrade, string organizationCode, string loginPic, string mobile, string tel, string email, string position, string trafficMap, int employeeNum, int userId, string memo, int statuz, int classNum, int studentNum, int mileage, int id);

        //<used>0</used>
        Task<Result> ChangeMobile(int id, string mobile);

        //<used>0</used>
        Task<List<PUnit>> Delete(List<PUnit> entityCollection);

        //<used>0</used>
        Task<Result> UpdateSchoolInfo(long id, string brief, string organizationCode, string address, string zipCode, string url, string mobile, string introduction, int classNum, int studentNum, int teacherNum, decimal floorArea, decimal buildArea, int schoolStage, string memo, string schoolAdmin, string adminMobile, string headMaster, string msaterMobile, int schoolNature);

        //<used>0</used>
        Task<Result> DeleteByIds(string ids);

        //<used>0</used>
        Task<Result> DeleteUnitInfo(long id);

        //<used>0</used>
        Task<Result> Inactive(int id);

        //<used>0</used>
        Task<DataTable> ExistsOrganizationCode(string orgCode);

        //<used>0</used>
        Task<DataTable> ExistsCompanyName(string name);

        //<used>0</used>
        Task<DataTable> ExistsUserName(string name);

        //<used>1</used>
        Task<Result> ExamineStatuz(long id,string companyName);

        //<used>1</used>
        Task<Result> InsertCompany(long pid, int unitType, int industryId, string code, string name, string mobile, int statuz, string userName, string acctName, string pwd, long serviceAreaId);

        //<used>0</used>
        Task<Result> SubmitCertificationCompany(string unitName, long areaId, string legal, string address, string organizationCode, string userName, string tel, string busLinUrl);

        //<used>0</used>
        Task<Result> SchoolRegister(int pid, string name, string mobile, string userName, string userPswd, int id);

        //<used>0</used>
        Task<DataTable> GetSchoolDetail(PUnitParam param);

        //<used>0</used>
        Task<List<PUnit>> GetByPid(int pid);

       Task<Result> InsertUpdate(PUnitDto o);

       Task<Result> InsertUpdateUser(VUserDetail o);

       Task<WebSiteDataTable> GetWebSiteV1(int unitType, long unitId, string domainName, long unitPid);

        /// <summary>
        /// 获取区县、市级单位信息
        /// </summary>
        /// <returns></returns>
        Task<Result<PUnitSetDto>> GetXfUnitPaged();

        /// <summary>
        /// 保存区县、市级单位信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> SetXfUnitInfo(PUnitSetDto o);


        /// <summary>
        /// 单位、企业注册
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result> XfUnitRegister(UnitRegisterModel o);

        /// <summary>
        /// 区县导入单位信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<Result> SaveSchoolData(List<SchoolImportDto> list);

        /// <summary>
        /// 市级导入区县信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<Result> SaveCountyCityCompanyData(List<CountyCityCompanyImportDto> list);

        /// <summary>
        /// 导入下属单位账号密码信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<Result> SaveImportUserData(List<UserImportDto> list);

        /// <summary>
        /// 根据城市Id获取区县信息，当前市级可以传0
        /// </summary>
        /// <param name="cityId"></param>
        /// <returns></returns>
        Task<List<VCountyUnit>> GetAreaListByCityId(long cityId);

        /// <summary>
        /// 获取学校信息
        /// </summary>
        /// <returns></returns>
        Task<Result> GetSchoolUnitInfo();

        /// <summary>
        /// 保存学校信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> SaveSchoolUnitInfo(SchoolUnitInfoModel o);

        /// <summary>
        /// 获取省-市-区信息
        /// </summary>
        /// <param name="areaId"></param>
        /// <returns></returns>
        Task<string> GetAreaName(long areaId);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<Result> SaveSchoolViewData(List<SchoolImportDto> list);
    }
}

