﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcWasteDisposalDetail接口方法
    ///</summary>
    public interface IDcWasteDisposalDetailServices : IBaseServices<DcWasteDisposalDetail>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<DcWasteDisposalDetail> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<DcWasteDisposalDetail>> Find(Expression<Func<DcWasteDisposalDetail, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">DcWasteDisposalDetailParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<DcWasteDisposalDetail>> GetPaged(DcWasteDisposalDetailParam param);

       //<used>0</used>
       Task<DataTable> GetBaseWasteList();

       //<used>1</used>
       Task<PageModel<object>> GetSchoolWasteList(long unitId);

       //<used>1</used>
       Task<Result> DeleteTempByWasteId(long unitId, string ids);

       //<used>1</used>
       Task<Result> DeleteTempByUnitId(long unitId);

       //<used>0</used>
       Task<List<DcWasteDisposalDetail>> Insert(List<DcWasteDisposalDetail> entityCollection);

       //<used>1</used>
       Task<PageModel<object>> DcWasteStatisticsList_GetByCounty(DcWasteDisposalDetailParam param);

       //<used>0</used>
       Task<PageModel<object>> DcWasteStatisticsList_GetByCity(DcWasteDisposalDetailParam param);

       //<used>0</used>
       Task<DataTable> GetWasteStandbook(DcWasteDisposalDetailParam param);

    }
}

