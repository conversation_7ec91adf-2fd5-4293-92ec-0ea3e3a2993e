namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位参数设置
    ///</summary>
    [SugarTable("b_UnitSetting","单位参数设置")]
    public class BUnitSetting : BaseEntity
    {

          public BUnitSetting()
          {

          }

           /// <summary>
           ///单位id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///需要校长审批
          /// </summary>
          public int NeedHeader { get; set; }

           /// <summary>
           ///需要区县审核
          /// </summary>
          public int NeedCounty { get; set; }

           /// <summary>
           ///用户评价公开
          /// </summary>
          public int EvaluationPublic { get; set; }

           /// <summary>
           ///校内响应报警时间（小时）
          /// </summary>
          public int SchoolResponseHour { get; set; }

           /// <summary>
           ///校内维修报警时间（天）
          /// </summary>
          public int SchoolRepairDay { get; set; }

           /// <summary>
           ///企业响应报警时间（小时）
          /// </summary>
          public int CompanyResponseHour { get; set; }

           /// <summary>
           ///企业维修报警时间（天）
          /// </summary>
          public int CompanyRepairDay { get; set; }

           /// <summary>
           ///默认审核通过时间（天）
          /// </summary>
          public int DefaultAuditPassDay { get; set; }

           /// <summary>
           ///默认好评通过时间（天）
          /// </summary>
          public int DefaultEvaluationDay { get; set; }

           /// <summary>
           ///默认评价星级（5星）
          /// </summary>
          public int DefaultEvaluationStar { get; set; }

           /// <summary>
           ///使用上级维修服务费
          /// </summary>
          public int InheritServiceFee { get; set; }

           /// <summary>
           ///使用上级维修配件费
          /// </summary>
          public int InheritPartsFee { get; set; }

           /// <summary>
           ///使用上级维修上门费
          /// </summary>
          public int InheritRepairHomeFee { get; set; }

           /// <summary>
           ///使用上级指定企业
          /// </summary>
          public int InheritCompany { get; set; }

        /// <summary>
        ///上门费单价
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal HomePrice { get; set; }

           /// <summary>
           ///报修人评价截点（天）
          /// </summary>
          public int EvaluationDay { get; set; }

           /// <summary>
           ///单位验收截点（天）
          /// </summary>
          public int SchoolAcceptanceDay { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

