﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///下属单位流程禁用表
    ///</summary>
    [SugarTable("wf_ChildUnitDisable","下属单位流程禁用表")]
    public class WfChildUnitDisable : BaseEntity
    {

          public WfChildUnitDisable()
          {

          }

           /// <summary>
           ///流程Id
          /// </summary>
          public long ProcessId { get; set; } = 0;

           /// <summary>
           ///禁用单位Id
          /// </summary>
          public long DisableUnitId { get; set; } = 0;

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

