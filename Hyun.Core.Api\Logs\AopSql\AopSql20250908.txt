
Date：2025-09-08 14:53:13.000
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE ((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:25.074
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:592123693924485 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:26.120
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:592123693924485 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:26.209
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:592123693924485 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:42.453
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PAU].[Id] AS [Id] , [PAU].[BusinessType] AS [BusinessType] , [PAU].[ProcessId] AS [ProcessId] , [PAU].[ProcessNodeId] AS [ProcessNodeId] , [PAU].[AuditUnitId] AS [AuditUnitId] , [PAU].[AuditUserId] AS [AuditUserId] , [UE].[Name] AS [AuditUserName]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [SysUserExtension] [UE] ON (( [PAU].[AuditUserId] = [UE].[Id] ) AND ( [UE].[Statuz] = @Statuz0 )) AND ( [UE].[IsDeleted] = @IsDeleted4 )   WHERE ((( [PAU].[AuditUnitId] = @AuditUnitId1 ) AND ( [PAU].[IsDeleted] = @IsDeleted2 )) AND ( [UE].[IsDeleted] = @IsDeleted3 ))  AND ( [PAU].[IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@AuditUnitId1 [Value]:592123124052101 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:42.524
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:592122937471109 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:42.571
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:592123124052101 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:42.690
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , [P].[IsOpen] AS [IsOpen]  FROM [wf_Module] [M] Inner JOIN [wf_Process] [P] ON ( [M].[Id] = [P].[ModuleId] ) AND ( [P].[IsDeleted] = @IsDeleted13 )   WHERE (((((NOT (1=2)  AND( [M].[IsDeleted] = @IsDeleted1 )) AND ( [P].[IsDeleted] = @IsDeleted2 )) AND ( [M].[Statuz] = @Statuz3 )) AND ( [P].[Statuz] = @Statuz4 )) AND ((((( [P].[Usage] = @Usage5 ) AND ( [P].[UseUnitId] = @UseUnitId6 )) OR (( [P].[Usage] = @Usage7 ) AND ( [P].[UseUnitId] = @UseUnitId8 ))) OR (( [P].[Usage] = @Usage9 ) AND ( [P].[UseUnitId] = @UseUnitId10 ))) OR (( [P].[Usage] = @Usage11 ) AND ( [P].[UseUnitId] = @UseUnitId12 ))))  AND ( [M].[IsDeleted] = @IsDeleted13 ) 
[Pars]:
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@Usage5 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId6 [Value]:592123124052101 [Type]:Int64    
[Name]:@Usage7 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId8 [Value]:592123124052101 [Type]:Int64    
[Name]:@Usage9 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId10 [Value]:592122937471109 [Type]:Int64    
[Name]:@Usage11 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId12 [Value]:592122710352005 [Type]:Int64    
[Name]:@IsDeleted13 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:53:42.827
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PRS].[ModuleId] AS [ModuleId] , [M].[Name] AS [ModuleName] , [PRS].[ProcessId] AS [ProcessId] , [PRS].[ProcessNodeId] AS [ProcessNodeId] , [P].[ProcessName] AS [ProcessName] , [PN].[NodeShowName] AS [ProcessNodeName] , [PN].[RoleName] AS [RoleName] , @constant15 AS [AuditUserName] , ISNULL([PRS].[Sort],@MethodConst16) AS [Sort] , [P].[IsOpen] AS [IsOpen] , ISNULL([LEOS].[IsLook],@MethodConst17) AS [IsLook] , ISNULL([PN].[UseGroupValue],@MethodConst18) AS [UseGroupValue]  FROM [wf_ProcessReturnSet] [PRS] Inner JOIN [wf_Module] [M] ON ( [PRS].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted14 )  Inner JOIN [wf_Process] [P] ON ( [PRS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted14 )  Left JOIN [wf_ProcessNode] [PN] ON ( [PRS].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted14 )  Left JOIN [wf_LookEachOtherSet] [LEOS] ON ((( [LEOS].[ProcessId] = [P].[Id] ) AND ( [LEOS].[ProcessNodeId] = [PN].[Id] )) AND ( [LEOS].[UnitId] = @UnitId0 )) AND ( [LEOS].[IsDeleted] = @IsDeleted14 )   WHERE (((((NOT (1=2)  AND( [PRS].[IsDeleted] = @IsDeleted2 )) AND ( [PN].[ProcessLevel] = @ProcessLevel3 )) AND ( [M].[Statuz] = @Statuz4 )) AND ( [P].[Statuz] = @Statuz5 )) AND ((((( [P].[Usage] = @Usage6 ) AND ( [P].[UseUnitId] = @UseUnitId7 )) OR (( [P].[Usage] = @Usage8 ) AND ( [P].[UseUnitId] = @UseUnitId9 ))) OR (( [P].[Usage] = @Usage10 ) AND ( [P].[UseUnitId] = @UseUnitId11 ))) OR (( [P].[Usage] = @Usage12 ) AND ( [P].[UseUnitId] = @UseUnitId13 ))))  AND ( [PRS].[IsDeleted] = @IsDeleted14 ) 
[Pars]:
[Name]:@UnitId0 [Value]:592123124052101 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@ProcessLevel3 [Value]:3 [Type]:Int32    
[Name]:@Statuz4 [Value]:1 [Type]:Int32    
[Name]:@Statuz5 [Value]:1 [Type]:Int32    
[Name]:@Usage6 [Value]:1 [Type]:Int32    
[Name]:@UseUnitId7 [Value]:592123124052101 [Type]:Int64    
[Name]:@Usage8 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId9 [Value]:592123124052101 [Type]:Int64    
[Name]:@Usage10 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId11 [Value]:592122937471109 [Type]:Int64    
[Name]:@Usage12 [Value]:2 [Type]:Int32    
[Name]:@UseUnitId13 [Value]:592122710352005 [Type]:Int64    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
[Name]:@constant15 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
[Name]:@MethodConst17 [Value]:2 [Type]:Int32    
[Name]:@MethodConst18 [Value]:2 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:55:25.858
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:592123693924485 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:55:28.525
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:592123693924485 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:55:28.539
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:592123693924485 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:55:32.243
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [UI].[UserExtensionId] AS [UserId] , [SUE].[Name] AS [UserName] , ISNULL([AU].[Id],@MethodConst10) AS [IsCheck]  FROM [SysUserInfo] [UI] Inner JOIN [SysUserExtension] [SUE] ON ( [UI].[UserExtensionId] = [SUE].[Id] ) AND ( [SUE].[IsDeleted] = @IsDeleted7 )  Inner JOIN [SysUserRole] [UR] ON (( [UI].[UserExtensionId] = [UR].[UserId] ) AND ( [UI].[Statuz] = @Statuz0 )) AND ( [UR].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProjectAuditUser] [AU] ON (((( [UI].[UserExtensionId] = [AU].[AuditUserId] ) AND ( [AU].[ProcessId] = @ProcessId1 )) AND ( [AU].[ProcessNodeId] = @ProcessNodeId2 )) AND ( [AU].[AuditUnitId] = @AuditUnitId3 )) AND ( [AU].[IsDeleted] = @IsDeleted9 )   WHERE ( [SUE].[UnitId] = @UnitId4 )  AND ( [UR].[RoleId] = @RoleId5 )GROUP BY [UI].[UserExtensionId],[SUE].[Name],[AU].[Id]  
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ProcessId1 [Value]:676375854522501 [Type]:Int64    
[Name]:@ProcessNodeId2 [Value]:20000 [Type]:Int64    
[Name]:@AuditUnitId3 [Value]:592123124052101 [Type]:Int64    
[Name]:@UnitId4 [Value]:592123124052101 [Type]:Int64    
[Name]:@RoleId5 [Value]:3200 [Type]:Int64    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted9 [Value]:False [Type]:Boolean    
[Name]:@MethodConst10 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-08 14:55:32.324
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessId],[ProcessNodeId],[UnitId],[IsLook],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_LookEachOtherSet]  WHERE (((( [IsDeleted] = @IsDeleted0 ) AND ( [ProcessId] = @ProcessId1 )) AND ( [ProcessNodeId] = @ProcessNodeId2 )) AND ( [UnitId] = @UnitId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:676375854522501 [Type]:Int64    
[Name]:@ProcessNodeId2 [Value]:20000 [Type]:Int64    
[Name]:@UnitId3 [Value]:592123124052101 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------