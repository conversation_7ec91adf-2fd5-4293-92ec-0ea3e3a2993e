{
  "hyun": {
    "Cookie.Key": "!@#123qwe000", //Cookie 加密key和向量 
    "Cookie.IV": "aas@#$w4t2@#$%$", // 
    "Pagination.PageSize": "50", //分页 最大记录数 
    "Page.Index": "~/default.aspx", //系统首页 
    "Page.Login": "~/login.html", //系统登录页面 
    "Page.Error": "~/Err.aspx", //系统错误页 
    "Page.Logout": "~/Logout.aspx", //系统退出页面 
    "Permission.Page.Enabled": "true", //权限验证是否采用，正式发布时改成：true 
    "Permission.Enabled": "false", // 
    "Permission.WhiteList": "Default2020.aspx,Plan_bj3/AuditProjectList-debug.aspx,Default.aspx,Main.aspx,Index.aspx,Article/AttachmentDown.aspx,Article/ViewList.aspx,Article/ArticleDetailView.aspx,Article/View.aspx,Default_Dev.aspx,Process2_0/DictionaryCategorySet.aspx,LowValueMain.aspx,DangerChemicalsMain.aspx", //页面白名单 
    "Import.Unit": "单位编号|单位名称|单位简称|法人|地址|邮编|网址|组织机构代码|联系电话|邮箱|员工数|简介", //单位导入 Excel 标题列：普通类型 
    "Import.Unit.School": "单位编码|单位名称|单位简称|单位性质|班级总数|学生总数|教职工数|占地面积（平方米）|建筑面积（平方米）", //单位导入 Excel 标题列：学校类型 
    "Import.Unit.Company": "单位编号|单位名称|单位简称|法人|地址|邮编|网址|组织机构代码|联系电话|邮箱|员工数|简介|企业性质", //单位导入 Excel 标题列：企业类型 
    "Import.User": "姓名|手机电话|性别|QQ|登录账号|密码|单位编号|单位名称", //用户导入 Excel 标题列：带单位信息 
    "Import.User.Mine": "姓名|手机电话|性别|QQ|登录账号|密码", //用户导入 Excel 标题列：本单位用户 
    "Import.Address": "楼宇场馆名称|楼宇场馆别名|楼宇场馆备注|场所(室)名称|场所(室)别名|场所(室)备注", //学校导入 Excel 标题列：本单位办公场所 
    "Import.CompanyAreaService": "入选维修商|区县|服务类别", //用户导入 Excel 标题列：招标企业服务项目、服务地区 
    "Import.EquipmentList": "设备名称|设备品牌|规格型号|具体配置或材质说明|数量|单位|单价|金额|设备属性", //设备库导入 Excel 标题列：设备名称,设备品牌,规格型号,具体配置或材质说明,数量,单位,单价,金额,设备属性 
    "Import.Project.EquipmentList": "序号|装备类别|设备类别|标准设备名称|设备属性|设备名称|设备品牌|设备型号|具体配置或材质说明|数量|单位|单价(元）|金额（元）|质保期（月）", //新版设备库导入 Excel 标题列：设备名称,设备品牌,规格型号,具体配置或材质说明,数量,单位,单价,金额,设备属性 
    "Import.Project.Company.EquipmentList": "序号|设备名称|设备品牌|设备型号|具体配置或材质说明|数量|单位|单价(元）|金额（元）|质保期（月）", // 
    "Import.SchoolEquipment": "供货单位|设备名称|设备品牌|设备型号|设备配置或材质说明|设备数量|计量单位|设备单价|采购时间|质保期（月）|经手人", //学校设备导入 Excel 标题列：供货单位,设备名称,设备品牌,设备型号,设备配置或材质说明,设备数量,计量单位,设备单价,采购时间,质保期（月）,经手人 
    "Import.SchoolEquipmentV2": "资产名称|资产品牌|资产型号|资产配置或材质说明|数量|单位|单价（元）|采购时间|质保期（月）|经手人", //学校设备导入 Excel 标题列：供货单位,设备名称,设备品牌,设备型号,设备配置或材质说明,设备数量,计量单位,设备单价,采购时间,质保期（月）,经手人 
    "Import.Contract.EquipmentList": "序号|设备名称|设备品牌|设备型号|具体配置或材质说明|数量|单位|单价(元）|金额（元）|质保期（月）", //合同设备导入 Excel 标题列：序号|设备名称|设备品牌|设备型号|具体配置或材质说明|数量|单位|单价(元）|金额（元）|质保期（月） 
    "Import.Contract.ProjectList": "序号|设备名称|设备品牌|设备型号|具体配置或材质说明|数量|单位|单价(元）|金额（元）|使用场所|备注", //采购立项 
    "Import.Contract.ProjectListWj": "序号|设备名称|推荐品牌|推荐型号|具体配置|数量|单位|单价(元）|金额（元）|使用场所|备注", //采购立项（吴江版） 
    "Import.Plan.ProjectListBj": "序号|设备名称|设备品牌|设备型号|具体配置或材质说明|数量|单位|单价(元)|金额(元)|使用场所|备注", //项目预算明细（北京预算编制） 
    "Import.Plan.ProjectListBjV3": "序号|设备名称|参考品牌|参考型号|具体配置|数量|单位|单价(元)|金额(元)|使用场所", //项目清单导入（北京预算编制V3版本） 
    "Import.Contract.ProjectListHc": "序号|设备名称|具体配置或材质说明|数量|单位|单价(元）|金额（元）|使用场所|备注", //采购立项 
    "Import.Equipment.ProjectListFy4": "序号|设备名称|具体配置或材质说明|数量|单位|单价(元）|金额（元）|使用场所|备注", //项目清单导入（富阳方案审批v4版本） 
    "Import.CapitalSettingNc": "序号|学校名称|资金来源|项目名称|项目预算（万元）", //南昌审批定制：导入资金来源 
    "Import.CapitalSettingWj": "序号|学校名称|项目名称|项目性质|预算金额（元）|备注", //吴江审批定制：导入资金来源 
    "Import.CapitalSettingPlanV3": "序号|学校名称|项目名称|项目性质|预算金额（元）|预算年度|备注", //计划编制V3：导入资金来源 
    "Import.CapitalSettingTz": "序号|使用单位|资金来源|资金性质|预算年度|预算金额（元）", //通州审批定制：导入资金来源 
    "Import.CapitalSettingHa": "序号|学校名称|资金性质|专项款名称|专项款文号|预算金额（元）", //淮安审批定制：导入资金来源 
    "Import.CapitalSettingHc": "预算单位|校区|项目分类|项目编码|一级预算分类|二级预算分类|三级预算分类|预算项目名称|预算来源|预算金额（元）|预算年度|资金来源|支出功能分类|项目内容|储备库编号|备注", //海沧审批定制：导入资金来源 
    "Import.CapitalSourceJbxq": "预算单位|项目分类|预算项目名称|预算来源|预算金额（元）|预算年度|资金来源|采购主体|备注", //Process3_0：导入资金来源 
    "Import.CapitalSourceV3": "预算单位|项目分类|预算项目名称|预算来源|预算金额（元）|预算年度|资金来源|项目文号|备注", //Process3_0：导入资金来源 
    "Import.Plan.School.Level1": "装备类别|装备项目|项目名称|* 数量|* 计划金额|* 建设类型|备 注\n（设备名称及配置）|资金配比（%）||||", //学校计划导入 
    "Import.Plan.School.Level2": "|||||||区级|学校|市级|街道|其它", // 
    "Import.PartsItem": "序号|设备名称|配件编码|配件名称|配件品牌|配件型号|计量单位|质保期(月)|建议单价(元）|备注", //配件库导入 
    "Import.ServiceItem": "序号|设备名称|服务项目类别名称|建议单价(元）|备注", //服务项目类别导入 
    "Import.SchoolMaterial": "ID|申请批次|物品名称|规格型号材质|数量|单位|单价|金额|采购品牌|供货品牌|有效期至|质保期(月)", //低值易耗批量导入 Excel 标题列： 
    "Import.LowValue.StockMaterial": "单位名称|仓库名称|物品名称|规格型号材质|品牌|数量|计量单位|单价（元）|供应商|有效期至|质保期（月）", //低值易耗存量导入 Excel 标题列： 
    "Import.BudgetFy": "序号|学校名称|项目名称|预算金额（元）|区县|学校|乡镇|其他|区县|学校|乡镇|其他|计划内预留|采购结余", //富阳应急预算： 
    "Import.Collection": "单位编码|单位名称|班级多媒体(套)|学生电脑(台)|教师电脑(台)|其中笔记本电脑(台)|图书(册)|当年新增图书(册)", //下属单位信息导入(达标评估数据) 
    "Import.PlanPurchaseRevise": "序号|ID|集中标识|学校名称|采购项目名称|采购项目编号|采购方式|预算来源|项目金额|修正金额|资金方式|区级资金|乡镇资金|校级资金|市级资金|其他资金|建设内容与规模|项目负责人|联系电话", //计划编制导入修正金额 
    "Import.PlanAudit": "修改类型|单位名称|ID|装备项目|项目名称|数量|单位|单价|计划金额|紧急程度|备注|资金方式|区县|学校|乡镇|市级|其它", //计划审核 
    "Import.BjAssetDisposalList": "资产编号|资产名称|资产国标大类|资产分类代码|资产原值（元）|累计折旧（摊销）|资产净值|数量|取得日期|规格型号|申报原因|备注", //北京资产处置，清单导入 
    "Import.Plan.ProjectListZjg": "物品编号|物品名称|规格型号|技术要求|产地|申请数量|单位|预算单价(元)|预算金额(元)", //张家港计划编制：预算明细导入 
    "Import.Process.ProjectListYzGl": "序号|设备名称|型号规格|技术参数|数量|单位|单价（元）|金额（元）|使用地点", //扬州广陵采购审批：采购明细导入 
    "Import.Contract.ProjectListConfig": "序号|分类名称|设备名称|设备品牌|设备型号|具体配置或材质说明|单位|单价(元）|使用场所|备注", //区县配置立项清单 
    "Import.DangerChemicals.SchoolMaterial": "学校名称|危化品分类|危化品名称|规格属性|品牌|库存数量|计量单位|单价（元）|供应商|存放地点|有效期至|质保期（月）|是否可用", //危化品管理：学校存量数据导入 
    "Import.ProcessZjg.ItemStandard": "一级分类|二级分类|物品编号|物品名称|单位|预算单价（元）|产地|规格型号|技术要求|采购技术要求|备注", //张家港集中采购审批：区县标准库导入 
    "Import.User.ThirdAllow": "单位名称|人员名称|手机号码|类型|角色", //用户导入 Excel 标题列：第三方用户白名单 
    "Import.FyAssetClear": "序号|所属单位|GS1编码|资产分类编码|资产名称|资产原值|取得日期", //富阳魔方导入资产数据验证必须包含以下字段名称 
    "SendMessage.Event.Submit": "{0}{1}报修{2}，单号{3}", // 
    "SendMessage.Event.RepairNeed.InSchool": "{0}老师({1})报修{2},单号:{3}", // 
    "SendMessage.Event.Dispatching": "{0}{3}({4})报修{1}，单号:{2}", // 
    "SendMessage.Event.ProjectAcceptance": "{0}申请验收{1}项目", // 
    "SendMessage.Event.ProjectCodePost": "您申请的二维码已经由{0}寄出单号{1}", // 
    "SendMessage.Event.AuditEnterprise": "您注册的企业[{0}]已审核通过，账号[{1}]", // 
    "SendMessage.Event.SendBaseCode": "验证码：{0}。有效期{1}分钟。", // 
    "SendMessage.Event.SendCode": "{0}。亲爱的用户您好，以上是您申请找回密码的验证码。", // 
    "SendMessage.Event.RegisterSchoolCode": "{0}。亲爱的用户您好，以上是您注册学校信息的验证码。", // 
    "SendMessage.Event.RegisterSupplierCode": "{0}。亲爱的用户您好，以上是您注册单位信息的验证码。", // 
    "SendMessage.Event.BorrowCode": "{0}。亲爱的用户您好，以上是您借用资产的验证码。", // 
    "SeneMessage.Event.NcContractSendCheckCode": "{0}。亲爱的用户您好，以上是您领取合同编号：{1}文本合同的验证码。", //南昌合同送审，领取合同、领取验收单发送验证码内容 
    "SendMessage.Event.ContractReceive": "合同编号：{0}的文本合同我办已接收。", //南昌版合同送审，接收合同 
    "SeneMessage.Event.LvLowValueCollarCheckCode": "{0}。亲爱的用户您好，以上是您领用物品的验证码。领用详情：{1} {2} {3} {4}{5}", //低值易耗，领取物品、领取验收单发送验证码内容 
    "SeneMessage.Event.LvLowValueBatchCollarCheckCode": "{0}。亲爱的用户您好，以上是您领用物品的验证码！", //低值易耗，批量领取物品、领取验收单发送验证码内容 
    "SeneMessage.Event.LvLowValueRevertMsg": "你领用的{0}于{1}已归还。", //低值易耗，领取物品、物品归还提示信息 
    "SeneMessage.Event.TerminalBox": "{0}。亲爱的用户您好，以上是您的取货码，请尽快前往(物流柜{1}-{2})领取。", //低值易耗，物流柜取货短信重发 
    "SeneMessage.Event.DcDangerChemicalsDistributeCode": "{0}。亲爱的用户您好，以上是您领用危化品（申领批次：{1}）的验证码。", //危化品管理配货验证码 
    "SendMessage.Event.AcceptanceApplication": "{0}申请验收项目({1}),请处理", //申请验收 
    "SendMessage.Event.AcceptanceDocuments": "{0}申请审核项目({1}),请处理", //提交验收文档 
    "SendMessage.Event.Apply": "项目({0})有变更通知,请处理", //变更申请 
    "SendMessage.Event.ProjectRectification": "项目({0})有整改通知,请处理", //项目整改 
    "SendMessage.Event.AuditBackBJ": "项目({0})审核不通过被退回,请处理", //审核学校退回给企业（用于北京） 
    "SendMessage.Event.ProjectPurchasedNc": "您申报的项目({0})已采购，请注意查收。", //南昌版本审核审批，已采购给项目申报人发送短信 
    "SendMessage.Event.ApprovalNoPassNc": "您申报的项目({0})审批不通过，请修改后重新申报。", //南昌版本审核审批，审批不通过退回申报人给项目申报人发送短信 
    "SendMessage.Event.ApprovalNext": "{0}提交了一个方案，请及时审批。", //镇江版本方案审批，转交下一步给项目分管领导发送短信 
    "SendMessage.Event.HcTenderUpdate": "“{0}”的招标项目已变更：{1}，请上平台查看。", //海沧区招标管理，招标公示更改给投标企业发送短信 
    "SendMessage.Event.HcTenderRevoke": "“{0}”的招标项目已暂停招标。", //海沧区招标管理，招标公示撤销给投标企业发送短信 
    "SendMessage.ProcessV3.TurnNextMsg": "{0}，{1}，请您及时处理！", //审批V3通用短信默默{项目名称}，{项目状态}， 请您及时处理 
    "SendMessage.Event.DcApplyMsg": "收到{0}提交的危化品领用申请，请您及时处理！", //危化品管理领用简易版领用通知 
    "SendMessage.Event.DcGrantMsg": "您领用的危化品已准备好，请您及时领取！", //危化品管理领用简易版发放通知 
    "SeneMessage.Event.LvLowValueStockWarningMsg": "物品库存不足。{0}/Lv/{1}/{2}", //低值易耗，物品存量库预警发送短信(0:手机号码  1：编码) 
    "SendMessage.Event.FyProcess.ExpertReview": "{0}/p/{1} 。亲爱的用户您好，以上是需要您评审的项目，请在您的手机浏览器中打开该链接查看详情。", //富阳方案审批向专家发放评审通知短信 
    "SendMessage.Event.FyProcess.ExpertReviewSign": "{0}/p/{1} 。亲爱的用户您好，以上是您评审的项目，请在您的手机浏览器中打开该链接查看详情，并确认无误后签字确认。", //富阳方案审批向专家发放签字通知短信 
    "Import.Assets.StockAsset": "资产编码|资产名称|资产品牌|资产型号|资产配置或材质说明|数量|单位|单价（元）|采购时间|质保期（月）|存放楼宇|存放场所|项目名称|供应商|使用部门|使用人|经办人", //资产监管V6：存量资产导入 
    "Import.Assets.SchoolAsset": "资产名称|资产品牌|资产型号|资产配置或材质说明|数量|单位|单价（元）|采购时间|质保期（月）|存放楼宇|存放场所|项目名称|供应商|使用部门|使用人|经办人", //资产监管V6：学校增量资产导入 
    "Import.Assets.Management": "资产编码|资产名称|资产数量|处置方式|处置时间", //资产监管V6：处置清单导入 
    "Thumbnails.Articl.Width": "260", //资讯缩略图大小设置 
    "Thumbnails.Articl.Height": "180", // 
    "File.Upload.Ext": ".jpg.jpeg.png.gif.bmp.doc.pdf.ppt.pptx.txt.xls.xlsx.docx.dwg.wps.zip.rar", //上传文件类型控制 txt:60115 
    "File.Upload.ZipExt": ".zip", // 
    "File.Upload.ExtCode": ".255216.255216.13780.7173.6677.60116.3780.208207.8075.102100.208207.8075.8075.6567.8297", // 
    "Encrypt.PublicKey": "AwEAAaXLcq1MxzbuLIOT2IA1txsykR997Fwh3hP4mHD9zmsVQpeZ3pg+yRA3vMjNPkHmbFCpqVDITBjiLTgHSOUF3vgf8bTVoNYDZqrfv2tHVbg20VE/WgxIWjZtDMU06E1MdYS5eOQhe9KB5ooP6K86ov0rIt5g/IPotpDSzmN12DPZ", // 
    "Encrypt.PrivateKey": "gCH31Sq+8ojlUt7xZVfdSYLrwRJ57vlwYernEbqCsa48WJytZI7+PB0TwlzEukbvsgzzudIoe3jUrC2QxoVPxt7sZWmx+AnsU0WNJmTn5eWbkTXP0VHyiwhUAwzj7TxyyJDQI+p39lq4trqtXhWJNsOfz2JMQHmmrv5g040/M5tlpctyrUzHNu4sg5PYgDW3GzKRH33sXCHeE/iYcP3OaxVCl5nemD7JEDe8yM0+QeZsUKmpUMhMGOItOAdI5QXe+B/xtNWg1gNmqt+/a0dVuDbRUT9aDEhaNm0MxTToTUx1hLl45CF70oHmig/orzqi/Ssi3mD8g+i2kNLOY3XYM9k=", // 
    "Encrypt.Pwd.PrivateKey": "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAOg2sBUXtRn9PuB/9RFgqin1Qmgb/22wEgD1M6uByppZglTsjiqwJ5aXIa8ai471zcXxi12ALugllsrUXrLd5Bhnnsh00mO09i3eyXBPu1YnYC43E5iJLDyH5hKt/0pwyHHsYQJG3OGSW6j50mXXHT9h+kYDTSokn5mUdy6yZlH9AgEDAoGAAcJai8vQ4xshci+Dy8SccM1+kE3JsVDZWTgpHmPkcad3SlcgG3mTsaAs3HpiBua17CqOoa2Tv9G9Q3nZ7fYLbMbJkvya46w5I+t0FosRJRNIBW2cSWXhB/6jnn7RrdMNGa357D6JxM1qi6BDzVwDmxNjn51j0qpbnh8rwaUmaPsCQQD3UwB9wGqksxS06eMtkjY6kwXvzzBSctalXy/++cn6mbmTa+6A1gOhIAafh3ShRF2QRQyUmsFM7mzARpE7hHBNAkEA8Fv9tzSUQsyTwgPtbVIB+6pbwq6PrrFGlikpSuffo216pBdDhv1kBkujt1UNw/zr0lbsRznJAHOkwaDETxPAcQJBAKTiAFPVnG3MuHib7MkMJCcMrp/fdYxMjxjqH/9RMVG70QzynwCOrRYVWb+voxYtk7WDXbhnK4ie8yrZtietoDMCQQCgPVPPeGLXMw0sAp5I4VanxufXHwp0di8OxhuHRT/CSPxtZNevU5gEMm0k416CqJ025J2E0TCq98MrwILfYoBLAkBmSZM+M5mfKCRycA87g6FitxiugKNYfr0sbFTT6GDW2dWSO+e+tPlYwkjPbxedUY1RaB35VGWcjxOsBsaR5Uyu", //密码解密私钥 
    "Encrypt.Pwd.Regex": "^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{6,30}$", //密码验证规则 
    "SendMessage.WaitSecond": "30", //验证码等待时间(秒) 
    "SendMessage.TimesPhone": "10", //限制手机当天最多使用次数 
    "SendMessage.TimesIP": "10", //限制IP地址当天最多使用次数 
    "SendMessage.TimesDayFindPwd": "100", //注册、找回密码当天限制次数 
    "SendMessage.SmsDuration": "5", //控制短信验证码失效时长(分钟) 
    "Personality.Domain": "1", //开启个性化域名（0：禁用；1：开启） 
    "Open.Depart.County": "0", //开启区县部门管理（0：禁用；1：开启） 
    "Open.Depart.City": "0", //开启市级部门管理（0：禁用；1：开启） 
    "Asset.IsInAssets": "1", //资产监管，是否开启小数必须转换才可审核入资产库 
    "SendMessage.Config.OpenStatus": "1", //发送短信,开启状态：1：开启，0：关闭；通道：（1：掌骏）用户名、密码、地址 
    "SendMessage.Config.SendWay": "1", // 
    "SendMessage.Config.UserId": "10460", // 
    "SendMessage.Config.UserName": "jfkj", // 
    "SendMessage.Config.Password": "43C73F3D4833F00C0D53E5AEBC09745037E0E374E6560B2D31B0C8ECFEAA4B1A109B3A49163BD9327CA0B52034F30095130E6EA5C82839B1D15C02B99FB8B4FC140B48CFF01248B66A467D59781B04635C876E4A0A3FEB969CFE907B62F3CBD73AA0B32F4D59014224D13466C69769462F3457B8FA57A7FC14229CCFB8EC13CA", // 
    "SendMessage.Config.Url": "http://sms.izjun.cn/v2sms.aspx", // 
    "SendMessage.Config.ClientSign": "【校服平台】", // 
    "CommonLog.FilePath": "D:\\WebSiteLog\\Common\\", //日志 
    "ErrorLog.FilePath": "D:\\WebSiteLog\\Error\\", // 
    "Process.Version": "0", //项目审批版本控制（0：默认；1：徐州；2：北京；3：南昌；4：吴江；5：富阳；7:建邺  8:镇江 9:通州(阆中)  10:厦门 11:淮安经开区 12:淮安高职校 13:南京 14：宿城区 15:德兴市 16：金陵高中） 
    "Platform.Code": "20", // 
    "Expert.OpenStatus": "0", //专家配置（0：无；1：市级；2：区级） 
    "DangerChemicals.Level": "2", //危化品管理模块级别（2：区级 ；3：校级） 
    "MProject.RelationDataSource": "0", //修改履约验收项目，根据不同配置跳转到不同页面；-1：企业创建项目；0：不关联；2：北京(f_ProjectDeclaration)；5：富阳(fy_PlanBudget)；8：镇江(zj_ProjectPurchase)；9：通州、厦门、吴江、阆中、宿城区(xa_ProjectPurchase) 
    "Chart.IndexProject": "200", //控制首页履约验收图表显示（200：多校标准版、220：北京版、250：富阳版） 
    "defaultSet.defaultProvince": "818", //地区配置 
    "defaultSet.defaultCity": "819", // 
    "defaultSet.defaultCounty": "820", // 
    "Center.Mobile.Server.Address": "http://m.cneefix.com/", // 
    "Sn.Code.Title": "教育装备", //二维码标题         
    "IsEnableMultVerificationCode": "0", //是否启用复杂验证码 
    "Project.IsSingleSchool": "0", //履约验收打印项目和学校对应关系(1:一对一 0：一对多) 
    "SnCode.SendWay": "2", //二维码发放方式（1：学校代发；2：运营商发放; 
    "Center.ClientKey": "03520541E76A457198510BE69743D515", //    
    "Current.Classify.Server.IsOpenRemotelyClassify": "1", //是否开启远程分类  远程分类服务器地址:http://a.cneefix.com。  服务器公钥 
    "Center.Classify.Server.Address": "http://localhost:33791", // 
    "Classify.Server.PublicKey": "AwEAAaXLcq1MxzbuLIOT2IA1txsykR997Fwh3hP4mHD9zmsVQpeZ3pg+yRA3vMjNPkHmbFCpqVDITBjiLTgHSOUF3vgf8bTVoNYDZqrfv2tHVbg20VE/WgxIWjZtDMU06E1MdYS5eOQhe9KB5ooP6K86ov0rIt5g/IPotpDSzmN12DPZ", // 
    "Center.Sms.Server.IsOpenGetResult": "1", //是否开启向中心获取短信回复结果 
    "dd.AgentId": "1295122653", //富阳钉钉对接 
    "dd.AppKey": "dingdbp063ajegcoegn7", // 
    "dd.AppSecret": "k7wHMCHSKe_vrIEcjQsHaMvHXNmj4Z9yO_sLW01Pih7YsMpI_JXLVcg_qsBgEpNT", // 
    "dd.Corpid": "dingd84c4bbed0167b4635c2f4657eb6378f", // 
    "dysoftauth.authHost": "http://api.weixiaojia.cn", //南京十三中 微研平台 
    "dysoftauth.dataHost": "http://api.weixiaojia.cn", // 
    "dysoftauth.clientID": "SGj51yax6M", // 
    "dysoftauth.clientSecret": "1330ee11-d6f6-4521-bc4c-1238d62b86e7", // 
    "dysoftauth.callBackUrl": "http://www.cneefix.cn/AccountThird/index_ssz.aspx?params", // 
    "dysoftauth.Logout": "https://www.zhxiaoyuan.cn/#/main/homePage/index", // 
    "SSO.Client.Url2": "http://localhost:58023/Account/Index?t=", // 
    "Assets.OpenEntrance": "0", //资产监管开放采购入库接口 0：不接入  1：接入 
    "Assets.OpenUrl": "http://*************:8090/", //资产监管开放接口URL(南京、富阳共用) 
    "Assets.OpenCode": "fyjyjzcxt", //资产监管开放接口授权码(南京、富阳共用，对应富阳sysCode值) 
    "Assets.Authority": "d2ViLDM2MDAsNitVTWhRYmtZV21kV090OUlPZ0tkcz", //富阳固定资产Authority值 
    "Assets.RgCode": "330111", //富阳rgCode值 
    "Assets.BeginDate": "2020-01-01", //固定资产审核入库界定日期 
    "Project.DefinitionDate": "2020-02-26", //企业二维码申领界定日期 
    "LowValue.OpenUrl": "ws://ebox.hfkj58.com/ebox-seller/ws", //物流柜接口URL(Socket请求) 
    "LowValue.DataLoadUrl": "http://ebox.hfkj58.com/ebox-seller/api/school/", //物流柜上传存放物品信息URL(Http请求) 
    "DangerChemicals.ZbUrl": "http://**********:1580", //中爆危化品平台接口URL(Http请求) 
    "DangerChemicals.ZbId": "weihuapin", //中爆危化品平台接口Id 
    "DangerChemicals.ZbUserName": "school", //中爆危化品平台接口用户名 
    "DangerChemicals.ZbPwd": "szxx123", //中爆危化品平台接口密码 
    "DangerChemicals.ApiCallType": "2", //危化品平台接口对接方式1：直报（直接调用中爆平台接口） 2：转报（调用中心接口转报中爆平台）
    "WeixinMessageSend.IsOpen": "1", //是否开启微信小程序消息推送.1：推送；0：不推送 
    "WxOpenAppId": "wxdfa9ad256aa6d472", // 微信小程序秘钥信息  
    "WxOpenAppSecret": "cf9a22e02b53f31780b2ca8121fe07ce", // 
    "Fy.Mf.OpenUrl": "http://122.224.134.157:12380/", //富阳魔方、图书、资产 
    "Fy.Mf.Authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJkYXRhQ2VudGVyIiwiaXNzIjoie1wiZWFzeUFwaVVzZXJJZFwiOjI1fSIsImV4cCI6MTk3OTI3MTgxMSwiaWF0IjoxNjYzOTExODExLCJrZXkiOiJkYXRhQ2VudGVyIn0.aoebxZYjFnAf_COYVbXB-laoQGJSP8dauz6_44t9LprkxKUZBWeUPlSk3MJ2YK2un_tQ0o5cD3OrogeYOM5Okg", // 
    "Fy.Mf.ApiToken": "9418DECD6F6948720FE8EE5D929104A3203DF74C23A975FDCC021C1860BEE1A2", // 
    "Fy.Ts.OpenUrl": "http://fyimportservice.tylibrary.com/Service1.svc", // 
    "Fy.Ts.AppKey": "FYAdmin", // 
    "Fy.Ts.AppSecret": "FY88888888", // 
    "Fy.Zc.OpenUrl": "https://zams.assetcloud.zj.gov.cn", // 
    "Fy.Zc.AppKey": "fyjyj", // 
    "Fy.Zc.AppSecret": "9be0c53a-37bd-11ed-9686-00163e001" // 

  }
}