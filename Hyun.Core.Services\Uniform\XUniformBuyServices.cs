﻿using AutoMapper;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.IServices.Uniform;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.Validator;
using NetTaste;
using Org.BouncyCastle.Bcpg.OpenPgp;
using System.Collections.Generic;
using System.Reflection;
namespace Hyun.Core.Services
{

    ///<summary>
    ///XUniformBuy方法
    ///</summary>
    public class XUniformBuyServices : BaseServices<XUniformBuy>, IXUniformBuyServices
    {
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformPurchaseServices uniformPurchaseManager;
        public XUniformBuyServices(IMapper _mapper, IUser _user, IXUniformPurchaseServices _uniformPurchaseManager)
        {
            mapper = _mapper;
            user = _user;
            uniformPurchaseManager = _uniformPurchaseManager;
        }
        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="o">XUniformBuy对象</param>
        /// <returns></returns>
        public async Task<Result<string>> InsertUpdate(XUniformBuyDto m)
        {
            XUniformBuy o = mapper.Map<XUniformBuy>(m);

            #region 增加FluentValidation验证
            var validator = new XUniformBuyValidator();
            var result = validator.Validate(o);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                return Result<string>.Fail(tipMsg);
            }
            #endregion

            string msg = "保存成功";

            if(o.PlanYear == 0)
            {
                return Result<string>.Fail("年度必须选择");
            }

            if (o.Id > 0)
            {
                var obj = await this.Db.Queryable<XUniformBuy>().Where(f => f.Id == o.Id).FirstAsync();
                if(obj == null)
                {
                    return Result<string>.Fail("未查询到数据");
                }
                if(obj.PurchaseStatuz != UniformFilingEnum.SubmitNone.ToEnumInt())
                {
                    return Result<string>.Fail("当前状态不可修改");
                }

                o.SchoolId = obj.SchoolId;
                o.CountyId = obj.CountyId;
                o.PurchaseNo = obj.PurchaseNo;
                o.PurchaseStatuz = obj.PurchaseStatuz;

                if (obj.PlanYear != o.PlanYear)
                {
                    //处理编码
                    string code = await uniformPurchaseManager.GenerateCode(3, o.PlanYear);
                    o.PurchaseNo = code;
                }

                await base.Update(o);
            }
            else
            {
                o.Id = BaseDBConfig.GetYitterId();
                //处理编码
                string code = await uniformPurchaseManager.GenerateCode(3, o.PlanYear);
                o.PurchaseNo = code;
                o.SchoolId = user.UnitId;
                o.CountyId = user.UnitPId;
                o.IsFiling = 0;
                o.PurchaseStatuz = UniformFilingEnum.SubmitNone.ToEnumInt();
                await base.Add(o);
            }

            //处理附件
            if (m.ListAttachmentId.Count > 0)
            {
                List<BAttachmentData> listAttData = await this.Db.Queryable<BAttachmentData>().Where(f => f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.PurchaseApply.ObjToInt() && f.UnitId == user.UnitId && m.ListAttachmentId.Contains(f.Id)).ToListAsync();
                List<BAttachment> listAddFile = new List<BAttachment>();
                foreach(BAttachmentData b in listAttData)
                {
                    listAddFile.Add(new BAttachment()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        MainId = 0,
                        ObjectId = o.Id,
                        ModuleType = ModuleTypeEnum.PurchaseApply.ObjToInt(),
                        Title = b.Title,
                        Path = b.Path,
                        Width = b.Width,
                        Height = b.Height,
                        DocType = 0,
                        IsDefault = 0,
                        Remark = "",
                        UserId = user.ID,
                        UnitId = user.UnitId,
                        FileCategory = b.FileCategory,
                        IsDelete = 0,
                        Ext = b.Ext,
                        AttachmentDataId = b.Id
                    });
                }

                if (listAddFile.Count > 0)
                {
                    await this.Db.Insertable<BAttachment>(listAddFile).ExecuteCommandAsync();
                }
            }

            //如果为提交
            if(m.ButtonType == 1)
            {
                o.PurchaseStatuz = UniformFilingEnum.Filinged.ObjToInt();

                //验证附件必填
                string errorMsg = string.Empty;

                if (o.PlanYear == 0)
                {
                    errorMsg += "年度不能为空\n";
                }
                if (o.PreparationDate == null)
                {
                    errorMsg += "采购需求编制日期不能为空\n";
                }
                if (o.Num == 0)
                {
                    errorMsg += "校服数量必须大于0\n";
                }
                if (o.BudgetAmount == 0)
                {
                    errorMsg += "预算金额必须大于0\n";
                }

                if (o.IsNeedBidding == 0)
                {
                    errorMsg += "请选择是否需要招标\n";
                }

                if (o.IsNeedBidding == 1)
                {
                    if (o.Organizational == 0)
                    {
                        errorMsg += "请选择组织形式\n";
                    }
                    if (o.BidPublic == 0)
                    {
                        errorMsg += "请选择招标公告公开\n";
                    }
                    if (o.Method == 0)
                    {
                        errorMsg += "请选择采购方式\n";
                    }
                    if (o.PublicDate == null)
                    {
                        errorMsg += "请选择公开日期\n";
                    }
                    if (string.IsNullOrEmpty(o.PublicMediaName))
                    {
                        errorMsg += "请填写公开媒体名称\n";
                    }
                }

                List<BAttachment> listAttach = await this.Db.Queryable<BAttachment>().Where(f => f.IsDeleted == false && f.ObjectId == o.Id && f.ModuleType == ModuleTypeEnum.PurchaseApply.ToEnumInt() && f.UnitId == user.UnitId).ToListAsync();
                List<BAttachmentConfig> listConfig = await this.Db.Queryable<BAttachmentConfig>().Where(f => f.IsDeleted == false && f.IsFilled == 1 && f.ModuleType == ModuleTypeEnum.PurchaseApply.ToEnumInt()).ToListAsync();
                foreach (BAttachmentConfig config in listConfig)
                {
                    if (o.IsNeedBidding == 2 && config.FileCategory == 107001)
                    {
                        continue;
                    }
                    else
                    {
                        if (!listAttach.Exists(f => f.FileCategory == config.FileCategory))
                        {
                            errorMsg += $"“{config.Name}”未上传\n";
                        }
                    }

                }

                if (!string.IsNullOrEmpty(errorMsg))
                {
                    return Result<string>.Fail(errorMsg);
                }

                //判断是否需要审核
                var entityConfigList = await this.Db.Queryable<XUniformConfig>().Where((config) => (config.UnitId == user.UnitPId || config.UnitId == 0) && config.Code == "2000" && config.DicValue == "202").ToListAsync();
                if (entityConfigList != null && entityConfigList.Count() > 0)
                {
                    var entityTempconfig = entityConfigList.Where(m => m.UnitId == user.UnitPId).FirstOrDefault();
                    if (entityTempconfig != null)
                    {
                        if (entityTempconfig.ValueNum == 1)
                        {
                            o.PurchaseStatuz = UniformFilingEnum.Wait.ObjToInt();
                        }
                    }
                    else if (entityConfigList.FirstOrDefault().ValueNum == 1)
                    {
                        o.PurchaseStatuz = UniformFilingEnum.Wait.ObjToInt();
                    }
                }

                await this.Db.Updateable<XUniformBuy>(o).ExecuteCommandAsync();
                msg = "提交成功";
            }

            return Result<string>.Success(msg, o.PurchaseNo, 1);
        }

        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> Submit(long id)
        {
            var obj = await this.Db.Queryable<XUniformBuy>().Where(f => f.Id == id && f.SchoolId == user.UnitId).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.PurchaseStatuz != UniformFilingEnum.SubmitNone.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可提交");
            }
            //验证附件必填
            string errorMsg = string.Empty;

            if(obj.PlanYear == 0)
            {
                errorMsg += "年度不能为空\n";
            }
            if(obj.PreparationDate == null)
            {
                errorMsg += "采购需求编制日期不能为空\n";
            }
            if(obj.Num == 0)
            {
                errorMsg += "校服数量必须大于0\n";
            }
            if (obj.BudgetAmount == 0)
            {
                errorMsg += "预算金额必须大于0\n";
            }

            if(obj.IsNeedBidding == 0)
            {
                errorMsg += "请选择是否需要招标\n";
            }

            if(obj.IsNeedBidding == 1)
            {
                if(obj.Organizational == 0)
                {
                    errorMsg += "请选择组织形式\n";
                }
                if(obj.BidPublic == 0)
                {
                    errorMsg += "请选择招标公告公开\n";
                }
                if(obj.Method == 0)
                {
                    errorMsg += "请选择采购方式\n";
                }
                if(obj.PublicDate == null)
                {
                    errorMsg += "请选择公开日期\n";
                }
                if (string.IsNullOrEmpty(obj.PublicMediaName))
                {
                    errorMsg += "请填写公开媒体名称\n";
                }
            }

            List<BAttachment> listAttach = await this.Db.Queryable<BAttachment>().Where(f => f.IsDeleted == false && f.ObjectId == id && f.ModuleType == ModuleTypeEnum.PurchaseApply.ToEnumInt() && f.UnitId == user.UnitId).ToListAsync();
            List<BAttachmentConfig> listConfig = await this.Db.Queryable<BAttachmentConfig>().Where(f => f.IsDeleted == false && f.IsFilled == 1 && f.ModuleType == ModuleTypeEnum.PurchaseApply.ToEnumInt()).ToListAsync();
            foreach(BAttachmentConfig config in listConfig)
            {
                if(obj.IsNeedBidding == 2 && config.FileCategory == 107001)
                {
                    continue;
                }
                else
                {
                    if (!listAttach.Exists(f => f.FileCategory == config.FileCategory))
                    {
                        errorMsg += $"“{config.Name}”未上传\n";
                    }
                }
                
            }

            if (!string.IsNullOrEmpty(errorMsg))
            {
                return Result<string>.Fail(errorMsg);
            }
            //
            obj.PurchaseStatuz = UniformFilingEnum.Filinged.ObjToInt();
            //判断是否需要审核
            var entityConfigList = await this.Db.Queryable<XUniformConfig>().Where((config) => (config.UnitId == user.UnitPId || config.UnitId == 0) && config.Code == "2000" && config.DicValue == "202").ToListAsync();
            if (entityConfigList != null && entityConfigList.Count() > 0)
            {
                var entityTempconfig = entityConfigList.Where(m => m.UnitId == user.UnitPId).FirstOrDefault();
                if (entityTempconfig != null)
                {
                    if (entityTempconfig.ValueNum == 1)
                    {
                        obj.PurchaseStatuz = UniformFilingEnum.Wait.ObjToInt();
                    }
                }
                else if (entityConfigList.FirstOrDefault().ValueNum == 1)
                {
                    obj.PurchaseStatuz = UniformFilingEnum.Wait.ObjToInt();
                }
            }

            await this.Db.Updateable<XUniformBuy>(obj).ExecuteCommandAsync();

            return Result<string>.Success("提交成功");
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> DeleteById(long id)
        {
            var obj = await this.Db.Queryable<XUniformBuy>().Where(f => f.Id == id && f.SchoolId == user.UnitId).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.PurchaseStatuz != UniformFilingEnum.SubmitNone.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可删除");
            }

            //判断是否被关联

            //

            await this.Db.Updateable<XUniformBuy>().SetColumns(f => new XUniformBuy() { IsDeleted = true}).Where(f => f.Id == id).ExecuteCommandAsync();

            return Result<string>.Success("删除成功");
        }

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> Audit(BuyAuditModel o)
        {
            var obj = await this.Db.Queryable<XUniformBuy>().Where(f => f.Id == o.Id).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.PurchaseStatuz != UniformFilingEnum.Wait.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可审核");
            }
            var objUnit = await this.Db.Queryable<PUnit>().Where(f => f.Id == obj.SchoolId).FirstAsync();
            if (objUnit == null)
            {
                return Result<string>.Fail("当前数据单位信息不存在");
            }
            if (objUnit.PId != user.UnitId)
            {
                return Result<string>.Fail("非本单位的下属单位禁止操作");
            }
            if (o.Statuz == 2 && string.IsNullOrEmpty(o.FilingExplanation))
            {
                return Result<string>.Fail("审核不通过，必须填写原因");
            }

            if (o.Statuz == 1)
            {
                obj.IsFiling = 1;
                obj.PurchaseStatuz = UniformFilingEnum.Filinged.ToEnumInt();
            }
            else
            {
                obj.PurchaseStatuz = UniformFilingEnum.SubmitNone.ToEnumInt();
                obj.FilingExplanation = o.FilingExplanation;
            }
            await this.Db.Updateable<XUniformBuy>(obj).ExecuteCommandAsync();

            //插入记录表 UniformAuditLog
            var entityLog = new XUniformAuditLog();
            entityLog.Id = BaseDBConfig.GetYitterId();
            entityLog.UniformId = o.Id;
            entityLog.AuditTime = DateTime.Now;
            entityLog.AduitStatuz = obj.PurchaseStatuz;
            entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Buy.ObjToInt().ToString();
            entityLog.AuditExplain = $"审核：{o.FilingExplanation},结果：{o.Statuz}";
            entityLog.IsCurrent = 1;
            await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();

            return Result<string>.Success("审核成功");
        }

        /// <summary>
        /// 撤销、撤回
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> Revoke(BuyRevokeModel o)
        {
            var obj = await this.Db.Queryable<XUniformBuy>().Where(f => f.Id == o.Id).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("未查询到数据");
            }
            if (obj.PurchaseStatuz != UniformFilingEnum.Filinged.ToEnumInt())
            {
                return Result<string>.Fail("当前状态不可撤销");
            }
            var objUnit = await this.Db.Queryable<PUnit>().Where(f => f.Id == obj.SchoolId).FirstAsync();
            if(objUnit == null)
            {
                return Result<string>.Fail("当前数据单位信息不存在");
            }
            if(objUnit.PId != user.UnitId)
            {
                return Result<string>.Fail("非本单位的下属单位禁止操作");
            }
            string explain = string.Empty;
            if(o.OptType == 1)
            {
                obj.PurchaseStatuz = UniformFilingEnum.Wait.ObjToInt();
                explain = $"撤销：{o.FilingExplanation}";
            }
            else
            {
                obj.PurchaseStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
            }
            
            obj.IsFiling = 0;
            obj.FilingExplanation = "";
            await this.Db.Updateable<XUniformBuy>(obj).ExecuteCommandAsync();

            //插入记录表 UniformAuditLog
            var entityLog = new XUniformAuditLog();
            entityLog.Id = BaseDBConfig.GetYitterId();
            entityLog.UniformId = o.Id;
            entityLog.AuditTime = DateTime.Now;
            entityLog.AduitStatuz = obj.PurchaseStatuz;
            entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Buy.ObjToInt().ToString();
            entityLog.AuditExplain = explain;
            entityLog.IsCurrent = 1;
            await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();

            return Result<string>.Success("操作成功");
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">XUniformBuyParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<XUniformBuy>> GetPaged(XUniformBuyParam param)
        {
            PageModel<XUniformBuy> pageList = new PageModel<XUniformBuy>();
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> totalCount = 0;
            var list = await this.Db.Queryable<XUniformBuy>()
                .InnerJoin<PUnit>((UB, U) => UB.SchoolId == U.Id)
                .InnerJoin<PUnit>((UB, U, UC) => U.PId == UC.Id)
                .LeftJoin<BArea>((UB, U, UC, B) => UC.AreaId == B.Id)
                .Where((UB, U, UC, B) => UB.IsDeleted == false && U.IsDeleted == false)
                .WhereIF(user.UnitTypeId == 3, (UB, U, UC, B) => U.Id == user.UnitId)
                .WhereIF(user.UnitTypeId == 2, (UB, U, UC, B) => U.PId == user.UnitId)
                .WhereIF(user.UnitTypeId == 1, (UB, U, UC, B) => UC.PId == user.UnitId)
                .WhereIF(param.SchoolId > 0, (UB, U, UC, B) => UB.SchoolId == param.SchoolId)
                .WhereIF(param.CountyId > 0, (UB, U, UC, B) => UC.Id == param.CountyId)
                .Select((UB, U, UC, B) => new XUniformBuy()
                {
                    Id = UB.Id,
                    IsDeleted = UB.IsDeleted,
                    SchoolId = UB.SchoolId,
                    SchoolName = U.Name,
                    AreaName = B.Name,
                    CountyId = UB.CountyId,
                    PlanYear = UB.PlanYear,
                    PurchaseNo = UB.PurchaseNo,
                    Num = UB.Num,
                    BudgetAmount = UB.BudgetAmount,
                    IsNeedBidding = UB.IsNeedBidding,
                    Organizational = UB.Organizational,
                    OrganizationalName = UB.OrganizationalName,
                    BidPublic = UB.BidPublic,
                    Method = UB.Method,
                    MethodName = UB.MethodName,
                    PublicDate = UB.PublicDate,
                    PublicMediaName = UB.PublicMediaName,
                    IsFiling = UB.IsFiling,
                    PurchaseStatuz = UB.PurchaseStatuz,
                    PurchaseStatuzName = SqlFunc.IF(UB.PurchaseStatuz == 10).Return("待审核").ElseIF(UB.PurchaseStatuz == 100).Return("已备案").End("待备案 ")
                })
                .MergeTable()
                .WhereIF(expression!=null,expression)
                .OrderByIF(!string.IsNullOrEmpty(orderByFields),orderByFields)
                .ToPageListAsync(param.pageIndex, param.pageSize, totalCount); ;
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }

        /// <summary>
        /// 校服采购-删除附件
        /// </summary>
        /// <param name="model">撤销信息</param>
        /// <returns></returns>
        public async Task<Result> UpdateAttachmentDelete(XUniformBuyDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前校服采购数据已不存在，请刷新重新操作。";
                return r;
            }
            var listAddAttachment = new List<BAttachment>();
            var entityAttachment = await this.Db.Queryable<BAttachment>()
            .Where((att) => att.Id == model.AttachmentId && att.ObjectId == entity.Id && att.ModuleType == ModuleTypeEnum.PurchaseApply.ObjToInt() && att.IsDeleted == false).FirstAsync();

            if (entityAttachment == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前附件已不存在，请刷新重新操作。";
                return r;
            }
            var resultNo = await this.Db.Updateable<BAttachment>().SetColumns((att) => new BAttachment()
            {
                IsDeleted = true,
                ModifyTime = DateTime.Now,
                ModifyBy = model.CreateBy,
                ModifyId = model.CreateId
            })
            .Where((att) => att.Id == entityAttachment.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                r.flag = 1;
                r.msg = "删除成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "删除失败,请刷新重试，如无法解决请联系客服协助处理！";
            }
            return r;
        }


        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">XUniformBuyParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<XUniformBuy, bool>> ListFilter(XUniformBuyParam param)
        {
            var expression = LinqExtensions.True<XUniformBuy>();
            //if(user.UnitTypeId == 3)
            //{
            //    expression = expression.AndNew(f => f.SchoolId == user.UnitId);
            //}
            //else if(user.UnitTypeId == 2)
            //{
            //    expression = expression.AndNew(f => f.CountyId == user.UnitId);
            //}
            //else
            //{
            //    expression = expression.AndNew(f => f.SchoolId == 0);
            //}
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
                if (!string.IsNullOrEmpty(param.PurchaseNo))
                {
                    expression = expression.AndNew(f => f.PurchaseNo.Contains(param.PurchaseNo));
                }
                if(param.PlanYear > 0)
                {
                    expression = expression.AndNew(f => f.PlanYear == param.PlanYear);
                }
                if (param.IsNeedBidding > 0)
                {
                    expression = expression.AndNew(f => f.IsNeedBidding == param.IsNeedBidding);
                }
                if (param.Organizational > 0)
                {
                    expression = expression.AndNew(f => f.Organizational == param.Organizational);
                }
                if (param.PurchaseStatuz > -1)
                {
                    expression = expression.AndNew(f => f.PurchaseStatuz == param.PurchaseStatuz);
                }
            }
            return expression;
        }
        #endregion
    }
}

