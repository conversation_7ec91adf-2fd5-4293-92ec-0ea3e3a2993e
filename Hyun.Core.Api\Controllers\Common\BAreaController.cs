﻿using Hyun.Core.Common.HttpContextUser;
using NPOI.SS.Formula.PTG;
using SqlSugar;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace Hyun.Core.Api
{

    [Route("api/hyun/barea")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class BAreaController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IBAreaServices areaManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IVCityRoutingServices vCityRoutingManager;
        private readonly IBCityRoutingServices cityRoutingManager;
        private readonly IPUnitServices unitManager;
        private readonly IVUnitServices vUnitManager;
        private readonly IUser user;

        public BAreaController(IMapper _mapper, IWebHostEnvironment _env, IBAreaServices _areaManager, IBDictionaryServices _dictionaryManager, IVCityRoutingServices _vCityRoutingManager, IBCityRoutingServices _cityRoutingManager, IPUnitServices _unitManager, IVUnitServices _vUnitManager,IUser _user)
        {
            mapper = _mapper;
            env = _env;
            areaManager = _areaManager;
            dictionaryManager = _dictionaryManager;
            vCityRoutingManager = _vCityRoutingManager;
            cityRoutingManager = _cityRoutingManager;
            unitManager = _unitManager;
            vUnitManager = _vUnitManager;
            user = _user;
        }

        /// <summary>
        /// 根据当前单位Id，获取下属单位信息，如果为超管则获取市、区
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("areafind")]
        public async Task<Result> Area_Find()
        {
            Result r = new Result();
            if (user.IsSystemUser)
            {
                List<PUnit> listUnit = await unitManager.Query(f => f.Statuz == 1 && f.IsDeleted == false && (f.UnitType == 1 || f.UnitType == 2));
                List<dropdownModel> listTree = listUnit.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name, pid = f.PId == 0 ? null : f.PId }).ToList();
                var list = BuildDropDownModelTree(listTree);
                r.data.rows = list;
                r.flag = 1;
                r.msg = "查询成功";
            }
            else if (user.Roles.Contains(RoleTypes.CityAdmin))
            {
                List<PUnit> listUnit = await unitManager.Query(f => f.PId == user.UnitId && f.Statuz == 1 && f.IsDeleted == false);
                List<dropdownModel> list = listUnit.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();
                r.data.rows = list;
                r.flag = 1;
                r.msg = "查询成功";
            }
            return r;
        }

        /// <summary>
        /// 获取区县名称
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getareanamefind")]
        //<used>1</used>
        public async Task<Result> GetAreaName_Find()
        {
            Result r = new Result();
            List<BArea> list = await areaManager.Find(t => t.Pid == user.AreaId);
            int listCount = list.Count;
            if (listCount > 0)
            {
                list.RemoveAt(listCount - 1);
                list.RemoveAt(listCount - 2);
                r.data.total = listCount - 2;
                r.data.rows = list.Select(t => new { t.Id, t.Name });
            }
            else
            {
                r.data.total = listCount;
            }
            r.flag = 1;
           
            return r;
        }


        /// <summary>
        /// 根据区域Id查询区域完整名称
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("areagetbyid")]
        //<used>1</used>
        public async Task<Result> Area_GetById(long id)
        {
            Result r = new Result();
            BArea o = new BArea();
            List<BArea> listArea =await areaManager.Query(f => f.Id == id);
            if (listArea.Count > 0)
            {
                o = listArea[0];
                if (o.Path.Contains("-"))
                {
                    string[] strPath = o.Path.Split('-');
                    List<BArea> list = await areaManager.QueryByIDs(strPath);
                    o.FullName = string.Join(",", list.Select(f => f.Name));
                }
            }
            //VArea o = vAreaManager.Query(f => f.Id == id).Result.FirstOrDefault();
            //if (o != null && o.Path.Contains("-"))
            //{
            //    string[] strPath = o.Path.Split('-');
            //    List<BArea> list = await areaManager.QueryByIDs(strPath);
            //    o.FullName = string.Join(",", list.Select(f => f.Name));

            //}
            r.flag = 1;
            r.data.total = 1;
            r.data.rows = o;
            return r;
        }


        /// <summary>
        /// 根据区域父级Id查询区域信息
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("areagetbypid")]
        //<used>1</used>
        public async Task<Result> Area_GetByPid(long pid)
        {
            Result r = new Result();
            //BAreaParam areaParam = new BAreaParam()
            //{
            //    PId = pid,
            //    pageSize = int.MaxValue,
            //    sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Sort", SortType = "ASC" }, new SortBaseModel { SortCode = "Id", SortType = "ASC" } },
            //};
            //PageModel<BArea> pg = await areaManager.GetPaged(areaParam);
           var listArea = await areaManager.Query(t => t.Pid == pid);
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = listArea;
            return r;
        }


        /// <summary>
        /// 根据单位Id获取单位所属区域信息
        /// </summary>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getareabyunitid")]
        //<used>1</used>
        public async Task<Result> GetAreaByUnitId(long UnitId)
        {
            Result r = new Result();
            BAreaDto areaDto = new BAreaDto();
            areaDto.ProvinceId = 818;
            areaDto.CityId = 819;
            areaDto.CountyId = 820;
            areaDto.ProvinceName = "江苏省";
            areaDto.CityName = "南京市";
            areaDto.CountyName = "玄武区";
            r.data.rows = areaDto;

            PUnit unit =await unitManager.QueryById(UnitId);
            if (unit != null)
            {
                areaDto = await vUnitManager.GetDefaultAreaByUnitId(UnitId);
                if (areaDto != null)
                {
                    r.data.rows = areaDto;
                }
            }
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }


        #region 私有方法
        /// <summary>
        /// 获取树数据
        /// </summary>
        /// <param name="flatList"></param>
        /// <returns></returns>
        private List<dropdownModel> BuildDropDownModelTree(List<dropdownModel> flatList)
        {
            var lookup = flatList.ToDictionary(n => n.value);
            var rootNodes = new List<dropdownModel>();

            foreach (var node in flatList)
            {
                if (node.pid == null)
                {
                    // 这是根节点，没有父节点
                    rootNodes.Add(node);
                }
                else if (lookup.ContainsKey(node.pid.ToString()))
                {
                    // 将当前节点添加到其父节点的子节点列表中
                    lookup[node.pid.ToString()].children.Add(node);
                }
                // 如果PID在列表中不存在，则忽略该节点或进行其他处理
            }

            return rootNodes;
        }
        #endregion

    }
}
