﻿
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models.Uniform;
using Hyun.Core.IServices.Uniform;
using Microsoft.AspNetCore.Mvc;
using Hyun.Core.Services.Uniform;
using Hyun.Core.Model;
using Hyun.Core.Services;
using Hyun.Core.Model.Models;
using Hyun.Old.Util;
using Com.Ctrip.Framework.Apollo.Enums;
using Dm;
using Hyun.Core.Model.Models.Workflow;

namespace Hyun.Core.Api.Controllers.Uniform
{

    [Route("api/hyun/xuniformbidding")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class UniformBiddingController : BaseApiController
    {


        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformBiddingServices uniformBiddingManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IBAttachmentServices attachmentManager;
        private readonly IPUnitServices unitManager;
        private readonly IBAreaServices areaManager;

        public UniformBiddingController(IWebHostEnvironment _env, IMapper _mapper, IUser _user, IXUniformBiddingServices _uniformBiddingManager, IBDictionaryServices _dictionaryManager, IBAttachmentServices _attachmentManager, IPUnitServices _unitManager, IBAreaServices _areaManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            uniformBiddingManager = _uniformBiddingManager;
            dictionaryManager = _dictionaryManager;
            attachmentManager = _attachmentManager;
            unitManager = _unitManager;
            areaManager = _areaManager;
        }

        /// <summary>
        ///  招标结果列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getuniformbiddinglist")]
        public async Task<Result<List<XUniformBiddingDto>>> GetUniformBiddingList([FromBody] XUniformBiddingParam param)
        {
            var msgdata = new Result<List<XUniformBiddingDto>>();
            PageModel<XUniformBidding> pg = await uniformBiddingManager.GetPaged(param);
            if (param.isFirst)
            {
                //招标结果状态
                var listEnumUsage = EnumExtensions.EnumToList<UniformFilingEnum>();
                List<dropdownModel> listPurchaseStatuz = listEnumUsage.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //开标结果公开
                var listEnumBidPublic = EnumExtensions.EnumToList<BidPublic>();
                List<dropdownModel> listPublic = listEnumBidPublic.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //获取采购方式
                List<BDictionary> listMethod = await dictionaryManager.GetByTypeCode("507000");
                List<dropdownModel> listMethodDropdown = listMethod.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                if (user.UnitTypeId == 3)
                {
                    msgdata = baseSucc(mapper.Map<List<XUniformBiddingDto>>(pg.data), pg.dataCount,
                  $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                  new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listMethod = listMethodDropdown });
                }
                else if (user.UnitTypeId == 2)
                {
                    var listSchoolInfo = await unitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    List<dropdownModel> listSchool = listSchoolInfo.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();
                    msgdata = baseSucc(mapper.Map<List<XUniformBiddingDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listMethod = listMethodDropdown, listSchool = listSchool });
                }
                else if (user.UnitTypeId == 1)
                {
                    //var listAreaInfo = await areaManager.Query(f => f.Pid == user.AreaId && f.IsDeleted == false);
                    //List<dropdownModel> listArea = listAreaInfo.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                    List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                    List<BArea> listArea = null;
                    var listCounty = await unitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        //获取所有区域Id
                        if (listArea == null)
                        {
                            listArea = await areaManager.Find(m => m.IsDeleted == false);
                        }
                        foreach (var item in listCounty)
                        {
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = GetAreaName(item.AreaId, item.Name, listArea) });
                        }
                    }

                    msgdata = baseSucc(mapper.Map<List<XUniformBiddingDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listMethod = listMethodDropdown, listArea = dropdownCounty });
                }
            }
            else
            {
                msgdata = baseSucc(mapper.Map<List<XUniformBiddingDto>>(pg.data), pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 保存修改招标结果
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("uniformbiddingsave")]
        public async Task<Result<string>> UniformBiddingSave([FromBody] XUniformBiddingDto o)
        {
            return await uniformBiddingManager.InsertUpdate(o);
        }

        /// <summary>
        /// 根据Id获取招标结果数据信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("uniformbiddinggetbyid")]
        public async Task<Result> UniformBiddingGetById(long id)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";

            //招标结果状态
            var listEnumUsage = EnumExtensions.EnumToList<UniformFilingEnum>();
            List<dropdownModel> listPurchaseStatuz = listEnumUsage.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

            //开标结果公开
            var listEnumBidPublic = EnumExtensions.EnumToList<BidPublic>();
            List<dropdownModel> listPublic = listEnumBidPublic.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

            //获取采购方式
            List<BDictionary> listMethod = await dictionaryManager.GetByTypeCode("507000");
            List<dropdownModel> listMethodDropdown = listMethod.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

            r.data.other = new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listMethod = listMethodDropdown };

            XUniformBidding obj = await uniformBiddingManager.GetByBuyId(id);
            if (obj != null)
            {
                r.data.rows = mapper.Map<XUniformBiddingDto>(obj);
                List<BAttachment> listAttach = await attachmentManager.Query(f => f.IsDelete == 0 && f.IsDeleted == false && f.ObjectId == obj.Id && f.ModuleType == ModuleTypeEnum.PurchaseBid.ToEnumInt());
                r.data.footer = listAttach;
            }
            return r;
        }

        /// <summary>
        /// 提交招标结果
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("uniformbiddingsubmit")]
        public async Task<Result<string>> UniformBiddingSubmit(long id)
        {
            return await uniformBiddingManager.Submit(id);
        }

        /// <summary>
        /// 招标结果删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("uniformbiddingdeletebyid")]
        public async Task<Result<string>> UniformBiddingDeleteById(long id)
        {
            return await uniformBiddingManager.DeleteById(id);
        }

        /// <summary>
        /// 招标结果审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("uniformbiddingaudit")]
        public async Task<Result<string>> UniformBiddingAudit([FromBody] BuyAuditModel o)
        {
            return await uniformBiddingManager.Audit(o);
        }

        /// <summary>
        /// 招标结果撤销、退回
        /// OptType（1：撤销 2：退回）
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("uniformbiddingrevoke")]
        public async Task<Result<string>> UniformBiddingRevoke([FromBody] BuyRevokeModel o)
        {
            return await uniformBiddingManager.Revoke(o);
        }

        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="id"></param>
        /// <param name="attid"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delattachmentbyid")]
        public async Task<Result> DeleteAttachmentById(long id, long attid)
        {
            Result r = new Result();
            var model = new XUniformBiddingDto();
            model.Id = id;
            model.AttachmentId = attid;
            model.SchoolId = user.UnitId;
            model.CreateId = user.UserId;
            r = await uniformBiddingManager.UpdateAttachmentDelete(model);
            return r;
        }


        /// <summary>
        /// 获取区域名称
        /// </summary>
        /// <param name="countyAreaId">区县区域Id</param>
        /// <param name="name">区县单位名称，默认值</param>
        /// <param name="listArea">区域集合</param>
        /// <returns></returns>
        private string GetAreaName(long countyAreaId, string name, List<BArea> listArea)
        {
            if (listArea != null && listArea.Where(m => m.Id == countyAreaId).Count() > 0)
            {
                name = listArea.Where(m => m.Id == countyAreaId).FirstOrDefault().Name;
            }
            return name;
        }
    }
}
