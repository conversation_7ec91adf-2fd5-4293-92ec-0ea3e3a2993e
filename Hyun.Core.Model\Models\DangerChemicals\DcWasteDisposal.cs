namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///废弃物处置主表
    ///</summary>
    [SugarTable("dc_WasteDisposal","废弃物处置主表")]
    public class DcWasteDisposal : BaseEntity
    {

          public DcWasteDisposal()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///处置批次
          /// </summary>
          [SugarColumn(Length = 31)]
          public string BatchNo { get; set; }

           /// <summary>
           ///申请人
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///申请时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///审核状态（0：待审核，1：审核通过，2：审核不通过，3：已处置 [指：已填报处置信息] ）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///审核意见
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string AuditRemark { get; set; }

           /// <summary>
           ///处置时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? DisposalDate { get; set; }

           /// <summary>
           ///处置备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string DisposalRemark { get; set; }

           /// <summary>
           ///处置过程材料
          /// </summary>
          [SugarColumn(Length = 2000,IsNullable = true)]
          public string ProcessFile { get; set; }

           /// <summary>
           ///处置过程照片
          /// </summary>
          [SugarColumn(Length = 2000,IsNullable = true)]
          public string ProcessImg { get; set; }

           /// <summary>
           ///审核人
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? AuditUserId { get; set; }

           /// <summary>
           ///审核时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? AuditDate { get; set; }

           /// <summary>
           ///处置企业名称
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string CompanyName { get; set; }

        /// <summary>
        ///处置来源（1：危废物[危废物填报]  2：危化品[危化品报废]）
        /// </summary>
        public int SourceType { get; set; } = 1;

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

