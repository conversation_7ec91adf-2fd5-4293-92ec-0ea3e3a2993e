﻿using Grpc.Core;
using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.SearchModels.Common;
using Hyun.Old.Util;
using log4net;
using Microsoft.AspNetCore.Identity;
using NetTaste;
using NPOI.HSSF.UserModel;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using NPOI.Util;
using SkyWalking.NetworkProtocol.V3;
using SqlSugar;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq.Expressions;
using System.Reflection.Metadata;
using System.Security.Principal;
using System.Text.RegularExpressions;

namespace Hyun.Core.Api
{
    [Route("api/hyun/puser")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PUserController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly ISysUserExtensionServices userManager;
        private readonly IPUserInDepartServices userInDepartManager;
        private readonly IVUserListServices vUserListManager;
        private readonly IVUserDetailServices vUserDetailManager;
        private readonly IPUnitServices unitManager;
        private readonly IVUserMobileInfoServices vUserMobileInfoManager;
        private readonly IOUserThirdUserServices userThirdManager;
        private readonly IBSmsHistoryValidateServices smsHistoryValidateManager;
        private readonly ISysUserInfoServices accountManager;
        private readonly IBUserActionLogServices userActionLogManager;
        private readonly IVUserMultAccountServices vUserMultAccountManager;
        private readonly IBAreaServices areaManager;
        private readonly IPUserInRoleServices userInRoleManager;
        private readonly IVWebSiteConfigServices vWebSiteConfigManager;
        private readonly IBWebSiteConfigServices webSiteConfigManager;
        private readonly IVSysStatServices vSysStatManager;
        private readonly IPSchoolExtensionServices schoolExtensionManager;
        private readonly IVSchoolInfoServices vSchoolInfoManager;
        private readonly IVAddressPlacePropertyServices vAddressPlacePropertyManager;
        private readonly IPUnitBankAccountServices unitBankAccountManager;
        private readonly IPRoleServices roleManager;
        private readonly ISysRoleServices sysRoleManager;
        private readonly IPUserMultAccountServices userMultAccountManager;
        private readonly IPDepartmentAuditConfigServices departmentAuditConfigManager;
        private readonly IPDepartmentServices departmentManager;
        private readonly IPDepartmentAuditUserServices departmentAuditUserManager;
        //private readonly IAAssetsEquipmentListServices assetsEquipmentListManager;
        //private readonly IAAssetsEquipmentBorrowServices assetsEquipmentBorrowManager;
        private readonly IPUnitExchangeServices unitExchangeManager;
        private readonly IBAttachmentDataServices attachmentDataManager;
        private readonly IConfiguration configuration;
        private readonly IUser user;
        private readonly IVUserListTempServices vUserListtManager;
        private readonly IBConfigSetServices congigSetManager;
        private readonly ISysUserRoleServices sysUserRoleManager;

        public PUserController(IMapper _mapper, IWebHostEnvironment _env, ISysUserExtensionServices _userManager, IPUserInDepartServices _userInDepartManager, IVUserListServices _vUserListManager, IVUserDetailServices _vUserDetailManager, IOUserThirdUserServices _userThirdManager, IPUnitServices _unitManager, IVUserMobileInfoServices _vUserMobileInfoManager, IBSmsHistoryValidateServices _smsHistoryValidateManager, ISysUserInfoServices _accountManager, IBUserActionLogServices _userActionLogManager, IVUserMultAccountServices _vUserMultAccountManager, IBAreaServices _areaManager, IPUserInRoleServices _userInRoleManager, IVWebSiteConfigServices _vWebSiteConfigManager, IBWebSiteConfigServices _webSiteConfigManager, IVSysStatServices _vSysStatManager, IPSchoolExtensionServices _schoolExtensionManager, IVSchoolInfoServices _vSchoolInfoManager, IVAddressPlacePropertyServices _vAddressPlacePropertyManager, IPUnitBankAccountServices _unitBankAccountManager, IPRoleServices _roleManager, IPUserMultAccountServices _userMultAccountManager, IPDepartmentAuditConfigServices _departmentAuditConfigManager, IPDepartmentServices _departmentManager, IPDepartmentAuditUserServices _departmentAuditUserManager,/* IAAssetsEquipmentListServices _assetsEquipmentListManager, IAAssetsEquipmentBorrowServices _assetsEquipmentBorrowManager,*/ IPUnitExchangeServices _unitExchangeManager
            , IBAttachmentDataServices _attachmentDataManager, IConfiguration _configuration, IUser _user, 
            IVUserListTempServices _vUserListtManager, IBConfigSetServices _congigSetManager, ISysRoleServices _sysRoleManager, ISysUserRoleServices _sysUserRoleManager)
        {
            mapper = _mapper;
            env = _env;
            userManager = _userManager;
            userInDepartManager = _userInDepartManager;
            vUserListManager = _vUserListManager;
            vUserDetailManager = _vUserDetailManager;
            userThirdManager = _userThirdManager;
            unitManager = _unitManager;
            vUserMobileInfoManager = _vUserMobileInfoManager;
            smsHistoryValidateManager = _smsHistoryValidateManager;
            accountManager = _accountManager;
            userActionLogManager = _userActionLogManager;
            vUserMultAccountManager = _vUserMultAccountManager;
            areaManager = _areaManager;
            userInRoleManager = _userInRoleManager;
            vWebSiteConfigManager = _vWebSiteConfigManager;
            webSiteConfigManager = _webSiteConfigManager;
            vSysStatManager = _vSysStatManager;
            schoolExtensionManager = _schoolExtensionManager;
            vSchoolInfoManager = _vSchoolInfoManager;
            vAddressPlacePropertyManager = _vAddressPlacePropertyManager;
            unitBankAccountManager = _unitBankAccountManager;
            roleManager = _roleManager;
            userMultAccountManager = _userMultAccountManager;
            departmentAuditConfigManager = _departmentAuditConfigManager;
            departmentManager = _departmentManager;
            departmentAuditUserManager = _departmentAuditUserManager;
            //assetsEquipmentListManager = _assetsEquipmentListManager;
            //assetsEquipmentBorrowManager = _assetsEquipmentBorrowManager;
            unitExchangeManager = _unitExchangeManager;
            attachmentDataManager = _attachmentDataManager;
            configuration = _configuration;
            user = _user;
            vUserListtManager = _vUserListtManager;
            congigSetManager = _congigSetManager;
            sysRoleManager = _sysRoleManager;
            sysUserRoleManager = _sysUserRoleManager;
        }

      

        [HttpPost]
        [Route("personfindidname")]
        //<used>1</used>
        public async Task<Result> Person_FindIdName([FromBody] SysUserExtensionParam param)
        {
            Result r = new Result();
            param.statuzgt = 0;
            param.unitId = user.UnitId;
            param.UserType = 1;
            PageModel<SysUserExtension> pg = await userManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        [HttpPost]
        [Route("userchangepass")]
        //<used>1</used>
        public async Task<Result> User_ChangePass([FromBody] UserPswdModel o)
        {
            Result r = new Result();
            r = await userManager.ChangePass(user.AcctId, o.OldPwd, o.NewPwd);
            return r;
        }


        [HttpPost]
        [Route("useredit")]
        //<used>1</used>
        public async Task<Result> User_Edit([FromBody] VUserDetail o)
        {
            Result r = new Result();
            o.Id = user.ID;
            r =await userManager.Edit(o);
            return r;
        }


        [HttpPost]
        [Route("usergetbyid")]
        //<used>1</used>
        public async Task<Result> User_GetById(long id)
        {
            Result r = new Result();
            VUserDetail userDetail = null;
            if (user.IsSystemUser)
            {
                userDetail = vUserDetailManager.Query(f => f.Id == id).Result.FirstOrDefault();
            }
            else if(user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin)
                || user.Roles.Contains(RoleTypes.SchoolAdmin) || user.Roles.Contains(RoleTypes.CompanyAdmin))
            {
                userDetail = vUserDetailManager.Query(f => f.Id == id && (f.UnitId == user.UnitId || f.UnitPId == user.UnitId)).Result.FirstOrDefault();
            }

            if (userDetail != null)
            {
                userDetail.DepartmentIds = "";
                if(user.UnitTypeId == UnitTypes.School.ObjToInt())
                {
                    List<PUserInDepart> listUserInDepartment =  await userInDepartManager.Query(f => f.UserId == userDetail.Id);
                    if (listUserInDepartment.Count > 0)
                    {
                        userDetail.DepartmentIds = string.Join(",", listUserInDepartment.Select(f => f.DepartmentId));
                    }
                }

                List<SysUserRole> listUserInRole = await sysUserRoleManager.Query(f => f.UserId == userDetail.Id);
                if (listUserInRole.Count > 0)
                {
                    userDetail.StrRoleIds = string.Join(",", listUserInRole.Select(f => f.RoleId));
                }

                r.flag = 1;
                r.data.rows = userDetail;
                r.msg = "查询成功";
            }

            return r;
        }


        [HttpPost]
        [Route("usersave")]
        //<used>1</used>

        public async Task<Result> User_Save([FromBody] VUserDetail o)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            if (!string.IsNullOrEmpty(o.AcctName))
            {
                if (ComLib.IsNumber(o.AcctName) && ComLib.GetStringLength(o.AcctName) < 8)
                {
                    r.flag = 0;
                    r.msg = "帐号如果为纯数字，必须大于等于8位！";
                    return r;
                }
            }
          
            if (string.IsNullOrEmpty(o.Sex))
            {
                o.Sex = "-1";
            }
            o.DepartmentIds = "-1";
            int userType = 1;
            if (user.IsSystemUser)
            {
                userType = o.UserType;
            }
            //增加判断，运维人员不能添加系统超管只能添加运维人员及其它单位类型账号
            string strRoleIds = "," + o.RoleIds + ",";
            SysUserExtension userInfo = await userManager.QueryById(user.ID);
            if (userInfo != null)
            {
                if (userInfo.AdministratorType != 0 && strRoleIds.Contains(",0,"))
                {
                    r.flag = 0;
                    r.msg = "运营超管不能添加系统超管";
                    return r;
                }
            }
            r =await unitManager.InsertUpdateUser(o);
            return r;
        }


        [HttpPost]
        [Route("usersavechildren")]
        //<used>1</used>
        public async Task<Result> User_SaveChildren([FromBody] VUserDetail o)
        {
            Result r = new Result();
            if (o.Id > 0)
            {
                VUserDetail po =await vUserDetailManager.GetByUserId(o.Id);
                if (po.UnitPId != user.UnitId)
                {
                    r.flag = 0;
                    r.msg = "执行失败。可能原因：非管理辖区用户";
                    r.data = null;
                    return r;
                }
            }
            o.UnitPId = user.UnitId;
            o.DepartmentIds = "-1";

            r = await unitManager.InsertUpdateUser(o);
            return r;
        }


        [HttpPost]
        [Route("usersavemyunit")]
        //<used>1</used>
        public async Task<Result> User_SaveMyUnit([FromBody] VUserDetail o)
        {
            Result r = new Result();

            //辖区判断
            if (o.Id > 0)
            {
                VUserDetail po = vUserDetailManager.Query(f => f.Id == o.Id).Result.FirstOrDefault();
                if (po.UnitId != user.UnitId)
                {
                    r.flag = 2;
                    r.msg = "执行失败。可能原因：用户单位错误，不可修改非本单位用户";
                    r.data = null;
                    return r;
                }
            }
            o.UnitId = user.UnitId;
            var list = o.StrRoleIds.Split(",");
            if (!user.IsSystemUser)
            {

                if (user.UnitTypeId == 1)
                {
                    if (o.StrRoleIds.Contains(",2") || o.StrRoleIds.Contains(",3") || o.StrRoleIds.Contains(",4"))
                    {
                        r.flag = 0;
                        r.msg = "非法操作";
                        return r;
                    }

                }
                else if (user.UnitTypeId == 2)
                {
                    if (o.StrRoleIds.Contains(",1") || o.StrRoleIds.Contains(",3") || o.StrRoleIds.Contains(",4"))
                    {
                        r.flag = 0;
                        r.msg = "非法操作";
                        return r;
                    }
                }
                else if(user.UnitTypeId == 3)
                {
                    if (o.StrRoleIds.Contains(",1") || o.StrRoleIds.Contains(",2") || o.StrRoleIds.Contains(",4"))
                    {
                        r.flag = 0;
                        r.msg = "非法操作";
                        return r;
                    }
                }
                else if(user.UnitTypeId == 4)
                {
                    if (o.StrRoleIds.Contains(",1") || o.StrRoleIds.Contains(",2") || o.StrRoleIds.Contains(",3"))
                    {
                        r.flag = 0;
                        r.msg = "非法操作";
                        return r;
                    }
                }
            }

            return await User_InsertUpdate(o);
        }

        /// <summary>
        /// 私有方法保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        //<used>1</used>
        private async Task<Result> User_InsertUpdate(VUserDetail o)
        {
            Result r = new Result();
            long id = o.Id;
            string strRoleIds = "," + o.StrRoleIds + ",";

            #region 增加单位超管数量限制
            if (user.Roles.Contains(RoleTypes.SchoolAdmin) && strRoleIds.Contains(",30,"))
            {
                bool isNeedCheck = false;
                long countyId = 0;
                int limitSchoolCount = -1;
                PUnit u =await unitManager.QueryById((object)user.UnitId);
                if (u != null)
                {
                    countyId = u.PId;
                }

                if (id > 0)
                {
                    List<SysUserRole> listUserInRole = sysUserRoleManager.Query(f => f.UserId == id).Result;
                    if (listUserInRole.Count > 0)
                    {
                        if(!listUserInRole.Exists(f=>f.RoleId == 30))
                        {
                            isNeedCheck = true;
                        }
                    }
                }
                else
                {
                    isNeedCheck = true;
                }

                if (isNeedCheck)
                {

                    string configValue = null;
                    var listConfigSet = await congigSetManager.Query(f => f.ModuleCode == "14" && f.TypeCode == "YHGLXXCGSL" && f.Statuz == 1);
                    BConfigSet config = listConfigSet.Where(f => f.UnitId == countyId).FirstOrDefault();
                    if(config == null)
                    {
                        config = listConfigSet.Where(f => f.UnitId == 0).FirstOrDefault();
                    }
                    if (config != null)
                    {
                        configValue = config.ConfigValue;
                    }
                    if (!string.IsNullOrEmpty(configValue))
                    {
                        int.TryParse(configValue, out limitSchoolCount);
                        if (limitSchoolCount != 0)
                        {
                            List<long> listUserId = sysUserRoleManager.Db.Queryable<SysUserRole>()
                         .InnerJoin<SysUserExtension>((ur, u) => ur.UserId == u.Id)
                         .Where((ur, u) => u.UnitId == user.UnitId && ur.RoleId == 30)
                         .Select((ur, u) => u.Id).ToList();
                            if(listUserId.Count >= limitSchoolCount)
                            {
                                r.flag = 0;
                                r.msg = string.Format("本单位最多只能有{0}个超管账号", limitSchoolCount);
                                return r;
                            }

                        }
                    }
                }
            }
            #endregion

            #region 超管修改自己账号信息
            if ((user.IsSystemUser
                || user.Roles.Contains(RoleTypes.CityAdmin)
                || user.Roles.Contains(RoleTypes.CoutyAdmin)
                || user.Roles.Contains(RoleTypes.SchoolAdmin)
                || user.Roles.Contains(RoleTypes.CompanyAdmin)) && user.ID == id)
            {
                bool isChangeRole = false;
                int roleId = 30;

                if (user.Roles.Contains(RoleTypes.SchoolAdmin) && !strRoleIds.Contains(",30,"))
                {
                    isChangeRole = true;
                }
                else if (user.Roles.Contains(RoleTypes.CoutyAdmin) && !strRoleIds.Contains(",20,"))
                {
                    isChangeRole = true;
                    roleId = 20;
                }
                else if (user.Roles.Contains(RoleTypes.CityAdmin) && !strRoleIds.Contains(",10,"))
                {
                    isChangeRole = true;
                    roleId = 10;
                }
                else if (user.Roles.Contains(RoleTypes.CompanyAdmin) && !strRoleIds.Contains(",40,"))
                {
                    isChangeRole = true;
                    roleId = 40;
                }
                else if (user.IsSystemUser && !strRoleIds.Contains(",0,"))
                {
                    isChangeRole = true;
                    roleId = 0;
                }

                if (isChangeRole)
                {
                    var listAdmin = accountManager.Db.Queryable<SysUserInfo>()
                        .InnerJoin<SysUserExtension>((a, u) => a.UserExtensionId == u.Id)
                        .InnerJoin<SysUserRole>((a, u, ur) => u.Id == ur.UserId)
                        .InnerJoin<PUnit>((a, u, ur, unit) => u.UnitId == unit.Id)
                        .Where((a, u, ur, unit) => u.Statuz == 1 && a.Statuz == 1 && u.UserType == 1 && u.UnitId == user.UnitId && ur.RoleId == roleId && u.Id != user.ID)
                        .WhereIF(roleId == 0, (a, u, ur, unit) => u.AdministratorType == 0).ToList();
                    if (listAdmin.Count == 0)
                    {
                        r.flag = 0;
                        r.msg = "本单位至少需要一个管理员，您取消自己账号管理员权限后本单位将不存在管理员，固不能取消！若确实要取消该账号管理员权限，请先添加一个管理员后再取消！";
                        return r;
                    }
                }
            }

            #endregion



            if (!user.IsThirdClient) //非统一身份认证的用户 判断帐号如果为纯数字，必须大于等于8位
            {
                if (!string.IsNullOrEmpty(o.AcctName))
                {
                    if (ComLib.IsNumber(o.AcctName) && ComLib.GetStringLength(o.AcctName) < 8)
                    {
                        r.flag = 0;
                        r.msg = "帐号如果为纯数字，必须大于等于8位。";
                        return r;
                    }
                }
            }
            
            if (string.IsNullOrEmpty(o.Sex))
            {
                o.Sex = "-1";
            }

            /*
            *第三方登录用户
            *1：修改用户，只允许修改其他用户的名称、手机号、角色（没有账号，其他信息的修改权限） 
            *2：获取修改的用户的其他信息信息赋值，执行用户修改统一的存储过程
            *
            */
            if (user.IsThirdClient)
            {
                SysUserExtension entity =await userManager.QueryById(o.Id);

                if (entity != null)
                {

                    var accountList = await accountManager.Query(f => f.UserExtensionId == entity.Id);
                    if (accountList == null && accountList.Count > 0)
                    {
                        r.msg = "执行失败，当前账号不存在。";
                        return r;
                    }
                    var account = accountList.FirstOrDefault();
                    o.StaffNumber = entity.StaffNumber;
                    o.IdNumber = entity.IdNumber;
                    o.Sex = entity.Sex;
                    o.Birthday = entity.Birthday.Value;
                    o.Address = entity.Address;
                    o.ZipCode = entity.ZipCode;
                    o.Tel = entity.Tel;
                    o.Qq = entity.Qq;
                    o.Email = entity.Email;
                    o.Memo = entity.Memo;
                    o.AcctName = account.LoginName;
                    o.NickName = account.NickName;
                    o.UserType = entity.UserType;
                }
                else
                {
                    r.msg = "执行失败，当前用户不存在。";
                    return r;
                }
            }
            int userType = 1;
            if (user.IsSystemUser)
            {
                userType = o.UserType == 0 ? 1 : o.UserType;

                //增加判断，运维人员不能添加系统超管只能添加运维人员及其它单位类型账号
                if (user.AdministratorType != 0 && strRoleIds.Contains(",0,"))
                {
                    r.flag = 0;
                    r.msg = "运营超管不能添加系统超管";
                    return r;
                }
            }
            r = await unitManager.InsertUpdateUser(o);
            return r;
        }


        [HttpPost]
        [Route("userdelbatch")]
        //<used>1</used>
        public async Task<Result> User_DelBatch([FromBody] List<long> listId)
        {
            Result r = new Result();
            if(!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CityAdmin)
                && !user.Roles.Contains(RoleTypes.CoutyAdmin) && !user.Roles.Contains(RoleTypes.SchoolAdmin)
                && !user.Roles.Contains(RoleTypes.CompanyAdmin))
            {
                r.flag = 0;
                r.msg = "只有单位超管才能删除账号!";
                return r;
            }
            string msg = "";

            foreach(long id in listId)
            {
                r = await userManager.BatchDeleteUser(id);
                msg += r.msg;
            }

            r.flag = 1;
            if (string.IsNullOrEmpty(msg))
            {
                r.msg = "删除成功";
            }
            return r;
        }

        [HttpPost]
        [Route("userupdateuserstatuz")]
        //<used>1</used>
        public async Task<Result> User_UpdateUserStatuz(long userId)
        {
            Result r = new Result();
            r =await userManager.UpdateStatuz(userId);
            return r;
        }


        [HttpPost]
        [Route("usermyunitupdateuserstatuz")]
        //<used>1</used>
        public async Task<Result> UserMyUnit_UpdateUserStatuz(long userId)
        {
            Result r = new Result();
            r = await userManager.UpdateMyUnitStatuz(userId);
            return r;
        }

        /// <summary>
        /// 超管所有单位用户列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userfind")]
        public async Task<Result> User_Find([FromBody] VUserListParam param)
        {
            Result r = new Result();
            if (user.IsSystemUser)
            {
                param.UnitNotEqType = 0;
            }
            else if(user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin) 
                || user.Roles.Contains(RoleTypes.SchoolAdmin) || user.Roles.Contains(RoleTypes.CompanyAdmin))
            {
                param.statuzgt = 0;
                param.UserType = 1;
                param.IsAdmin = true;
                param.UnitId = user.UnitId;

            }
            PageModel<VUserList> pg = await vUserListManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            r.data.other = $"/Download/User.xlsx";
            return r;
        }


        /// <summary>
        /// 下属单位用户列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userfindchildren")]
        public async Task<Result> User_FindChildren([FromBody] VUserListParam param)
        {
            Result r = new Result();
            param.UnitPId = user.UnitId;
            param.statuzge = 0;
            PageModel<VUserList> pg = null;
            if (!param.IsShowAllRole)
            {
                pg = await vUserListManager.GetChildPaged(param);
            }
            else
            {
                pg = await vUserListManager.GetPaged(param);
            }
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            r.data.other = $"/Download/User.xlsx";
            return r;
        }


        [HttpPost]
        [Route("userfindchildrenall")]
        //<used>1</used>
        public async Task<Result> User_FindChildrenAll([FromBody] VUserListParam param)
        {
            Result r = new Result();
            param.UnitPId = user.UnitId;
            param.statuzgt = 0;
            param.UserType = 1;
            PageModel<VUserList> pg = null;
            pg = await vUserListManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 用户管理：获取企业单位用户列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userfindcompany")]
        //<used>1</used>
        public async Task<Result> User_FindCompany([FromBody] VUserListParam param)
        {
            Result r = new Result();

            param.statuzgt = 0;
            param.UnitType = UnitTypes.Company.ObjToInt();
            if (user.AdministratorType != 1)
            {
                param.UserType = 1;//用户类型（1：普通用户  2：内置账号【用于物流柜等】）
            }
            var list = await vUserListManager.GetPaged(param);
            if (list != null && list.data != null && list.data.Count > 0)
            {
                r.flag = 1;
                r.msg = string.Format("{0}-{1}/{2}", param.pageIndex + 1, param.pageIndex + param.pageSize, param.totalCount);
                r.data.total = param.totalCount;
                r.data.rows = list;
                //r.data.rows = Object2JSON(list, "Id,Name,RoleIds,RoleNames,UnitId,UnitName,UnitType,UnitPId,UnitPName,AcctId,AcctName,StaffNumber,IdNumber,Mobile,Qq,Email,Statuz,NickName,AcctStatuz,ContiLoginCount,TotalLoginCount,LoginCount,LastLogin,DepartmentNames,UserType,AdministratorType,UserValidate", true);
            }
            r.flag = 1;
            return r;
        }

        /// <summary>
        /// 用户管理：获取用户列表（所有）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userfindmyunitusername")]
        //<used>1</used>
        public async Task<Result> User_FindMyUnitUserName(VUserListParam param)
        {
            Result r = new Result();
            PageModel<VUserList> pg = await vUserListManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 用户管理：根据指定角色获取本单位所有用户集合
        /// </summary>
        /// <param name="rids"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userfindmyunitusernamebyroleid")]
        //<used>1</used>
        public async Task<Result> User_FindMyUnitUserNameByRoleId(string rids)
        {
            Result r = new Result();
            var param = new VUserListParam();
            param.UnitId = user.UnitId;
            param.statuzgt = 0;
            if (rids != null)
            {
                param.RoleIdList = TextHelper.SplitToArray<long>(rids, ',').ToList();
            }
            if (user.AdministratorType != 1)
            {
                param.UserType = 1;
            }
            param.pageSize = int.MaxValue;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Sort", SortType = "DESC" });
            param.sortModel.Add(new SortBaseModel() { SortCode = "Name", SortType = "ASC" });
            var list = await vUserListManager.GetPaged(param);
            if (list != null)
            {
                r.flag = 1;
                r.data.rows = list.data.Select(n => new { Id = n.Id, Name = n.Name });
                r.data.total = list.dataCount;
            }
            else
            {
                r.flag = 0;
                r.data.rows = null;
                r.data.total = 0;
            }
            return r;
        }


        [HttpPost]
        [Route("userfindmyunit")]
        //<used>1</used>
        public async Task<Result> User_FindMyUnit([FromBody] VUserListParam param)
        {
            Result r = new Result();
            param.statuzge = 0;
            param.UnitId = user.UnitId;
            PageModel<VUserList> pg = await vUserListManager.GetPagedWithDepartment(param);

            List<SysModleDto> listModule = new List<SysModleDto>();
            List<SysRole> listRole = await sysRoleManager.Query(f => f.RoleType == user.UnitTypeId);
            if (listRole.Count > 0)
            {
                listModule = listRole.GroupBy(f => new
                {
                    f.ModuleName,
                    f.ModuleSort
                })
               .Select(group => new SysModleDto
               {
                     ModuleName = group.Key.ModuleName,
                     ModuleSort = group.Key.ModuleSort,
                }).ToList();

                listModule = listModule.Where(f => f.ModuleName != "").ToList();
                if (listModule.Count > 0)
                {
                    foreach (SysModleDto i in listModule)
                    {
                        var roleList = listRole.Where(a => a.ModuleName == i.ModuleName).OrderBy(a => a.OrderSort).ToList();
                        i.ListRole = roleList;
                    }
                }
            }
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.data.other = new { listModule = listModule };
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 用户管理：获取用户详情信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("usergetpubbyid")]
        //<used>1</used>
        public async Task<Result> User_GetPubById(long id)
        {
            Result r = new Result(); 
            var param = new VUserDetailParam();
            param.Id = id;
            param.pageSize = 1;
            var list =await vUserDetailManager.GetPaged(param);
            if (list != null && list.data != null && list.data.Count > 0)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.total = 1;
                r.data.rows = list.data.FirstOrDefault();
            }
            r.flag = 1;
            return r;
        }
        /// <summary>
        /// 用户管理：获取本单位普通用户集合
        /// </summary>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("usergetnamebyunitid")]
        //<used>1</used>
        public async Task<Result> User_GetNameByUnitId(long UnitId)
        {
            Result r = new Result();
            List<SysUserExtension> listUser = await userManager.Query(f => f.UnitId == UnitId && f.UserType == 1);
            if (listUser.Count > 0)
            {
                SysUserExtension u = listUser.FirstOrDefault();
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<PUserDto>(u);
            }
            return r;
        }

        /// <summary>
        /// 用户管理：获取所有管理员
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("userfindadmin")]
        //<used>1</used>
        public async Task<Result> User_FindAdmin()
        {
            Result r = new Result();
            VUserListTempParam param = new VUserListTempParam();
            param.RoleId = 0;
            param.Statuzgt = 0;
            param.AdministratorType = 1;
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;

            if (!user.IsSystemUser) //是否是系统管理员
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }
            return await UserT_Find(param);
        }

        /// <summary>
        /// 用户查询，后台调用
        /// </summary>
        /// <param name="param">查询实体</param>
        //<used>1</used>
        private async Task<Result> UserT_Find(VUserListTempParam param)
        {
            Result r = new Result();
            if (!user.IsSystemUser) //是否是系统管理员
            {
                param.UserType = 1;
            }

            var list = await vUserListtManager.GetPaged(param);
            if (list != null && list.data != null && list.data.Count > 0)
            {
                r.data.total = list.dataCount;
                r.flag = 1;
                r.msg = $"{param.pageIndex}-{param.pageSize}/{list.dataCount}";
                r.data.rows = mapper.Map<List<VUserListTemp>>(list.data);
            }
            return r;
        }

        [HttpPost]
        [Route("usergetinfo")]
        //<used>1</used>
        public async Task<Result> User_GetInfo()
        {
            Result r = new Result();
            if (user != null)
            {
                VUserDetail vUserDetail = await vUserDetailManager.GetByUserId(user.UserId);
                if (vUserDetail != null)
                {
                    var vUser = new
                    {
                        Id = vUserDetail.Id,
                        Name = vUserDetail.Name,
                        RoleIds = vUserDetail.RoleIds,
                        RoleNames = vUserDetail.RoleNames,
                        UnitId = vUserDetail.UnitId,
                        UnitName = vUserDetail.UnitName,
                        UnitType = vUserDetail.UnitType,
                        UnitPId = vUserDetail.UnitPId,
                        UnitPName = vUserDetail.UnitPName,
                        AcctId = vUserDetail.AcctId,
                        AcctName = vUserDetail.AcctName,
                        StaffNumber = vUserDetail.StaffNumber,
                        IdNumber = vUserDetail.IdNumber,
                        Sex = vUserDetail.Sex,
                        Birthday = vUserDetail.Birthday,
                        Address = vUserDetail.Address,
                        ZipCode = vUserDetail.ZipCode,
                        Tel = vUserDetail.Tel,
                        Mobile = vUserDetail.Mobile,
                        Qq = vUserDetail.Qq,
                        Email = vUserDetail.Email,
                        RegTime = vUserDetail.RegTime,
                        Memo = vUserDetail.Memo,
                        Statuz = vUserDetail.Statuz,
                        NickName = vUserDetail.NickName,
                        OptId = vUserDetail.CreateId,
                        AcctStatuz = vUserDetail.AcctStatuz,
                    };
                    r.flag = 1;
                    r.msg = "用户已经登录";
                    r.data.total = 1;
                    r.data.rows = vUser;
                }              
                //是否绑定第三方认证
                var thirdUser = await userThirdManager.Find(f => f.UserId == user.UserId);
                if (thirdUser.Count > 0)
                    r.data.headers = 1;
                else
                    r.data.headers = 0;
            }
            else
            {
                r.flag = 0;
                r.msg = "用户尚未登录";
                r.data = null;
            }
            return r;
        }


        [HttpPost]
        [Route("usergetroleinfo")]
        //<used>1</used>
        public async Task<Result> User_GetRoleInfo()
        {
            var r = await Task.Run(() =>
            {
                Result r = new Result();
                if (user != null)
                {
                    var vUser = new
                    {
                        user.Name,
                        user.UserRoles,
                        user.UnitName,
                        user.UserName,
                        user.UserRoleIds,
                        user.UnitTypeId
                    };
                    r.flag = 1;
                    r.msg = "用户已经登录";
                    r.data.total = 1;
                    r.data.rows = vUser;
                }
                else
                {
                    r.flag = 0;
                    r.msg = "用户尚未登录";
                    r.data = null;
                }
                return r;
            });
            return r;
        }



        /// <summary>
        /// 获取Home页面用户名及关联账号列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("usergetmultaccountinfo")]
        //<used>1</used>
        public async Task<Result> User_GetWithMultAccountInfo()
        {
            Result r = new Result();
            if (user != null)
            {

                var vUser = new { user.Name, user.AcctName, user.UserRoleIds };
                r.flag = 1;
                r.msg = "用户已经登录";
                r.data.total = 1;
                r.data.rows = vUser;

                List<VUserMultAccount> list = null;
                list = await vUserMultAccountManager.Find(f => f.UserLoginId == user.ID);
                if (list.Count > 0)
                {    // 该账号关联的其他账号              
                    r.data.footer = (from item in list
                                     orderby item.LoginName
                                     select new
                                     {
                                         item.UserName,
                                         item.LoginName,
                                         item.UserId,
                                         item.UnitName
                                     }).ToList();
                }
                //是否绑定第三方认证
                var thirdUser = await userThirdManager.Find(f => f.UserId == user.UserId);
                if (thirdUser.Count > 0)
                    r.data.headers = 1;
                else
                    r.data.headers = 0;
            }
            else
            {
                r.flag = 0;
                r.msg = "用户尚未登录";
                r.data = null;
            }
            return r;
        }
        /// <summary>
        /// 运维管理：获取平台配置信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("websiteprofilesget")]
        //<used>1</used>
        public async Task<Result> WebSiteProfiles_Get()
        {
            Result r = new Result();
            DataTable ds = await webSiteConfigManager.GetWebSite(user.UnitTypeId, user.UnitId, "", 0);
            r.flag = 1;
            r.msg = "执行成功";
            r.data.rows = ds;
            r.data.total = 1;
            return r;
        }

        /// <summary>
        /// 运维管理：保存平台配置信息logo、名称
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("websiteprofilessave")]
        //<used>1</used>
        public async Task<Result> WebSiteProfiles_Save([FromBody] WebSiteProfilesModel o)
        {
            Result r = new Result();
            r = await webSiteConfigManager.UpdateValue(o.IdName, o.IdLogo, o.name, o.logo, user.UnitId, 0);
            return r;
        }

        /// <summary>
        /// 运维管理：保存平台域名信息
        /// </summary>
        /// <param name="IdLogo"></param>
        /// <param name="DomainName"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("setdefaultlog")]
        //<used>1</used>
        public async Task<Result> SetDefaultLog(long IdLogo,string DomainName)
        {
            Result r = new Result();
            int domainUnitId = 0;
            BWebSiteConfig bs =await webSiteConfigManager.GetById(IdLogo);
            if (bs.UnitId == user.UnitId)
            {
                List<BWebSiteConfig> listWebSite = await webSiteConfigManager.Find(f => f.UnitId == user.UnitId);
                var listId = listWebSite.Select(f => f.Id.ToString()).ToList().ToArray();
                await webSiteConfigManager.DeleteByIds(listId);

            }
            DataTable ds = await webSiteConfigManager.GetWebSite(user.UnitTypeId, user.UnitId, DomainName, domainUnitId);
            r.flag = 1;
            r.msg = "执行成功";
            r.data.rows = ds;
            r.data.total = 1;
            return r;
        }
        /// <summary>
        /// 运维管理：获取平台配置信息列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("websiteconfigsupermanagerfind")]
        //<used>1</used>
        public async Task<Result> WebSiteConfigSuperManager_Find([FromBody] VWebSiteConfigParam param)
        {
            Result r = new Result();
            param.ConfigType = 14;
            PageModel<VWebSiteConfig> pg = await vWebSiteConfigManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 运维管理：获取平台配置集合（根据配置类型type）
        /// </summary>
        /// <param name="configType"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getwebsiteconfigcombox")]
        //<used>1</used>
        public async Task<Result> GetWebSiteConfig_Combox(int configType)
        {
            Result r = new Result();
            List<BWebSiteConfig> listConfig = await webSiteConfigManager.Query(f => f.ConfigType == configType);
            var listSum = listConfig.GroupBy(item => new {
                item.ConfigCode,
                item.ConfigType,
            })
            .Select(group => new BWebSiteConfigDto
            {
                ConfigCode = group.Key.ConfigCode,
                ConfigType = group.Key.ConfigType.Value
            }).ToList();
            r.flag = 1;
            r.msg = "";
            r.data.rows = listSum;
            return r;
        }

        /// <summary>
        /// 运维管理：保存平台配置
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("websiteconfigsupermanagersave")]
        //<used>1</used>
        public async Task<Result> WebSiteConfigSuperManager_Save([FromBody] WebSiteConfigSuperModel o)
        {
            Result r = new Result();
            r = await webSiteConfigManager.InsertUpdate(o.unitId, o.ConfigKey, o.ConfigValue, o.ConfigType, o.Memo, user.ID, o.ConfigId);
            return r;
        }

        /// <summary>
        /// 运维管理：保存平台配置（运维）
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("websiteconfigsupermanageredit")]
        //<used>1</used>
        public async Task<Result> WebSiteConfigSuperManager_Edit([FromBody] WebSiteConfigModel o)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            try
            {
                r = await webSiteConfigManager.InsertUpdate(0, o.ConfigKey, o.ConfigValue, 13, o.Memo, user.ID,o.ConfigId);
                return r;
            }
            catch
            {
                r.flag = 0;
            }
            return r;
        }

        /// <summary>
        /// 运维管理：获取配置详情信息（根据配置id）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("websiteconfigsupermanagergetbyid")]
        //<used>1</used>
        public async Task<Result> WebSiteConfigSuperManager_GetById(long id)
        {
            Result r = new Result();
            var list = await vWebSiteConfigManager.Query(f => f.Id == id);
            if (list.Count > 0)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.total = 1;
                r.data.rows = list[0];
            }
            return r;
        }

        /// <summary>
        /// 单位管理：获取单位扩展信息锁的状态
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getlockstate")]
        //<used>1</used>
        public async Task<Result> GetLockState()
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "";
            r.data.footer = 0;
            List<PSchoolExtension> listSchool =await schoolExtensionManager.Query(f => f.UnitId == user.UnitId);
            if (listSchool.Count > 0)
            {
                r.data.footer = listSchool[0].IsLock;
            }
            return r;
        }


        [HttpPost]
        [Route("batchlockunlock")]
        //<used>1</used>
        public async Task<Result> BatchLockUnLock(string ids, bool isLock)
        {
            Result r = new Result();
            if (string.IsNullOrEmpty(ids))
            {
                r.flag = 0;
                r.msg = "请选择要锁定或解锁的项";
                return r;
            }

            List<PSchoolExtension> list = await schoolExtensionManager.QueryByIDs(ids.Split(','));
            if (list.Count > 0)
            {
                int Lock = 0;
                if (isLock)
                {
                    Lock = 1;
                }
                foreach (PSchoolExtension p in list)
                {
                    //判断是区县、市级、还是超管
                    if (user.Roles.Contains(RoleTypes.CoutyAdmin))
                    {

                        PUnit u =await unitManager.QueryById((object)p.UnitId);
                        if (u.PId == user.UnitId)
                        {
                            p.IsLock = Lock;
                            await schoolExtensionManager.Update(p);
                        }
                    }
                    //市级
                    else if (user.Roles.Contains(RoleTypes.CityAdmin))
                    {
                        PUnit u =await unitManager.QueryById((object)p.UnitId);
                        PUnit u1 =await unitManager.QueryById((object)u.PId);
                        if (u1.PId == user.UnitId)
                        {
                            p.IsLock = Lock;
                            await schoolExtensionManager.Update(p);
                        }
                    }
                    //超管
                    else if (user.IsSystemUser)
                    {
                        p.IsLock = Lock;
                        await schoolExtensionManager.Update(p);
                    }

                }
            }

            r.flag = 1;
            r.msg = "执行成功";
            return r;
        }

       /// <summary>
       /// 下属单位信息管理列表
       /// </summary>
       /// <param name="param"></param>
       /// <returns></returns>
        [HttpPost]
        [Route("schoolinfoadminfind")]
        public async Task<Result> SchoolInfoAdmin_Find([FromBody] VSchoolInfoParam param)
        {
            Result r = new Result();
            PageModel<VSchoolInfo> pg = await vSchoolInfoManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        /// <summary>
        /// 查看单位明细信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("schoolinfogetbyid")]
        public async Task<Result> SchoolInfo_GetById(long Id)
        {
            Result r = new Result();
            List<VSchoolInfo> list = await vSchoolInfoManager.GetById(Id);
            if (list.Count > 0)
            {
                r.flag = 1;
                r.msg = "";
                r.data.rows = list;
            }
            return r;
        }


        [HttpPost]
        [Route("schoolinfopropertyfind")]
        //<used>1</used>
        public async Task<Result> SchoolInfo_ProPertyFind([FromBody] VAddressPlacePropertyParam param)
        {
            Result r = new Result();
            PageModel<VAddressPlaceProperty> pg = await vAddressPlacePropertyManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        [HttpPost]
        [Route("schoolinfouserfind")]
        //<used>1</used>
        public async Task<Result> SchoolInfoUser_Find([FromBody] VUserListParam param)
        {
            Result r = new Result();
            PageModel<VUserList> pg = await vUserListManager.GetPagedWithDepartment(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        /// <summary>
        /// 解冻
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("useraccountunlock")]
        public async Task<Result> UserAccount_UnLock(long id)
        {
            Result r = new Result();
            if (!user.IsSystemUser &&
                !user.Roles.Contains(RoleTypes.CityAdmin) &&
                !user.Roles.Contains(RoleTypes.CoutyAdmin) &&
                !user.Roles.Contains(RoleTypes.SchoolAdmin) &&
                !user.Roles.Contains(RoleTypes.CompanyAdmin))
            {
                r.flag = 0;
                r.msg = "请只有管理员才能解锁。";
                return r;
            }
            r = await accountManager.Unlock(id, "", IpHelper.GetClientIp(HttpContext), user.ID);

            return r;
        }

        /// <summary>
        /// 履约验收：获取单位账号信息列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("schoolbankaccountlistfind")]
        //<used>0</used>
        public async Task<Result> SchoolBankAccountList_Find(PUnitBankAccountParam param)
        {
            Result r = new Result();
            PageModel<PUnitBankAccount> pg = await unitBankAccountManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 角色管理：获取用户角色集合（本单位）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getroleremark")]
        //<used>1</used>
        public async Task<Result> GetRoleRemark()
        {
            Result r = new Result();
            Expression<Func<SysRole, bool>> expression = null;
            if (!user.IsSystemUser)
            {
                if (user.UnitTypeId == 1)
                {
                    expression = f => f.RoleType == 1;
                }
                else if (user.UnitTypeId == 2)
                {
                    expression = f => f.RoleType == 2;
                }
                else if (user.UnitTypeId == 3)
                {
                    expression = f => f.RoleType == 3;
                }
                else if (user.UnitTypeId == 4)
                {
                    expression = f => f.RoleType == 4;
                }
            }
            List<SysRole> listRole = await sysRoleManager.Query(expression);
            var listOD = listRole.OrderBy(f=>f.RoleType).ToList();
            r.flag = 1;
            r.msg = "查询成功";
            r.data.total = listRole.Count;
            r.data.rows = listOD;
            return r;
        }


        [HttpPost]
        [Route("multaccountfind")]
        //<used>1</used>
        public async Task<Result> MultAccount_Find([FromBody] VUserMultAccountParam param)
        {
            Result r = new Result();
            param.UserLoginId = user.UserId;
            PageModel<VUserMultAccount> pg = await vUserMultAccountManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        [HttpPost]
        [Route("multaccountdelbyid")]
        //<used>1</used>
        public async Task<Result> MultAccount_DelById(long id)
        {
            Result r = new Result();
            List<VUserMultAccount> list = await vUserMultAccountManager.Find(f => f.UserLoginId == user.ID && f.Id == id);
            if (list.Count > 0)
            {
                PUserMultAccount p =await userMultAccountManager.GetById(id);
                await userMultAccountManager.DeleteById(id);
                r.flag = 1;
                r.msg = "删除成功";
            }
            else
            {
                r.flag = 0;
                r.msg = "未找到账号，删除失败";
            }
            return r;
        }


        [HttpPost]
        [Route("multaccountadd")]
        //<used>1</used>
        public async Task<Result> MultAccount_Add(string loginName, string pwd)
        {
            Result r = new Result();
            r = await userManager.BinderOtherAccount(loginName, pwd, user.ID);
            return r;
        }

        [HttpPost]
        [Route("unitexchangecheck")]
        //<used>1</used>
        public async Task<Result> UnitExchange_Check()
        {
            Result r = new Result();
            //List<AAssetsEquipmentList> listEqul = await assetsEquipmentListManager.Find(f => f.SchoolId == user.UnitId && f.UseUserId == user.ID && f.IsScrap == 0);
            //var listBorrow = await assetsEquipmentBorrowManager.Find(f => f.RecipientsId == user.ID && f.IsCurrentValid == 1 && (f.Statuz == 0 || f.Statuz == 1));
            //if (listEqul.Count > 0 || listBorrow.Count > 0)
            //{
            //    r.flag = 0;
            //    r.msg = "请处理完你名下资产后再申请";
            //    return r;
            //}
            ////判断是否已经有申请数据
            //var listUnitExchange = await unitExchangeManager.Find(f => f.ApplyUserId == user.ID && f.Statuz == 0);
            //if (listUnitExchange.Count > 0)
            //{
            //    string unitName = "";
            //    PUnit u =await unitManager.GetById(listUnitExchange[0].TransferUnitId);
            //    if (u != null)
            //    {
            //        unitName = u.Name;
            //    }
            //    r.flag = 0;
            //    r.msg = "您申请调入单位：“" + unitName + "”正在处理中，不能同时申请多个调入单位。";
            //    return r;
            //}
            r.flag = 1;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 单位管理：获取调入单位信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("unitexchangeisshow")]
        //<used>1</used>
        public async Task<Result> UnitExchange_IsShow()
        {
            Result r = new Result();
            r =await userManager.ChangeIsShow(user.ID);
            return r;
        }


        [HttpPost]
        [Route("getunitexchangelistfind")]
        //<used>1</used>
        public async Task<Result> GetUnitExchangeList_Find([FromBody] PUnitExchangeParam param)
        {
            Result r = new Result();
            param.TransferUnitId = user.UnitId;
            DataTable dt =await unitExchangeManager.GetUnitExchangeList(param);
            int pageCount = (int)Math.Ceiling((decimal)param.totalCount / param.pageSize);
            r.data.total = param.totalCount;
            r.flag = 1;
            r.data.rows = dt;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pageCount}";
            return r;
        }


        [HttpPost]
        [Route("unitexchangeschoollist")]
        //<used>1</used>
        public async Task<Result> UnitExchange_SchoolList()
        {
            Result r = new Result();
            List<PUnit> listUnit = await unitManager.Find(f => f.Statuz == 1 && f.UnitType == 3 && f.Id != user.UnitId);
            if (listUnit.Count > 0)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<List<PUnitDto>>(listUnit);
            }
            return r;
        }


        [HttpPost]
        [Route("unitexchangeapply")]
        //<used>1</used>
        public async Task<Result> UnitExchange_Apply(long transferUnitId)
        {
            Result r = new Result();
            r = await unitExchangeManager.Apply(transferUnitId, user.UnitId, user.ID);
            return r;
        }

        /// <summary>
        /// 单位管理：审核用户调用（单位单位管理员）
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitexchangejudge")]
        //<used>1</used>
        public async Task<Result> UnitExchange_Judge([FromBody] JudgeModel o)
        {
            Result r = new Result();
            if (!user.Roles.Contains(RoleTypes.SchoolAdmin))
            {
                r.flag = 0;
                r.msg = "只有单位管理员才能审核";
                return r;
            }
            r = await unitExchangeManager.Judge(o.unitExchangeId, o.remark, o.statuz, o.roleIdz, user.UnitId, user.ID, user.UnitTypeId);
            return r;
        }


        [HttpPost]
        [Route("unitexchangeexchangeunit")]
        //<used>1</used>
        public async Task<Result> UnitExchange_ExchangeUnit([FromBody] ExchangeUnitModel o)
        {
            Result r = new Result();
            if (!user.Roles.Contains(RoleTypes.SchoolAdmin))
            {
                r.flag = 0;
                r.msg = "只有单位管理员才能审核";
                return r;
            }
            r =await userManager.ExchangeUnit(o.unitExchangeId, o.remark, o.statuz, o.roleIdz, user.UnitId, user.ID, user.UnitTypeId, o.isUseNewAccount);
            return r;
        }


        [HttpPost]
        [Route("usersetuservalidate")]
        //<used>1</used>
        public async Task<Result> User_SetUserValidate([FromBody] UserValidateModel o)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "只有系统超管才能修改";
                return r;
            }
            SysUserInfo account =await accountManager.QueryById(o.AccountId);
            if (account != null)
            {
                account.UserValidate = o.UserValidate;
                if (await accountManager.Update(account))
                {
                    r.flag = 1;
                    r.msg = "设置成功";
                }
            }
            return r;
        }


        /// <summary>
        /// 用户管理：用户导入下级单位超管信息（包含超管、市级、区县）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("uploaduserfile")]
        public async Task<Result> UploadUserFile([FromBody] ImportUnitParam param)
        {
            Result r = new Result();

            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CityAdmin)
               && !user.Roles.Contains(RoleTypes.CoutyAdmin))
            {
                r.flag = 0;
                r.msg = "您无权使用该功能";
                return r;
            }
            List<UserImportDto> list = new ExcelHelper<UserImportDto>().ImportFromExcel(env.ContentRootPath, param.FilePath, 0);
            r = await unitManager.SaveImportUserData(list);
            return r;
        }

        /// <summary>
        /// 上级单位导入下级单位超管账户或平台超级管理员导入市级或企业账号
        /// </summary>
        /// <param name="sheet"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        private async Task<Result> SaveChildData(DataTable sheet,int roleId=0)
        {
            Result r = new Result();

            string field = ApplicationConfig.ImportUser;
            if (string.IsNullOrEmpty(field))
            {
                r.flag = 0;
                r.msg = "配置文件中未配置“Import.User”";
                return r;
            }
            r = ExeclHelp.ValidateFile(field, sheet);

            if(r.flag == 1)
            {
                #region 判断密码是否需要强化验证
                //判断密码是否需要强化验证
                int PwdVerificationWay = 0;
                string PwdVerificationMsg = "密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种";
                List<BWebSiteConfig> listJs = await webSiteConfigManager.Query(f => f.ConfigType == 10 && (f.ConfigCode == "PwdVerificationWay" || f.ConfigCode == "PwdVerificationMsg"));
                if (listJs.Count > 0)
                {
                    foreach (BWebSiteConfig config in listJs)
                    {
                        if (config.ConfigCode == "PwdVerificationWay")
                        {
                            int.TryParse(config.ConfigValue, out PwdVerificationWay);
                        }
                        if (config.ConfigCode == "PwdVerificationMsg")
                        {
                            PwdVerificationMsg = config.ConfigValue;
                        }
                    }
                }
                #endregion

                //整理数据
                List<VUserDetail> vos = new List<VUserDetail>();//导入成功的信息
                List<VUserDetail> pos = new List<VUserDetail>();//导入失败的信息
                for (int i = 1; i < sheet.Rows.Count; i++)
                {
                    DataRow dr = sheet.Rows[i];
                    VUserDetail vo = new VUserDetail();
                    vo.Name = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 0));
                    vo.Mobile = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 1));
                    vo.Sex = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 2));
                    vo.Qq = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                    vo.AcctName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 4));
                    vo.Pwd = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 5));
                    string unitCode = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 6));
                    string unitName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 7));
                    vo.IdNumber = "";
                    vo.Address = "";
                    vo.ZipCode = "";
                    vo.Tel = "";
                    vo.Memo = "";
                    vo.NickName = vo.Name;
                    vo.RegTime = DateTime.Now;
                    vo.CreateId = user.ID;
                    vo.Statuz = 1;
                    vo.StrRoleIds = roleId.ToString();
                    vo.UnitName = unitName;
                    if (string.IsNullOrEmpty(vo.Name) || string.IsNullOrEmpty(vo.Mobile) || string.IsNullOrEmpty(vo.AcctName)
                        || string.IsNullOrEmpty(vo.Pwd) || string.IsNullOrEmpty(unitCode) || string.IsNullOrEmpty(unitName))
                    {
                        vo.Memo = "姓名、手机号码、登录账号、密码、单位编码、单位名称不能为空";
                        pos.Add(vo);
                        continue;
                    }

                    //判断账号如果为纯数字，必须大于等于11位
                    if (ComLib.IsNumber(vo.AcctName) && ComLib.GetStringLength(vo.AcctName) < 11)
                    {
                        vo.Memo = "账号如果为纯数字，必须大于等于11位";
                        pos.Add(vo);
                        continue;
                    }
                    //验证手机号码输入是否正确
                    if (!StringHelper.IsPhoneNo(vo.Mobile))
                    {
                        vo.Memo = "手机号码输入有误";
                        pos.Add(vo);
                        continue;
                    }
                    if (!string.IsNullOrEmpty(vo.Sex))
                    {
                        if (!vo.Sex.Equals("男") && !vo.Sex.Equals("女"))
                        {
                            vo.Memo = "性别填写有误，只能填写男或女";
                            pos.Add(vo);
                            continue;
                        }
                    }
                    if (!string.IsNullOrEmpty(vo.Qq))
                    {
                        string regex = "[1-9][0-9]{4,14}";
                        if (!Regex.IsMatch(vo.Qq, regex))
                        {
                            vo.Memo = "QQ号码输入有误";
                            pos.Add(vo);
                            continue;
                        }
                    }

                    if (PwdVerificationWay != 0)
                    {
                        Regex objReg = new Regex(@"^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$");
                        if (!objReg.IsMatch(vo.Pwd))
                        {
                            vo.Memo = PwdVerificationMsg;
                            pos.Add(vo);
                            continue;
                        }
                    }

                    //判断单位编码、单位名称是否存在
                    List<PUnit> listUnit = await unitManager.Query(f => f.Code == unitCode && f.Name == unitName && f.Statuz == 1);
                    if (listUnit.Count > 0)
                    {
                        PUnit p = listUnit[0];
                        if (p != null)
                        {
                            if (user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin))
                            {
                                if (p.PId != user.UnitId)
                                {
                                    vo.Memo = "该单位不属于下属单位";
                                    pos.Add(vo);
                                    continue;
                                }
                            }
                            vo.UnitId = p.Id;
                            vo.UnitName = p.Name;
                            vo.Pwd = MD5Helper.MD5Encrypt32(vo.Pwd).ToLower();

                            await unitManager.InsertUpdateUser(vo);

                            //判断是否为单位
                            if (p.UnitType == 3)
                            {
                                List<PSchoolExtension> listSchoolExtension = await schoolExtensionManager.Query(f => f.UnitId == p.Id);
                                if (listSchoolExtension.Count > 0)
                                {
                                    PSchoolExtension extension = listSchoolExtension[0];
                                    if (extension != null)
                                    {
                                        if (string.IsNullOrEmpty(extension.SchoolAdmin))
                                        {
                                            extension.SchoolAdmin = vo.Name;
                                            extension.AdminMobile = vo.Mobile;
                                            await schoolExtensionManager.Update(extension);
                                        }
                                    }
                                }
                            }

                            vos.Add(vo);
                        }
                        else
                        {
                            vo.Memo = "单位异常";
                            pos.Add(vo);
                            continue;
                        }
                    }
                    else
                    {
                        vo.Memo = "单位编码、单位名称不存在";
                        pos.Add(vo);
                        continue;
                    }

                }

                r.flag = 1;
                r.data.rows = vos;
                r.data.footer = pos;
                r.msg = $"信息导入成功 {vos.Count}条，导入失败 {pos.Count}条";
            }
            return r;
        }

        /// <summary>
        /// 单位保存本单位用户账号信息(单位、企业)
        /// </summary>
        /// <param name="sheet"></param>
        /// <returns></returns>
        private async Task<Result> SaveSelfData(DataTable sheet)
        {
            Result r = new Result();
            string field = ApplicationConfig.ImportUserMine;
            if (string.IsNullOrEmpty(field))
            {
                r.flag = 0;
                r.msg = "配置文件中未配置“Import.User.Mine”";
                return r;
            }
            r = ExeclHelp.ValidateFile(field, sheet);
            if(r.flag == 1)
            {
                int roleId = -1;
                if (user.Roles.Contains(RoleTypes.SchoolAdmin))
                {
                    roleId = (int)RoleTypes.Teacher;
                }
                else if (user.Roles.Contains(RoleTypes.CompanyAdmin))
                {
                    roleId = (int)RoleTypes.CompanyRepair;
                }

                #region 判断密码是否需要强化验证
                //判断密码是否需要强化验证
                int PwdVerificationWay = 0;
                string PwdVerificationMsg = "密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种";
                List<BWebSiteConfig> listJs = await webSiteConfigManager.Find(f => f.ConfigType == 10 && (f.ConfigCode.Equals("PwdVerificationWay") || f.ConfigCode.Equals("PwdVerificationMsg")));
                if (listJs.Count > 0)
                {
                    foreach (BWebSiteConfig config in listJs)
                    {
                        if (config.ConfigCode == "PwdVerificationWay")
                        {
                            int.TryParse(config.ConfigValue, out PwdVerificationWay);
                        }
                        if (config.ConfigCode == "PwdVerificationMsg")
                        {
                            PwdVerificationMsg = config.ConfigValue;
                        }
                    }
                }
                #endregion

                //整理数据
                List<VUserDetail> vos = new List<VUserDetail>();//导入成功的信息
                List<VUserDetail> pos = new List<VUserDetail>();//导入失败的信息
                for (int i = 1; i < sheet.Rows.Count; i++)
                {
                    DataRow dr = sheet.Rows[i];
                    VUserDetail vo = new VUserDetail();
                    vo.Name = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 0));
                    vo.Mobile = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 1));
                    vo.Sex = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 2));
                    vo.Qq = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                    vo.AcctName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 4));
                    vo.Pwd = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 5));

                    vo.IdNumber = "";
                    vo.Address = "";
                    vo.ZipCode = "";
                    vo.Tel = "";
                    vo.Memo = "";
                    vo.NickName = vo.Name;
                    vo.RegTime = DateTime.Now;
                    vo.CreateId = user.UserId;
                    vo.Statuz = 1;
                    vo.StrRoleIds = roleId.ToString();
                    vo.UnitName = user.UnitName;
                    if (string.IsNullOrEmpty(vo.Name) || string.IsNullOrEmpty(vo.Mobile) || string.IsNullOrEmpty(vo.AcctName)
                        || string.IsNullOrEmpty(vo.Pwd))
                    {
                        vo.Memo = "姓名、手机号码、登录账号、密码不能为空";
                        pos.Add(vo);
                        continue;
                    }
                    //判断账号如果为纯数字，必须大于等于11位
                    if (StringHelper.IsNumber(vo.AcctName) && StringHelper.GetStringLength(vo.AcctName) < 11)
                    {
                        vo.Memo = "账号如果为纯数字，必须大于等于11位";
                        pos.Add(vo);
                        continue;
                    }
                    //验证手机号码输入是否正确
                    if (!StringHelper.IsPhoneNo(vo.Mobile))
                    {
                        vo.Memo = "手机号码输入有误";
                        pos.Add(vo);
                        continue;
                    }
                    if (!string.IsNullOrEmpty(vo.Sex))
                    {
                        if (!vo.Sex.Equals("男") && !vo.Sex.Equals("女"))
                        {
                            vo.Memo = "性别填写有误，只能填写男或女";
                            pos.Add(vo);
                            continue;
                        }
                    }
                    if (!string.IsNullOrEmpty(vo.Qq))
                    {
                        string regex = "[1-9][0-9]{4,14}";
                        if (!Regex.IsMatch(vo.Qq, regex))
                        {
                            vo.Memo = "QQ号码输入有误";
                            pos.Add(vo);
                            continue;
                        }
                    }


                    if (PwdVerificationWay != 0)
                    {
                        Regex objReg = new Regex(@"^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$");
                        if (!objReg.IsMatch(vo.Pwd))
                        {
                            vo.Memo = PwdVerificationMsg;
                            pos.Add(vo);
                            continue;
                        }
                    }

                    vo.UnitId = user.UnitId;
                    vo.Pwd = MD5Helper.MD5Encrypt32(vo.Pwd).ToLower();
                    await unitManager.InsertUpdateUser(vo);

                    vos.Add(vo);
                }

                r.data.rows = vos;
                r.data.footer = pos;
                r.msg = $"信息导入成功 {vos.Count}条，导入失败 {pos.Count}条";
            }
            return r;
        }

        [HttpPost]
        [Route("usermyunitexport")]
        //<used>1</used>
        public async Task<Result> UserMyUnitExport([FromBody] VUserListParam param)
        {
            Result r = new Result();
            param.statuzge = 0;
            param.UnitId = user.UnitId;
            param.pageSize = int.MaxValue;
            PageModel<VUserList> pg = await vUserListManager.GetPagedWithDepartment(param);
            if (pg.dataCount > 0)
            {
                IWorkbook iwork = new HSSFWorkbook();
                ISheet isheet = iwork.CreateSheet("部门和人员");
                IRow row = null;
                ICell cell = null;
                IFont font = iwork.CreateFont();
                font.IsBold = true;
                IFont font1 = iwork.CreateFont();
                IFont font2 = iwork.CreateFont();

                IFont font3 = iwork.CreateFont();
                font3.IsBold = true;
                font3.Color = IndexedColors.Red.Index;

                ICellStyle cellstyle = iwork.CreateCellStyle();
                cellstyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle.SetFont(font);
                cellstyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;

                ICellStyle cellRed = iwork.CreateCellStyle();
                cellRed.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellRed.SetFont(font3);
                cellRed.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellRed.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRed.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRed.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRed.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;


                ICellStyle cellstyle1 = iwork.CreateCellStyle();
                cellstyle1.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellstyle1.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle1.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;


                ICellStyle cellstyle2 = iwork.CreateCellStyle();
                cellstyle2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
                cellstyle2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle2.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;

                ICellStyle cellstyle0 = iwork.CreateCellStyle();
                cellstyle0.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle0.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle0.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle0.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle0.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle0.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;

                ICellStyle cellstyle3 = iwork.CreateCellStyle();
                cellstyle3.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                font1.IsBold = true;
                font1.FontHeightInPoints = 13;
                cellstyle3.SetFont(font1);
                cellstyle3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

                ICellStyle cellstyle4 = iwork.CreateCellStyle();
                cellstyle4.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle4.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
                font2.IsBold = false;
                font2.FontHeightInPoints = 9;
                cellstyle4.SetFont(font2);
                cellstyle4.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


                ICellStyle cellstyle5 = iwork.CreateCellStyle();
                cellstyle5.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
                cellstyle5.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle5.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                font1.IsBold = true;
                font1.FontHeightInPoints = 13;
                cellstyle5.SetFont(font1);

                int rowIndex = 0;
                row = isheet.CreateRow(rowIndex);
                row.HeightInPoints = 22;
                row.Height = 600;

                int cellIndex = 0;
                cell = row.CreateCell(cellIndex);
                cell.SetCellValue("序号");
                isheet.SetColumnWidth(cellIndex, 3000);
                cell.CellStyle = cellstyle;
                cellIndex++;


                cell = row.CreateCell(cellIndex);
                cell.SetCellValue("部门");
                isheet.SetColumnWidth(cellIndex, 6500);
                cell.CellStyle = cellstyle;
                cellIndex++;


                cell = row.CreateCell(cellIndex);
                cell.SetCellValue("姓名");
                isheet.SetColumnWidth(cellIndex, 6500);
                cell.CellStyle = cellstyle;
                cellIndex++;


                int index = 1;
                foreach (VUserList detail in pg.data)
                {
                    rowIndex++;
                    row = isheet.CreateRow(rowIndex);
                    row.HeightInPoints = 22;
                    row.Height = 30 * 20;

                    cellIndex = 0;

                    cell = row.CreateCell(cellIndex);
                    cell.SetCellValue(index);
                    cell.CellStyle = cellstyle0;
                    cellIndex++;


                    cell = row.CreateCell(cellIndex);
                    cell.SetCellValue(detail.DepartmentNames);
                    cell.CellStyle = cellstyle0;
                    cellIndex++;

                    cell = row.CreateCell(cellIndex);
                    cell.SetCellValue(detail.Name);
                    cell.CellStyle = cellstyle0;
                    cellIndex++;

                    index++;
                }



                string exlName = user.UnitName + "-部门和人员";
                var fileInfo = FileHelper.GetSaveFileInfo(env.ContentRootPath, exlName);
                using (MemoryStream ms = new MemoryStream())
                {
                    //将工作簿的内容放到内存流中
                    iwork.Write(ms);
                    iwork.Close();
                    ms.Flush();
                    ms.Position = 0;
                    using (FileStream fs = new FileStream(fileInfo.filePath, FileMode.Create, FileAccess.Write))
                    {
                        byte[] data = ms.ToArray();
                        fs.Write(data, 0, data.Length);
                        fs.Flush();
                    }
                }
                r.flag = 1;
                r.data.rows = new { fileInfo.url, fileInfo.fileName };
                return r;

            }
            r.flag = 0;
            r.msg = "没有需要导出的数据！";
            return r;
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getuserinfo")]
        public async Task<Result> GetUserInfo()
        {
            Result r = await userManager.GetUserInfo(user.ID);
            return r;
        }

        /// <summary>
        /// 设置当前用户信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("setuserinfo")]
        public async Task<Result> SetUserInfo([FromBody] UserModel o)
        {
            Result r = await userManager.SetUserInfo(o);
            return r;
        }


        /// <summary>
        /// 获取校服管理平台家长、班主任用户信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getuserxfpage")]
        public async Task<Result<List<VUserList>>> GetXfUserPaged([FromBody] VUserListParam param)
        {
            var msgdata = new Result<List<VUserList>>();
            if (user.IsSystemUser)
            {
                PageModel<VUserList> pg = await userManager.GetXfUserPaged(param);
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 校服管理平台超管重置家长、班主任密码
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("setuserxfpwd")]
        public async Task<Result<string>> AmendPswd([FromBody] UserXfPswdModel o)
        {
            if (!user.IsSystemUser)
            {
                return Result<string>.Fail("您无权操作");
            }
            var msgdata = await userManager.AmendPswd(o);
            return msgdata;
        }


        /// <summary>
        /// 平台设置：下属单位用户导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportchilduser")]
        public async Task<Result> ExportChildUser([FromBody] VUserListParam param)
        {
            Result r = new Result();
            param.UnitPId = user.UnitId;
            param.statuzge = 0;
            PageModel<VUserList> pg = null;
            param.pageSize = int.MaxValue;
            if (!param.IsShowAllRole)
            {
                pg = await vUserListManager.GetChildPaged(param);
            }
            else
            {
                pg = await vUserListManager.GetPaged(param);
            }
            if (pg.dataCount > 0)
            {
                string execlName = $"{user.UnitName}-下属单位用户信息";
                string file = new ExcelHelper<VUserList>().ExportToExcel(env.WebRootPath, execlName + ".xls", execlName,
                                                                                          pg.data.ToList(),
                                                                                          new string[] { "Code", "UnitName", "Name", "Mobile", "AcctName", "RoleNames" });
                r.data.rows = $"/{file}";
                r.flag = 1;
                r.msg = "导出成功";
                r.data.headers = "下属单位用户信息";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }
    }
}
