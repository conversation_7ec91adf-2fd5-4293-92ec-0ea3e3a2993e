﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///查询统计页面列配置表
    ///</summary>
    [SugarTable("wf_PageColumnConfig", "查询统计页面列配置表")]
    public class WfPageColumnConfig : BaseEntity
    {

        public WfPageColumnConfig()
        {

        }

        /// <summary>
        /// 模块Id
        /// </summary>
        public long ModuleId { get; set; }

        /// <summary>
        /// 模块类型（1：查询统计模块  2：节点模块  3：资金来源）
        /// </summary>
        public int ModeType { get; set; }

        /// <summary>
        /// 所属菜单，用于节点模块（1：待处理   2：已处理）
        /// </summary>
        public int MenuType { get; set; }

        /// <summary>
        ///查询统计页面表Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long PageDefinitionId { get; set; }

        /// <summary>
        ///节点表Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long ProcessNodeId { get; set; }

        /// <summary>
        /// 流程Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long ProcessId { get; set; } = 0;

        /// <summary>
        ///名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string FieldName { get; set; }

        /// <summary>
        ///字段（类型为2,3时，存路径）
        /// </summary>
        [SugarColumn(Length = 511)]
        public string FieldCode { get; set; }

        /// <summary>
        ///字段说明
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string FieldDescription { get; set; }

        /// <summary>
        ///输入框类型(1：文本输入框，2：字典表(B_Dictionary)下拉框组件，3：审批字典表(Wf_Dictionary)下拉框组件，4：联系人组件，5：年度下拉框组件，11：Div分割线，12：多行文本框，13：帮助文字，14：编辑器，15：表格控件，16：开关，17：文件上传，18：日期控件）
        /// </summary>
        public int TypeBox { get; set; } = 1;

        /// <summary>
        ///列类型（1：数字  2：金额  3：文本   4：日期   5：按钮）
        /// </summary>
        public int FieldType { get; set; } = 1;

        /// <summary>
        ///排序值（值从小到大排序）
        /// </summary>
        public int Sort { get; set; } = 0;

        /// <summary>
        ///列显示宽度
        /// </summary>
        public int Width { get; set; } = 100;

        /// <summary>
        ///内容对齐方式（center、left、right）
        /// </summary>
        [SugarColumn(Length = 31)]
        public string ContentStyle { get; set; } = "";

        /// <summary>
        ///Js格式化脚本
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string JsFormat { get; set; }

        /// <summary>
        /// 状态（1：启用   2：禁用）默认1
        /// </summary>
        public int Statuz { get; set; } = 1;

        /// <summary>
        ///配置类别（1、列表；2：搜索（仅部分字段有效）  4：分组汇总）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ConfigType { get; set; } = 1;

        /// <summary>
        ///分类编码
        /// </summary>
        public long TypeCode { get; set; }


        /// <summary>
        ///底部是否汇总（只有数字和金额类型才能汇总）（1：是  2：否）
        /// </summary>
        public int IsNeedSum { get; set; } = 1;

        /// <summary>
        ///绑定事件名
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string CalculateFun { get; set; }

        /// <summary>
        /// 是否是查询条件（1：列  2：查询）
        /// </summary>
        public int ListFieldType { get; set; } = 2;

        /// <summary>
        /// 控件类型
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string ControlType { get; set; }

        /// <summary>
        /// 查询条件默认文字（是否是查询条件选是的时候必填，选否的时候可以不填写）
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string ConditionWord { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 项目库字段管理表Id
        /// </summary>
        [SugarColumn(DefaultValue ="0")]
        public long FundFieldSetId { get; set; } = 0;

        /// <summary>
        /// 审批流程节点字段表Id（用于模块为“资金来源”）
        /// </summary>
        [SugarColumn(DefaultValue = "0", IsNullable = true)]
        public long ProcessFieldId { get; set; } = 0;


        /// <summary>
        /// 项目清单对应Code编码
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string PageDefineFieldCode { get; set; }

        /// <summary>
        /// 统计方式（0：无，1：sum汇总，2：count汇总）
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int StatisticalMethod { get; set; } = 0;

        /// <summary>
        /// 时间显示格式
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int DateDisplay { get; set; }


        /// <summary>
        /// 列表字段显示类型（1：数字  2：金额  3：文本   4：日期   5：按钮）
        /// </summary>
        [SugarColumn(DefaultValue = "3")]
        public int ColumnFieldType { get; set; }


        /// <summary>
        /// 是否为日期区间（1：是，2：否）默认1
        /// </summary>
        [SugarColumn(DefaultValue = "1")]
        public int IsDateRange { get; set; }
    }


}

