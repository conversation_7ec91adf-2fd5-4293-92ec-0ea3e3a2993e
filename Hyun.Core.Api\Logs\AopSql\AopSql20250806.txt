
Date：2025-08-06 14:14:17.760
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:29.051
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE ((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:30.564
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:30.564
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:30.784
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:30.908
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.198
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/6 14:14:31 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.216
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/6 14:14:31 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.240
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:99999999999999 [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.421
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.438
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:100000000000000 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.499
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.564
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.712
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.758
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:31.809
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:32.509
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id] FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:32.518
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 50 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:38.850
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:38.852
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:38.861
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:38.862
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:55.263
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:55.349
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:55.350
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:55.370
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:55.434
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:14:55.498
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:15.317
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:15.334
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:15.343
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:15.436
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE (( [NodeName] = @NodeName0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@NodeName0 [Value]:测试 [Type]:String    
[Name]:@ModuleId1 [Value]:704618325426373 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:15.975
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [wf_ProcessNode]  
           ([ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@ModuleId,@NodeName,@NodeShowName,@NodeType,@TreatHandle,@TreatHandleUrl,@StopHandle,@StopHandleUrl,@AuditBackCanDel,@UseSameList,@IsBegin,@IsDepartProcess,@ProcessLevel,@Instruction,@AmountName,@UnitId,@AduitType,@IsAuditProjectList,@IsAllowExport,@IsWithdraw,@IsLockProjectAmount,@AduitUserType,@AuditObjectType,@DesigneeNum,@Sort,@NodeConfig,@ConditionConfig,@ListConfig,@BackIsSendMsg,@NextIsSendMsg,@AuditWay,@SubmitButtonName,@IsWriteOpinion,@ApprovalMethod,@RoleName,@IsUsetListSum,@TreatTipMsg,@StopTipMsg,@StagingButton,@IsUsePreData,@NextTipMsg,@UseGroupValue,@StrUseGroupValue,@IsBringOut,@NextProcessNodeIds,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@ModuleId [Value]:704618325426373 [Type]:Int64    
[Name]:@NodeName [Value]:测试 [Type]:String    
[Name]:@NodeShowName [Value]:测试 [Type]:String    
[Name]:@NodeType [Value]:1 [Type]:Int32    
[Name]:@TreatHandle [Value]:待处理 [Type]:String    
[Name]:@TreatHandleUrl [Value]:approvalconfiguration/pages/node/pendinglist [Type]:String    
[Name]:@StopHandle [Value]:已处理 [Type]:String    
[Name]:@StopHandleUrl [Value]:approvalconfiguration/pages/node/processedlist [Type]:String    
[Name]:@AuditBackCanDel [Value]:1 [Type]:Int32    
[Name]:@UseSameList [Value]:1 [Type]:Int32    
[Name]:@IsBegin [Value]:2 [Type]:Int32    
[Name]:@IsDepartProcess [Value]:2 [Type]:Int32    
[Name]:@ProcessLevel [Value]:2 [Type]:Int32    
[Name]:@Instruction [Value]: [Type]:String    
[Name]:@AmountName [Value]: [Type]:String    
[Name]:@UnitId [Value]:0 [Type]:Int64    
[Name]:@AduitType [Value]:1 [Type]:Int32    
[Name]:@IsAuditProjectList [Value]:2 [Type]:Int32    
[Name]:@IsAllowExport [Value]:2 [Type]:Int32    
[Name]:@IsWithdraw [Value]:1 [Type]:Int32    
[Name]:@IsLockProjectAmount [Value]:2 [Type]:Int32    
[Name]:@AduitUserType [Value]:1 [Type]:Int32    
[Name]:@AuditObjectType [Value]:1 [Type]:Int32    
[Name]:@DesigneeNum [Value]:1 [Type]:Int32    
[Name]:@Sort [Value]:0 [Type]:Int32    
[Name]:@NodeConfig [Value]: [Type]:String    
[Name]:@ConditionConfig [Value]: [Type]:String    
[Name]:@ListConfig [Value]: [Type]:String    
[Name]:@BackIsSendMsg [Value]:2 [Type]:Int32    
[Name]:@NextIsSendMsg [Value]:2 [Type]:Int32    
[Name]:@AuditWay [Value]:0 [Type]:Int32    
[Name]:@SubmitButtonName [Value]:转交下一步 [Type]:String    
[Name]:@IsWriteOpinion [Value]:1 [Type]:Int32    
[Name]:@ApprovalMethod [Value]:1 [Type]:Int32    
[Name]:@RoleName [Value]: [Type]:String    
[Name]:@IsUsetListSum [Value]:1 [Type]:Int32    
[Name]:@TreatTipMsg [Value]: [Type]:String    
[Name]:@StopTipMsg [Value]: [Type]:String    
[Name]:@StagingButton [Value]: [Type]:String    
[Name]:@IsUsePreData [Value]:2 [Type]:Int32    
[Name]:@NextTipMsg [Value]: [Type]:String    
[Name]:@UseGroupValue [Value]:2 [Type]:Int64    
[Name]:@StrUseGroupValue [Value]: [Type]:String    
[Name]:@IsBringOut [Value]:2 [Type]:Int64    
[Name]:@NextProcessNodeIds [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:99999999999999 [Type]:Int64    
[Name]:@CreateBy [Value]:jfdqy [Type]:String    
[Name]:@CreateTime [Value]:2025/8/6 14:15:15 [Type]:DateTime    
[Name]:@ModifyId [Value]:99999999999999 [Type]:Int64    
[Name]:@ModifyBy [Value]:jfdqy [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/6 14:15:15 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:705723244343493 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:16.042
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:16.052
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:16.074
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:16.082
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:20.087
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:20.096
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:20.124
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:704618963005637 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:20.834
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:20.835
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:20.847
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:20.850
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:21.235
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:21.455
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:21.701
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:22.180
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:22.191
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Sequence asc) AS RowIndex  FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10000 
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.392
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.393
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.413
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.415
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.426
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.427
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.547
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.558
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.559
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.652
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.683
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:26.788
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:28.147
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:28.157
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:28.183
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:28.191
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 11 AND 20 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:30.784
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:30.794
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:30.814
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:705723244343493 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.114
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.119
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.124
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.127
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.199
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.203
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.209
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Sequence asc) AS RowIndex  FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10000 
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.305
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:15:31.494
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:16:20.706
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:16:20.725
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:16:20.734
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:16:24.450
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:16:24.460
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.740
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.767
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.780
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.814
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:705723244343493 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.881
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.891
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.904
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.905
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.926
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.933
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Sequence asc) AS RowIndex  FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10000 
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:58.994
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:59.428
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:17:59.497
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.032
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.032
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.107
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.108
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.116
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.118
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.162
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.165
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.174
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.200
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.244
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:25.293
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:28.830
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:28.844
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:28.862
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:28.869
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Sort ASC) AS RowIndex  FROM  (SELECT  [d].[Id] AS [Id] , [d].[IsDeleted] AS [IsDeleted] , [d].[CreateTime] AS [CreateTime] , [m].[Name] AS [ModuleName] , [d].[NodeName] AS [NodeName] , [d].[NodeShowName] AS [NodeShowName] , [d].[NodeType] AS [NodeType] , [d].[IsDepartProcess] AS [IsDepartProcess] , [d].[ProcessLevel] AS [ProcessLevel] , [d].[AduitType] AS [AduitType] , [d].[AuditObjectType] AS [AuditObjectType] , [m].[Id] AS [ModuleId] , [d].[Sort] AS [Sort] , ( CASE  WHEN ( [d].[NodeType] = @NodeType1 )  THEN  @Const2   WHEN ( [d].[NodeType] = @NodeType3 )  THEN  @Const4  ELSE  @Const5  END ) AS [StrNodeType] , ( CASE  WHEN ( [d].[ProcessLevel] = @ProcessLevel6 )  THEN  @Const7   WHEN ( [d].[ProcessLevel] = @ProcessLevel8 )  THEN  @Const9  ELSE  @Const10  END ) AS [StrProcessLevel] , ( CASE  WHEN ( [d].[IsWithdraw] = @IsWithdraw11 )  THEN  @Const12  ELSE  @Const13  END ) AS [StrIsWithdraw]  FROM [wf_ProcessNode] [d] Inner JOIN [wf_Module] [m] ON ( [d].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )   WHERE ( [d].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted14 )) T WHERE RowIndex BETWEEN 1 AND 20 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@NodeType1 [Value]:1 [Type]:Int32    
[Name]:@Const2 [Value]:填报 [Type]:String    
[Name]:@NodeType3 [Value]:2 [Type]:Int32    
[Name]:@Const4 [Value]:审批 [Type]:String    
[Name]:@Const5 [Value]:资金库 [Type]:String    
[Name]:@ProcessLevel6 [Value]:1 [Type]:Int32    
[Name]:@Const7 [Value]:市级 [Type]:String    
[Name]:@ProcessLevel8 [Value]:2 [Type]:Int32    
[Name]:@Const9 [Value]:区级 [Type]:String    
[Name]:@Const10 [Value]:校级 [Type]:String    
[Name]:@IsWithdraw11 [Value]:1 [Type]:Int32    
[Name]:@Const12 [Value]:是 [Type]:String    
[Name]:@Const13 [Value]:否 [Type]:String    
[Name]:@IsDeleted14 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.837
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.848
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.869
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:705723244343493 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.910
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.912
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.926
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.928
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.954
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.969
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Sequence asc) AS RowIndex  FROM [b_AttachmentConfig]  WHERE (( 1 = 1 ) AND( [ModuleType] = @ModuleType1 ))  AND ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10000 
[Pars]:
[Name]:@ModuleType1 [Value]:501 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:31.971
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:32.012
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:32.187
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:35.784
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:35.806
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:35.814
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:19:35.835
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:705723244343493 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.171
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.172
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.219
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.220
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.227
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.228
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.268
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [TypeCode] = @TypeCode1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@TypeCode1 [Value]:ZD000 [Type]:String    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.298
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DicType] = @DicType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DicType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.298
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.305
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY ModuleId DESC) AS RowIndex  FROM  (SELECT  [p].[Id] AS [Id] , [p].[IsOpen] AS [IsOpen] , [p].[IsDeleted] AS [IsDeleted] , [p].[CreateTime] AS [CreateTime] , [p].[ProcessName] AS [ProcessName] , [m].[Name] AS [ModuleName] , [p].[UseUnitId] AS [UseUnitId] , [u].[Name] AS [UnitName] , [p].[Statuz] AS [Statuz] , [m].[Id] AS [ModuleId] , [p].[IsSignature] AS [IsSignature]  FROM [wf_Process] [p] Inner JOIN [wf_Module] [m] ON ( [p].[ModuleId] = [m].[Id] ) AND ( [m].[IsDeleted] = @IsDeleted0 )  Inner JOIN [p_Unit] [u] ON ( [p].[UseUnitId] = [u].[Id] ) AND ( [u].[IsDeleted] = @IsDeleted0 )   WHERE ( [p].[IsDeleted] = @IsDeleted0 )) MergeTable   WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted1 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.334
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE (( [IsOpen] = @IsOpen0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsOpen0 [Value]:1 [Type]:Int32    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.363
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Name],[UseUnitId],[Usage],[Statuz],[Logo],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Module]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:22.396
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( [IsDeleted] = @IsDeleted0 ) AND ( [Statuz] = @Statuz1 )) AND ((( [UnitType] =@constant3) OR ( [UnitType] =@constant5)) OR ( [UnitType] =@constant7)))  AND ( [IsDeleted] = @IsDeleted8 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:1 [Type]:Int32    
[Name]:@constant5 [Value]:2 [Type]:Int32    
[Name]:@constant7 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:24.992
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:24.999
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:25.021
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:704620544532677 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:25.063
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE ( [ModuleId] = @ModuleId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:704618325426373 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:20:25.300
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessNodeId],[FieldId],[ControlType],[ApiUrl],[FieldType],[FieldSource],[FieldCode],[FieldName],[ShowName],[HelpRemark],[HelpShowMode],[TypeBox],[IsRequired],[Statuz],[ValidType],[SqlCondition],[IsCondition],[Sort],[TypeCode],[TemplateUrl],[Width],[TitleStyle],[ContentStyle],[IsSort],[Pid],[ProjectListType],[ProjectListTitle],[ProjectListDesc],[SelfFundName],[IsInheritData],[DefaultValue],[ParentCode],[MasterControl],[Controlled],[IsAmountControl],[IsDetail],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessField]  WHERE (((( [ModuleId] = @ModuleId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [IsCondition] = @IsCondition2 )) AND ( [IsRequired] = @IsRequired3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:704618325426373 [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsCondition2 [Value]:1 [Type]:Int32    
[Name]:@IsRequired3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:22:32.838
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:22:32.858
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:22:32.864
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:22:32.881
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:705723244343493 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:25:58.985
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:25:59.016
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:25:59.023
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:25:59.048
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:704620544532677 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 14:25:59.131
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessId],[ProcessNodeId],[BackWay],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessReturnSet]  WHERE (( [ProcessId] = @ProcessId0 ) AND ( [ProcessNodeId] = @ProcessNodeId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@ProcessId0 [Value]:704620544532677 [Type]:Int64    
[Name]:@ProcessNodeId1 [Value]:704619687977157 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.053
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:99999999999999 [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.104
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.120
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:100000000000000 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.128
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.190
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.328
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:99999999999999 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.345
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.352
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:99999999999999 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.400
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:704620544532677 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.617
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE ( [ModuleId] = @ModuleId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:704618325426373 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:29.866
LogLevel：Information
Message：------------------ 
 User:["jfdqy"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessNodeId],[FieldId],[ControlType],[ApiUrl],[FieldType],[FieldSource],[FieldCode],[FieldName],[ShowName],[HelpRemark],[HelpShowMode],[TypeBox],[IsRequired],[Statuz],[ValidType],[SqlCondition],[IsCondition],[Sort],[TypeCode],[TemplateUrl],[Width],[TitleStyle],[ContentStyle],[IsSort],[Pid],[ProjectListType],[ProjectListTitle],[ProjectListDesc],[SelfFundName],[IsInheritData],[DefaultValue],[ParentCode],[MasterControl],[Controlled],[IsAmountControl],[IsDetail],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessField]  WHERE (((( [ModuleId] = @ModuleId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [IsCondition] = @IsCondition2 )) AND ( [IsRequired] = @IsRequired3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@ModuleId0 [Value]:704618325426373 [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsCondition2 [Value]:1 [Type]:Int32    
[Name]:@IsRequired3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:34.387
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:34.390
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:34.391
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:34.503
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:34.531
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:34.569
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/6 15:47:34 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:47:34.576
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/6 15:47:34 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:36.712
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/6 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:36.849
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/6 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:36.869
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:ceshu [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:36.900
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/6 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:36.949
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/6 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.035
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [CreateTime] > @CreateTime1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@CreateTime1 [Value]:2025/8/6 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.061
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.081
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.615
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:ceshu [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/6 15:49:37 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/6 15:49:37 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.622
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.657
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.731
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:2 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:2 [Type]:Int32    
[Name]:@LoginCount [Value]:9 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/6 15:49:37 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:705426280939653 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:37.747
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (2200,20)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:38.858
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:38.928
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (20,2200)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:39.087
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000070,100000000007005,100000000007030,100000000007017,100000000007018,100000000007019,100000000007033,100000000002016,100000000007022,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,814917889516638208,815152216284336128,815152703427579904,827558788750905344,827558788880928768,827558790864834560,852930331584499712,852931042762297344,852932243931271168,853563899415367680,853564152633888768,870714490365153280,870714919371149312)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:39.269
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst8) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst9) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst10) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst11) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst12) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst13) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst15  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted7 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted5 ) AND ( [PAU].[AuditUserId] = @AuditUserId6 ))  AND ( [PAU].[IsDeleted] = @IsDeleted7 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId6 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@MethodConst8 [Value]: [Type]:String    
[Name]:@MethodConst9 [Value]:0 [Type]:Int32    
[Name]:@MethodConst10 [Value]: [Type]:String    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:39.355
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:704618325426373 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:39.467
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:704618325426373 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:40.060
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:40.070
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:40.077
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.267
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:704619380007109 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.597
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [IsDeleted] = @IsDeleted0 ) AND ( [ProcessId] = @ProcessId1 )) AND ( [ProcessNodeId] = @ProcessNodeId2 )) AND ( [AuditUserId] = @AuditUserId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ProcessNodeId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@AuditUserId3 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.638
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [ModuleId],[ProcessId],[ProcessNodeId],[UnitId],[IsLook],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_LookEachOtherSet]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [ProcessId] = @ProcessId1 )) AND ( [ProcessNodeId] = @ProcessNodeId2 )) AND ( [UnitId] = @UnitId3 )) AND ( [IsLook] = @IsLook4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ProcessNodeId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@UnitId3 [Value]:*************** [Type]:Int64    
[Name]:@IsLook4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.678
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ModeType],[MenuType],[PageDefinitionId],[ProcessNodeId],[ProcessId],[FieldName],[FieldCode],[FieldDescription],[TypeBox],[FieldType],[Sort],[Width],[ContentStyle],[JsFormat],[Statuz],[ConfigType],[TypeCode],[IsNeedSum],[CalculateFun],[ListFieldType],[ControlType],[ConditionWord],[FundFieldSetId],[ProcessFieldId],[PageDefineFieldCode],[StatisticalMethod],[DateDisplay],[ColumnFieldType],[IsDateRange],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageColumnConfig]  WHERE ((((( [ProcessNodeId] = @ProcessNodeId0 ) AND ( [ModeType] = @ModeType1 )) AND ( [MenuType] = @MenuType2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@ProcessNodeId0 [Value]:704619380007109 [Type]:Int64    
[Name]:@ModeType1 [Value]:2 [Type]:Int32    
[Name]:@MenuType2 [Value]:1 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.703
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [PId] = @PId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@PId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.716
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND (CAST([ClassifyId] AS NVARCHAR(MAX)) = CAST(@MethodConst1 AS NVARCHAR(MAX))))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@MethodConst1 [Value]:704657822212293 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.724
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND (CAST([ClassifyId] AS NVARCHAR(MAX)) = CAST(@MethodConst1 AS NVARCHAR(MAX))))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@MethodConst1 [Value]:704663742677189 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.750
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND (CAST([ClassifyId] AS NVARCHAR(MAX)) = CAST(@MethodConst1 AS NVARCHAR(MAX))))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@MethodConst1 [Value]:704675445817541 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.772
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND (CAST([ClassifyId] AS NVARCHAR(MAX)) = CAST(@MethodConst1 AS NVARCHAR(MAX))))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@MethodConst1 [Value]:704675492282565 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.781
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ClassifyId],[ClassifyName],[Depth],[Pid],[Name],[Sort],[Memo],[Statuz],[DicType],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Dictionary]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND (CAST([ClassifyId] AS NVARCHAR(MAX)) = CAST(@MethodConst1 AS NVARCHAR(MAX))))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@MethodConst1 [Value]:704676283343045 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.860
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[SchoolId],[GroupId],[GroupItemId],[GroupItemName],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_GroupUnitSet]  WHERE ((((( [ProcessId] = @ProcessId0 ) AND ( [GroupId] = @GroupId1 )) AND ( [GroupItemId] = @GroupItemId2 )) AND ( [TypeBox] = @TypeBox3 )) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@ProcessId0 [Value]:704620544532677 [Type]:Int64    
[Name]:@GroupId1 [Value]:704676847767749 [Type]:Int64    
[Name]:@GroupItemId2 [Value]:705076664979654 [Type]:Int64    
[Name]:@TypeBox3 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.880
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessNodeId],[FieldId],[ControlType],[ApiUrl],[FieldType],[FieldSource],[FieldCode],[FieldName],[ShowName],[HelpRemark],[HelpShowMode],[TypeBox],[IsRequired],[Statuz],[ValidType],[SqlCondition],[IsCondition],[Sort],[TypeCode],[TemplateUrl],[Width],[TitleStyle],[ContentStyle],[IsSort],[Pid],[ProjectListType],[ProjectListTitle],[ProjectListDesc],[SelfFundName],[IsInheritData],[DefaultValue],[ParentCode],[MasterControl],[Controlled],[IsAmountControl],[IsDetail],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessField]  WHERE (((( [Statuz] = @Statuz0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [TypeBox] = @TypeBox2 )) AND ( [TypeCode] = @TypeCode3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@TypeBox2 [Value]:3 [Type]:Int32    
[Name]:@TypeCode3 [Value]:704676847767749 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.932
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [FromId] FROM [wf_ProcessNodeLink]  WHERE (( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@ProcessId0 [Value]:704620544532677 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:43.983
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProjectDeclarationId] FROM [wf_ProjectDeclarationQuery]  WHERE ((( [ProcessId] = @ProcessId0 ) AND ( [FieldCode] = @FieldCode1 )) AND (CAST([InfoId] AS NVARCHAR(MAX)) = CAST(@MethodConst2 AS NVARCHAR(MAX))))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@ProcessId0 [Value]:704620544532677 [Type]:Int64    
[Name]:@FieldCode1 [Value]:yefgld201 [Type]:String    
[Name]:@MethodConst2 [Value]:705076664979654 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:44.044
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [PD].[Id] AS [Id] , [PD].[IsDeleted] AS [IsDeleted] , [PD].[CreateId] AS [CreateId] , [PD].[CreateTime] AS [CreateTime] , [PD].[FillingDate] AS [FillingDate] , [PD].[CreateBy] AS [FillingUser] , [PD].[UnitId] AS [UnitId] , [PD].[ProjectName] AS [ProjectName] , [PD].[ProjectAmount] AS [ProjectAmount] , [PD].[Statuz] AS [Statuz] , [PD].[StatuzDesc] AS [StatuzDesc] , [PD].[PlanYear] AS [PlanYear] , ISNULL([PD].[ToId],@MethodConst7) AS [ToId] , [PD].[NumSum] AS [NumSum] , [U].[Name] AS [SchoolName] , [PD].[IsWithdraw] AS [IsWithdraw]  FROM [wf_ProjectDeclaration] [PD] Inner JOIN [p_Unit] [U] ON ( [PD].[UnitId] = [U].[Id] ) AND ( [U].[IsDeleted] = @IsDeleted6 )   WHERE (( [PD].[IsDeleted] = @IsDeleted0 ) AND ( [PD].[ProcessId] = @ProcessId1 ))  AND ((( [PD].[ToId] = @ToId2 ) AND (( [PD].[AuditUserIds] IS NULL   OR [PD].[AuditUserIds]='') OR ([PD].[AuditUserIds] like '%'+ CAST(@MethodConst3 AS NVARCHAR(MAX))+'%') )) AND NOT ([PD].[AuditedUserIds] like '%'+ CAST(@MethodConst4 AS NVARCHAR(MAX))+'%') )  AND  ([PD].[Id] IN (705364099236037,705388345917573))   AND ( [PD].[IsDeleted] = @IsDeleted6 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted8 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ToId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@MethodConst3 [Value]:*************** [Type]:Int64    
[Name]:@MethodConst4 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@MethodConst7 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:49:44.059
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY FillingDate DESC) AS RowIndex  FROM  (SELECT  [PD].[Id] AS [Id] , [PD].[IsDeleted] AS [IsDeleted] , [PD].[CreateId] AS [CreateId] , [PD].[CreateTime] AS [CreateTime] , [PD].[FillingDate] AS [FillingDate] , [PD].[CreateBy] AS [FillingUser] , [PD].[UnitId] AS [UnitId] , [PD].[ProjectName] AS [ProjectName] , [PD].[ProjectAmount] AS [ProjectAmount] , [PD].[Statuz] AS [Statuz] , [PD].[StatuzDesc] AS [StatuzDesc] , [PD].[PlanYear] AS [PlanYear] , ISNULL([PD].[ToId],@MethodConst7) AS [ToId] , [PD].[NumSum] AS [NumSum] , [U].[Name] AS [SchoolName] , [PD].[IsWithdraw] AS [IsWithdraw]  FROM [wf_ProjectDeclaration] [PD] Inner JOIN [p_Unit] [U] ON ( [PD].[UnitId] = [U].[Id] ) AND ( [U].[IsDeleted] = @IsDeleted6 )   WHERE (( [PD].[IsDeleted] = @IsDeleted0 ) AND ( [PD].[ProcessId] = @ProcessId1 ))  AND ((( [PD].[ToId] = @ToId2 ) AND (( [PD].[AuditUserIds] IS NULL   OR [PD].[AuditUserIds]='') OR ([PD].[AuditUserIds] like '%'+ CAST(@MethodConst3 AS NVARCHAR(MAX))+'%') )) AND NOT ([PD].[AuditedUserIds] like '%'+ CAST(@MethodConst4 AS NVARCHAR(MAX))+'%') )  AND  ([PD].[Id] IN (705364099236037,705388345917573))   AND ( [PD].[IsDeleted] = @IsDeleted6 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted8 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ToId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@MethodConst3 [Value]:*************** [Type]:Int64    
[Name]:@MethodConst4 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@MethodConst7 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.686
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.695
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.700
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.711
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [ModuleId],[NodeName],[NodeShowName],[NodeType],[TreatHandle],[TreatHandleUrl],[StopHandle],[StopHandleUrl],[AuditBackCanDel],[UseSameList],[IsBegin],[IsDepartProcess],[ProcessLevel],[Instruction],[AmountName],[UnitId],[AduitType],[IsAuditProjectList],[IsAllowExport],[IsWithdraw],[IsLockProjectAmount],[AduitUserType],[AuditObjectType],[DesigneeNum],[Sort],[NodeConfig],[ConditionConfig],[ListConfig],[BackIsSendMsg],[NextIsSendMsg],[AuditWay],[SubmitButtonName],[IsWriteOpinion],[ApprovalMethod],[RoleName],[IsUsetListSum],[TreatTipMsg],[StopTipMsg],[StagingButton],[IsUsePreData],[NextTipMsg],[UseGroupValue],[StrUseGroupValue],[IsBringOut],[NextProcessNodeIds],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessNode]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:704619380007109 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.736
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [IsDeleted] = @IsDeleted0 ) AND ( [ProcessId] = @ProcessId1 )) AND ( [ProcessNodeId] = @ProcessNodeId2 )) AND ( [AuditUserId] = @AuditUserId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ProcessNodeId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@AuditUserId3 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.757
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [ModuleId],[ProcessId],[ProcessNodeId],[UnitId],[IsLook],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_LookEachOtherSet]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [ProcessId] = @ProcessId1 )) AND ( [ProcessNodeId] = @ProcessNodeId2 )) AND ( [UnitId] = @UnitId3 )) AND ( [IsLook] = @IsLook4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ProcessNodeId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@UnitId3 [Value]:*************** [Type]:Int64    
[Name]:@IsLook4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.778
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ModeType],[MenuType],[PageDefinitionId],[ProcessNodeId],[ProcessId],[FieldName],[FieldCode],[FieldDescription],[TypeBox],[FieldType],[Sort],[Width],[ContentStyle],[JsFormat],[Statuz],[ConfigType],[TypeCode],[IsNeedSum],[CalculateFun],[ListFieldType],[ControlType],[ConditionWord],[FundFieldSetId],[ProcessFieldId],[PageDefineFieldCode],[StatisticalMethod],[DateDisplay],[ColumnFieldType],[IsDateRange],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageColumnConfig]  WHERE ((((( [ProcessNodeId] = @ProcessNodeId0 ) AND ( [ModeType] = @ModeType1 )) AND ( [MenuType] = @MenuType2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@ProcessNodeId0 [Value]:704619380007109 [Type]:Int64    
[Name]:@ModeType1 [Value]:2 [Type]:Int32    
[Name]:@MenuType2 [Value]:1 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.805
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[SchoolId],[GroupId],[GroupItemId],[GroupItemName],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_GroupUnitSet]  WHERE ((((( [ProcessId] = @ProcessId0 ) AND ( [GroupId] = @GroupId1 )) AND ( [GroupItemId] = @GroupItemId2 )) AND ( [TypeBox] = @TypeBox3 )) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@ProcessId0 [Value]:704620544532677 [Type]:Int64    
[Name]:@GroupId1 [Value]:704676847767749 [Type]:Int64    
[Name]:@GroupItemId2 [Value]:705076664979654 [Type]:Int64    
[Name]:@TypeBox3 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.828
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[ProcessNodeId],[FieldId],[ControlType],[ApiUrl],[FieldType],[FieldSource],[FieldCode],[FieldName],[ShowName],[HelpRemark],[HelpShowMode],[TypeBox],[IsRequired],[Statuz],[ValidType],[SqlCondition],[IsCondition],[Sort],[TypeCode],[TemplateUrl],[Width],[TitleStyle],[ContentStyle],[IsSort],[Pid],[ProjectListType],[ProjectListTitle],[ProjectListDesc],[SelfFundName],[IsInheritData],[DefaultValue],[ParentCode],[MasterControl],[Controlled],[IsAmountControl],[IsDetail],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessField]  WHERE (((( [Statuz] = @Statuz0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [TypeBox] = @TypeBox2 )) AND ( [TypeCode] = @TypeCode3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@TypeBox2 [Value]:3 [Type]:Int32    
[Name]:@TypeCode3 [Value]:704676847767749 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.846
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [FromId] FROM [wf_ProcessNodeLink]  WHERE (( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@ProcessId0 [Value]:704620544532677 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.863
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProjectDeclarationId] FROM [wf_ProjectDeclarationQuery]  WHERE ((( [ProcessId] = @ProcessId0 ) AND ( [FieldCode] = @FieldCode1 )) AND (CAST([InfoId] AS NVARCHAR(MAX)) = CAST(@MethodConst2 AS NVARCHAR(MAX))))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@ProcessId0 [Value]:704620544532677 [Type]:Int64    
[Name]:@FieldCode1 [Value]:yefgld201 [Type]:String    
[Name]:@MethodConst2 [Value]:705076664979654 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.878
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [PD].[Id] AS [Id] , [PD].[IsDeleted] AS [IsDeleted] , [PD].[CreateId] AS [CreateId] , [PD].[CreateTime] AS [CreateTime] , [PD].[FillingDate] AS [FillingDate] , [PD].[CreateBy] AS [FillingUser] , [PD].[UnitId] AS [UnitId] , [PD].[ProjectName] AS [ProjectName] , [PD].[ProjectAmount] AS [ProjectAmount] , [PD].[Statuz] AS [Statuz] , [PD].[StatuzDesc] AS [StatuzDesc] , [PD].[PlanYear] AS [PlanYear] , ISNULL([PD].[ToId],@MethodConst7) AS [ToId] , [PD].[NumSum] AS [NumSum] , [U].[Name] AS [SchoolName] , [PD].[IsWithdraw] AS [IsWithdraw]  FROM [wf_ProjectDeclaration] [PD] Inner JOIN [p_Unit] [U] ON ( [PD].[UnitId] = [U].[Id] ) AND ( [U].[IsDeleted] = @IsDeleted6 )   WHERE (( [PD].[IsDeleted] = @IsDeleted0 ) AND ( [PD].[ProcessId] = @ProcessId1 ))  AND ((( [PD].[ToId] = @ToId2 ) AND (( [PD].[AuditUserIds] IS NULL   OR [PD].[AuditUserIds]='') OR ([PD].[AuditUserIds] like '%'+ CAST(@MethodConst3 AS NVARCHAR(MAX))+'%') )) AND NOT ([PD].[AuditedUserIds] like '%'+ CAST(@MethodConst4 AS NVARCHAR(MAX))+'%') )  AND  ([PD].[Id] IN (705364099236037,705388345917573))   AND ( [PD].[IsDeleted] = @IsDeleted6 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted8 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ToId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@MethodConst3 [Value]:*************** [Type]:Int64    
[Name]:@MethodConst4 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@MethodConst7 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-06 15:50:01.898
LogLevel：Information
Message：------------------ 
 User:["ceshu"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY FillingDate DESC) AS RowIndex  FROM  (SELECT  [PD].[Id] AS [Id] , [PD].[IsDeleted] AS [IsDeleted] , [PD].[CreateId] AS [CreateId] , [PD].[CreateTime] AS [CreateTime] , [PD].[FillingDate] AS [FillingDate] , [PD].[CreateBy] AS [FillingUser] , [PD].[UnitId] AS [UnitId] , [PD].[ProjectName] AS [ProjectName] , [PD].[ProjectAmount] AS [ProjectAmount] , [PD].[Statuz] AS [Statuz] , [PD].[StatuzDesc] AS [StatuzDesc] , [PD].[PlanYear] AS [PlanYear] , ISNULL([PD].[ToId],@MethodConst7) AS [ToId] , [PD].[NumSum] AS [NumSum] , [U].[Name] AS [SchoolName] , [PD].[IsWithdraw] AS [IsWithdraw]  FROM [wf_ProjectDeclaration] [PD] Inner JOIN [p_Unit] [U] ON ( [PD].[UnitId] = [U].[Id] ) AND ( [U].[IsDeleted] = @IsDeleted6 )   WHERE (( [PD].[IsDeleted] = @IsDeleted0 ) AND ( [PD].[ProcessId] = @ProcessId1 ))  AND ((( [PD].[ToId] = @ToId2 ) AND (( [PD].[AuditUserIds] IS NULL   OR [PD].[AuditUserIds]='') OR ([PD].[AuditUserIds] like '%'+ CAST(@MethodConst3 AS NVARCHAR(MAX))+'%') )) AND NOT ([PD].[AuditedUserIds] like '%'+ CAST(@MethodConst4 AS NVARCHAR(MAX))+'%') )  AND  ([PD].[Id] IN (705364099236037,705388345917573))   AND ( [PD].[IsDeleted] = @IsDeleted6 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted8 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@ProcessId1 [Value]:704620544532677 [Type]:Int64    
[Name]:@ToId2 [Value]:704619380007109 [Type]:Int64    
[Name]:@MethodConst3 [Value]:*************** [Type]:Int64    
[Name]:@MethodConst4 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@MethodConst7 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------