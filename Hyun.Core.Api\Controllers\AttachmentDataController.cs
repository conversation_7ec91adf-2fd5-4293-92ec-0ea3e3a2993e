﻿using Hyun.Old.Util;
using SixLabors.ImageSharp;
using System.Runtime.InteropServices;

namespace Hyun.Core.Api.Controllers
{
    /// <summary>
    /// 通用附件上传
    /// </summary>
    /// <remarks>
    /// 2024-08-08
    /// </remarks>
    [Route("api/attachment")]
    public class AttachmentDataController : BaseApiController
    {
        private readonly IMapper mapper;
        private readonly IWebHostEnvironment env;
        private readonly IConfiguration configuration;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IServiceProvider serviceProvider;
        private readonly IUser userContext;
        private readonly IUnitOfWorkManage unitOfWork;
        private readonly ILogger<AttachmentDataController> logger; // 修改为针对控制器的日志记录器

        private readonly IBAttachmentConfigServices ibattachmentconfigservicesManager;
        private readonly IBAttachmentDataServices attachmentDataManager;
        private readonly IBAttachmentServices attachmentManager;
        /// <summary>
        /// AttachmentDataController 的构造函数
        /// </summary>
        /// <param name="mapper">用于对象映射的IMapper实例</param>
        /// <param name="env"></param>
        /// <param name="configuration">应用程序的配置</param>
        /// <param name="logger">用于记录日志的ILogger实例</param>
        /// <param name="httpContextAccessor">用于访问当前HttpContext的IHttpContextAccessor实例</param>
        /// <param name="serviceProvider">用于解析服务的IServiceProvider实例</param>
        /// <param name="attachmentDataManager">用于管理附件数据的IBAttachmentDataServices实例</param>
        /// <param name="attachmentManager">用于管理附件数据的IBAttachmentServices实例</param>
        /// <param name="userContext">当前用户上下文</param>
        /// <param name="unitOfWork">用于管理数据库事务的IUnitOfWorkManage实例</param>
        /// <param name="ibattachmentconfigservicesManager"></param>
        public AttachmentDataController(
            IMapper mapper,
            IWebHostEnvironment env,
            IConfiguration configuration,
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider,
            IUser userContext,
            IUnitOfWorkManage unitOfWork,
            ILogger<AttachmentDataController> logger, // 修改为针对控制器的日志记录器
            IBAttachmentServices attachmentManager,
            IBAttachmentDataServices attachmentDataManager,
            IBAttachmentConfigServices ibattachmentconfigservicesManager)
        {
            this.mapper = mapper;
            this.env = env;
            this.configuration = configuration;
            this.httpContextAccessor = httpContextAccessor;
            this.serviceProvider = serviceProvider;
            this.userContext = userContext;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
            this.attachmentManager = attachmentManager;
            this.attachmentDataManager = attachmentDataManager;
            this.ibattachmentconfigservicesManager = ibattachmentconfigservicesManager;
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="fileCategory">文件分类编码</param>
        /// <param name="files">文件集合</param>
        /// <returns></returns>
        [HttpPost]
        [Route("upload")]
        public async Task<Result<List<BAttachmentDataDto>>> Upload([FromHeader(Name = "filecategory")] int fileCategory, IFormFileCollection files)
        {
            logger.LogInformation($"{userContext.ID}，{userContext.Name}【上传附件】");

            //int fileCategory = 0;
            //if(HttpContext.Request.Headers.ContainsKey("filecategory")  )
            //{
            //    int.TryParse(HttpContext.Request.Headers["filecategory"], out fileCategory);
            //}
            if (fileCategory <= 0)
            {
                return baseFailed<List<BAttachmentDataDto>>("未指定文件分类，请在heard中添加filecategory，并传递正确的值。");
            }
            var configList = await ibattachmentconfigservicesManager.Find(c => c.FileCategory == fileCategory);

            if (configList == null || !configList.Any())
            {
                return baseFailed<List<BAttachmentDataDto>>("上传失败，请先在附件管理中配置文件分类。");
            }

            var config = configList.FirstOrDefault();

            var msgdata = new Result<List<BAttachmentDataDto>>();
            List<string> arrs = new List<string>();
            List<BAttachmentDataDto> attalist = new List<BAttachmentDataDto>();
            if (files == null || !files.Any())
            {
                files = Request.Form.Files;
            }
            ;

            if (files == null || !files.Any()) return baseFailed<List<BAttachmentDataDto>>("请选择上传的文件。");

            foreach (var fileitem in files)
            {
                logger.LogInformation($"【上传附件】：文件 {fileitem.FileName}，{fileitem.ContentType}");
            }

            var filetype = files;
            //格式限制
            var allowType = FileHelper.GetMimeTypes();
            var allowedFile = files.Where(c => allowType.Contains(c.ContentType));
            if (!allowedFile.Any())
            {
                logger.LogInformation($"【上传附件】：文件格式错误");
                return baseFailed<List<BAttachmentDataDto>>("文件格式错误");
            }
            if (allowedFile.Sum(c => c.Length) > 1024 * 1024 * 20)
            {
                logger.LogInformation($"【上传附件】：文件过大");
                return baseFailed<List<BAttachmentDataDto>>("文件过大");
            }

            string foldername = $"uploadfile\\{fileCategory}\\{DateTime.Now.ToString("yyyyMMdd")}";
            string folderpath = Path.Combine(env.WebRootPath, foldername);
            if (!Directory.Exists(folderpath))
            {
                Directory.CreateDirectory(folderpath);
            }
            var listData = new List<BAttachmentDataDto>();
            var errorFileType = new List<string>();
            foreach (var file in allowedFile)
            {
                string fileType = "file";
                string fileExtension = Path.GetExtension(file.FileName).ToLower();
                //验证文件类型是否允许上传
                if (!config.UploadFileType.Contains(fileExtension))
                {
                    errorFileType.Add(file.FileName);
                    continue;
                }
                string newFileName = DateTime.Now.ToString("yyMMdd") + SecurityHelper.GetGuid(true) + fileExtension;
                string strpath = Path.Combine(foldername, newFileName);
                var filePath = Path.Combine(env.WebRootPath, strpath);

                using (var stream = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
                {
                    await file.CopyToAsync(stream);
                }

                #region 读取文件头判断文件类型(暂时隐藏，暂时用不到，先直接通过文件后缀判断)

                //读取文件头判断文件类型
                //using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                //{
                //    using (var reader = new BinaryReader(fs))
                //    {
                //        string fileClass;
                //        byte buffer;
                //        buffer = reader.ReadByte();
                //        fileClass = buffer.ToString();
                //        buffer = reader.ReadByte();
                //        fileClass += buffer.ToString();
                //        //255216是jpg，7173是gif，6677是BMP，13780是PNG
                //        logger.LogInformation($"【上传附件】：fileClass {fileClass}");
                //        if (fileClass == "255216" || fileClass == "7173" || fileClass == "6677" || fileClass == "13780")
                //        {
                //            fileType = "image";
                //        }
                //    }
                //}

                #endregion 读取文件头判断文件类型(暂时隐藏，暂时用不到，先直接通过文件后缀判断)

                int width = 0;
                int height = 0;

                if (StringHelper.IsImageByExtension(fileExtension))
                {
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        using (Image image = Image.Load(filePath))
                        {
                            // 获取图片的宽度和高度
                            width = image.Width;
                            height = image.Height;
                        }
                    }
                    fileType = "image";
                }
                //将文件路径转换为虚拟路径
                var virtualPath = "/" + strpath.Replace('\\', '/');

                //存储数据至数据库表
                BAttachmentData attachData = new BAttachmentData()
                {
                    Title = file.FileName.Replace(fileExtension, ""),
                    Path = virtualPath,
                    Width = width,
                    Height = height,
                    FileSize = file.Length / 1024,
                    Ext = fileExtension,
                    UnitId = userContext.UnitId,
                    ModuleType = config.ModuleType,
                    FileCategory = fileCategory,
                    FileType = fileType
                };
                attachData.Id = await attachmentDataManager.Add(attachData);
                if (attachData.Id > 0)
                {
                    listData.Add(mapper.Map<BAttachmentDataDto>(attachData));
                }
            }

            var excludeFiles = files.Except(allowedFile);

            if (listData.Any())
            {
                bool isPartSuccess = errorFileType.Any() || excludeFiles.Any();

                msgdata = baseSucc(listData, listData.Count, isPartSuccess ? "部分上传成功" : "上传成功");
            }
            else if (excludeFiles.Any())
            {
                var infoMsg = $"{string.Join('、', excludeFiles.Select(c => c.FileName))} 文件格式错误";
                msgdata = baseFailed<List<BAttachmentDataDto>>(infoMsg);
            }
            else if (errorFileType.Any())
            {
                msgdata = baseFailed<List<BAttachmentDataDto>>(string.Join('、', errorFileType) + " 文件类型不允许上传");
            }
            else
            {
                msgdata = baseFailed<List<BAttachmentDataDto>>("上传失败");
            }

            return msgdata;
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="model">文件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("save")]
        public async Task<Result<object>> Save([FromBody] BAttachmentDto model)
        {
            Result<object> r = new Result<object>();

            var entity = await attachmentDataManager.GetById(model.Id);
            if (entity == null)
            {
                return baseFailed<object>("保存失败，请从页面点击操作。");
            }
            try
            {
                var entityAtt = attachmentManager.GetModel(entity);
                if (!string.IsNullOrEmpty(model.Title))
                {
                    entityAtt.Title = model.Title;
                }
                if (model.ObjectId > 0)
                {
                    entityAtt.ObjectId = model.ObjectId;
                }
                else
                {
                    entityAtt.ObjectId = userContext.UnitId;
                }
                long newid = await attachmentManager.Add(entityAtt);
                if (newid > 0)
                {
                    return baseSucc<object>(newid, 1, "保存成功");
                }
                else
                {
                    return baseFailed<object>("保存失败，数据异常。");
                }
            }
            catch (Exception)
            {
                return baseFailed<object>("保存失败，数据异常。");
            }
        }


        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="model">文件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("delete")]
        public async Task<Result<object>> Delete([FromBody] BAttachmentDto model)
        {
            Result<object> r = new Result<object>();

            var entity = await attachmentManager.GetById(model.Id);
            if (entity == null || entity.IsDelete == 1)
            {
                return baseFailed<object>("删除失败，当前文件已不存在。");
            }
            try
            {
                if (entity.UnitId != userContext.UnitId)
                {
                    return baseFailed<object>("禁止删除其它单位的文件。");
                }
                if (model.ModuleType != entity.ModuleType || model.FileCategory != entity.FileCategory)
                {
                    return baseFailed<object>("删除失败，禁止删除其他类型文件。");
                }
                if (model.ObjectId > 0 && entity.ObjectId != model.ObjectId)
                {
                    return baseFailed<object>("删除失败，禁止在当前数据下，删除其他数据下的文件。");
                }
                else if (model.ObjectId <= 0 && (entity.ObjectId != 0 && entity.ObjectId != userContext.UnitId))
                {
                    return baseFailed<object>("删除失败，禁止在当前数据下，删除其他数据下的文件。");
                }

                entity.IsDelete = 1;
                entity.ModifyId = userContext.UserId;
                entity.ModifyTime = DateTime.Now;
                entity.ModifyBy = userContext.UserName;
                if (await attachmentManager.Update(entity, new List<string>() { "IsDelete", "ModifyTime", "ModifyId", "ModifyBy" }))
                {
                    return baseSucc<object>("", 1, "删除成功。");
                }
                else
                {
                    return baseFailed<object>("删除失败，数据异常，请联系客服协助处理。");
                }
            }
            catch (Exception)
            {
                return baseFailed<object>("保存失败，数据异常。");
            }
        }
    }
}