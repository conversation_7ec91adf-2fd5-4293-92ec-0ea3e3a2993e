namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批流程节点表
    ///</summary>
    [SugarTable("wf_ProcessNode", "审批流程节点表")]
    public class WfProcessNode : BaseEntity
    {

        public WfProcessNode()
        {

        }

        /// <summary>
        ///对应模块表xa_Module中Id
        /// </summary>
        public long ModuleId { get; set; }

        /// <summary>
        ///节点名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string NodeName { get; set; }

        /// <summary>
        ///节点显示名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string NodeShowName { get; set; }

        /// <summary>
        ///类型（1：填报 2：审批 3：项目库）填报即为起始节点
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? NodeType { get; set; }

        /// <summary>
        /// 待处理菜单名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string TreatHandle { get; set; } = "待处理";

        /// <summary>
        /// 待处理菜单地址
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string TreatHandleUrl { get; set; }

        /// <summary>
        /// 已处理菜单名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string StopHandle { get; set; } = "已处理";

        /// <summary>
        /// 已处理菜单地址
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string StopHandleUrl { get; set; }

        /// <summary>
        /// 审核不通过退回到起始节点是否可删除（1：是  2：否）
        /// </summary>
        [SugarColumn(IsNullable = true, DefaultValue = "1")]
        public int AuditBackCanDel { get; set; } = 1;

        /// <summary>
        /// 使用相同配置列表（2：否  1：是）默认1
        /// </summary>
        public int UseSameList { get; set; } = 1;

        /// <summary>
        ///是否是开始节点(2：否 1：是)
        /// </summary>
        public int IsBegin { get; set; } = 2;


        /// <summary>
        ///是否为部门审批(2：否 1：是)
        /// </summary>
        public int IsDepartProcess { get; set; } = 2;

        /// <summary>
        ///流程级别（3：校级  2：区级  1：市级）
        /// </summary>
        public int ProcessLevel { get; set; }

        /// <summary>
        ///使用说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Instruction { get; set; }


        /// <summary>
        /// 合计名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string AmountName { get; set; }

        /// <summary>
        ///单位
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        ///审批方式(1：单个用户审批   2：多用户都要审批)
        /// </summary>
        public int AduitType { get; set; }

        /// <summary>
        ///是否审批清单（2：否；1：是（必须审批清单））
        /// </summary>
        public int IsAuditProjectList { get; set; } = 2;

        /// <summary>
        ///是否允许导出Excel
        /// </summary>
        public int IsAllowExport { get; set; }

        /// <summary>
        ///是否可撤回（1：是  2：否）默认1
        /// </summary>
        public int IsWithdraw { get; set; } = 2;

        /// <summary>
        ///提交时是否锁金额并验证清单总和（2：否  1：是）默认2
        /// </summary>
        public int IsLockProjectAmount { get; set; } = 2;

        /// <summary>
        ///审核人员类型（1：该流程所有人；2：该流程指定用户）
        /// </summary>
        public int AduitUserType { get; set; }

       
        /// <summary>
        ///指定审批人对象类型（1：指定的审批人  2：专家列表  ）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AuditObjectType { get; set; }

        /// <summary>
        ///指定人员数量（1：单人   2：多人）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? DesigneeNum { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 模块名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ModuleName { get; set; }

        /// <summary>
        ///排序值
        /// </summary>
        public int Sort { get; set; } = 0;

        /// <summary>
        /// 节点Json数据
        /// </summary>

        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        //[SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(MAX),clob")]
        public string NodeConfig { get; set; }

        /// <summary>
        /// 条件Json数据
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        //[SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(MAX),clob")]
        public string ConditionConfig { get; set; }

        /// <summary>
        /// 列表Json数据
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        //[SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(MAX),clob")]
        public string ListConfig { get; set; }

        /// <summary>
        /// 退回是否发送短信（2：否 1：是）
        /// </summary>
        [SugarColumn(IsNullable = true,DefaultValue ="2")]
        public int BackIsSendMsg { get; set; } = 2;

        /// <summary>
        /// 转交下一步是否发送短信（2：否 1：是）
        /// </summary>
        [SugarColumn(IsNullable = true, DefaultValue = "2")]
        public int NextIsSendMsg { get; set; } = 2;

        /// <summary>
        /// 审批方式（1：通过/不通过  2：仅通过）
        /// </summary>
        public int AuditWay { get; set; }

        /// <summary>
        /// 提交按钮名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string SubmitButtonName { get; set; }

        /// <summary>
        /// 不通过是否填写审批意见（1：是  2：否）默认是
        /// </summary>
        public int IsWriteOpinion { get; set; }

        /// <summary>
        /// 节点类型
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrNodeType { get; set; }

        /// <summary>
        /// 流程级别
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrProcessLevel { get; set; }


        /// <summary>
        ///是否可撤回
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrIsWithdraw { get; set; }

        /// <summary>
        /// 审批人员方式（1：个人  2：部门  3：人员）
        /// </summary>
        [SugarColumn(IsNullable = true, DefaultValue = "1")]
        public int ApprovalMethod { get; set; } = 1;

        /// <summary>
        /// 角色名称
        /// </summary>

        [SugarColumn(Length = 127, IsNullable = true)]
        public string RoleName { get; set; }

        /// <summary>
        /// 金额是否使用清单总和（1：是，2：否）默认是
        /// </summary>
        [SugarColumn(DefaultValue = "1")]
        public int IsUsetListSum { get; set; } = 1;


        /// <summary>
        /// 待处理页面提示文字
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string TreatTipMsg { get; set; }

        /// <summary>
        /// 已处理页面提示文字
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string StopTipMsg { get; set; }

        /// <summary>
        /// 暂存文字配置
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string StagingButton { get; set; }

        ///// <summary>
        ///// 是否带出上一节点相同字段Code数据（1：是，2：否）默认否(去除，放表单配置处)
        ///// </summary>
        //[SugarColumn(DefaultValue = "2")]
        //public int IsUsePreData { get; set; }


        /// <summary>
        /// 转交下一步提示文字
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string NextTipMsg { get; set; }

        /// <summary>
        /// 待处理列表启用分组筛选（默认 2  不启用，启用选择具体的下拉TypeCode值）
        /// </summary>
        [SugarColumn(DefaultValue = "2")]
        public long UseGroupValue { get; set; }

        /// <summary>
        /// 待处理列表启用分组筛选实际值，例如：1000_2 ，5000_3其中2代表基础字典表数据，3代表审批字典表数据
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string StrUseGroupValue { get; set; }

        /// <summary>
        /// 填报页面是否带出主表项目名称编号数据（1：是，2：否）默认否
        /// </summary>
        [SugarColumn(DefaultValue = "2")]
        public long IsBringOut { get; set; }

        /// <summary>
        /// 指定下一个流程，可多选
        /// </summary>

        [SugarColumn(Length = 1023, IsNullable = true)]
        public string NextProcessNodeIds { get; set; }

        /// <summary>
        /// 开启指定人（1：是，2：否）默认否
        /// </summary>
        [SugarColumn(DefaultValue = "2")]
        public int IsOpen { get; set; }
    }


}

