namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///校服采购选用组织
    ///</summary>
    [SugarTable("x_UniformOrganization", "校服采购选用组织")]
    public class XUniformOrganization : BaseEntity
    {

        public XUniformOrganization()
        {

        }

        /// <summary>
        ///采购表Id
        /// </summary>
        public long UniformPurchaseId { get; set; }
        /// <summary>
        /// 单位Id
        /// </summary>
        public long SchoolId { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        public int OrganizationYear { get; set;}
        /// <summary>
        ///家长代表人数（人）
        /// </summary>
        public int ParentNum { get; set; }

        /// <summary>
        ///学生代表人数（人）
        /// </summary>
        public int StudentNum { get; set; }

        /// <summary>
        ///教师代表人数（人）
        /// </summary>
        public int TeacherNum { get; set; }

        /// <summary>
        ///单位管理人员数（人）
        /// </summary>
        public int SchoolAdminNum { get; set; }

        /// <summary>
        ///其他人员数（人）
        /// </summary>
        public int OtherNum { get; set; }

        /// <summary>
        ///选用组织状态(0:待备案  10：待备查  100：已备案)
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        /// 是否备查
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int IsFiling { get; set; }

        /// <summary>
        ///备查时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? FilingTime { get; set; }

        /// <summary>
        ///备案说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string FilingExplanation { get; set; }
        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


    }


}

