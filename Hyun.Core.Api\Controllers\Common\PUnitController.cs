﻿using Hyun.Core.Model.SearchModels.Common;
using Hyun.Old.Util;
using Microsoft.Extensions.Caching.Memory;
using SqlSugar;
using System.Collections.Generic;
using System.Data;

namespace Hyun.Core.Api
{

    [Route("api/hyun/punit")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PUnitController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IPUnitServices unitManager;
        private readonly IRUnitGroupServices unitGroupManager;
        private readonly IPSchoolExtensionServices schoolExtensionManager;
        private readonly IBRepairHomeFeeServices repairHomeFeeManager;
        private readonly IPCompanyExtensionServices companyExtensionManager;
        private readonly IVUnitServices vUnitManager;
        private readonly IVUnitListServices vUnitListManager;
        private readonly ISysUserExtensionServices userManager;
        private readonly ISysUserInfoServices accountManager;
        private readonly IPCompanyImageServices companyImageManager;
        private readonly IBSmsHistoryValidateServices smsHistoryValidateManager;
        private readonly IBWebSiteConfigServices webSiteConfigManager;
        private readonly IVCountyUnitServices vCountyUnitManager;
        //private readonly IVUnitUserRoleServices vUnitUserRoleManager;
        private readonly IPRoleServices roleManager;
        private readonly IPUnitBankAccountServices unitBankAccountManager;
        private readonly IBUserActionLogServices userActionLogManager;
        private readonly IDArticleServices articleManager;
        private readonly IVDepartmentServices vDepartmentManager;
        private readonly IVUserInDepartServices vUserInDepartManager;
        private readonly IPDepartmentServices departmentManager;
        private readonly IPUserInDepartServices userInDepartManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IPDepartmentModuleServices departmentModuleManager;
        private readonly IVUnitAreaByPidListServices vUnitAreaByPidListManager;
        private readonly IVSchoolInfoServices vSchoolInfoManager;
        private readonly IUser user;
        //private readonly IAPermanentFieldConfigServices permanentFieldConfigManager;
        private readonly IBAttachmentDataServices attachmentDataManager;
        private readonly IBUnitSettingServices unitSettingManager;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IOUserThirdAllowServices oUserThirdAllowManager;
        protected IMemoryCache _memoryCache { get; set; }

        public PUnitController(IMapper _mapper, IWebHostEnvironment _env, IPUnitServices _unitManager, IRUnitGroupServices _unitGroupManager, IPSchoolExtensionServices _schoolExtensionManager, IBRepairHomeFeeServices _repairHomeFeeManager, IPCompanyExtensionServices _companyExtensionManager, IVUnitServices _vUnitManager, IVUnitListServices _vUnitListManager, ISysUserExtensionServices _userManager, ISysUserInfoServices _accountManager, IPCompanyImageServices _companyImageManager, IBSmsHistoryValidateServices _smsHistoryValidateManager, IBWebSiteConfigServices _webSiteConfigManager, IVCountyUnitServices _vCountyUnitManager, IVUnitUserRoleServices _vUnitUserRoleManager, IPRoleServices _roleManager, IPUnitBankAccountServices _unitBankAccountManager, IBUserActionLogServices _userActionLogManager, IDArticleServices _articleManager, IVDepartmentServices _vDepartmentManager, IVUserInDepartServices _vUserInDepartManager, IPDepartmentServices _departmentManager, IPUserInDepartServices _userInDepartManager, IBDictionaryServices _dictionaryManager, IPDepartmentModuleServices _departmentModuleManager, IVUnitAreaByPidListServices _vUnitAreaByPidListManager, IVSchoolInfoServices _vSchoolInfoManager, IUser _user, /*IAPermanentFieldConfigServices _permanentFieldConfigManager,*/ IBAttachmentDataServices _attachmentDataManager, IBUnitSettingServices _unitSettingManager, IHttpContextAccessor _httpContextAccessor, IOUserThirdAllowServices _oUserThirdAllowManager, IMemoryCache memoryCache)
        {
            mapper = _mapper;
            env = _env;
            unitManager = _unitManager;
            unitGroupManager = _unitGroupManager;
            schoolExtensionManager = _schoolExtensionManager;
            repairHomeFeeManager = _repairHomeFeeManager;
            companyExtensionManager = _companyExtensionManager;
            vUnitManager = _vUnitManager;
            vUnitListManager = _vUnitListManager;
            userManager = _userManager;
            accountManager = _accountManager;
            companyImageManager = _companyImageManager;
            smsHistoryValidateManager = _smsHistoryValidateManager;
            webSiteConfigManager = _webSiteConfigManager;
            vCountyUnitManager = _vCountyUnitManager;
            //vUnitUserRoleManager = _vUnitUserRoleManager;
            roleManager = _roleManager;
            unitBankAccountManager = _unitBankAccountManager;
            userActionLogManager = _userActionLogManager;
            articleManager = _articleManager;
            vDepartmentManager = _vDepartmentManager;
            vUserInDepartManager = _vUserInDepartManager;
            departmentManager = _departmentManager;
            userInDepartManager = _userInDepartManager;
            dictionaryManager = _dictionaryManager;
            departmentModuleManager = _departmentModuleManager;
            vUnitAreaByPidListManager = _vUnitAreaByPidListManager;
            vSchoolInfoManager = _vSchoolInfoManager;
            user = _user;
            //permanentFieldConfigManager = _permanentFieldConfigManager;
            attachmentDataManager = _attachmentDataManager;
            unitSettingManager = _unitSettingManager;
            httpContextAccessor = _httpContextAccessor;
            oUserThirdAllowManager = _oUserThirdAllowManager;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// 新增单位信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitadd")]
        //<used>1</used>
        public async Task<Result> Unit_Add(PUnitDto o)
        {
            Result r = new Result();
            if (string.IsNullOrEmpty(o.Name.Trim()))
            {
                r.flag = 0;
                r.msg = "单位名称不能为空";
                return r;
            }

            if (o.Id == 0)
            {
                if (user.IsSystemUser)
                {

                }
                else if (user.Roles.Contains(RoleTypes.CityAdmin))
                {
                    o.PId = user.UnitId;
                    o.UnitType = (int)UnitTypes.Couty;
                    o.IndustryId = (int)IndustryTypes.GeneralEducation;
                }
                else if (user.Roles.Contains(RoleTypes.CoutyAdmin))
                {
                    o.PId = user.UnitId;
                    o.UnitType = (int)UnitTypes.School;
                    o.IndustryId = (int)IndustryTypes.GeneralEducation;
                }
                else
                {
                    r.flag = 0;
                    r.msg = "无此权限";
                    r.data = null;
                    return r;
                }
            }
            else
            {
                PUnit po = await unitManager.QueryById((object)o.Id);
                if (po == null)
                {
                    r.flag = 2;
                    r.msg = "执行失败，可能原因：无此单位";
                    r.data = null;
                    return r;
                }
                if (user.IsSystemUser)
                {
                }
                else if (user.Roles.Contains(RoleTypes.CityAdmin) && po.PId == user.UnitId)
                {
                    o.PId = user.UnitId;
                    o.UnitType = (int)UnitTypes.Couty;
                }
                else if (user.Roles.Contains(RoleTypes.CoutyAdmin) && po.PId == user.UnitId)
                {
                    o.PId = user.UnitId;
                    o.UnitType = (int)UnitTypes.School;
                }
                else if ((user.Roles.Contains(RoleTypes.CityAdmin)
                    || user.Roles.Contains(RoleTypes.CoutyAdmin)
                    || user.Roles.Contains(RoleTypes.SchoolAdmin)
                    || user.Roles.Contains(RoleTypes.CompanyAdmin)) && o.Id == user.UnitId)
                {
                    o.PId = po.PId;
                    o.UnitType = po.UnitType;
                }
                else
                {
                    r.flag = 0;
                    r.msg = "无此权限";
                    r.data = null;
                    return r;
                }
            }
            r = await unitManager.InsertUpdate(o);
            return r;
        }


        [HttpPost]
        [Route("unitgetbyid")]
        //<used>1</used>
        public async Task<Result> Unit_GetById(long Id)
        {
            Result r = new Result();

            //单位扩展信息：班级数、在校生数、上门费信息
            PUnit o = await unitManager.QueryById((object)Id);
            if (o != null)
            {
                if (o.UnitType == 3)
                {
                    PSchoolExtension se = schoolExtensionManager.Query(f => f.UnitId == Id).Result.FirstOrDefault();
                    if (se != null)
                    {
                        o.ClassNum = se.ClassNum;
                        o.StudentNum = se.StudentNum;
                        o.TeacherNum = se.TeacherNum;
                        o.FloorArea = decimal.Parse(se.FloorArea.ToString());
                        o.BuildArea = decimal.Parse(se.BuildArea.ToString());
                        o.SchoolStage = se.SchoolStage;
                        o.SchoolAdmin = se.SchoolAdmin;
                        o.AdminMobile = se.AdminMobile;
                        o.SchoolNature = se.SchoolNature;
                    }
                    BRepairHomeFee fee = repairHomeFeeManager.Query(f => f.SchoolId == Id).Result.FirstOrDefault();
                    if (fee != null)
                    {
                        o.Mileage = fee.Mileage;
                    }
                }
                else if (o.UnitType == 4)    //update by jiangpeng 2017-10-09
                {
                    o.IsServiceProvider = false;
                    o.IsSupplier = false;
                    //企业查询是否供应商和是否服务商
                    var companyExtensionList = companyExtensionManager.Query(f => f.UnitId == Id).Result;
                    if (companyExtensionList.Count > 0)
                    {
                        var objProvider = companyExtensionList.Where(f => f.ServerType == 1).FirstOrDefault();
                        if (objProvider != null) o.IsServiceProvider = true;
                        var objSupplier = companyExtensionList.Where(f => f.ServerType == 1).FirstOrDefault();
                        if (objSupplier != null) o.IsSupplier = true;
                    }
                }
                r.flag = 1;
                r.msg = "执行成功";
                r.data.total = 1;
                r.data.rows = mapper.Map<PUnitDto>(o);
            }
            return r;
        }

        [HttpPost]
        [Route("unitgetinfo")]
        //<used>1</used>
        public async Task<Result> Unit_GetInfo()
        {
            Result r = new Result();
            PUnit o = await unitManager.QueryById(user.UnitId);
            if (o != null)
            {
                r.flag = 1;
                r.msg = "";
                r.data.rows = mapper.Map<PUnitDto>(o);
            }
            return r;
        }

        /// <summary>
        /// Unit_GetSchoolInfo 获取当前单位信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("unitgetschoolinfo")]
        //<used>1</used>
        public async Task<Result> Unit_GetSchoolInfo()
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                DataTable dt = vUnitManager.GetSchoolInfo(new VUnitParam { Id = user.UnitId, pageIndex = 1, pageSize = 1 });
                r.data.total = 1;
                r.flag = 1;
                r.data.rows = dt;
                r.msg = "";
                return r;
            });
            return result;
        }

        [HttpPost]
        [Route("unitinsertupdate")]
        //<used>1</used>
        public async Task<Result> Unit_InsertUpdate([FromBody] PUnitDto o)
        {
            Result r = new Result();
            r = await Unit_Add(o);
            return r;
        }


        [HttpPost]
        [Route("unitupdateschoolinfo")]
        public async Task<Result> Unit_UpdateSchoolInfo([FromBody] PUnitDto o)
        {
            Result r = new Result();
            r = await unitManager.UpdateSchoolInfo(o.Id, o.Brief, o.OrganizationCode, o.Address, o.ZipCode, o.Url, o.Mobile, o.Introduction, o.ClassNum, o.StudentNum, o.TeacherNum, o.FloorArea, o.BuildArea, o.SchoolStage, o.Memo, o.SchoolAdmin, o.AdminMobile, o.HeadMaster, o.MsaterMobile, o.SchoolNature);
            return r;
        }


        [HttpPost]
        [Route("unitdelbatch")]
        //<used>1</used>
        public async Task<Result> Unit_DelBatch(string ids)
        {
            Result r = new Result();
            r = await unitManager.DeleteByIds(ids);
            return r;
        }

        /// <summary>
        /// 单位管理：删除单位信息（运维、区县管理员），未审核、未禁用的可删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitdelunitinfo")]
        //<used>1</used>
        public async Task<Result> Unit_DelUnitInfo(long id)
        {
            Result r = new Result();
            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CoutyAdmin))
            {
                r.flag = 0;
                r.msg = "您无权操作";
                return r;
            }
            r = await unitManager.DeleteUnitInfo(id);
            return r;
        }

        [HttpPost]
        [Route("unitfind")]
        //<used>1</used>
        public async Task<Result> Unit_Find([FromBody] VUnitParam param)
        {
            Result r = new Result();
            param.IsSystemAdmin = user.IsSystemUser;
            string roleIds = $",{user.UserRoleIds},";
            if (roleIds.Contains(",10,"))
            {
                param.IsCityAdmin = true;
            }
            else if (roleIds.Contains(",20,"))
            {
                param.IsCoutyAdmin = true;
            }
            else if (roleIds.Contains(",40,"))
            {
                param.IsCompanyAdmin = true;
            }
            PageModel<VUnit> pg = await vUnitManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (user.IsSystemUser)
            {
                r.data.other = $"/Download/Unit.xlsx";
            }
            return r;
        }

        /// <summary>
        /// 下属单位信息列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitfindchildren")]
        public async Task<Result> Unit_FindChildren([FromBody] VUnitParam param)
        {
            Result r = new Result();
            param.Pid = user.UnitId;
            param.statuzge = 0;
            PageModel<VUnit> pg = await vUnitManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (user.IsSystemUser)
            {

            }
            else if(user.UnitTypeId == UnitTypeEnum.County.ToEnumInt())
            {
                r.data.other = $"/Download/UnitSchool.xlsx";
                //List<BDictionary> listDic = await dictionaryManager.Query(f => f.TypeCode == "1101" && f.Statuz == 1 && f.IsDeleted == false);
                //List<dropdownModel> listSchoolStage = listDic.Select(f => new dropdownModel { value = f.DicValue.ToString(), label = f.DicName }).ToList();
                //r.data.footer = listSchoolStage;
                r.data.footer = await unitManager.GetAreaName(user.AreaId);
            }
            else if(user.UnitTypeId == UnitTypeEnum.City.ToEnumInt())
            {
                r.data.other = $"/Download/Unit.xlsx";
            }
            return r;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitfindidname")]
        //<used>1</used>
        public async Task<Result> Unit_FindIdName([FromBody] PUnitParam param)
        {
            Result r = new Result();
            param.statuzgt = 0;
            param.pageSize = int.MaxValue;
            param.sortModel = new List<SortBaseModel>() { new SortBaseModel() { SortCode = "Id", SortType = "ASC" } };
            PageModel<PUnit> pg = await unitManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<PUnitDto>>(pg.data);
            r.msg = "查询成功";
            return r;
        }


        [HttpPost]
        [Route("unitfindchildrenidname")]
        //<used>1</used>
        public async Task<Result> Unit_FindChildrenIdName()
        {
            Result r = new Result();
            List<PUnit> list = await unitManager.Query(f => f.PId == user.UnitId && f.UnitType != UnitTypes.Company.ObjToInt() && f.Statuz >= 0);
            r.flag = 1;
            r.data.rows = mapper.Map<List<PUnitDto>>(list);
            r.msg = "查询成功";
            r.data.total = list.Count;
            return r;
        }

        /// <summary>
        /// 单位管理：审核单位信息，发送审核结果短信
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitexamine")]
        //<used>1</used>
        public async Task<Result> Unit_Examine([FromBody] PUnit o)
        {
            Result r = new Result();
            long id = o.Id;
            r = await unitManager.ExamineStatuz(id, o.Name);
            if (r.flag == 1)
            {
                //发送短信
                string AcctNumber = "";
                SysUserExtension us = userManager.Db.Queryable<SysUserExtension>().Where(f => f.UnitId == id && f.UserType == 1).Take(1).OrderBy(st => SqlFunc.Desc(st.Id)).ToList().FirstOrDefault();
                if (us != null)
                {
                    SysUserInfo account = accountManager.Db.Queryable<SysUserInfo>().Where(f => f.UserExtensionId == us.Id).Take(1).OrderBy(st => SqlFunc.Desc(st.Id)).ToList().FirstOrDefault();
                    if (account != null)
                    {
                        AcctNumber = account.LoginName;
                    }
                }
                if (!string.IsNullOrEmpty(AcctNumber))
                {
                    string msg = ApplicationConfig.SendMessageEventAuditEnterprise;
                    string strMessage = string.Format(msg, o.Name, AcctNumber);
                    await SendMessage.SendToMobile(o.Mobile, strMessage);
                }
            }
            return r;
        }

        /// <summary>
        /// 单位管理：获取企业证件信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitgetcompanyimage")]
        //<used>1</used>
        public async Task<Result> Unit_GetCompanyImage(long Id)
        {
            Result r = new Result();
            List<PCompanyImage> list = await companyImageManager.Find(f => f.UnitId == Id);
            r.flag = 1;
            r.msg = "";
            r.data.rows = mapper.Map<List<PCompanyImageDto>>(list);
            return r;
        }



        private int ValidationImageCaptcha(string Uuid, string Code)
        {

            var value = _memoryCache.Get<string>($"Hyun:Captcha:{Code.ToLower()}");
            if (value is not null)
            {
                if (value.Equals(Uuid))
                    return 1;
                else
                    return 3;
            }
            else
            {
                return 2;
            }
        }

        /// <summary>
        /// 获取单位
        /// </summary>
        /// <param name="CountyId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("punitgetschoolbycountyid")]
        //<used>1</used>
        public async Task<Result> PUnit_GetSchoolByCountyId(long CountyId)
        {
            Result r = new Result(); 
            List<PUnit> list = null;
            if (CountyId == 0)
            {
                if (user.UnitTypeId == UnitTypeEnum.County.ObjToInt())
                {
                    list = await unitManager.Query(n => n.Statuz > 0 && n.PId == user.UnitId, " Sort ASC ,Id ASC ");
                }
                else
                {
                    list = await unitManager.Query(n => n.Statuz > 0 && n.UnitType == UnitTypeEnum.School.ObjToInt(), " Sort ASC ,Id ASC ");
                }
            }
            else
            {
                list = await unitManager.Query(n => n.PId == CountyId && n.Statuz > 0 && n.UnitType == UnitTypeEnum.School.ObjToInt(), " Sort ASC ,Id ASC ");
            }
            if (list != null && list.Count > 0)
            {
                r.data.rows = (from item in list
                               select new
                               {
                                   UnitId = item.Id,
                                   UnitName = item.Name
                               });
                r.data.total = list.Count;
            }
            r.flag = 1;
            r.msg = "";

            return r;
        }

        /// <summary>
        /// 单位管理：获取区县、单位集合
        /// </summary>
        /// <param name="CityId">市Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("punitgetcountybycityid")]
        //<used>1</used>
        public async Task<Result> PUnit_GetCountyByCityId(long CityId)
        {
            Result r = new Result();
            VCountyUnitParam param = new VCountyUnitParam();
            param.UnitType = UnitTypes.Couty.ObjToInt();
            if (CityId > 0)
            {
                param.CityId = CityId;
            }
            else
            {
                param.CountyId = user.UnitId;
            }
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "UnitId", SortType = "ASC" } };
            }

            var list = await vCountyUnitManager.Find(param);
            if (list != null && list.Count > 0)
            {
                r.data.rows = list.Select(t => new { t.UnitId, t.CountyName });
                r.data.total = list.Count;
            }
            r.flag = 1;
            return r;
        }

        /// <summary>
        /// 单位管理：获取区县集合（运维）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("punitgetcountybyadmin")]
        //<used>0</used>
        public async Task<Result> PUnit_GetCountyByAdmin()
        {
            Result r = new Result();
            VCountyUnitParam param = new VCountyUnitParam();
            param.UnitType = UnitTypes.Couty.ObjToInt();
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "UnitId", SortType = "ASC" });
            var list = await vCountyUnitManager.Find(param);
            r.data.rows = list;
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }

        /// <summary>
        /// 单位管理：获取当前单位、下属单位和企业集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("countyunitfind")]
        //<used>1</used>
        public async Task<Result> CountyUnit_Find([FromBody] VUnitParam param)
        {
            Result r = new Result();
            param.IsCoutyAdmin = true;
            PageModel<VUnit> pg = await vUnitManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        [HttpPost]
        [Route("departmentlistfind")]
        //<used>1</used>
        public async Task<Result> DepartmentList_Find([FromBody] VDepartmentParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            param.statuzgt = 0;
            PageModel<VDepartment> pg = await vDepartmentManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        [HttpGet]
        [Route("getalldepartment")]
        //<used>1</used>
        public async Task<Result> GetAllDepartment()
        {
            Result r = new Result();
            VDepartmentParam param = new VDepartmentParam();
            param.unitId = user.UnitId;
            param.statuzgt = 0;
            param.pageSize = int.MaxValue;
            PageModel<VDepartment> pg = await vDepartmentManager.GetPaged(param);
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = "";
            return r;
        }

        [HttpPost]
        [Route("departmentlistfindbyid")]
        //<used>1</used>
        public async Task<Result> DepartmentList_FindById(long id)
        {
            Result r = new Result();
            if (!user.Roles.Contains(RoleTypes.SchoolAdmin)
                && !user.Roles.Contains(RoleTypes.CoutyAdmin)
                && !user.IsSystemUser
                && !user.Roles.Contains(RoleTypes.CityAdmin))
            {
                r.flag = 0;
                r.msg = "您无权操作";
                return r;
            }
            PDepartment p = await departmentManager.GetById(id);

            if (p != null && p.UnitId == user.UnitId)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.total = 1;
                r.data.rows = mapper.Map<PDepartmentDto>(p);
            }
            return r;
        }


        [HttpPost]
        [Route("departmentuserlistfind")]
        //<used>1</used>
        public async Task<Result> DepartmentUserList_Find([FromBody] SysUserExtensionParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            param.UserType = 1;
            List<PUserInDepart> list = await userInDepartManager.Find(f => f.DepartmentId == param.DepartmentId);
            if (list.Count > 0)
            {
                param.ListUserId = list.Select(f => f.UserId).ToList();
            }
            PageModel<SysUserExtension> pg = await userManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<PUserDto>>(pg.data);
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        [HttpPost]
        [Route("departmentinsertupdate")]
        //<used>1</used>
        public async Task<Result> Department_InsertUpdate([FromBody] PDepartment o)
        {
            Result r = new Result();
            r = await departmentManager.InsertUpdate(o.Name, o.Memo, o.moduleIds, o.IsManage, user.UnitId, o.Id);
            return r;
        }


        [HttpDelete]
        [Route("departmentdelbyid")]
        //<used>1</used>
        public async Task<Result> Department_DelById(long Id)
        {
            Result r = new Result();
            r = await departmentManager.DeleteById(Id, user.UnitId);
            return r;
        }


        [HttpGet]
        [Route("userindepartbatchinsert")]
        //<used>1</used>
        public async Task<Result> UserInDepart_BatchInsert(string ids, long departId)
        {
            Result r = new Result();
            if (!user.Roles.Contains(RoleTypes.SchoolAdmin))
            {
                r.flag = 0;
                r.msg = "只有单位超管才能为部门批量添加用户信息";
                return r;
            }
            r = await userInDepartManager.InsertUpdate(ids, departId, user.UnitId, 0);
            return r;
        }


        [HttpPost]
        [Route("userindepartlistfind")]
        //<used>1</used>
        public async Task<Result> UserInDepartList_Find([FromBody] VUserInDepartParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            PageModel<VUserInDepart> pg = await vUserInDepartManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        [HttpDelete]
        [Route("userindepartbatchdel")]
        //<used>1</used>
        public async Task<Result> UserInDepart_BatchDel(string ids, long departId)
        {
            Result r = new Result();
            if (!user.Roles.Contains(RoleTypes.SchoolAdmin))
            {
                r.flag = 0;
                r.msg = "您无权操作此功能";
                return r;
            }
            List<PUserInDepart> list = await userInDepartManager.GetBatchDelList(ids, departId, user.UnitId);
            var err = 0;
            if (list != null && list.Count > 0)
            {
                foreach (var item in list)
                {
                    if (item.DepartmentId == departId)
                    {
                        if (await userInDepartManager.DeleteById(item.Id))
                        {
                            r.flag = 1;
                            r.msg = "移除成功";
                        }
                        else
                        {
                            err++;
                        }
                    }
                }
                if (err > 0)
                {
                    r.flag = 0;
                    r.msg = "你选择的部门人员不存在无法移除";
                    if (list.Count == err)
                    {
                        r.msg = "你选择的部门人员存在非本单位的无法移除";
                    }

                }

            }
            return r;
        }


        [HttpGet]
        [Route("departmentenable")]
        //<used>1</used>
        public async Task<Result> Department_Enable(long Id)
        {
            Result r = new Result();
            PDepartment p = await departmentManager.GetById(Id);
            if (p.UnitId != user.UnitId)
            {
                r.flag = 0;
                r.msg = "非法操作";
            }
            else
            {
                if (p.Statuz == 0)
                {
                    p.Statuz = 1;
                }
                else
                {
                    p.Statuz = 0;
                }
                await departmentManager.Update(p);
                r.flag = 1;
                r.msg = "操作成功";
            }
            return r;
        }


        [HttpPost]
        [Route("departmentuserupdate")]
        //<used>1</used>
        public async Task<Result> Department_UserUpdate([FromBody] DepartmentUserUpdateModel o)
        {
            Result r = new Result();
            r = await userInDepartManager.BatchUpdateUser(o.id, o.odDepartId, o.newDepartId, user.UnitId);
            return r;
        }

        /// <summary>
        /// 单位管理：获取部门管理集合列表
        /// </summary>
        /// <param name="departId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("departmentmoduleget")]
        //<used>1</used>
        public async Task<Result> DepartmentModule_Get(long departId)
        {
            Result r = new Result();
            var dictionaryList = await dictionaryManager.Find(f => f.TypeCode == "9000");
            if (dictionaryList.Count > 0)
            {
                List<PDepartmentModule> departModuleList = new List<PDepartmentModule>();
                if (departId > 0)
                {
                    var deaprtModule = await departmentModuleManager.Find(f => f.DepartmentId == departId);
                    if (deaprtModule != null) departModuleList = deaprtModule.ToList();
                }
                var data = from p in dictionaryList
                           select new
                           {
                               DicValue = p.DicValue,
                               DicName = p.DicName,
                               Checked = departModuleList.Find(f => f.ModuleId.ToString().Equals(p.DicValue)) == null ? 0 : 1
                           };
                r.data.rows = data;
            }
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }

        [HttpPost]
        [Route("unitsortedit")]
        //<used>1</used>
        public async Task<Result> UnitSort_Edit(long Id, int Sort)
        {
            Result r = new Result();
            PUnit u = await unitManager.QueryById((object)Id);
            if (u != null)
            {
                u.Sort = Sort;
                if (await unitManager.Update(u))
                {
                    r.flag = 1;
                    r.msg = "修改排序成功";
                }
            }
            return r;
        }

        [HttpPost]
        [Route("departmentuserbatchset")]
        //<used>0</used>
        public async Task<Result> Department_UserBatchSet([FromBody] DepartmentBatchSetModel o)
        {
            Result r = new Result();
            List<long> listUserId = o.ListUserId;
            List<long> listDepartment = o.ListDepartment;

            r = await departmentManager.BatchSetDepartment(listUserId, listDepartment);
            return r;
        }

        /// <summary>
        /// 单位管理：保存更新单位Guid
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveschoolguid")]
        //<used>1</used>
        public async Task<Result> SaveSchoolGuid([FromBody] SchoolGuidModel model)
        {
            Result r = new Result();

            var list = await schoolExtensionManager.Query(f => f.UnitId != model.SchoolId && f.SchoolGuid == model.SchoolGuid);
            if (list.Count > 0)
            {
                r.flag = 0;
                r.msg = "存在相同编码，保存失败";
                return r;
            }
            PUnit unit = await unitManager.QueryById((object)model.SchoolId);
            if (unit != null)
            {
                var schoolExtension = await schoolExtensionManager.Query(f => f.UnitId == model.SchoolId);
                if (schoolExtension.Count > 0)
                {
                    PSchoolExtension obj = schoolExtension.FirstOrDefault();
                    if (model.Tp == 2 || (model.Tp == 1 && string.IsNullOrEmpty(obj.SchoolGuid)))
                    {
                        obj.SchoolGuid = model.SchoolGuid;
                        await schoolExtensionManager.Update(obj);
                        r.flag = 1;
                        r.msg = "保存成功";
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "该单位已经绑定Guid，不能修改！";
                    }
                }
                else
                {
                    await schoolExtensionManager.Add(
                          new PSchoolExtension
                          {
                              UnitId = model.SchoolId,
                              SchoolGuid = model.SchoolGuid
                          }
                          );
                    r.flag = 1;
                    r.msg = "保存成功";
                }
            }

            r.flag = 1;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 单位信息管理列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("schoolinfofind")]
        public async Task<Result> SchoolInfo_Find([FromBody] VSchoolInfoParam param)
        {
            Result r = new Result();
            param.statuzgt = 0;
            if (user.UnitTypeId == 2)
            {
                param.CountyId = user.UnitId;
            }
            else if (user.UnitTypeId == 1)
            {
                param.CityId = user.UnitId;
            }
            PageModel<VSchoolInfo> pg = await vSchoolInfoManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        [HttpPost]
        [Route("unitprint")]
        //<used>1</used>
        public async Task<Result> Unit_Print([FromBody] VSchoolInfoParam param)
        {
            Result r = new Result();
            param.Pid = user.UnitId;
            param.pageSize = int.MaxValue;
            PageModel<VSchoolInfo> pg = await vSchoolInfoManager.GetSchoolDetail(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 用户管理：保存批量设置用户角色（管理员）
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userrolebatchset")]
        //<used>1</used>
        public async Task<Result> UserRole_BatchSet([FromBody] RoleBatchSetModel m)
        {
            Result r = new Result();
            string listRoleIds = m.ListRoleIds;
            string listUserIds = m.ListUserIds;
            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CityAdmin)
               && !user.Roles.Contains(RoleTypes.CoutyAdmin) && !user.Roles.Contains(RoleTypes.SchoolAdmin)
               && !user.Roles.Contains(RoleTypes.CompanyAdmin))
            {
                r.flag = 0;
                r.msg = "只有管理员才能批量修改用户角色";
                return r;
            }
            r = await userManager.BatchSetRole(listUserIds, listRoleIds, user.UnitId, user.ID, user.UnitTypeId, 1);
            return r;
        }

        /// <summary>
        /// 用户管理：保存批量设置用户角色（管理员）
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userrolechildunitbatchset")]
        //<used>1</used>
        public async Task<Result> UserRoleChildUnit_BatchSet([FromBody] RoleBatchSetModel m)
        {
            Result r = new Result();
            string listRoleIds = m.ListRoleIds;
            string listUserIds = m.ListUserIds;
            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CityAdmin)
                && !user.Roles.Contains(RoleTypes.CoutyAdmin) && !user.Roles.Contains(RoleTypes.SchoolAdmin)
                && !user.Roles.Contains(RoleTypes.CompanyAdmin))
            {
                r.flag = 0;
                r.msg = "只有管理员才能批量修改用户角色";
                return r;
            }
            string strRoleIds = "," + listRoleIds + ",";
            if (!user.IsSystemUser)
            {
                if (strRoleIds.Contains(",0,"))
                {
                    r.flag = 0;
                    r.msg = "非法操作";
                    return r;
                }
            }

            if (user.UnitTypeId == 1)
            {
                //市级超管只能添加区县账号信息
                if (strRoleIds.Contains(",3") || strRoleIds.Contains(",4") || strRoleIds.Contains(",1"))
                {
                    r.flag = 0;
                    r.msg = "非法操作";
                    return r;
                }

            }
            else if (user.UnitTypeId == 2)
            {
                if (strRoleIds.Contains(",2") || strRoleIds.Contains(",1") || strRoleIds.Contains(",4"))
                {
                    r.flag = 0;
                    r.msg = "非法操作";
                    return r;
                }
            }
            r = await userManager.BatchSetRole(listUserIds, listRoleIds, user.UnitId, user.ID, user.UnitTypeId, 2);
            return r;
        }

       
        /// <summary>
        /// 导入单位信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("superadminuploadunitfile")]
        public async Task<Result> SuperAdminUploadUnitFile([FromBody] ImportUnitParam param)
        {
            Result r = new Result();
            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CityAdmin)
               && !user.Roles.Contains(RoleTypes.CoutyAdmin))
            {
                r.flag = 0;
                r.msg = "您无权使用该功能";
                return r;
            }
            if (user.IsSystemUser)
            {
                List<CountyCityCompanyImportDto> list = new ExcelHelper<CountyCityCompanyImportDto>().ImportFromExcel(env.ContentRootPath, param.FilePath,0);
                r = await unitManager.SaveCountyCityCompanyData(list);
            }
            else
            {
                //区县导单位
                if (user.UnitTypeId == UnitTypeEnum.County.ToEnumInt())
                {
                    List<SchoolImportDto> list = new ExcelHelper<SchoolImportDto>().ImportFromExcel(env.ContentRootPath, param.FilePath, 1);
                    r = await unitManager.SaveSchoolViewData(list);
                }
                //市级导区县
                else if (user.UnitTypeId == UnitTypeEnum.City.ToEnumInt())
                {
                    List<CountyCityCompanyImportDto> list = new ExcelHelper<CountyCityCompanyImportDto>().ImportFromExcel(env.ContentRootPath, param.FilePath,0);
                    r = await unitManager.SaveCountyCityCompanyData(list);
                }
            }
            return r;
        }
 
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="sheet"></param>
        /// <param name="pid"></param>
        /// <param name="industryId">1普教、2高教、3职教、4机关、5家庭</param>
        /// <param name="unitType">1市级、2区县、3单位、4企业</param>
        /// <param name="cityId"></param>
        /// <returns></returns>
        private async Task<Result> SaveData(DataTable sheet, long pid, int industryId, int unitType, long cityId)
        {
            Result r = new Result();
            //整理数据
            List<PUnit> vos = new List<PUnit>();
            List<PUnit> pos = new List<PUnit>();
            //bool bHaveSchool = false;
            for (int i = 1; i < sheet.Rows.Count; i++)
            {
                DataRow dr = sheet.Rows[i];

                PUnit unit = new PUnit();           //单位信息
                BUnitSetting unitSetting;           //区县，单位设置
                //PSchoolExtension schoolExtension;   //单位扩展
                //BRepairHomeFee repairHomeFee;       //上门费
                //0        1        2        3    4    5    6    7            8        9    10     11  
                //单位编号|单位名称|单位简称|法人|地址|邮编|网址|组织机构代码|联系电话|邮箱|员工数|简介
                unit.Code = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 0)).ToUpper();

                if (user.UnitTypeId == 1 || user.UnitTypeId == 2)
                {
                    unit.AreaId = user.AreaId;
                }
                else
                {
                    if (cityId > 0)
                    {
                        unit.AreaId = long.Parse(ApplicationConfig.defaultSetdefaultCity);
                    }
                }
                unit.PId = pid;
                unit.IndustryId = industryId;
                unit.UnitType = unitType;
                unit.Name = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 1));
                unit.Brief = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 2));
                unit.Legal = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                unit.Address = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 4));
                unit.ZipCode = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 5));
                unit.Url = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 6));
                unit.OrganizationCode = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 7));
                unit.Tel = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 8));
                unit.Email = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 9));
                unit.EmployeeNum = ExeclHelp.GetValue<int>(dr, 10);
                unit.Introduction = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 11));
                if (unit.Introduction != null && unit.Introduction.Length > 1023)
                {
                    unit.Introduction = unit.Introduction.Substring(0, 1020);
                }
                unit.RegTime = DateTime.Now;
                unit.UserId = user.ID;
                unit.Statuz = 1;

                //if (unitType == 3)
                //{
                //    bHaveSchool = true;
                //}

                //数据合法性校验
                //编码、名称、简称不能为空
                if (string.IsNullOrEmpty(unit.Code) || unit.Code.Length < 3 || string.IsNullOrEmpty(unit.Name) || string.IsNullOrEmpty(unit.Brief))
                {
                    unit.Memo = "编码、名称、简称不能为空，或者编码不符合规则";
                    pos.Add(unit);
                    continue;
                }
                //编码规则校验
                string[] iTypes = { "A", "B", "C", "D", "E" };
                string[] uTypes = { "S", "Q", "X", "G" };
                if (!(unitType > 0 && unitType < 4 && unit.Code.Substring(0, 1).Equals(iTypes[industryId - 1]) && unit.Code.Substring(1, 1).Equals(uTypes[unitType - 1])
                    || unitType == 4 && unit.Code.Substring(0, 1).Equals(uTypes[unitType - 1])))
                {
                    unit.Memo = "编码不符合规则";
                    pos.Add(unit);
                    continue;
                }
            
                string whereCode = string.Format(" code = '{0}'", unit.Code);
                List<PUnit> listP = await unitManager.Query(f => f.Code == unit.Code);
                if (listP.Count > 0)
                {
                    var po = listP.Where(m => m.Code == unit.Code).FirstOrDefault();
                    unit.Memo = "编码在系统中已经存在";

                    po.AreaId = unit.AreaId;
                    if (await SaveComanyExtension(dr, po))
                    {
                        unit.Memo += "，但服务类型增加成功";
                        vos.Add(unit);
                    }
                    else
                    {
                        //添加服务商或者供应商成功也算成功
                        unit.Memo += "，服务类型系统中已经存在";
                        pos.Add(unit);
                    }
                    continue;
                }

                whereCode = string.Format(" name = '{0}'", unit.Name);
                List<PUnit> listUnit = await unitManager.Query(f => f.Name == unit.Name);
                if (listUnit.Count > 0)
                {
                    unit.Memo = "单位名称在系统中已经存在";
                    pos.Add(unit);
                    continue;
                }


                //保存单位信息到数据库
                vos.Add(unit);
                await unitManager.Add(unit);

                //处理保存区县、单位默认系统参数
                if (unit.UnitType == UnitTypes.Couty.ObjToInt() || unit.UnitType == UnitTypes.School.ObjToInt())
                {
                    unitSetting = unitSettingManager.Query(f => f.UnitId == unit.Id).Result.FirstOrDefault();
                    if (unitSetting == null)
                    {
                        unitSetting = new BUnitSetting();
                        unitSetting.UnitId = unit.Id;
                        await unitSettingManager.Add(unitSetting);
                    }
                }


            }
            r.flag = 1;
            r.data.rows = vos;
            r.data.footer = pos;
            r.msg = $"信息导入成功 {vos.Count}条，导入失败 {pos.Count}条";

            return r;
        }

        /// <summary>
        /// 保存导入的单位数据信息
        /// </summary>
        /// <param name="sheet"></param>
        /// <param name="pid"></param>
        /// <param name="industryId"></param>
        /// <param name="unitType"></param>
        /// <param name="cityId"></param>
        private async Task<Result> SaveSchoolData(DataTable sheet, long pid, int industryId, int unitType, long cityId)
        {
            Result r = new Result();
            //整理数据
            List<PUnitDto> vos = new List<PUnitDto>();
            List<PUnitDto> pos = new List<PUnitDto>();

            List<BDictionary> listDic = await dictionaryManager.Query(f => f.TypeCode == "1101");
            string strDictionary = string.Join(",", listDic);

            for (int i = 1; i < sheet.Rows.Count; i++)
            {
                DataRow dr = sheet.Rows[i];

                PUnitDto unit = new PUnitDto();

                unit.AreaId = user.AreaId;
                unit.PId = pid;
                unit.IndustryId = industryId;
                unit.UnitType = unitType;
                unit.Code = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 0)).ToUpper();
                unit.Name = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 1));
                unit.Brief = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 2));
                unit.RegTime = DateTime.Now;
                unit.UserId = user.ID;
                unit.Statuz = 1;

                if (string.IsNullOrEmpty(unit.Code))
                {
                    unit.Memo = "单位编码不能为空";
                    pos.Add(unit);
                    continue;
                }

                if (string.IsNullOrEmpty(unit.Name))
                {
                    unit.Memo = "单位名称不能为空";
                    pos.Add(unit);
                    continue;
                }

                if (string.IsNullOrEmpty(unit.Brief))
                {
                    unit.Memo = "单位简称不能为空";
                    pos.Add(unit);
                    continue;
                }

                List<PUnit> listP = await unitManager.Query(f => f.Name == unit.Name && f.Statuz == 1 || f.Code == unit.Code);
                if (listP.Exists(m => m.Code == unit.Code))
                {
                    unit.Memo = "单位编码已经存在";
                    pos.Add(unit);
                    continue;
                }
                if (listP.Exists(m => m.Name == unit.Name))
                {
                    unit.Memo = "单位名称已经存在";
                    pos.Add(unit);
                    continue;
                }

                string schoolStage = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3)).Trim();
                int classNum = ExeclHelp.GetValue<int>(dr, 4);
                int studentNum = ExeclHelp.GetValue<int>(dr, 5);
                int teacherNum = ExeclHelp.GetValue<int>(dr, 6);
                decimal floorArea = ExeclHelp.GetValue<decimal>(dr, 7);
                decimal buildArea = ExeclHelp.GetValue<decimal>(dr, 8);

                unit.StrSchoolStage = schoolStage;
                unit.ClassNum = classNum;
                unit.StudentNum = studentNum;
                unit.TeacherNum = teacherNum;
                unit.FloorArea = floorArea;
                unit.BuildArea = buildArea;

                //判断学段是否存在
                if (!string.IsNullOrEmpty(schoolStage))
                {
                    List<BDictionary> list = listDic.Where(a => a.DicName == schoolStage).ToList();
                    if (list.Count > 0)
                    {
                        BDictionary b = list[0];
                        if (b != null)
                        {
                            unit.SchoolStage = int.Parse(b.DicValue);
                        }
                    }
                    else
                    {
                        unit.Memo = "填写的单位属性不存在";
                        pos.Add(unit);
                        continue;
                    }
                }
                else
                {
                    unit.SchoolStage = -1;
                }
                r = await unitManager.InsertUpdate(unit);
                if (r.flag == 1)
                {
                    long id = long.Parse(r.data.rows.ToString());
                    List<PSchoolExtension> schoolExtension = await schoolExtensionManager.Query(f => f.UnitId == id);
                    if (schoolExtension.Count > 0)
                    {
                        //根据需求修改班级数、教师数、学生数都没填写时默认为1  zyf 20200611 (解决装备评估以0做除数的问题)
                        if (classNum == 0)
                        {
                            classNum = 1;
                        }
                        if (teacherNum == 0)
                        {
                            teacherNum = 1;
                        }
                        if (studentNum == 0)
                        {
                            studentNum = 1;
                        }

                        PSchoolExtension e = schoolExtension[0];
                        if (e != null)
                        {
                            e.SchoolStage = unit.SchoolStage;
                            e.ClassNum = classNum;
                            e.TeacherNum = teacherNum;
                            e.StudentNum = studentNum;
                            e.FloorArea = floorArea;
                            e.BuildArea = buildArea;
                            await schoolExtensionManager.Update(e);
                        }
                    }
                    else
                    {
                        PSchoolExtension se = new PSchoolExtension();
                        se.UnitId = id;
                        se.ClassNum = classNum;
                        se.StudentNum = studentNum;
                        se.TeacherNum = teacherNum;
                        se.FloorArea = floorArea;
                        se.BuildArea = buildArea;
                        se.SchoolStage = unit.SchoolStage;
                        await schoolExtensionManager.Add(se);
                    }

                    vos.Add(unit);
                }
                else
                {
                    pos.Add(unit);
                    continue;
                }

            }

            r.flag = 1;
            r.data.rows = vos;
            r.data.footer = pos;
            r.msg = $"信息导入成功 {vos.Count}条，导入失败 {pos.Count}条";

            return r;
        }

        private async Task<bool> SaveComanyExtension(DataRow dr, PUnit unit)
        {
            PCompanyExtension companyExtension;
            //处理企业对应类型
            if (unit.UnitType == UnitTypes.Company.ObjToInt())
            {
                companyExtension = new PCompanyExtension();
                companyExtension.UnitId = unit.Id;

                //支持文字支持数字。
                string serverType = ExeclHelp.GetValue<string>(dr, 12);
                if (serverType == null)
                {
                    return false;
                }
                if (serverType == "1" || serverType.Contains("服务商"))
                {
                    companyExtension.ServerType = 1;
                }
                else if (serverType == "2" || serverType.Contains("供应商"))
                {
                    companyExtension.ServerType = 2;
                }
                else if (serverType == "3" || serverType.Contains("厂商"))
                {
                    companyExtension.ServerType = 3;
                }
                else
                {
                    return false;
                }

                //TODO: 当前默认南京819
                companyExtension.AreaId = long.Parse(ApplicationConfig.defaultSetdefaultCity);
                //12
                //企业性质
                List<PCompanyExtension> ces = await companyExtensionManager.Query(f => f.UnitId == unit.Id);
                if (ces.Count == 0)
                {
                    await companyExtensionManager.Add(companyExtension);
                    return true;
                }
            }

            return false;
        }

        #region 用户白名单
        /// <summary>
        /// 用户白名单列表
        /// </summary>
        /// <users>
        /// 适用角色：超级管理员、区县管理员
        /// </users>
        /// <remarks>
        /// 功能说明：
        /// 作   者：李世申
        /// 创建日期：2022-04-26
        /// 修改说明：
        /// </remarks>
        [HttpPost]
        [Route("userthirdallowfind")]
        //<used>1</used>
        public async Task<Result> OUserThirdAllow_Find([FromBody] OUserThirdAllowParam param)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                if (user.Roles.Contains(RoleTypes.CoutyAdmin) || user.Roles.Contains(RoleTypes.SchoolAdmin))
                {
                    param.unitId = user.UnitId;
                }
                else
                {
                    param.unitId = -1;
                }
            }
            PageModel<OUserThirdAllow> pg = await oUserThirdAllowManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 用户白名单实体信息
        /// </summary>
        /// <users>
        /// 适用角色：超级管理员、区县管理员
        /// </users>
        /// <remarks>
        /// 功能说明：
        /// 作   者：李世申
        /// 创建日期：2022-04-26
        /// 修改说明：
        /// </remarks>
        [HttpPost]
        [Route("userthirdallowgetbyid")]
        public async Task<Result> OUserThirdAllow_GetById(long id)
        {
            Result r = new Result();
            var entity = await oUserThirdAllowManager.GetById(id);
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = entity;
            return r;
        }

        /// <summary>
        /// 用户白名单删除
        /// </summary>
        /// <users>
        /// 适用角色：超级管理员、区县管理员
        /// </users>
        /// <remarks>
        /// 功能说明：
        /// 作   者：李世申
        /// 创建日期：2022-04-26
        /// 修改说明：
        /// </remarks>
        [HttpPost]
        [Route("userthirdallowdelete")]
        public async Task<Result> OUserThirdAllow_Delete(long id)
        {
            Result r = new Result();
            var entity = await oUserThirdAllowManager.GetById(id);
            if (user.IsSystemUser)
            {
                if (await oUserThirdAllowManager.DeleteById(id))
                {
                    r.flag = 1;
                    r.msg = "删除成功";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "删除失败";
                }
            }
            else if (user.Roles.Contains(RoleTypes.CoutyAdmin) || user.Roles.Contains(RoleTypes.SchoolAdmin))
            {
                if (entity.UnitId == user.UnitId)
                {
                    if (await oUserThirdAllowManager.DeleteById(id))
                    {
                        r.flag = 1;
                        r.msg = "删除成功";
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "删除失败";
                    }
                }
                else
                {
                    r.flag = 0;
                    r.msg = "你无权操作。";
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "你无权操作。";
            }
            return r;
        }

        /// <summary>
        /// 保存用户白名单
        /// </summary>
        /// <users>
        /// 适用角色：超级管理员、区县管理员
        /// </users>
        /// <remarks>
        /// 功能说明：1:验证手机号唯一（账号是否要验证。）
        /// 作   者：李世申
        /// 创建日期：2022-04-26
        /// 修改说明：
        /// </remarks>
        [HttpPost]
        [Route("userthirdallowsave")]
        public async Task<Result> OUserThirdAllow_Save([FromBody] OUserThirdAllow o)
        {
            Result r = new Result();
            var model = new OUserThirdAllow();
            if (!string.IsNullOrEmpty(o.UserName) && o.UserName.Length > 63)
            {
                r.flag = 0;
                r.msg = "人员名称不能大于63个字符";
                return r;
            }

            if (!string.IsNullOrEmpty(o.RoleNames) && o.RoleNames.Length > 511)
            {
                r.flag = 0;
                r.msg = "角色不能大于511个字符";
                return r;
            }

            if (string.IsNullOrEmpty(o.Mobile) || o.Mobile.Length != 11)
            {
                r.flag = 0;
                r.msg = "手机号码不正确，请正确填写手机号码。";
                return r;
            }

            if (o.Id > 0)
            {
                var entity = await oUserThirdAllowManager.GetById(o.Id);
                if (entity != null)
                {
                    model = entity;
                }
                else
                {
                    r.flag = 0;
                    r.msg = "当前用户信息已不存在。";
                    return r;
                }
            }
            else
            {
                model.UserId = user.ID;
                model.UnitId = user.UnitId;
            }
            if (user.UnitTypeId == EnumExtensions.ToEnumInt(UnitTypes.School))
            {
                model.UnitName = user.UnitName;
                model.UnitTypeId = EnumExtensions.ToEnumInt(UnitTypes.School);
                model.UnitTypeName = "单位";
                o.UnitTypeId = EnumExtensions.ToEnumInt(UnitTypes.School);
            }
            else
            {
                model.UnitName = ComLib.filterSqlValue(o.UnitName);
                model.UnitTypeId = o.UnitTypeId;
                model.UnitTypeName = o.UnitTypeName;
            }

            model.UserName = o.UserName;
            model.Mobile = o.Mobile;
            model.RoleNames = o.RoleNames;
            model.RegTime = DateTime.Now;
            if (o.UnitTypeId != EnumExtensions.ToEnumInt(UnitTypes.School) && o.UnitTypeId != EnumExtensions.ToEnumInt(UnitTypes.Couty))
            {
                r.flag = 0;
                r.msg = "请选择单位类型。";
                return r;
            }
            //分析转换，角色Id，类型，
            var roleResult = await GetRoleIds(o.RoleNames, o.UnitTypeId);
            if (roleResult.flag == 1)
            {
                model.RoleIds = roleResult.data.rows.ToString();
            }
            else
            {
                return roleResult;
            }
            var list = await oUserThirdAllowManager.Find(f => f.Mobile == model.Mobile && f.UnitTypeId == model.UnitTypeId && f.UnitName == model.UnitName);
            if (list != null && list.Count > 0)
            {
                if (o.Id > 0)
                {
                    if (list.Count > 1 || list[0].Id != o.Id)
                    {
                        r.flag = 0;
                        r.msg = "手机号码验证失败，你填写的手机号码当前单位已存在。";
                        return r;
                    }
                }
                else
                {
                    r.flag = 0;
                    r.msg = "手机号码验证失败，你填写的手机号码当前单位已存在。";
                    return r;
                }
            }
            if (o.Id > 0)
            {
                await oUserThirdAllowManager.Update(model);
            }
            else
            {
                await oUserThirdAllowManager.Add(model);
            }

            r.flag = 1;
            r.msg = "保存成功";
            return r;
        }
        /// <summary>
        /// 根据角色名称转换成角色Id集合方法。
        /// </summary>
        /// <param name="names">角色名称</param>
        /// <param name="unittypeid">角色了类型</param>
        /// <returns></returns>
        private async Task<Result> GetRoleIds(string names, int unittypeid)
        {
            Result r = new Result();
            var roleIdList = new List<long>();
            var roleErrorName = new List<string>();
            if (!string.IsNullOrEmpty(names))
            {
                //string where = string.Format(" RoleType = {0} ", unittypeid);
                var roleList = await roleManager.Find(f => f.RoleType == unittypeid);
                var arrRoleName = names.Replace("，", ",").Split(',');
                if (arrRoleName != null && arrRoleName.Count() > 0)
                {
                    foreach (var item in arrRoleName)
                    {
                        if (!string.IsNullOrEmpty(item))
                        {
                            var roleEntity = roleList.Where(m => m.Name == item && m.RoleType == unittypeid).FirstOrDefault();
                            if (roleEntity != null && roleEntity.Id > 0)
                            {
                                roleIdList.Add(roleEntity.Id);
                            }
                            else
                            {
                                roleErrorName.Add(item);
                            }
                        }
                    }
                }
            }
            if (roleErrorName.Count > 0)
            {
                r.flag = 0;
                r.msg = string.Format("角色名称【{0}】和平台角色不匹配。", string.Join(",", roleErrorName));

            }
            else if (roleIdList.Count > 0)
            {
                r.flag = 1;
                r.data.rows = string.Join(",", roleIdList);
            }
            else
            {
                r.flag = 0;
                r.msg = "角色和平台角色名称不匹配。";
            }
            return r;
        }
        #endregion


        /// <summary>
        /// 白名单导入
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("uploaduserthirdfile")]
        public async Task<Result> UploadUserThirdFile()
        {
            Result r = new Result();
            var files = await Request.ReadFormAsync();
            r = FileHelper.UploadExecl(env.WebRootPath, files);
            if (r.flag == 1)
            {
                DataTable sheet = r.data.rows as DataTable;
                BAttachmentData attach = r.data.other as BAttachmentData;
                await attachmentDataManager.Add(attach);

                //拷贝以前的装备平台导入代码
                string field = ApplicationConfig.ImportUserThirdAllow;
                if (user.UnitTypeId == 3)
                {
                    field = field.Replace("单位名称|", "").Replace("类型|", "");
                }
                if (string.IsNullOrEmpty(field))
                {
                    r.flag = 0;
                    r.msg = "配置文件中未配置“hyun:Import.UserThirdAllow”";
                    return r;
                }
                r = ExeclHelp.ValidateFile(field, sheet);
                if (r.flag == 1)
                {
                    var userList = await oUserThirdAllowManager.Find(null);
                    var roleList = await roleManager.Find(null);
                    if (roleList == null || roleList.Count == 0)
                    {
                        r.msg = "导入失败，平台角色信息未初始化。";
                        return r;
                    }

                    //整理数据
                    List<OUserThirdAllow> vos = new List<OUserThirdAllow>();//导入成功的信息
                    List<OUserThirdAllow> pos = new List<OUserThirdAllow>();//导入失败的信息

                    int rowsCount = sheet.Rows.Count;
                    if (user.UnitTypeId == EnumExtensions.ToEnumInt(UnitTypes.School))
                    {
                        for (int i = 1; i < rowsCount; i++)
                        {
                            DataRow dr = sheet.Rows[i];
                            OUserThirdAllow entity = new OUserThirdAllow();
                            entity.UserName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 0));
                            entity.Mobile = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 1));
                            entity.RoleNames = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 2));
                            entity.UnitName = user.UnitName;
                            entity.UnitTypeId = EnumExtensions.ToEnumInt(UnitTypes.School);
                            entity.UnitTypeName = "单位";
                            entity.RegTime = DateTime.Now;

                            #region 验证
                            if (string.IsNullOrEmpty(entity.UserName))
                            {
                                entity.ErrorMsg = "人员名称未填写";
                                pos.Add(entity);
                                continue;
                            }
                            else if (entity.UserName.Length > 30)
                            {
                                entity.ErrorMsg = "人员名称不能超过30字符";
                                pos.Add(entity);
                                continue;
                            }

                            if (string.IsNullOrEmpty(entity.Mobile) || entity.Mobile.Length != 11)
                            {
                                entity.ErrorMsg = "手机号码填写不正确";
                                pos.Add(entity);
                                continue;
                            }
                            else
                            {
                                //验证手机号是否在平台中已存在。
                                if (userList != null && userList.Count > 0)
                                {
                                    var templist = userList.Where(m => m.Mobile == entity.Mobile && m.UnitTypeId == entity.UnitTypeId && m.UnitName == entity.UnitName);
                                    if (templist != null && templist.Count() > 0)
                                    {
                                        entity.ErrorMsg = "手机号码当前单位已存在";
                                        pos.Add(entity);
                                        continue;
                                    }
                                }
                            }
                            if (string.IsNullOrEmpty(entity.RoleNames))
                            {
                                entity.ErrorMsg = "角色未填写";
                                pos.Add(entity);
                                continue;
                            }
                            else if (entity.RoleNames.Length > 500)
                            {
                                entity.ErrorMsg = "角色不能超过500字符";
                                pos.Add(entity);
                                continue;
                            }
                            else
                            {
                                var roleIdList = new List<long>();
                                var roleErrorName = new List<string>();
                                var arrRoleName = entity.RoleNames.Replace("，", ",").Split(',');
                                if (arrRoleName != null && arrRoleName.Count() > 0)
                                {
                                    foreach (var item in arrRoleName)
                                    {
                                        if (!string.IsNullOrEmpty(item))
                                        {
                                            var roleEntity = roleList.Where(m => m.Name == item && m.RoleType == entity.UnitTypeId).FirstOrDefault();
                                            if (roleEntity != null && roleEntity.Id > 0)
                                            {
                                                roleIdList.Add(roleEntity.Id);
                                            }
                                            else
                                            {
                                                roleErrorName.Add(item);
                                            }
                                        }
                                    }
                                }
                                if (roleErrorName.Count > 0)
                                {
                                    entity.ErrorMsg = $"角色名称【{string.Join(",", roleErrorName)}】和平台角色不匹配";
                                    pos.Add(entity);
                                    continue;
                                }
                                if (roleIdList.Count > 0)
                                {
                                    entity.RoleIds = string.Join(",", roleIdList);
                                }
                                else
                                {
                                    entity.ErrorMsg = "角色和平台角色名称不匹配";
                                    pos.Add(entity);
                                    continue;
                                }
                            }
                            #endregion

                            entity.UserId = user.ID;
                            entity.UnitId = user.UnitId;
                            vos.Add(entity);
                        }
                    }
                    else
                    {
                        for (int i = 1; i < rowsCount; i++)
                        {
                            DataRow dr = sheet.Rows[i];
                            OUserThirdAllow entity = new OUserThirdAllow();
                            entity.UnitName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 0));
                            entity.UserName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 1));
                            entity.Mobile = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 2));
                            entity.UnitTypeName = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 3));
                            entity.RoleNames = StringHelper.ClearBR(ExeclHelp.GetValue<string>(dr, 4));
                            entity.RegTime = DateTime.Now;
                            #region 验证
                            if (string.IsNullOrEmpty(entity.UnitName))
                            {
                                entity.ErrorMsg = "单位名称未填写";
                                pos.Add(entity);
                                continue;
                            }
                            else if (entity.UnitName.Length > 200)
                            {
                                entity.ErrorMsg = "单位名称不能超过200字符";
                                pos.Add(entity);
                                continue;
                            }
                            if (string.IsNullOrEmpty(entity.UserName))
                            {
                                entity.ErrorMsg = "人员名称未填写";
                                pos.Add(entity);
                                continue;
                            }
                            else if (entity.UserName.Length > 30)
                            {
                                entity.ErrorMsg = "人员名称不能超过30字符";
                                pos.Add(entity);
                                continue;
                            }
                            if (string.IsNullOrEmpty(entity.UnitTypeName) || (entity.UnitTypeName != "单位" && entity.UnitTypeName != "教育局"))
                            {
                                entity.ErrorMsg = "类型填写不正确，请从下拉中选择";
                                pos.Add(entity);
                                continue;
                            }
                            else
                            {
                                entity.UnitTypeId = EnumExtensions.ToEnumInt(UnitTypes.School);
                                if (entity.UnitTypeName == "教育局")
                                {
                                    entity.UnitTypeId = EnumExtensions.ToEnumInt(UnitTypes.Couty);
                                }
                            }
                            if (string.IsNullOrEmpty(entity.Mobile) || entity.Mobile.Length != 11)
                            {
                                entity.ErrorMsg = "手机号码填写不正确";
                                pos.Add(entity);
                                continue;
                            }
                            else
                            {
                                //验证手机号是否在平台中已存在。
                                if (userList != null && userList.Count > 0)
                                {
                                    var templist = userList.Where(m => m.Mobile == entity.Mobile && m.UnitTypeId == entity.UnitTypeId && m.UnitName == entity.UnitName);
                                    if (templist != null && templist.Count() > 0)
                                    {
                                        entity.ErrorMsg = "手机号码当前单位已存在";
                                        pos.Add(entity);
                                        continue;
                                    }
                                }
                            }
                            if (string.IsNullOrEmpty(entity.RoleNames))
                            {
                                entity.ErrorMsg = "角色未填写";
                                pos.Add(entity);
                                continue;
                            }
                            else if (entity.RoleNames.Length > 500)
                            {
                                entity.ErrorMsg = "角色不能超过500字符";
                                pos.Add(entity);
                                continue;
                            }
                            else
                            {
                                var roleIdList = new List<long>();
                                var roleErrorName = new List<string>();
                                var arrRoleName = entity.RoleNames.Replace("，", ",").Split(',');
                                if (arrRoleName != null && arrRoleName.Count() > 0)
                                {
                                    foreach (var item in arrRoleName)
                                    {
                                        if (!string.IsNullOrEmpty(item))
                                        {
                                            var roleEntity = roleList.Where(m => m.Name == item && m.RoleType == entity.UnitTypeId).FirstOrDefault();
                                            if (roleEntity != null && roleEntity.Id > 0)
                                            {
                                                roleIdList.Add(roleEntity.Id);
                                            }
                                            else
                                            {
                                                roleErrorName.Add(item);
                                            }
                                        }
                                    }
                                }
                                if (roleErrorName.Count > 0)
                                {
                                    entity.ErrorMsg = $"角色名称【{string.Join(",", roleErrorName)}】和平台角色不匹配";
                                    pos.Add(entity);
                                    continue;
                                }
                                if (roleIdList.Count > 0)
                                {
                                    entity.RoleIds = string.Join(",", roleIdList);
                                }
                                else
                                {
                                    entity.ErrorMsg = "角色和平台角色名称不匹配";
                                    pos.Add(entity);
                                    continue;
                                }

                            }
                            #endregion

                            entity.UserId = user.ID;
                            entity.UnitId = user.UnitId;
                            entity.ErrorMsg = "";
                            vos.Add(entity);
                        }
                    }

                    if (vos.Count > 0)
                    {
                        await oUserThirdAllowManager.Add(vos);
                    }


                    r.data.rows = vos;
                    r.data.footer = pos;
                    r.msg = $"信息导入成功 {vos.Count}条，导入失败 {pos.Count}条";
                }
            }
            return r;
        }


        /// <summary>
        /// 单位管理：获取所有单位集合（除企业）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("useunitallfind")]
        public async Task<Result> UseUnitAll_Find()
        {
            Result r = new Result();
            List<PUnit> list = await unitManager.Query(f => f.UnitType != 4 && f.Statuz == 1);
            r.flag = 1;
            r.msg = "";
            r.data.rows = mapper.Map<List<PUnitDto>>(list);
            return r;
        }

        #region 获取单位信息接口

        /// <summary>
        /// 方案选用-根据Id获取对象
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getinfobyid")]
        public async Task<Result<PUnit>> GetInfoById(long id = 0)
        {
            Result<PUnit> r = new Result<PUnit>();
            long queryUnitId = user.UnitId;
            if (id > 0)
            {
                queryUnitId = id;
            }
            var entity = await unitManager.QueryById((object)queryUnitId);
            if (entity != null)
            {
                return baseSucc<PUnit>(new PUnit() { IsCountyManager = entity.IsCountyManager, Name = entity.Name }, 1, "查询成功");
            }
            else
            {
                return baseFailed<PUnit>("未查询到相关信息");
            }
        }
        #endregion


        /// <summary>
        /// 获取区县、市级单位信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getxfunitinfo")]
        public async Task<Result<PUnitSetDto>> GetXfUnitInfo()
        {
            var msgdata = await unitManager.GetXfUnitPaged();
            return msgdata;
        }


        /// <summary>
        /// 设置区县、市级单位信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("setxfunitinfo")]
        public async Task<Result<string>> SetXfUnitInfo([FromBody] PUnitSetDto o)
        {
            var msgdata = await unitManager.SetXfUnitInfo(o);
            return msgdata;
        }

        /// <summary>
        /// 单位管理：获取学校
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getschoolunitinfo")]
        public async Task<Result> GetSchoolUnitInfo()
        {
            var msgdata = await unitManager.GetSchoolUnitInfo();
            return msgdata;
        }

        /// <summary>
        /// 单位管理：保存学校信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveschoolunitinfo")]
        public async Task<Result<string>> SaveSchoolUnitInfo([FromBody] SchoolUnitInfoModel o)
        {
            return await unitManager.SaveSchoolUnitInfo(o);
        }

    }
}
