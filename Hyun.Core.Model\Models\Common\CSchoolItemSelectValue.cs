namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位标题条目选项值
    ///</summary>
    [SugarTable("c_SchoolItemSelectValue","单位标题条目选项值")]
    public class CSchoolItemSelectValue : BaseEntity
    {

          public CSchoolItemSelectValue()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///标准条目Id
          /// </summary>
          public long StandardItemId { get; set; }

           /// <summary>
           ///标准条目结果Id
          /// </summary>
          public long SchoolItemValueId { get; set; }

           /// <summary>
           ///地址表Id
          /// </summary>
          public long AddressId { get; set; }

        /// <summary>
        ///面积
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]

        public decimal LocationArea { get; set; }

           /// <summary>
           ///座位数
          /// </summary>
          public int Seatz { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Remark { get; set; }

        /// <summary>
        ///资产金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal AssetsAmount { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

