﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcBaseWaste接口方法
    ///</summary>
    public interface IDcBaseWasteServices : IBaseServices<DcBaseWaste>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<DcBaseWaste> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<DcBaseWaste>> Find(Expression<Func<DcBaseWaste, bool>> expression);
        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">DcBaseWasteParam实体参数</param>
        /// <param name="orderBy">排
        Task<List<DcBaseWaste>> Find(DcBaseWasteParam param);
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcBaseWasteParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<DcBaseWaste>> GetPaged(DcBaseWasteParam param);
        /// <summary>
        /// 存量台账
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<DcSchoolMaterialModelDto>> GetStockbookStand(DcSchoolMaterialModelParam param);
        /// <summary>
        /// 处置台账
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<DcBaseWasteDto>> GetWasteStandbook(DcWasteDisposalDetailParam param);
    }
}

