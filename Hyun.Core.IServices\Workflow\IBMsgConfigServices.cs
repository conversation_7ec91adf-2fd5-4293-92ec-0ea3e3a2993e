﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///BMsgConfig接口方法
    ///</summary>
    public interface IBMsgConfigServices : IBaseServices<BMsgConfig>
    {


        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">BMsgConfigParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<BMsgConfig>> GetPaged(BMsgConfigParam param);

        /// <summary>
        /// 添加修改
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> InsertUpdate(BMsgConfigDto o);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        Task<Result<string>> BatchSetStatuz(MsgConfigStatuzModel m);

    }
}

