
Date：2025-08-19 13:07:43.137
LogLevel：Information
Message：Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.178
LogLevel：Information
Message：Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.188
LogLevel：Information
Message：Quartz Scheduler created
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.190
LogLevel：Information
Message：RAMJobStore initialized.
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.192
LogLevel：Information
Message：Quartz Scheduler 3.14.0.0 - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.193
LogLevel：Information
Message：Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.194
LogLevel：Information
Message：Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.316
LogLevel：Information
Message：Start Initialization Db Seed Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.319
LogLevel：Information
Message：Start QuartzJob Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.323
LogLevel：Information
Message：Start Consul Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.330
LogLevel：Information
Message：Start EventBus Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.638
LogLevel：Information
Message：Now listening on: "http://[::]:9291"
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.787
LogLevel：Information
Message：Application started. Press Ctrl+C to shut down.
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.788
LogLevel：Information
Message：Hosting environment: "Development"
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:43.790
LogLevel：Information
Message：Content root path: "D:\工作管理\项目研发\维修平台\系统开发\程序\hyun.core\Hyun.Core.Api"
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:45.223
LogLevel：Information
Message：HTTP "GET" "/favicon.ico" QueryString:"" Body:""  responded 404 in 169.3521 ms
----------------------------------------------------------------------------------------------------
Date：2025-08-19 13:07:45.223
LogLevel：Information
Message：HTTP "POST" "/api/Values" QueryString:"" Body:null  responded 404 in 205.9166 ms
----------------------------------------------------------------------------------------------------