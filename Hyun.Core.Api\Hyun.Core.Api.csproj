﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\build\common.targets" />
  <PropertyGroup>
    <OutputType>Exe</OutputType>
	<ImplicitUsings>enable</ImplicitUsings>
    <!--<AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>-->
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <EnableUnsafeBinaryFormatterSerialization>true</EnableUnsafeBinaryFormatterSerialization>
    
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>..\Hyun.Core.Api\Hyun.Core.xml</DocumentationFile>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DocumentationFile>..\Hyun.Core\Hyun.Core.xml</DocumentationFile>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <ServerGarbageCollection>false</ServerGarbageCollection>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Extensions\**" />
    <Compile Remove="Hubs\**" />
    <Compile Remove="Logs\**" />
    <Compile Remove="Log\**" />
    <Compile Remove="Middlewares\**" />
    <Compile Remove="wwwroot\ui\**" />
    <Content Remove="Extensions\**" />
    <Content Remove="Hubs\**" />
    <Content Remove="Logs\**" />
    <Content Remove="Log\**" />
    <Content Remove="Middlewares\**" />
    <Content Remove="wwwroot\ui\**" />
    <EmbeddedResource Remove="Extensions\**" />
    <EmbeddedResource Remove="Hubs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <EmbeddedResource Remove="Log\**" />
    <EmbeddedResource Remove="Middlewares\**" />
    <EmbeddedResource Remove="wwwroot\ui\**" />
    <None Remove="Extensions\**" />
    <None Remove="Hubs\**" />
    <None Remove="Logs\**" />
    <None Remove="Log\**" />
    <None Remove="Middlewares\**" />
    <None Remove="wwwroot\ui\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="index.html" />
    <None Remove="WangWu.db" />
    <None Remove="WMBlog.db" />
    <None Remove="WMBlogLog.db" />
    <None Remove="WMBlogLog.db-journal" />
    <None Remove="ZhaoLiu.db" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="log4mongo-netcore" Version="3.2.0" />
    <PackageReference Include="MicroKnights.Log4NetAdoNetAppender" Version="2.2.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="SkyAPM.Agent.AspNetCore" Version="2.1.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />

  </ItemGroup>

  <ItemGroup>
    <None Include="wwwroot\HyunCore.Data.json\BlogArticle.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\Modules.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\Permission.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\Role.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\RoleModulePermission.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\SysUserInfo.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\Topic.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\TopicDetail.tsv" />
    <None Include="wwwroot\HyunCore.Data.json\UserRole.tsv" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="index.html" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="StopContainerImg.sh">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\DangerChemicals\" />
    <Folder Include="wwwroot\HyunCore.Data.excel\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hyun.Core.Extensions\Hyun.Core.Extensions.csproj" />
  </ItemGroup>

  <ProjectExtensions>
    <VisualStudio>
      <UserProperties appsettings_1json__JsonSchema="" />
    </VisualStudio>
  </ProjectExtensions>

</Project>