﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models;
using Hyun.Core.IServices;
using Hyun.Core.Model.SearchModels.Common;
using Hyun.Core.Common.Extensions;
using Hyun.Old.Util;
using NPOI.OpenXmlFormats.Dml.Chart;
namespace Hyun.Core.Api
{

    /// <summary>
    /// 年级班级管理
    /// </summary>
    [Route("api/hyun/pclassinfo")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PClassInfoController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IPClassInfoServices ipclassinfoservicesManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IPClassInfoServices classinfoservicesManager;
        private readonly IPSchoolExtensionServices schoolExtensionManager;

        public PClassInfoController(I<PERSON>apper _mapper, IWebHostEnvironment _env, IUser _user, IPClassInfoServices _ipclassinfoservicesManager, IBDictionaryServices _dictionaryManager, IPClassInfoServices _classinfoservicesManager, IPSchoolExtensionServices _schoolExtensionManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ipclassinfoservicesManager = _ipclassinfoservicesManager;
            dictionaryManager = _dictionaryManager;
            classinfoservicesManager = _classinfoservicesManager;
            schoolExtensionManager= _schoolExtensionManager;
        }


        /// <summary>
        /// 新增班级
        /// </summary>
        /// <param name="obj">PClassInfoDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("add")]
        public async Task<Result> PClassInfoAdd([FromBody] PClassInfoDto obj)
        {
            Result r = new Result();
            r = await ipclassinfoservicesManager.InsertUpdate(obj);
            return r;
        }

        /// <summary>
        /// 修改班级
        /// </summary>
        /// <param name="obj">PClassInfoDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("edit")]
        public async Task<Result> PClassInfoEdit([FromBody] PClassInfoDto obj)
        {
            Result r = new Result();
            r = await ipclassinfoservicesManager.InsertUpdate(obj);
            return r;
        }

        /// <summary>
        /// 根据Id查询班级信息
        /// </summary>
        /// <param name="id">班级Id</param>
        /// <param name="isFirst">是否第一次调用(true/false)</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbyeditid")]
        public async Task<Result> PClassInfoByEditId(long id,bool isFirst = false)
        {
            Result r = new Result();
            PClassInfo m = await ipclassinfoservicesManager.QueryById(id);
            if (m != null)
            {
                r.data.rows = mapper.Map<PClassInfoDto>(m);
            }
            if (isFirst)
            {
                //获取学段
                //List<BDictionary> listStage = await dictionaryManager.GetByTypeCode("100000");
                //List<dropdownModel> listStageDropdown = listStage.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();
                //获取年级
                string strPeriod = "";
                //获取单位学段
                PSchoolExtension schoolExtension = schoolExtensionManager.Query(f => f.UnitId == user.UnitId).Result.FirstOrDefault();
                if (schoolExtension != null)
                {
                    strPeriod = schoolExtension.Period;
                }
                List<BDictionaryDto> listGrade = await dictionaryManager.GetGradeByPeriod(strPeriod);
                List<dropdownModel> listGradeDropdown = listGrade.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                //获取班级
                List<BDictionary> listClass = await dictionaryManager.GetByTypeCode("300000");
                List<dropdownModel> listClassDropdown = listClass.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                r.data.other = new { ClassList = listClassDropdown, GradeList = listGradeDropdown };
            }
           
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }


        /// <summary>
        /// 根据班级Id删除班级信息
        /// </summary>
        /// <param name="id">班级Id</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("deletebyid")]
        public async Task<Result> PClassInfoDeleteById(long id)
        {
            Result r = new Result();
            r = await ipclassinfoservicesManager.FakeDeleteById(id, user.UnitId);
            return r;
        }

        /// <summary>
        /// 查询年级班级列表
        /// </summary>
        /// <param name="param">PClassInfoParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<PClassInfoDto>>> PClassInfoGetPaged([FromBody] PClassInfoParam param)
        {
            Result r = new Result();
            if(user.UnitTypeId == 3)
            {
                param.unitId = user.UnitId;
            }

            //升级年级
            await ipclassinfoservicesManager.GradeUpgrade(user.UnitId);
            //
            string downLoadFile = $"/Download/班级导入信息.xlsx";
            string studentDownLoadFile = $"/Download/学生导入信息.xlsx";
            var msgdata = new Result<List<PClassInfoDto>>();
            PageModel<PClassInfoDto> pg = await ipclassinfoservicesManager.GetPaged(param);
            if (param.isFirst)
            {
                //获取年级
                string strPeriod = "";
                PSchoolExtension schoolExtension = schoolExtensionManager.Query(f => f.UnitId == user.UnitId).Result.FirstOrDefault();
                if (schoolExtension != null)
                {
                    strPeriod = schoolExtension.Period;
                }
                List<BDictionaryDto> listGrade = await dictionaryManager.GetGradeByPeriod(strPeriod);
                List<dropdownModel> listGradeDropdown = listGrade.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();



                //获取班级
                List<BDictionary> listClass = await dictionaryManager.GetByTypeCode("300000");
                List<dropdownModel> listClassDropdown = listClass.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                //年级班级树形结构
                List<PClassInfo> listAddClass = await classinfoservicesManager.GetClassList();
                List<AreaViewModel> list = new List<AreaViewModel>();
                var listParent = listAddClass.GroupBy(f=>new {f.GradeId, f.GradeName }).Select(f => new AreaView { Id = f.Key.GradeId, Name = f.Key.GradeName }).ToList();
                listParent.ForEach(f =>
                {
                    list.Add(new AreaViewModel
                    {
                        Id = f.Id,
                        Name = f.Name,
                        Children = listAddClass.Where(f1 => f1.GradeName == f.Name).Select(f1 => new AreaViewModel { Id = f1.Id, Name = f1.ClassName, Children = [] }).ToList()
                    });
                });


                msgdata = baseSucc(pg.data, pg.dataCount,
                    $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                    new { ClassList = listClassDropdown, GradeList = listGradeDropdown, GradeClassList = list, ClassFilePath = downLoadFile,StudentFilePath = studentDownLoadFile });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount,
                    $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", new { filePath = downLoadFile });
            }
            return msgdata;
        }

        /// <summary>
        /// 导入班级数据信息
        /// </summary>
        /// <param name="fileList"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("import")]
        public async Task<Result> PClassInfoImport(IFormCollection fileList)
        {
            Result r = await FileHelper.UploadFile(env.ContentRootPath, (int)UploadFileType.Import, fileList.Files);
            if (r.flag == 1)
            {
                List<PClassInfoDto> list = new ExcelHelper<PClassInfoDto>().ImportFromExcel(env.ContentRootPath, r.data.rows.ToString());
                r = await ipclassinfoservicesManager.ImportClassInfo(list);
            }
            return r;
        }

        /// <summary>
        /// 年级升级
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("gradeupgrade")]
        public async Task<Result> GradeUpgrade()
        {
            Result r = await ipclassinfoservicesManager.GradeUpgrade(user.UnitId);
            return r;
        }

    }
}
