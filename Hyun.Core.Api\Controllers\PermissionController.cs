﻿using Castle.Core.Smtp;
using Hyun.Core.Model;
using Hyun.Core.Model.OverWrite;
using Hyun.Core.Common;
using Hyun.Core.Common.Helper;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.IServices;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Repository.UnitOfWorks;
using Hyun.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using NPOI.POIFS.Properties;
using Autofac.Features.Metadata;
using System.Collections.Generic;

namespace Hyun.Core.Controllers
{
    /// <summary>
    /// 菜单管理
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PermissionController : BaseApiController
    {
        readonly IUnitOfWorkManage _unitOfWorkManage;
        readonly IPermissionServices _permissionServices;
        readonly IModuleServices _moduleServices;
        readonly ISysModulePermissionServices _modulePermissionServices;
        readonly IRoleModulePermissionServices _roleModulePermissionServices;
        readonly ISysUserRoleServices _userRoleServices;
        private readonly IHttpClientFactory _httpClientFactory;
        readonly IHttpContextAccessor _httpContext;
        readonly IUser _user;
        private readonly IMapper _mapper;
        private readonly PermissionRequirement _requirement;
        private readonly IWfModuleServices _wfModuleManager;
        private readonly IWfProcessNodeServices _wfProcessNodeManager;
        private readonly IWfProjectAuditUserServices _wfProjectAuditUserManager;
        private readonly IWfPageDefinitionServices _wfPageDefinitionManager;
        private readonly IWfProcessStatuzServices _wfProcessStatuzManager;


        public PermissionController(IPermissionServices permissionServices, IModuleServices moduleServices, ISysModulePermissionServices modulePermissionServices,
            IRoleModulePermissionServices roleModulePermissionServices, ISysUserRoleServices userRoleServices,
            IUnitOfWorkManage unitOfWorkManage,
            IHttpClientFactory httpClientFactory,
            IHttpContextAccessor httpContext, IUser user, IMapper mapper,
            IWfModuleServices wfModuleManager, IWfProcessNodeServices wfProcessNodeManager,
            IWfProjectAuditUserServices wfProjectAuditUserManager,
            IWfPageDefinitionServices wfPageDefinitionManager,
            PermissionRequirement requirement,
            IWfProcessStatuzServices wfProcessStatuzManager
            )
        {
            _permissionServices = permissionServices;
            _unitOfWorkManage = unitOfWorkManage;
            _moduleServices = moduleServices;
            _modulePermissionServices = modulePermissionServices;
            _roleModulePermissionServices = roleModulePermissionServices;
            _userRoleServices = userRoleServices;
            _httpClientFactory = httpClientFactory;
            _httpContext = httpContext;
            _user = user;
            _mapper = mapper;
            _requirement = requirement;
            _wfModuleManager = wfModuleManager;
            _wfProcessNodeManager = wfProcessNodeManager;
            _wfProjectAuditUserManager = wfProjectAuditUserManager;
            _wfPageDefinitionManager = wfPageDefinitionManager;
            _wfProcessStatuzManager = wfProcessStatuzManager;
        }

        /// <summary>
        /// 获取菜单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="key"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        // GET: api/User
        [HttpGet]
        public async Task<Result<PageModel<SysPermission>>> Get(int page = 1, string key = "", int pageSize = 50)
        {
            //Result<PageModel<SysPermission>> permissions = new Result<PageModel<SysPermission>>();
            if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key))
            {
                key = "";
            }

            var permissions = await _permissionServices.QueryPage(a => a.IsDeleted != true && (a.Name != null && a.Name.Contains(key)), page, pageSize, " Id desc ");


            #region 单独处理

            var apis = await _moduleServices.Query(d => d.IsDeleted == false);
            var permissionsView = permissions.data;

            var permissionAll = await _permissionServices.Query(d => d.IsDeleted != true);
            foreach (var item in permissionsView)
            {
                List<long> pidarr = new()
                {
                    item.Pid
                };
                if (item.Pid > 0)
                {
                    pidarr.Add(0);
                }
                var parent = permissionAll.FirstOrDefault(d => d.Id == item.Pid);

                while (parent != null)
                {
                    pidarr.Add(parent.Id);
                    parent = permissionAll.FirstOrDefault(d => d.Id == parent.Pid);
                }


                item.PidArr = pidarr.OrderBy(d => d).Distinct().ToList();
                foreach (var pid in item.PidArr)
                {
                    var per = permissionAll.FirstOrDefault(d => d.Id == pid);
                    item.PnameArr.Add((per != null ? per.Name : "根节点") + "/");
                    //var par = Permissions.Where(d => d.Pid == item.Id ).ToList();
                    //item.PCodeArr.Add((per != null ? $"/{per.Code}/{item.Code}" : ""));
                    //if (par.Count == 0 && item.Pid == 0)
                    //{
                    //    item.PCodeArr.Add($"/{item.Code}");
                    //}
                }

                item.MName = apis.FirstOrDefault(d => d.Id == item.Mid)?.LinkUrl;
            } 
            permissions.data = permissionsView;
            #endregion

            return baseSucc(permissions.ConvertTo<SysPermission>(_mapper), permissions.dataCount);
        }

        /// <summary>
        /// 查询树形 Table
        /// </summary>
        /// <param name="f">父节点</param>
        /// <param name="key">关键字</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<Result<PageModel<SysPermission>>> GetTreeTable(long f = 0, string key = "")
        {
            var apiList = await _moduleServices.Query(d => d.IsDeleted == false);
            var permissionsList = await _permissionServices.Query(d => d.IsDeleted == false);
            var apiPermissionsList = await _modulePermissionServices.Query(d => d.IsDeleted == false);
            if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key))
            {
                key = "";
            }
            IEnumerable<SysPermission> permissions = null;
            if (key != "")
            {
                permissions = permissionsList.Where(a => a.Name.Contains(key)).OrderBy(a => a.OrderSort).ToList();
            }
            else
            {
                permissions = permissionsList.Where(a => a.Pid == f).OrderBy(a => a.OrderSort).ToList();
            }

            foreach (var item in permissions)
            {
                List<long> pidarr = new() { };
                var parent = permissionsList.FirstOrDefault(d => d.Id == item.Pid);

                while (parent != null)
                {
                    pidarr.Add(parent.Id);
                    parent = permissionsList.FirstOrDefault(d => d.Id == parent.Pid);
                }

                //item.PidArr = pidarr.OrderBy(d => d).Distinct().ToList();

                pidarr.Reverse();
                pidarr.Insert(0, 0);
                item.PidArr = pidarr;

                item.MName = apiList.FirstOrDefault(d => d.Id == item.Mid)?.LinkUrl;
                item.hasChildren = permissionsList.Where(d => d.Pid == item.Id).Any();
                if (!item.hasChildren && apiPermissionsList != null && apiPermissionsList.Where(m => m.PermissionId == item.Id).Count() > 0)
                {
                    item.hasChildren = true;
                }
            }
            //把api、按钮赋值。
            if (apiList!=null && apiList.Count>0)
            {
                //api、按钮转换成页面实体数据

                var permissionsListTemp = apiList.Join(apiPermissionsList, p1 => p1.Id, p2 => p2.ModuleId, 
                    (p1, p2) => new {p1,p2 })
                    .Where(result => result.p2.PermissionId == f)
                    .Select(result => new SysPermission()
                    {
                        Id = result.p1.Id, 
                        Pid = result.p2.PermissionId,
                        Name = result.p1.Name,
                        MName = result.p1.LinkUrl,
                        IsButton = true,
                        IsHide = true,
                        hasChildren = false
                    });
                if (permissionsListTemp!=null && permissionsListTemp.Count()> 0)
                {
                    permissions = permissions.Union(permissionsListTemp);
                }
            }

            PageModel<SysPermission> data = new PageModel<SysPermission>();
            data.data = permissions.ToList();
            data.dataCount = permissions.Count();
            return baseSucc(data.ConvertTo<SysPermission>(_mapper), data.dataCount);
        }

        /// <summary>
        /// 添加一个菜单
        /// </summary>
        /// <param name="permission"></param>
        /// <returns></returns>
        // POST: api/User
        [HttpPost]
        public async Task<Result<string>> Post([FromBody] SysPermission permission)
        {
            permission.CreateId = _user.ID;
            permission.CreateBy = _user.Name;

            var id = (await _permissionServices.Add(permission));
            return id > 0 ? baseSucc<string>(id.ToString(), 1, "添加") : baseFailed<string>("添加");
        }

        /// <summary>
        /// 保存菜单权限分配
        /// </summary>
        /// <param name="assignView"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<Result> Assign([FromBody] AssignView assignView)
        {
            if (assignView.rid > 0)
            {
                //开启事务
                try
                {
                    var old_rmps = await _roleModulePermissionServices.Query(d => d.RoleId == assignView.rid);

                    //查询接口,添加接口到菜单中去
                    //var moduleList = await _moduleServices.Query(d => d.IsDeleted == false);
                    var modulePermissionsList = await _modulePermissionServices.Query(d => d.IsDeleted == false);

                    _unitOfWorkManage.BeginTran();
                    await _permissionServices.Db.Deleteable<SysRoleModulePermission>(t => t.RoleId == assignView.rid).ExecuteCommandAsync();
                    var permissions = await _permissionServices.Query(d => d.IsDeleted == false);

                    List<SysRoleModulePermission> new_rmps = new List<SysRoleModulePermission>();
                    var nowTime = _permissionServices.Db.GetDate();
                    foreach (var item in assignView.pids)
                    {
                        var permissionsid = permissions.Find(p => p.Id == item)?.Id;
                        
                        //查询当前页面下的Api权限。
                        long? moduleid = 0;
                        if (permissionsid == null || permissionsid <= 0)
                        {
                            var modulePermission = modulePermissionsList.Find(m => m.Id == item);
                            if (modulePermission == null)
                            {
                                continue;
                            }
                            permissionsid = modulePermission.PermissionId;
                            moduleid = modulePermission.ModuleId;
                        }

                        var find_old_rmps = old_rmps.Find(p => p.PermissionId == item);

                        SysRoleModulePermission roleModulePermission = new SysRoleModulePermission()
                        {
                            IsDeleted = false,
                            RoleId = assignView.rid,
                            ModuleId = moduleid ?? 0,
                            PermissionId = permissionsid ?? 0,
                            CreateId = find_old_rmps == null ? _user.ID : find_old_rmps.CreateId,
                            CreateBy = find_old_rmps == null ? _user.Name : find_old_rmps.CreateBy,
                            CreateTime = find_old_rmps == null ? nowTime : find_old_rmps.CreateTime,
                            ModifyId = _user.ID,
                            ModifyBy = _user.Name,
                            ModifyTime = nowTime

                        };
                        new_rmps.Add(roleModulePermission);
                    }
                    if (new_rmps.Count > 0) await _roleModulePermissionServices.Add(new_rmps);
                    _unitOfWorkManage.CommitTran();
                }
                catch (Exception)
                {
                    _unitOfWorkManage.RollbackTran();
                    throw;
                }
                _requirement.Permissions.Clear();
                return baseSucc("保存成功");
            }
            else
            {
                return baseFailed("保存失败");
            }
        }


        /// <summary>
        /// 获取菜单树
        /// </summary>
        /// <param name="pid"></param>
        /// <param name="needbtn"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<Result<PermissionTree>> GetPermissionTree(long pid = 0, bool needbtn = false)
        {
            var permissions = await _permissionServices.Query(d => d.IsDeleted == false);
            var permissionTrees = (from child in permissions
                                   where child.IsDeleted == false
                                   orderby child.Id
                                   select new PermissionTree
                                   {
                                       value = child.Id,
                                       label = child.Name,
                                       Pid = child.Pid,
                                       isbtn = child.IsButton,
                                       order = child.OrderSort,
                                   }).ToList();
            PermissionTree rootRoot = new PermissionTree
            {
                value = 0,
                Pid = 0,
                label = "根节点"
            };

            //查询接口,添加接口到菜单中去
            var moduleList = await _moduleServices.Query(d => d.IsDeleted == false);
            var modulePermissionsList = await _modulePermissionServices.Query(d => d.IsDeleted == false);
            foreach (var item in modulePermissionsList)
            {
                var modulelistTemp = moduleList.Where(m => m.Id == item.ModuleId);
                if (modulelistTemp.Count() > 0)
                {
                    permissionTrees.AddRange((from module in modulelistTemp
                                          select new PermissionTree()
                                          {
                                              value = item.Id,
                                              label = module.Name,
                                              Pid = item.PermissionId,
                                              isbtn = true,
                                              order = module.OrderSort
                                          }).ToList());
                }
            }

            permissionTrees = permissionTrees.OrderBy(d => d.order).ToList();


            RecursionHelper.LoopToAppendChildren(permissionTrees, rootRoot, pid, needbtn);

            return baseSucc(rootRoot, 1, "获取成功");
        }

        /// <summary>
        /// 获取路由树
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="appType">菜单类型（1：PC端 2：移动）</param>
        /// <param name="platformType">平台类型（危化品、校服、低值易耗品）暂未支持，先默认0</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<MessageModel<NavigationBar>> GetNavigationBar(long uid, int appType = 1, List<int> platformType = null)
        {
            if (platformType == null)
            {
                platformType = new List<int> { 0 };
            }
            var data = new MessageModel<NavigationBar>();

            long uidInHttpcontext1 = 0;
            var roleIds = new List<long>();
            // ids4和jwt切换
            if (Permissions.IsUseIds4)
            {
                // ids4
                uidInHttpcontext1 = (from item in _httpContext.HttpContext.User.Claims
                                     where item.Type == ClaimTypes.NameIdentifier
                                     select item.Value).FirstOrDefault().ObjToLong();
                if (!(uidInHttpcontext1 > 0))
                {
                    uidInHttpcontext1 = (from item in _httpContext.HttpContext.User.Claims
                                         where item.Type == "sub"
                                         select item.Value).FirstOrDefault().ObjToLong();
                }
                roleIds = (from item in _httpContext.HttpContext.User.Claims
                           where item.Type == ClaimTypes.Role
                           select item.Value.ObjToLong()).ToList();
                if (!roleIds.Any())
                {
                    roleIds = (from item in _httpContext.HttpContext.User.Claims
                               where item.Type == "role"
                               select item.Value.ObjToLong()).ToList(); 
                }
            }
            else
            {
                // jwt
                uidInHttpcontext1 = ((JwtHelper.SerializeJwt(_httpContext.HttpContext.Request.Headers["Authorization"].ObjToString().Replace("Bearer ", "")))?.Uid).ObjToLong();
                roleIds = (await _userRoleServices.Query(d => d.IsDeleted == false && d.UserId == uid)).Select(d => d.RoleId.ObjToLong()).Distinct().ToList();
            }


            if (uid > 0 && uid == uidInHttpcontext1)
            {
                if (roleIds.Any())
                {
                    var pids = (await _roleModulePermissionServices.Query(d => d.IsDeleted == false && roleIds.Contains(d.RoleId))).Select(d => d.PermissionId.ObjToLong()).Distinct();
                    if (pids.Any())
                    {
                        //注释以下一行代码先不启用平台类型，后期升级开发时，可以启用
                        //var rolePermissionMoudles = (await _permissionServices.Query(d => d.AppType == appType && platformType.Contains(d.PlatformType ?? 0) && pids.Contains(d.Id))).OrderBy(c => c.OrderSort);
                        var rolePermissionMoudles = (await _permissionServices.Query(d => d.AppType == appType && pids.Contains(d.Id))).OrderBy(c => c.OrderSort);
                        //var temp = rolePermissionMoudles.ToList().Find(t => t.Id == 87);
                        var permissionTrees = (from child in rolePermissionMoudles
                                               where child.IsDeleted == false
                                               orderby child.Id
                                               select new NavigationBar
                                               {
                                                   id = child.Id,
                                                   name = child.Name,
                                                   pid = child.Pid,
                                                   order = child.OrderSort,
                                                   path = child.Code,
                                                   iconCls = child.Icon,
                                                   Func = child.Func,
                                                   IsHide = child.IsHide.ObjToBool(),
                                                   IsButton = child.IsButton.ObjToBool(),
                                                   meta = new NavigationBarMeta
                                                   {
                                                       requireAuth = true,
                                                       title = child.Name,
                                                       NoTabPage = child.IsHide.ObjToBool(),
                                                       keepAlive = child.IskeepAlive.ObjToBool()
                                                   }
                                               }).ToList();


                        NavigationBar rootRoot = new NavigationBar()
                        {
                            id = 0,
                            pid = 0,
                            order = 0,
                            name = "根节点",
                            path = "",
                            iconCls = "",
                            meta = new NavigationBarMeta(),

                        };

                        #region 获取审核审批配置信息 zyf 20250227
                        
                        List<NavigationBar> listNativeBar = new List<NavigationBar>();

                        //根据当前登录的用户获取该用户下对应授权使用的模块流程节点信息
                        List<ModuleMenuModel> listCurrentMenu = await _wfProjectAuditUserManager.GetModuleMenuList();

                        if (listCurrentMenu.Count > 0)
                        {
                            var obj = permissionTrees.Where(f => f.id == 815152216284336128).FirstOrDefault();
                            if(obj != null)
                            {
                                long moduleBeginId = 800000000000000000;
                                int moduleSort = obj.order;

                                //根据配置的“工作流填报”菜单获取该菜单下所有子菜单信息
                                List<NavigationBar> listNeedNavigation = StringHelper.GetItemsAndSubItemsById(permissionTrees, 815152216284336128);
                                //需要移除掉此块数据

                                //读取所有流程所用到的节点信息
                                List<WfProcessStatuz> listStatuz = await _wfProcessStatuzManager.Query(f => f.IsDeleted == false);

                                //获取模块集合
                                var listModule = listCurrentMenu.GroupBy(f => new { f.ModuleId, f.ModuleName, f.Logo }).Select(f => new { f.Key.ModuleId, f.Key.ModuleName, f.Key.Logo }).ToList();
                                //获取模块流程集合
                                var listModuleProcess = listCurrentMenu.Where(f=>!string.IsNullOrEmpty(f.ProcessName)).GroupBy(f => new { f.ModuleId, f.ModuleName, f.ProcessId, f.ProcessName }).Select(f => new { f.Key.ModuleId, f.Key.ModuleName, f.Key.ProcessId, f.Key.ProcessName }).ToList();

                                //获取模块流程节点集合
                                var listModuleProcessNode = listCurrentMenu.Where(f=>!string.IsNullOrEmpty(f.ProcessName)).GroupBy(f => new { f.ModuleId, f.ModuleName, f.ProcessId, f.ProcessName,f.ProcessNodeId,f.ProcessNodeName,f.IsBegin,f.TreatHandle,f.TreatHandleUrl,f.NodeType,f.StopHandle,f.StopHandleUrl }).Select(f => new { f.Key.ModuleId, f.Key.ModuleName, f.Key.ProcessId, f.Key.ProcessName,f.Key.ProcessNodeId,f.Key.ProcessNodeName, f.Key.IsBegin, f.Key.TreatHandle, f.Key.TreatHandleUrl, f.Key.NodeType, f.Key.StopHandle, f.Key.StopHandleUrl }).ToList();

                                //遍历模块
                                foreach (var module in listModule)
                                {
                                    string logo = "cdshenheshenpi";
                                    if (!string.IsNullOrEmpty(module.Logo))
                                    {
                                        logo = module.Logo;
                                    }
                                    NavigationBar nativeModule = new NavigationBar();
                                    nativeModule.id = module.ModuleId;
                                    nativeModule.pid = 0;
                                    nativeModule.order = moduleSort;
                                    nativeModule.name = module.ModuleName;
                                    nativeModule.path = "@url";
                                    nativeModule.iconCls = logo;
                                    nativeModule.meta = new NavigationBarMeta()
                                    {
                                        title = module.ModuleName,
                                    };
                                    nativeModule.IsHide = false;
                                    listNativeBar.Add(nativeModule);

                                    var listCurrentProcess = listModuleProcess.Where(f => f.ModuleId == module.ModuleId).ToList();
                                    //如果该模块下存在多个流程,增加流程菜单
                                    if(listCurrentProcess.Count > 1)
                                    {
                                        foreach (var process in listCurrentProcess)
                                        {
                                            moduleBeginId += 5;
                                            moduleSort += 5;
                                            NavigationBar nativeProcss = new NavigationBar();
                                            nativeProcss.id = moduleBeginId;
                                            nativeProcss.pid = nativeModule.id;
                                            nativeProcss.order = moduleSort;
                                            nativeProcss.name = process.ProcessName;
                                            nativeProcss.path = "@url";
                                            nativeProcss.iconCls = "";
                                            nativeProcss.meta = new NavigationBarMeta()
                                            {
                                                title = process.ProcessName,
                                            };
                                            nativeModule.IsHide = false;
                                            listNativeBar.Add(nativeProcss);

                                            //查询该流程下的节点信息
                                            List<long> listNodeIds = new List<long>();
                                            var currentListNodeLink = listStatuz.Where(f => f.ProcessId == process.ProcessId).ToList();
                                            listNodeIds.Add(20000);
                                            listNodeIds.AddRange(currentListNodeLink.Select(f => f.ProcessNodeId.Value));


                                            var listNode = listModuleProcessNode.Where(f => f.ProcessId == process.ProcessId && listNodeIds.Contains(f.ProcessNodeId) && f.ProcessNodeId != 30000).Distinct().ToList();
                                            foreach (var node in listNode)
                                            {
                                                //先添加节点菜单
                                                moduleBeginId += 5;
                                                moduleSort += 5;
                                                string nodeName = node.ProcessNodeName;
                                                if (node.ProcessNodeId.Equals(20000))
                                                {
                                                    nodeName = "项目库";
                                                }
                                                NavigationBar nodeModule = new NavigationBar();
                                                nodeModule.id = moduleBeginId;
                                                nodeModule.pid = nativeProcss.id;
                                                nodeModule.order = moduleSort;
                                                nodeModule.name = nodeName;
                                                nodeModule.path = "@url";
                                                nodeModule.iconCls = "";
                                                nodeModule.meta = new NavigationBarMeta()
                                                {
                                                    title = nodeName,
                                                };
                                                nodeModule.IsHide = false;
                                                listNativeBar.Add(nodeModule);

                                                //项目库
                                                if (node.ProcessNodeId.Equals(20000))
                                                {
                                                    //可以根据查询该节点信息然后带出子集信息循环插入,如果根据上面查询出来的数据去取根据什么字段来查询,目前写死
                                                    //添加项目库，（此处可以从上面读取的集合中取，也可以写死，因为“项目库”页面是定死的）
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nodeModule.id,
                                                        order = moduleSort,
                                                        name = "项目库",
                                                        path = $"approvalconfiguration/pages/statistics/list@moduleId={node.ModuleId}&processId={node.ProcessId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = "项目库",
                                                        },
                                                        IsHide = false
                                                    });

                                                    //添加项目库查看
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nodeModule.id,
                                                        order = moduleSort,
                                                        name = "项目库查看",
                                                        path = $"approvalconfiguration/pages/statistics/detail@moduleId={node.ModuleId}&processId={node.ProcessId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = "项目库查看",
                                                        },
                                                        IsHide = true
                                                    });
                                                }
                                                else
                                                {

                                                    if(node.IsBegin == 1)
                                                    {
                                                        //添加待处理数据
                                                        moduleBeginId += 2;
                                                        moduleSort += 2;
                                                        listNativeBar.Add(new NavigationBar()
                                                        {
                                                            id = moduleBeginId,
                                                            pid = nodeModule.id,
                                                            order = moduleSort,
                                                            name = node.TreatHandle,
                                                            path = $"{node.TreatHandleUrl}@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                            iconCls = "",
                                                            meta = new NavigationBarMeta
                                                            {
                                                                title = node.TreatHandle,
                                                            },
                                                            IsHide = false
                                                        });
                                                    }
                                                    else
                                                    {
                                                        //添加待处理数据
                                                        moduleBeginId += 2;
                                                        moduleSort += 2;
                                                        listNativeBar.Add(new NavigationBar()
                                                        {
                                                            id = moduleBeginId,
                                                            pid = nodeModule.id,
                                                            order = moduleSort,
                                                            name = node.TreatHandle,
                                                            path = $"{node.TreatHandleUrl}@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                            iconCls = "",
                                                            meta = new NavigationBarMeta
                                                            {
                                                                title = node.TreatHandle,
                                                            },
                                                            IsHide = false
                                                        });

                                                        //添加已处理
                                                        moduleBeginId += 2;
                                                        moduleSort += 2;
                                                        listNativeBar.Add(new NavigationBar()
                                                        {
                                                            id = moduleBeginId,
                                                            pid = nodeModule.id,
                                                            order = moduleSort,
                                                            name = node.StopHandle,
                                                            path = $"{node.StopHandleUrl}@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                            iconCls = "",
                                                            meta = new NavigationBarMeta
                                                            {
                                                                title = node.StopHandle,
                                                            },
                                                            IsHide = false
                                                        });
                                                    }

                                                    

                                                    //添加查看，（此处可以从上面读取的集合中取，也可以写死，因为查看页面是定死的）
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nodeModule.id,
                                                        order = moduleSort,
                                                        name = "待处理已处理查看",
                                                        path = $"approvalconfiguration/pages/node/detail@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = "待处理已处理查看",
                                                        },
                                                        IsHide = true
                                                    });

                                                    //如果类型为填报类型，则添加“填报修改”
                                                    if (node.NodeType.Equals(1))
                                                    {
                                                        //添加填报修改，（此处可以从上面读取的集合中取，也可以写死，因为“填报修改”页面是定死的）
                                                        moduleBeginId += 2;
                                                        moduleSort += 2;
                                                        listNativeBar.Add(new NavigationBar()
                                                        {
                                                            id = moduleBeginId,
                                                            pid = nodeModule.id,
                                                            order = moduleSort,
                                                            name = "填报修改",
                                                            path = $"approvalconfiguration/pages/node/formedit@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                            iconCls = "",
                                                            meta = new NavigationBarMeta
                                                            {
                                                                title = "填报修改",
                                                            },
                                                            IsHide = true
                                                        });
                                                    }
                                                    //如果类型为审核类型，则添加“审核”
                                                    else if (node.NodeType.Equals(2))
                                                    {
                                                        //添加审核，（此处可以从上面读取的集合中取，也可以写死，因为“审核”页面是定死的）
                                                        moduleBeginId += 2;
                                                        moduleSort += 2;
                                                        listNativeBar.Add(new NavigationBar()
                                                        {
                                                            id = moduleBeginId,
                                                            pid = nodeModule.id,
                                                            order = moduleSort,
                                                            name = "审核",
                                                            path = $"approvalconfiguration/pages/node/examine@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                            iconCls = "",
                                                            meta = new NavigationBarMeta
                                                            {
                                                                title = "审核",
                                                            },
                                                            IsHide = true
                                                        });
                                                    }
                                                }
                                            }
                                            moduleBeginId += 50;
                                            moduleSort += 50;
                                        }
                                    }
                                    else if(listCurrentProcess.Count == 1)
                                    {
                                        //查询该流程下的节点信息
                                        List<long> listNodeIds = new List<long>();
                                        var currentListNodeLink = listStatuz.Where(f => f.ProcessId == listCurrentProcess[0].ProcessId).ToList();
                                        listNodeIds.Add(20000);
                                        listNodeIds.AddRange(currentListNodeLink.Select(f => f.ProcessNodeId.Value).ToList());
       

                                        var listNode = listModuleProcessNode.Where(f => f.ModuleId == module.ModuleId && listNodeIds.Contains(f.ProcessNodeId) && f.ProcessNodeId != 30000).Distinct().ToList();
                                        listNode = listNode.OrderBy(x => x.ProcessNodeId == 20000).ThenBy(x => x.ProcessNodeId).ToList();

                                        foreach (var node in listNode)
                                        {
                                            //先添加节点菜单
                                            string nodeName = node.ProcessNodeName;
                                            if (node.ProcessNodeId.Equals(20000))
                                            {
                                                nodeName = "项目库";
                                            }
                                            moduleBeginId += 5;
                                            moduleSort += 5;
                                            NavigationBar nativeNode = new NavigationBar();
                                            nativeNode.id = moduleBeginId;
                                            nativeNode.pid = nativeModule.id;
                                            nativeNode.order = moduleSort;
                                            nativeNode.name = nodeName;
                                            nativeNode.path = "@url";
                                            nativeNode.iconCls = "";
                                            nativeNode.meta = new NavigationBarMeta()
                                            {
                                                title = nodeName,
                                            };
                                            nativeNode.IsHide = false;
                                            listNativeBar.Add(nativeNode);

                                            //项目库
                                            if (node.ProcessNodeId.Equals(20000))
                                            {
                                                //可以根据查询该节点信息然后带出子集信息循环插入,如果根据上面查询出来的数据去取根据什么字段来查询,目前写死
                                                //添加项目库，（此处可以从上面读取的集合中取，也可以写死，因为“项目库”页面是定死的）
                                                moduleBeginId += 2;
                                                moduleSort += 2;
                                                listNativeBar.Add(new NavigationBar()
                                                {
                                                    id = moduleBeginId,
                                                    pid = nativeNode.id,
                                                    order = moduleSort,
                                                    name = "项目库",
                                                    path = $"approvalconfiguration/pages/statistics/list@moduleId={node.ModuleId}&processId={node.ProcessId}",
                                                    iconCls = "",
                                                    meta = new NavigationBarMeta
                                                    {
                                                        title = "项目库",
                                                    },
                                                    IsHide = false
                                                });

                                                //添加项目库查看
                                                moduleBeginId += 2;
                                                moduleSort += 2;
                                                listNativeBar.Add(new NavigationBar()
                                                {
                                                    id = moduleBeginId,
                                                    pid = nativeNode.id,
                                                    order = moduleSort,
                                                    name = "项目库查看",
                                                    path = $"approvalconfiguration/pages/statistics/detail@moduleId={node.ModuleId}&processId={node.ProcessId}",
                                                    iconCls = "",
                                                    meta = new NavigationBarMeta
                                                    {
                                                        title = "项目库查看",
                                                    },
                                                    IsHide = true
                                                });
                                            }
                                            //数据报表
                                            else
                                            {

                                                if(node.IsBegin == 1)
                                                {
                                                    //添加待处理数据
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nativeNode.id,
                                                        order = moduleSort,
                                                        name = node.TreatHandle,
                                                        path = $"{node.TreatHandleUrl}@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = node.TreatHandle,
                                                        },
                                                        IsHide = false
                                                    });
                                                }
                                                else
                                                {
                                                    //添加待处理数据
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nativeNode.id,
                                                        order = moduleSort,
                                                        name = node.TreatHandle,
                                                        path = $"{node.TreatHandleUrl}@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = node.TreatHandle,
                                                        },
                                                        IsHide = false
                                                    });

                                                    //添加已处理
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nativeNode.id,
                                                        order = moduleSort,
                                                        name = node.StopHandle,
                                                        path = $"{node.StopHandleUrl}@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = node.StopHandle,
                                                        },
                                                        IsHide = false
                                                    });
                                                }

                                                //添加查看，（此处可以从上面读取的集合中取，也可以写死，因为查看页面是定死的）
                                                moduleBeginId += 2;
                                                moduleSort += 2;
                                                listNativeBar.Add(new NavigationBar()
                                                {
                                                    id = moduleBeginId,
                                                    pid = nativeNode.id,
                                                    order = moduleSort,
                                                    name = "待处理已处理查看",
                                                    path = $"approvalconfiguration/pages/node/detail@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                    iconCls = "",
                                                    meta = new NavigationBarMeta
                                                    {
                                                        title = "待处理已处理查看",
                                                    },
                                                    IsHide = true
                                                });

                                                //如果类型为填报类型，则添加“填报修改”
                                                if (node.NodeType.Equals(1))
                                                {
                                                    //添加填报修改，（此处可以从上面读取的集合中取，也可以写死，因为“填报修改”页面是定死的）
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nativeNode.id,
                                                        order = moduleSort,
                                                        name = "填报修改",
                                                        path = $"approvalconfiguration/pages/node/formedit@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = "填报修改",
                                                        },
                                                        IsHide = true
                                                    });
                                                }
                                                //如果类型为审核类型，则添加“审核”
                                                else if(node.NodeType.Equals(2))
                                                {
                                                    //添加审核，（此处可以从上面读取的集合中取，也可以写死，因为“审核”页面是定死的）
                                                    moduleBeginId += 2;
                                                    moduleSort += 2;
                                                    listNativeBar.Add(new NavigationBar()
                                                    {
                                                        id = moduleBeginId,
                                                        pid = nativeNode.id,
                                                        order = moduleSort,
                                                        name = "审核",
                                                        path = $"approvalconfiguration/pages/node/examine@moduleId={node.ModuleId}&processId={node.ProcessId}&processNode={node.ProcessNodeId}",
                                                        iconCls = "",
                                                        meta = new NavigationBarMeta
                                                        {
                                                            title = "审核",
                                                        },
                                                        IsHide = true
                                                    });
                                                }
                                            }

                                        }
                                    }


                                    #region 处理查询统计
                                    NavigationBar objSearch = listNeedNavigation.Where(f => f.path.Contains("pages/treasury/list")).FirstOrDefault();
                                    if(objSearch != null)
                                    {
                                        //判断是否有查询统计权限
                                        var listSearch = await _wfProjectAuditUserManager.Query(f => f.AuditUserId == _user.ID && f.IsDeleted == false && f.BusinessType == 3 && f.ModuleId == module.ModuleId);
                                        if (listSearch.Count > 0)
                                        {
                                            moduleBeginId += 10;

                                            NavigationBar nativeNode = new NavigationBar();
                                            nativeNode.id = moduleBeginId;
                                            nativeNode.pid = nativeModule.id;
                                            nativeNode.order = 10000000;
                                            nativeNode.name = "查询统计";
                                            nativeNode.path = $"approvalconfiguration/pages/treasury/list@moduleId={module.ModuleId}";
                                            nativeNode.iconCls = "";
                                            nativeNode.meta = new NavigationBarMeta()
                                            {
                                                title = "查询统计",
                                            };
                                            nativeNode.IsHide = false;
                                            listNativeBar.Add(nativeNode);


                                            //生成每个列表页面
                                            List<WfPageDefinition> listPage = await _wfPageDefinitionManager.Query(f => f.Statuz == 1 && f.ModuleId == module.ModuleId);
                                            foreach (WfPageDefinition page in listPage)
                                            {
                                                moduleBeginId += 20;
                                                moduleSort += 2;

                                                listNativeBar.Add(new NavigationBar()
                                                {
                                                    id = moduleBeginId,
                                                    pid = nativeModule.id,
                                                    order = moduleSort,
                                                    name = "查询统计列表页面",
                                                    path = $"approvalconfiguration/pages/treasury/listitem@id={page.Id}",
                                                    iconCls = "",
                                                    meta = new NavigationBarMeta
                                                    {
                                                        title = "查询统计列表页面",
                                                    },
                                                    IsHide = true
                                                });
                                            }
                                            //

                                            //增加查询统计查看
                                            moduleBeginId += 20;
                                            moduleSort += 2;
                                            listNativeBar.Add(new NavigationBar()
                                            {
                                                id = moduleBeginId,
                                                pid = nativeModule.id,
                                                order = moduleSort,
                                                name = "查询统计查看",
                                                path = $"approvalconfiguration/pages/treasury/detail@moduleId={module.ModuleId}",
                                                iconCls = "",
                                                meta = new NavigationBarMeta
                                                {
                                                    title = "查询统计查看",
                                                },
                                                IsHide = true
                                            });


                                            moduleBeginId += 20;
                                            moduleSort += 2;
                                        }
                                       
                                    }
                                    #endregion

                                    #region 添加预算清单权限
                                    NavigationBar objProjectList = listNeedNavigation.Where(f => f.path.Contains("pages/node/projectlist")).FirstOrDefault();
                                    if (objProjectList != null)
                                    {
                                        moduleBeginId += 2;
                                        moduleSort += 2;

                                        NavigationBar nativeNode = new NavigationBar();
                                        nativeNode.id = moduleBeginId;
                                        nativeNode.pid = nativeModule.id;
                                        nativeNode.order = 0;
                                        nativeNode.name = "项目清单";
                                        nativeNode.path = $"approvalconfiguration/pages/node/projectlist@moduleId={module.ModuleId}";
                                        nativeNode.iconCls = "";
                                        nativeNode.meta = new NavigationBarMeta()
                                        {
                                            title = "项目清单",
                                        };
                                        nativeNode.IsHide = true;
                                        listNativeBar.Add(nativeNode);
                                    }
                                    moduleBeginId += 2;
                                    moduleSort += 2;
                                    #endregion

                                    #region 项目清单审核
                                    NavigationBar objProjectListAudit = listNeedNavigation.Where(f => f.path.Contains("pages/node/projectexaminelist")).FirstOrDefault();
                                    if (objProjectListAudit != null)
                                    {
                                        moduleBeginId += 2;
                                        moduleSort += 2;

                                        NavigationBar nativeNode = new NavigationBar();
                                        nativeNode.id = moduleBeginId;
                                        nativeNode.pid = nativeModule.id;
                                        nativeNode.order = 0;
                                        nativeNode.name = "项目清单审核";
                                        nativeNode.path = $"approvalconfiguration/pages/node/projectexaminelist@moduleId={module.ModuleId}";
                                        nativeNode.iconCls = "";
                                        nativeNode.meta = new NavigationBarMeta()
                                        {
                                            title = "项目清单审核",
                                        };
                                        nativeNode.IsHide = true;
                                        listNativeBar.Add(nativeNode);
                                    }
                                    moduleBeginId += 2;
                                    moduleSort += 2;
                                    #endregion

                                    #region 项目清单查看
                                    NavigationBar objProjectListView = listNeedNavigation.Where(f => f.path.Contains("pages/node/projectdetail")).FirstOrDefault();
                                    if (objProjectListView != null)
                                    {

                                        moduleBeginId += 2;
                                        moduleSort += 2;

                                        NavigationBar nativeNode = new NavigationBar();
                                        nativeNode.id = moduleBeginId;
                                        nativeNode.pid = nativeModule.id;
                                        nativeNode.order = 0;
                                        nativeNode.name = "项目清单查看";
                                        nativeNode.path = $"approvalconfiguration/pages/node/projectdetail@moduleId={module.ModuleId}";
                                        nativeNode.iconCls = "";
                                        nativeNode.meta = new NavigationBarMeta()
                                        {
                                            title = "项目清单查看",
                                        };
                                        nativeNode.IsHide = true;
                                        listNativeBar.Add(nativeNode);
                                    }
                                    moduleBeginId += 2;
                                    moduleSort += 2;
                                    #endregion

                                    moduleBeginId += 100;
                                    moduleSort += 100;
                                }

                                

                                //删除当前模块
                                List<long> listDelIds = listNeedNavigation.Select(f => f.id).ToList();
                                permissionTrees.RemoveAll(f => listDelIds.Contains(f.id));
                            }

                            
                        }
                        #endregion

                        if (listNativeBar.Count > 0)
                        {
                            permissionTrees.AddRange(listNativeBar);
                        }

                        permissionTrees = permissionTrees.OrderBy(d => d.order).ToList();

                        //判断是否要删除“单位配置”这一菜单
                        bool isExists = await _wfProjectAuditUserManager.IsExistGroupProcessSet();
                        if (!isExists)
                        {
                            permissionTrees.RemoveAll(f => f.name == "单位配置");
                        }
                        //判断是否要删除“单位权限控制”这一菜单
                        bool isExistsPermission = await _wfProjectAuditUserManager.IsExistPermissionSet();
                        if (!isExistsPermission)
                        {
                            permissionTrees.RemoveAll(f => f.name == "单位权限控制");
                        }


                        RecursionHelper.LoopNaviBarAppendChildren(permissionTrees, rootRoot);

                        data.success = true;
                        if (data.success)
                        {
                            data.response = rootRoot;
                            data.msg = "获取成功";
                        }
                    }
                }
            }
            return data;
        }

        /// <summary>
        /// 获取路由树
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<NavigationBarPro>>> GetNavigationBarPro(long uid)
        {
            var data = new MessageModel<List<NavigationBarPro>>();

            long uidInHttpcontext1 = 0;
            var roleIds = new List<long>();
            // ids4和jwt切换
            if (Permissions.IsUseIds4)
            {
                // ids4
                uidInHttpcontext1 = (from item in _httpContext.HttpContext.User.Claims
                                     where item.Type == ClaimTypes.NameIdentifier
                                     select item.Value).FirstOrDefault().ObjToLong();
                if (!(uidInHttpcontext1 > 0))
                {
                    uidInHttpcontext1 = (from item in _httpContext.HttpContext.User.Claims
                                         where item.Type == "sub"
                                         select item.Value).FirstOrDefault().ObjToLong();
                }
                roleIds = (from item in _httpContext.HttpContext.User.Claims
                           where item.Type == ClaimTypes.Role
                           select item.Value.ObjToLong()).ToList();
                if (!roleIds.Any())
                {
                    roleIds = (from item in _httpContext.HttpContext.User.Claims
                               where item.Type == "role"
                               select item.Value.ObjToLong()).ToList();
                }
            }
            else
            {
                // jwt
                uidInHttpcontext1 = ((JwtHelper.SerializeJwt(_httpContext.HttpContext.Request.Headers["Authorization"].ObjToString().Replace("Bearer ", "")))?.Uid).ObjToLong();
                roleIds = (await _userRoleServices.Query(d => d.IsDeleted == false && d.UserId == uid)).Select(d => d.RoleId.ObjToLong()).Distinct().ToList();
            }

            if (uid > 0 && uid == uidInHttpcontext1)
            {
                if (roleIds.Any())
                {
                    var pids = (await _roleModulePermissionServices.Query(d => d.IsDeleted == false && roleIds.Contains(d.RoleId)))
                                    .Select(d => d.PermissionId.ObjToLong()).Distinct();
                    if (pids.Any())
                    {
                        var rolePermissionMoudles = (await _permissionServices.Query(d => pids.Contains(d.Id) && d.IsButton == false)).OrderBy(c => c.OrderSort);
                        var permissionTrees = (from item in rolePermissionMoudles
                                               where item.IsDeleted == false
                                               orderby item.Id
                                               select new NavigationBarPro
                                               {
                                                   id = item.Id,
                                                   name = item.Name,
                                                   parentId = item.Pid,
                                                   order = item.OrderSort,
                                                   path = item.Code == "-" ? item.Name.GetTotalPingYin().FirstOrDefault() : (item.Code == "/" ? "/dashboard/workplace" : item.Code),
                                                   component = item.Pid == 0 ? (item.Code == "/" ? "dashboard/Workplace" : "RouteView") : item.Code?.TrimStart('/'),
                                                   iconCls = item.Icon,
                                                   Func = item.Func,
                                                   IsHide = item.IsHide.ObjToBool(),
                                                   IsButton = item.IsButton.ObjToBool(),
                                                   meta = new NavigationBarMetaPro
                                                   {
                                                       show = true,
                                                       title = item.Name,
                                                       icon = "user"//item.Icon
                                                   }
                                               }).ToList();

                        permissionTrees = permissionTrees.OrderBy(d => d.order).ToList();

                        data.success = true;
                        if (data.success)
                        {
                            data.response = permissionTrees;
                            data.msg = "获取成功";
                        }
                    }
                }
            }
            return data;
        }

        /// <summary>
        /// 通过角色获取菜单
        /// </summary>
        /// <param name="rid"></param>
        /// <returns></returns>
        [HttpGet]      
        public async Task<MessageModel<AssignShow>> GetPermissionIdByRoleId(long rid = 0)
        {
            //var data = new MessageModel<AssignShow>();

            var rmps = await _roleModulePermissionServices.Query(d => d.IsDeleted == false && d.RoleId == rid);
            var permissionTrees = (from child in rmps
                                   orderby child.Id
                                   select child.PermissionId.ObjToLong()).ToList();

            var permissions = await _permissionServices.Query(d => d.IsDeleted == false);
            List<string> assignbtns = new List<string>();

            //查询接口,添加接口权限。
            var modulePermissionsList = await _modulePermissionServices.Query(d => d.IsDeleted == false);
            if (modulePermissionsList != null && modulePermissionsList.Count > 0)
            {
                foreach (var item in rmps)
                {
                    var modulePermissionId = modulePermissionsList.FirstOrDefault(d => d.PermissionId == item.PermissionId && d.ModuleId == item.ModuleId)?.Id;
                    if (modulePermissionId != null && modulePermissionId > 0)
                    {
                        assignbtns.Add(modulePermissionId.ObjToString());
                    }
                }
            }
            foreach (var item in permissionTrees)
            {
                var pername = permissions.FirstOrDefault(d => d.IsButton && d.Id == item)?.Name;
                if (!string.IsNullOrEmpty(pername))
                {
                    //assignbtns.Add(pername + "_" + item);
                    assignbtns.Add(item.ObjToString());
                }
            }

            //data.success = true;
            //if (data.success)
            //{
            //    data.response = new AssignShow()
            //    {
            //        permissionids = permissionTrees,
            //        assignbtns = assignbtns,
            //    };
            //    data.msg = "获取成功";
            //}

            return Success(new AssignShow()
            {
                permissionids = permissionTrees,
                assignbtns = assignbtns,
            }, "获取成功");

            //return data;
        }

        /// <summary>
        /// 更新菜单
        /// </summary>
        /// <param name="permission"></param>
        /// <returns></returns>
        // PUT: api/User/5
        [HttpPut]
        public async Task<Result<string>> Put([FromBody] SysPermission permission)
        {
            var data = new Result<string>();
            if (permission != null && permission.Id > 0)
            {
                if(await _permissionServices.Update(permission))
                {
                    await _roleModulePermissionServices.UpdateModuleId(permission.Id, permission.Mid);
                    data.flag = 1;
                    data.msg = "更新成功";
                    data.data.rows= permission?.Id.ObjToString();
                }
            }
            return data;
        }

        /// <summary>
        /// 删除菜单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        // DELETE: api/ApiWithActions/5
        [HttpDelete]
        public async Task<Result<string>> Delete(long id)
        {
            var data = new Result<string>();
            if (id > 0)
            {
                var userDetail = await _permissionServices.QueryById(id);
                userDetail.IsDeleted = true;
                if (await _permissionServices.Update(userDetail))
                {
                    data.flag = 1;
                    data.msg = "删除成功";
                    data.data.rows = userDetail?.Id.ObjToString();
                }
            }
            return data;
        }

        /// <summary>
        /// 导入多条菜单信息
        /// </summary>
        /// <param name="permissions"></param>
        /// <returns></returns>
        // POST: api/User
        [HttpPost]
        public async Task<MessageModel<string>> BatchPost([FromBody] List<SysPermission> permissions)
        {
            var data = new MessageModel<string>();
            string ids = string.Empty;
            int sucCount = 0;

            for (int i = 0; i < permissions.Count; i++)
            {
                var permission = permissions[i];
                if (permission != null)
                {
                    permission.CreateId = _user.ID;
                    permission.CreateBy = _user.Name;
                    ids += (await _permissionServices.Add(permission));
                    sucCount++;
                }
            }

            data.success = ids.IsNotEmptyOrNull();
            if (data.success)
            {
                data.response = ids;
                data.msg = $"{sucCount}条数据添加成功";
            }

            return data;
        }

        #region 菜单Api管理。

        /// <summary>
        /// 获取页面添加接口api列表（排查已添加的）
        /// </summary>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="key"></param>
        /// <param name="permisionid"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<Result<PageModel<SysModules>>> GetModule(int page = 1, int pageSize = 50, string key = "", long permisionid = 0)
        {
            if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key))
            {
                key = "";
            }
            var data = await _modulePermissionServices.GetModules(page, pageSize, key, permisionid);
            return baseSucc<PageModel<SysModules>>(data, data.dataCount, "获取成功");
        }

        /// <summary>
        /// 添加菜单Api
        /// </summary>
        /// <param name="permissionid"></param>
        /// <param name="moduleid"></param>
        /// <returns></returns>
        // POST: api/User
        [HttpPost]
        public async Task<Result<string>> PostApi(long permissionid,long moduleid)
        {
            Result<string> r = new Result<string>();
            long newId = 0;
            var entity = new SysModulePermission() { PermissionId = permissionid, ModuleId = moduleid, IsDeleted = false };
            var modulePermissions = await _modulePermissionServices.Query(a => a.IsDeleted == false && a.PermissionId == permissionid && a.ModuleId == moduleid);
            if (modulePermissions != null && modulePermissions.Count > 0)
            {
                r.flag = 0;
                r.msg = "添加失败，该页面已添加当前接口。";
            }
            else
            {
                entity.CreateId = _user.ID;
                entity.CreateBy = _user.Name;
                newId = await _modulePermissionServices.Add(entity);
                if (newId > 0)
                {
                    r.data.rows = newId.ToString();
                    r.flag = 1;
                    r.msg = "添加成功。";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "添加失败。";
                }
            }
            return newId > 0 ? baseSucc<string>(newId.ToString(), 1, "添加") : baseFailed<string>("添加");
        }

        /// <summary>
        /// 删除菜单Api
        /// </summary>
        /// <param name="permissionid"></param>
        /// <param name="moduleid"></param>
        /// <returns></returns>
        // DELETE: api/ApiWithActions/5
        [HttpDelete]
        public async Task<Result> DeleteApi(long permissionid, long moduleid)
        {
            Result r = new Result();
            var modulePermissions = await _modulePermissionServices.Query(a => a.IsDeleted == false && a.PermissionId == permissionid && a.ModuleId == moduleid);
            if (modulePermissions != null && modulePermissions.Count > 0)
            {
                var entity = modulePermissions.FirstOrDefault();
                entity.IsDeleted = true;
                if (await _modulePermissionServices.Update(entity))
                {
                    r.flag = 1;
                    r.msg = "删除成功。";
                }
                else
                {
                    r.flag = 1;
                    r.msg = "执行失败，删除失败。";
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "删除失败，该页面已不存在当前接口。";
            }
            return r.flag > 0 ? baseSucc("删除", 1) : baseFailed("删除");
        }
        #endregion

        /// <summary>
        /// 系统接口菜单同步接口
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<SysPermission>>> MigratePermission(string action = "", string token = "", string gatewayPrefix = "", string swaggerDomain = "", string controllerName = "", long pid = 0, bool isAction = false)
        {
            var data = new MessageModel<List<SysPermission>>();
            if (controllerName.IsNullOrEmpty())
            {
                data.msg = "必须填写要迁移的所属接口的控制器名称";
                return data;
            }

            controllerName = controllerName.TrimEnd('/').ToLower();

            gatewayPrefix = gatewayPrefix.Trim();
            swaggerDomain = swaggerDomain.Trim();
            controllerName = controllerName.Trim();

            using var client = _httpClientFactory.CreateClient();
            var Configuration = swaggerDomain.IsNotEmptyOrNull() ? swaggerDomain : AppSettings.GetValue("SystemCfg:Domain");
            var url = $"{Configuration}/swagger/V2/swagger.json";
            if (Configuration.IsNullOrEmpty())
            {
                data.msg = "Swagger.json在线文件域名不能为空";
                return data;
            }
            if (token.IsNullOrEmpty()) token = Request.Headers.Authorization;
            token = token.Trim();
            client.DefaultRequestHeaders.Add("Authorization", $"{token}");

            var response = await client.GetAsync(url);
            var body = await response.Content.ReadAsStringAsync();

            var resultJObj = (JObject)JsonConvert.DeserializeObject(body);
            var paths = resultJObj["paths"].ObjToString();
            var pathsJObj = (JObject)JsonConvert.DeserializeObject(paths);

            List<SysPermission> permissions = new List<SysPermission>();
            foreach (JProperty jProperty in pathsJObj.Properties())
            {
                var apiPath = gatewayPrefix + jProperty.Name.ToLower();
                if (action.IsNotEmptyOrNull())
                {
                    action = action.Trim();
                    if (!apiPath.Contains(action.ToLower()))
                    {
                        continue;
                    }
                }
                string httpmethod = "";
                if (jProperty.Value.ToString().ToLower().Contains("get"))
                {
                    httpmethod = "get";
                }
                else if (jProperty.Value.ToString().ToLower().Contains("post"))
                {
                    httpmethod = "post";
                }
                else if (jProperty.Value.ToString().ToLower().Contains("put"))
                {
                    httpmethod = "put";
                }
                else if (jProperty.Value.ToString().ToLower().Contains("delete"))
                {
                    httpmethod = "delete";
                }

                var summary = jProperty.Value?.SelectToken($"{httpmethod}.summary")?.ObjToString() ?? "";

                var subIx = summary.IndexOf("(Auth");
                if (subIx >= 0)
                {
                    summary = summary.Substring(0, subIx);
                }

                permissions.Add(new SysPermission()
                {
                    Code = " ",
                    Name = summary,
                    IsButton = true,
                    IsHide = false,
                    Enabled = true,
                    CreateTime = DateTime.Now,
                    IsDeleted = false,
                    Pid = pid,
                    Module = new SysModules()
                    {
                        LinkUrl = apiPath ?? "",
                        Name = summary,
                        Enabled = true,
                        CreateTime = DateTime.Now,
                        ModifyTime = DateTime.Now,
                        IsDeleted = false,
                    }
                });
            }

            var modulesList = (await _moduleServices.Query(d => d.IsDeleted == false && d.LinkUrl != null)).Select(d => d.LinkUrl.ToLower()).ToList();
            permissions = permissions.Where(d => !modulesList.Contains(d.Module.LinkUrl.ToLower()) && d.Module.LinkUrl.Contains($"/{controllerName}/")).ToList();


            if (isAction)
            {
                foreach (var item in permissions)
                {
                    List<SysModules> modules = await _moduleServices.Query(d => d.LinkUrl != null && d.LinkUrl.ToLower() == item.Module.LinkUrl);
                    if (!modules.Any())
                    {
                        var mid = await _moduleServices.Add(item.Module);
                        if (mid > 0)
                        {
                            item.Mid = mid;
                            var permissionid = await _permissionServices.Add(item);
                        }

                    }
                }
                data.msg = "同步完成";
            }

            data.response = permissions;
            data.status = 200;
            data.success = isAction;

            return data;
        }
    }

    public class AssignView
    {
        public List<long> pids { get; set; }
        public long rid { get; set; }
    }
    public class AssignShow
    {
        public List<long> permissionids { get; set; }
        public List<string> assignbtns { get; set; }
    }

}
