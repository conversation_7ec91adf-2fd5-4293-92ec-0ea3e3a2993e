﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///项目库数据写入节点配置表
    ///</summary>
    [SugarTable("wf_FundProcessNodeSet","项目库数据写入节点配置表")]
    public class WfFundProcessNodeSet : BaseEntity
    {

          public WfFundProcessNodeSet()
          {

          }

           /// <summary>
           ///流程Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ProcessId { get; set; }

           /// <summary>
           ///流程节点Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ProcessNodeId { get; set; }

           /// <summary>
           ///状态值（对应字典表b_Dictionary中的DicValue值）1：通过，2：不通过...
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string Statuz { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

