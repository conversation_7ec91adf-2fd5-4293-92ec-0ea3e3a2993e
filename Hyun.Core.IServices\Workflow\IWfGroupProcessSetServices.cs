﻿using Hyun.Core.Model.Models;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfGroupSet接口方法
    ///</summary>
    public interface IWfGroupProcessSetServices : IBaseServices<WfGroupProcessSet>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        Task<Result<string>> InsertUpdate(WfGroupProcessSetModel o);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result<string>> DeleteById(long id);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<WfGroupProcessSetDto>> GetPaged(WfGroupProcessSetParam param);


        Task<PageModel<WfGroupUnitSetDto>> GetUnitGroupSetList(WfGroupUnitSetParam param);


        Task<Result<string>> BatchSetGroupUnit(UnitGroupBatchModel o);

        Task<PageModel<DicAuditUserModel>> GetItemUserList(WfGroupUnitSetParam param);

        Task<Result<string>> SetGoupUserList(UserListModel userModel);

        Task<List<SetUserListModel>> GetSetUserList(long processId, long processNodeId);
    }

}

