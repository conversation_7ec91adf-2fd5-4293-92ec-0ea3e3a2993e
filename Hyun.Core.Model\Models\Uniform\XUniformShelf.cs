

namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///校服供应商上架表
    ///</summary>
    [SugarTable("x_UniformShelf", "供应商上架表")]
    public class XUniformShelf : BaseEntity
    {

        public XUniformShelf()
        {

        }

        /// <summary>
        ///校服清单表Id
        /// </summary>
        public long UniformCompanyId { get; set; }

        /// <summary>
        ///单位id
        /// </summary>
        public long UnitId { get; set; }
        /// <summary>
        ///适用单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///种类
        /// </summary>
        [SugarColumn(Length = 51, IsNullable = true)]
        public string Uniformtype { get; set; }

        /// <summary>
        ///品名
        /// </summary>
        [SugarColumn(Length = 51, IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        ///品牌
        /// </summary>
        [SugarColumn(Length = 51, IsNullable = true)]
        public string Brand { get; set; }

        /// <summary>
        ///具体参数
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Parameter { get; set; }

        /// <summary>
        ///适合性别(0:未知  1：男  2：女 3：男/女)
        /// </summary>
        public int Sex { get; set; }

        /// <summary>
        ///单价（元）
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Price { get; set; }

        /// <summary>
        ///标配数量
        /// </summary>
        public int StandardNum { get; set; }
        /// <summary>
        ///采购要求
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string PurchaseDemand { get; set; }
        /// <summary>
        ///单位
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string UnitName { get; set; }

        /// <summary>
        ///生产厂商
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string Producer { get; set; }

        /// <summary>
        ///产地
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string OriginAddress { get; set; }

        /// <summary>
        ///安全等级
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string SecurityLevel { get; set; }

        /// <summary>
        ///尺码表地址
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string SizePath { get; set; }
        /// <summary>
        ///主图地址
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string MainImagePath { get; set; }
        /// <summary>
        ///使用状态(1：启用  2：禁用)
        /// </summary>
        public int UseStatuz { get; set; }

        /// <summary>
        ///审核状态(10：待审核  11：审核不通过  20：审核通过)
        /// </summary>
        public int AuditStatuz { get; set; }

        /// <summary>
        ///审核时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? AuditTime { get; set; }

        /// <summary>
        ///审核说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string AuditExplain { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? Sort { get; set; }

        /// <summary>
        ///二维码地址
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string QrCodeUrl { get; set; }

        /// <summary>
        ///采购表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? UniformPurchaseId { get; set; }
        /// <summary>
        /// 是否展示1：展示  2：否、不展示
        /// </summary>
        public int IsShow { get; set; } = 1;
        /// <summary>
        /// 订购数量
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int OrderedNum { get; set; }
        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
    }
}

