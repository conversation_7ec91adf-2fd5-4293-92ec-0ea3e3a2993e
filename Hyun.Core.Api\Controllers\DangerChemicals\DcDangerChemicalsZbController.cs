﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Model;
using Hyun.Core.Services;
using Hyun.Old.Util;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using System.Reflection.Metadata;

namespace Hyun.Core.Api
{

    [Route("api/hyun/dcdangerchemicalszb")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class DcDangerChemicalsZbController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IDcSchoolMaterialCodeServices dcSchoolMaterialCodeManager;
        private readonly IDcSchoolMaterialServices dcSchoolMaterialManager;
        private readonly IPUnitServices unitManager;
        private readonly IDcApplyServices dcApplyManager;
        private readonly IDcApplyConfirmServices dcApplyConfirmManager;
        private readonly IDcApplyConfirmDetailServices dcApplyConfirmDetailManager;
        private readonly ISysUserExtensionServices userManager;
        private readonly IDcSchoolCatalogServices dcSchoolCatalogManager;
        private readonly IDcSchoolMaterialModelServices dcSchoolMaterialModelManager;
        private readonly IDcSchoolMaterialBrandServices dcSchoolMaterialBrandManager;
        private readonly IDcBaseCatalogServices dcBaseCatalogManager;
        private readonly IDcBaseModelExtensionServices dcBaseModelExtensionManager;
        //private readonly IApiAppSecretServices apiAppSecretManager;
        private readonly IDcMaterialsNumAuditServices dcMaterialsNumAuditManager;
        public DcDangerChemicalsZbController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IDcSchoolMaterialCodeServices _dcSchoolMaterialCodeManager, IDcSchoolMaterialServices _dcSchoolMaterialManager, IPUnitServices _unitManager, IDcApplyServices _dcApplyManager, IDcApplyConfirmServices _dcApplyConfirmManager, IDcApplyConfirmDetailServices _dcApplyConfirmDetailManager, ISysUserExtensionServices _userManager, IDcSchoolCatalogServices _dcSchoolCatalogManager, IDcSchoolMaterialModelServices _dcSchoolMaterialModelManager, IDcSchoolMaterialBrandServices _dcSchoolMaterialBrandManager, IDcBaseCatalogServices _dcBaseCatalogManager, IDcBaseModelExtensionServices _dcBaseModelExtensionManager,/* IApiAppSecretServices _apiAppSecret,*/ IDcMaterialsNumAuditServices dcMaterialsNumAudit)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            dcSchoolMaterialCodeManager = _dcSchoolMaterialCodeManager;
            dcSchoolMaterialManager = _dcSchoolMaterialManager;
            unitManager = _unitManager;
            dcApplyManager = _dcApplyManager;
            dcApplyConfirmManager = _dcApplyConfirmManager;
            dcApplyConfirmDetailManager = _dcApplyConfirmDetailManager;
            userManager = _userManager;
            dcSchoolCatalogManager = _dcSchoolCatalogManager;
            dcSchoolMaterialModelManager = _dcSchoolMaterialModelManager;
            dcSchoolMaterialBrandManager = _dcSchoolMaterialBrandManager;
            dcBaseCatalogManager = _dcBaseCatalogManager;
            dcBaseModelExtensionManager = _dcBaseModelExtensionManager;
            //apiAppSecretManager = _apiAppSecret;
            dcMaterialsNumAuditManager = dcMaterialsNumAudit;
        }


        [HttpPost]
        [Route("zbgetunitinfo")]
        //<used>0</used>
        public async Task<Result> ZbGetUnitInfo(string thirdUnitId)
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                DangerChemicalsZbModel<ZbDataUnitInfoList> res = GetZbDanweibeianInfo(thirdUnitId);
                r.flag = res.code;
                r.msg = res.info;
                r.data.rows = res.data.data;
                return r;
            });
            return result;
            
        }

        /// <summary>
        /// 获取单位备案信息接口
        /// </summary>
        /// <param name="thirdUnitId"></param>
        /// <returns></returns>
        private DangerChemicalsZbModel<ZbDataUnitInfoList> GetZbDanweibeianInfo(string thirdUnitId)
        {
            string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/getDanweibeianInfo";

            var postData = new
            {
                COMP_ID = new List<string> { thirdUnitId },
                ipage = 0,
                pagesize = 0
            };
            string postStr = string.Format("data={0}&key=duijie", GetSm2(postData));
            return ComLib.JSON2Object<DangerChemicalsZbModel<ZbDataUnitInfoList>>(ComLib.HttpPost(url, postStr, GetHeader()));
        }

        [HttpPost]
        [Route("zbgetstorageplacelist")]
        //<used>0</used>
        public async Task<Result> ZbGetStoragePlaceList(string thirdUnitId)
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/getKufangbeianInfo";

                var postData = new
                {
                    COMP_ID = new List<string> { thirdUnitId },
                    ipage = 0,
                    pagesize = 0
                };
                string postStr = string.Format("data={0}&key=duijie", GetSm2(postData));
                DangerChemicalsZbModel<ZbDataStoragePlaceList> res = ComLib.JSON2Object<DangerChemicalsZbModel<ZbDataStoragePlaceList>>(ComLib.HttpPost(url, postStr, GetHeader()));
                r.flag = res.code;
                r.msg = res.info;
                if (res.code == 1 && res.data.data.Count > 0)
                {
                    res.data.data = res.data.data.Where(p => p.FLOW_STATE == 1 && p.ZX_STATE == 0 && p.VERSION > 0).ToList();
                    r.data.rows = res.data.data;
                }
                return r;
            });
            return result;
            
           
        }


        [HttpPost]
        [Route("zbgetuserlist")]
        //<used>1</used>
        public async Task<Result> ZbGetUserList(string thirdUnitId)
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                DangerChemicalsZbModel<ZbDataUserList> res = GetZbRenyuanbeianInfo(thirdUnitId);
                r.flag = res.code;
                r.msg = res.info;
                if (res.code == 1 && res.data.data.Count > 0)
                {
                    var userList = res.data.data.Where(p => p.FLOW_STATE == 1 && p.ZX_STATE == 0 && p.VERSION > 0).ToList();
                    if (userList.Count > 0) res.data.data = userList;
                    r.data.rows = res.data.data;

                    //获取当前系统登录用户id
                    var currentList = res.data.data.Where(f => user.UserName.Equals(f.RY_NAME)).ToList();
                    if (currentList.Count > 0)
                    {
                        r.data.footer = currentList.FirstOrDefault();
                    }
                    else
                    {
                        var bgList = res.data.data.Where(t => t.IS_BG == 1).ToList();
                        if (bgList.Count > 0)
                        {
                            r.data.footer = bgList.FirstOrDefault();
                        }
                        else
                        {
                            r.data.footer = res.data.data.FirstOrDefault();
                        }
                    }
                }
                return r;
            });
            return result;
            
        }
        /// <summary>
        /// 获取人员备案信息接口
        /// </summary>
        /// <param name="thirdUnitId"></param>
        /// <returns></returns>
        private DangerChemicalsZbModel<ZbDataUserList> GetZbRenyuanbeianInfo(string thirdUnitId)
        {
            string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/getRenyuanbeianInfo";

            var postData = new
            {
                COMP_ID = new List<string> { thirdUnitId },
                ipage = 0,
                pagesize = 0
            };
            string postStr = string.Format("data={0}&key=duijie", GetSm2(postData));
            var beianInfo = ComLib.JSON2Object<DangerChemicalsZbModel<ZbDataUserList>>(ComLib.HttpPost(url, postStr, GetHeader()));
            if (beianInfo.code == 1 && beianInfo.data.data != null && beianInfo.data.data.Count > 0)
            {
                return beianInfo;
            }
            else
            {
                url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/getRenyuan";
                return ComLib.JSON2Object<DangerChemicalsZbModel<ZbDataUserList>>(ComLib.HttpPost(url, postStr, GetHeader()));
            }
        }

        [HttpPost]
        [Route("zbgetsaleunitlist")]
        //<used>1</used>
        public async Task<Result> ZbGetSaleUnitList(string name)
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                DangerChemicalsZbModel<ZbDataUnitInfoList> res = GetZbSaleUnitList(name);
                r.flag = res.code;
                r.msg = res.info;
                if (res.code == 1 && res.data.data.Count > 0)
                {
                    res.data.data = res.data.data.Where(p => p.FLOW_STATE == 1 && p.VERSION > 0).ToList();
                }
                r.data.rows = res.data.data;
                return r;
            });
            return result;
           
        }


        //[HttpPost]
        //[Route("zbgetuseunitlist")]
        ////<used>0</used>
        //public async Task<Result> ZbGetUseUnitList(string unitName)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        [HttpPost]
        [Route("zbgetmaterialinfobycode")]
        //<used>0</used>
        public async Task<Result> ZbGetMaterialInfoByCode(string code, string thirdUnitId)
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                if (!string.IsNullOrWhiteSpace(code) && !string.IsNullOrWhiteSpace(thirdUnitId))
                {
                    string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/selectBs";

                    var postData = new
                    {
                        BS_NO = new List<string> { code },
                        COMP_ID = thirdUnitId
                    };
                    string postStr = string.Format("data={0}&key=duijie", GetSm2(postData));
                    DangerChemicalsZbModel<ZbMaterialList> res = ComLib.JSON2Object<DangerChemicalsZbModel<ZbMaterialList>>(ComLib.HttpPost(url, postStr, GetHeader()));
                    if (res.code == 1 && res.data.data.Count > 0)
                    {
                        r.flag = "ok".Equals(res.data.data[0].error) ? 1 : 0;
                        r.msg = r.flag == 1 ? "查询成功。" : res.data.data[0].error;
                        r.data.rows = res.data.data[0];
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "与第三方接口通信失败";
                    }
                }
                return r;
            });
            return result;

            
        }


        [HttpPost]
        [Route("zbinwarehouse")]
        //<used>1</used>
        public async Task<Result> ZbInWarehouse([FromBody] ZbInWarehouseDto model)
        {
            Result r = new Result();
            string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/insertGmrk";
            var o = model.o;
            var schoolMaterialIds = model.schoolMaterialIds;
            o.CRK_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm");

            var param = new DcSchoolMaterialCodeParam();
            param.unitId = user.UnitId;
            param.SchoolMaterialIds = schoolMaterialIds;
            var schoolMaterialCodeList =await dcSchoolMaterialCodeManager.Find(param);

            List<ZbInWarehourseMaterial> crkwpList = new List<ZbInWarehourseMaterial>();
            string[] materialIdArray = schoolMaterialIds.Split(',');
            foreach (var materialIdStr in materialIdArray)
            {
                int schoolMaterialId = 0;
                int.TryParse(materialIdStr, out schoolMaterialId);
                var codeList = schoolMaterialCodeList.Where(t => t.SchoolMaterialId == schoolMaterialId).ToList();
                if (codeList.Count > 0)
                {
                    crkwpList.Add(new ZbInWarehourseMaterial
                    {
                        WP_ID = codeList.FirstOrDefault().ThirdMaterialId,
                        WP_JLDW = codeList.FirstOrDefault().ThirdUnitName,
                        WP_NAME = codeList.FirstOrDefault().ThirdMaterialName,
                        WP_SL = codeList.Sum(f => f.ThirdNum).ToString(),
                        BS_NOS = codeList.Select(f => f.ThirdCode).ToList()
                    });
                }
                else
                {
                    r.flag = 0;
                    r.msg = "存在未绑定标识码的危化品，请先绑定后再上报。";
                    return r;
                }
            }
            o.CRK_WP = crkwpList;
            string postStr = "";

            //#region 2024.04.08 增加判断上报接口 直报1 转报2（直报：通过中爆平台接口上报，转报：通过中心接口向中爆平台上报）
            //if (ApplicationConfig.DangerChemicalsApiCallType == "2")
            //{
            //    //转报
            //    string appid = "";
            //    string appSecret = "";
            //    var paramAppSecret = new ApiAppSecretParam();
            //    paramAppSecret.TypeCode = "W100001";
            //    var appsecretlist =await apiAppSecretManager.Find(paramAppSecret);
            //    if (appsecretlist != null && appsecretlist.Count > 0)
            //    {
            //        appid = appsecretlist.FirstOrDefault().AppId;
            //        appSecret = appsecretlist.FirstOrDefault().AppSecret;
            //    }
            //    string signDate = DateTime.Now.ToString("yyyyMMdd");
            //   // string signStr = SecurityHelper.Base64Encode(SecurityHelper.MD5(appid + appSecret + signDate)); //加密签名

            //   //  postStr = string.Format("data={0}&key=duijie&sign={1}", GetSm2(o), signStr);
            //}
            //else //直报
            //{
            //    postStr = string.Format("data={0}&key=duijie", GetSm2(o));
            //}
            //#endregion

            DangerChemicalsZbModel<ZbInResultData> res =ComLib.JSON2Object<DangerChemicalsZbModel<ZbInResultData>>(ComLib.HttpPost(url, postStr, GetHeader()));
            if (res.code == 1)
            {
                foreach (var materialIdStr in materialIdArray)
                {
                    int schoolMaterialId = 0;
                    int.TryParse(materialIdStr, out schoolMaterialId);
                    await dcSchoolMaterialManager.SplitMaterialByThirdCode(schoolMaterialId, user.UnitId);
                }
                r.flag = 1;
                r.msg = "执行成功。";
            }
            else
            {
                r.flag = 0;
                r.msg = res.info;
            }
            return r;
        }


        //[HttpPost]
        //[Route("zbgrantandback")]
        ////<used>0</used>
        //public async Task<Result> ZbGrantAndBack([FromQuery] List<DcThirdMaterialIdGrant> list, int optType,string thirdUnitId ,ZbDataStoragePlace storagePlace, ZbDataUser user)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        [HttpPost]
        [Route("zbgetmutualrecordmaterials")]
        //<used>0</used>
        public async Task<Result> ZbGetMutualRecordMaterials([FromQuery] List<DcSchoolMaterialTemp> list, string thirdSchoolId, string thirdCompanyId)
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                List<ZbUnitMaterial> schoolMaterialList = new List<ZbUnitMaterial>();
                List<ZbUnitMaterial> companyMaterialList = new List<ZbUnitMaterial>();

                DangerChemicalsZbModel<ZbDataUnitInfoList> schoolR = GetZbDanweibeianInfo(thirdSchoolId);
                if (schoolR.code == 1 && schoolR.data != null && schoolR.data.data.Count > 0)
                {
                    schoolMaterialList = schoolR.data.data[0].SH_DANWEIBEIAN_WP;
                }
                DangerChemicalsZbModel<ZbDataUnitInfoList> companyR = GetZbDanweibeianInfo(thirdCompanyId);
                if (companyR.code == 1 && companyR.data != null && companyR.data.data.Count > 0)
                {
                    companyMaterialList = companyR.data.data[0].SH_DANWEIBEIAN_WP;
                }

                List<ZbUnitMaterial> mutualList = new List<ZbUnitMaterial>();
                if (schoolMaterialList != null && companyMaterialList != null
                    && schoolMaterialList.Count > 0 && companyMaterialList.Count > 0)
                {
                    foreach (var schoolMaterial in schoolMaterialList)
                    {
                        if (companyMaterialList.Exists(t => t.WP_ID.Equals(schoolMaterial.WP_ID)))
                        {
                            mutualList.Add(schoolMaterial);
                        }
                    }
                }


                List<DcSchoolMaterialTemp> data = new List<DcSchoolMaterialTemp>();
                foreach (var item in list)
                {
                    if (mutualList.Exists(t => t.WP_NAME.Equals(item.Name)))
                    {
                        data.Add(item);
                    }
                }
                r.flag = 1;
                r.msg = "查询成功。";
                r.data.rows = data;
                return r;
            });
            return result;

        }


        //[HttpPost]
        //[Route("zbschoolautobind")]
        ////<used>0</used>
        //public async Task<Result> ZbSchoolAutoBind()
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        //[HttpPost]
        //[Route("zbschoolmanualbind")]
        ////<used>0</used>
        //public async Task<Result> ZbSchoolManualBind(int unitId, string thirdUnitId)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        [HttpPost]
        [Route("dcschoolmaterialbindcode")]
        //<used>1</used>
        public async Task<Result> DcSchoolMaterial_BindCode([FromBody] DcSchoolMaterialBindCode o)
        {
            Result r = new Result();
            var entity = await dcSchoolMaterialManager.GetById(o.SchoolMaterialId);
            if (entity == null || entity.SchoolId != user.UnitId)
            {
                r.flag = 0;
                r.msg = "数据不存在或您无权限操作！";
                return r;
            }
            if (entity.Statuz != 2 && entity.Statuz != 3)
            {
                r.flag = 0;
                r.msg = "当前状态不可操作。";
                return r;
            }
            if (o.CodeList == null || o.CodeList.Count == 0)
            {
                r.flag = 0;
                r.msg = "请填入标识码。";
                return r;
            }
            var count = o.CodeList.GroupBy(f => f.ThirdCode).Count();
            if (o.CodeList.Count != count)
            {
                r.flag = 0;
                r.msg = "您录入的编码中存在重复的编码，请修改。";
                return r;
            }
            if (o.CodeList.GroupBy(f => f.ThirdMaterialId).Count() > 1)
            {
                r.flag = 0;
                r.msg = "填入的标识码必须同属于一种物品。";
                return r;
            }
            if (o.CodeList.Sum(f => f.Num) != entity.Num)
            {
                r.flag = 0;
                r.msg = "标识码对应数量总和，必须与入库物品数量一致。";
                return r;
            }
            var paramCode = new DcSchoolMaterialCodeParam();
            paramCode.SchoolMaterialIdnt = o.SchoolMaterialId;
            var codeList = await dcSchoolMaterialCodeManager.Find(paramCode);
            foreach (var item in o.CodeList)
            {
                if (codeList.ToList().Where(f => f.ThirdCode == item.ThirdCode).Count() > 0)
                {
                    r.flag = 0;
                    r.msg = "您录入的编码在系统中已存在，不能重复录入。标识码：" + item.ThirdCode;
                    return r;
                }
            }
            List<DcSchoolMaterialCode> listAddCode = new List<DcSchoolMaterialCode>();
            o.CodeList.ForEach(p =>
            {
                listAddCode.Add(new DcSchoolMaterialCode
                {
                    SchoolId = user.UnitId,
                    SchoolMaterialId = o.SchoolMaterialId,
                    Num = p.Num,
                    UnitName = entity.UnitName,
                    ThirdNum = p.ThirdNum,
                    ThirdUnitName = p.ThirdUnitName,
                    ThirdCode = p.ThirdCode,
                    ThirdMaterialId = p.ThirdMaterialId,
                    ThirdMaterialName = p.ThirdMaterialName,
                    UserId = user.UserId,
                    RegDate = DateTime.Now
                });
            });

            var paramMaterialCode = new DcSchoolMaterialCodeParam();
            paramMaterialCode.SchoolMaterialId = o.SchoolMaterialId;
            var listMaterialCode = await dcSchoolMaterialCodeManager.Find(paramMaterialCode);

            if (listMaterialCode != null && listMaterialCode.Count > 0)
            {
                await dcSchoolMaterialCodeManager.DeleteByIds(listMaterialCode.Select(m => m.Id as object).ToArray());
            }

            await dcSchoolMaterialCodeManager.Add(listAddCode);

            entity.Statuz = 3;
            await dcSchoolMaterialManager.Update(entity);

            r.flag = 1;
            r.msg = "执行成功。";

            return r;
        }


        [HttpPost]
        [Route("dcschoolmaterialcodegetbymaterialid")]
        //<used>1</used>
        public async Task<Result> DcSchoolMaterialCode_GetByMaterialId(long schoolMaterialId)
        {
            Result r = new Result();
            var param = new DcSchoolMaterialCodeParam();
            param.SchoolMaterialId = schoolMaterialId;
            var list = await dcSchoolMaterialCodeManager.Find(param);
            r.flag = 1;
            r.msg = "查询成功。";
            r.data.rows = list;
            return r;
        }


        [HttpPost]
        [Route("dcapplythirdeasygrant")]
        //<used>1</used>
        public async Task<Result> DcApply_ThirdEasyGrant([FromBody] ThirdEasyGrantModel o)
        {
            Result r = new Result();
            r = await dcApplyManager.ThirdEasyGrant(o.id, o.isNeedSendMessage, o.list);
            if (r.flag == 1)
            {
                string message = ApplicationConfig.SendMessageEventDcGrantMsg;
                await SendMessage.SendToMobile(r.data.rows.ToString(), message);
            }
            return r;
        }


        [HttpPost]
        [Route("dcapplythirdeasyapply")]
        //<used>1</used>
        public async Task<Result> DcApply_ThirdEasyApply([FromBody] DcApplyDto o)
        {
            Result r = new Result();
            r = await dcApplyManager.ThirdEasyApply(o);
            return r;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcautobindissecuritysupervise")]
        //<used>1</used>
        public async Task<Result> DcAutoBindIsSecuritySupervise()
        {
            Result r = new Result();
            DangerChemicalsZbModel<ZbGetMaterialList> result = ZbGetMaterial();
            if (result.code == 1 && result.data.data.Count > 0)
            {
                List<ZbGetMaterial> zbList = result.data.data.Where(t => t.DEL == 0 && t.NEED_BIAOSHI == 1).ToList();
                var param = new DcBaseCatalogParam();
                param.Statuz = 1;
                param.Depth = 2;
                param.pageSize = int.MaxValue;
                var catalogList = await dcBaseCatalogManager.Find(param);
                var baseCatalogIds = new List<long>();
                foreach (var catalog in catalogList)
                {
                    var findList = zbList.Where(f => f.WP_NAME.Equals(catalog.Name) || (f.WP_BIEMING ?? "").Equals(catalog.Name)).ToList();
                    if (findList != null && findList.Count > 0)
                    {
                        baseCatalogIds.Add(catalog.Id);
                    }
                }
                if (baseCatalogIds.Count > 0)
                {
                    var paramSchoolModel = new DcSchoolMaterialModelParam();
                    paramSchoolModel.BaseCatalogIds = baseCatalogIds;
                    var listSchoolCatal = await dcSchoolMaterialModelManager.Find(paramSchoolModel);

                    var paramBaseModelExt = new DcBaseModelExtensionParam();
                    paramBaseModelExt.BaseCatalogIds = baseCatalogIds;
                    var listBaseModelExt = await dcBaseModelExtensionManager.Find(paramBaseModelExt);

                    if (listSchoolCatal != null && listSchoolCatal.Count > 0)
                    {
                        listSchoolCatal.ForEach(m => m.IsSecuritySupervise = 1);
                        await dcSchoolMaterialModelManager.Update(listSchoolCatal);
                    }

                    if (listBaseModelExt != null && listBaseModelExt.Count > 0)
                    {
                        listBaseModelExt.ForEach(m => m.IsSecuritySupervise = 1);
                        await dcBaseModelExtensionManager.Update(listBaseModelExt);
                    }
                }
                r.flag = 1;
                r.msg = "执行成功。";
            }
            else
            {
                r.flag = 0;
                r.msg = result.info;
            }
            return r;
        }

        [HttpPost]
        [Route("dcscrapsave")]
        //<used>1</used>
        public async Task<Result> DcScrap_Save(long id, int statuz, int wasetStatuz)
        {
            Result r = new Result();
            var audit = await dcMaterialsNumAuditManager.GetById(id);
            if (audit == null)
            {
                r.flag = 0;
                r.msg = "数据不存在。";
                return r;
            }
            DcSchoolMaterial entity = await dcSchoolMaterialManager.GetById(audit.SchoolMaterialId);
            if (entity == null || entity.SchoolId != user.UnitId)
            {
                r.flag = 0;
                r.msg = "数据不存在。";
                return r;
            }
            string thirdUnitId = ""; //第三方单位Id
            ZbDataStoragePlace zbStoragePlace = null;
            ZbDataUser zbUser = null;
            if (statuz == 1 && !string.IsNullOrEmpty(entity.ThirdMaterialId))
            {
                (r.msg, thirdUnitId, zbStoragePlace, zbUser) = await GrantCheckMessage(thirdUnitId, zbStoragePlace, zbUser);
                if (!string.IsNullOrEmpty(r.msg))
                {
                    return r;
                }
            }
            r = await dcSchoolMaterialManager.DcScrap_Save(id, statuz, wasetStatuz, user.UnitId, user.UserId);
            if (r.flag == 1 && r.Id > 0)
            {
                dcApplyManager.AutoStockNumUpdate(r.Id);
            }
            return r;
        }


        /// <summary>
        /// 获取化学品库信息
        /// </summary>
        /// <returns></returns>
        private DangerChemicalsZbModel<ZbGetMaterialList> ZbGetMaterial()
        {
            string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/getWupin";

            var postData = new
            {
                ipage = 0,
                pagesize = 0
            };
            string postStr = string.Format("data={0}&key=duijie", GetSm2(postData));
            DangerChemicalsZbModel<ZbGetMaterialList> res = ComLib.JSON2Object<DangerChemicalsZbModel<ZbGetMaterialList>>(ComLib.HttpPost(url, postStr, GetHeader()));
            return res;
        }
        /// <summary>
        /// 获取接口头信
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, string> GetHeader()
        {
            Dictionary<string, string> header = new Dictionary<string, string>();
            header.Add("authorization-token", Sm3Crypto.ToSM3HexStr(ApplicationConfig.DangerChemicalsZbId + "," + ApplicationConfig.DangerChemicalsZbUserName + "," + ApplicationConfig.DangerChemicalsZbPwd));
            return header;
        }
        /// <summary>
        /// 获取参数sm2加密后结果
        /// </summary>
        /// <returns></returns>
        private string GetSm2(object postData)
        {
            string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhouTool/s/SM2jiami";

            string result = ComLib.HttpPost(url, ComLib.Object2JSON(postData));

            DangerChemicalsZbModel<ZbDataString> sm2Result = ComLib.JSON2Object<DangerChemicalsZbModel<ZbDataString>>(result);
            if (sm2Result.code == 1)
                return sm2Result.data.data;
            else
                return "";
        }

        [HttpPost]
        [Route("dcreportzbhfyt")]
        //<used>0</used>
        public async Task<Result> DcReportZbHfyt(ZbAddLegalUse o)
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                DangerChemicalsZbModel<ZbGetMaterialList> result = ZbGetMaterial();
                if (result.code == 1 && result.data.data.Count > 0)
                {
                    List<ZbGetMaterial> zbList = result.data.data.Where(t => t.DEL == 0 && t.NEED_BIAOSHI == 1).ToList();

                    foreach (var item in o.HFYT_WP)
                    {
                        var findList = zbList.Where(f => f.WP_NAME.Equals(item.WP_NAME) || (f.WP_BIEMING ?? "").Equals(item.WP_NAME)).ToList();
                        if (findList.Count > 0)
                        {
                            item.WP_ID = findList.First().WP_ID;
                        }
                        item.WP_JLDW = ConvertUnitMeasurement(item.WP_JLDW);
                    }
                    string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/insertHfyt";

                    string postStr = string.Format("data={0}&key=duijie", GetSm2(o));
                    var apiR = ComLib.JSON2Object<DangerChemicalsZbModel<ZbLegalResult>>(ComLib.HttpPost(url, postStr, GetHeader()));
                    r.flag = apiR.code;
                    r.msg = apiR.info;
                }
                else
                {
                    r.flag = 0;
                    r.msg = result.info;
                }
                return r;
            });
            return result;
            
        }


        private string ConvertUnitMeasurement(string unitMeasurement)
        {
            string zm = "";
            switch (unitMeasurement)
            {
                case "毫克":
                    zm = "mg";
                    break;
                case "克":
                    zm = "g";
                    break;
                case "千克":
                    zm = "kg";
                    break;
                case "吨":
                    zm = "t";
                    break;
                case "毫升":
                    zm = "ml";
                    break;
                case "升":
                    zm = "l";
                    break;
                case "千升":
                    zm = "kl";
                    break;
                default:
                    zm = unitMeasurement;
                    break;
            }
            return zm;
        }

        /// <summary>
        /// 获取销售单位接口
        /// </summary>
        /// <returns></returns>
        private DangerChemicalsZbModel<ZbDataUnitInfoList> GetZbSaleUnitList(string name = "")
        {
            string url = ApplicationConfig.DangerChemicalsZbUrl + "/receiveSuzhou/s/getDanwei";

            var postData = new
            {
                ipage = 0,
                pagesize = 0,
                COMP_NATURE = "XS",
                COMP_NAME = name
            };
            string postStr = string.Format("data={0}&key=duijie", GetSm2(postData));
            return ComLib.JSON2Object<DangerChemicalsZbModel<ZbDataUnitInfoList>>(ComLib.HttpPost(url, postStr, GetHeader()));
        }


        /// <summary>
        /// 发放校验及获取第三方接口数据
        /// </summary>
        /// <param name="thirdUnitId"></param>
        /// <param name="storagePlace"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task<(string,string, ZbDataStoragePlace, ZbDataUser)> GrantCheckMessage(string thirdUnitId, ZbDataStoragePlace storagePlace, ZbDataUser user)
        {
            storagePlace = null;
            user = null;
            thirdUnitId =await GetThirdUnitId();  //单位第三方Id
            if (string.IsNullOrEmpty(thirdUnitId))
            {
                return( "本单位尚未与第三方平台关联，请联系平台客服处理。", thirdUnitId, storagePlace, user);
            }

            //获取默认库房
            Result placeResult =await ZbGetStoragePlaceList(thirdUnitId);
            if (placeResult.flag == 1 && placeResult.data.rows != null)
            {
                List<ZbDataStoragePlace> placeList =ComLib.JSON2Object<List<ZbDataStoragePlace>>(placeResult.data.rows.ToString());
                if (placeList.Count > 0)
                    storagePlace = placeList[0];
                else
                    return ("第三方平台中未设置储存场所。", thirdUnitId, storagePlace, user);

            }
            else
            {
                return (placeResult.msg, thirdUnitId, storagePlace, user);
            }

            //获取默认经办人
            Result userResult =await ZbGetUserList(thirdUnitId);
            if (userResult.flag == 1 && userResult.data.footer != null)
            {
                user =ComLib.JSON2Object<ZbDataUser>(userResult.data.footer.ToString());
            }
            else
            {
                return (userResult.msg, thirdUnitId, storagePlace, user);
            }
            return ("", thirdUnitId, storagePlace, user);
        }

        /// <summary>
        /// 获取当前单位对应的第三方Id
        /// </summary>
        /// <returns></returns>
        private async Task<string> GetThirdUnitId()
        {
            PUnit unit =await unitManager.QueryById(user.UnitId);
            if (unit != null)
            {
                return unit.ThirdUnitId;
            }
            else
            {
                return string.Empty;
            }
        }
    }

    public class DcReportResultCz
    {
        public int code { get; set; }
        public string info { get; set; }
        public string data { get; set; }
    }

    /// <summary>
    /// 物品绑定对应编码
    /// </summary>
    public class DcSchoolMaterialBindCode
    {
        /// <summary>
        /// 单位物品Id
        /// </summary>
        public int SchoolMaterialId { get; set; }

        /// <summary>
        /// 编码集合
        /// </summary>
        public List<DcMaterialCode> CodeList { get; set; }
    }
    /// <summary>
    /// 编码及对应数量
    /// </summary>
    public class DcMaterialCode
    {
        /// <summary>
        /// 对应数量
        /// </summary>
        public decimal Num { get; set; }

        /// <summary>
        /// 第三方标识码
        /// </summary>
        public string ThirdCode { get; set; }

        /// <summary>
        /// 第三方物品Id
        /// </summary>
        public string ThirdMaterialId { get; set; }

        /// <summary>
        /// 第三方物品名称
        /// </summary>
        public string ThirdMaterialName { get; set; }

        /// <summary>
        /// 第三方数量
        /// </summary>
        public decimal ThirdNum { get; set; }

        /// <summary>
        /// 第三方计量单位
        /// </summary>
        public string ThirdUnitName { get; set; }
    }
}
