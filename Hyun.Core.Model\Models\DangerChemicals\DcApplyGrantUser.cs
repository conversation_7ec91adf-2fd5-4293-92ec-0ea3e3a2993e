namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///领用人、发放人设置
    ///</summary>
    [SugarTable("dc_ApplyGrantUser", "领用人、发放人设置")]
    public class DcApplyGrantUser : BaseEntity
    {

        public DcApplyGrantUser()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///领用人、发放人Id
        /// </summary>
        public long MemberUserId { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        ///类型（1：领用人，2：发放人）
        /// </summary>
        public int UserType { get; set; }

        /// <summary>
        ///添加人
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///添加时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
    }


}

