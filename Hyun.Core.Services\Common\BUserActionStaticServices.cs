﻿using Hyun.Core.Common.DB;
using Hyun.Core.Model;
using MongoDB.Bson.IO;

namespace Hyun.Core.Services
{

    ///<summary>
    ///BUserActionStatic方法
    ///</summary>
    public class BUserActionStaticServices : BaseServices<BUserActionStatic>, IBUserActionStaticServices
    {

        public BUserActionStaticServices()
        {

        }

        #region 查询数据
        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        public async Task<BUserActionStatic> GetById(long id)
        {
            return await base.QueryById(id);
        }

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        public async Task<List<BUserActionStatic>> Find(Expression<Func<BUserActionStatic, bool>> expression)
        {
            return await base.Query(expression);
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">BUserActionStaticParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<BUserActionStatic>> GetPaged(BUserActionStaticParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            return await base.QueryPage(expression, param.pageIndex, param.pageSize, orderByFields);
        }
        #endregion

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<Result> Login(long userId)
        {
            Result r = new Result();
            BUserActionStatic actionStatic = await this.Db.Queryable<BUserActionStatic>().Where(f => f.UserId == userId).FirstAsync();
            if(actionStatic == null)
            {
                await base.Add(new BUserActionStatic()
                {
                    // 移除手动设置ID，让系统自动生成
                    UserId = userId,
                    ContiLoginCount = 1,
                    TotalLoginCount = 1,
                    LoginCount = 1,
                    CreateTime = DateTime.Now,
                    ModifyTime = DateTime.Now
                });
                r.flag = 1;
                r.msg = "保存成功";

            }
            else
            {
                int ContiLoginCount = actionStatic.ContiLoginCount;
                int TotalLoginCount = actionStatic.TotalLoginCount;
                TimeSpan difference = DateTime.Now - actionStatic.ModifyTime;
                int daysDiff = difference.Days;
                if(daysDiff == 1)
                {
                    ContiLoginCount += 1;
                    TotalLoginCount += 1;
                }
                else if(daysDiff > 1)
                {
                    ContiLoginCount = 1;
                    TotalLoginCount += 1;
                }
                actionStatic.ContiLoginCount = ContiLoginCount;
                actionStatic.TotalLoginCount = TotalLoginCount;
                actionStatic.LoginCount = actionStatic.LoginCount + 1;
                actionStatic.ModifyTime = DateTime.Now;
                await base.Update(actionStatic);
                r.flag = 1;
                r.msg = "修改成功";
            }

            return r;
        }

        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">BUserActionStaticParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<BUserActionStatic, bool>> ListFilter(BUserActionStaticParam param)
        {
            var expression = LinqExtensions.True<BUserActionStatic>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
            }
            return expression;
        }
        #endregion
    }
}

