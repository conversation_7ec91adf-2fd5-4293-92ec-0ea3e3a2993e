namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///912页面表
    ///</summary>
    [SugarTable("o_PowerPage","912页面表")]
    public class OPowerPage : BaseEntity
    {

          public OPowerPage()
          {

          }

           /// <summary>
           ///自定义编号
          /// </summary>
          public long PageId { get; set; }

           /// <summary>
           ///父编号
          /// </summary>
          public long Pid { get; set; }

           /// <summary>
           ///菜单名称
          /// </summary>
          [SugarColumn(Length = 63)]
          public string Name { get; set; }

           /// <summary>
           ///菜单路径
          /// </summary>
          [SugarColumn(Length = 255)]
          public string Url { get; set; }

           /// <summary>
           ///子菜单排序
          /// </summary>
          public int SubOrder { get; set; }

           /// <summary>
           ///深度
          /// </summary>
          public int Depth { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          [SugarColumn(Length = 1023,IsNullable = true)]
          public string Sort { get; set; }

           /// <summary>
           ///页面编码
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public string Code { get; set; }

           /// <summary>
           ///页面类型（0：共用，1：管理员专用）
          /// </summary>
          public int PageType { get; set; }

           /// <summary>
           ///是否为栏目
          /// </summary>
          public bool IsBar { get; set; }

           /// <summary>
           ///是否显示
          /// </summary>
          public bool IsShow { get; set; }

           /// <summary>
           ///图标
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string Icon { get; set; }

           /// <summary>
           ///小图标
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string SmallIcon { get; set; }

           /// <summary>
           ///描述
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///是否市级
          /// </summary>
          public bool IsCity { get; set; }

           /// <summary>
           ///是否区县
          /// </summary>
          public bool IsCounty { get; set; }

           /// <summary>
           ///是否单位
          /// </summary>
          public bool IsSchool { get; set; }

           /// <summary>
           ///是否供应商
          /// </summary>
          public bool IsCompany { get; set; }

           /// <summary>
           ///在本地使用别名，方便分辨。
          /// </summary>
          [SugarColumn(Length = 63)]
          public string CName { get; set; }

           /// <summary>
           ///授权方式（0：普通菜单（默认所有平台自动授权，如果有一个平台不需要，必须手动取消）；   1：版本特定专有菜单（默认不授权，如果需要，必须手工授权））
          /// </summary>
          public int AuthorizationType { get; set; }

           /// <summary>
           ///授权验证级别（1：一次验证授权即可；2：需要二次验证授权）
          /// </summary>
          public int VerificationLevel { get; set; }

           /// <summary>
           ///是否为二次授权主页
          /// </summary>
          public int IsLevel2MainPage { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

