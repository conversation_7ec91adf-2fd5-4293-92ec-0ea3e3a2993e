namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///实验后确认危废物填写记录表
    ///</summary>
    [SugarTable("dc_WasteRecord", "实验后确认危废物填写记录表")]
    public class DcWasteRecord : BaseEntity
    {

        public DcWasteRecord()
        {

        }

        /// <summary>
        ///关联表Id（类型1时dc_ApplyConfirmDetail表Id）
        /// </summary>
        public long ObjectId { get; set; }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///危废物基础分类Id
        /// </summary>
        public long BaseWasteId { get; set; }

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Num { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        ///操作人
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///更新时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///类型（1：实验后确认）
        /// </summary>
        public int ObjectType { get; set; }

        /// <summary>
        ///废弃物处置详细表Id
        /// </summary>
        public long WasteDisposalDetailId { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
    }


}

