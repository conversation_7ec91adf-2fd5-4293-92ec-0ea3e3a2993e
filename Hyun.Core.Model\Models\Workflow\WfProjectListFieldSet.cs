﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批预算清单配置
    ///</summary>
    [SugarTable("wf_ProjectListFieldSet", "审批预算清单配置")]
    public class WfProjectListFieldSet : BaseEntity
    {

        public WfProjectListFieldSet()
        {

        }

        /// <summary>
        ///审批流程配置表Id
        /// </summary>
        public long ProcessId { get; set; } = 0;

        /// <summary>
        ///配置类型（0：基础数据  1：业务填报——添加数据  2：业务填报——列表数据  3：业务填报——Execl数据  4：基础数据——添加数据 5：基础数据——列表数据；6：基础数据——Execl数据 ）
        /// </summary>
        public int ConfigType { get; set; } = 0;

        /// <summary>
        ///标题显示名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Title { get; set; }

        /// <summary>
        ///列绑定字段值
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string FieldValue { get; set; }

        /// <summary>
        ///排序值（值从小到大排序）
        /// </summary>
        public int Sort { get; set; } = 0;

        /// <summary>
        ///列表字段是否排序（1：是  2：否）【仅用于列表】
        /// </summary>
        public int IsSort { get; set; } = 1;

        /// <summary>
        ///列显示宽度【仅用于列表】
        /// </summary>
        public int Width { get; set; } = 100;

        /// <summary>
        ///标题对齐方式（center、left、right）【仅用于列表】
        /// </summary>
        [SugarColumn(Length = 31)]
        public string TitleStyle { get; set; } = "center";

        /// <summary>
        ///内容对齐方式（center、left、right）【仅用于列表】
        /// </summary>
        [SugarColumn(Length = 31)]
        public string ContentStyle { get; set; } = "left";

        /// <summary>
        ///添加标题是否显示（2：不显示 1：显示）默认显示
        /// </summary>
        public int IsShow { get; set; } = 1;

        /// <summary>
        ///类型（1：文本框  2：数字框 3：下拉框 4：多行文本框 5：日期）
        /// </summary>
        public int TypeStyle { get; set; } = 1;

        /// <summary>
        ///当类型框为下拉框是此处为[{"text":"张三"},{"text":"李四"}]数据形式【仅用于TypeStyle=3的】
        /// </summary>
        [SugarColumn(Length = 1024, IsNullable = true)]
        public string SourceValue { get; set; }

        /// <summary>
        ///是否必填（2：非必填  1：必填）默认非必填【仅用于添加数据】
        /// </summary>
        public int IsRequired { get; set; } = 2;

        /// <summary>
        ///提示文字【仅用于添加数据】
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string TipMsg { get; set; }

        /// <summary>
        ///单位Id（为0则是基础数据为超管配置）
        /// </summary>
        public long UnitId { get; set; } = 0;

        /// <summary>
        ///搜索是否显示（1：显示 2：不显示）
        /// </summary>
        public int SearchIsShow { get; set; } = 2;

        /// <summary>
        ///搜索排序值（值从小到大排序）
        /// </summary>
        public int SearchSortValue { get; set; } = 0;

        /// <summary>
        /// 表单宽度（百分比）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? FormWidth { get; set; } = 100;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 字段类型
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StrTypeStyle { get; set; }

        /// <summary>
        /// 字段Code编码
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string FieldCode { get; set; }

        /// <summary>
        /// 时间显示格式
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int DateDisplay { get; set; }

        /// <summary>
        /// 列表字段显示类型（1：数字  2：金额  3：文本   4：日期   5：按钮）
        /// </summary>
        [SugarColumn(DefaultValue = "3")]
        public int ColumnFieldType { get; set; }
    }


}

