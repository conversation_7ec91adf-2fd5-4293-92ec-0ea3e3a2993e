﻿using Hyun.Core.Model.Helper;
using NPOI.SS.UserModel;
using System.ComponentModel;

namespace Hyun.Core.Model
{

    ///<summary>
    ///101单位p_Unit
    ///</summary>
    public class PUnitDto : BaseEntity
    {

        public PUnitDto()
        {

        }

        /// <summary>
        ///区域Id
        /// </summary>
        public long AreaId { get; set; }
        /// <summary>
        /// 区域名称
        /// </summary>
        public string AreaName { get; set; }
        /// <summary>
        ///父级单位Id
        /// </summary>
        public long PId { get; set; }

        /// <summary>
        ///性质类型：1市级、2区县、3单位、4企业
        /// </summary>
        public int UnitType { get; set; }

        /// <summary>
        ///行业类型：普教、高教、职教、机关、家庭
        /// </summary>
        public int IndustryId { get; set; }

        /// <summary>
        ///单位编号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        ///名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        ///简称
        /// </summary>
        public string Brief { get; set; }

        /// <summary>
        ///全拼
        /// </summary>
        public string PinYin { get; set; }

        /// <summary>
        ///简拼
        /// </summary>
        public string PinYinBrief { get; set; }

        /// <summary>
        ///法人
        /// </summary>
        public string Legal { get; set; }

        /// <summary>
        ///地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        ///邮编
        /// </summary>
        public string ZipCode { get; set; }

        /// <summary>
        ///网址
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        ///简介
        /// </summary>
        public string Introduction { get; set; }

        /// <summary>
        ///Logo
        /// </summary>
        public string Logo { get; set; }

        /// <summary>
        ///VIP等级
        /// </summary>
        public int VipGrade { get; set; }

        /// <summary>
        ///组织机构代码
        /// </summary>
        public string OrganizationCode { get; set; }

        /// <summary>
        ///登录界面图片
        /// </summary>
        public string LoginPic { get; set; }

        /// <summary>
        ///短信服务手机号
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        ///联系人
        /// </summary>
        public string ContactUser { get; set; }

        /// <summary>
        ///联系电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        ///邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        ///地理位置打点
        /// </summary>
        public string Position { get; set; }

        /// <summary>
        ///交通路线图
        /// </summary>
        public string TrafficMap { get; set; }

        /// <summary>
        ///员工数
        /// </summary>
        public int EmployeeNum { get; set; }

        /// <summary>
        ///添加人
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///添加时间
        /// </summary>
        public DateTime RegTime { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        public string Memo { get; set; }

        /// <summary>
        ///状态（0：待审核，1：正常，2：待提交审核）
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        ///来源类型（1：企业注册  2：程序自动添加  3：程序自动添加后企业又来平台注册）默认1，主要用于企业
        /// </summary>
        public int SourceType { get; set; }

        /// <summary>
        ///区域街道、镇名称
        /// </summary>
        public string TownName { get; set; }

        /// <summary>
        ///第三方单位编码
        /// </summary>
        public string ThirdUnitId { get; set; }

        /// <summary>
        ///第三方单位名称
        /// </summary>
        public string ThirdUnitName { get; set; }

        /// <summary>
        ///主体性质（主要用于性质类型为单位的单位）对应字典表TypeCode为15000值
        /// </summary>
        public int SubjectNature { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        public int Mileage { get; set; }


        /// <summary>
        /// 班级数数，for 单位更新
        /// </summary>
        public int ClassNum { get; set; }
        /// <summary>
        /// 在校生数，for 单位更新
        /// </summary>
        public int StudentNum { get; set; }

        /// <summary>
        /// 教职工数
        /// </summary>
        public int TeacherNum { get; set; }

        /// <summary>
        /// 占地面积
        /// </summary>
        public decimal FloorArea { get; set; }

        /// <summary>
        /// 建筑面积
        /// </summary>
        public decimal BuildArea { get; set; }

        /// <summary>
        /// 学段
        /// </summary>
        public int SchoolStage { get; set; }

        /// <summary>
        /// 学段名称
        /// </summary>
        public string StrSchoolStage { get; set; }

        /// <summary>
        /// 单位管理员
        /// </summary>
        public string SchoolAdmin { get; set; }

        /// <summary>
        /// 单位管理员手机号码
        /// </summary>
        public string AdminMobile { get; set; }

        /// <summary>
        /// 单位Guid
        /// </summary>
        public string SchoolGuid { get; set; }

        /// <summary>
        /// 单位性质(1：公办  2：民办)
        /// </summary>
        public int SchoolNature { get; set; }

        /// <summary>
        /// 企业是否服务商
        /// 2017-11-09 update by jiangpeng
        /// </summary>
        public bool IsServiceProvider { get; set; } = false;
        /// <summary>
        /// 企业是否供应商
        /// 2017-11-09 update by jiangpeng
        /// </summary>
        public bool IsSupplier { get; set; } = false;

        /// <summary>
        /// 
        /// </summary>
        public string HeadMaster { get; set; }

        public string MsaterMobile { get; set; }

        /// <summary>
        /// 街道镇Id，对应Area表Id
        /// </summary>
        public long StreetTown { get; set; } = 0;

    }

    /// <summary>
    /// 单位导入实体
    /// </summary>
    public class SchoolImportDto
    {
        /// <summary>
        ///单位代码
        /// </summary>
        [Description("单位代码")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Code { get; set; }

        /// <summary>
        ///单位全称
        /// </summary>
        [Description("单位全称")]
        [ExportExcel(HorizontalAlignment.Left, 20)]
        public string Name { get; set; }

        /// <summary>
        ///单位简称
        /// </summary>
        [Description("单位简称")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Brief { get; set; }

        /// <summary>
        /// 单位性质
        /// </summary>
        [Description("单位性质")]
        [ExportExcel(HorizontalAlignment.Left, 20)]
        public string StrSchoolNature { get; set; } = "";


        /// <summary>
        /// 单位属性
        /// </summary>
        [Description("单位属性")]
        [ExportExcel(HorizontalAlignment.Left, 20)]
        public string StrSchoolStage { get; set; } = "";

        /// <summary>
        /// 班级总数
        /// </summary>
        [Description("班级总数")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public int ClassNum { get; set; } = 0;

        /// <summary>
        /// 学生总数
        /// </summary>
        [Description("学生总数")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public int StudentNum { get; set; } = 0;

        /// <summary>
        /// 教职工数
        /// </summary>
        [Description("教职工数")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public int TeacherNum { get; set; } = 0;

        /// <summary>
        /// 占地面积（平方米）
        /// </summary>
        [Description("占地面积（平方米）")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public decimal FloorArea { get; set; } = 0;

        /// <summary>
        /// 建筑面积（平方米）
        /// </summary>
        [Description("建筑面积（平方米）")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public decimal BuildArea { get; set; } = 0;

        /// <summary>
        /// 组织机构代码
        /// </summary>
        [Description("组织机构代码")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string OrganizationCode { get; set; }

        /// <summary>
        /// 单位地址
        /// </summary>
        [Description("单位地址")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Address { get; set; }

        /// <summary>
        /// 单位简介
        /// </summary>
        [Description("单位简介")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Introduction { get; set; }

        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
    }

    /// <summary>
    /// 区县导入实体
    /// </summary>
    public class CountyCityCompanyImportDto
    {
        /// <summary>
        ///单位编号
        /// </summary>
        [Description("单位编号")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Code { get; set; }

        /// <summary>
        ///单位名称
        /// </summary>
        [Description("单位名称")]
        [ExportExcel(HorizontalAlignment.Left, 20)]
        public string Name { get; set; }

        /// <summary>
        ///单位简称
        /// </summary>
        [Description("单位简称")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Brief { get; set; }

        /// <summary>
        /// 法人
        /// </summary>
        [Description("法人")]
        [ExportExcel(HorizontalAlignment.Left, 20)]
        public string Legal { get; set; } = "";

        /// <summary>
        /// 地址
        /// </summary>
        [Description("地址")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string Address { get; set; } = "";

        /// <summary>
        /// 邮编
        /// </summary>
        [Description("邮编")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string ZipCode { get; set; } = "";

        /// <summary>
        /// 网址
        /// </summary>
        [Description("网址")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string Url { get; set; } = "";

        /// <summary>
        /// 组织机构代码
        /// </summary>
        [Description("组织机构代码")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string OrganizationCode { get; set; } = "";

        /// <summary>
        /// 联系电话
        /// </summary>
        [Description("联系电话")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Tel { get; set; } = "";

        /// <summary>
        /// 邮箱
        /// </summary>
        [Description("邮箱")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Email { get; set; } = "";

        /// <summary>
        /// 员工数
        /// </summary>
        [Description("员工数")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public int EmployeeNum { get; set; } = 0;

        /// <summary>
        /// 简介
        /// </summary>
        [Description("简介")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Introduction { get; set; } = "";

        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
    }


    /// <summary>
    /// 区县、市级单位信息
    /// </summary>
    public class PUnitSetDto
    {
        /// <summary>
        /// 上级单位Id
        /// </summary>
        public long Pid { get; set; }

        /// <summary>
        /// 单位类型
        /// </summary>
        public int UnitType { get; set; }

        /// <summary>
        /// 单位全称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 统一社会信用代码
        /// </summary>
        public string OrganizationCode { get; set; }

        /// <summary>
        /// 单位隶属
        /// </summary>
        public string ParentUnitName { get; set; }
    }

    /// <summary>
    /// 单位注册实体
    /// </summary>
    public class UnitRegisterModel
    {
        /// <summary>
        /// 单位类型（3：单位 4：企业）
        /// </summary>
        public int UnitType { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 手机号码（账号）
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string PassWord { get; set; }

        /// <summary>
        /// 短信验证码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 手机号唯一标识
        /// </summary>
        public string UidMobile { get; set; }

        /// <summary>
        /// 短信验证码唯一标识
        /// </summary>
        public string UidCode { get; set; }
    }

    ///// <summary>
    ///// 校服平台单位信息
    ///// </summary>
    //public class UnitXfDto
    //{
    //    /// <summary>
    //    /// 单位Id
    //    /// </summary>
    //    public long Id { get; set; }

    //    /// <summary>
    //    ///单位类型：1市级、2区县、3单位、4企业
    //    /// </summary>
    //    public int UnitType { get; set; }

    //    /// <summary>
    //    /// 单位类型名称
    //    /// </summary>
    //    public string UnitTypeName { get; set; }

    //    /// <summary>
    //    ///单位名称
    //    /// </summary>
    //    public string Name { get; set; }

    //    /// <summary>
    //    ///单位简称
    //    /// </summary>
    //    public string Brief { get; set; }


    //    /// <summary>
    //    ///状态（0：待审核，1：正常）
    //    /// </summary>
    //    public int Statuz { get; set; }


    //    /// <summary>
    //    ///创建时间
    //    /// </summary>
    //    public DateTime RegTime { get; set; }

    //}

    public class WfUnitDto
    {
        /// <summary>
        /// 单位Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 单位地址
        /// </summary>
        public string UnitAddress { get; set; }

        /// <summary>
        /// 单位学段
        /// </summary>
        public string UnitPeriod { get; set; }

        /// <summary>
        /// 单位所属街道
        /// </summary>
        public string UnitStreetTown { get; set; }
    }

}

