﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///PUser接口方法已改名为SysUserExtension
    ///</summary>
    public interface ISysUserExtensionServices : IBaseServices<SysUserExtension>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<SysUserExtension> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<SysUserExtension>> Find(Expression<Func<SysUserExtension, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">PUserParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<SysUserExtension>> GetPaged(SysUserExtensionParam param);

        //<used>0</used>
        Task<string> GetUserNames(int userId = 0, int unitId = 0, int roleId = 0);

        //<used>0</used>
        Task<Result> CreateByProjectItemId(int projectItemId, string mobile, string name, int optId, int optUnitId, int id);

        //<used>0</used>
        Task<Result> BatchSetRole(string userIds, string roleIds, long unitId, long userId, int unitTypeId, int operateType);

        //<used>0</used>
        Task<DataTable> GetSysStatInfo();

        //<used>0</used>
        Task<DataTable> GetPersonUnitCount(int userId);

        //<used>0</used>
        Task<DataTable> GetPersonOptInfo(int userId);

        //<used>0</used>
        Task<Result> Add(string staffNumber, int unitId, string idNumber, string name, string sex, DateTime birthday, string address, string zipCode, string tel, string mobile, string qq, string email, int userId, string memo, int statuz, int id);

        //<used>0</used>
        Task<Result> ChangeAccount(int acctId, string pwd, string oldAcctName, string newAcctName);

        //<used>0</used>
        Task<Result> ChangeMobile(int userId, string mobile);

        //<used>0</used>
        Task<Result> ChangeNickName(int userId, string nickName);

        //<used>0</used>
        Task<Result> ChangePass(long acctId, string oldPwd, string newPwd);

        //<used>1</used>
        Task<Result> Edit(VUserDetail o);

        //<used>0</used>
        Task<Result> InsertUpdate(VUserDetail o);

        //<used>0</used>
        Task<Result> DeleteByIds(string ids, int userId, int unitId);

        //<used>0</used>
        Task<Result> Leave(int optId, int userId);

        //<used>0</used>
        Task<int> GetUserCountByRoleId(int unitId, int roleId, int userId);

        //<used>1</used>
        Task<Result> BinderOtherAccount(string loginName, string pwd, long createUserId);

        //<used>0</used>
        Task<List<SysUserExtension>> GetUserByRoleId(int roleId, int unitId);

        //<used>0</used>
        Task<string> GetUserMobile(string ids);

        Task<Result> UpdateStatuz(long userId);

        Task<Result> BatchDeleteUser(long userId);

        Task<Result> UpdateMyUnitStatuz(long userId);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns></returns>
         //<used>1</used>
        Task<List<PUserDto>> StatisticsUser(long unitId);

        //<used>1</used>
        Task<Result> ChangeIsShow(long userId);

        //<used>1</used>
        Task<Result> ExchangeUnit(long unitExchangeId, string remark, int statuz, string roleIds, long unitId, long userId, int unitTypeId, int isUseNewAccount);

        /// <summary>
        /// 获取用户信息(校服平台)
        /// </summary>
        /// <returns></returns>
        Task<Result> GetUserInfo(long userId);

        /// <summary>
        /// 设置个人信息(校服平台)
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result> SetUserInfo(UserModel o);

        /// <summary>
        /// 校服管理平台班主任、家长账号信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VUserList>> GetXfUserPaged(VUserListParam param);

        /// <summary>
        /// 超管重置班主任、家长密码
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> AmendPswd(UserXfPswdModel o);
    }
}

