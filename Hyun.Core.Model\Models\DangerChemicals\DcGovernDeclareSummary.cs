namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///危化品治理填报汇总（按周、按月写入、按任务）
    ///</summary>
    [SugarTable("dc_GovernDeclareSummary", "危化品治理填报汇总（按周、按月写入、按任务）")]
    public class DcGovernDeclareSummary : BaseEntity
    {

        public DcGovernDeclareSummary()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///整治年度
        /// </summary>
        public int GovernYear { get; set; }

        /// <summary>
        ///填报类型(1:周   2：月 3：任务）
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        ///存在问题（个数）
        /// </summary>
        public int ProblemNum { get; set; }

        /// <summary>
        ///已整改问题（个数）
        /// </summary>
        public int RectifyProblemNum { get; set; }

        /// <summary>
        ///存在隐患（个数）
        /// </summary>
        public int DangerNum { get; set; }

        /// <summary>
        ///已整改隐患（个数）
        /// </summary>
        public int RectifyDangerNum { get; set; }

        /// <summary>
        ///申报时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///申报单位类型
        /// </summary>
        public int UnitIdType { get; set; }

        /// <summary>
        ///创建单位Id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        ///申报人Id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///任务Id（填报类型1,2时为0 ，3的时候对应任务Id）
        /// </summary>
        public long GovernTaskId { get; set; }

        /// <summary>
        ///周期数
        /// </summary>
        public int NumberCyclez { get; set; }

        /// <summary>
        ///填报时间段
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string DatePeriod { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
    }
}

