
Date：2025-07-28 17:43:29.925
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:29.925
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.139
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/7/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.308
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.391
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.423
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.481
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/7/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.536
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:702563544686725 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.836
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:34.870
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.553
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/7/28 17:43:34 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/28 17:43:34 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.591
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.592
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.592
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.592
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.622
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.622
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.633
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.813
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.813
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.845
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.870
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:1 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:194 [Type]:Int32    
[Name]:@LoginCount [Value]:1575 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/7/28 17:43:35 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.875
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/28 17:43:35 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:35.875
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/7/28 17:43:35 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:36.025
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/28 17:43:35 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:36.025
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/7/28 17:43:35 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:36.081
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355,355,355,355,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:38.389
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:38.454
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (30,350,354,351,355,352,353,370,3200)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:38.815
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000010,100000000001001,100000000001002,100000000001003,100000000001004,100000000000020,100000000002001,100000000002002,100000000002017,100000000002008,100000000002009,100000000002010,100000000003001,100000000000030,100000000000040,100000000004001,100000000004003,100000000004004,100000000000050,100000000005001,100000000000060,100000000006001,100000000006002,100000000006003,100000000006004,100000000000070,100000000007004,100000000007005,100000000007006,100000000007007,100000000007017,100000000007018,100000000007019,100000000002011,100000000002012,100000000002013,100000000002014,100000000002015,100000000007001,100000000004002,100000000007002,100000000007014,100000000007020,100000000007021,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,815152703427579904,815152216284336128,814917889516638208,827558788750905344,827558788880928768,827558790864834560,840599930178375680,840600506052120576,842694325652426752,827558797206622208,815512156001800192,815512500404490240,815512620718100480,815512817057665024,844961710316982272,844957453845008384,827558795155607552,844957620178522112,844966541932892160,844966811886686208,844972844101144576,844972973948407808,844973424735424512,844973591006023680,844973895587991552,844974175394205696,844974402880671744,844974534216912896,844974696779747328,844974828485087232,844975023654440960,844975297731235840,844975449124638720,844975635729223680,844975797407059968,844975728242987008,844976309716127744,844976490087976960,844976660540297216,844976914006282240,844977126699438080,844977251022802944,844977462650605568,844977581781422080,844977790431268864,844978207466721280,844978326723366912,844977922153385984,844978006509228032,844978402241810432,844978933500743680,844979146500083712,844979303874564096,845255096391438336,844979454731096064,844979648616992768,844979828049317888,844980287262691328,844980359450857472,844980598295498752,844982125559025664,844981921363529728,844981743516651520,844981082699862016,844981392927363072,844980852977831936,844983416607739904,844983343421329408,844982957755076608,844982807137619968,844982595073609728,844982459761168384,844982262398193664,844983686142103552,844983827448205312,844983906800242688,844984091039240192,844984272614854656,844984515939012608,844984636638498816,844984773846765568,844984942273236992,844985090730627072,844985239972352000,844985309048344576,844985864302891008,844986081353928704,844986204750352384,844986319925940224,844986470987993088,844988224278368256,844988795462881280,844988925083652096,844989069602590720,844992020215762944,844992477537505280,844992359836946432,844992873186201600,844992931449278464,844993347960442880,844993496422027264,844997017473126400,844997280166580224,844997405890842624,844997569112182784,844997754236178432,844997884142161920,844997950672211968,844998050886717440,844998119732023296,855107575388049408,855107866284003328)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.025
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst8) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst9) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst10) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst11) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst12) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst13) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst15  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted7 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted7 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted5 ) AND ( [PAU].[AuditUserId] = @AuditUserId6 ))  AND ( [PAU].[IsDeleted] = @IsDeleted7 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId6 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
[Name]:@MethodConst8 [Value]: [Type]:String    
[Name]:@MethodConst9 [Value]:0 [Type]:Int32    
[Name]:@MethodConst10 [Value]: [Type]:String    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst15 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.074
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.129
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.152
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.174
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.204
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:702534294601861 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.224
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679134371973 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.276
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:674228238864517 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.309
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:675249294651525 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.349
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:39.374
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:42.122
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:42.149
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:42.199
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:43.802
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:43.810
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:44.139
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted1 )   WHERE ( [scheme].[SchoolId] = @SchoolId0 )  AND ( [scheme].[IsDeleted] = @IsDeleted1 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@SchoolId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:44.139
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted1 )   WHERE ( [scheme].[SchoolId] = @SchoolId0 )  AND ( [scheme].[IsDeleted] = @IsDeleted1 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@SchoolId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:44.147
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY  CreateTime DESC ) AS RowIndex  FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted1 )   WHERE ( [scheme].[SchoolId] = @SchoolId0 )  AND ( [scheme].[IsDeleted] = @IsDeleted1 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@SchoolId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:44.147
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY  CreateTime DESC ) AS RowIndex  FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted1 )   WHERE ( [scheme].[SchoolId] = @SchoolId0 )  AND ( [scheme].[IsDeleted] = @IsDeleted1 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@SchoolId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:49.898
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:49.898
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:49.904
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:49.905
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:50.307
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UnitId],[Pid],[Name],[SocialCreditCode],[BusLicense],[ProvinceId],[CityId],[CountyId],[Address],[Url],[Introduction],[Tel],[Nature],[SchoolStage],[BeLongUnit],[AuthStatuz],[Period],[Reason],[IsCurrent],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_SupplierSchoolAudit]  WHERE (( [UnitId] = @UnitId0 ) AND ( [IsCurrent] = @IsCurrent1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsCurrent1 [Value]:True [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:50.431
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:50.447
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:100000 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:50.478
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (( [Statuz] = @Statuz0 ) AND ( [UnitType] = @UnitType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@UnitType1 [Value]:2 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:50.714
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:43:50.727
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER( ORDER BY GetDate() ) AS RowIndex  FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:38.533
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:38.533
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:38.636
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:38.647
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:38.663
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:38.674
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:38.706
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:42.334
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER( ORDER BY GetDate() ) AS RowIndex  FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:42.347
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UnitId],[Pid],[Name],[SocialCreditCode],[BusLicense],[ProvinceId],[CityId],[CountyId],[Address],[Url],[Introduction],[Tel],[Nature],[SchoolStage],[BeLongUnit],[AuthStatuz],[Period],[Reason],[IsCurrent],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_SupplierSchoolAudit]  WHERE (( [UnitId] = @UnitId0 ) AND ( [IsCurrent] = @IsCurrent1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsCurrent1 [Value]:True [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:46.378
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:47.185
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:100000 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:45:49.641
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (( [Statuz] = @Statuz0 ) AND ( [UnitType] = @UnitType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@UnitType1 [Value]:2 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:29.864
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:29.864
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:29.879
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:29.879
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:29.884
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:29.886
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:35.226
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:35.230
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UnitId],[Pid],[Name],[SocialCreditCode],[BusLicense],[ProvinceId],[CityId],[CountyId],[Address],[Url],[Introduction],[Tel],[Nature],[SchoolStage],[BeLongUnit],[AuthStatuz],[Period],[Reason],[IsCurrent],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_SupplierSchoolAudit]  WHERE (( [UnitId] = @UnitId0 ) AND ( [IsCurrent] = @IsCurrent1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsCurrent1 [Value]:True [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:35.234
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER( ORDER BY GetDate() ) AS RowIndex  FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:35.238
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:35.251
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:100000 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:46:35.258
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (( [Statuz] = @Statuz0 ) AND ( [UnitType] = @UnitType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@UnitType1 [Value]:2 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:49:00.903
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:49:00.904
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:49:53.948
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:49:53.949
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:12.969
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:12.978
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:13.034
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:13.063
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:13.086
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:13.146
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.771
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UnitId],[Pid],[Name],[SocialCreditCode],[BusLicense],[ProvinceId],[CityId],[CountyId],[Address],[Url],[Introduction],[Tel],[Nature],[SchoolStage],[BeLongUnit],[AuthStatuz],[Period],[Reason],[IsCurrent],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_SupplierSchoolAudit]  WHERE (( [UnitId] = @UnitId0 ) AND ( [IsCurrent] = @IsCurrent1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsCurrent1 [Value]:True [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.775
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UnitId],[Pid],[Name],[SocialCreditCode],[BusLicense],[ProvinceId],[CityId],[CountyId],[Address],[Url],[Introduction],[Tel],[Nature],[SchoolStage],[BeLongUnit],[AuthStatuz],[Period],[Reason],[IsCurrent],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_SupplierSchoolAudit]  WHERE (( [UnitId] = @UnitId0 ) AND ( [IsCurrent] = @IsCurrent1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UnitId0 [Value]:*************** [Type]:Int64    
[Name]:@IsCurrent1 [Value]:True [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.803
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.792
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.810
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.819
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:100000 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.810
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.827
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER( ORDER BY GetDate() ) AS RowIndex  FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.835
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (( [Statuz] = @Statuz0 ) AND ( [UnitType] = @UnitType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@UnitType1 [Value]:2 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.841
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:50:21.822
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE (( [TypeCode] = @TypeCode0 ) AND ( [Statuz] = @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:100000 [Type]:String    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:57:34.485
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (( [Statuz] = @Statuz0 ) AND ( [UnitType] = @UnitType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@UnitType1 [Value]:2 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:57:34.490
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-07-28 17:57:36.519
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER( ORDER BY GetDate() ) AS RowIndex  FROM [b_AttachmentConfig]  WHERE ((( 1 = 1 ) AND( [ModuleType] = @ModuleType1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@ModuleType1 [Value]:107 [Type]:Int32    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------