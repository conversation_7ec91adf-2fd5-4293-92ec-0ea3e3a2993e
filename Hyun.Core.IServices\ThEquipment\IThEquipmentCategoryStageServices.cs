﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///ThEquipmentCategoryStage接口方法
    ///</summary>
    public interface IThEquipmentCategoryStageServices : IBaseServices<ThEquipmentCategoryStage>
    {

        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="o">ThEquipmentCategoryStage对象</param>
        /// <returns></returns>
        Task<Result> InsertUpdate(ThEquipmentCategoryStage o);

        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="o">List<ThEquipmentCategoryStage>对象</param>
        /// <returns></returns>
        Task<Result> BatchAdd(List<ThEquipmentCategoryStage> list);

        /// <summary>
        /// 根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        Task<Result> DeleteByCategoryId(ThEquipmentCategoryStageDto model);

        /// <summary>
        /// 根据Id集合批量删除数据【假删除】
        /// </summary>
        ///  <param name="ids">id集合逗号分隔</param>
        /// <returns></returns>
        Task<Result> FakeDeleteByIds(string ids);

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<ThEquipmentCategoryStage> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<ThEquipmentCategoryStage>> Find(Expression<Func<ThEquipmentCategoryStage, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">ThEquipmentCategoryStageParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<ThEquipmentCategoryStage>> GetPaged(ThEquipmentCategoryStageParam param);

    }
}

