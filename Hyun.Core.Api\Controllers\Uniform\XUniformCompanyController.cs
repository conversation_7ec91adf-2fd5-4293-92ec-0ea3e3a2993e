﻿using Hyun.Core.IServices.Uniform;
namespace Hyun.Core.Api
{
    /// <summary>
    /// 校服维护
    /// </summary>
    [Route("api/hyun/xuniformlist")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformCompanyController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformCompanyServices ixuniformCompanyManager;
        private readonly IXUniformCompanySizeServices ixuniformCompanySizeManager;
        private readonly IPUnitServices ipunitManager;
        private readonly IBAttachmentServices ibattachmentManager;
        private readonly IBDictionaryServices ibdictionaryManager;

        public XUniformCompanyController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IXUniformCompanyServices _ixuniformCompanyManager, IXUniformCompanySizeServices _ixuniformCompanySizeManager, IPUnitServices _ipunitManager, IBAttachmentServices _ibattachmentManager, IBDictionaryServices _ibdictionaryManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ixuniformCompanyManager = _ixuniformCompanyManager;
            ixuniformCompanySizeManager = _ixuniformCompanySizeManager;
            ipunitManager = _ipunitManager;
            ibattachmentManager = _ibattachmentManager;
            ibdictionaryManager = _ibdictionaryManager;
        }


        /// <summary>
        /// 校服采购-校服管理-查询列表（校服录入）
        /// </summary>
        /// <param name="param">XUniformListParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<XUniformCompanyDto>>> GetPaged([FromBody] XUniformCompanyParam param)
        {
            var r = new Result<List<XUniformCompanyDto>>();
            param.UnitType = user.UnitTypeId;
            param.unitId = user.UnitId;
            param.userId = user.UserId;
            var pg = await ixuniformCompanyManager.GetPaged(param);
            r.data.total = pg.dataCount; ;
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                r.data.rows = (from item in pg.data
                               select new XUniformCompanyDto
                               {
                                   Id = item.Id,
                                   SchoolId = item.SchoolId,
                                   SchoolName = item.SchoolName,
                                   Brand = item.Brand,
                                   Name = item.Name,
                                   OriginAddress = item.OriginAddress,
                                   Parameter = item.Parameter,
                                   Price = item.Price,
                                   Producer = item.Producer,
                                   PurchaseDemand = item.PurchaseDemand,
                                   SecurityLevel = item.SecurityLevel,
                                   Sex = item.Sex,
                                   SizePath = item.SizePath,
                                   Sort = item.Sort,
                                   StandardNum = item.StandardNum,
                                   Uniformtype = item.Uniformtype,
                                   UnitId = item.UnitId,
                                   UnitName = item.UnitName,
                                   UseStatuz = item.UseStatuz,
                                   ModifyTime = item.ModifyTime, //  == null ? item.CreateTime : item.ModifyTime,
                                   LastSubmitTime = item.LastSubmitTime
                               }).ToList();
            }
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                //使用状态
                List<dropdownModel> dropStatuzUse = new List<dropdownModel>();
                var listStatuz = EnumExtensions.EnumToList<StatuzEnum>();
                if (listStatuz != null)
                {
                    foreach (var item in listStatuz)
                    {
                        dropStatuzUse.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                    }
                }

                //加载单位下拉列表
                List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                var listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                if (listSchool != null)
                {
                    foreach (var item in listSchool)
                    {
                        dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                    }
                }
                r.data.other = new { StatuzUseList = dropStatuzUse, SchoolList = dropdownSchool };
            }
            return r;
        }


        /// <summary>
        /// 校服采购-校服管理-根据Id获取详情（供应商）
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("geteditbyid")]
        public async Task<Result<XUniformCompanyDto>> GetEditById(long id)
        {
            Result<XUniformCompanyDto> r = new Result<XUniformCompanyDto>();
            IEnumerable<object> listAttachment = null;
            IEnumerable<object> listSizeArr = null;
            if (id > 0)
            {
                XUniformCompany m = await ixuniformCompanyManager.QueryById(id);
                if (m != null)
                {
                    r.flag = 1;
                    r.msg = "查询成功";
                    r.data.rows = mapper.Map<XUniformCompanyDto>(m);
                    var entityUnit = await ipunitManager.QueryById((object)m.SchoolId);
                    if (entityUnit != null)
                    {
                        r.data.rows.SchoolName = entityUnit.Name;
                    }
                    //获取尺码集合
                    var listSize = await ixuniformCompanySizeManager.Find(a => a.UniformCompanyId == id && a.IsDeleted == false);
                    if (listSize != null && listSize.Count > 0)
                    {
                        var listTemp = (from item in listSize
                                        orderby item.Sort
                                        select new
                                        {
                                            Id = item.Id,
                                            StandardName = item.StandardName,
                                            Sort = item.Sort,
                                            Memo = item.Memo
                                        });
                        listSizeArr = listTemp;
                    }
                    var list = await ibattachmentManager.Find(f => f.ObjectId == m.Id && f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.Create.ObjToInt());
                    if (list != null && list.Count > 0)
                    {
                        var listTemp = (from item in list
                                        select new
                                        {
                                            Id = item.Id,
                                            Title = item.Title,
                                            Path = item.Path,
                                            Width = item.Width,
                                            Height = item.Height,
                                            DocType = item.DocType,
                                            IsDefault = item.IsDefault,
                                            Remark = item.Remark,
                                            FileCategory = item.FileCategory,
                                            Ext = item.Ext,
                                        });
                        listAttachment = listTemp;
                    }
                }
            }
            //使用单位、种类、品名、品牌、单位、生产厂商、产地、安全登记、
            List<dropdownModel> dropdownSchool = new List<dropdownModel>();
            List<dropdownModel> dropdownUniformtype = new List<dropdownModel>();
            List<dropdownModel> dropdownName = new List<dropdownModel>();
            List<dropdownModel> dropdownBrand = new List<dropdownModel>();
            List<dropdownModel> dropdownProducer = new List<dropdownModel>();
            List<dropdownModel> dropdownOriginAddress = new List<dropdownModel>();
            List<dropdownModel> dropdownSecurityLevel = new List<dropdownModel>();
            List<dropdownModel> dropdownUnitName = new List<dropdownModel>();
            List<dropdownModel> dropdownPurchaseDemand = new List<dropdownModel>();
            r.flag = 1;
            r.msg = "查询成功";
            //加载最后一条录入的数据
            var listUniform = await ixuniformCompanyManager.Find(m => m.UnitId == user.UnitId && m.IsDeleted == false);
            if (listUniform != null && listUniform.Count > 0)
            {
                var uniformtypeList = listUniform.Where(m => !string.IsNullOrEmpty(m.Uniformtype)).GroupBy(m => m.Uniformtype).Select(m => m.FirstOrDefault().Uniformtype).ToList();
                if (uniformtypeList != null)
                {
                    foreach (var item in uniformtypeList)
                    {
                        dropdownUniformtype.Add(new dropdownModel() { value = item, label = item });
                    }
                }
                var nameList = listUniform.Where(m => !string.IsNullOrEmpty(m.Name)).GroupBy(m => m.Name).Select(m => m.FirstOrDefault().Name).ToList();
                if (nameList != null)
                {
                    foreach (var item in nameList)
                    {
                        dropdownName.Add(new dropdownModel() { value = item, label = item });
                    }
                }
                var brandList = listUniform.Where(m => !string.IsNullOrEmpty(m.Brand)).GroupBy(m => m.Brand).Select(m => m.FirstOrDefault().Brand).ToList();
                if (brandList != null)
                {
                    foreach (var item in brandList)
                    {
                        dropdownBrand.Add(new dropdownModel() { value = item, label = item });
                    }
                }

                var producerList = listUniform.Where(m => !string.IsNullOrEmpty(m.Producer)).GroupBy(m => m.Producer).Select(m => m.FirstOrDefault().Producer).ToList();
                if (producerList != null)
                {
                    foreach (var item in producerList)
                    {
                        dropdownProducer.Add(new dropdownModel() { value = item, label = item });
                    }
                }
                var originAddressList = listUniform.Where(m => !string.IsNullOrEmpty(m.OriginAddress)).GroupBy(m => m.OriginAddress).Select(m => m.FirstOrDefault().OriginAddress).ToList();
                if (originAddressList != null)
                {
                    foreach (var item in originAddressList)
                    {
                        dropdownOriginAddress.Add(new dropdownModel() { value = item, label = item });
                    }
                }
                var securityLevelList = listUniform.Where(m => !string.IsNullOrEmpty(m.SecurityLevel)).GroupBy(m => m.SecurityLevel).Select(m => m.FirstOrDefault().SecurityLevel).ToList();
                if (securityLevelList != null)
                {
                    foreach (var item in securityLevelList)
                    {
                        dropdownSecurityLevel.Add(new dropdownModel() { value = item, label = item });
                    }
                }
            }

            //加载单位下拉列表
            var listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
            if (listSchool != null)
            {
                foreach (var item in listSchool)
                {
                    dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                }
            }
            var listUnitName = await ibdictionaryManager.Find(m => m.TypeCode == DictionaryTypeCodeEnum.UniformUnitName.ObjToInt().ToString() && m.IsDeleted == false);
            if (listUnitName != null)
            {
                foreach (var item in listUnitName)
                {
                    dropdownUnitName.Add(new dropdownModel() { value = item.DicName, label = item.DicName });
                }
            }
            var listpurchaseDemand= await ibdictionaryManager.Find(m => m.TypeCode == DictionaryTypeCodeEnum.PurchaseDemand.ObjToInt().ToString() && m.IsDeleted == false);
            if (listpurchaseDemand != null)
            {
                foreach (var item in listpurchaseDemand)
                {
                    dropdownPurchaseDemand.Add(new dropdownModel() { value = item.DicValue, label = item.DicName });
                }
            }
            r.data.other = new
            {
                SchoolList = dropdownSchool,
                UniformtypeList = dropdownUniformtype,
                NameList = dropdownName,
                BrandList = dropdownBrand,
                ProducerList = dropdownProducer,
                OriginAddressList = dropdownOriginAddress,
                SecurityLevelList = dropdownSecurityLevel,
                UnitNameList = dropdownUnitName,
                PurchaseDemandList = dropdownPurchaseDemand,
                UniformSizeList = listSizeArr,
                AttachmentList = listAttachment
            };
            return r;
        }


        [HttpGet]
        [Route("getbyid")]
        [AllowAnonymous]
        public async Task<Result<XUniformCompanyDto>> GetById(long id)
        {
            Result<XUniformCompanyDto> r = new Result<XUniformCompanyDto>();
            IEnumerable<object> listAttachment = null;
            IEnumerable<object> listSizeArr = null;
            if (id > 0)
            {
                XUniformCompany m = await ixuniformCompanyManager.QueryById(id);
                if (m != null)
                {
                    r.flag = 1;
                    r.msg = "查询成功";
                    r.data.rows = mapper.Map<XUniformCompanyDto>(m);
                    var entityUnit = await ipunitManager.QueryById((object)m.SchoolId);
                    if (entityUnit != null)
                    {
                        r.data.rows.SchoolName = entityUnit.Name;
                    }
                    //获取尺码集合
                    var listSize = await ixuniformCompanySizeManager.Find(a => a.UniformCompanyId == id && a.IsDeleted == false);
                    if (listSize != null && listSize.Count > 0)
                    {
                        var listTemp = (from item in listSize
                                        orderby item.Sort
                                        select new
                                        {
                                            Id = item.Id,
                                            StandardName = item.StandardName,
                                            Sort = item.Sort,
                                            Memo = item.Memo
                                        });
                        listSizeArr = listTemp;
                    }
                    var list = await ibattachmentManager.Find(f => f.ObjectId == m.Id && f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.Create.ObjToInt());
                    if (list != null && list.Count > 0)
                    {
                        var listTemp = (from item in list
                                        select new
                                        {
                                            Id = item.Id,
                                            Title = item.Title,
                                            Path = item.Path,
                                            Width = item.Width,
                                            Height = item.Height,
                                            DocType = item.DocType,
                                            IsDefault = item.IsDefault,
                                            Remark = item.Remark,
                                            FileCategory = item.FileCategory,
                                            Ext = item.Ext,
                                        });
                        listAttachment = listTemp;
                    }
                }
            }
            r.flag = 1;
            r.msg = "查询成功";

            r.data.other = new
            {
                UniformSizeList = listSizeArr,
                AttachmentList = listAttachment
            };
            return r;
        }
        /// <summary>
        /// 校服采购-校服管理-新增（供应商）
        /// </summary>
        /// <param name="model">XUniformListDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("add")]
        public async Task<Result<long>> Add([FromBody] XUniformCompanyDto model)
        {
            model.UnitId = user.UnitId;
            model.CreateId = user.UserId;
            model.CreateBy = user.UserName;
            model.Id = 0;
            return await ixuniformCompanyManager.InsertUpdate(model);
        }
        /// <summary>
        /// 校服采购-校服管理-修改（供应商）
        /// </summary>
        /// <param name="model">XUniformListDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("edit")]
        public async Task<Result<long>> Edit([FromBody] XUniformCompanyDto model)
        {
            Result r = new Result();
            model.UnitId = user.UnitId;
            model.CreateId = user.UserId;
            model.CreateBy = user.UserName;
            if (model.Id <= 0)
            {
                return baseFailed<long>("非法操作，请从页面点击操作。");
            }
            return await ixuniformCompanyManager.InsertUpdate(model);
        }

        /// <summary>
        /// 方案选用-复制
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpPut]
        [Route("savecopy")]
        public async Task<Result<long>> Copy(long id)
        {
            var model = new XUniformCompanyDto();
            model.CreateId=user.UserId;
            model.UnitId = user.UnitId;
            model.Id= id;
            return await ixuniformCompanyManager.SaveCopy(model);
        }

        /// <summary>
        /// 校服采购- 校服管理- 根据Id删除数据（供应商）
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("deletebyid")]
        public async Task<Result> DeleteById(long id)
        {
            Result r = new Result();
            XUniformCompanyDto model = new XUniformCompanyDto();
            model.UnitId = user.UnitId;
            model.CreateId = user.UserId;
            model.CreateBy = user.UserName;
            model.Id = id;
            r = await ixuniformCompanyManager.FakeDeleteById(model);
            return r;
        }

        /// <summary>
        /// 校服管理-校服录入-删除附件
        /// </summary>
        /// <param name="id">校服表Id</param>
        /// <param name="attid">附件Id</param>
        /// <returns></returns>
        [HttpPut]
        [Route("delattachmentbyid")]
        public async Task<Result> DeleteAttachmentById(long id, long attid)
        {
            Result r = new Result();
            var model = new XUniformCompanyDto();
            model.Id = id;
            model.AttachmentId = attid;
            model.UnitId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformCompanyManager.UpdateAttachmentDelete(model);
            return r;
        }

        /// <summary>
        /// 校服管理-校服录入-删除尺码
        /// </summary>
        /// <param name="id">校服表Id</param>
        /// <param name="sizeid">尺码表id</param>
        /// <returns></returns>
        [HttpPut]
        [Route("delsizebyid")]
        public async Task<Result> DeleteSizeById(long id, long sizeid)
        {
            Result r = new Result();
            var model = new XUniformCompanyDto();
            model.Id = id;
            model.UniformCompanySizeId = sizeid;
            model.UnitId = user.UnitId;
            model.CreateId = user.UserId;
            r = await ixuniformCompanyManager.DeleteSizeById(model);
            return r;
        }


        /// <summary>
        /// 校服采购- 校服管理- 设置状态（供应商）
        /// </summary>
        /// <param name="ids">Id集合逗号分隔</param>
        /// <param name="statuz">更新的状态</param>
        /// <returns></returns>
        [HttpPut]
        [Route("setsattuz")]
        public async Task<Result> SetSattuz(string ids, int statuz)
        {
            Result r = new Result();
            r = await ixuniformCompanyManager.SetStatuzByIds(ids, statuz, user.UnitId);
            return r;
        }


        /// <summary>
        /// 校服采购- 校服管理- 提交审核（供应商）
        /// </summary>
        /// <param name="ids">Id集合逗号分隔</param>
        /// <returns></returns>
        [HttpPut]
        [Route("submit")]
        public async Task<Result> Submit(string ids)
        {
            return await ixuniformCompanyManager.SubmitByIds(ids, user.UnitId);
        }


    }
}
