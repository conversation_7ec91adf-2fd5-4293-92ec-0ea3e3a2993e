﻿using Grpc.Core;
using Hyun.Core.Common.DB;
using Hyun.Core.Model;
using NetTaste;
using NPOI.SS.UserModel;
using Pipelines.Sockets.Unofficial.Arenas;
using StackExchange.Redis;
using System.Dynamic;
using System.Reflection;
using static NPOI.HSSF.Util.HSSFColor;
using System.Security.Principal;
using System.Xml.Linq;
using NPOI.SS.Formula.PTG;
using Org.BouncyCastle.Utilities.Zlib;
using Hyun.Core.Common.HttpContextUser;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace Hyun.Core.Services
{

    ///<summary>
    ///OUserThirdUser方法
    ///</summary>
    public class OUserThirdUserServices : BaseServices<OUserThirdUser>, IOUserThirdUserServices
    {

        private readonly IBUserActionLogServices userActionLogManager;
        private readonly IBUserActionStaticServices userActionStaticManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly ISysUserRoleServices sysUserRoleManager;
        private readonly IBAddressServices addressManager;

        public OUserThirdUserServices(IBUserActionLogServices _userActionLogManager, IBUserActionStaticServices _userActionStaticManager, IBDictionaryServices _dictionaryManager, ISysUserRoleServices _sysUserRoleManager, IBAddressServices _addressManager)
        {
            userActionLogManager = _userActionLogManager;
            userActionStaticManager = _userActionStaticManager;
            dictionaryManager = _dictionaryManager;
            addressManager = _addressManager;
            sysUserRoleManager = _sysUserRoleManager;
        }

        #region 查询数据
        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        public async Task<OUserThirdUser> GetById(long id)
        {
            return await base.QueryById(id);
        }

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        //<used>1</used>
        public async Task<List<OUserThirdUser>> Find(Expression<Func<OUserThirdUser, bool>> expression)
        {
            return await base.Query(expression);
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">OUserThirdUserParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<OUserThirdUser>> GetPaged(OUserThirdUserParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            return await base.QueryPage(expression, param.pageIndex, param.pageSize, orderByFields);
        }
        #endregion

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thirdUserId"></param>
        /// <param name="thirdUnitId"></param>
        /// <param name="userIP"></param>
        /// <returns></returns>
        public async Task<Result> CheckIsBinder(string thirdUserId,string thirdUnitId,string userIP)
        {
            Result r = new Result();
            r.data.headers = 0;
            r.data.footer = 0;
            
            if (thirdUnitId.Equals("0"))
            {
                //非绑定账号
                OUserThirdUser obj = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUserId == thirdUserId).FirstAsync();
                if (obj != null)
                {
                    r.flag = 1;
                    r.msg = "";
                    r.data.headers = obj.UnitId;
                    r.data.footer = obj.UserId;
                }
                else
                {
                    obj = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUnitId == thirdUnitId).FirstAsync();
                    if (obj != null)
                    {
                        r.data.headers = obj.UnitId;
                    }
                    r.flag = 0;
                    r.msg = "未绑定账号";
                }
                return r;
            }

            //存在账号单位一致，直接登录
            OUserThirdUser o = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUserId == thirdUserId && f.ThirdUnitId == thirdUnitId).FirstAsync();
            if (o != null)
            {
                SysUserExtension puser =await this.Db.Queryable<SysUserExtension>().Where(f => f.Id == o.UserId).FirstAsync();
                if (puser != null)
                {
                    await userActionLogManager.Add(new BUserActionLog()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UserId = o.UserId,
                        AccountName = puser.Name,
                        UserIp = userIP,
                        Type = 1,
                        CreateTime = DateTime.Now,
                        Statuz = 1
                    });

                    await userActionStaticManager.Login(o.UserId);

                    r.data.headers = o.UnitId;
                    r.data.footer = o.UserId;

                    r.flag = 1;
                    r.msg = "";
                }
            }
            else
            {
                o = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUnitId == thirdUnitId).FirstAsync();
                if (o != null)
                {
                    r.data.headers = o.UnitId;
                }
                r.flag = 0;
                r.msg = "未绑定账号";
            }

            return r;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thirdUserId"></param>
        /// <param name="thirdUnitId"></param>
        /// <param name="unitId"></param>
        /// <param name="unitType"></param>
        /// <param name="realName"></param>
        /// <param name="mobile"></param>
        /// <param name="roleids"></param>
        /// <param name="loginName"></param>
        /// <param name="unitName"></param>
        /// <param name="schoolStage">以cn_开头，表示传的是中文</param>
        /// <param name="staffNumber"></param>
        /// <param name="unitcode"></param>
        /// <param name="county"></param>
        /// <param name="address"></param>
        /// <param name="userIp"></param>
        /// <returns></returns>
        public async Task<Result> CreateUserInfo(string thirdUserId, string thirdUnitId, long unitId,int unitType,string realName,string mobile,string roleids,string loginName,string unitName,string schoolStage,string staffNumber,string unitcode,string county,string address,string userIp,long userId,int flag)
        {
            Result r = new Result();
            if (schoolStage.Contains("cn_"))
            {
                schoolStage = schoolStage.Replace("cn_", "");
                BDictionary dic = await this.Db.Queryable<BDictionary>().Where(f => f.TypeCode == "1101" && f.DicName == schoolStage).FirstAsync();
                if (dic != null)
                {
                    schoolStage = dic.DicValue;
                }
                else
                {
                    int maxDicValue = 0;
                    int maxSequence = 0;

                    maxDicValue = await this.Db.Queryable<BDictionary>().Where(f => f.TypeCode == "1101").MaxAsync(f => int.Parse(f.DicValue));
                    maxSequence = await this.Db.Queryable<BDictionary>().Where(f => f.TypeCode == "1101").MaxAsync(f => f.Sequence.Value);
                    maxDicValue += 1;
                    maxSequence += 1;

                    await dictionaryManager.Add(new BDictionary()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        TypeCode = "1101",
                        TypeName = "学段",
                        DicName = "schoolStage",
                        DicValue = maxDicValue.ToString(),
                        Memo ="",
                        Sequence = maxSequence,
                        Statuz = 1

                    });
                    schoolStage = maxDicValue.ToString();
                }
                if (string.IsNullOrEmpty(mobile))
                {
                    mobile = "";
                }
                if (string.IsNullOrEmpty(realName))
                {
                    realName = loginName;
                }
                if (string.IsNullOrEmpty(unitcode))
                {
                    unitcode = BaseDBConfig.GetYitterId().ToString();
                }
                if(userId > 0 && flag == -99)
                {
                    if (!string.IsNullOrEmpty(unitName))
                    {
                        await this.Db.Updateable<PUnit>().SetColumns(f => new PUnit() { Name = unitName }).Where(f => f.Id == unitId).ExecuteCommandAsync();
                    }
                    if (!string.IsNullOrEmpty(address))
                    {
                        await this.Db.Updateable<PUnit>().SetColumns(f => new PUnit() { Address = address }).Where(f => f.Id == unitId).ExecuteCommandAsync();
                    }

                    if(mobile.Length != 11)
                    {
                        await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { Name = realName }).Where(f => f.Id == userId).ExecuteCommandAsync();
                    }
                    else
                    {
                        await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { Name = realName,Mobile = mobile }).Where(f => f.Id == userId).ExecuteCommandAsync();
                    }

                    if (!string.IsNullOrEmpty(staffNumber))
                    {
                        await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { StaffNumber = staffNumber }).Where(f => f.Id == userId).ExecuteCommandAsync();
                    }
                    if (mobile.Length == 11)
                    {
                        await this.Db.Updateable<SysUserInfo>().SetColumns(f => new SysUserInfo() { Mobile = mobile }).Where(f => f.UserExtensionId == userId).ExecuteCommandAsync();
                    }
                    long UnitPid = 0;
                    long AreaId = 0;

                    int bInsertUnit = 0;
                    int bInsertUser = 0;
                    int bNeedUpdatePower = 0;

                    OUserThirdUser thirdUnitUser = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUserId == thirdUserId && f.ThirdUnitId == thirdUnitId).FirstAsync();
                    OUserThirdUser thirdUser = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUserId == thirdUserId).FirstAsync();
                    if (thirdUnitUser != null)
                    {
                        await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { Name = realName }).Where(f => f.Id == userId).ExecuteCommandAsync();
                        r.flag = 1;
                        r.msg = "";
                        return r;
                    }
                    else if(thirdUser != null)
                    {
                        OUserThirdUser thirdUnit = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUnitId == thirdUnitId).FirstAsync();

                        if (thirdUnit != null)
                        {
                            userId = thirdUser.UserId;
                            unitId = thirdUnit.UnitId;
                            if(unitId > 0)
                            {
                                OUserThirdUser o = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUserId == thirdUserId && f.UnitId == unitId).FirstAsync();
                                if (o == null)
                                {
                                    await this.Db.Updateable<OUserThirdUser>().SetColumns(f => new OUserThirdUser() { UnitId = unitId, ThirdUnitId = thirdUnitId }).Where(f => f.ThirdUserId == thirdUserId).ExecuteCommandAsync();
                                    await this.Db.Deleteable<SysUserRole>().Where(f => f.UserId == userId).ExecuteCommandAsync();
                                    bNeedUpdatePower = 1;
                                }

                                await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { Name = realName, UnitId = unitId }).Where(f => f.Id == userId).ExecuteCommandAsync();
                                await this.Db.Updateable<SysUserInfo>().SetColumns(f => new SysUserInfo() { LoginName = loginName }).Where(f => f.UserExtensionId == userId).ExecuteCommandAsync();
                            }
                            else
                            {
                                if(unitType == 4)
                                {
                                    UnitPid = 0;
                                    AreaId = 0;
                                    bInsertUnit = 1;
                                    //存在用户，但是不存在单位，表示原用户更换了单位
                                    bInsertUser = 0;
                                }
                                else if(unitType == 3)
                                {
                                    PUnit u = await this.Db.Queryable<PUnit>().FirstAsync(f => f.UnitType == 2);
                                    if (u != null)
                                    {
                                        UnitPid = u.Id;
                                        AreaId = u.AreaId;
                                        bInsertUnit = 1;
                                        bInsertUser = 0;
                                    }
                                }
                                else
                                {
                                    PUnit u = await this.Db.Queryable<PUnit>().FirstAsync(f => f.UnitType == 2);
                                    if (u != null)
                                    {
                                        unitId = u.Id;
                                        bInsertUser = 1;
                                    }
                                    
                                }
                            }
                        }
                    }
                    else
                    {
                        OUserThirdUser thirdUnit = await this.Db.Queryable<OUserThirdUser>().Where(f => f.ThirdUnitId == thirdUnitId).FirstAsync();
                        if (thirdUnit != null)
                        {
                            unitId = thirdUnit.UnitId;
                            if(unitId <= 0)
                            {
                                if(unitType == 4)
                                {
                                    UnitPid = 0;
                                    AreaId = 0;
                                    bInsertUnit = 1;
                                    bInsertUser = 0;
                                }
                                else if(unitType == 3)
                                {
                                    PUnit u = await this.Db.Queryable<PUnit>().FirstAsync(f => f.UnitType == 2);
                                    if (u != null)
                                    {
                                        UnitPid = u.Id;
                                        AreaId = u.AreaId;
                                        bInsertUnit = 1;
                                        bInsertUser = 1;
                                    }                                  
                                }
                                else
                                {
                                    PUnit u = await this.Db.Queryable<PUnit>().FirstAsync(f => f.UnitType == 2);
                                    if (u != null)
                                    {
                                        unitId = u.Id;
                                        bInsertUser = 1;
                                    }
                                    //如果此时另外一个区县用户登录，则阻止该用户登录，否则会造成，任何区县都可以看该区县数据
                                    //需创建新的区县，并向o_UserThirdUser插入一条unitid 与ThirdUnitId关联的数据
                                    thirdUnit = await this.Db.Queryable<OUserThirdUser>().Where(f => f.UnitId == unitId && f.ThirdUnitId != thirdUnitId).FirstAsync();
                                    if (thirdUnit != null)
                                    {
                                        r.flag = 0;
                                        r.msg = $"无法使用平台，请联系客户人员添加一条数据【{thirdUnitId}】，然后再使用平台";
                                        return r;
                                    }

                                }
                            }
                            else
                            {
                                bInsertUser = 1;
                            }
                        }
                    }

                    if(unitType == 3 && UnitPid <= 0)
                    {
                        r.flag = 0;
                        r.msg = "未初始化区级单位，分配权限失败，请稍后再试。";
                        return r;
                    }

                    if(bInsertUnit == 1)
                    {
                        //根据名称二次判断，防止以前存在单位
                        PUnit oUnit = await this.Db.Queryable<PUnit>().Where(f => f.Name == unitName && f.PId == UnitPid).FirstAsync();
                        if (oUnit != null) 
                        {
                            unitId = oUnit.Id;
                            await this.Db.Updateable<PUnit>().SetColumns(f => new PUnit() { Statuz = 1, ThirdUnitId = thirdUnitId }).Where(f => f.Id == unitId).ExecuteCommandAsync();
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(unitcode))
                            {
                                unitcode = "UX" + DateTime.Now.ToString("yyyyMMddhhss");
                            }
                            unitId = await this.Db.Insertable(new PUnit() {Id = BaseDBConfig.GetYitterId(), PId = UnitPid, UnitType = unitType, IndustryId = 1, Name = unitName, RegTime = DateTime.Now, Statuz = 1, VipGrade = 0, UserId = 0, AreaId = AreaId, Code = unitcode, EmployeeNum = 0, Sort = 10000, Address = address, ThirdUnitId = thirdUnitId, SourceType = 2, SubjectNature = 0 }).ExecuteCommandAsync();
                            //调用存储过程 exec USP_B_Address_InitalizationUnit @UnitId
                            await addressManager.InitalizationUnit(unitId);

                            dic = await this.Db.Queryable<BDictionary>().Where(f => f.TypeCode == "1101" && f.DicValue == schoolStage).FirstAsync();
                            if (dic != null)
                            {
                                await this.Db.Insertable(new PSchoolExtension() { Id = BaseDBConfig.GetYitterId(), UnitId = unitId, ClassNum = 0, StudentNum = 0, TeacherNum = 0, FloorArea = 0, BuildArea = 0, SchoolNature = 1, SchoolStage = int.Parse(schoolStage), IsLock = 0}).ExecuteCommandAsync();
                            }

                            //插入单位（更换单位），需删除角色信息
                            if (userId > 0)
                            {
                                await this.Db.Deleteable<SysUserRole>().Where(f => f.UserId == userId).ExecuteCommandAsync();
                                bNeedUpdatePower = 1;
                            }
                        }
                    }

                    r.flag = 1;
                    r.msg = "初始化用户信息成功。";
                    if(bInsertUser == 1)
                    {
                        //如果存在手机号码相同的，且未关联账号的，且单位相同的，合并账号
                        int isFirstExit = 0;
                        SysUserExtension oUser = await this.Db.Queryable<SysUserExtension>().Where(f => f.Mobile == mobile && f.UnitId == unitId).FirstAsync();
                        if(mobile.Length == 11 && oUser != null)
                        {
                            userId = oUser.Id;
                            OUserThirdUser oThirdUser = await this.Db.Queryable<OUserThirdUser>().Where(f => f.UserId == userId).FirstAsync();
                            if(oThirdUser == null)
                            {
                                isFirstExit = 1;
                            }
                        }
                        if(isFirstExit == 0)
                        {
                            userId = await this.Db.Insertable(new SysUserExtension() { Id = BaseDBConfig.GetYitterId(), UnitId = unitId, StaffNumber = staffNumber, Name = realName, Mobile = mobile, UserId = 0, RegTime = DateTime.Now, Statuz = 1, UserType = 1, AdministratorType  = 2}).ExecuteCommandAsync();

                            int i = 0;
                            string loginNameTemp = loginName;

                            List<SysUserInfo> listAccount = await this.Db.Queryable<SysUserInfo>().Where(f => f.LoginName == loginName).ToListAsync();
                            foreach(SysUserInfo account in listAccount)
                            {
                                i += 1;
                                loginNameTemp = loginName + i.ToString();
                            }
                            await this.Db.Insertable(new SysUserInfo() { UserExtensionId = userId, LoginName = loginNameTemp, Mobile = mobile, LoginPWD = "", NickName = "", CreateId = 0, CreateTime = DateTime.Now, Statuz = 1 }).ExecuteCommandAsync();
                        }

                        await this.Db.Insertable(new OUserThirdUser() { UserId = userId, ThirdUserId = thirdUserId, UnitId = unitId, ThirdUnitId = thirdUnitId, RegDate = DateTime.Now}).ExecuteCommandAsync();
                    }
                    else
                    {
                        await this.Db.Updateable<OUserThirdUser>().SetColumns(f => new OUserThirdUser() { UnitId = unitId,ThirdUnitId = thirdUnitId }).Where(f => f.ThirdUserId == thirdUserId).ExecuteCommandAsync();
                        await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { UnitId = unitId }).Where(f => f.Id == userId).ExecuteCommandAsync();
                    }

                    if(bInsertUnit == 1)
                    {
                        await this.Db.Updateable<OUserThirdUser>().SetColumns(f => new OUserThirdUser() { UnitId = unitId, ThirdUnitId = thirdUnitId }).Where(f => f.ThirdUserId == thirdUserId).ExecuteCommandAsync();
                        bNeedUpdatePower = 1;
                    }

                    if(bNeedUpdatePower == 1)
                    {
                        if (roleids.Equals("0_1"))
                        {
                            await this.Db.Insertable(new SysUserRole() { Id = BaseDBConfig.GetYitterId(), UserId = userId, RoleId = 0}).ExecuteCommandAsync();
                            await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { AdministratorType = 1 }).Where(f => f.Id == userId).ExecuteCommandAsync();
                        }
                        else if (roleids.Contains("0_0"))
                        {
                            await this.Db.Insertable(new SysUserRole() { Id = BaseDBConfig.GetYitterId(), UserId = userId, RoleId = 0 }).ExecuteCommandAsync();
                            await this.Db.Updateable<SysUserExtension>().SetColumns(f => new SysUserExtension() { AdministratorType = 0 }).Where(f => f.Id == userId).ExecuteCommandAsync();
                        }
                        else
                        {
                            //初始化用户账号，如果白名单中有账号角色，且单位类型一致，按白名单判断
                            long UserThirdAllowId = 0;
                            long CreateUnitId = 0;
                            OUserThirdAllow allow = await this.Db.Queryable<OUserThirdAllow>().Where(f => f.Mobile == mobile && f.UnitTypeId == unitType).FirstAsync();
                            if (allow != null)
                            {
                                UserThirdAllowId = allow.Id;
                                roleids = allow.RoleIds;
                                CreateUnitId = allow.UnitId;
                            }
                            //如果是单位或企业导入的数据，只能对本单位有效
                            PUnit unit = await this.Db.Queryable<PUnit>().Where(f => f.Id == CreateUnitId && (f.UnitType == 3 || f.UnitType == 4)).FirstAsync();
                            if (unit != null)
                            {
                                UserThirdAllowId = 0;
                                roleids = "";
                                allow = await this.Db.Queryable<OUserThirdAllow>().Where(f => f.Mobile == mobile && f.UnitTypeId == unitType && f.UnitId == unitId).FirstAsync();
                                if (allow != null)
                                {
                                    UserThirdAllowId = allow.Id;
                                    roleids = allow.RoleIds;
                                }

                            }

                            if(UserThirdAllowId > 0 && roleids.Length > 0)
                            {
                                List<int> listRole = roleids.Split(',').Select(int.Parse).ToList();
                                List<SysUserRole> listUserInRole = new List<SysUserRole>();
                                foreach (int roleid in listRole)
                                {
                                    if (roleid > 0)
                                    {
                                        listUserInRole.Add(new SysUserRole()
                                        {
                                            Id = BaseDBConfig.GetYitterId(),
                                            UserId = userId,
                                            RoleId = roleid
                                        });
                                    }
                                   
                                }
                                if (listUserInRole.Count > 0)
                                {
                                    await sysUserRoleManager.Add(listUserInRole);
                                }
                                await this.Db.Updateable<OUserThirdAllow>().SetColumns(f => new OUserThirdAllow() { RelationUserId = userId }).Where(f => f.Id == UserThirdAllowId).ExecuteCommandAsync();
                            }
                            else if(roleids.Length > 0)
                            {
                                //插入程序默认角色
                                List<int> listRole = roleids.Split(',').Select(int.Parse).ToList();
                                List<SysUserRole> listUserInRole = new List<SysUserRole>();
                                foreach (int roleid in listRole)
                                {
                                    if (roleid > 0)
                                    {
                                        listUserInRole.Add(new SysUserRole()
                                        {
                                            Id = BaseDBConfig.GetYitterId(),
                                            UserId = userId,
                                            RoleId = roleid
                                        });
                                    }

                                }
                                if (listUserInRole.Count > 0)
                                {
                                    await sysUserRoleManager.Add(listUserInRole);
                                }
                            }
                            else
                            {
                                r.msg = "您目前还没有权限使用本功能，请联系本单位管理员或客服给您授权。您的相关信息是：";
                                if(realName.Length > 0)
                                {
                                    r.msg += $"姓名  {realName}";
                                }
                                if (unitName.Length > 0)
                                {
                                    r.msg += $"单位  {unitName}";
                                }
                                if(mobile.Length > 0)
                                {
                                    r.msg += $"电话  {mobile}";
                                }
                                r.flag = 2;
                            }
                        }
                    }

                    string name = "";
                    SysUserExtension userAync = await this.Db.Queryable<SysUserExtension>().Where(f => f.Id == userId).FirstAsync();
                    if (userAync != null)
                    {
                        name = userAync.Name;
                    }
                    await this.Db.Insertable(new BUserActionLog() { Id = BaseDBConfig.GetYitterId(), UserId = userId, AccountName = name, UserIp = userIp, Type = 1, CreateTime = DateTime.Now, Statuz = 1}).ExecuteCommandAsync();

                    r.flag = 1;
                    return r;
                }
            }
            return r;
        }

        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">OUserThirdUserParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<OUserThirdUser, bool>> ListFilter(OUserThirdUserParam param)
        {
            var expression = LinqExtensions.True<OUserThirdUser>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
            }
            return expression;
        }
        #endregion


    }
}

