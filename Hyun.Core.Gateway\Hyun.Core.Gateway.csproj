<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\build\common.targets" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>..\Hyun.Core.Gateway\Hyun.Core.Gateway.xml</DocumentationFile>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Extensions\ApiResponseHandler.cs" />
    <Compile Remove="Helper\HeaderDelegatingHandler.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="index.html" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="index.html" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.7" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.7" />
    <PackageReference Include="Ocelot" Version="24.0.1" />
    <PackageReference Include="Ocelot.Provider.Consul" Version="23.3.3" />
    <PackageReference Include="Ocelot.Provider.Polly" Version="23.3.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hyun.Core.Extensions\Hyun.Core.Extensions.csproj" />
    <ProjectReference Include="..\Hyun.Core.Model\Hyun.Core.Model.csproj" />
  </ItemGroup>

</Project>
