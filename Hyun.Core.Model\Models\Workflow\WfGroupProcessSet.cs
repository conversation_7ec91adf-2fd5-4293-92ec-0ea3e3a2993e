﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///分组设置表
    ///</summary>
    [SugarTable("wf_GroupProcessSet", "分组设置表")]
    public class WfGroupProcessSet : BaseEntity
    {

          public WfGroupProcessSet()
          {

          }

           /// <summary>
           ///流程Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ProcessId { get; set; }

           /// <summary>
           ///分组名称
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string GroupName { get; set; }

           /// <summary>
           ///分类编码
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? TypeCode { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;

        /// <summary>
        /// 2：字典表(B_Dictionary)下拉框组件，3：审批字典表(Wf_Dictionary)下拉框组件
        /// </summary>
        [SugarColumn(DefaultValue ="3")]
        public int TypeBox { get; set; }


    }


}

