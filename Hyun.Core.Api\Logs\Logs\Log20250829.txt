
Date：2025-08-29 11:19:53.068
LogLevel：Information
Message：Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.104
LogLevel：Information
Message：Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.105
LogLevel：Information
Message：Quartz Scheduler created
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.110
LogLevel：Information
Message：RAMJobStore initialized.
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.114
LogLevel：Information
Message：Quartz Scheduler 3.14.0.0 - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.120
LogLevel：Information
Message：Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.124
LogLevel：Information
Message：Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.215
LogLevel：Information
Message：Start Initialization Db Seed Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.220
LogLevel：Information
Message：Start QuartzJob Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.222
LogLevel：Information
Message：Start Consul Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.236
LogLevel：Information
Message：Start EventBus Service!
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.467
LogLevel：Information
Message：Now listening on: "http://[::]:9291"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.542
LogLevel：Information
Message：Application started. Press Ctrl+C to shut down.
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.543
LogLevel：Information
Message：Hosting environment: "Development"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:53.547
LogLevel：Information
Message：Content root path: "D:\工作管理\项目研发\维修平台\系统开发\程序\hyun.core\Hyun.Core.Api"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:54.922
LogLevel：Warning
Message：Error unprotecting the session cookie.
System.Security.Cryptography.CryptographicException: The key {34cab3f5-abde-410d-9fd9-b8446699002c} was not found in the key ring. For more information go to https://aka.ms/aspnet/dataprotectionwarning
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.UnprotectCore(Byte[] protectedData, Boolean allowOperationsOnRevokedKeys, UnprotectStatus& status)
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.Unprotect(Byte[] protectedData)
   at Microsoft.AspNetCore.Session.CookieProtection.Unprotect(IDataProtector protector, String protectedText, ILogger logger)
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:54.922
LogLevel：Warning
Message：Error unprotecting the session cookie.
System.Security.Cryptography.CryptographicException: The key {34cab3f5-abde-410d-9fd9-b8446699002c} was not found in the key ring. For more information go to https://aka.ms/aspnet/dataprotectionwarning
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.UnprotectCore(Byte[] protectedData, Boolean allowOperationsOnRevokedKeys, UnprotectStatus& status)
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.Unprotect(Byte[] protectedData)
   at Microsoft.AspNetCore.Session.CookieProtection.Unprotect(IDataProtector protector, String protectedText, ILogger logger)
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:55.089
LogLevel：Information
Message：HTTP "GET" "/favicon.ico" QueryString:"" Body:""  responded 404 in 110.4031 ms
----------------------------------------------------------------------------------------------------
Date：2025-08-29 11:19:55.089
LogLevel：Information
Message：HTTP "POST" "/api/Values" QueryString:"" Body:null  responded 404 in 147.8702 ms
----------------------------------------------------------------------------------------------------