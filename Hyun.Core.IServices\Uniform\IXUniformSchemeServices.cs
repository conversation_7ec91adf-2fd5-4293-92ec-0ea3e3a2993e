﻿using Hyun.Core.Model.Models.Uniform;


namespace Hyun.Core.IServices.Uniform
{

    ///<summary>
    ///XUniformScheme接口方法
    ///</summary>
    public interface IXUniformSchemeServices : IBaseServices<XUniformScheme>
    {
        /// <summary>
        /// 方案选用- 保存、发布
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result<XUniformScheme>> SavePublish(XUniformSchemeDto model);
        /// <summary>
        /// 方案选用-复制
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userid"></param>
        /// <returns></returns>
        Task<Result<long>> SaveCopy(long id, long userid);
        /// <summary>
        /// 方案选用-家长填报意见
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> SaveOpinion(XUniformSchemeOpinionDto model);
      
        /// <summary>
        /// 方案选用-附件删除
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> UpdateAttachmentDelete(XUniformSchemeDto model);
        /// <summary>
        /// 方案选用-删除（更新IsDelete = 1）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> UpdateDelete(XUniformSchemeDto model);
        /// <summary>
        /// 方案选用-单位发布撤销
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> PublishCancel(XUniformSchemeDto model);
        /// <summary>
        /// 方案选用-单位提交
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> FilingSubmit(XUniformSchemeDto model);
        /// <summary>
        /// 方案选用-区县备查
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> FilingConfirm(XUniformSchemeDto model);
        /// <summary>
        /// 方案选用-备案退回
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> FilingBackout(XUniformSchemeDto model);
        /// <summary>
        /// 方案选用-根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<XUniformScheme> GetById(long id);

        /// <summary>
        /// 方案选用-根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<XUniformScheme>> Find(Expression<Func<XUniformScheme, bool>> expression);

        /// <summary>
        /// 方案选用-适应装备平台查询方法
        /// </summary>
        /// <param name="param">XUniformSchemeParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<XUniformSchemeDto>> GetPaged(XUniformSchemeParam param);
        /// <summary>
        /// 方案选用-选用结果（家长）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformSchemeDto>> GetParentPaged(XUniformSchemeParam param);
        /// <summary>
        /// 方案选用-获取本次方案家长填写意见列表
        /// </summary>
        /// <param name="schemeid">选用方案Id</param>
        /// <param name="parentid">家长用户Id</param>
        /// <returns></returns>
        Task<List<XUniformSchemeOpinion>> GetOpinion(long schemeid, long parentid);
        /// <summary>
        /// 方案选用-获取意见详情列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformSchemeOpinionDto>> GetOpinionPaged(XUniformSchemeOpinionParam param);

    }
}

