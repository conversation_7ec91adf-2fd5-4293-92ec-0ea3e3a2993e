{"format": 1, "restore": {"D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Tasks\\Hyun.Core.Tasks.csproj": {}}, "projects": {"D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj", "projectName": "Hyun.Core.Common", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj"}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Serilog.Es\\Hyun.Core.Serilog.Es.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Serilog.Es\\Hyun.Core.Serilog.Es.csproj"}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Ocelot.Provider.Nacos\\Ocelot.Provider.Nacos.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Ocelot.Provider.Nacos\\Ocelot.Provider.Nacos.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"InitQ": {"target": "Package", "version": "[********, )"}, "Magicodes.IE.Excel": {"target": "Package", "version": "[*******, )"}, "Mapster": {"target": "Package", "version": "[7.4.0, )"}, "Mapster.Core": {"target": "Package", "version": "[1.2.1, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "MiniProfiler.Shared": {"target": "Package", "version": "[4.3.8, )"}, "PinYinConverterCore": {"target": "Package", "version": "[1.0.2, )"}, "RestSharp": {"target": "Package", "version": "[111.4.1, )"}, "Serilog": {"target": "Package", "version": "[4.0.1, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.2, )"}, "Serilog.Expressions": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[2.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "SixLabors.Fonts": {"target": "Package", "version": "[2.0.4, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.5, )"}, "SixLabors.ImageSharp.Drawing": {"target": "Package", "version": "[2.1.4, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.1, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.4.4, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}, "log4net": {"target": "Package", "version": "[2.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.IServices\\Hyun.Core.IServices.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.IServices\\Hyun.Core.IServices.csproj", "projectName": "Hyun.Core.IServices", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.IServices\\Hyun.Core.IServices.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.IServices\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj"}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj", "projectName": "Hyun.Core.Model", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\OpenIddict.SqlSugar.Models\\OpenIddict.SqlSugar.Models.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\OpenIddict.SqlSugar.Models\\OpenIddict.SqlSugar.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[12.0.0, )"}, "NPOI": {"target": "Package", "version": "[2.7.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Repository\\Hyun.Core.Repository.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Repository\\Hyun.Core.Repository.csproj", "projectName": "Hyun.Core.Repository", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Repository\\Hyun.Core.Repository.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Repository\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj"}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Model\\Hyun.Core.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MiniProfiler.AspNetCore.Mvc": {"target": "Package", "version": "[4.3.8, )"}, "MongoDB.Bson": {"target": "Package", "version": "[2.28.0, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.28.0, )"}, "MongoDB.Driver.Core": {"target": "Package", "version": "[2.28.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Serilog.Es\\Hyun.Core.Serilog.Es.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Serilog.Es\\Hyun.Core.Serilog.Es.csproj", "projectName": "Hyun.Core.Serilog.Es", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Serilog.Es\\Hyun.Core.Serilog.Es.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Serilog.Es\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[4.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Tasks\\Hyun.Core.Tasks.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Tasks\\Hyun.Core.Tasks.csproj", "projectName": "Hyun.Core.Tasks", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Tasks\\Hyun.Core.Tasks.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Tasks\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Common\\Hyun.Core.Common.csproj"}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.IServices\\Hyun.Core.IServices.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.IServices\\Hyun.Core.IServices.csproj"}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Repository\\Hyun.Core.Repository.csproj": {"projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Repository\\Hyun.Core.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Quartz": {"target": "Package", "version": "[3.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Ocelot.Provider.Nacos\\Ocelot.Provider.Nacos.csproj": {"version": "1.2.1", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Ocelot.Provider.Nacos\\Ocelot.Provider.Nacos.csproj", "projectName": "Ocelot.Provider.Nacos", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Ocelot.Provider.Nacos\\Ocelot.Provider.Nacos.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Ocelot.Provider.Nacos\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"EasyCaching.InMemory": {"target": "Package", "version": "[1.9.2, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Ocelot": {"target": "Package", "version": "[24.0.1, )"}, "nacos-sdk-csharp": {"target": "Package", "version": "[1.3.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\OpenIddict.SqlSugar.Models\\OpenIddict.SqlSugar.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\OpenIddict.SqlSugar.Models\\OpenIddict.SqlSugar.Models.csproj", "projectName": "OpenIddict.SqlSugar.Models", "projectPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\OpenIddict.SqlSugar.Models\\OpenIddict.SqlSugar.Models.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\OpenIddict.SqlSugar.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SqlSugarCore": {"target": "Package", "version": "[5.1.4.193, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}}}