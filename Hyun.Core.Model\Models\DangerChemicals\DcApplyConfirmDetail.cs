namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///仓管配货明细表
    ///</summary>
    [SugarTable("dc_ApplyConfirmDetail","仓管配货明细表")]
    public class DcApplyConfirmDetail : BaseEntity
    {

          public DcApplyConfirmDetail()
          {

          }

           /// <summary>
           ///物品申领Id
          /// </summary>
          public long ApplyId { get; set; }

           /// <summary>
           ///物品确认单Id
          /// </summary>
          public long ApplyConfirmId { get; set; }

           /// <summary>
           ///单位物品Id
          /// </summary>
          public long SchoolMaterialId { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///分类Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///规格型号Id
          /// </summary>
          public long SchoolMaterialModelId { get; set; }

           /// <summary>
           ///品牌Id
          /// </summary>
          public long SchoolMaterialBrandId { get; set; }

           /// <summary>
           ///设备名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///设备品牌
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Brand { get; set; }

           /// <summary>
           ///规格型号
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Model { get; set; }

           /// <summary>
           ///单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

           /// <summary>
           ///领用批次
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string TakeBatchNo { get; set; }

        /// <summary>
        ///领用数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Num { get; set; }

        /// <summary>
        ///累计归还/退回数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal BackNum { get; set; }

           /// <summary>
           ///状态（0：已保存；10：待生成领用单；12：已生成领用单（待确认领用）；14：申领人已确认领用；20：物品已发放 ）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///领用时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? RegDate { get; set; }

           /// <summary>
           ///代领人（用于物品分发）
          /// </summary>
          public long ReplaceCollarUserId { get; set; }

           /// <summary>
           ///仓管是否已发放
          /// </summary>
          public int IsGrant { get; set; }

           /// <summary>
           ///发放时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? GrantDate { get; set; }

           /// <summary>
           ///剩余状态（1：有剩余；2：无剩余；3：有剩余并已处理；）
          /// </summary>
          public int SurplusStatuz { get; set; }

           /// <summary>
           ///废弃物状态（1：有废弃物；2：无废弃物；3：有废弃物并已处理）
          /// </summary>
          public int WasetStatuz { get; set; }

           /// <summary>
           ///实验后是否已确认
          /// </summary>
          public bool IsAfterConfirm { get; set; }

           /// <summary>
           ///同发放人
          /// </summary>
          public long WithUserId { get; set; }

           /// <summary>
           ///初始领用数量
          /// </summary>
          [SugarColumn(IsNullable = true,ColumnDataType = "money")]
          public decimal? InitalNum { get; set; }

           /// <summary>
           ///发放人
          /// </summary>
          public long GrantUserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

