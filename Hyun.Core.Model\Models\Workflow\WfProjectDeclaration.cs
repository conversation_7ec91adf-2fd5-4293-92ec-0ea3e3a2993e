﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///采购项目立项申报
    ///</summary>
    [SugarTable("wf_ProjectDeclaration", "采购项目立项申报")]
    public class WfProjectDeclaration : BaseEntity
    {

        public WfProjectDeclaration()
        {

        }

        /// <summary>
        ///流程名称配置表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long ProcessId { get; set; }

        /// <summary>
        ///单位Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long UnitId { get; set; }

        /// <summary>
        ///采购项目名称
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string ProjectName { get; set; }

        /// <summary>
        ///项目金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal ProjectAmount { get; set; } = 0;

        /// <summary>
        ///项目状态(0：已创建  1：审批中  1000：审批结束；其他为配置的状态，待审核列表必须 满足状态大于0，小于1000)
        /// </summary>
        public int Statuz { get; set; } = 0;

        /// <summary>
        ///项目状态描述
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string StatuzDesc { get; set; }

        /// <summary>
        ///审核完成时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        ///退回次数
        /// </summary>
        public int NoPassNum { get; set; } = 0;

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(DefaultValue ="0", IsNullable = true)]
        public decimal? NumSum { get; set; } = 0;

        /// <summary>
        ///填报Json数据
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        public string DataContent { get; set; }

        /// <summary>
        ///流程节点关系表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessNodeLinkId { get; set; }

        /// <summary>
        /// 申报年度
        /// </summary>
        public int PlanYear { get; set; }

        /// <summary>
        /// 部门Id
        /// </summary>
        public long DepartmentId { get; set; }

        /// <summary>
        ///开始节点
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long FromId { get; set; }

        /// <summary>
        ///结束节点
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long ToId { get; set; }

        /// <summary>
        ///审批人Id逗号分隔（只有节点配置设置成指定人的时候）
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string AuditUserIds { get; set; }

        /// <summary>
        /// 是否可撤销（1：是  2：否）
        /// </summary>
        public int IsWithdraw { get; set; } = 2;

        /// <summary>
        /// 当前状态
        /// </summary>
        public int CurrentStatuz { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 单位名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SchoolName { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string CountyName { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public DateTime AuditTime { get; set; }


        /// <summary>
        ///已审批人Id逗号分隔（只有多人审批未完成时（即主表from to 未改变时），更新改字段；当多人全部审核转交下一步时，将该字段清空）
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string AuditedUserIds { get; set; }

        /// <summary>
        /// 审核表Id
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long ProjectAuditId { get; set; }

        /// <summary>
        /// 填报日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? FillingDate { get; set; }

        /// <summary>
        /// 填报人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FillingUser { get; set; }

        /// <summary>
        /// 审核人Id
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long ProjectAuditUserId { get; set; }

        /// <summary>
        /// 单位Id
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long SchoolId { get; set; }


        /// <summary>
        /// 区县Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long CountyId { get; set; }


        /// <summary>
        /// 市级Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long CityId { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string AreaName { get; set; }

        /// <summary>
        /// 单位地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string Address { get; set; }

        /// <summary>
        /// 单位性质
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string UnitNature { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string ProjectCode { get; set; }

        /// <summary>
        /// 一级分类Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long OneClassId { get; set; }

        /// <summary>
        /// 二级分类Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long TwoClassId { get; set; }

        /// <summary>
        /// 指定节点Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long AppointProcessNodeId { get; set; }

        /// <summary>
        /// 已指定节点Id，用于撤销使用
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long AppointedProcessNodeId { get; set; }
    }


}

