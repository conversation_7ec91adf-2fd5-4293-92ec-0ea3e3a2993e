﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///装备分类适用学段表
    ///</summary>
    [SugarTable("th_EquipmentCategoryStage","装备分类适用学段表")]
    public class ThEquipmentCategoryStage : BaseEntity
    {

          public ThEquipmentCategoryStage()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///装备分类表Id
          /// </summary>
          public long EquipmentCategoryId { get; set; }

           /// <summary>
           ///学段Id
          /// </summary>
          public int SchoolStageId { get; set; }

           /// <summary>
           ///学段名称
          /// </summary>
          [SugarColumn(Length = 127)]
          public string Name { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          public int Sort { get; set; } = 0;

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

