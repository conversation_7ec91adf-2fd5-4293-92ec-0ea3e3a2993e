﻿using Dm;
using Hyun.Core.Common.DB;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.SearchModels.Common;
using Hyun.Old.Util;
using Microsoft.AspNetCore.Mvc;
using NetTaste;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SharpCompress.Common;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Dynamic;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Hyun.Core.Api.Controllers.Workflow
{
    [Route("api/hyun/process")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcessController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IWfProjectAuditUserServices projectAuditUserManager;
        private readonly IWfProcessNodeServices processNodeManager;
        private readonly IWfProcessFieldServices processFieldManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IWfDictionaryServices wfDictionaryManager;
        private readonly ISysUserExtensionServices sysUserExtensionManager;
        private readonly IWfProjectDeclarationServices projectDeclarationManager;
        private readonly IWfPageColumnConfigServices pageColumnConfigManager;
        private readonly IWfProcessStatuzServices processStatuzManager;
        private readonly IPUnitServices unitManager;
        private readonly IWfProjectListServices projectListManager;
        private readonly IWfProjectListFieldSetServices projectListFieldSetManager;
        private readonly IWfPageDefinitionServices pageDefinitionManager;
        private readonly IWfProjectListReviewServices projectListReviewManager;
        private readonly IWfProjectApprovalNoServices projectApprovalNoManager;
        private readonly IWfProjectListHistoryServices projectListHistoryManager;
        private readonly IWfSourceFundServices sourceFundManager;
        private readonly IWfSourceFundUseServices sourceFundUserManager;
        private readonly IWfLinkAgeHostServices linkAgeHostManager;
        private readonly IWfLookEachOtherSetServices lookEachOtherSetManager;
        private readonly IWfControlDetailServices controlDetailManager;
        private readonly IWfCodeGenerateSetServices codeGenerateSetManager;
        private readonly IWfFundFieldSetServices fundFieldSetManager;
        private readonly IWfGroupProcessSetServices groupSetManager;
        private readonly IWfProcessServices processManager;
        private readonly IWfMsgConfigServices msgConfigManager;

        public ProcessController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IWfProjectAuditUserServices _projectAuditUserManager, IWfProcessNodeServices _processNodeManager, IWfProcessFieldServices _processFieldManager, IBDictionaryServices _dictionaryManager, IWfDictionaryServices _wfDictionaryManager, ISysUserExtensionServices _sysUserExtensionManager, IWfProjectDeclarationServices _projectDeclarationManager, IWfPageColumnConfigServices _pageColumnConfigManager, IWfProcessStatuzServices _processStatuzManager, IPUnitServices _unitManager, IWfProjectListServices _projectListManager, IWfProjectListFieldSetServices _projectListFieldSetManager, IWfPageDefinitionServices _pageDefinitionManager, IWfProjectListReviewServices _projectListReviewManager, IWfProjectApprovalNoServices _projectApprovalNoManager, IWfProjectListHistoryServices _projectListHistoryManager, IWfSourceFundServices _sourceFundManager, IWfSourceFundUseServices _sourceFundUserManager, IWfLinkAgeHostServices _linkAgeHostManager, IWfLookEachOtherSetServices _lookEachOtherSetManager, IWfControlDetailServices _controlDetailManager, IWfCodeGenerateSetServices _codeGenerateSetManager, IWfFundFieldSetServices _fundFieldSetManager, IWfGroupProcessSetServices _groupSetManager, IWfProcessServices _processManager, IWfMsgConfigServices _msgConfigManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            projectAuditUserManager = _projectAuditUserManager;
            processNodeManager = _processNodeManager;
            processFieldManager = _processFieldManager;
            dictionaryManager = _dictionaryManager;
            wfDictionaryManager = _wfDictionaryManager;
            sysUserExtensionManager = _sysUserExtensionManager;
            projectDeclarationManager = _projectDeclarationManager;
            pageColumnConfigManager = _pageColumnConfigManager;
            processStatuzManager = _processStatuzManager;
            unitManager = _unitManager;
            projectListManager = _projectListManager;
            projectListFieldSetManager = _projectListFieldSetManager;
            pageDefinitionManager = _pageDefinitionManager;
            projectListReviewManager = _projectListReviewManager;
            projectApprovalNoManager = _projectApprovalNoManager;
            projectListHistoryManager = _projectListHistoryManager;
            sourceFundManager = _sourceFundManager;
            sourceFundUserManager = _sourceFundUserManager;
            linkAgeHostManager = _linkAgeHostManager;
            lookEachOtherSetManager = _lookEachOtherSetManager;
            controlDetailManager = _controlDetailManager;
            codeGenerateSetManager = _codeGenerateSetManager;
            fundFieldSetManager = _fundFieldSetManager;
            groupSetManager = _groupSetManager;
            processManager = _processManager;
            msgConfigManager = _msgConfigManager;
        }

        #region 业务配置-权限设置

        /// <summary>
        /// 流程管理：权限设置列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getprocessaudituserlist")]
        public async Task<Result<PageModel<ModuleAuditModel>>> GetProcessAuditUserList()
        {
            PageModel<ModuleAuditModel> data = await projectAuditUserManager.GetProcessAuditUserList();
            return baseSucc(data, data.dataCount);
        }

        /// <summary>
        /// 流程管理：获取设置的审核审批人数据信息
        /// </summary>
        /// <param name="processId"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsetuserlist")]
        public async Task<Result> GetSetUserList(long processId, long processNodeId)
        {
            Result r = new Result();
            int isLook = 2;
            List<SetUserListModel> list = await projectAuditUserManager.GetSetUserList(processId, processNodeId);
            //根据流程节点Id获取是否配置互看信息
            var listLook = await lookEachOtherSetManager.Query(f => f.IsDeleted == false && f.ProcessId == processId && f.ProcessNodeId == processNodeId && f.UnitId == user.UnitId);
            if (listLook.Count > 0)
            {
                isLook = listLook.First().IsLook;
            }
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = list;
            r.data.headers = isLook;
            return r;
        }

        /// <summary>
        /// 流程管理：设置节点审核审批人
        /// </summary>
        /// <param name="userModel"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("setnodeaudituser")]
        public async Task<Result<string>> SetProcessNodeAuditUser([FromBody] UserListModel userModel)
        {
            return await projectAuditUserManager.SetUserList(userModel);
        }

        #endregion

        #region 数据填报提交

        /// <summary>
        /// 流程审批：获取填报页面信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getfillinginfo")]
        public async Task<Result> GetFillingInfo([FromBody] DropListParam o)
        {
            Result r = new Result();
            var listNode = await processNodeManager.Query(f => f.Id == o.ProcessNodeId);
            if (listNode.Count > 0)
            {
                WfProcessNode objNode = listNode[0];
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = objNode.NodeConfig;
                r.data.footer = 0;
                r.data.other2 = objNode;

                //if (objNode.IsBringOut == 1) 
                //{ 
                //    List<WfProjectDeclaration> listDeclaration = await projectDeclarationManager.Query(f=>f.IsDeleted == false && f.UnitId == user.UnitId);
                //    var listGroup = listDeclaration.GroupBy(f => new
                //    {
                //        f.ProjectName,
                //        f.ProjectCode
                //    })
                //    .Select(group => new WfProjectDeclarationDto
                //    {
                //        ProjectName = group.Key.ProjectName,
                //        ProjectCode = group.Key.ProjectCode
                //    }).ToList();
                //    r.data.other3 = listGroup;
                //}

                List<dropdownModel> listProjectCode = new List<dropdownModel>();
                if (o.ProjectDeclarationId > 0)
                {
                    WfProjectDeclaration objProjectDeclaration = projectDeclarationManager.Query(f=>f.Id == o.ProjectDeclarationId && f.UnitId == user.UnitId).Result.FirstOrDefault();
                    if (objProjectDeclaration != null)
                    {
                        r.data.headers = objProjectDeclaration.DataContent;

                        List<WfProjectList> listProject = await projectListManager.Query(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.FieldCode == "ProjectList" && f.IsDeleted == false);
                        if (listProject.Count > 0)
                        {
                            r.data.footer = listProject.Sum(f => f.Sum);
                        }

                        //如果主表中有ProjectCode数据读取主表中ProjectCode数据
                        listProjectCode.Add(new dropdownModel()
                        {
                            label = "ProjectCode",
                            value = objProjectDeclaration.ProjectCode,
                            desp = "projectnumber"
                        });
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "非法操作";
                    }
                }


                if (o.ProjectDeclarationId == 0)
                {
                    listProjectCode = await codeGenerateSetManager.GenerateProjectCode(o.ProcessNodeId);
                }

                //查询是否有下拉框配置值
                SelectDropDownModel objModel = await projectDeclarationManager.GetDropList(o.ProcessId, o.ProcessNodeId, o.ProjectDeclarationId);
                if (listProjectCode.Count > 0)
                {
                    objModel.ListSelect.AddRange(listProjectCode);
                }
                r.data.other = new { listSelect = objModel.ListSelect };
                if (objModel.ListSourceFund != null && objModel.ListSourceFund.Count > 0)
                {
                    r.data.other1 = objModel.ListSourceFund;
                }
                //if(objModel.ListDefault != null && objModel.ListDefault.Count > 0)
                //{
                //    r.data.other3 = objModel.ListDefault;
                //}
            }
            return r;
        }

        /// <summary>
        /// 流程审批：根据父级编码获取子级数据信息
        /// </summary>
        /// <param name="code">格式：节点Id_输入框类型_父级Code编码_分类编码_父级选择值</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getlinkagedatasource")]
        public async Task<Result> GetLinkAgeDataSource(string code)
        {
            return await linkAgeHostManager.GetUserLinkAgeList(code);
        }

        /// <summary>
        /// 流程审批：根据流程节点Id获取项目编号
        /// </summary>
        /// <param name="projectNodeId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getprojectcode")]
        public async Task<Result> GetProjectCode(long projectNodeId)
        {
            Result r = new Result();
            List<dropdownModel> listSelect = await codeGenerateSetManager.GetProjectCode(projectNodeId);
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = listSelect;
            return r;
        }

        /// <summary>
        /// 流程审批：根据一级二级分类Id获取项目名称及编码
        /// </summary>
        /// <param name="oneClassId">一级分类Id</param>
        /// <param name="twoClassId">二级分类Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getprojectdeclarationcodename")]
        public async Task<Result> GetProjectDeclarationCodeName(long oneClassId = 0, long twoClassId = 0)
        {
            Result r = new Result();
            //var expression = LinqExtensions.True<WfProjectDeclaration>();
            //expression = expression.AndNew(f => f.IsDeleted == false && f.UnitId == user.UnitId);
            //if(oneClassId > 0)
            //{
            //    expression = expression.AndNew(f => f.OneClassId == oneClassId);
            //}
            //if(twoClassId > 0)
            //{
            //    expression = expression.AndNew(f => f.TwoClassId == twoClassId);
            //}
            List<WfProjectDeclaration> listDeclaration = await projectDeclarationManager.Query(f => f.IsDeleted == false && f.UnitId == user.UnitId && f.OneClassId == oneClassId && f.TwoClassId == twoClassId && !string.IsNullOrEmpty(f.ProjectName) && !string.IsNullOrEmpty(f.ProjectCode));
            var listGroup = listDeclaration.GroupBy(f => new
            {
                f.ProjectName,
                f.ProjectCode
            })
            .Select(group => new dropdownModel
            {
                label = $"{group.Key.ProjectName}",
                value = group.Key.ProjectCode,
                pname = group.Key.ProjectCode
            }).ToList();
            r.flag = 1;
            r.data.rows = listGroup;
            r.msg = "查询成功";
            return r;
        }

        /// <summary>
        /// 流程审批：根据编码获取被控制字段显示信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getcontroldetaildatasource")]
        public async Task<Result> GetControlDetailDataSource([FromBody] ReceptionControlMode model)
        {
            return await controlDetailManager.GetFieldCodeList(model);
        }

        /// <summary>
        /// 流程审批：填报页面保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("savefillinginfo")]
        public async Task<Result> SaveFillingInfo([FromBody] FillingModel o)
        {
            return await projectDeclarationManager.FillingSave(o, 1);
        }

        /// <summary>
        /// 流程审批：填报页面提交
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("submitfillinginfo")]
        public async Task<Result> SubmitFillingInfo([FromBody] FillingModel o)
        {
            Result r = new Result();
            r = await projectDeclarationManager.FillingSave(o, 2);
            if(r.flag == 1)
            {
                //消息推送
                await ProcessMsgSend(o.ProcessId, o.ProcessNodeId, o.ProjectDeclarationId, "100100101");
            }
            return r; 
        }

        /// <summary>
        /// 流程审批：删除填报数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delfillinginfo")]
        public async Task<Result<string>> DelFillingInfo(long id)
        {
            return await projectDeclarationManager.DelFillingInfo(id);
        }

        /// <summary>
        /// 流程审批：根据填报Id获取详情信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("fillinginfodetail")]
        public async Task<Result> FillingInfoDetail(long id)
        { 
            return await projectDeclarationManager.FillingDetail(id);
        }

        /// <summary>
        /// 流程审批：根据填报Id获取清单总金额
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getlistsumbyprojectdeclarationid")]
        public async Task<Result> GetListSumByProjectDeclarationId(long id)
        {
            Result r = new Result();
            decimal sumMoney = 0;
            var obj = await projectDeclarationManager.QueryById(id);
            if (obj == null)
            {
                r.flag = 0;
                r.msg = "填报项目不存在";
                return r;
            }
            if(obj.CreateId != user.ID)
            {
                r.flag = 0;
                r.msg = "您无权查看他人清单信息";
                return r;
            }
            List<WfProjectList> list = await projectListManager.Query(f => f.IsDeleted == false && f.ProjectDeclarationId == id && f.FieldCode == "ProjectList");
            if (list.Count > 0)
            {
                sumMoney = list.Sum(f => f.Sum);
            }
            r.flag = 1;
            r.msg = "";
            r.data.rows = sumMoney;
            return r;
        }
        #endregion

        #region 待处理已处理列表
        /// <summary>
        /// 流程审批：获取待处理列表数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getfillsearchlist")]
        public async Task<Result<PageModel<dynamic>>> GetFillSearchList([FromBody] WfPageDefinitionParam param)
        {
            PageModel<dynamic> pg = await pageColumnConfigManager.GetNodePageList(param);
            return baseSucc(pg, pg.dataCount);
        }


        /// <summary>
        /// 流程审批：获取已处理列表数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getprocessedlist")]
        public async Task<Result<PageModel<dynamic>>> GetProcessedList([FromBody] WfPageDefinitionParam param)
        {
            PageModel<dynamic> pg = await pageColumnConfigManager.GetProcessedPageList(param);
            return baseSucc(pg, pg.dataCount);
        }
        #endregion

        #region 流程审批
        /// <summary>
        /// 流程审批：审核审批页面信息
        /// </summary>
        /// <param name="id">填报Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("fillinginfoaudit")]
        public async Task<Result> FillingInfoAudit(long id, long processNodeId)
        {
            return await projectDeclarationManager.FillingAudit(id, processNodeId);
        }

        /// <summary>
        /// 流程审批：暂存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveapproval")]
        public async Task<Result<string>> SaveApproval([FromBody] FillingModel o)
        {
            return await projectDeclarationManager.ProcessSave(o);
        }

        /// <summary>
        ///  流程审批：审核审批
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("submitapproval")]
        public async Task<Result> SubmitApproval([FromBody] FillingModel o)
        {
            Result r = new Result();
            r = await projectDeclarationManager.ProcessApproval(o);
            if(r.flag == 1)
            {
                //审核审批通过
                if(r.Id == 1)
                {
                    await ProcessMsgSend(o.ProcessId, o.ProcessNodeId, o.ProjectDeclarationId, "100100201");
                }
                else if(r.Id == 2)
                {
                    await ProcessMsgSend(o.ProcessId, o.ProcessNodeId, o.ProjectDeclarationId, "100100202");
                }
            }
            return r;
        }


        /// <summary>
        /// 流程审批：操作撤销
        /// </summary>
        /// <param name="projectAuditId">审核表Id</param>
        /// <param name="projectDeclarationId">填报表Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("operaterevoke")]
        public async Task<Result<string>> OperateRevoke(long projectAuditId, long projectDeclarationId,long processNodeId)
        {
            return await projectDeclarationManager.Revoke(projectAuditId, projectDeclarationId, processNodeId);
        }
        #endregion

        #region 项目清单
        /// <summary>
        /// 流程审批：查询审批预算清单表数据
        /// </summary>
        /// <param name="param">
        /// ProjectDeclarationId:立项填报Id必传
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("projectlistsearch")]
        public async Task<Result<PageModel<WfProjectListDto>>> FindProjectListFieldSetList([FromBody] WfProjectListParam param)
        {
            PageModel<WfProjectList> data = await projectListManager.GetPaged(param);
            if (param.isFirst)
            {
                var listFieldSet = await projectListFieldSetManager.Query(f => f.ProcessId == param.ProcessId && f.IsShow == 1 && f.FieldCode == param.FieldCode);
                var listColumn = listFieldSet.Where(f => f.ConfigType == 2).OrderBy(f => f.Sort).ToList();
                var listSearch = listFieldSet.Where(f => f.SearchIsShow == 1 && f.ConfigType == 2).OrderBy(f => f.Sort).ToList();
                var listAdd = listFieldSet.Where(f => f.ConfigType == 1).OrderBy(f => f.Sort).ToList();

                //获取下载项目清单地址
                string sFileName = $"{SecurityHelper.GetGuid(true)}_预算清单.xls";
                var listFieldCode = await processFieldManager.Query(f => f.FieldCode == param.FieldCode);
                if (listFieldCode.Count > 0)
                {
                    if (!string.IsNullOrEmpty(listFieldCode[0].FieldName))
                    {
                        sFileName = listFieldCode[0].FieldName + ".xls";
                    }
                }
                string sRoot = env.WebRootPath;
                string partDirectory = $"Resource{Path.DirectorySeparatorChar}Export{Path.DirectorySeparatorChar}Excel";
                string sDirectory = Path.Combine(sRoot, partDirectory);
                string sFilePath = Path.Combine(sDirectory, sFileName);
                if (!Directory.Exists(sDirectory))
                {
                    Directory.CreateDirectory(sDirectory);
                }
                //查询配置Execl下载数据
                var listFieldExeclSet = listFieldSet.Where(f => f.ConfigType == 3).ToList();
                if (listFieldExeclSet.Count == 0)
                {
                    listFieldExeclSet = await projectListFieldSetManager.Query(f => f.IsShow == 1 && f.ConfigType == 0);
                }
                using (MemoryStream ms = CreateExportMemoryStream("", "", listFieldExeclSet))
                {
                    using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                    {
                        byte[] execlData = ms.ToArray();
                        fs.Write(execlData, 0, execlData.Length);
                        fs.Flush();
                    }
                }
                string filePath = "/" + partDirectory + Path.DirectorySeparatorChar + sFileName;
                string downLoadFile = filePath;

                return baseSucc(data.ConvertTo<WfProjectListDto>(mapper), data.dataCount, "查询成功", new { listColumn = listColumn, listSearch = listSearch, listAdd = listAdd, downLoadFile = downLoadFile, execlName = sFileName });
            }
            else
            {
                return baseSucc(data.ConvertTo<WfProjectListDto>(mapper), data.dataCount);
            }

        }

        /// <summary>
        /// 流程审批：保存预算清单信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("projectlistsave")]
        public async Task<Result<WfProjectListDto>> ProjectListSave([FromBody] WfProjectListDto o)
        {
            o.UnitId = user.UnitId;
            return await projectListManager.InsertUpdate(o);
        }

        /// <summary>
        /// 流程审批：根据Id查询预算清单信息
        /// </summary>
        /// <param name="id">预算清单Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("projectlistbyid")]
        public async Task<Result<WfProjectListDto>> ProjectListById(long id)
        {
            WfProjectList m = await projectListManager.QueryById(id);
            if (m != null)
            {
                return Result<WfProjectListDto>.Success("查询成功。", mapper.Map<WfProjectListDto>(m), 1);
            }
            else
            {
                return Result<WfProjectListDto>.Fail("未查询到数据");
            }
        }

        /// <summary>
        /// 流程审批：根据Id删除预算清单信息
        /// </summary>
        /// <param name="id">预算清单Id</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("projectlistdeletebyid")]
        public async Task<Result<string>> ProjectListDeleteById(long id)
        {
            return await projectListManager.FakeDeleteById(id);
        }

        /// <summary>
        /// 流程审批：根据Id集合删除预算清单信息
        /// </summary>
        /// <param name="ids">Id集合，逗号分割</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("projectlistdeletebyids")]
        public async Task<Result<string>> ProjectListDeleteByIds(string ids)
        {
            return await projectListManager.FakeDeleteByIds(ids);
        }

        /// <summary>
        /// 流程审批：下载项目清单Execl
        /// </summary>
        /// <param name="param">
        /// ProcessId：必传
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("downloadprojectlist")]
        public async Task<Result> DownLoadProjectList([FromBody] WfProjectListParam param)
        {
            Result r = new Result();
            string sFileName = $"{SecurityHelper.GetGuid(true)}_预算清单.xls";
            string sRoot = env.WebRootPath;
            string partDirectory = $"Resource{Path.DirectorySeparatorChar}Export{Path.DirectorySeparatorChar}Excel";
            string sDirectory = Path.Combine(sRoot, partDirectory);
            string sFilePath = Path.Combine(sDirectory, sFileName);
            if (!Directory.Exists(sDirectory))
            {
                Directory.CreateDirectory(sDirectory);
            }
            //查询配置Execl下载数据
            var listFieldSet = await projectListFieldSetManager.Query(f => f.FieldCode == param.FieldCode && f.ProcessId == param.ProcessId && f.IsShow == 1 && f.FieldCode == param.FieldCode && f.ConfigType == 3);
            if (listFieldSet.Count == 0)
            {
                listFieldSet = await projectListFieldSetManager.Query(f => f.IsShow == 1 && f.ConfigType == 0);
            }
            using (MemoryStream ms = CreateExportMemoryStream("", "", listFieldSet))
            {
                using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                {
                    byte[] data = ms.ToArray();
                    fs.Write(data, 0, data.Length);
                    fs.Flush();
                }
            }
            string filePath = "/" + partDirectory + Path.DirectorySeparatorChar + sFileName;
            r.data.rows = filePath;
            r.flag = 1;
            r.data.headers = "预算清单";
            r.msg = "下载成功";
            return r;
        }


        /// <summary>
        /// 流程审批：项目清单导入
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("projectlistimport")]
        public async Task<Result<string>> ProjectListImport([FromBody] ProjectListImportParam param)
        {
            var listFieldSet = await projectListFieldSetManager.Query(f => f.ProcessId == param.ProcessId && f.FieldCode == param.FieldCode && f.IsShow == 1 && f.ConfigType == 3);
            if (listFieldSet.Count == 0)
            {
                listFieldSet = await projectListFieldSetManager.Query(f => f.IsShow == 1 && f.ConfigType == 0);
            }
            Dictionary<string, string> listField = new Dictionary<string, string>();
            foreach (var f in listFieldSet)
            {
                listField.Add(f.Title, f.FieldValue);
            }
            List<WfProjectList> list = new ExcelHelper<WfProjectList>().ImportProjectListFromExcel(env.ContentRootPath, param.FilePath, 1, listField);
            return await projectListManager.ImportProjectList(listFieldSet, list, param.ProjectDeclarationId, param.FieldCode);
        }



        /// <summary>
        /// 流程审批：项目清单审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findauditprojectlist")]
        public async Task<Result<PageModel<WfProjectListDto>>> FindAuditProjectList([FromBody] WfProjectListReviewParam param)
        {
            PageModel<WfProjectListDto> data = await projectListReviewManager.GetAuditProjectListPaged(param);
            if (param.isFirst)
            {
                var listFieldSet = await projectListFieldSetManager.Query(f => f.ProcessId == param.ProcessId && f.IsShow == 1 && f.FieldCode == param.FieldCode);
                var listColumn = listFieldSet.Where(f => f.ConfigType == 2).ToList();
                //此处后面2列要固定，分别是“上一次审核意见（字段：LastReviewNote）”、“审核意见(字段：ReviewNote)”
                return baseSucc(data, data.dataCount, "查询成功", new { listColumn = listColumn});
            }
            else
            {
                return baseSucc(data, data.dataCount);
            }
        }

        /// <summary>
        /// 流程审批：项目清单保存、删除、审核处理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveprojectlistreview")]
        public async Task<Result<string>> SaveProjectListReview([FromBody] AuditModel model)
        {
            return await projectListReviewManager.SaveProjectListReview(model);
        }

        /// <summary>
        /// 流程审批：获取项目清单历史审批记录数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findhistoryprojectlist")]
        public async Task<Result<PageModel<WfProjectListHistoryDto>>> FindHistoryProjectList([FromBody] WfProjectListHistoryParam param)
        {

            PageModel<WfProjectListHistoryDto> pg = new PageModel<WfProjectListHistoryDto>();

            if (param.ProcessId == 0)
            {
                var obj = await projectDeclarationManager.QueryById(param.ProjectDeclarationId);
                if (obj != null)
                {
                    param.ProcessId = obj.ProcessId;
                }
            }

            if (param.isFirst)
            {
                var listFieldSet = await projectListFieldSetManager.Query(f => f.ProcessId == param.ProcessId && f.IsShow == 1 && f.FieldCode == param.FieldCode);
                var listColumn = listFieldSet.Where(f => f.ConfigType == 2).ToList();
                var listSearch = listFieldSet.Where(f => f.SearchIsShow == 1 && f.ConfigType == 2).ToList();

                //先获取批次信息
                var listNo = await projectApprovalNoManager.Query(f => f.ProjectDeclarationId == param.ProjectDeclarationId && f.ProcessNodeId == param.ProcessNodeId && f.FieldCode == param.FieldCode);
                if (listNo.Count == 0)
                {
                    return baseSucc(pg, pg.dataCount);
                }
                else
                {
                    List<dropdownModel> listApprovalNo = listNo.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.AuditNo.ToString() }).ToList();
                    WfProjectApprovalNo approvalNo = listNo.LastOrDefault();
                    param.ProjectApprovalNoId = approvalNo.Id;
                    pg = await projectListHistoryManager.GetHistoryProjectListPaged(param);
                    return baseSucc(pg, pg.dataCount, "查询成功", new { listApprovalNo = listApprovalNo, listColumn = listColumn, listSearch = listSearch });
                }
            }
            else
            {
                pg = await projectListHistoryManager.GetHistoryProjectListPaged(param);
                return baseSucc(pg, pg.dataCount);
            }
        }
        #endregion

        #region 查询统计

        /// <summary>
        /// 流程审批：查询统计列表页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("findpagedelist")]
        public async Task<Result> FindPageDefinitionList(long moduleId)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            var expression = LinqExtensions.True<WfPageDefinition>();
            expression = expression.AndNew(f => f.Statuz == 1 && f.ModuleId == moduleId && f.Unittype == user.UnitTypeId);
            if (user.UnitTypeId == UnitTypeEnum.School.ToEnumInt())
            {
                long countyId = 0;
                long cityId = 0;
                PUnit unitSchool = await unitManager.QueryById(user.UnitId);
                if (unitSchool != null)
                {
                    countyId = unitSchool.PId;

                    PUnit unitCounty = await unitManager.QueryById(countyId);
                    if (unitCounty != null)
                    {
                        cityId = unitCounty.PId;
                    }
                }
                expression = expression.AndNew(f => f.UseUnitId == user.UnitId || f.UseUnitId == countyId || f.UseUnitId == cityId);
            }
            else if(user.UnitTypeId == UnitTypeEnum.County.ToEnumInt())
            {
                expression = expression.AndNew(f => f.UseUnitId == user.UnitId || f.UseUnitId == user.UnitPId);
            }
            else
            {
                expression = expression.AndNew(f => f.UseUnitId == user.UnitId);
            }
            List<WfPageDefinition> listPageDefinition = await pageDefinitionManager.Query(expression);
            r.data.rows = mapper.Map<List<WfPageDefinitionDto>>(listPageDefinition);
            return r;
        }

        /// <summary>
        /// 流程审批：动态生成查询列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getsearchlist")]
        public async Task<Result<PageModel<dynamic>>> GetSearchList([FromBody] WfPageDefinitionParam param)
        {
            PageModel<dynamic> pg = await pageColumnConfigManager.GetSearchPageList(param);
            return baseSucc(pg, pg.dataCount);
        }


        /// <summary>
        /// 流程审批：查询统计导出Execl
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("downloadsearchlist")]
        public async Task<IActionResult> DownLoadSearchList([FromBody] WfPageDefinitionParam param)
        {
            param.pageSize = int.MaxValue;
            PageModel<dynamic> pg = await pageColumnConfigManager.GetSearchPageList(param);
            WfPageDefinition objPageDefinition = await pageDefinitionManager.GetById(param.PageDefinitionId);
            if (objPageDefinition == null)
            {
                return NotFound("未获取到页面对象");
            }
            string totalShowName = objPageDefinition.TotalName;
            //获取显示的列
            List<WfPageColumnConfig> listConfig = await pageColumnConfigManager.Query(f => f.PageDefinitionId == param.PageDefinitionId && f.ModeType == 1 && f.Statuz == 1 && f.IsDeleted == false);
            List<WfPageColumnConfig> listColumn = listConfig.Where(f => f.ListFieldType == 1 && f.TypeBox != 20).OrderBy(f => f.Sort).ToList();
            var excelBytes = await CreateQueryMemoryStream(objPageDefinition.ShowName, pg, listColumn, totalShowName);
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", objPageDefinition.ShowName);
        }
        #endregion

        #region 资金来源
        /// <summary>
        /// 流程审批：资金来源显示列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("findsourcefundlist")]
        public async Task<Result<PageModel<WfSourceFundDto>>> FindSourceFundList([FromBody] WfSourceFundParam param)
        {
            PageModel<WfSourceFundDto> data = await sourceFundManager.GetPaged(param);
            if (param.isFirst)
            {
                //获取显示列
                List<WfPageColumnConfig> listConfig = await pageColumnConfigManager.Query(f => f.ModeType == 3 && f.ProcessId == param.ProcessId && f.Statuz == 1 && !string.IsNullOrEmpty(f.FieldCode) && f.FieldType != 5);

                //实际显示字段
                List<WfFundFieldSet> listFundFieldSet = await fundFieldSetManager.Query(f => f.IsDeleted == false);

                //获取列表页面所有查询条件
                List<WfPageColumnConfig> listWhere = listConfig.Where(f => f.ListFieldType == 2).OrderBy(f => f.Sort).ToList();
                foreach(WfPageColumnConfig config in listWhere)
                {
                    var obj = listFundFieldSet.Where(f => f.Id == config.FundFieldSetId).FirstOrDefault();
                    if (obj != null)
                    {
                        config.FieldCode = obj.Code;
                    }
                }

                //获取显示的列
                List<WfPageColumnConfig> listColumn = listConfig.Where(f => f.ListFieldType == 1).OrderBy(f => f.Sort).ToList();

                foreach (WfPageColumnConfig config in listColumn)
                {
                    var obj = listFundFieldSet.Where(f => f.Id == config.FundFieldSetId).FirstOrDefault();
                    if (obj != null)
                    {
                        config.FieldCode = obj.Code;
                    }
                }

                return baseSucc(data, data.dataCount, "查询成功", new { listColumn = listColumn, listWhere = listWhere});
            }
            else
            {
                return baseSucc(data, data.dataCount);
            }
        }
        #endregion

        #region 分组管理
        /// <summary>
        /// 流程管理：分级授权列表
        /// </summary>
        /// <param name="param">
        ///  ProcessId:流程Id
        ///  ProcessNodeId：流程节点Id
        ///  GroupId:对应UseGroupValue值,待处理列表启用分组筛选值
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("getgroupitemuserlist")]
        public async Task<Result<PageModel<DicAuditUserModel>>> GetGroupItemUserList([FromBody] WfGroupUnitSetParam param)
        {
            PageModel<DicAuditUserModel> data = await groupSetManager.GetItemUserList(param);
            return baseSucc(data, data.dataCount);
        }

        /// <summary>
        /// 流程管理：获取待授权用户列表信息
        /// </summary>
        /// <param name="processId">流程Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getwaitaudituserlist")]
        public async Task<Result> GetWaitAuditUserList(long processId,long processNodeId)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            List<SetUserListModel> list = await groupSetManager.GetSetUserList(processId, processNodeId);
            r.data.rows = list;
            return r;
        }

        /// <summary>
        /// 流程管理：设置分组项对应人员信息
        /// </summary>
        /// <param name="userModel"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("setgroupunitaudituser")]
        public async Task<Result<string>> SetGroupUnitAuditUser([FromBody] UserListModel userModel)
        {
            return await groupSetManager.SetGoupUserList(userModel);
        }
        #endregion

        #region 首页流程审批数据展示
        /// <summary>
        /// 流程审批：根据当前用户获取流程审批数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getprocessindexinfo")]
        public async Task<Result> GetProcessIndexInfo()
        {
            return await processManager.GetHomePageData();
        }
        #endregion


        #region 私有方法
        /// <summary>
        /// 获取树数据
        /// </summary>
        /// <param name="flatList"></param>
        /// <returns></returns>
        private List<dropdownModel> BuildDropDownModelTree(List<dropdownModel> flatList)
        {
            var lookup = flatList.ToDictionary(n => n.value);
            var rootNodes = new List<dropdownModel>();

            foreach (var node in flatList)
            {
                if (node.pid == 0)
                {
                    // 这是根节点，没有父节点
                    rootNodes.Add(node);
                }
                else if (lookup.ContainsKey(node.pid.ToString()))
                {
                    // 将当前节点添加到其父节点的子节点列表中
                    lookup[node.pid.ToString()].children.Add(node);
                }
                // 如果PID在列表中不存在，则忽略该节点或进行其他处理
            }

            return rootNodes;
        }

        /// <summary>
        /// 生成Execl数据
        /// </summary>
        /// <param name="sheetName">execl底部名称</param>
        /// <param name="headText">标题文字内容</param>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream CreateExportMemoryStream(string sheetName, string headText, List<WfProjectListFieldSet> list)
        {
            if (string.IsNullOrEmpty(sheetName))
            {
                sheetName = "预算清单";
            }
            if (string.IsNullOrEmpty(headText))
            {
                headText = "红色标题为必填项，请不要改变列头循序及表格式否则会导入不成功!";
            }
            list = list.OrderBy(f => f.Sort).ToList();
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet(sheetName);

            //红色字体
            IFont fontHeadRed = workbook.CreateFont();
            fontHeadRed.FontName = "宋体";
            fontHeadRed.IsBold = false;
            fontHeadRed.FontHeightInPoints = 10;
            fontHeadRed.Color = HSSFColor.OliveGreen.Red.Index;

            IFont fontTitleRed = workbook.CreateFont();
            fontTitleRed.FontName = "宋体";
            fontTitleRed.IsBold = true;
            fontTitleRed.FontHeightInPoints = 10;
            fontTitleRed.Color = HSSFColor.OliveGreen.Red.Index;

            IFont fontTitleBlack = workbook.CreateFont();
            fontTitleBlack.FontName = "宋体";
            fontTitleBlack.IsBold = true;
            fontTitleBlack.FontHeightInPoints = 10;
            fontTitleBlack.Color = HSSFColor.OliveGreen.Black.Index;

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Left;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(fontHeadRed);

            ICellStyle titleRedStyle = workbook.CreateCellStyle();
            titleRedStyle.Alignment = HorizontalAlignment.Center;
            titleRedStyle.VerticalAlignment = VerticalAlignment.Center;
            titleRedStyle.BorderTop = BorderStyle.Thin;
            titleRedStyle.BorderLeft = BorderStyle.Thin;
            titleRedStyle.BorderRight = BorderStyle.Thin;
            titleRedStyle.BorderBottom = BorderStyle.Thin;
            titleRedStyle.SetFont(fontTitleRed);

            ICellStyle titleBlackStyle = workbook.CreateCellStyle();
            titleBlackStyle.Alignment = HorizontalAlignment.Center;
            titleBlackStyle.VerticalAlignment = VerticalAlignment.Center;
            titleBlackStyle.BorderTop = BorderStyle.Thin;
            titleBlackStyle.BorderLeft = BorderStyle.Thin;
            titleBlackStyle.BorderRight = BorderStyle.Thin;
            titleBlackStyle.BorderBottom = BorderStyle.Thin;
            titleBlackStyle.SetFont(fontTitleBlack);

            int rangeLength = list.Count - 1;
            if (rangeLength <= 0)
            {
                rangeLength = 1;
            }
            //行
            IRow row = null;
            //列
            NPOI.SS.UserModel.ICell cell = null;
            int rowIndex = 0;
            int cellIndex = 0;

            row = sheet.CreateRow(rowIndex);
            row.HeightInPoints = 22;
            cell = row.CreateCell(cellIndex);
            cell.SetCellValue(headText);
            CellRangeAddress region = new CellRangeAddress(0, 0, cellIndex, rangeLength);
            sheet.AddMergedRegion(region);
            cell.CellStyle = headStyle;

            rowIndex++;
            row = sheet.CreateRow(rowIndex);
            row.HeightInPoints = 22;

            foreach (WfProjectListFieldSet f in list)
            {
                string title = f.Title;
                cell = row.CreateCell(cellIndex);
                if (f.IsRequired == 1)
                {
                    title = "*" + title;
                    cell.SetCellValue(title);
                    sheet.SetColumnWidth(cellIndex, 80 * f.Width);
                    cell.CellStyle = titleRedStyle;
                }
                else
                {
                    cell.SetCellValue(title);
                    sheet.SetColumnWidth(cellIndex, 80 * f.Width);
                    cell.CellStyle = titleBlackStyle;
                }
                cellIndex++;
            }

            //在插入10行空的
            for (int i = 0; i < 10; i++)
            {
                rowIndex++;
                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 22;
                cellIndex = 0;

                foreach (WfProjectListFieldSet f in list)
                {
                    cell = row.CreateCell(cellIndex);
                    cell.SetCellValue("");
                    sheet.SetColumnWidth(cellIndex, 80 * f.Width);
                    cell.CellStyle = titleBlackStyle;
                    cellIndex++;
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        /// <summary>
        /// 动态导出查询统计数据
        /// </summary>
        /// <param name="sheetName"></param>
        /// <param name="pg"></param>
        /// <param name="listColumn"></param>
        /// <param name="totalShowName"></param>
        /// <returns></returns>
        private async Task<byte[]> CreateQueryMemoryStream(string sheetName,PageModel<dynamic> pg, List<WfPageColumnConfig> listColumn,string totalShowName)
        {
            return await Task.Run(() =>
            {
                HSSFWorkbook workbook = new HSSFWorkbook();
                ISheet sheet = workbook.CreateSheet(sheetName);

                //红色字体
                IFont fontHeadRed = workbook.CreateFont();
                fontHeadRed.FontName = "宋体";
                fontHeadRed.IsBold = false;
                fontHeadRed.FontHeightInPoints = 10;
                fontHeadRed.Color = HSSFColor.OliveGreen.Red.Index;

                IFont fontTitleRed = workbook.CreateFont();
                fontTitleRed.FontName = "宋体";
                fontTitleRed.IsBold = true;
                fontTitleRed.FontHeightInPoints = 10;
                fontTitleRed.Color = HSSFColor.OliveGreen.Red.Index;

                IFont fontTitleBlack = workbook.CreateFont();
                fontTitleBlack.FontName = "宋体";
                fontTitleBlack.IsBold = true;
                fontTitleBlack.FontHeightInPoints = 10;
                fontTitleBlack.Color = HSSFColor.OliveGreen.Black.Index;


                IFont fontCommon = workbook.CreateFont();
                fontCommon.FontName = "宋体";
                fontCommon.IsBold = false;
                fontCommon.FontHeightInPoints = 10;
                fontCommon.Color = HSSFColor.OliveGreen.Black.Index;

                ICellStyle headStyle = workbook.CreateCellStyle();
                headStyle.Alignment = HorizontalAlignment.Left;
                headStyle.VerticalAlignment = VerticalAlignment.Center;
                headStyle.BorderTop = BorderStyle.Thin;
                headStyle.BorderLeft = BorderStyle.Thin;
                headStyle.BorderRight = BorderStyle.Thin;
                headStyle.BorderBottom = BorderStyle.Thin;
                headStyle.SetFont(fontHeadRed);

                ICellStyle titleRedStyle = workbook.CreateCellStyle();
                titleRedStyle.Alignment = HorizontalAlignment.Center;
                titleRedStyle.VerticalAlignment = VerticalAlignment.Center;
                titleRedStyle.BorderTop = BorderStyle.Thin;
                titleRedStyle.BorderLeft = BorderStyle.Thin;
                titleRedStyle.BorderRight = BorderStyle.Thin;
                titleRedStyle.BorderBottom = BorderStyle.Thin;
                titleRedStyle.SetFont(fontTitleRed);

                ICellStyle titleBlackStyle = workbook.CreateCellStyle();
                titleBlackStyle.Alignment = HorizontalAlignment.Center;
                titleBlackStyle.VerticalAlignment = VerticalAlignment.Center;
                titleBlackStyle.BorderTop = BorderStyle.Thin;
                titleBlackStyle.BorderLeft = BorderStyle.Thin;
                titleBlackStyle.BorderRight = BorderStyle.Thin;
                titleBlackStyle.BorderBottom = BorderStyle.Thin;
                titleBlackStyle.SetFont(fontTitleBlack);


                //居右加粗
                ICellStyle titleRight = workbook.CreateCellStyle();
                titleRight.Alignment = HorizontalAlignment.Right;
                titleRight.VerticalAlignment = VerticalAlignment.Center;
                titleRight.BorderTop = BorderStyle.Thin;
                titleRight.BorderLeft = BorderStyle.Thin;
                titleRight.BorderRight = BorderStyle.Thin;
                titleRight.BorderBottom = BorderStyle.Thin;
                titleRight.SetFont(fontTitleBlack);


                //
                ICellStyle contentLeft = workbook.CreateCellStyle();
                contentLeft.Alignment = HorizontalAlignment.Left;
                contentLeft.VerticalAlignment = VerticalAlignment.Center;
                contentLeft.BorderTop = BorderStyle.Thin;
                contentLeft.BorderLeft = BorderStyle.Thin;
                contentLeft.BorderRight = BorderStyle.Thin;
                contentLeft.BorderBottom = BorderStyle.Thin;
                contentLeft.SetFont(fontCommon);

                ICellStyle contentRight = workbook.CreateCellStyle();
                contentRight.Alignment = HorizontalAlignment.Right;
                contentRight.VerticalAlignment = VerticalAlignment.Center;
                contentRight.BorderTop = BorderStyle.Thin;
                contentRight.BorderLeft = BorderStyle.Thin;
                contentRight.BorderRight = BorderStyle.Thin;
                contentRight.BorderBottom = BorderStyle.Thin;
                contentRight.SetFont(fontCommon);

                ICellStyle contentCenter = workbook.CreateCellStyle();
                contentCenter.Alignment = HorizontalAlignment.Center;
                contentCenter.VerticalAlignment = VerticalAlignment.Center;
                contentCenter.BorderTop = BorderStyle.Thin;
                contentCenter.BorderLeft = BorderStyle.Thin;
                contentCenter.BorderRight = BorderStyle.Thin;
                contentCenter.BorderBottom = BorderStyle.Thin;
                contentCenter.SetFont(fontCommon);

                //行
                IRow row = null;
                //列
                NPOI.SS.UserModel.ICell cell = null;
                int rowIndex = 0;
                int cellIndex = 0;

                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 22;

                foreach (WfPageColumnConfig f in listColumn)
                {
                    string title = f.FieldName;
                    cell = row.CreateCell(cellIndex);
                    cell.SetCellValue(title);
                    sheet.SetColumnWidth(cellIndex, 80 * f.Width);
                    cell.CellStyle = titleBlackStyle;
                    cellIndex++;
                }

                string contentValue = string.Empty;

                foreach (var item in pg.data)
                {
                    rowIndex++;
                    row = sheet.CreateRow(rowIndex);
                    row.HeightInPoints = 22;
                    cellIndex = 0;

                    foreach (WfPageColumnConfig f in listColumn)
                    {
                        cell = row.CreateCell(cellIndex);
                        object content = GetPropertyValue(item, f.FieldCode) ?? "";
                        if (content != null)
                        {
                            contentValue = content.ToString();
                            decimal moneyValue = 0;
                            decimal.TryParse(content.ToString(), out moneyValue);
                            if (moneyValue > 0)
                            {
                                contentValue = moneyValue.ToString("0.####");
                            }
                        }
                        cell.SetCellValue(contentValue);
                        switch (f.ContentStyle)
                        {
                            case "center":
                                cell.CellStyle = contentCenter;
                                break;
                            case "left":
                                cell.CellStyle = contentLeft;
                                break;
                            case "right":
                                cell.CellStyle = contentRight;
                                break;
                        }
                        sheet.SetColumnWidth(cellIndex, 80 * f.Width);
                        cellIndex++;
                    }
                }

                //判断是否有总计
                if (pg.Statistics != null)
                {
                    var objStatic = pg.Statistics;

                    Type type = objStatic.GetType();
                    string displayColumn = string.Empty;
                    PropertyInfo displayColumnProp = type.GetProperty("displayColumn");
                    if (displayColumnProp != null)
                    {
                        displayColumn = displayColumnProp.GetValue(objStatic) as string;
                    }

                    if (displayColumn != null)
                    {
                        // 获取 listSum 属性
                        PropertyInfo listSumProp = type.GetProperty("listSum");
                        if (listSumProp != null)
                        {
                            rowIndex++;
                            row = sheet.CreateRow(rowIndex);
                            row.HeightInPoints = 22;
                            cellIndex = 0;
                            string sumValue = string.Empty;
                            List<ListStatisticsModel> listSum = listSumProp.GetValue(objStatic) as List<ListStatisticsModel>;
                            if (listSum.Count > 0)
                            {
                                foreach (WfPageColumnConfig f in listColumn)
                                {
                                    sumValue = "";
                                    if (cellIndex == 0)
                                    {
                                        sumValue = totalShowName;

                                        cell = row.CreateCell(cellIndex);
                                        cell.SetCellValue(sumValue);
                                        cell.CellStyle = titleBlackStyle;
                                    }
                                    else
                                    {
                                        var objSum = listSum.Where(zyf => zyf.Code == f.FieldCode).FirstOrDefault();
                                        if (objSum != null)
                                        {
                                            sumValue = objSum.SumValue.ToString("0.####");
                                        }

                                        cell = row.CreateCell(cellIndex);
                                        cell.SetCellValue(sumValue);
                                        cell.CellStyle = titleRight;
                                    }


                                    sheet.SetColumnWidth(cellIndex, 80 * f.Width);
                                    cellIndex++;
                                }
                            }
                        }
                    }
                }

                // 写入内存流
                using var stream = new MemoryStream();
                workbook.Write(stream, true);
                return stream.ToArray();
            });
        }

        /// <summary>
        /// 读取动态对象
        /// </summary>
        /// <param name="item"></param>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        private object GetPropertyValue(dynamic item, string propertyName)
        {
            try
            {
                if (item is IDictionary<string, object> dict)
                {
                    if (dict.TryGetValue(propertyName, out object value))
                    {
                        return value;
                    }
                }
                else if (item is ExpandoObject)
                {
                    return ((IDictionary<string, object>)item)[propertyName];
                }
                else
                {
                    Type type = item.GetType();
                    PropertyInfo prop = type.GetProperty(propertyName);
                    if (prop != null && prop.CanRead)
                    {
                        return prop.GetValue(item);
                    }
                }
            }
            catch
            {
                return null;
            }
            return null;
        }


        /// <summary>
        /// 审核审批消息发送
        /// </summary>
        /// <param name="processId">流程Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <param name="projectDeclarationId">填报Id</param>
        /// <param name="msgCode">消息编号</param>
        /// <returns></returns>
        private async Task ProcessMsgSend(long processId, long processNodeId, long projectDeclarationId, string msgCode)
        {
            var objMsgConfig = msgConfigManager.Query(f => f.ProcessId == processId && f.ProcessNodeId == processNodeId && f.MsgCode == msgCode).Result.FirstOrDefault();
            if (objMsgConfig != null && objMsgConfig.MainSwitch == 1)
            {
                string userPhone = string.Empty;
                string content = objMsgConfig.MsgTemplate;
                var objDeclaration = projectDeclarationManager.Query(f => f.Id == projectDeclarationId).Result.FirstOrDefault();
                if (objDeclaration != null && objDeclaration.Statuz != 1000)
                {
                    //退回
                    if (msgCode.Equals("100100202"))
                    {
                        var objUser = await sysUserExtensionManager.QueryById(objDeclaration.CreateId);
                        if (objUser != null)
                        {
                            userPhone = objUser.Mobile;
                        }
                    }
                    else
                    {
                        userPhone = await projectDeclarationManager.GetUserMobileInfo(objDeclaration, processNodeId);
                    }
                    //短信开启
                    if (objMsgConfig.MsgSwitch == 1 && !string.IsNullOrEmpty(userPhone) && !string.IsNullOrEmpty(content))
                    {
                        content = content.Replace("[projectname]", objDeclaration.ProjectName).Replace("[status]", objDeclaration.StatuzDesc);
                        await SendMessage.SendToMobile(userPhone, content);
                    }

                    //微信消息推送开启
                    if (objMsgConfig.WeChatSwitch == 1)
                    {

                    }
                }
            }

        }
    }
    #endregion

}
