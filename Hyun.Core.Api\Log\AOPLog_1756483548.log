--------------------------------
2025/8/29 16:05:47|
【操作时间】：2025-08-29 04:05:47 282
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： d => ((d.AppType == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_1).CS$<>8__locals1.appType, Nullable`1)) AndAlso value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_1).pids.Contains(d.Id)) 
【携带的参数JSON】：  
【响应时间】：59ms
【执行完成时间】：2025-08-29 04:05:47 341
【执行完成结果】：[{"Code":"@url","Name":"校服选用","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1100000,"Icon":"cdxiaofuxuanyong","Description":"市级管理员、区县管理员、学校管理员、学校班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000010},{"Code":"@url","Name":"校服采购","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1300000,"Icon":"cdxiaofucaigou","Description":"学校班主任、学校管理员、区县管理员、市级管理员、企业管理员（供应商）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000020},{"Code":"@url","Name":"选用组织","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1200000,"Icon":"cdxuanyongzuzhi","Description":"学校管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000030},{"Code":"@url","Name":"校服调换","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1500000,"Icon":"cdxiaofudiaohuan","Description":"学校管理员、企业管理员（供应商）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000040},{"Code":"@url","Name":"校服资助","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1600000,"Icon":"cdxiaofuzizhu","Description":"学校管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000050},{"Code":"@url","Name":"校服评价","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1700000,"Icon":"cdxiaofupingjia","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000000060},{"Code":"@url","Name":"平台设置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5600000,"Icon":"cdpingtaishezhi","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":***************},{"Code":"selection/project/list","Name":"选用方案","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1101000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001001},{"Code":"selection/project/edit","Name":"创建/修改方案","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1105000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001002},{"Code":"selection/solicit/list","Name":"征求结果","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1106000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001003},{"Code":"selection/solicit/opiniondetail","Name":"意见详情","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1107000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000010,"Mid":0,"PidArr":null,"Id":100000000001004},{"Code":"purchase/management/list","Name":"采购管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1301000,"Icon":"","Description":"市级管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002001},{"Code":"purchase/management/edit","Name":"新增/修改采购","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1303000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002002},{"Code":"@url","Name":"校服审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1312000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002008},{"Code":"purchase/audit/auditlist","Name":"待审核校服","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1312001,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002008,"Mid":0,"PidArr":null,"Id":100000000002009},{"Code":"purchase/audit/auditedlist","Name":"已审核校服","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1312002,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002008,"Mid":0,"PidArr":null,"Id":100000000002010},{"Code":"@url","Name":"校服征订","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1400000,"Icon":"cdxiaofuzhengding","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000002011},{"Code":"purchase/solicitsubscrip/orderlist","Name":"征订单管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1401000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002012},{"Code":"purchase/solicitsubscrip/orderedit","Name":"生成/修改征订单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1402000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002013},{"Code":"purchase/solicitsubscrip/orderexamine","Name":"查看征订单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1403000,"Icon":"","Description":"学校管理员、区县管理员、市级管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002014},{"Code":"purchase/solicitsubscrip/orderdetail","Name":"征订单明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1404000,"Icon":"","Description":"学校管理员、区县管理员、市级管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000002011,"Mid":0,"PidArr":null,"Id":100000000002015},{"Code":"purchase/management/detail","Name":"采购管理查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1308000,"Icon":"","Description":"学校管理员、区县管理员、市级管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000020,"Mid":0,"PidArr":null,"Id":100000000002017},{"Code":"organization/organize/editlist","Name":"组建选用组织","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1201000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000030,"Mid":0,"PidArr":null,"Id":100000000003001},{"Code":"exchange/launch/list","Name":"调换管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1501000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000040,"Mid":0,"PidArr":null,"Id":100000000004001},{"Code":"user/teacheraffair/launch","Name":"校服调换","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2203000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000004002},{"Code":"exchange/swaporder/list","Name":"查看调换单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1505000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000040,"Mid":0,"PidArr":null,"Id":100000000004003},{"Code":"exchange/swaporder/detail","Name":"调换明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1507000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000040,"Mid":0,"PidArr":null,"Id":100000000004004},{"Code":"subsidize/subsidize/list","Name":"资助信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1601000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000050,"Mid":0,"PidArr":null,"Id":100000000005001},{"Code":"evaluate/list","Name":"评价管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1701000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006001},{"Code":"evaluate/edit","Name":"创建/修改评价","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1703000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006002},{"Code":"evaluate/examinelist","Name":"查看评价","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":1704000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006003},{"Code":"evaluate/examinedetail","Name":"评价明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":1705000,"Icon":"","Description":"学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000000060,"Mid":0,"PidArr":null,"Id":100000000006004},{"Code":"@url","Name":"班主任事务","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2200000,"Icon":"cdbanzhurenshiwu","Description":"学校班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":100000000007001},{"Code":"user/teacheraffair/studentmanage","Name":"学生管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2201000,"Icon":"","Description":"学校班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007002},{"Code":"user/backset/unit","Name":"单位信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2307001,"Icon":"","Description":"（校服）学校管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":***************},{"Code":"user/backset/useraccount","Name":"用户账号","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2309000,"Icon":"","Description":"市级管理员、区县管理员、学校管理员、企业管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":***************},{"Code":"user/backset/gradeclass","Name":"年级班级","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2310000,"Icon":"","Description":"（校服）学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":100000000007006},{"Code":"user/backset/student","Name":"学生名单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2311000,"Icon":"","Description":"（校服）学校管理员","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":100000000007007},{"Code":"user/teacheraffair/solicitsubscrip","Name":"校服征订","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2202000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007014},{"Code":"@url","Name":"个人中心","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2388000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":100000000007017},{"Code":"user/my/useredit","Name":"个人信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2328001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007017,"Mid":0,"PidArr":null,"Id":100000000007018},{"Code":"user/my/changepass","Name":"修改密码","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2328002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007017,"Mid":0,"PidArr":null,"Id":100000000007019},{"Code":"user/teacheraffair/orderdetail","Name":"征订单明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2204000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007020},{"Code":"user/teacheraffair/launchdetail","Name":"调换明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2205000,"Icon":"","Description":"班主任","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":100000000007001,"Mid":0,"PidArr":null,"Id":100000000007021},{"Code":"approvalconfiguration/pages/node/formedit","Name":"填报修改","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814852328300482560},{"Code":"approvalconfiguration/pages/node/detail","Name":"查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814852564905365504},{"Code":"approvalconfiguration/pages/node/examine","Name":"审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814852943126728704},{"Code":"approvalconfiguration/pages/node/processedlist","Name":"已处理","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814853692145537024},{"Code":"approvalconfiguration/pages/node/pendinglist","Name":"待处理","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814853925042655232},{"Code":"approvalconfiguration/pages/statistics/list","Name":"资金库","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814855159220801536},{"Code":"approvalconfiguration/workflow/node/conditionset","Name":"条件控制","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":796311544399925248,"Mid":0,"PidArr":null,"Id":814855268880879616},{"Code":"approvalconfiguration/pages/treasury/list","Name":"查询统计","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8009,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814855791944142848},{"Code":"approvalconfiguration/pages/treasury/detail","Name":"查询统计查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8011,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814855881240875008},{"Code":"approvalconfiguration/pages/node/projectlist","Name":"项目清单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":11101,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":814916365319147520},{"Code":"approvalconfiguration/business/permission","Name":"业务授权","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2324000,"Icon":"","Description":"（非校服）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":814917889516638208},{"Code":"@url","Name":"工作流填报","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2500000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":815152216284336128},{"Code":"@url","Name":"节点","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":11001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152216284336128,"Mid":0,"PidArr":null,"Id":815152703427579904},{"Code":"dangerchemicals/daily/governdeclarelist@t=1","Name":"周申报列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512156001800192},{"Code":"dangerchemicals/daily/governitemreport@t=2","Name":"月排查申报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512500404490240},{"Code":"dangerchemicals/daily/governdeclarelist@t=2","Name":"月申报列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512620718100480},{"Code":"dangerchemicals/daily/dcgovernrectifylist","Name":"问题隐患清单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":815512817057665024},{"Code":"approvalconfiguration/pages/treasury/listitem","Name":"查询统计列表","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":8010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":827558788750905344},{"Code":"approvalconfiguration/pages/node/projectexaminelist","Name":"项目清单审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":12104,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":827558788880928768},{"Code":"approvalconfiguration/pages/node/projectdetail","Name":"项目清单查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":12105,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":815152703427579904,"Mid":0,"PidArr":null,"Id":827558790864834560},{"Code":"@url","Name":"安全排查","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957453845008384,"Mid":0,"PidArr":null,"Id":827558795155607552},{"Code":"dangerchemicals/daily/governitemreport@t=1","Name":"周排查申报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":10002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":827558797206622208},{"Code":"@URL","Name":"应用管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5700000,"Icon":"cdyingyongguanli","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":840599930178375680},{"Code":"user/application/list","Name":"应用列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5701000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":840599930178375680,"Mid":0,"PidArr":null,"Id":840600506052120576},{"Code":"user/application/add","Name":"应用添加修改","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":5702000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":840599930178375680,"Mid":0,"PidArr":null,"Id":842694325652426752},{"Code":"@url","Name":"危化品治理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2600000,"Icon":"cdweihuapinzhili","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":844957453845008384},{"Code":"@url","Name":"危化品管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2800000,"Icon":"cdweihuapinguanli","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":844957620178522112},{"Code":"dangerchemicals/daily/governdetail","Name":"申报查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":10008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":827558795155607552,"Mid":0,"PidArr":null,"Id":844961710316982272},{"Code":"@url","Name":"危化品领用","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2802000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844966541932892160},{"Code":"dangerchemicals/apply/carlist","Name":"领用车","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2802003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844972844101144576},{"Code":"dangerchemicals/apply/detailview","Name":"领用查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2802004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844972973948407808},{"Code":"dangerchemicals/apply/filleasy","Name":"领用填报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2802006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844973591006023680},{"Code":"dangerchemicals/applyed/listeasy","Name":"已领用物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2802007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844966541932892160,"Mid":0,"PidArr":null,"Id":844973895587991552},{"Code":"@url","Name":"领用审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2803000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844974175394205696},{"Code":"dangerchemicals/apply/auditlist@p=20","Name":"待审核物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2803002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974175394205696,"Mid":0,"PidArr":null,"Id":844974402880671744},{"Code":"dangerchemicals/apply/auditedlist@p=20","Name":"已审核物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2803003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974175394205696,"Mid":0,"PidArr":null,"Id":844974534216912896},{"Code":"dangerchemicals/apply/audit","Name":"领用审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2803004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974175394205696,"Mid":0,"PidArr":null,"Id":844974696779747328},{"Code":"@url","Name":"危化品配发","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844974828485087232},{"Code":"dangerchemicals/apply/auditlist@p=30","Name":"待配货物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975023654440960},{"Code":"dangerchemicals/apply/confirm","Name":"仓库配货","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2804003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975297731235840},{"Code":"dangerchemicals/apply/givelist","Name":"待发放物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975449124638720},{"Code":"dangerchemicals/apply/givedlist@t=1","Name":"已发放物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2804005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975635729223680},{"Code":"dangerchemicals/apply/print","Name":"打印领用单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2804006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844974828485087232,"Mid":0,"PidArr":null,"Id":844975728242987008},{"Code":"@url","Name":"危化品采购","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844975797407059968},{"Code":"dangerchemicals/purchase/fill","Name":"采购填报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805001,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976309716127744},{"Code":"dangerchemicals/purchase/list","Name":"采购车","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2805003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976490087976960},{"Code":"dangerchemicals/purchase/orderlist","Name":"已填报列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976660540297216},{"Code":"dangerchemicals/purchase/endlist","Name":"采购计划","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2805002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844976914006282240},{"Code":"dangerchemicals/purchase/detaillist","Name":"物品明细列表","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2805007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844975797407059968,"Mid":0,"PidArr":null,"Id":844977126699438080},{"Code":"@url","Name":"采购审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2806000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844977251022802944},{"Code":"dangerchemicals/purchase/auditlist@p=10","Name":"待审核列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2806002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977251022802944,"Mid":0,"PidArr":null,"Id":844977462650605568},{"Code":"dangerchemicals/purchase/auditedlist@p=10","Name":"已审核列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2806003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977251022802944,"Mid":0,"PidArr":null,"Id":844977581781422080},{"Code":"@url","Name":"采购审批","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2807000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844977790431268864},{"Code":"dangerchemicals/purchase/auditlist@p=20","Name":"待审批列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2807002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844977922153385984},{"Code":"dangerchemicals/purchase/auditedlist@p=20","Name":"已审批列表","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2807003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844978006509228032},{"Code":"dangerchemicals/purchase/audit","Name":"采购审核","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2807004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844978207466721280},{"Code":"dangerchemicals/purchase/detailview","Name":"采购查看","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2807005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844977790431268864,"Mid":0,"PidArr":null,"Id":844978326723366912},{"Code":"@url","Name":"危化品入库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844978402241810432},{"Code":"dangerchemicals/input/byplanlist","Name":"按计划入库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844978933500743680},{"Code":"dangerchemicals/school/itemstorage","Name":"存量录入","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979146500083712},{"Code":"dangerchemicals/school/materialstoragelog","Name":"入库记录","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979303874564096},{"Code":"dangerchemicals/school/purchasestorageauditlist","Name":"采购入库审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979454731096064},{"Code":"dangerchemicals/input/materialbyplan","Name":"计划入库","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2808007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979648616992768},{"Code":"dangerchemicals/school/storagelogprint","Name":"打印入库单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2808008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":844979828049317888},{"Code":"@url","Name":"危化品库管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844980359450857472},{"Code":"dangerchemicals/school/materialcataloglist","Name":"危化品存量库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844980598295498752},{"Code":"dangerchemicals/apply/granteasy","Name":"领用发放","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844980852977831936},{"Code":"dangerchemicals/apply/backlisteasy","Name":"领用退回","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981082699862016},{"Code":"dangerchemicals/apply/givedlist@t=2","Name":"已领用物品","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981392927363072},{"Code":"dangerchemicals/material/backlist","Name":"领用退回","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981743516651520},{"Code":"dangerchemicals/school/materialbacklist","Name":"采购退货","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844981921363529728},{"Code":"dangerchemicals/school/materialbacklog","Name":"已退货清单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844982125559025664},{"Code":"dangerchemicals/school/materialbacklogprint","Name":"打印退货单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2809010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844982262398193664},{"Code":"dangerchemicals/scrap/list","Name":"危化品报废","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809015,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983343421329408},{"Code":"dangerchemicals/scraped/list","Name":"已报废清单","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809016,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983416607739904},{"Code":"dangerchemicals/materials/numaudit@t=3","Name":"退货审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809017,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983686142103552},{"Code":"dangerchemicals/materials/numaudit@t=2","Name":"报废审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2809019,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844983906800242688},{"Code":"dangerchemicals/apply/print","Name":"打印领用单","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2809020,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844980359450857472,"Mid":0,"PidArr":null,"Id":844984091039240192},{"Code":"@url","Name":"危废物处置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844984272614854656},{"Code":"dangerchemicals/waste/list","Name":"危废物存量库","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984515939012608},{"Code":"dangerchemicals/waste/disposallisted","Name":"已处置危废物","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984636638498816},{"Code":"dangerchemicals/waste/reportlist","Name":"待处置信息填报","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984773846765568},{"Code":"dangerchemicals/waste/detaillist","Name":"危废物明细","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2810005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844984942273236992},{"Code":"dangerchemicals/waste/auditlist","Name":"处置审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2810006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844985090730627072},{"Code":"dangerchemicals/waste/recordlist","Name":"危废物详情","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2810007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844984272614854656,"Mid":0,"PidArr":null,"Id":844985239972352000},{"Code":"@url","Name":"安全保障要求","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844985309048344576},{"Code":"dangerchemicals/system/teambuild","Name":"制度与队伍建设","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844985864302891008},{"Code":"dangerchemicals/train/safeeducationlist","Name":"培训与安全教育","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986081353928704},{"Code":"dangerchemicals/emergency/planlist","Name":"演练与应急预案","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986204750352384},{"Code":"dangerchemicals/msds/list","Name":"查看MSDS","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986319925940224},{"Code":"dangerchemicals/word/guidelist","Name":"工作指导","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2811006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844985309048344576,"Mid":0,"PidArr":null,"Id":844986470987993088},{"Code":"@url","Name":"查询统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844988224278368256},{"Code":"dangerchemicals/purchase/yearnumstatistics","Name":"采购数量统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844988795462881280},{"Code":"dangerchemicals/apply/yearnumstatistics","Name":"领用数量统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844988925083652096},{"Code":"dangerchemicals/apply/usernumstatistics","Name":"按领用人统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2813006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844989069602590720},{"Code":"@url","Name":"配置信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844992020215762944},{"Code":"dangerchemicals/school/materialmodelset","Name":"危化品信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992359836946432},{"Code":"dangerchemicals/company/list","Name":"供应商信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992477537505280},{"Code":"dangerchemicals/apply/grantuserset@t=1","Name":"领用人配置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992873186201600},{"Code":"dangerchemicals/apply/grantuserset@t=2","Name":"发放人配置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844992931449278464},{"Code":"dangerchemicals/base/configset@m=9&opt=0&t=领用参数配置","Name":"领用参数配置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844993347960442880},{"Code":"dangerchemicals/deposit/addresslist","Name":"存放地点设置","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2814008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":844993496422027264},{"Code":"@url","Name":"台账打印","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844957620178522112,"Mid":0,"PidArr":null,"Id":844997017473126400},{"Code":"dangerchemicals/standbook/purchase","Name":"采购台账","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816002,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997280166580224},{"Code":"dangerchemicals/standbook/purchaseprint","Name":"采购台账打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816003,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997405890842624},{"Code":"dangerchemicals/standbook/apply","Name":"领用台账","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816004,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997569112182784},{"Code":"dangerchemicals/standbook/applyprint","Name":"领用台账打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816005,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997754236178432},{"Code":"dangerchemicals/standbook/stock","Name":"存量台账","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997884142161920},{"Code":"dangerchemicals/standbook/stockprint","Name":"存量台账打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816007,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844997017473126400,"Mid":0,"PidArr":null,"Id":844997950672211968},{"Code":"dangerchemicals/standbook/waste","Name":"危废物统计","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2816008,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844998050886717440},{"Code":"dangerchemicals/standbook/wasteprint","Name":"危废物统计打印","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2816009,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844988224278368256,"Mid":0,"PidArr":null,"Id":844998119732023296},{"Code":"dangerchemicals/school/itemstorageauditlist","Name":"存量入库审核","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2808006,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844978402241810432,"Mid":0,"PidArr":null,"Id":845255096391438336},{"Code":"user/department/usermanager","Name":"部门用户列表","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2308400,"Icon":"","Description":"非校服（学校）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":852931579138281472},{"Code":"user/department/address","Name":"地点管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2309200,"Icon":"","Description":"非校服（学校）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":852932447195631616},{"Code":"user/department/manager","Name":"部门管理","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2308330,"Icon":"","Description":"非校服（学校）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":853564351586504704},{"Code":"dangerchemicals/school/modelbranddisablelist","Name":"规格品牌配置","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2814009,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":855107575388049408},{"Code":"dangerchemicals/school/modelbrand","Name":"规格品牌配置","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2814010,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":844992020215762944,"Mid":0,"PidArr":null,"Id":855107866284003328},{"Code":"approvalconfiguration/business/classification","Name":"分级授权","IsButton":false,"IsHide":true,"IskeepAlive":false,"Func":"","OrderSort":2324100,"Icon":"","Description":"（非校服）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":870714919371149312},{"Code":"@url","Name":"装备登记","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":3000000,"Icon":"cdzhuangbeidengji","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":0,"Mid":0,"PidArr":null,"Id":871030740077252608},{"Code":"thequipment/record/list","Name":"装备登记","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":3002000,"Icon":"","Description":"","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":871030740077252608,"Mid":0,"PidArr":null,"Id":872554852792668160},{"Code":"user/backset/newunit","Name":"单位信息","IsButton":false,"IsHide":false,"IskeepAlive":false,"Func":"","OrderSort":2306000,"Icon":"","Description":"（非校服）","Enabled":true,"CreateId":0,"CreateBy":"","CreateTime":"2025-08-29T11:04:33","ModifyId":0,"ModifyBy":"","ModifyTime":"2025-08-29T11:04:33","IsDeleted":false,"PnameArr":[],"PCodeArr":[],"MName":null,"hasChildren":true,"Children":[],"Module":null,"SqlText":"","PageCode":"    ","PlatformType":0,"AppType":1,"Pid":***************,"Mid":0,"PidArr":null,"Id":875322682860638208}]

--------------------------------
2025/8/29 16:05:47|
【操作时间】：2025-08-29 04:05:47 570
【当前操作用户】：yucai 
【当前执行方法】：GetModuleMenuList 
【携带的参数有】：  
【携带的参数JSON】：  
【响应时间】：201ms
【执行完成时间】：2025-08-29 04:05:47 772
【执行完成结果】：[{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":658679471378565,"ProcessNodeName":"学校填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":658679471378565,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":658681824243845,"ProcessNodeName":"教导主任审核","NodeType":2,"TreatHandle":"待审核","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审核","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":658681824243845,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":658689418428549,"ProcessName":"学校信息填报","ProcessNodeId":658682131247237,"ProcessNodeName":"校长审核","NodeType":2,"TreatHandle":"待审核","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审核","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":658682131247237,"ModuleSort":0},{"ModuleId":658679134371973,"ModuleName":"采购审批","Logo":"el-icon-ice-tea","ProcessId":670258683539589,"ProcessName":"测试流程","ProcessNodeId":661475211567237,"ProcessNodeName":"填报01","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":661475211567237,"ModuleSort":0},{"ModuleId":658679134371973,"ModuleName":"采购审批","Logo":"el-icon-ice-tea","ProcessId":670258683539589,"ProcessName":"测试流程","ProcessNodeId":661475211567237,"ProcessNodeName":"填报01","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":661475211567237,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667815747584133,"ProcessNodeName":"需求填报","NodeType":1,"TreatHandle":"项目列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":667815747584133,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667839088402565,"ProcessNodeName":"需求审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":667839088402565,"ModuleSort":0},{"ModuleId":667811814981765,"ModuleName":"需求管理","Logo":"el-icon-orange","ProcessId":667848427954309,"ProcessName":"需求管理","ProcessNodeId":667839181226117,"ProcessNodeName":"财务审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":667839181226117,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":676389824655493,"ProcessName":"两个起始节点——测试","ProcessNodeId":676389425160325,"ProcessNodeName":"两个起始节点-学校填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":676389425160325,"ModuleSort":0},{"ModuleId":658679024259205,"ModuleName":"计划编制","Logo":"el-icon-lollipop","ProcessId":676389824655493,"ProcessName":"两个起始节点——测试","ProcessNodeId":676389492240517,"ProcessNodeName":"两个起始节点-学校审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":676389492240517,"ModuleSort":0},{"ModuleId":702534294601861,"ModuleName":"资金库测试","Logo":"el-icon-lollipop","ProcessId":702534674399365,"ProcessName":"资金库测试流程","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":712848436027525,"ProcessNodeName":"测试开始节点","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":0,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":20000,"ProcessNodeName":"","NodeType":0,"TreatHandle":"","TreatHandleUrl":"","StopHandle":"","StopHandleUrl":"","IsBegin":0,"Sort":0,"ModuleSort":1},{"ModuleId":674228238864517,"ModuleName":"多人审批","Logo":"el-icon-lollipop","ProcessId":674230575259781,"ProcessName":"多人审批项目","ProcessNodeId":674228986634373,"ProcessNodeName":"多人学校审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":674228986634373,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675274736050309,"ProcessName":"多条件流程","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":675249294651525,"ModuleName":"多条件测试","Logo":"el-icon-lollipop","ProcessId":675276588191877,"ProcessName":"多条件流程ABC","ProcessNodeId":675249516048517,"ProcessNodeName":"学校多条件审核","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":675249516048517,"ModuleSort":1},{"ModuleId":701431378894981,"ModuleName":"暂存模块","Logo":"el-icon-lollipop","ProcessId":701436251205765,"ProcessName":"测试流程-暂存","ProcessNodeId":701431612588165,"ProcessNodeName":"学校填报-暂存","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":701431612588165,"ModuleSort":1},{"ModuleId":701431378894981,"ModuleName":"暂存模块","Logo":"el-icon-lollipop","ProcessId":701436251205765,"ProcessName":"测试流程-暂存","ProcessNodeId":701431842508933,"ProcessNodeName":"学校审核-暂存","NodeType":2,"TreatHandle":"待审核列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审核列表","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":701431842508933,"ModuleSort":1},{"ModuleId":701431378894981,"ModuleName":"暂存模块","Logo":"el-icon-lollipop","ProcessId":701436251205765,"ProcessName":"测试流程-暂存","ProcessNodeId":701432050229381,"ProcessNodeName":"学校审批-暂存","NodeType":2,"TreatHandle":"待审批列表","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已审批列表","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":701432050229381,"ModuleSort":1},{"ModuleId":702534294601861,"ModuleName":"资金库测试","Logo":"el-icon-lollipop","ProcessId":702534674399365,"ProcessName":"资金库测试流程","ProcessNodeId":702534414794885,"ProcessNodeName":"学校填报-资金库测试","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":702534414794885,"ModuleSort":1},{"ModuleId":702534294601861,"ModuleName":"资金库测试","Logo":"el-icon-lollipop","ProcessId":702534674399365,"ProcessName":"资金库测试流程","ProcessNodeId":702534572294277,"ProcessNodeName":"学校审批-资金库测试","NodeType":2,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":2,"Sort":702534572294277,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":703914263199877,"ProcessName":"项目审批流程","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":704429713551493,"ProcessName":"项目审批测试流程1","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":703912462606469,"ModuleName":"项目审批","Logo":"el-icon-ice-drink","ProcessId":704429713551493,"ProcessName":"项目审批测试流程1","ProcessNodeId":703912581980293,"ProcessNodeName":"项目填报","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":703912581980293,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":705091701473413,"ProcessNodeName":"项目填报-指定审核人","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":705091701473413,"ModuleSort":1},{"ModuleId":705091520331909,"ModuleName":"指定审核人","Logo":"el-icon-tableware","ProcessId":705106779930757,"ProcessName":"指定审核人流程","ProcessNodeId":705091701473413,"ProcessNodeName":"项目填报-指定审核人","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":705091701473413,"ModuleSort":1},{"ModuleId":698593460789381,"ModuleName":"一个节点模块","Logo":"el-icon-milk-tea","ProcessId":698593675456645,"ProcessName":"一个节点流程","ProcessNodeId":698593592631429,"ProcessNodeName":"学校填报-一个节点","NodeType":1,"TreatHandle":"待处理","TreatHandleUrl":"approvalconfiguration/pages/node/pendinglist","StopHandle":"已处理","StopHandleUrl":"approvalconfiguration/pages/node/processedlist","IsBegin":1,"Sort":698593592631429,"ModuleSort":10}]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 055
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => (f.IsDeleted == False) 
【携带的参数JSON】：  
【响应时间】：34ms
【执行完成时间】：2025-08-29 04:05:48 090
【执行完成结果】：[{"ProcessId":658689418428549,"ProcessNodeId":658679471378565,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.593","Version":0,"Id":658689964335237},{"ProcessId":658689418428549,"ProcessNodeId":658679471378565,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339333},{"ProcessId":658689418428549,"ProcessNodeId":658681824243845,"Statuz":2,"StatuzDesc":"等待教导主任审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339334},{"ProcessId":658689418428549,"ProcessNodeId":658681824243845,"Statuz":5,"StatuzDesc":"教导主任审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339335},{"ProcessId":658689418428549,"ProcessNodeId":658682131247237,"Statuz":6,"StatuzDesc":"等待校长审核","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339336},{"ProcessId":658689418428549,"ProcessNodeId":658682131247237,"Statuz":9,"StatuzDesc":"校长审核退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339337},{"ProcessId":658689418428549,"ProcessNodeId":658682545234053,"Statuz":10,"StatuzDesc":"等待区县审批","WaitOrBack":1,"IsEnable":1,"Sort":10,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339338},{"ProcessId":658689418428549,"ProcessNodeId":658682545234053,"Statuz":13,"StatuzDesc":"区县审批退回","WaitOrBack":2,"IsEnable":1,"Sort":13,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-03-26T16:36:21.597","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-03-26T16:36:21.597","Version":0,"Id":658689964339339},{"ProcessId":661475468013701,"ProcessNodeId":661475211567237,"Statuz":0,"StatuzDesc":"等待采购审批01","WaitOrBack":1,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.453","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893701},{"ProcessId":661475468013701,"ProcessNodeId":661475211567237,"Statuz":3,"StatuzDesc":"采购审批01退回","WaitOrBack":2,"IsEnable":1,"Sort":3,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.457","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893702},{"ProcessId":661475468013701,"ProcessNodeId":661478208483461,"Statuz":4,"StatuzDesc":"等待审批02","WaitOrBack":1,"IsEnable":1,"Sort":4,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.457","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893703},{"ProcessId":661475468013701,"ProcessNodeId":661478208483461,"Statuz":7,"StatuzDesc":"审批02退回","WaitOrBack":2,"IsEnable":1,"Sort":7,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-03T13:42:02.457","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-03T13:42:02.453","Version":0,"Id":661478278893704},{"ProcessId":667848427954309,"ProcessNodeId":667815747584133,"Statuz":0,"StatuzDesc":"等待需求填报","WaitOrBack":1,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.883","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121226885},{"ProcessId":667848427954309,"ProcessNodeId":667815747584133,"Statuz":3,"StatuzDesc":"需求填报退回","WaitOrBack":2,"IsEnable":1,"Sort":3,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230981},{"ProcessId":667848427954309,"ProcessNodeId":667839088402565,"Statuz":4,"StatuzDesc":"等待需求审核","WaitOrBack":1,"IsEnable":1,"Sort":4,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230982},{"ProcessId":667848427954309,"ProcessNodeId":667839088402565,"Statuz":7,"StatuzDesc":"需求审核退回","WaitOrBack":2,"IsEnable":1,"Sort":7,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230983},{"ProcessId":667848427954309,"ProcessNodeId":667839181226117,"Statuz":8,"StatuzDesc":"等待财务审核","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230984},{"ProcessId":667848427954309,"ProcessNodeId":667839181226117,"Statuz":11,"StatuzDesc":"财务审核退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230985},{"ProcessId":667848427954309,"ProcessNodeId":667839300063365,"Statuz":12,"StatuzDesc":"等待教育局审核","WaitOrBack":1,"IsEnable":1,"Sort":12,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230986},{"ProcessId":667848427954309,"ProcessNodeId":667839300063365,"Statuz":15,"StatuzDesc":"教育局审核退回","WaitOrBack":2,"IsEnable":1,"Sort":15,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-21T13:45:03.887","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-21T13:45:03.883","Version":0,"Id":667849121230987},{"ProcessId":670258683539589,"ProcessNodeId":661475211567237,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581957},{"ProcessId":670258683539589,"ProcessNodeId":661475211567237,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581958},{"ProcessId":670258683539589,"ProcessNodeId":661478208483461,"Statuz":2,"StatuzDesc":"等待审批02","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581959},{"ProcessId":670258683539589,"ProcessNodeId":661478208483461,"Statuz":5,"StatuzDesc":"审批02退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-28T09:09:57.917","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-04-28T09:09:57.913","Version":0,"Id":670258773581960},{"ProcessId":674230575259781,"ProcessNodeId":674228817981573,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642117},{"ProcessId":674230575259781,"ProcessNodeId":674228817981573,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642118},{"ProcessId":674230575259781,"ProcessNodeId":674228986634373,"Statuz":2,"StatuzDesc":"等待多人学校审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642119},{"ProcessId":674230575259781,"ProcessNodeId":674228986634373,"Statuz":5,"StatuzDesc":"多人学校审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642120},{"ProcessId":674230575259781,"ProcessNodeId":674229194723461,"Statuz":6,"StatuzDesc":"等待区县多用户审核","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642121},{"ProcessId":674230575259781,"ProcessNodeId":674229194723461,"Statuz":9,"StatuzDesc":"区县多用户审核退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-09T14:31:38.127","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-09T14:31:38.123","Version":0,"Id":674230665642122},{"ProcessId":675274736050309,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.013","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242911365},{"ProcessId":675274736050309,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915461},{"ProcessId":675274736050309,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915462},{"ProcessId":675274736050309,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915463},{"ProcessId":675274736050309,"ProcessNodeId":675249600512133,"Statuz":6,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915464},{"ProcessId":675274736050309,"ProcessNodeId":675249600512133,"Statuz":9,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:26:06.017","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:26:06.013","Version":0,"Id":675276242915465},{"ProcessId":675276588191877,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163013},{"ProcessId":675276588191877,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163014},{"ProcessId":675276588191877,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163015},{"ProcessId":675276588191877,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163016},{"ProcessId":675276588191877,"ProcessNodeId":675249600512133,"Statuz":6,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163017},{"ProcessId":675276588191877,"ProcessNodeId":675249600512133,"Statuz":9,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T13:28:27.677","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T13:28:27.677","Version":0,"Id":675276823163018},{"ProcessId":675286298050693,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157509},{"ProcessId":675286298050693,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157510},{"ProcessId":675286298050693,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157511},{"ProcessId":675286298050693,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157512},{"ProcessId":675286298050693,"ProcessNodeId":675249600512133,"Statuz":6,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.703","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157513},{"ProcessId":675286298050693,"ProcessNodeId":675249600512133,"Statuz":9,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T14:07:40.707","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T14:07:40.7","Version":0,"Id":675286461157514},{"ProcessId":675315597021317,"ProcessNodeId":675249401610373,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.46","Version":0,"Id":675315746574469},{"ProcessId":675315597021317,"ProcessNodeId":675249401610373,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578565},{"ProcessId":675315597021317,"ProcessNodeId":675249516048517,"Statuz":2,"StatuzDesc":"等待学校多条件审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578566},{"ProcessId":675315597021317,"ProcessNodeId":675249516048517,"Statuz":5,"StatuzDesc":"学校多条件审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578567},{"ProcessId":675315597021317,"ProcessNodeId":675315433488517,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578568},{"ProcessId":675315597021317,"ProcessNodeId":675315433488517,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578569},{"ProcessId":675315597021317,"ProcessNodeId":675249600512133,"Statuz":8,"StatuzDesc":"等待区县多条件审批","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578570},{"ProcessId":675315597021317,"ProcessNodeId":675249600512133,"Statuz":11,"StatuzDesc":"区县多条件审批退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578571},{"ProcessId":675315597021317,"ProcessNodeId":675315513630853,"Statuz":12,"StatuzDesc":"等待区县审批","WaitOrBack":1,"IsEnable":1,"Sort":12,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578572},{"ProcessId":675315597021317,"ProcessNodeId":675315513630853,"Statuz":15,"StatuzDesc":"区县审批退回","WaitOrBack":2,"IsEnable":1,"Sort":15,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-12T16:06:50.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-12T16:06:50.463","Version":0,"Id":675315746578573},{"ProcessId":676375478214789,"ProcessNodeId":676375207424133,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.037","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129861},{"ProcessId":676375478214789,"ProcessNodeId":676375207424133,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129862},{"ProcessId":676375478214789,"ProcessNodeId":676375283605637,"Statuz":2,"StatuzDesc":"等待A测试审批1","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129863},{"ProcessId":676375478214789,"ProcessNodeId":676375283605637,"Statuz":5,"StatuzDesc":"A测试审批1退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129864},{"ProcessId":676375478214789,"ProcessNodeId":676375359160453,"Statuz":6,"StatuzDesc":"等待A测试审批3","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129865},{"ProcessId":676375478214789,"ProcessNodeId":676375359160453,"Statuz":9,"StatuzDesc":"A测试审批3退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T15:59:23.04","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T15:59:23.037","Version":0,"Id":676375597129866},{"ProcessId":676375854522501,"ProcessNodeId":676375808934021,"Statuz":0,"StatuzDesc":"等待A测试需求1","WaitOrBack":1,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937221},{"ProcessId":676375854522501,"ProcessNodeId":676375808934021,"Statuz":3,"StatuzDesc":"A测试需求1退回","WaitOrBack":2,"IsEnable":1,"Sort":3,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937222},{"ProcessId":676375854522501,"ProcessNodeId":676375283605637,"Statuz":4,"StatuzDesc":"等待A测试审批1","WaitOrBack":1,"IsEnable":1,"Sort":4,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937223},{"ProcessId":676375854522501,"ProcessNodeId":676375283605637,"Statuz":7,"StatuzDesc":"A测试审批1退回","WaitOrBack":2,"IsEnable":1,"Sort":7,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937224},{"ProcessId":676375854522501,"ProcessNodeId":676375359160453,"Statuz":8,"StatuzDesc":"等待A测试审批3","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937225},{"ProcessId":676375854522501,"ProcessNodeId":676375359160453,"Statuz":11,"StatuzDesc":"A测试审批3退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:21:12.073","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:21:12.073","Version":0,"Id":676380958937226},{"ProcessId":676389824655493,"ProcessNodeId":676389425160325,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733637},{"ProcessId":676389824655493,"ProcessNodeId":676389425160325,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733638},{"ProcessId":676389824655493,"ProcessNodeId":676389492240517,"Statuz":2,"StatuzDesc":"等待两个起始节点-学校审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733639},{"ProcessId":676389824655493,"ProcessNodeId":676389492240517,"Statuz":5,"StatuzDesc":"两个起始节点-学校审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733640},{"ProcessId":676389824655493,"ProcessNodeId":676389582729349,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733641},{"ProcessId":676389824655493,"ProcessNodeId":676389582729349,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.493","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733642},{"ProcessId":676389824655493,"ProcessNodeId":676389640851589,"Statuz":8,"StatuzDesc":"等待两个起始节点-区县审批","WaitOrBack":1,"IsEnable":1,"Sort":8,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.497","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733643},{"ProcessId":676389824655493,"ProcessNodeId":676389640851589,"Statuz":11,"StatuzDesc":"两个起始节点-区县审批退回","WaitOrBack":2,"IsEnable":1,"Sort":11,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-05-15T16:59:14.497","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-15T16:59:14.493","Version":0,"Id":676390307733644},{"ProcessId":701436251205765,"ProcessNodeId":701431612588165,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.983","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.98","Version":0,"Id":701436342112389},{"ProcessId":701436251205765,"ProcessNodeId":701431612588165,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116485},{"ProcessId":701436251205765,"ProcessNodeId":701431842508933,"Statuz":2,"StatuzDesc":"等待学校审核-暂存","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116486},{"ProcessId":701436251205765,"ProcessNodeId":701431842508933,"Statuz":5,"StatuzDesc":"学校审核-暂存退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116487},{"ProcessId":701436251205765,"ProcessNodeId":701432050229381,"Statuz":6,"StatuzDesc":"等待学校审批-暂存","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116488},{"ProcessId":701436251205765,"ProcessNodeId":701432050229381,"Statuz":9,"StatuzDesc":"学校审批-暂存退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-25T11:31:48.987","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-25T11:31:48.983","Version":0,"Id":701436342116489},{"ProcessId":702534674399365,"ProcessNodeId":702534414794885,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.64","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728167557},{"ProcessId":702534674399365,"ProcessNodeId":702534414794885,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.643","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728171653},{"ProcessId":702534674399365,"ProcessNodeId":702534572294277,"Statuz":2,"StatuzDesc":"等待学校审批-资金库测试","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.643","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728171654},{"ProcessId":702534674399365,"ProcessNodeId":702534572294277,"Statuz":5,"StatuzDesc":"学校审批-资金库测试退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-28T14:01:09.643","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-28T14:01:09.64","Version":0,"Id":702534728171655},{"ProcessId":703914263199877,"ProcessNodeId":703912581980293,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364229},{"ProcessId":703914263199877,"ProcessNodeId":703912581980293,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364230},{"ProcessId":703914263199877,"ProcessNodeId":703913893118085,"Statuz":2,"StatuzDesc":"等待教育局审核","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364231},{"ProcessId":703914263199877,"ProcessNodeId":703913893118085,"Statuz":5,"StatuzDesc":"教育局审核退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-01T11:34:59.523","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-01T11:34:59.52","Version":0,"Id":703914383364232},{"ProcessId":704429713551493,"ProcessNodeId":703912581980293,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.26","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937619077},{"ProcessId":704429713551493,"ProcessNodeId":703912581980293,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623173},{"ProcessId":704429713551493,"ProcessNodeId":704428996944005,"Statuz":2,"StatuzDesc":"等待学校审批","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623174},{"ProcessId":704429713551493,"ProcessNodeId":704428996944005,"Statuz":5,"StatuzDesc":"学校审批退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623175},{"ProcessId":704429713551493,"ProcessNodeId":703913893118085,"Statuz":6,"StatuzDesc":"等待教育局审核","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623176},{"ProcessId":704429713551493,"ProcessNodeId":703913893118085,"Statuz":9,"StatuzDesc":"教育局审核退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-02T22:32:47.263","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-02T22:32:47.26","Version":0,"Id":704429937623177},{"ProcessId":705106779930757,"ProcessNodeId":705091701473413,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.653","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272709},{"ProcessId":705106779930757,"ProcessNodeId":705091701473413,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.653","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272710},{"ProcessId":705106779930757,"ProcessNodeId":705091859951749,"Statuz":2,"StatuzDesc":"等待单位审核-指定审核人","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272711},{"ProcessId":705106779930757,"ProcessNodeId":705091859951749,"Statuz":5,"StatuzDesc":"单位审核-指定审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272712},{"ProcessId":705106779930757,"ProcessNodeId":705092139769989,"Statuz":6,"StatuzDesc":"等待财务处审核-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272713},{"ProcessId":705106779930757,"ProcessNodeId":705092139769989,"Statuz":9,"StatuzDesc":"财务处审核-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272714},{"ProcessId":705106779930757,"ProcessNodeId":705092317196421,"Statuz":10,"StatuzDesc":"等待教导主任审核-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":10,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272715},{"ProcessId":705106779930757,"ProcessNodeId":705092317196421,"Statuz":13,"StatuzDesc":"教导主任审核-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":13,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272716},{"ProcessId":705106779930757,"ProcessNodeId":705092469321861,"Statuz":14,"StatuzDesc":"等待校长审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":14,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272717},{"ProcessId":705106779930757,"ProcessNodeId":705092469321861,"Statuz":17,"StatuzDesc":"校长审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":17,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272718},{"ProcessId":705106779930757,"ProcessNodeId":705092789448837,"Statuz":18,"StatuzDesc":"等待区级领导审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":18,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272719},{"ProcessId":705106779930757,"ProcessNodeId":705092789448837,"Statuz":21,"StatuzDesc":"区级领导审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":21,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272720},{"ProcessId":705106779930757,"ProcessNodeId":705093020311685,"Statuz":22,"StatuzDesc":"等待区级财务科审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":22,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272721},{"ProcessId":705106779930757,"ProcessNodeId":705093020311685,"Statuz":25,"StatuzDesc":"区级财务科审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":25,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-04T20:28:00.657","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-04T20:28:00.65","Version":0,"Id":705107061272722},{"ProcessId":710330317377669,"ProcessNodeId":705091701473413,"Statuz":0,"StatuzDesc":"项目申报中","WaitOrBack":3,"IsEnable":1,"Sort":0,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262469},{"ProcessId":710330317377669,"ProcessNodeId":705091701473413,"Statuz":1000,"StatuzDesc":"审批结束","WaitOrBack":4,"IsEnable":1,"Sort":1000,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262470},{"ProcessId":710330317377669,"ProcessNodeId":705091859951749,"Statuz":2,"StatuzDesc":"等待单位审核-指定审核人","WaitOrBack":1,"IsEnable":1,"Sort":2,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262471},{"ProcessId":710330317377669,"ProcessNodeId":705091859951749,"Statuz":5,"StatuzDesc":"单位审核-指定审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":5,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.463","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262472},{"ProcessId":710330317377669,"ProcessNodeId":705092317196421,"Statuz":6,"StatuzDesc":"等待教导主任审核-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":6,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262473},{"ProcessId":710330317377669,"ProcessNodeId":705092317196421,"Statuz":9,"StatuzDesc":"教导主任审核-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":9,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262474},{"ProcessId":710330317377669,"ProcessNodeId":705092789448837,"Statuz":10,"StatuzDesc":"等待区级领导审批-执行审核人","WaitOrBack":1,"IsEnable":1,"Sort":10,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262475},{"ProcessId":710330317377669,"ProcessNodeId":705092789448837,"Statuz":13,"StatuzDesc":"区级领导审批-执行审核人退回","WaitOrBack":2,"IsEnable":1,"Sort":13,"Enabled":true,"NodeName":null,"NodeShowName":null,"StrWaitOrBack":null,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-08-19T14:42:18.467","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-08-19T14:42:18.46","Version":0,"Id":710330517262476}]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 195
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：14ms
【执行完成时间】：2025-08-29 04:05:48 210
【执行完成结果】：[{"BusinessType":3,"ProcessId":667811814981765,"ProcessNodeId":30000,"AuditUnitId":592123124052101,"AuditUserId":592123693924485,"Remark":"","Enabled":true,"ModuleId":667811814981765,"GroupValue":0,"TypeBox":3,"IsDeleted":false,"CreateId":592123693924485,"CreateBy":"yucai","CreateTime":"2025-04-21T13:52:15.203","ModifyId":592123693924485,"ModifyBy":"yucai","ModifyTime":"2025-04-21T13:52:15.2","Version":0,"Id":667850887905413}]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 239
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((f.Statuz == 1) AndAlso (f.ModuleId == value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId)) 
【携带的参数JSON】：  
【响应时间】：46ms
【执行完成时间】：2025-08-29 04:05:48 285
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 307
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：23ms
【执行完成时间】：2025-08-29 04:05:48 330
【执行完成结果】：[{"BusinessType":3,"ProcessId":658679024259205,"ProcessNodeId":30000,"AuditUnitId":592123124052101,"AuditUserId":592123693924485,"Remark":"","Enabled":true,"ModuleId":658679024259205,"GroupValue":0,"TypeBox":3,"IsDeleted":false,"CreateId":592123693924485,"CreateBy":"yucai","CreateTime":"2025-04-25T14:34:41.43","ModifyId":592123693924485,"ModifyBy":"yucai","ModifyTime":"2025-04-25T14:34:41.427","Version":0,"Id":669276894855301}]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 399
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((f.Statuz == 1) AndAlso (f.ModuleId == value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId)) 
【携带的参数JSON】：  
【响应时间】：17ms
【执行完成时间】：2025-08-29 04:05:48 416
【执行完成结果】：[{"ModuleId":658679024259205,"Name":"简单流程项目","ShowName":"项目列表","ProcessIds":"658689418428549,661475468013701","Unittype":1,"SourceData":4,"UseType":3,"PageType":0,"PageSize":100,"TotalName":"总计：","TotalCounmn":"SchoolName","UseUnitId":592122710352005,"Logo":null,"Statuz":1,"DefaultSort":"PlanYear","DefaultWhere":"Statuz = 2","PkId":"","SortType":2,"UseUnitField":"SchoolId","UseUserField":"","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"ProjectList","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-02T13:30:38.25","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-21T18:11:11.567","Version":0,"Id":661121584091269},{"ModuleId":658679024259205,"Name":"das","ShowName":"asd","ProcessIds":"658689418428549","Unittype":1,"SourceData":4,"UseType":1,"PageType":0,"PageSize":20,"TotalName":"dasd","TotalCounmn":"SchoolName","UseUnitId":592122710352005,"Logo":null,"Statuz":1,"DefaultSort":"fsd","DefaultWhere":"","PkId":"","SortType":1,"UseUnitField":"SchoolId","UseUserField":"UserId","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"ProjectList","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-02T15:55:42.03","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-24T13:44:54.243","Version":0,"Id":661157234729093},{"ModuleId":658679024259205,"Name":"测试项目清单1A","ShowName":"测试项目清单","ProcessIds":"658689418428549,661475468013701","Unittype":3,"SourceData":4,"UseType":1,"PageType":0,"PageSize":0,"TotalName":"总计：","TotalCounmn":"Name","UseUnitId":592122937471109,"Logo":null,"Statuz":1,"DefaultSort":"ModifyTime","DefaultWhere":null,"PkId":"","SortType":2,"UseUnitField":"SchoolId","UseUserField":"","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"ProjectList","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-04-08T15:57:07.69","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-05-19T13:53:25.74","Version":0,"Id":663280952029317},{"ModuleId":658679024259205,"Name":"测试基础信息显示","ShowName":"测试基础信息显示","ProcessIds":"658689418428549","Unittype":1,"SourceData":1,"UseType":0,"PageType":0,"PageSize":0,"TotalName":null,"TotalCounmn":null,"UseUnitId":592122710352005,"Logo":null,"Statuz":1,"DefaultSort":null,"DefaultWhere":null,"PkId":"","SortType":0,"UseUnitField":"","UseUserField":"","IsSubpage":2,"SearchType":1,"Url":"approvalconfiguration/pages/treasury/list","Enabled":true,"StrUnittype":null,"StrUseType":null,"StrPageType":null,"StrStatuz":null,"FieldCode":"","IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2025-07-18T18:19:04.117","ModifyId":100000000000000,"ModifyBy":"超级管理员","ModifyTime":"2025-07-21T18:07:01.363","Version":0,"Id":699059169710213}]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 472
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：30ms
【执行完成时间】：2025-08-29 04:05:48 503
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 558
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：14ms
【执行完成时间】：2025-08-29 04:05:48 573
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 599
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：15ms
【执行完成时间】：2025-08-29 04:05:48 614
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 636
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：14ms
【执行完成时间】：2025-08-29 04:05:48 651
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 720
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：9ms
【执行完成时间】：2025-08-29 04:05:48 730
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 749
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：9ms
【执行完成时间】：2025-08-29 04:05:48 759
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 803
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：10ms
【执行完成时间】：2025-08-29 04:05:48 814
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 832
【当前操作用户】：yucai 
【当前执行方法】：Query 
【携带的参数有】： f => ((((f.AuditUserId == Convert(value(Hyun.Core.Controllers.PermissionController)._user.ID, Nullable`1)) AndAlso (f.IsDeleted == False)) AndAlso (f.BusinessType == Convert(3, Nullable`1))) AndAlso (f.ModuleId == Convert(value(Hyun.Core.Controllers.PermissionController+<>c__DisplayClass22_4).CS$<>8__locals4.module.ModuleId, Nullable`1))) 
【携带的参数JSON】：  
【响应时间】：15ms
【执行完成时间】：2025-08-29 04:05:48 847
【执行完成结果】：[]

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 851
【当前操作用户】：yucai 
【当前执行方法】：IsExistGroupProcessSet 
【携带的参数有】：  
【携带的参数JSON】：  
【响应时间】：31ms
【执行完成时间】：2025-08-29 04:05:48 882
【执行完成结果】：true

--------------------------------
2025/8/29 16:05:48|
【操作时间】：2025-08-29 04:05:48 886
【当前操作用户】：yucai 
【当前执行方法】：IsExistPermissionSet 
【携带的参数有】：  
【携带的参数JSON】：  
【响应时间】：14ms
【执行完成时间】：2025-08-29 04:05:48 900
【执行完成结果】：false

--------------------------------
2025/8/29 16:05:49|
【操作时间】：2025-08-29 04:05:49 434
【当前操作用户】：yucai 
【当前执行方法】：QueryById 
【携带的参数有】： 592123693924485, True 
【携带的参数JSON】：  
【响应时间】：35ms
【执行完成时间】：2025-08-29 04:05:49 469
【执行完成结果】：{"StaffNumber":null,"UnitId":592123124052101,"IdNumber":null,"Name":"育才小学","Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"UserId":0,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"VerifiedMobile":null,"UserType":1,"AdministratorType":2,"Wechat":null,"HeadPortrait":null,"Enabled":true,"UnitType":0,"UnitPId":0,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-09-19T14:17:30.2","ModifyId":592123595010181,"ModifyBy":"浦口教育局","ModifyTime":"2025-08-28T16:16:01.043","Version":0,"Id":592123693924485}

--------------------------------
2025/8/29 16:05:49|
【操作时间】：2025-08-29 04:05:49 479
【当前操作用户】：yucai 
【当前执行方法】：GetByUserId 
【携带的参数有】： 592123693924485 
【携带的参数JSON】：  
【响应时间】：53ms
【执行完成时间】：2025-08-29 04:05:49 532
【执行完成结果】：{"Id":592123693924485,"Name":"育才小学","AreaId":826,"UnitId":592123124052101,"UnitName":"浦口育才小学","UnitType":3,"UnitPId":592122937471109,"UnitPName":"南京市浦口教育局","AcctId":592123696050309,"AcctName":"yucai","RoleIds":null,"RoleNames":null,"StaffNumber":null,"IdNumber":null,"Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"NickName":null,"CreateId":0,"AcctStatuz":1,"UserType":1,"AdministratorType":2,"UserValidate":null,"IsThirdClient":null,"Pwd":null,"DepartmentIds":null,"StrRoleIds":null,"StrRoleNames":null,"HeadPortrait":null,"UnitStatus":1}

--------------------------------
2025/8/29 16:06:03|
【操作时间】：2025-08-29 04:06:03 611
【当前操作用户】：yucai 
【当前执行方法】：QueryById 
【携带的参数有】： 592123693924485, True 
【携带的参数JSON】：  
【响应时间】：12ms
【执行完成时间】：2025-08-29 04:06:03 623
【执行完成结果】：{"StaffNumber":null,"UnitId":592123124052101,"IdNumber":null,"Name":"育才小学","Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"UserId":0,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"VerifiedMobile":null,"UserType":1,"AdministratorType":2,"Wechat":null,"HeadPortrait":null,"Enabled":true,"UnitType":0,"UnitPId":0,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-09-19T14:17:30.2","ModifyId":592123595010181,"ModifyBy":"浦口教育局","ModifyTime":"2025-08-28T16:16:01.043","Version":0,"Id":592123693924485}

--------------------------------
2025/8/29 16:06:03|
【操作时间】：2025-08-29 04:06:03 630
【当前操作用户】：yucai 
【当前执行方法】：GetByUserId 
【携带的参数有】： 592123693924485 
【携带的参数JSON】：  
【响应时间】：21ms
【执行完成时间】：2025-08-29 04:06:03 652
【执行完成结果】：{"Id":592123693924485,"Name":"育才小学","AreaId":826,"UnitId":592123124052101,"UnitName":"浦口育才小学","UnitType":3,"UnitPId":592122937471109,"UnitPName":"南京市浦口教育局","AcctId":592123696050309,"AcctName":"yucai","RoleIds":null,"RoleNames":null,"StaffNumber":null,"IdNumber":null,"Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"NickName":null,"CreateId":0,"AcctStatuz":1,"UserType":1,"AdministratorType":2,"UserValidate":null,"IsThirdClient":null,"Pwd":null,"DepartmentIds":null,"StrRoleIds":null,"StrRoleNames":null,"HeadPortrait":null,"UnitStatus":1}

--------------------------------
2025/8/29 16:06:05|
【操作时间】：2025-08-29 04:06:05 064
【当前操作用户】：yucai 
【当前执行方法】：QueryById 
【携带的参数有】： 592123693924485, True 
【携带的参数JSON】：  
【响应时间】：26ms
【执行完成时间】：2025-08-29 04:06:05 091
【执行完成结果】：{"StaffNumber":null,"UnitId":592123124052101,"IdNumber":null,"Name":"育才小学","Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"UserId":0,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"VerifiedMobile":null,"UserType":1,"AdministratorType":2,"Wechat":null,"HeadPortrait":null,"Enabled":true,"UnitType":0,"UnitPId":0,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-09-19T14:17:30.2","ModifyId":592123595010181,"ModifyBy":"浦口教育局","ModifyTime":"2025-08-28T16:16:01.043","Version":0,"Id":592123693924485}

--------------------------------
2025/8/29 16:06:05|
【操作时间】：2025-08-29 04:06:05 103
【当前操作用户】：yucai 
【当前执行方法】：GetByUserId 
【携带的参数有】： 592123693924485 
【携带的参数JSON】：  
【响应时间】：37ms
【执行完成时间】：2025-08-29 04:06:05 140
【执行完成结果】：{"Id":592123693924485,"Name":"育才小学","AreaId":826,"UnitId":592123124052101,"UnitName":"浦口育才小学","UnitType":3,"UnitPId":592122937471109,"UnitPName":"南京市浦口教育局","AcctId":592123696050309,"AcctName":"yucai","RoleIds":null,"RoleNames":null,"StaffNumber":null,"IdNumber":null,"Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"NickName":null,"CreateId":0,"AcctStatuz":1,"UserType":1,"AdministratorType":2,"UserValidate":null,"IsThirdClient":null,"Pwd":null,"DepartmentIds":null,"StrRoleIds":null,"StrRoleNames":null,"HeadPortrait":null,"UnitStatus":1}

--------------------------------
2025/8/29 16:06:28|
【操作时间】：2025-08-29 04:06:28 084
【当前操作用户】：yucai 
【当前执行方法】：QueryById 
【携带的参数有】： 592123693924485, True 
【携带的参数JSON】：  
【响应时间】：7ms
【执行完成时间】：2025-08-29 04:06:28 091
【执行完成结果】：{"StaffNumber":null,"UnitId":592123124052101,"IdNumber":null,"Name":"育才小学","Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"UserId":0,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"VerifiedMobile":null,"UserType":1,"AdministratorType":2,"Wechat":null,"HeadPortrait":null,"Enabled":true,"UnitType":0,"UnitPId":0,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-09-19T14:17:30.2","ModifyId":592123595010181,"ModifyBy":"浦口教育局","ModifyTime":"2025-08-28T16:16:01.043","Version":0,"Id":592123693924485}

--------------------------------
2025/8/29 16:06:28|
【操作时间】：2025-08-29 04:06:28 096
【当前操作用户】：yucai 
【当前执行方法】：GetByUserId 
【携带的参数有】： 592123693924485 
【携带的参数JSON】：  
【响应时间】：14ms
【执行完成时间】：2025-08-29 04:06:28 111
【执行完成结果】：{"Id":592123693924485,"Name":"育才小学","AreaId":826,"UnitId":592123124052101,"UnitName":"浦口育才小学","UnitType":3,"UnitPId":592122937471109,"UnitPName":"南京市浦口教育局","AcctId":592123696050309,"AcctName":"yucai","RoleIds":null,"RoleNames":null,"StaffNumber":null,"IdNumber":null,"Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"NickName":null,"CreateId":0,"AcctStatuz":1,"UserType":1,"AdministratorType":2,"UserValidate":null,"IsThirdClient":null,"Pwd":null,"DepartmentIds":null,"StrRoleIds":null,"StrRoleNames":null,"HeadPortrait":null,"UnitStatus":1}

--------------------------------
2025/8/29 16:06:28|
【操作时间】：2025-08-29 04:06:28 156
【当前操作用户】：yucai 
【当前执行方法】：GetLoginInfo 
【携带的参数有】： 592123693924485 
【携带的参数JSON】：  
【响应时间】：51ms
【执行完成时间】：2025-08-29 04:06:28 208
【执行完成结果】：{"flag":1,"msg":"获取成功。","data":{"total":0,"headers":null,"rows":{"Id":592123693924485,"Name":"育才小学","AreaId":826,"UnitId":592123124052101,"UnitName":"浦口育才小学","UnitType":3,"UnitPId":592122937471109,"UnitPName":"南京市浦口教育局","AcctId":592123696050309,"AcctName":"yucai","RoleIds":[370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355],"RoleNames":["班主任","班主任","班主任","班主任","单位管理员","单位管理员","单位管理员","单位管理员","单位管理员","单位管理员","单位管理员","用户业务处理","危化品领用人","危化品领用人","危化品领用人","危化品领用人","危化品领用人","危化品领用人","危化品领用人","危化品保管人","危化品保管人","危化品保管人","危化品保管人","危化品保管人","危化品保管人","危化品保管人","危化品采购审批人","危化品采购审批人","危化品采购审批人","危化品采购审批人","危化品采购审批人","危化品采购审批人","危化品采购审批人","危化品审核人","危化品审核人","危化品审核人","危化品审核人","危化品审核人","危化品审核人","危化品领用审核人","危化品领用审核人","危化品领用审核人","危化品领用审核人","危化品领用审核人","危化品领用审核人","危化品领用审核人","危化品安全排查人"],"StaffNumber":null,"IdNumber":null,"Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"RegTime":"2024-09-19T14:17:30.2","Memo":null,"Statuz":1,"NickName":null,"CreateId":0,"AcctStatuz":1,"UserType":1,"AdministratorType":2,"UserValidate":null,"IsThirdClient":null,"Pwd":null,"DepartmentIds":null,"StrRoleIds":null,"StrRoleNames":null,"HeadPortrait":null,"UnitStatus":1},"footer":null,"other":null,"other1":null,"other2":null,"other3":null},"userId":0,"unitId":0,"Id":0}

--------------------------------
2025/8/29 16:06:28|
【操作时间】：2025-08-29 04:06:28 218
【当前操作用户】：yucai 
【当前执行方法】：RoleModuleMaps 
【携带的参数有】： System.Collections.Generic.List`1[System.Int64] 
【携带的参数JSON】：  
【响应时间】：16ms
【执行完成时间】：2025-08-29 04:06:28 235
【执行完成结果】：[{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/getpaged","IsCommon":false,"Id":790210369736937472,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/delbyid","IsCommon":false,"Id":790210369804046336,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/savecopy","IsCommon":false,"Id":790210369804046337,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/getbyid","IsCommon":false,"Id":790210377194409984,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/saveadd","IsCommon":false,"Id":790210377261518848,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/saveedit","IsCommon":false,"Id":790210377261518849,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/delattachmentbyid","IsCommon":false,"Id":790210377261518850,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/savepublish","IsCommon":false,"Id":790210377261518851,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/publishcancel","IsCommon":false,"Id":790210377261518852,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":790210377261518853,"RoleId":30},{"Role":"单位管理员","Url":"/api/attachment/upload","IsCommon":false,"Id":790210377261518854,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/getpaged","IsCommon":false,"Id":790210380570824704,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/savefiling","IsCommon":false,"Id":790210380570824705,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/getopinionpaged","IsCommon":false,"Id":790210383355842560,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformscheme/exportopinion","IsCommon":false,"Id":790210383418757120,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":790211661326716928,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getpaged","IsCommon":false,"Id":790211661326716929,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/savesubmit","IsCommon":false,"Id":790211661326716930,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/deletebyid","IsCommon":false,"Id":790211661326716931,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/confirmfiling","IsCommon":false,"Id":790211661326716932,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/filingbackout","IsCommon":false,"Id":790211661326716933,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/filingrevoked","IsCommon":false,"Id":790211661326716934,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/geteditbyid","IsCommon":false,"Id":790211665156116480,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/saveadd","IsCommon":false,"Id":790211665156116481,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/saveedit","IsCommon":false,"Id":790211665156116482,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/delattachmentbyid","IsCommon":false,"Id":790211665156116483,"RoleId":30},{"Role":"单位管理员","Url":"/api/attachment/upload","IsCommon":false,"Id":790211665156116484,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":790211665156116485,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":790211688208011264,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/geteditbyid","IsCommon":false,"Id":790211688208011265,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformshelf/getwaitauditpaged","IsCommon":false,"Id":790211720403488768,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getpurchaseno","IsCommon":false,"Id":790211720403488769,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformshelf/audit","IsCommon":false,"Id":790211720403488770,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformshelf/getauditedpaged","IsCommon":false,"Id":790211723503079424,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformshelf/revokedbyid","IsCommon":false,"Id":790211723503079425,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformorganization/getpaged","IsCommon":false,"Id":790212077401673728,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformorganization/saveedit","IsCommon":false,"Id":790212077401673729,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformorganization/geteditbyid","IsCommon":false,"Id":790212077401673730,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformorganization/delattachmentbyid","IsCommon":false,"Id":790212077468782592,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformorganization/savefiling","IsCommon":false,"Id":790212077468782593,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":790212077468782594,"RoleId":30},{"Role":"单位管理员","Url":"/api/attachment/upload","IsCommon":false,"Id":790212077468782595,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getswappaged","IsCommon":false,"Id":790212228799270912,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/launch","IsCommon":false,"Id":790212228799270913,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getexchangeorder","IsCommon":false,"Id":790212231764643840,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getswapdetail","IsCommon":false,"Id":790212234147008512,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/exportswapdetail","IsCommon":false,"Id":790212234147008513,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getschoolsponsorpaged","IsCommon":false,"Id":790212372101861376,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/sponsoradd","IsCommon":false,"Id":790212372101861377,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/sponsoredit","IsCommon":false,"Id":790212372101861378,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getsponsorbyid","IsCommon":false,"Id":790212372101861379,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/delsponsorbyid","IsCommon":false,"Id":790212372101861380,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/delfilebyid","IsCommon":false,"Id":790212372101861381,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":790212372101861382,"RoleId":30},{"Role":"单位管理员","Url":"/api/attachment/upload","IsCommon":false,"Id":790212372101861383,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformevaluate/getevaluatepaged","IsCommon":false,"Id":790212498274914304,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getcreateorder","IsCommon":false,"Id":790212500388843520,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformevaluate/launch","IsCommon":false,"Id":790212500388843521,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformevaluate/getevaluatelist","IsCommon":false,"Id":790212503362605056,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformevaluate/getevaluatedetaillist","IsCommon":false,"Id":790212505606557696,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformevaluate/exportdetail","IsCommon":false,"Id":790212505606557697,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/psupplierschoolaudit/getmyunit","IsCommon":false,"Id":790212750914621440,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/psupplierschoolaudit/apply","IsCommon":false,"Id":790212750914621441,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":790212750914621442,"RoleId":30},{"Role":"单位管理员","Url":"/api/attachment/upload","IsCommon":false,"Id":790212750914621443,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/psupplierschoolaudit/delfilebyid","IsCommon":false,"Id":790212750914621444,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/userfindmyunit","IsCommon":false,"Id":790213208890675200,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/userdelbatch","IsCommon":false,"Id":790213208890675201,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/prole/rolefindmyunit","IsCommon":false,"Id":790213208890675202,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/usermyunitupdateuserstatuz","IsCommon":false,"Id":790213208890675203,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/useraccountunlock","IsCommon":false,"Id":790213208890675204,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/usergetbyid","IsCommon":false,"Id":790213208890675205,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/usersavemyunit","IsCommon":false,"Id":790213208890675206,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/getalldepartment","IsCommon":false,"Id":790213208957784064,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentuserbatchset","IsCommon":false,"Id":790213208957784065,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pclassinfo/getpaged","IsCommon":false,"Id":790213472011948032,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pclassinfo/add","IsCommon":false,"Id":***************896,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pclassinfo/edit","IsCommon":false,"Id":***************897,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pclassinfo/deletebyid","IsCommon":false,"Id":***************898,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pclassinfo/import","IsCommon":false,"Id":***************899,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pstudent/import","IsCommon":false,"Id":***************900,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pclassinfo/gradeupgrade","IsCommon":false,"Id":***************901,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/upload/postexecl","IsCommon":false,"Id":***************902,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pstudent/getpaged","IsCommon":false,"Id":790213474566279168,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pstudent/add","IsCommon":false,"Id":790213474629193728,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pstudent/edit","IsCommon":false,"Id":790213474629193729,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pstudent/exportschoolstu","IsCommon":false,"Id":790213474629193730,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/pstudent/deletebyid","IsCommon":false,"Id":790213474629193731,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/upload/postexecl","IsCommon":false,"Id":790213474629193732,"RoleId":30},{"Role":"班主任","Url":"/api/hyun/puser/setuserinfo","IsCommon":false,"Id":790214345614168064,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/puser/getuserinfo","IsCommon":false,"Id":790214345614168065,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/upload/posthead","IsCommon":false,"Id":790214345614168066,"RoleId":370},{"Role":"单位管理员","Url":"/api/hyun/puser/setuserinfo","IsCommon":false,"Id":790214398755999744,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/getuserinfo","IsCommon":false,"Id":790214398755999745,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/upload/posthead","IsCommon":false,"Id":790214398755999746,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/userchangepass","IsCommon":false,"Id":790214401134170112,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/geteditorderpaged","IsCommon":false,"Id":790214891100180480,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getcreateorder","IsCommon":false,"Id":790214893021171712,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/saveeditorder","IsCommon":false,"Id":790214893021171713,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformpurchase/getorderpaged","IsCommon":false,"Id":790214894740836352,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformparentpurchase/getstudentpaged","IsCommon":false,"Id":790214896984788992,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/xuniformparentpurchase/exportstudent","IsCommon":false,"Id":790214897047703552,"RoleId":30},{"Role":"班主任","Url":"/api/hyun/xuniformpurchase/getteacherorder","IsCommon":false,"Id":790214995781619712,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/pstudent/getpagedbyteacher","IsCommon":false,"Id":790214997832634368,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/pstudent/teacheraddstu","IsCommon":false,"Id":790214997832634369,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/pstudent/teachereditstu","IsCommon":false,"Id":790214997832634370,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/pstudent/teacherimportstu","IsCommon":false,"Id":790214997832634371,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/pstudent/exportteacherstu","IsCommon":false,"Id":790214997899743232,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/pstudent/teacherdelstu","IsCommon":false,"Id":790214997899743233,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/xuniformpurchase/getorderteacherpaged","IsCommon":false,"Id":790214999749431296,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/xuniformparentpurchase/getteacherstudentpaged","IsCommon":false,"Id":790215001724948480,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/xuniformparentpurchase/exportstudent","IsCommon":false,"Id":790215001724948481,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/xuniformpurchase/getswapdetailbyteacher","IsCommon":false,"Id":790215003968901120,"RoleId":370},{"Role":"班主任","Url":"/api/hyun/xuniformpurchase/exportbyteacher","IsCommon":false,"Id":790215003968901121,"RoleId":370},{"Role":"单位管理员","Url":"/api/hyun/process/getsetuserlist","IsCommon":false,"Id":816626066708762624,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/process/setnodeaudituser","IsCommon":false,"Id":816626066708762625,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/process/getprocessaudituserlist","IsCommon":false,"Id":816626066708762626,"RoleId":30},{"Role":"用户业务处理","Url":"/api/hyun/process/getfillinginfo","IsCommon":false,"Id":816712522898870272,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/savefillinginfo","IsCommon":false,"Id":816712522898870273,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/submitfillinginfo","IsCommon":false,"Id":817064719788544000,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getfillsearchlist","IsCommon":false,"Id":817816815810908160,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/delfillinginfo","IsCommon":false,"Id":820322448535719936,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/fillinginfodetail","IsCommon":false,"Id":820337593731256320,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/fillinginfoaudit","IsCommon":false,"Id":820349665638617088,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getprocessedlist","IsCommon":false,"Id":820598800748908544,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/submitapproval","IsCommon":false,"Id":820599449595154432,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/operaterevoke","IsCommon":false,"Id":824218918330568704,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/projectlistsearch","IsCommon":false,"Id":824652246359740416,"RoleId":3200},{"Role":"用户业务处理","Url":" /api/hyun/process/projectlistsave","IsCommon":false,"Id":825326862728171520,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/projectlistbyid","IsCommon":false,"Id":825326862728171521,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/projectlistdeletebyid","IsCommon":false,"Id":825326862728171522,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/projectlistdeletebyids","IsCommon":false,"Id":825326862728171523,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getlistsumbyprojectdeclarationid","IsCommon":false,"Id":825393534994485248,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/downloadprojectlist","IsCommon":false,"Id":825670446081904640,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/projectlistimport","IsCommon":false,"Id":825673320656539648,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/findpagedelist","IsCommon":false,"Id":827852186091261952,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/findauditprojectlist","IsCommon":false,"Id":830122064294711296,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getsearchlist","IsCommon":false,"Id":830123047997411328,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/fillinginfodetail","IsCommon":false,"Id":830123066230050816,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/saveprojectlistreview","IsCommon":false,"Id":830141054580690944,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/findhistoryprojectlist","IsCommon":false,"Id":830851498555478016,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/puser/setuserinfo","IsCommon":false,"Id":832620426814296064,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/puser/getuserinfo","IsCommon":false,"Id":832620426814296065,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/upload/posthead","IsCommon":false,"Id":832620426814296066,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/puser/userchangepass","IsCommon":false,"Id":832620433286107136,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/attachment/upload","IsCommon":false,"Id":832990986182332416,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/attachment/upload","IsCommon":false,"Id":832991007048994816,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/findsourcefundlist","IsCommon":false,"Id":834453954300481536,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getlinkagedatasource","IsCommon":false,"Id":836991833518116864,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getcontroldetaildatasource","IsCommon":false,"Id":840285665915572224,"RoleId":3200},{"Role":"单位管理员","Url":"/api/application/getpaged","IsCommon":false,"Id":840601853119959040,"RoleId":30},{"Role":"单位管理员","Url":"/api/application/resetSecret/{}","IsCommon":false,"Id":842423300826075137,"RoleId":30},{"Role":"单位管理员","Url":"/api/application/display/switch/{}","IsCommon":false,"Id":842423300826075138,"RoleId":30},{"Role":"单位管理员","Url":"/api/application/enable/switch/{}","IsCommon":false,"Id":842423300826075139,"RoleId":30},{"Role":"单位管理员","Url":"/api/application/{}","IsCommon":false,"Id":842703728405385216,"RoleId":30},{"Role":"单位管理员","Url":"/api/application/addOrEdit","IsCommon":false,"Id":842703728476688384,"RoleId":30},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseauditfind","IsCommon":false,"Id":848161054201483264,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcpurchasedetaillistfind","IsCommon":false,"Id":848161073293955072,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcpurchaselistdelete","IsCommon":false,"Id":848161073293955073,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161073293955074,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcpurchasewithdraw","IsCommon":false,"Id":848161092373843968,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseauditedfind","IsCommon":false,"Id":848161092373843969,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848161111357263872,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseaudit","IsCommon":false,"Id":848161111357263873,"RoleId":350},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyauditlistfind","IsCommon":false,"Id":848161169163161600,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplybatchconfirm","IsCommon":false,"Id":848161169163161601,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyauditedlistfind","IsCommon":false,"Id":848161192177307648,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplybatchaudit","IsCommon":false,"Id":848161192177307649,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyauditlistfind","IsCommon":false,"Id":848161208417652736,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyview","IsCommon":false,"Id":848161208417652737,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyaudit","IsCommon":false,"Id":848161208417652738,"RoleId":351},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfilllistfind","IsCommon":false,"Id":848161254731157504,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplybatchadd","IsCommon":false,"Id":848161254731157505,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplydirectapply","IsCommon":false,"Id":848161254731157506,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/bconfigset/bconfigsetgetpunit","IsCommon":false,"Id":848161254731157507,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcapplygrantusercomboget","IsCommon":false,"Id":848161254731157508,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall","IsCommon":false,"Id":848161254731157509,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyadd","IsCommon":false,"Id":848161254731157510,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplycarlistfind","IsCommon":false,"Id":848161279121035264,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplydelete","IsCommon":false,"Id":848161279121035265,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfindbyid","IsCommon":false,"Id":848161279121035266,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogmodelgetv2","IsCommon":false,"Id":848161279121035267,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogbrandgetbymodelidv2","IsCommon":false,"Id":848161279121035268,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplygetstocknum","IsCommon":false,"Id":848161279121035269,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyinsertupdate","IsCommon":false,"Id":848161279121035270,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall","IsCommon":false,"Id":848161279121035271,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcomboboxget","IsCommon":false,"Id":848161279121035272,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcommonusefind","IsCommon":false,"Id":848161279121035273,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplybatchapply","IsCommon":false,"Id":848161279121035274,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/bconfigset/bconfigsetgetpunit","IsCommon":false,"Id":848161279121035275,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcapplygrantusercomboget","IsCommon":false,"Id":848161279121035276,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyview","IsCommon":false,"Id":848161294702874624,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplylistfind","IsCommon":false,"Id":848161309194194944,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyrevoke","IsCommon":false,"Id":848161309194194945,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplydelete","IsCommon":false,"Id":848161309194194946,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfindbyid","IsCommon":false,"Id":848161309194194947,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplynopassreasonget","IsCommon":false,"Id":848161309194194948,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogmodelgetv2","IsCommon":false,"Id":848161309194194949,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogbrandgetbymodelidv2","IsCommon":false,"Id":848161309194194950,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall","IsCommon":false,"Id":848161309194194951,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyinsertupdate","IsCommon":false,"Id":848161309194194952,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyapply","IsCommon":false,"Id":848161309194194953,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcpurchasenopassreasonget","IsCommon":false,"Id":848161309194194954,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcommonusefind","IsCommon":false,"Id":848161309194194955,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcomboboxget","IsCommon":false,"Id":848161309194194956,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplylookaudituser","IsCommon":false,"Id":848161309194194957,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyafterconfirm","IsCommon":false,"Id":848161309194194958,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyupdatebacknum","IsCommon":false,"Id":848161309194194959,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcwastebasefind","IsCommon":false,"Id":848161309194194960,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyeasylistfind","IsCommon":false,"Id":848161326625722368,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161326625722369,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyeasyapply","IsCommon":false,"Id":848161326625722370,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/bconfigset/bconfigsetgetpunit","IsCommon":false,"Id":848161326625722371,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcapplygrantusercomboget","IsCommon":false,"Id":848161326625722372,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/bconfigset/bconfigsetget","IsCommon":false,"Id":848161326625722373,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplylistfind","IsCommon":false,"Id":848161342010429440,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplydelete","IsCommon":false,"Id":848161342010429441,"RoleId":352},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161393294184448,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcmaterialsnumauditfind","IsCommon":false,"Id":848161393294184449,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialreturnlibrary","IsCommon":false,"Id":848161393294184450,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcmaterialsnumauditfind","IsCommon":false,"Id":848161411539406848,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161411539406849,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcinventorysave","IsCommon":false,"Id":848161411539406851,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcmaterialsnumauditfind","IsCommon":false,"Id":848161430090813440,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161430090813441,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848161457274097664,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogrecord","IsCommon":false,"Id":848161457274097665,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161457274097666,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848161457274097667,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseendfind","IsCommon":false,"Id":848161478665048064,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialinsertupdate","IsCommon":false,"Id":848161504543903744,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialcatalogfind","IsCommon":false,"Id":848161504543903745,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848161504543903746,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchasebatchnofind","IsCommon":false,"Id":848161504543903747,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialbatchaudit","IsCommon":false,"Id":848161504543903748,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialdeletebyid","IsCommon":false,"Id":848161504543903749,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848161504543903750,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialcataloggetbyid","IsCommon":false,"Id":848161504543903751,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialnext","IsCommon":false,"Id":848161504543903752,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelgetbycatalogid","IsCommon":false,"Id":848161504543903753,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmodelbrandgetbrand","IsCommon":false,"Id":848161504543903754,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall","IsCommon":false,"Id":848161504543903755,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcommonusefind","IsCommon":false,"Id":848161504543903756,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressfind","IsCommon":false,"Id":848161504543903757,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseorderuploadfile","IsCommon":false,"Id":848161504543903758,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseordergetbyid","IsCommon":false,"Id":848161504543903759,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcstoragelogprintupdatestatuz","IsCommon":false,"Id":848161520188657664,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848161520188657665,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848161536080875520,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161536080875521,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848161536080875522,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/punit/unitgetinfo","IsCommon":false,"Id":848161536080875523,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialstatisticsfind","IsCommon":false,"Id":848161557333413888,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848161557333413889,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressfind","IsCommon":false,"Id":848161557333413890,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcmaterialaddressbatchedit","IsCommon":false,"Id":848161557333413891,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848161580192370688,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseaudit","IsCommon":false,"Id":848161580192370689,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseauditfind","IsCommon":false,"Id":848161598215294976,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseauditedfind","IsCommon":false,"Id":848161616796061696,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchasewithdraw","IsCommon":false,"Id":848161616796061697,"RoleId":353},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyeasylistfind","IsCommon":false,"Id":848161674786508800,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161674786508801,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyeasyapply","IsCommon":false,"Id":848161674786508802,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/bconfigset/bconfigsetgetpunit","IsCommon":false,"Id":848161674786508803,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcapplygrantusercomboget","IsCommon":false,"Id":848161674786508804,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/bconfigset/bconfigsetget","IsCommon":false,"Id":848161674786508805,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplylistfind","IsCommon":false,"Id":848161701772660736,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplydelete","IsCommon":false,"Id":848161701772660737,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyauditlistfind","IsCommon":false,"Id":848161721318117376,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplybatchconfirm","IsCommon":false,"Id":848161721318117377,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplybatchaudit","IsCommon":false,"Id":848161721318117378,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyauditlistfind","IsCommon":false,"Id":848161737722040320,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyview","IsCommon":false,"Id":848161737722040321,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyconfirm","IsCommon":false,"Id":848161737722040322,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyschoolmaterialfind","IsCommon":false,"Id":848161737722040323,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetaildelete","IsCommon":false,"Id":848161737722040324,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyadjust","IsCommon":false,"Id":848161737722040325,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyadjustfind","IsCommon":false,"Id":848161737722040326,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848161756076314624,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetailcollar","IsCommon":false,"Id":848161756076314625,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetailgetbyid","IsCommon":false,"Id":848161756076314626,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetailresendcode","IsCommon":false,"Id":848161756076314627,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/bconfigset/bconfigsetgetpunit","IsCommon":false,"Id":848161756076314628,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcapplygrantusercomboget","IsCommon":false,"Id":848161756076314629,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848161773440733184,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyprintlistfind","IsCommon":false,"Id":848161793544032256,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchasebycatalogfind","IsCommon":false,"Id":848161817178935296,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall","IsCommon":false,"Id":848161817178935297,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseadd","IsCommon":false,"Id":848161817178935298,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchasebatchadd","IsCommon":false,"Id":848161817178935299,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaselistfind","IsCommon":false,"Id":848161833406697472,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseapply","IsCommon":false,"Id":848161833406697473,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaselistdelete","IsCommon":false,"Id":848161833406697474,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaselistfindbyid","IsCommon":false,"Id":848161833406697475,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall","IsCommon":false,"Id":848161833406697476,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelgetbycatalogid","IsCommon":false,"Id":848161833406697477,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaselistupdate","IsCommon":false,"Id":848161833406697478,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseadd","IsCommon":false,"Id":848161833406697479,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchasenopassreasonget","IsCommon":false,"Id":848161833406697480,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcommonusefind","IsCommon":false,"Id":848161833406697481,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcomboboxget","IsCommon":false,"Id":848161833406697482,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseorderfind","IsCommon":false,"Id":848161850511069184,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseorderdelete","IsCommon":false,"Id":848161850511069185,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseendexport","IsCommon":false,"Id":848161850511069186,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaserevoke","IsCommon":false,"Id":848161850511069187,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseendfind","IsCommon":false,"Id":848161868433330176,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaselistexportexcel","IsCommon":false,"Id":848161868433330177,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchasedetaillistfind","IsCommon":false,"Id":848161892533800960,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaselistdelete","IsCommon":false,"Id":848161892533800961,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161892533800962,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseendfind","IsCommon":false,"Id":848161926981619712,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseorderfinishinputstatuz","IsCommon":false,"Id":848161926981619713,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialinsertupdate","IsCommon":false,"Id":848161944413147136,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848161944413147137,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848161944413147138,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelgetbycatalogid","IsCommon":false,"Id":848161944413147139,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmodelbrandgetbrand","IsCommon":false,"Id":848161944413147140,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall","IsCommon":false,"Id":848161944413147141,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogcommonusefind","IsCommon":false,"Id":848161944413147142,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressfind","IsCommon":false,"Id":848161944413147143,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/bconfigset/bconfigsetgetpunit","IsCommon":false,"Id":848161944413147144,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848161963669196800,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolcatalogrecord","IsCommon":false,"Id":848161963669196801,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848161963669196802,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848161963669196803,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcinputmaterialbyplanfind","IsCommon":false,"Id":848161982749085696,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcmaterialbyplansave","IsCommon":false,"Id":848161982749085697,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848161982749085698,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialtempdelete","IsCommon":false,"Id":848161982749085699,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmodelbrandgetbrand","IsCommon":false,"Id":848161982749085700,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressfind","IsCommon":false,"Id":848161982749085701,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":848161982749085702,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcmaterialbyplaninput","IsCommon":false,"Id":848161982749085703,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848161982749085704,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/punit/unitgetinfo","IsCommon":false,"Id":848161982749085705,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcstoragelogprintupdatestatuz","IsCommon":false,"Id":848161998674857984,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848161998674857985,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848162018945929216,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848162018945929217,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848162018945929218,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/punit/unitgetinfo","IsCommon":false,"Id":848162018945929219,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialstatisticsfind","IsCommon":false,"Id":848162048192811008,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848162048192811009,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressfind","IsCommon":false,"Id":848162048192811010,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcmaterialaddressbatchedit","IsCommon":false,"Id":848162048192811011,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyauditlistfind","IsCommon":false,"Id":848162073283137536,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyeasygrant","IsCommon":false,"Id":848162073283137537,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelgetbyid","IsCommon":false,"Id":848162073283137538,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848162097186476032,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848162113686867968,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyeasyback","IsCommon":false,"Id":848162113686867969,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcmaterialbackfind","IsCommon":false,"Id":848162132007587840,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcmaterialrevertbackconfirm","IsCommon":false,"Id":848162132007587841,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialoptfind","IsCommon":false,"Id":848162148319236096,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcmaterialsnumauditadd","IsCommon":false,"Id":848162148319236097,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchasebatchnofind","IsCommon":false,"Id":848162148319236098,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialbacklogfind","IsCommon":false,"Id":848162165314555904,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanycomboxfind","IsCommon":false,"Id":848162165314555905,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialbacklogfind","IsCommon":false,"Id":848162180502130688,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwarningstocklistfind","IsCommon":false,"Id":848162199594602496,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848162199594602497,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848162199594602498,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfiggetbyid","IsCommon":false,"Id":848162199594602499,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwarningvaliditylistfind","IsCommon":false,"Id":848162217663664128,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelsetstatuz","IsCommon":false,"Id":848162217663664129,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848162217663664130,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialoptfind","IsCommon":false,"Id":848162234847727616,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848162234847727617,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialgetbyid","IsCommon":false,"Id":848162234847727618,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcmaterialsnumauditadd","IsCommon":false,"Id":848162234847727619,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcinventoryrecordfind","IsCommon":false,"Id":848162255680835584,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848162255680835585,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialoptfind","IsCommon":false,"Id":848162282746679296,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcmaterialsetismayuse","IsCommon":false,"Id":848162282746679297,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848162282746679298,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialgetbyid","IsCommon":false,"Id":848162282746679299,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcmaterialsnumauditadd","IsCommon":false,"Id":848162282746679300,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscrapedlistfind","IsCommon":false,"Id":848162302728343552,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscrapdisposal","IsCommon":false,"Id":848162302728343553,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848162302728343554,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyprintlistfind","IsCommon":false,"Id":848162331346079744,"RoleId":354},{"Role":"单位管理员","Url":"/api/hyun/dcdangerchemicals/dcapplygrantuserfind","IsCommon":false,"Id":848231439500578816,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/dcdangerchemicals/dcapplygrantuseradd","IsCommon":false,"Id":848231439500578817,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/userfindmyunitusernamebyroleid","IsCommon":false,"Id":848231439500578818,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/dcdangerchemicals/dcapplygrantuserdelete","IsCommon":false,"Id":848231439500578819,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/dcdangerchemicals/dcapplygrantuserfind","IsCommon":false,"Id":848231455338270720,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/dcdangerchemicals/dcapplygrantuseradd","IsCommon":false,"Id":848231455338270721,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/puser/userfindmyunitusernamebyroleid","IsCommon":false,"Id":848231455338270722,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/dcdangerchemicals/dcapplygrantuserdelete","IsCommon":false,"Id":848231455338270723,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/bconfigset/bconfigsetsavebyunit","IsCommon":false,"Id":848231470366461952,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/bconfigset/bconfigsetgetbyunit","IsCommon":false,"Id":848231470366461953,"RoleId":30},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848232511048781824,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848232511048781825,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232511048781826,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/schoolmaterialmsdsfileedit","IsCommon":false,"Id":848232511048781827,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":848232527402373120,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/punit/punitgetcountybycityid","IsCommon":false,"Id":848232527402373121,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":848232527402373122,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/bdictionary/getdictionarycombox","IsCommon":false,"Id":848232527402373123,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcpurchasenumstatisticsfind","IsCommon":false,"Id":848232549514743808,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/getdcpurchasestatisticsyear","IsCommon":false,"Id":848232549514743809,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232549514743810,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcapplynumstatisticsfind","IsCommon":false,"Id":848232574055616512,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848232574055616513,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232574055616514,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcapplyusernumstatisticsfind","IsCommon":false,"Id":848232590627311616,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsuser","IsCommon":false,"Id":848232590627311617,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232590627311618,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848232590627311619,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcstandbookpurchasefind","IsCommon":false,"Id":848232625565863936,"RoleId":350},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcstandbookpurchasefind","IsCommon":false,"Id":848232644985491456,"RoleId":350},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848232674869907456,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848232695237447680,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dcapplyusernumstatisticsfind","IsCommon":false,"Id":848232725742620672,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsuser","IsCommon":false,"Id":848232725742620673,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232725742620674,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848232725742620675,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dcapplynumstatisticsfind","IsCommon":false,"Id":848232742012325888,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848232742012325889,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232742012325890,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":848232769615040512,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/punit/punitgetcountybycityid","IsCommon":false,"Id":848232769615040513,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":848232769615040514,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/bdictionary/getdictionarycombox","IsCommon":false,"Id":848232769615040515,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848232786614554624,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848232786614554625,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232786614554626,"RoleId":351},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/schoolmaterialmsdsfileedit","IsCommon":false,"Id":848232786614554627,"RoleId":351},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcwasterecordfind","IsCommon":false,"Id":848232921570480128,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848232939727622144,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848232939727622145,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848232939727622146,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/schoolmaterialmsdsfileedit","IsCommon":false,"Id":848232939727622147,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":848232956982988800,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/punit/punitgetcountybycityid","IsCommon":false,"Id":848232956982988801,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":848232956982988802,"RoleId":352},{"Role":"危化品领用人","Url":"/api/hyun/bdictionary/getdictionarycombox","IsCommon":false,"Id":848232956982988803,"RoleId":352},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcwasteclassget","IsCommon":false,"Id":848233130174189569,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposaldetailfind","IsCommon":false,"Id":848233148788510720,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalauditfind","IsCommon":false,"Id":848233167042121728,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalaudit","IsCommon":false,"Id":848233167042121729,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalgetbyid","IsCommon":false,"Id":848233167042121730,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848233187111866368,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848233187111866369,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233187111866370,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/schoolmaterialmsdsfileedit","IsCommon":false,"Id":848233187111866371,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":848233204702777344,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/punit/punitgetcountybycityid","IsCommon":false,"Id":848233204702777345,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":848233204702777346,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/bdictionary/getdictionarycombox","IsCommon":false,"Id":848233204702777347,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcpurchasenumstatisticsfind","IsCommon":false,"Id":848233221605822464,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/getdcpurchasestatisticsyear","IsCommon":false,"Id":848233221605822465,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233221605822466,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcapplynumstatisticsfind","IsCommon":false,"Id":848233237841973248,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848233237841973249,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233237841973250,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcapplyusernumstatisticsfind","IsCommon":false,"Id":848233255827148800,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsuser","IsCommon":false,"Id":848233255827148801,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233255827148802,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848233255827148803,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigfind","IsCommon":false,"Id":848233280388993024,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233280388993025,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetbypid","IsCommon":false,"Id":848233280388993026,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfiggetbyid","IsCommon":false,"Id":848233280388993027,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelsave","IsCommon":false,"Id":848233280388993028,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigsave","IsCommon":false,"Id":848233280388993029,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanyfind","IsCommon":false,"Id":848233297321398272,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanyinsertupdate","IsCommon":false,"Id":848233297321398273,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/puser/usergetinfo","IsCommon":false,"Id":848233297321398274,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/bconfigset/bconfigsetget","IsCommon":false,"Id":848233297321398275,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/punit/unitfindidname","IsCommon":false,"Id":848233297321398276,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/puser/userfindcompany","IsCommon":false,"Id":848233297321398277,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/puser/usergetpubbyid","IsCommon":false,"Id":848233297321398278,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanydeletebyid","IsCommon":false,"Id":848233297321398279,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanyenabled","IsCommon":false,"Id":848233297321398280,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccompanygetbyid","IsCommon":false,"Id":848233297321398281,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcunitlicenseinfogetcommany","IsCommon":false,"Id":848233297321398282,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcunitlicenseinfosave","IsCommon":false,"Id":848233297321398283,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcstandbookpurchasefind","IsCommon":false,"Id":848233363838865408,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcstandbookpurchasefind","IsCommon":false,"Id":848233379114520576,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcstandbookstockfind","IsCommon":false,"Id":848233396885786624,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcstandbookstockfind","IsCommon":false,"Id":848233417429487616,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcstandbookwastefind","IsCommon":false,"Id":848233437780250624,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcstandbookwastefind","IsCommon":false,"Id":848233453764743168,"RoleId":353},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalfind","IsCommon":false,"Id":848233767502876672,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwasetdisposalsave","IsCommon":false,"Id":848233767502876673,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwasetdisposalgetisexists","IsCommon":false,"Id":848233767502876674,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwasteclassget","IsCommon":false,"Id":848233785085399041,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalauditfind","IsCommon":false,"Id":848233799710937088,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848233799710937089,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalgetbyid","IsCommon":false,"Id":848233799710937090,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalreport","IsCommon":false,"Id":848233799710937091,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposaldetailfind","IsCommon":false,"Id":848233815091449856,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848233831646367744,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/basefieldconfigfind","IsCommon":false,"Id":848233831646367745,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/basefieldconfigsavelist","IsCommon":false,"Id":848233831646367746,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/basefieldconfigsavefile","IsCommon":false,"Id":848233831646367747,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/basefieldconfigdeletefile","IsCommon":false,"Id":848233831646367748,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/trainsafeeducationfind","IsCommon":false,"Id":848233846724890624,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/trainsafeeducationinsertupdate","IsCommon":false,"Id":848233846724890625,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848233846724890626,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/trainsafeeducationdelete","IsCommon":false,"Id":848233846724890627,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/trainsafeeducationedit","IsCommon":false,"Id":848233846724890628,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/attachmentsavefile","IsCommon":false,"Id":848233846724890629,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/attachmentdeletefile","IsCommon":false,"Id":848233846724890630,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/emergencyplanfind","IsCommon":false,"Id":848233860855500800,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/emergencyplaninsertupdate","IsCommon":false,"Id":848233860855500801,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848233860855500802,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/emergencyplandelete","IsCommon":false,"Id":848233860855500803,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/emergencyplanedit","IsCommon":false,"Id":848233860855500804,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/attachmentdeletefile","IsCommon":false,"Id":848233860855500805,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/attachmentsavefile","IsCommon":false,"Id":848233860855500806,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcscraplistfind","IsCommon":false,"Id":848233877628522496,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848233877628522497,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233877628522498,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/schoolmaterialmsdsfileedit","IsCommon":false,"Id":848233877628522499,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":848233895609503744,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/punit/punitgetcountybycityid","IsCommon":false,"Id":848233895609503745,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":848233895609503746,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/bdictionary/getdictionarycombox","IsCommon":false,"Id":848233895609503747,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchasenumstatisticsfind","IsCommon":false,"Id":848233911854043136,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/getdcpurchasestatisticsyear","IsCommon":false,"Id":848233911854043137,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233911854043138,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcapplynumstatisticsfind","IsCommon":false,"Id":848233926999674880,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848233926999674881,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233926999674882,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcapplyusernumstatisticsfind","IsCommon":false,"Id":848233940266258432,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsuser","IsCommon":false,"Id":848233940266258433,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233940266258434,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/getdcapplystatisticsyear","IsCommon":false,"Id":848233940266258435,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigfind","IsCommon":false,"Id":848233959488753664,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":848233959488753665,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetbypid","IsCommon":false,"Id":848233959488753666,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfiggetbyid","IsCommon":false,"Id":848233959488753667,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelsave","IsCommon":false,"Id":848233959488753668,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigsave","IsCommon":false,"Id":848233959488753669,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanyfind","IsCommon":false,"Id":848233975611658240,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanyinsertupdate","IsCommon":false,"Id":848233975611658241,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/puser/usergetinfo","IsCommon":false,"Id":848233975611658242,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/bconfigset/bconfigsetget","IsCommon":false,"Id":848233975611658243,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/punit/unitfindidname","IsCommon":false,"Id":848233975611658244,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/puser/userfindcompany","IsCommon":false,"Id":848233975611658245,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/puser/usergetpubbyid","IsCommon":false,"Id":848233975611658246,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanydeletebyid","IsCommon":false,"Id":848233975611658247,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanyenabled","IsCommon":false,"Id":848233975611658248,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccompanygetbyid","IsCommon":false,"Id":848233975611658249,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcunitlicenseinfogetcommany","IsCommon":false,"Id":848233975611658250,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcunitlicenseinfosave","IsCommon":false,"Id":848233975611658251,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddresslistfind","IsCommon":false,"Id":848233997791137792,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressinsertupdate","IsCommon":false,"Id":848233997791137793,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":848233997791137794,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressdelete","IsCommon":false,"Id":848233997791137795,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcdepositaddressgetbyid","IsCommon":false,"Id":848233997791137796,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/attachmentsavefile","IsCommon":false,"Id":848233997791137797,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/attachmentdeletefile","IsCommon":false,"Id":848233997791137798,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcstandbookpurchasefind","IsCommon":false,"Id":848234024311721984,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcstandbookpurchasefind","IsCommon":false,"Id":848234043718766592,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848234059225108480,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplyfind","IsCommon":false,"Id":848234073095671808,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcstandbookstockfind","IsCommon":false,"Id":848234104657809408,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcstandbookstockfind","IsCommon":false,"Id":848234128552759296,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcstandbookwastefind","IsCommon":false,"Id":848234144424005632,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcstandbookwastefind","IsCommon":false,"Id":848234164363726848,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":850059677055062016,"RoleId":354},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":850369340178960384,"RoleId":353},{"Role":"用户业务处理","Url":"/api/hyun/process/downloadsearchlist","IsCommon":false,"Id":852873695423631360,"RoleId":3200},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetbypid","IsCommon":false,"Id":855108612094169088,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":855108612094169089,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigfind","IsCommon":false,"Id":855108612094169090,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfiggetbyid","IsCommon":false,"Id":855108612094169091,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigsave","IsCommon":false,"Id":855108612094169092,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmaterialmodelsave","IsCommon":false,"Id":855108612094169093,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":855108619165765632,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":855108619165765633,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcschoolmodelbrandsetdetail","IsCommon":false,"Id":855108619165765634,"RoleId":353},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcpurchaseendgetbyid","IsCommon":false,"Id":858307605947224064,"RoleId":354},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dcworkguidefind","IsCommon":false,"Id":859844356251586560,"RoleId":353},{"Role":"危化品领用人","Url":"/api/hyun/dcdangerchemicals/dcworkguidefind","IsCommon":false,"Id":859844388509978624,"RoleId":352},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicals/dcworkguidefind","IsCommon":false,"Id":859844412820164608,"RoleId":351},{"Role":"危化品采购审批人","Url":"/api/hyun/dcdangerchemicals/dcworkguidefind","IsCommon":false,"Id":859844439550464000,"RoleId":350},{"Role":"危化品领用审核人","Url":"/api/hyun/dcdangerchemicalsapply/dcapplybatchaudit","IsCommon":false,"Id":860088357575725056,"RoleId":351},{"Role":"危化品安全排查人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":860848695451586560,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgovernitemreportgetbyid","IsCommon":false,"Id":860848695451586561,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/bdictionary/getdictionarycombox","IsCommon":false,"Id":860848695451586562,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgovernitemreportsave","IsCommon":false,"Id":860848695451586563,"RoleId":355},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":860881012219973632,"RoleId":353},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":860881012219973633,"RoleId":353},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccataloggetclasstwo","IsCommon":false,"Id":860881051629654016,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dccabinetaddressget","IsCommon":false,"Id":860881051629654017,"RoleId":354},{"Role":"危化品审核人","Url":"/api/hyun/dcdangerchemicalszb/dcscrapsave","IsCommon":false,"Id":862280355703427072,"RoleId":353},{"Role":"危化品安全排查人","Url":"/api/hyun/puser/userfindmyunitusernamebyroleid","IsCommon":false,"Id":862352952650633216,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgoverndeclaresummaryweekfind","IsCommon":false,"Id":862352952650633217,"RoleId":355},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposaldetailfind","IsCommon":false,"Id":862368211616665600,"RoleId":354},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgovernitemreportgetbyid","IsCommon":false,"Id":862649575893110784,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/bdictionary/getdictionarycombox","IsCommon":false,"Id":862649575893110785,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgovernitemreportsave","IsCommon":false,"Id":862649575893110786,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":862649575893110787,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/puser/userfindmyunitusernamebyroleid","IsCommon":false,"Id":862649608495435776,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgoverndeclaresummarymonthfind","IsCommon":false,"Id":862649608495435777,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgovernrectifylistfind","IsCommon":false,"Id":862649629638922240,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/upload/postfile","IsCommon":false,"Id":862649629638922241,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgoverndeclaredetailfind","IsCommon":false,"Id":862649667949694976,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgovernrectifyrectifysave","IsCommon":false,"Id":863112871020597248,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcgovern/dcgovernrectifygetbyid","IsCommon":false,"Id":863112871020597249,"RoleId":355},{"Role":"危化品保管人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":863371383587999744,"RoleId":354},{"Role":"危化品审核人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":863371462629658624,"RoleId":353},{"Role":"危化品采购审批人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":863371508087525376,"RoleId":350},{"Role":"危化品保管人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":863415145311768576,"RoleId":354},{"Role":"危化品审核人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":863415165373124608,"RoleId":353},{"Role":"危化品领用审核人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":863415190090158080,"RoleId":351},{"Role":"危化品采购审批人","Url":"/api/hyun/punit/punitgetschoolbycountyid","IsCommon":false,"Id":863415223434874880,"RoleId":350},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/exportdcschoolmaterialoptfind","IsCommon":false,"Id":865623013230383104,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicalsapply/exportdcapplyfind","IsCommon":false,"Id":865638157368954880,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/exportdcstandbookpurchasefind","IsCommon":false,"Id":865646409259421696,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/exportdcstandbookwastefind","IsCommon":false,"Id":865654641327607808,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/exportdcstandbookstockfind","IsCommon":false,"Id":865654692674277376,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/bdictionary/dictionaryfind","IsCommon":false,"Id":865882912833146880,"RoleId":354},{"Role":"危化品审核人","Url":"/api/hyun/bdictionary/dictionaryfind","IsCommon":false,"Id":865882949029990400,"RoleId":353},{"Role":"危化品保管人","Url":"/api/hyun/dcdangerchemicals/exportdcscraplist","IsCommon":false,"Id":865958630422220800,"RoleId":354},{"Role":"用户业务处理","Url":"/api/hyun/process/saveapproval","IsCommon":false,"Id":868811009463160832,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getlinkagedatasource","IsCommon":false,"Id":870973611140190208,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getcontroldetaildatasource","IsCommon":false,"Id":870973611140190209,"RoleId":3200},{"Role":"单位管理员","Url":"/api/hyun/process/getgroupitemuserlist","IsCommon":false,"Id":871878945509740544,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/process/getwaitaudituserlist","IsCommon":false,"Id":871878945509740545,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/process/setgroupunitaudituser","IsCommon":false,"Id":871878945509740546,"RoleId":30},{"Role":"用户业务处理","Url":"/api/hyun/process/getprojectdeclarationcodename","IsCommon":false,"Id":872095231011459072,"RoleId":3200},{"Role":"用户业务处理","Url":"/api/hyun/process/getprojectcode","IsCommon":false,"Id":872240339316903936,"RoleId":3200},{"Role":"单位管理员","Url":"/api/hyun/thequipmentcategory/thequipmentcategoryschoolfind","IsCommon":false,"Id":872559562580824064,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/thequipmentcategory/thequipmentcategoryschoolsave","IsCommon":false,"Id":872559562580824065,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/saveschoolunitinfo","IsCommon":false,"Id":875732557457526784,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/getschoolunitinfo","IsCommon":false,"Id":875732557457526785,"RoleId":30},{"Role":"用户业务处理","Url":"/api/hyun/process/getprocessindexinfo","IsCommon":false,"Id":876502310367268864,"RoleId":3200},{"Role":"危化品保管人","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":878206981066723328,"RoleId":354},{"Role":"危化品保管人","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":878207136969003008,"RoleId":354},{"Role":"危化品安全排查人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalauditfind","IsCommon":false,"Id":878207979692756992,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalaudit","IsCommon":false,"Id":878207979692756993,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/dcdangerchemicals/dcwastedisposalgetbyid","IsCommon":false,"Id":878207979692756994,"RoleId":355},{"Role":"危化品安全排查人","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":878207979692756995,"RoleId":355},{"Role":"危化品审核人","Url":"/api/hyun/battachmentconfig/getpagedbytype","IsCommon":false,"Id":878208124652097536,"RoleId":353},{"Role":"用户业务处理","Url":"/api/hyun/process/getprocessindexinfo","IsCommon":false,"Id":880814492366147584,"RoleId":3200},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentlistfind","IsCommon":false,"Id":881215423981293569,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentinsertupdate","IsCommon":false,"Id":881220760549462016,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentenable","IsCommon":false,"Id":881222440078807040,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentdelbyid","IsCommon":false,"Id":881222440078807041,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentuserlistfind","IsCommon":false,"Id":881222440078807042,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/userindepartbatchinsert","IsCommon":false,"Id":881222440078807043,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/userindepartlistfind","IsCommon":false,"Id":881473744218886144,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/userindepartbatchdel","IsCommon":false,"Id":881473744218886145,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/getalldepartment","IsCommon":false,"Id":881473744218886146,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentuserupdate","IsCommon":false,"Id":881473744218886147,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/baddress/addressfind","IsCommon":false,"Id":881484305832873984,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/punit/departmentlistfindbyid","IsCommon":false,"Id":881492765962145792,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/baddress/addressinsertupdate","IsCommon":false,"Id":881494498100645888,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/baddress/addressgetbyid","IsCommon":false,"Id":881494498100645889,"RoleId":30},{"Role":"单位管理员","Url":"/api/hyun/baddress/addressdelbatch","IsCommon":false,"Id":881494498100645890,"RoleId":30}]

--------------------------------
2025/8/29 16:06:28|
【操作时间】：2025-08-29 04:06:28 787
【当前操作用户】：yucai 
【当前执行方法】：QueryById 
【携带的参数有】： 592123693924485, True 
【携带的参数JSON】：  
【响应时间】：4ms
【执行完成时间】：2025-08-29 04:06:28 792
【执行完成结果】：{"StaffNumber":null,"UnitId":592123124052101,"IdNumber":null,"Name":"育才小学","Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"UserId":0,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"VerifiedMobile":null,"UserType":1,"AdministratorType":2,"Wechat":null,"HeadPortrait":null,"Enabled":true,"UnitType":0,"UnitPId":0,"IsDeleted":false,"CreateId":100000000000000,"CreateBy":"超级管理员","CreateTime":"2024-09-19T14:17:30.2","ModifyId":592123595010181,"ModifyBy":"浦口教育局","ModifyTime":"2025-08-28T16:16:01.043","Version":0,"Id":592123693924485}

--------------------------------
2025/8/29 16:06:28|
【操作时间】：2025-08-29 04:06:28 801
【当前操作用户】：yucai 
【当前执行方法】：GetByUserId 
【携带的参数有】： 592123693924485 
【携带的参数JSON】：  
【响应时间】：25ms
【执行完成时间】：2025-08-29 04:06:28 827
【执行完成结果】：{"Id":592123693924485,"Name":"育才小学","AreaId":826,"UnitId":592123124052101,"UnitName":"浦口育才小学","UnitType":3,"UnitPId":592122937471109,"UnitPName":"南京市浦口教育局","AcctId":592123696050309,"AcctName":"yucai","RoleIds":null,"RoleNames":null,"StaffNumber":null,"IdNumber":null,"Sex":"1","Birthday":null,"Address":null,"ZipCode":null,"Tel":null,"Mobile":"15000000000","Qq":"394023002","Email":null,"RegTime":"1900-01-01T00:00:00","Memo":null,"Statuz":1,"NickName":null,"CreateId":0,"AcctStatuz":1,"UserType":1,"AdministratorType":2,"UserValidate":null,"IsThirdClient":null,"Pwd":null,"DepartmentIds":null,"StrRoleIds":null,"StrRoleNames":null,"HeadPortrait":null,"UnitStatus":1}

