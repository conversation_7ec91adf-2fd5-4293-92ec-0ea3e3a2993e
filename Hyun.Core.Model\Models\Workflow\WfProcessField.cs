namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批流程字段表
    ///</summary>
    [SugarTable("wf_ProcessField", "审批流程字段表")]
    public class WfProcessField : BaseEntity
    {

        public WfProcessField()
        {

        }

        /// <summary>
        ///对应模块表xa_Module中Id（此处模块非必填，选择后方便配置时筛选）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ModuleId { get; set; }

        /// <summary>
        /// 审批流程节点表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ProcessNodeId { get; set; }

        /// <summary>
        /// 字段Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public string FieldId { get; set; }

        /// <summary>
        /// 控件类型
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public string ControlType { get; set; }

        /// <summary>
        /// 接口调用地址
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public string ApiUrl { get; set; }

        /// <summary>
        ///字段类型（1：编辑，2：列表，3：审批，查询）
        /// </summary>
        public int FieldType { get; set; }

        /// <summary>
        ///字段来源（1：系统   2：自定义）
        /// </summary>
        public int FieldSource { get; set; } = 2;

        /// <summary>
        ///字段Code（当输入类型为附件时，字段必须为数字）
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string FieldCode { get; set; }

        /// <summary>
        ///字段名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string FieldName { get; set; }

        /// <summary>
        ///显示名称
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string ShowName { get; set; }

        /// <summary>
        ///帮助文字
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string HelpRemark { get; set; }

        /// <summary>
        ///帮助文字显示方式（1：问号提示，2：直接提示）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? HelpShowMode { get; set; }

        /// <summary>
        ///类型框
        ///1：从基础字典表(b_Dictionary)读取数据；
        ///2：从审批字典表(wf_Dictionary)读取数据；
        ///3：联系人组件(SysUserExtension)；
        ///4：年度组件 
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int TypeBox { get; set; }

        /// <summary>
        ///是否必填(2：否  1：是)默认2
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int IsRequired { get; set; } = 2;

        /// <summary>
        ///状态(1：启用 2：禁用)默认1
        /// </summary>
        public int Statuz { get; set; } = 1;

        /// <summary>
        ///验证类型（文本框JS验证参照common/js/validate.js；typebox=8时，为上传附件类型）   
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ValidType { get; set; }

        /// <summary>
        ///下拉框绑定数据脚本（textbox为6时，存sql语句，为5、9时候存数据）
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string SqlCondition { get; set; }


        /// <summary>
        ///是否作为条件(2：否 1：是)默认2
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int IsCondition { get; set; } = 2;

        /// <summary>
        ///排序值（值越小越排前面）默认0
        /// </summary>
        public int Sort { get; set; } = 0;

        /// <summary>
        ///分类编码（50000~60000）（当typebox=2和6时，配置数据存Xa_Dictionary；typebox=3时，配置数据存zj_Catalog）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? TypeCode { get; set; }

        /// <summary>
        ///模板下载地址（提供附件模板下载）
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string TemplateUrl { get; set; }

        /// <summary>
        ///宽度
        /// </summary>
        public int? Width { get; set; }

        /// <summary>
        ///标题对齐方式（center、left、right）
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string TitleStyle { get; set; }

        /// <summary>
        ///内容对齐方式（center、left、right）
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string ContentStyle { get; set; }

        /// <summary>
        ///列头是否支持排序，用于列表（1：支持排序  2：不支持排序）默认1
        /// </summary>
        public int IsSort { get; set; } = 2;

        /// <summary>
        ///上级字段Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long Pid { get; set; } = 0;

        /// <summary>
        ///项目清单数据类型（1：录入  2：选择  3：选择+录入）
        /// </summary>
        public int ProjectListType { get; set; }

        /// <summary>
        ///项目清单标题
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ProjectListTitle { get; set; }

        /// <summary>
        ///项目清单列表说明文字（列头标题问号显示的文字）
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ProjectListDesc { get; set; }

        /// <summary>
        ///自筹资金名称，有内容显示自筹资金，否则为空
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string SelfFundName { get; set; }

        /// <summary>
        ///是否继承之前数据（2：否  1：是）
        /// </summary>
        public int IsInheritData { get; set; } = 2;

        /// <summary>
        /// 默认值
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string DefaultValue { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 控制字段Code
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ControlFieldCode { get; set; }

        /// <summary>
        /// 控制字段名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ControlFieldName { get; set; }


        /// <summary>
        /// 被控制字段名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ControlledName { get; set; }

        /// <summary>
        /// 父级Code
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long TypePCode { get; set; }

        /// <summary>
        /// 子级Code
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long TypeCCode { get; set; }

        /// <summary>
        /// 父节点Code
        /// </summary>

        [SugarColumn(Length = 255, IsNullable = true)]
        public string ParentCode { get; set; }

        /// <summary>
        /// 主控
        /// </summary>
        [SugarColumn(DefaultValue = "0", IsNullable = true)]
        public int? MasterControl { get; set; }

        /// <summary>
        /// 被控
        /// </summary>
        [SugarColumn(DefaultValue = "0", IsNullable = true)]
        public int? Controlled { get; set; }


        /// <summary>
        /// 是否金额控制（1：是，2：否）
        /// </summary>
        [SugarColumn(DefaultValue = "2")]
        public int IsAmountControl { get; set; } = 2;

        /// <summary>
        /// 是否查看显示（1：是，2：否）默认否，如果为否则创建就生成编号，是的话转交下一步创建
        /// </summary>
        [SugarColumn(DefaultValue = "2")]
        public int IsDetail { get; set; } = 2;


        /// <summary>
        /// 是否继承上一次数据（1：是，2：否）默认否(去除，放表单配置处)
        /// </summary>
        [SugarColumn(DefaultValue = "2")]
        public int IsUsePreData { get; set; }

    }

}

