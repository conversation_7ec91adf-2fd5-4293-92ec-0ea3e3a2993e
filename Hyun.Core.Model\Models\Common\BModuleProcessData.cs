namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///首页模块流程数据
    ///</summary>
    [SugarTable("b_ModuleProcessData","首页模块流程数据")]
    public class BModuleProcessData : BaseEntity
    {

          public BModuleProcessData()
          {

          }

           /// <summary>
           ///Id1主键id
          /// </summary>
          public long Id1 { get; set; }

           /// <summary>
           ///备用id，对应状态中forma2参数
          /// </summary>
          public long Id2 { get; set; }

           /// <summary>
           ///备用id，对应状态中format3参数
          /// </summary>
          public long Id3 { get; set; }

           /// <summary>
           ///名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///状态值
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? Statuz { get; set; }

           /// <summary>
           ///状态描述
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string StatuzDesc { get; set; }

           /// <summary>
           ///下一步操作单位Id（结束传0，未知传0，如待创建履约项目）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? UnitId { get; set; }

           /// <summary>
           ///下一步操作单位类型（1：市；2区；3：校；4：企业）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? UnitTypeId { get; set; }

           /// <summary>
           ///单位Id（不涉及存0）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? SchoolId { get; set; }

           /// <summary>
           ///区县Id（不涉及存0）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? CountyId { get; set; }

           /// <summary>
           ///市级id（不涉及存0）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? CityId { get; set; }

           /// <summary>
           ///企业id（不涉及存0）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? CompanyId { get; set; }

           /// <summary>
           ///所在模块Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ModuleProcessConfigId { get; set; }

           /// <summary>
           ///上一模块Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ModuleProcessConfigIdLast { get; set; }

           /// <summary>
           ///是否当前（0：否  1：是）
          /// </summary>
          public int IsCurrent { get; set; }

           /// <summary>
           ///记录类型（1：正常；2：虚拟，当前流程结束，下一个流程未开始前的记录；3：填充节点，开始没有流程的）
          /// </summary>
          public int RecordType { get; set; }

           /// <summary>
           ///发生金额
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? AmountIncurred { get; set; }

           /// <summary>
           ///上一模块金额
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? PreModuleAmount { get; set; }

           /// <summary>
           ///余额
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? Balance { get; set; }

           /// <summary>
           ///上一模块节点Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? PreModuleNodeId { get; set; }

           /// <summary>
           ///更新时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///是否模块结束（结束且UnitId=0时待处理中不显示，UnitId=0且IsEnd=0表示未知操作人的开始节点）
          /// </summary>
          public int IsEnd { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


        /// <summary>
		/// 模块名称
		/// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ShowName { get; set; }
        /// <summary>
		/// 显示图标
		/// </summary>
		[SugarColumn(IsIgnore = true)]
        public string ShowIconName { get; set; }
        /// <summary>
		/// 模块编号
		/// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ModuleCode { get; set; }
        /// <summary>
		///  模块排序值
		/// </summary>
        [SugarColumn(IsIgnore = true)]
        public int ModuleSort { get; set; }
        /// <summary>
        /// 链接地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ListUrl { get; set; }
        /// <summary>
        /// 市级链接地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ListUrl1 { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string DetailUrl1 { get; set; }

        /// <summary>
        /// 区县链接地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ListUrl2 { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string DetailUrl2 { get; set; }
        /// <summary>
        /// 单位链接地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ListUrl3 { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string DetailUrl3 { get; set; }
        /// <summary>
        /// 企业链接地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ListUrl4 { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string DetailUrl4 { get; set; }
        /// <summary>
        /// 角色列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string RoleIdz { get; set; }
        /// <summary>
        /// 统计数量
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long? Num { get; set; }
    }


}

