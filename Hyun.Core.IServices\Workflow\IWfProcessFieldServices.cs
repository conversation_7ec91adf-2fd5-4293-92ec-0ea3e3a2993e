﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfProcessField接口方法
    ///</summary>
    public interface IWfProcessFieldServices : IBaseServices<WfProcessField>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<WfProcessField> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<WfProcessField>> Find(Expression<Func<WfProcessField, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">WfProcessFieldParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<WfProcessField>> GetPaged(WfProcessFieldParam param);

        //<used>1</used>
        Task<Result> InsertUpdate(WfProcessField o);

        //<used>1</used>
        Task<Result> DeleteById(long id);

        //<used>1</used>
        Task<PageModel<WfProcessField>> GetLinkAgePaged(WfProcessFieldParam param);

        //<used>1</used>
        Task<List<WfProcessField>> GetFieldParentCombox(long id);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<WfProcessField>> GetSelectPage(WfProcessFieldParam param);

        /// <summary>
        /// 获取选择字段列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<WfProcessFieldDto>> GetChooseColumnPage(WfProcessFieldParam param);

        /// <summary>
        /// 选择列确认
        /// </summary>
        /// <param name="listField"></param>
        /// <returns></returns>
        Task<Result<string>> SaveColumn(ChoseColumnModel listField);

        /// <summary>
        /// 选择资金来源字段
        /// </summary>
        /// <param name="choseColumn"></param>
        /// <returns></returns>
        Task<Result<string>> SaveFund(ChoseColumnModel choseColumn);

        /// <summary>
        /// 绑定资金来源字段
        /// </summary>
        /// <param name="pageColumnConfigId"></param>
        /// <param name="processFieldId"></param>
        /// <returns></returns>
        Task<Result<string>> SaveSourceFundField(long pageColumnConfigId, long processFieldId);


        /// <summary>
        /// 选择清单列确认
        /// </summary>
        /// <param name="listField"></param>
        /// <returns></returns>
        Task<Result<string>> SaveProjectListColumn(ChoseColumnModel listField);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="typeBox"></param>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        Task<List<dropdownModel>> GetDropList(int typeBox, string typeCode);


        /// <summary>
        /// 批量保存
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<Result<string>> BatchUpdateListEdit(List<WfPageColumnConfigDto> list);
    }
}

