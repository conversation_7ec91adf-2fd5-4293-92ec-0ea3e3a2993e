﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcScrap接口方法
    ///</summary>
    public interface IDcScrapServices : IBaseServices<DcScrap>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<DcScrap> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<DcScrap>> Find(Expression<Func<DcScrap, bool>> expression);
        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">DcScrapParam实体参数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<List<DcScrap>> Find(DcScrapParam param);
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcScrapParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<DcScrap>> GetPaged(DcScrapParam param);

        //<used>0</used>
        Task<List<DcScrap>> Insert(List<DcScrap> entityCollection);

        //<used>0</used>
        Task<List<DcScrap>> Delete(List<DcScrap> entityCollection);

        //<used>1</used>
        Task<Result> Disposal(long unitId, long userId, string ids);
        #region 查询统计
        /// <summary>
        /// 单位危化品报废列表(原 ： V_dc_ScrapList GetPaged)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcScrapList>> GetListPaged(VDcScrapListParam param);
        #endregion
    }
}

