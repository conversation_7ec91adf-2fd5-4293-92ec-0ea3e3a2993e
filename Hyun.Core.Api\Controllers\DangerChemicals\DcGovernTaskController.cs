﻿using Dm;
using Google.Protobuf.Collections;
using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Old.Util;
using MongoDB.Bson;
using NPOI.Util;
using OfficeOpenXml.VBA;
using SkyWalking.NetworkProtocol.V3;
using SqlSugar;
using StackExchange.Redis;
using System.Collections.Generic;
using System.Reflection.Metadata;

namespace Hyun.Core.Api
{

    [Route("api/hyun/dcgoverntask")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class DcGovernTaskController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IDcGovernTaskServices dcGovernTaskManager;
        private readonly IVSysStatServices vSysStatManager;
        private readonly IVCountyDeclareUnitServices vCountyDeclareUnitManager;
        private readonly IVCountyCityUnitServices vCountyCityUnitManager;
        private readonly IDcGovernTaskUnitServices dcGovernTaskUnitManager;
        private readonly IDcGovernDeclareDetailServices dcGovernDeclareDetailManager;
        private readonly IDcGovernSetServices dcGovernSetManager;
        private readonly IDcGovernDeclareSummaryServices dcGovernDeclareSummaryManager;
        private readonly IDcGovernRectifyServices dcGovernRectifyManager;
        private readonly IUser user;
        private readonly IBAttachmentServices bAttachmentManager;

        public DcGovernTaskController(IMapper _mapper, IWebHostEnvironment _env, IDcGovernTaskServices _dcGovernTaskManager,IVSysStatServices _vSysStatManager,
            IVCountyCityUnitServices _vCountyCityUnitManager, IVCountyDeclareUnitServices _vCountyDeclareUnitManager, IDcGovernTaskUnitServices _dcGovernTaskUnitManager, IDcGovernDeclareDetailServices _dcGovernDeclareDetailManager, IDcGovernSetServices _dcGovernSetManager,
            IDcGovernDeclareSummaryServices _dcGovernDeclareSummaryManager, IDcGovernRectifyServices _dcGovernRectifyManager, IUser _user, IBAttachmentServices _bAttachmentManager)
        {
            mapper = _mapper;
            env = _env;
            dcGovernTaskManager = _dcGovernTaskManager;
            vSysStatManager = _vSysStatManager;
            vCountyDeclareUnitManager = _vCountyDeclareUnitManager;
            vCountyCityUnitManager = _vCountyCityUnitManager;
            dcGovernTaskUnitManager = _dcGovernTaskUnitManager;
            dcGovernDeclareDetailManager = _dcGovernDeclareDetailManager;
            dcGovernSetManager = _dcGovernSetManager;
            dcGovernDeclareSummaryManager = _dcGovernDeclareSummaryManager;
            dcGovernRectifyManager = _dcGovernRectifyManager;
            user = _user;
            bAttachmentManager = _bAttachmentManager;
        }

        /// <summary>
        /// 获取当前危化品治理任务信息-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskunitbytasknamefindbyid")]
        //<used>1</used>
        public async Task<Result> GovernTaskUnitByTaskName_FindbyId()
        {
            Result r = new Result();
            var pg = await dcGovernTaskManager.GetPaged(new DcGovernTaskParam { UnitId = user.UnitId, Statuz = 0, pageIndex = 1, pageSize = 1 });
     
            if (pg.dataCount > 0)
            {
                var task = pg.data.FirstOrDefault();
                r.data.total = pg.dataCount;
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = new { task.Id, task.Name };
            }
            else
            {
                r.flag = 0;
                r.msg = "";
            }
            return r;
        }

        /// <summary>
        /// 被检查单位列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskunitlistfind")]
        public async Task<Result<PageModel<VDcGovernTaskSummaryList>>> GovernTaskUnitList_Find([FromBody] VDcGovernTaskSummaryListParam param)
        {
            Result r = new Result();
            param.UnitId = user.UnitId;
            param.UnitTypeId = user.UnitTypeId;
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "GovernTaskId", SortType = "ASC" } };
            }
            PageModel<VDcGovernTaskSummaryList> pg = await dcGovernTaskManager.GetSummaryPaged(param);

            List<DcGovernTask> list = await dcGovernTaskManager.Query(f => f.Statuz == 0 && f.UnitId == user.UnitId);
            if (list.Count > 0)
            {
                pg.Other = new { GovernTask = list[0] };
            }

            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 选择单位列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskschoollistfind")]
        public async Task<Result<PageModel<VCountyDeclareUnit>>> GovernTaskSchoolList_Find([FromBody] VCountyDeclareUnitParam param)
        {
            Result r = new Result();

            if (user.UnitTypeId == 2)
            {
                param.CountyUnitId = user.UnitId;
            }
            else if (user.UnitTypeId == 1)
            {
                param.CityUnitId = user.UnitId;
            }
            //查询未填写检查结果的单位集合
            var UnitIdList = (await dcGovernTaskUnitManager.Query(t => t.GovernTaskId == param.GovernTaskId)).Select(t => t.UnitId).ToList();
            if (UnitIdList != null && UnitIdList.Count > 0)
            {
                param.SchoolIdArr = UnitIdList;
            }

            List<SortBaseModel> listModel = new List<SortBaseModel>() { new SortBaseModel { SortCode = "SchoolId", SortType = "ASC" } };
            param.sortModel = listModel;
            PageModel<VCountyDeclareUnit> pg = await vCountyDeclareUnitManager.GetPaged(param);
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 添加单位(废除，都用批量添加)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskschooladd")]
        //<used>1</used>
        public async Task<Result> GovernTaskSchool_Add([FromBody] DcGovernTaskUnitDto model)
        {
            Result r = new Result();
            r = await dcGovernTaskUnitManager.SchoolInsert(model.SchoolId, model.Name, DateTime.Now.Year, user.UserId, user.UnitId, user.UnitTypeId);
            string strJson = string.Format("【schoolId:{0},name:{1}】", model.SchoolId, model.Name);
            if (r.flag == 1)
            {
                r.msg = "添加成功";
                //文本日志
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】添加检查单位成功,数据信息为：{2}！", user.UnitName, user.UserName, strJson), ApplicationConfig.CommonLogFilePath + "DangerChemicals\\");
            }
            else
            {
                r.msg = "添加失败";
                //文本日志
                strJson += ",失败原因：" + r.msg;
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】添加检查单位失败,失败原因：{2},数据信息为：{3}！", user.UnitName, user.UserName, strJson), ApplicationConfig.CommonLogFilePath + "DangerChemicals\\");
            }
            return r;
        }


        /// <summary>
        /// 批量添加单位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskschoolbatchadd")]
        public async Task<Result> GovernTaskSchool_BatchAdd([FromBody] GovernTaskModelDto model)
        {
            Result r = new Result();
            if (model.ListSchoolId.Count > 0)
            {
                foreach (var item in model.ListSchoolId)
                {
                    r = await dcGovernTaskUnitManager.SchoolInsert(item, model.Name, DateTime.Now.Year, user.UserId, user.UnitId, user.UnitTypeId);
                }
            }
            return r;
        }

        /// <summary>
        /// 删除单位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskschooldelete")]
        public async Task<Result> GovernTaskSchool_Delete([FromBody] GovernTaskDelDto model)
        {
            Result r = new Result();
            r = await dcGovernTaskUnitManager.SchoolDelete(model.TaskId, model.SchoolId, user.UserId, user.UnitId, user.UnitTypeId);
            string strJson = string.Format("【taskId:{0},schoolId:{1}】", model.TaskId, model.SchoolId);
            if (r.flag == 1)
            {
                //文本日志
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】删除检查单位成功,数据信息为：{2}！", user.UnitName, user.UserName, strJson), ApplicationConfig.CommonLogFilePath + "DangerChemicals\\");
            }
            else
            {
                //文本日志
                strJson += ",失败原因：" + r.msg;
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】删除检查单位失败,失败原因：{2},数据信息为：{3}！", user.UnitName, user.UserName, r.msg, strJson), ApplicationConfig.CommonLogFilePath + "DangerChemicals\\");
            }

            return r;
        }

        /// <summary>
        /// 获取危化品治理任务单位数据-查询
        /// </summary>
        /// <param name="GovernTaskUnitId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskunitgetbyid")]
        //<used>1</used>
        public async Task<Result> GovernTaskUnit_GetById(long GovernTaskUnitId)
        {
            Result r = new Result();
            DcGovernTaskUnit taskUnit = await dcGovernTaskUnitManager.GetById(GovernTaskUnitId);
            if (taskUnit != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = new { taskUnit.Id, taskUnit.CheckingDate, taskUnit.Leaderusers, taskUnit.ExpertUsers, taskUnit.WorkUsers };

                //查询附件信息
                List<BAttachment> list = await bAttachmentManager.Query(f => f.ObjectId == GovernTaskUnitId && f.IsDelete == 0 && f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.Inspection.ObjToInt());
                r.data.footer = list;
            }
            return r;
        }

        /// <summary>
        /// 保存检查基本信息-保存
        /// </summary>
        /// <param name="model">参数实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskunitupdate")]
        //<used>1</used>
        public async Task<Result> GovernTaskUnit_Update([FromBody] DcGovernTaskUnitDto model)
        {
            Result r = new Result();
            //DcGovernTaskUnit o = mapper.Map<DcGovernTaskUnit>(model);
            long id = model.Id;
            if (!string.IsNullOrEmpty(model.Leaderusers))
            {
                model.Leaderusers = model.Leaderusers.TrimEnd(';');
                model.Leaderusers = model.Leaderusers.TrimEnd('；');
                model.Leaderusers = model.Leaderusers.Replace('；', ';');
                model.LeaderusersCount = model.Leaderusers.Split(';').Length;
            }
            if (!string.IsNullOrEmpty(model.ExpertUsers))
            {
                model.ExpertUsers = model.ExpertUsers.TrimEnd(';');
                model.ExpertUsers = model.ExpertUsers.TrimEnd('；');
                model.ExpertUsers = model.ExpertUsers.Replace('；', ';');
                model.ExpertUsersCount = model.ExpertUsers.Split(';').Length;
            }
            if (!string.IsNullOrEmpty(model.WorkUsers))
            {
                model.WorkUsers = model.WorkUsers.TrimEnd(';');
                model.WorkUsers = model.WorkUsers.TrimEnd('；');
                model.WorkUsers = model.WorkUsers.Replace('；', ';');
                model.WorkUsersCount = model.WorkUsers.Split(';').Length;
            }
            DateTime dtCheckDate = DateTime.Parse(model.CheckingDate.ToString());
            model.CheckingUserNum = model.LeaderusersCount + model.ExpertUsersCount + model.WorkUsersCount;
            r = await dcGovernTaskUnitManager.InsertUpdate(model.GovernTaskId, dtCheckDate, model.Leaderusers, model.LeaderusersCount, model.ExpertUsers, model.ExpertUsersCount, model.WorkUsers, model.WorkUsersCount, model.CheckingUserNum, user.UserId, user.UnitId, model.Id, model.ListFileId);
            return r;
        }


        /// <summary>
        /// 危化品检查结束任务-修改
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskfinish")]
        //<used>1</used>
        public async Task<Result> GovernTask_Finish(long taskId, string name)
        {
            Result r = new Result();
            r = await dcGovernTaskManager.EndTask(taskId, name, user.UnitId, user.UserId);
            string strJson = string.Format("【taskId:{0},name:{1}】", taskId, name);
            if (r.flag == 1)
            {
                r.msg = "检查结束";
                //文本日志
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】结束本次检查成功,数据信息为：{2}！", user.UnitName, user.UserName, strJson), ApplicationConfig.CommonLogFilePath + "DangerChemicals\\");
            }
            else
            {
                r.flag = 0;
                r.msg = "操作失败:" + r.msg;
                strJson += ",失败原因：" + r.msg;
                //文本日志
                FileLog.LogMessage(string.Format("用户：【{0}-{1}】结束本次检查失败,失败原因：{2},数据信息为：{3}！", user.UnitName, user.UserName, r.msg, strJson), ApplicationConfig.CommonLogFilePath + "DangerChemicals\\");
            }
            return r;
        }

        /// <summary>
        /// 已创建检查记录列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskedlistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcGovernTask>>> GovernTaskedList_Find([FromBody] DcGovernTaskParam param)
        {
            Result r = new Result();
            param.UnitId = user.UnitId;
            if(param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Id", SortType = "DESC" } };
            }
            var pg = await dcGovernTaskManager.GetPaged(param);
            if (pg.data != null && pg.data.Count > 0)
            {
                //添加统计
                var listSum = await dcGovernTaskManager.GetStatistics(param);
                pg.Statistics = listSum;
            }
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 问题隐患清单-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("problemgovernrectifylistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcProblemGovernRectifyList>>> ProblemGovernRectifyList_Find([FromBody] VDcProblemGovernRectifyListParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            param.IsTallyClaim = 1;
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel>() { new SortBaseModel { SortCode = "GovernTaskId", SortType = "ASC" } };
            }
            PageModel<VDcProblemGovernRectifyList> pg = await dcGovernDeclareDetailManager.GetProblemPaged(param);
            if (pg.dataCount > 0)
            {
                foreach(var obj in pg.data)
                {
                    if (obj.RectifyLimit != null)
                    {
                        obj.RectifyLimit = DateTime.Parse(obj.RectifyLimit.Value.ToString().Split(' ')[0] + " 23:59:59");
                    }
                }
            }
            return baseSucc(pg, pg.dataCount);
        }

        /// <summary>
        /// 单位危险化学品安全综合治理检查记录表打印-打印
        /// </summary>
        /// <param name="GovernTaskId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("governtaskunitlistprint")]
        //<used>1</used>
        public async Task<Result> GovernTaskUnitList_Print(long GovernTaskId)
        {
            Result r = new Result();
            List<DcGovernSet> listSet = await dcGovernSetManager.Query(f => f.UnitId == user.UnitId);
            if (listSet.Count > 0)
            {
                DcGovernSet governSet = listSet[0];
                r.data.headers = new { governSet.UnitName, governSet.UserName, governSet.UserPhoneNumber };
                var list = await dcGovernTaskManager.GetSummaryPaged(new VDcGovernTaskSummaryListParam { GovernTaskId = GovernTaskId, UnitId = user.UnitId, pageIndex = 1, pageSize = int.MaxValue });
                r.flag = 1;
                r.msg = "";
                r.data.total = list.dataCount;
                r.data.rows = list;

                //查询问题与隐患清单
                VDcProblemGovernRectifyListParam vdcPGRParam = new VDcProblemGovernRectifyListParam();
                vdcPGRParam.GovernTaskId = GovernTaskId;
                vdcPGRParam.IsTallyClaim = 1;
                vdcPGRParam.pageIndex = 1;
                vdcPGRParam.pageSize = int.MaxValue;
                if (user.UnitTypeId == 1)
                {
                    vdcPGRParam.CityId = user.UnitId;
                }
                else if (user.UnitTypeId == 2)
                {
                    vdcPGRParam.CountyId = user.UnitId;
                }

                var listItem = await dcGovernDeclareDetailManager.GetProblemPaged(vdcPGRParam);
                if (listItem != null && listItem.dataCount > 0)
                {
                    r.data.footer = listItem.data;
                }
            }
            return r;
        }

        /// <summary>
        /// 查询问题与隐患清单周/月统计表-打印
        /// </summary>
        /// <param name="param">查询实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("problemdangerstatisticsprintprint")]
        //<used>1</used>
        public async Task<Result> ProblemDangerStatisticsPrint_Print([FromBody] VDcGovernDeclareSummaryParam param)
        {
            Result r = new Result();
            var vDcGRListParam = new VDcGovernRectifyListParam();
            if (param != null)
            {
                vDcGRListParam.ReportType = param.ReportType;
                vDcGRListParam.GovernYear = param.GovernYear.ObjToInt();
                vDcGRListParam.NumberCyclez = param.NumberCyclez;
            }

            DcGovernSet governSet = (await dcGovernSetManager.GetPaged(new DcGovernSetParam { unitId = user.UnitId, pageIndex = 1, pageSize = 1 })).data.FirstOrDefault();
            if (governSet != null)
            {
                r.data.headers = new { governSet.UnitName, governSet.UserName, governSet.UserPhoneNumber };

                if (user.UnitTypeId == 2)
                {
                    param.CountyId = user.UnitId;
                    vDcGRListParam.CountyId = user.UnitId;
                }
                else if (user.UnitTypeId == 1)
                {
                    param.CityId = user.UnitId;
                    vDcGRListParam.CityId = user.UnitId;
                }
                param.sortModel = new List<SortBaseModel> {
                    new SortBaseModel { SortCode = "CountyId", SortType = "ASC" },
                    new SortBaseModel { SortCode = "SchoolId", SortType = "ASC" }
                };
                param.pageIndex = 1;
                param.pageSize = int.MaxValue;

                vDcGRListParam.sortModel = new List<SortBaseModel> {
                    new SortBaseModel { SortCode = "CountyId", SortType = "ASC" },
                    new SortBaseModel { SortCode = "SchoolId", SortType = "ASC" }
                };
                vDcGRListParam.pageIndex = 1;
                vDcGRListParam.pageSize = int.MaxValue;

                var list = await dcGovernDeclareSummaryManager.GetWeekPaged(param);
                r.flag = 1;
                r.data.total = list.dataCount;
                r.data.rows = list.data;

                //查询问题与隐患清单
                List<VDcGovernRectifyList> listItem = (await dcGovernRectifyManager.GetStatisticsPaged(vDcGRListParam)).data;
                if (listItem.Count > 0)
                {
                    r.data.footer = listItem;
                }
            }
            r.flag = 1;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 验证单位信息是否已经设置-保存
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("printcheck")]
        //<used>1</used>
        public async Task<Result> Print_Check()
        {
            Result r = new Result();
            DcGovernSet governSet = (await dcGovernSetManager.GetPaged(new DcGovernSetParam { pageIndex = 1, pageSize = 1, UnitId = user.UnitId ,UnitName =user.UnitName})).data.FirstOrDefault();
            if (governSet != null)
            {
                r.flag = 1;
                r.msg = "单位信息已设置";
            }
            return r;
        }

    }
}
