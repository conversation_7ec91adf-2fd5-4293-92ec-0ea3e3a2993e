﻿namespace Hyun.Core.Model
{

    ///<summary>
    ///校服采购
    ///</summary>
    public class XUniformBuyParam : BaseSearch
    {

        public XUniformBuyParam()
        {

        }

        /// <summary>
        ///Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        ///Name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        ///Ids
        /// </summary>
        public string Ids { get; set; }


        /// <summary>
        /// 年度
        /// </summary>
        public int PlanYear { get; set; } = 0;

        /// <summary>
        /// 是否需要招标（1：是 2：否）
        /// </summary>
        public int IsNeedBidding { get; set; } = 0;

        /// <summary>
        /// 组织形式
        /// </summary>
        public int Organizational { get; set; } = 0;

        /// <summary>
        /// 采购申请状态
        /// </summary>
        public int PurchaseStatuz { get; set; } = -1;

        /// <summary>
        /// 采购批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        /// 区域Id
        /// </summary>
        public long CountyId { get; set; }
    }


}

