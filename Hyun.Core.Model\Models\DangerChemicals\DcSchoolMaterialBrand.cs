namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位物品品牌
    ///</summary>
    [SugarTable("dc_SchoolMaterialBrand","单位物品品牌")]
    public class DcSchoolMaterialBrand : BaseEntity
    {

          public DcSchoolMaterialBrand()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///单位库Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///品牌基础库Id
          /// </summary>
          public long BaseModelBrandId { get; set; }

           /// <summary>
           ///品牌
          /// </summary>
          [SugarColumn(Length = 255)]
          public string Brand { get; set; }

           /// <summary>
           ///状态（-1：已经删除；1：启用；2：禁用）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Remark { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

