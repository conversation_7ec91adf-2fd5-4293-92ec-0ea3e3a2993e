namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///危化品治理分类填报表
    ///</summary>
    [SugarTable("dc_GovernItemReport", "危化品治理分类填报表")]
    public class DcGovernItemReport : BaseEntity
    {

        public DcGovernItemReport()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///整治年度
        /// </summary>
        public int GovernYear { get; set; }

        /// <summary>
        ///条目Id
        /// </summary>
        public long GovernItemId { get; set; }

        /// <summary>
        ///是否符合要求（1：是  0：否）
        /// </summary>
        public int IsTallyClaim { get; set; }

        /// <summary>
        ///整改期限
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? RectifyLimit { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        ///整改建议
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Suggest { get; set; }

        /// <summary>
        ///申请人Id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///创建单位Id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        ///填报单位类型
        /// </summary>
        public int UnitIdType { get; set; }

        /// <summary>
        ///申请时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 附件Id集合
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<long> AttachmentIdList { get; set; }
    }


}

