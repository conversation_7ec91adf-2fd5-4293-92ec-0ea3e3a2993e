﻿using Hyun.Core.Model.Model;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcModelStock接口方法
    ///</summary>
    public interface IDcModelStockServices : IBaseServices<DcModelStock>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<DcModelStock> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<DcModelStock>> Find(Expression<Func<DcModelStock, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">DcModelStockParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<DcModelStock>> GetPaged(DcModelStockParam param);

       //<used>0</used>
       Task<List<KbDcData>> GetKbDcApplyFx(int unitid, int unittype, int type);

       //<used>0</used>
       Task<List<KbDcData>> GetKbDcReportFx(int unitid, int unittype);

       //<used>0</used>
       Task<int> GetSchoolNum(int unitid, int unittype);

       //<used>0</used>
       Task<List<KbDcData>> GetKbDcWasteRecordFx(int unitid, int unittype);

       //<used>0</used>
       Task<List<KbDcSchoolData>> GetKbDcTrainSafeEducation(int unitid, int unittype, int year);

       //<used>0</used>
       Task<List<KbDcSchoolData>> GetKbDcEmergencyPlan(int unitid, int unittype, int year);

       //<used>0</used>
       Task<DataTable> KbValiditySchoolList_Find(DcModelStockParam param);

       //<used>0</used>
       Task<DataTable> KbStorageSchoolList_Find(DcModelStockParam param);

       //<used>0</used>
       Task<List<VDcSchoolMaterialModelConfig>> GetKbDcPropData(DcModelStockParam param);

       //<used>0</used>
       Task<List<DcSchoolCatalog>> GetKbDcCatalogData(DcModelStockParam param);

    }
}

