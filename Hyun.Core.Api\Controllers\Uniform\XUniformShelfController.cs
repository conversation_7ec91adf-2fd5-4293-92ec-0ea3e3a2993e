﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models.Uniform;
using Hyun.Core.IServices.Uniform;
using Com.Ctrip.Framework.Apollo.Internals;
namespace Hyun.Core.Api
{

    [Route("api/hyun/xuniformshelf")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformShelfController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformShelfServices ixuniformshelfManager;
        private readonly IXUniformShelfSizeServices ixuniformshelfsizeManager;
        private readonly IPUnitServices ipunitManager;
        private readonly IBAttachmentServices ibattachmentManager;
        private readonly IBDictionaryServices ibdictionaryManager;
        private readonly IXUniformConfigServices configManager;

        public XUniformShelfController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IXUniformShelfServices _ixuniformshelfManager, IXUniformShelfSizeServices _ixuniformshelfsizeManager, IPUnitServices _ipunitManager, IBAttachmentServices _ibattachmentManager, IBDictionaryServices _ibdictionaryManager, IXUniformConfigServices _configManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ixuniformshelfManager = _ixuniformshelfManager;
            ixuniformshelfsizeManager = _ixuniformshelfsizeManager;
            ipunitManager = _ipunitManager;
            ibattachmentManager = _ibattachmentManager;
            ibdictionaryManager = _ibdictionaryManager;
            configManager = _configManager;
        }

        #region 查询列表集合

        /// <summary>
        /// 校服采购-校服管理-已提交列表（供应商）
        /// </summary>
        /// <param name="param">XUniformShowParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<XUniformShelfDto>>> XUniformShowGetPaged([FromBody] XUniformShelfParam param)
        {
            Result<List<XUniformShelfDto>> r = new Result<List<XUniformShelfDto>>();
            param.unitId = user.UnitId; 
            PageModel<XUniformShelfDto> pg = await ixuniformshelfManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                r.data.rows = (from item in pg.data
                               select new XUniformShelfDto
                               {
                                   Id = item.Id,
                                   SchoolId = item.SchoolId,
                                   SchoolName = item.SchoolName,
                                   PurchaseNo = item.PurchaseNo,
                                   ContractEndDate=item.ContractEndDate,

                                   Uniformtype = item.Uniformtype,
                                   Name = item.Name,
                                   Brand = item.Brand,

                                   Producer = item.Producer,
                                   OriginAddress = item.OriginAddress,
                                   SecurityLevel = item.SecurityLevel,
                                   Price = item.Price,
                                   Parameter = item.Parameter,
                                   PurchaseDemand = item.PurchaseDemand,
                              
                                   Sex = item.Sex,
                                   SizePath = item.SizePath,
                                   Sort = item.Sort,
                                   StandardNum = item.StandardNum,
                              
                                   UnitId = item.UnitId,
                                   UnitName = item.UnitName,
                                   UseStatuz = item.UseStatuz,
                                   ModifyTime = item.ModifyTime, // == null ? item.CreateTime : item.ModifyTime,
                                   AuditStatuz = item.AuditStatuz,
                                   AuditTime = item.AuditTime
                               }).ToList();
                if (param.isFirst)
                {
                    //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                    //使用状态
                    List<dropdownModel> dropStatuzUse = new List<dropdownModel>();
                    var listStatuz = EnumExtensions.EnumToList<UniformShelfAuditEnum>();
                    if (listStatuz != null)
                    {
                        foreach (var item in listStatuz)
                        {
                            if (item.Value > 0)
                            {
                                dropStatuzUse.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                            }
                       
                        }
                    }

                    //加载单位下拉列表
                    List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                    var listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSchool != null)
                    {
                        foreach (var item in listSchool)
                        {
                            dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                    r.data.other = new { StatuzUseList = dropStatuzUse, SchoolList = dropdownSchool };
                }
            }
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                //使用状态
                List<dropdownModel> dropStatuzUse = new List<dropdownModel>();
                var listStatuz = EnumExtensions.EnumToList<UniformShelfAuditEnum>();
                if (listStatuz != null)
                {
                    foreach (var item in listStatuz)
                    {
                        if (item.Value > 0)
                        {
                            dropStatuzUse.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                        }
                    }
                }

                //加载单位下拉列表
                List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                var listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                if (listSchool != null)
                {
                    foreach (var item in listSchool)
                    {
                        dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                    }
                }
                r.data.other = new { StatuzUseList = dropStatuzUse, SchoolList = dropdownSchool };
            }
            return r;
        }

        /// <summary>
        /// 校服采购-校服管理-待审核列表（单位）
        /// </summary>
        /// <param name="param">XUniformShowParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getwaitauditpaged")]
        public async Task<Result<List<XUniformShelfDto>>> XUniformShowGetWaitAuditPaged([FromBody] XUniformShelfParam param)
        {
            Result<List<XUniformShelfDto>> r = new Result<List<XUniformShelfDto>>();
            param.SchoolId = user.UnitId;
            param.AuditStatuz = UniformShelfAuditEnum.AuditWait.ObjToInt();

         
            if (param.sortModel==null)
            {
                param.sortModel = new List<SortBaseModel>();
            }
            param.sortModel.Add(new SortBaseModel() { SortCode = "ModifyTime", SortType = "DESC" });

            PageModel<XUniformShelfDto> pg = await ixuniformshelfManager.GetAuditPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                r.data.rows = (from item in pg.data
                               select new XUniformShelfDto
                               {
                                   Id = item.Id,
                                   SchoolId = item.SchoolId,
                                   SchoolName = item.SchoolName,
                                   PurchaseNo = item.PurchaseNo,

                                   Uniformtype = item.Uniformtype,
                                   Name = item.Name,
                                   Brand = item.Brand,

                                   Producer = item.Producer,
                                   OriginAddress = item.OriginAddress,
                                   SecurityLevel = item.SecurityLevel,
                                   Price = item.Price,
                                   Parameter = item.Parameter,
                                   PurchaseDemand = item.PurchaseDemand,

                                   Sex = item.Sex,
                                   SizePath = item.SizePath,
                                   Sort = item.Sort,
                                   StandardNum = item.StandardNum,
                                   SupplierName = item.SupplierName,
                                   UnitId = item.UnitId,
                                   UnitName = item.UnitName,
                                   UseStatuz = item.UseStatuz,
                                   ModifyTime = item.ModifyTime, // == null ? item.CreateTime : item.ModifyTime,
                                   AuditStatuz = item.AuditStatuz,
                                   AuditTime = item.AuditTime
                               }).ToList();
            }
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                //使用状态
                List<dropdownModel> dropStatuzAudit = new List<dropdownModel>();
                var listAuditStatuz = EnumExtensions.EnumToList<UniformShelfAuditEnum>();
                if (listAuditStatuz != null)
                {
                    foreach (var item in listAuditStatuz)
                    {
                        if (item.Value > 0)
                        {
                            dropStatuzAudit.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                        }

                    }
                }

                //加载单位下拉列表
                List<dropdownModel> dropdownCompany = new List<dropdownModel>();
                var listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                if (listSchool != null)
                {
                    foreach (var item in listSchool)
                    {
                        dropdownCompany.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                    }
                }
                r.data.other = new { AuditStatuzList = dropStatuzAudit, SupplierList = dropdownCompany };
            }
            return r;
        }

        /// <summary>
        /// 校服采购-校服管理-已审核列表（单位）
        /// </summary>
        /// <param name="param">XUniformShowParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getauditedpaged")]
        public async Task<Result<List<XUniformShelfDto>>> XUniformShowGetAuditedPaged([FromBody] XUniformShelfParam param)
        {
            Result<List<XUniformShelfDto>> r = new Result<List<XUniformShelfDto>>();
            param.SchoolId = user.UnitId;
            param.AuditStatuzGt = UniformShelfAuditEnum.AuditWait.ObjToInt();
            PageModel<XUniformShelfDto> pg = await ixuniformshelfManager.GetAuditPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                r.data.total = pg.dataCount;
                r.data.rows = (from item in pg.data
                               select new XUniformShelfDto
                               {
                                   Id = item.Id,
                                   SchoolId = item.SchoolId,
                                   SchoolName = item.SchoolName,
                                   PurchaseNo = item.PurchaseNo,

                                   Uniformtype = item.Uniformtype,
                                   Name = item.Name,
                                   Brand = item.Brand,

                                   Producer = item.Producer,
                                   OriginAddress = item.OriginAddress,
                                   SecurityLevel = item.SecurityLevel,
                                   Price = item.Price,
                                   Parameter = item.Parameter,
                                   PurchaseDemand = item.PurchaseDemand,

                                   Sex = item.Sex,
                                   SizePath = item.SizePath,
                                   Sort = item.Sort,
                                   StandardNum = item.StandardNum,
                                   SupplierName = item.SupplierName,
                                   UnitId = item.UnitId,
                                   UnitName = item.UnitName,
                                   UseStatuz = item.UseStatuz,
                                   ModifyTime = item.ModifyTime, // == null ? item.CreateTime : item.ModifyTime,
                                   AuditStatuz = item.AuditStatuz,
                                   AuditTime = item.AuditTime,
                                   OrderedNum=item.OrderedNum
                               }).ToList(); 
            }
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                //使用状态
                List<dropdownModel> dropStatuzAudit = new List<dropdownModel>();
                var listAuditStatuz = EnumExtensions.EnumToList<UniformShelfAuditEnum>();
                if (listAuditStatuz != null)
                {
                    foreach (var item in listAuditStatuz)
                    {
                        if (item.Value > 0)
                        {
                            dropStatuzAudit.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                        }

                    }
                }

                //加载单位下拉列表
                List<dropdownModel> dropdownCompany = new List<dropdownModel>();
                var listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                if (listSchool != null)
                {
                    foreach (var item in listSchool)
                    {
                        dropdownCompany.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                    }
                }
                r.data.other = new { AuditStatuzList = dropStatuzAudit, SupplierList = dropdownCompany };
            }
            return r;
        }

        /// <summary>
        /// 校服展示列表-根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpagelist")]
        public async Task<Result<List<XUniformShelfDto>>> GetPageList([FromBody] XUniformShelfParam param)
        {
            Result<List<XUniformShelfDto>> r = new Result<List<XUniformShelfDto>>();
            if (!user.IsSystemUser)
            {
                return baseFailed<List<XUniformShelfDto>>("你无权访问。");
            }
            var pg = await ixuniformshelfManager.GetPageList(param);
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                pg.data.ForEach(x => { x.SexName = GetSexName(x.Sex); });
            }
            object other = null;
            if (param.isFirst)
            {
                //加载供应商下拉列表
                List<dropdownModel> dropdownCompany = new List<dropdownModel>();
                var listCompany = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                if (listCompany != null)
                {
                    foreach (var item in listCompany)
                    {
                        dropdownCompany.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                    }
                }
                //加载单位下拉列表
                List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                var listSchool = await ipunitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                if (listSchool != null)
                {
                    foreach (var item in listSchool)
                    {
                        dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                    }
                }
                other = new { CompanyList = dropdownCompany, SchoolList = dropdownSchool };
            }
            return baseSucc(pg.data, pg.dataCount, "查询成功", other);
        }
        /// <summary>
        /// 获取性别名称0:未知  1：男  2：女 3：男/女
        /// </summary>
        /// <param name="sex"></param>
        /// <returns></returns>
        private string GetSexName(int sex)
        {
            string name = "";
            switch (sex)
            {
                case 1:
                    name = "男";
                    break;
                case 2:
                    name = "女";
                    break;
                case 3:
                    name = "男/女";
                    break;
                default:
                    name = "未知";
                    break;
            }
            return name;
        }
        #endregion

        /// <summary>
        /// 根据Id获取对象
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPut]
        [Route("xuniformshowbyid")]
        public async Task<Result> XUniformShowById(long id)
        {
            Result r = new Result();
            XUniformShelf m = await ixuniformshelfManager.QueryById(id);
            if (m != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<XUniformShelfDto>(m);
            }
            return r;
        }

        #region 查看方法（公共）
        /// <summary>
        /// 根据Id获取对象
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbyid")]
        [AllowAnonymous]
        public async Task<Result<XUniformShelfDto>> GetById(long id)
        {
            Result< XUniformShelfDto> r = new Result<XUniformShelfDto>();
            XUniformShelf m = await ixuniformshelfManager.QueryById(id);
            if (m != null)
            { 
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = mapper.Map<XUniformShelfDto>(m);
                var entityUnit = await ipunitManager.QueryById((object)m.SchoolId);
                if (entityUnit != null)
                {
                    r.data.rows.SchoolName = entityUnit.Name;
                }
                //获取尺码
                var listSize = await ixuniformshelfsizeManager.Find(size => size.UniformShelfId == m.Id && size.IsDeleted == false);
                if (listSize != null && listSize.Count > 0)
                {
                    listSize = listSize.OrderByDescending(a => a.Sort).ToList();
                }
                var listAttachment = await ibattachmentManager.Find(att => att.ObjectId == m.Id && att.IsDeleted == false && att.ModuleType == ModuleTypeEnum.Create.ObjToInt());
                r.data.other = new { SizeList = listSize, AttachmentList = listAttachment };

                var listConfig = await configManager.Find(n => n.Code == "4000" && n.IsDeleted == false); 
                if (listConfig != null && listConfig.Count > 0)
                {
               
                    XUniformConfig entityConfig = listConfig.Where(m => m.UnitId == entityUnit.PId).First();
                    if (entityConfig==null)
                    {
                        entityConfig = listConfig.Where(m => m.UnitId == 0).First();
                    }
                    if (entityConfig!=null)
                    {
                        r.data.rows.IsShowPrice = entityConfig.ValueNum ?? 0;
                        r.data.rows.Price = r.data.rows.IsShowPrice == 0 ? 0 : r.data.rows.Price;
                    }
                }
            }
            return r;
        }
        #endregion

        #region 更新排序（供应商）
        /// <summary>
        /// 根据Id获取对象
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="sort">排序</param>
        /// <returns></returns>
        [HttpPut]
        [Route("setsortbyid")]
        public async Task<Result> SetSortById(long id,int sort)
        {
            XUniformShelfDto model=new XUniformShelfDto (); 
            model.Id = id;
            model.Sort = sort;
            model.UnitId=user.UnitId;
            return await ixuniformshelfManager.SetSortById(model); 
        }
        #endregion

        #region 提交保存

        /// <summary>
        /// 校服审核-审核（单位）
        /// </summary>
        /// <param name="id">校服上架表Id</param>
        /// <param name="statuz">审核状态1：通过  0或2审核不通过</param>
        /// <param name="purchaseid">采购批次Id</param>
        /// <param name="explain">审核说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("audit")]
        public async Task<Result> Audit(long id, int statuz, long purchaseid = 0, string explain = "")
        {
            XUniformShelfDto model = new XUniformShelfDto();
            model.Id = id;
            model.AuditStatuz = statuz;
            model.UniformPurchaseId = purchaseid;
            model.AuditExplain = explain;
            model.SchoolId = user.UnitId;
            return await ixuniformshelfManager.Audit(model);
        }


        /// <summary>
        /// 根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("deletebyid")]
        public async Task<Result> DeleteById(long id)
        {
            var model= new XUniformShelfDto();
            model.Id = id;
            model.UnitId = user.UnitId;
            return await ixuniformshelfManager.FakeDeleteById(model); 
        }

        /// <summary>
        /// 校服管理-已审审核列表-撤销
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpPut]
        [Route("revokedbyid")]
        public async Task<Result> Revoked(long id)
        {
            var model = new XUniformShelfDto();
            model.Id = id;
            model.SchoolId = user.UnitId;
            return await ixuniformshelfManager.Revoked(model);
        }

        /// <summary>
        /// 校服管理-已提交审核列表-设置状态（启用禁用）
        /// </summary>
        /// <param name="id">Id值</param>
        /// <param name="statuz">1：启用  2：禁用</param>
        /// <returns></returns>
        [HttpPut]
        [Route("setstatuzbyid")]
        public async Task<Result> SetStatuz(long id,int statuz)
        {
            var model = new XUniformShelfDto();
            model.Id = id;
            model.UseStatuz = statuz;
            model.UnitId = user.UnitId;
            return await ixuniformshelfManager.SetStatuz(model);
        }

        /// <summary>
        /// 校服管理-管理员设置是否展示
        /// </summary>
        /// <param name="id">Id值</param>
        /// <param name="statuz">1：展示  2：否不展示</param>
        /// <returns></returns>
        [HttpPut]
        [Route("setisshow")]
        public async Task<Result> SetShowStatuz(long id, int statuz)
        {
            var model = new XUniformShelfDto();
            model.Id = id;
            model.UseStatuz = statuz;
            model.UnitId = user.UnitId;
            if (!user.IsSystemUser)
            {
                return baseFailed("你无权访问。");
            }
            if (statuz==1)
            {
                model.IsShow = 1;
            }
            else
            {
                model.IsShow = 2;
            }
            return await ixuniformshelfManager.SetShowStatuz(model);
        }
        #endregion


        #region 私有方法

        #endregion
    }
}
