namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///物品编码
    ///</summary>
    [SugarTable("dc_SchoolMaterialCode","物品编码")]
    public class DcSchoolMaterialCode : BaseEntity
    {

          public DcSchoolMaterialCode()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///单位物品Id
          /// </summary>
          public long SchoolMaterialId { get; set; }

           /// <summary>
           ///数量
          /// </summary>
          [SugarColumn(IsNullable = true, ColumnDataType = "money")]
          public decimal? Num { get; set; }

           /// <summary>
           ///计量单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

        /// <summary>
        ///第三方数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal ThirdNum { get; set; }

           /// <summary>
           ///第三方计量单位
          /// </summary>
          [SugarColumn(Length = 31)]
          public string ThirdUnitName { get; set; }

           /// <summary>
           ///第三方标识码
          /// </summary>
          [SugarColumn(Length = 255)]
          public string ThirdCode { get; set; }

           /// <summary>
           ///第三方物品Id
          /// </summary>
          [SugarColumn(Length = 255)]
          public string ThirdMaterialId { get; set; }

           /// <summary>
           ///第三方物品名称
          /// </summary>
          [SugarColumn(Length = 255)]
          public string ThirdMaterialName { get; set; }

           /// <summary>
           ///操作人
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///操作时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

