﻿using AutoMapper;
using Hyun.Core.Common.DB;
using Hyun.Core.Model.Validator;
using Hyun.Core.Model.Validator.Workflow;
namespace Hyun.Core.Services
{

    ///<summary>
    ///BMsgConfig方法
    ///</summary>
    public class BMsgConfigServices : BaseServices<BMsgConfig>, IBMsgConfigServices
    {
        private readonly IMapper mapper;

        public BMsgConfigServices(IMapper _mapper)
        {
            mapper = _mapper;
        }


        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">BMsgConfigParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<BMsgConfig>> GetPaged(BMsgConfigParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = "PageId ASC";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            return await base.QueryPage(expression, param.pageIndex, param.pageSize, orderByFields);
        }

        /// <summary>
        /// 添加修改
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> InsertUpdate(BMsgConfigDto m)
        {
            BMsgConfig o = mapper.Map<BMsgConfig>(m);

            #region 增加FluentValidation验证
            var validator = new BMsgConfigValidator();
            var result = validator.Validate(o);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                return Result<string>.Fail(tipMsg);
            }
            #endregion
            
            if (o.Id > 0)
            {
                BMsgConfig obj = await this.Db.Queryable<BMsgConfig>().Where(f => f.Id == o.Id).FirstAsync();
                if (obj == null)
                {
                    return Result<string>.Fail("数据不存在，非法操作!");
                }
                //判断消息编号是否存在
                var list = await this.Db.Queryable<BMsgConfig>().Where(f => f.MsgCode == o.MsgCode && f.Id != o.Id).ToListAsync();
                if (list.Count > 0)
                {
                    return Result<string>.Fail("消息编号已经存在");
                }

                NavigationModel model = await GetNavigation(o.PageId.Value);
                obj.PageId = o.PageId;
                obj.OneNavigation = model.OneNavigation;
                obj.TwoNavigation = model.TwoNavigation;
                obj.PageName = o.PageName;
                obj.MsgCode = o.MsgCode;
                obj.MsgExplain = o.MsgExplain;
                obj.MsgShowExplain = o.MsgShowExplain;
                obj.MainSwitch = o.MainSwitch;
                obj.MsgSwitch = o.MsgSwitch;
                obj.MsgTemplate = o.MsgTemplate;
                obj.WeChatSwitch = o.WeChatSwitch;
                obj.WeChatTemplateId = o.WeChatTemplateId;
                obj.WeChatTemplate = o.WeChatTemplate;
                obj.WeChatRedirectPage = o.WeChatRedirectPage;
                obj.ClientIsShow = o.ClientIsShow;
                await this.Db.Updateable<BMsgConfig>(obj).ExecuteCommandAsync();
            }
            else
            {
                //判断消息编号是否存在
                var list = await this.Db.Queryable<BMsgConfig>().Where(f => f.MsgCode == o.MsgCode).ToListAsync();
                if (list.Count > 0)
                {
                    return Result<string>.Fail("消息编号已经存在");
                }
                // 移除手动设置ID，让系统自动生成
                NavigationModel model = await GetNavigation(o.PageId.Value);
                o.OneNavigation = model.OneNavigation;
                o.TwoNavigation = model.TwoNavigation;
                await this.Db.Insertable<BMsgConfig>(o).ExecuteCommandAsync();
            }

            return Result<string>.Success("保存成功");
        }

        /// <summary>
        /// 批量设置状态
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        public async Task<Result<string>> BatchSetStatuz(MsgConfigStatuzModel m)
        {
            List<BMsgConfig> list = await this.Db.Queryable<BMsgConfig>().Where(f => f.IsDeleted == false && m.ListId.Contains(f.Id)).ToListAsync();
            if(m.SwitchType == 1)
            {
                list.ForEach(f => f.MainSwitch = m.Statuz);
            }
            else if(m.SwitchType == 2)
            {
                list.ForEach(f => f.MsgSwitch = m.Statuz);
            }
            else if(m.SwitchType == 3)
            {
                list.ForEach(f => f.WeChatSwitch = m.Statuz);
            }
            await this.Db.Updateable<BMsgConfig>(list).ExecuteCommandAsync();
            return Result<string>.Success("批量设置成功");
        }

        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">BMsgConfigParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<BMsgConfig, bool>> ListFilter(BMsgConfigParam param)
        {
            var expression = LinqExtensions.True<BMsgConfig>();
            if (param.NavigationId != 0)
            {
                expression = expression.AndNew(f => f.OneNavigation == param.NavigationId || f.TwoNavigation == param.NavigationId || f.PageId == param.NavigationId);
            }
            if (!string.IsNullOrEmpty(param.PageName))
            {
                expression = expression.AndNew(f => f.PageName.Contains(param.PageName));
            }
            if (!string.IsNullOrEmpty(param.MsgCode))
            {
                expression = expression.AndNew(f => f.MsgCode.Contains(param.MsgCode));
            }
            return expression;
        }

        /// <summary>
        /// 根据页面Id获取一、二级导航
        /// </summary>
        /// <param name="pageId"></param>
        /// <returns></returns>
        private async Task<NavigationModel> GetNavigation(long pageId)
        {
            NavigationModel navigation = new NavigationModel();
            navigation.OneNavigation = 0;
            navigation.TwoNavigation = 0;
            var objPage = await this.Db.Queryable<SysPermission>().Where(f => f.Id == pageId).FirstAsync();
            if (objPage != null && objPage.Pid > 0)
            {
                navigation.TwoNavigation = objPage.Pid;
                var parentOjbPage = await this.Db.Queryable<SysPermission>().Where(f => f.Id == objPage.Pid).FirstAsync();
                if (parentOjbPage != null && parentOjbPage.Pid > 0)
                {
                    navigation.OneNavigation = parentOjbPage.Pid;
                }
            }
            return navigation;
        }

        #endregion
    }
}

