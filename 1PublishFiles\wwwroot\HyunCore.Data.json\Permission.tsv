﻿[
  {
    "Code": "\/",
    "Name": "首页",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": -90,
    "Icon": "fa-home",
    "Description": "33",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 1,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "用户角色管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-users",
    "Description": "11",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 2,
    "IsHide": 0
  },
  {
    "Code": "\/User\/Roles",
    "Name": "角色管理",
    "IsButton": 0,
    "Pid": 2,
    "Mid": 22,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 3,
    "IsHide": 0
  },
  {
    "Code": "\/User\/Users",
    "Name": "用户管理",
    "IsButton": 0,
    "Pid": 2,
    "Mid": 7,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 4,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "菜单权限管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-sitemap",
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 5,
    "IsHide": 0
  },
  {
    "Code": "\/Permission\/Module",
    "Name": "接口管理",
    "IsButton": 0,
    "Pid": 5,
    "Mid": 13,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 6,
    "IsHide": 0
  },
  {
    "Code": "\/Permission\/Permission",
    "Name": "菜单管理",
    "IsButton": 0,
    "Pid": 5,
    "Mid": 17,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 7,
    "IsHide": 0
  },
  {
    "Code": "\/System\/BasicSetting",
    "Name": "个人设置",
    "IsButton": 0,
    "Pid": 68,
    "Mid": 0,
    "OrderSort": 5,
    "Icon": "fa-star ",
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 8,
    "IsHide": 0
  },
  {
    "Code": "无",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 4,
    "Mid": 7,
    "OrderSort": 0,
    "Icon": null,
    "Description": "这个用户页的查询按钮",
    "Enabled": 1,
    "Func": "getUsers",
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 9,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "报表管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-line-chart",
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 10,
    "IsHide": 0
  },
  {
    "Code": "\/Form\/Charts",
    "Name": "图表",
    "IsButton": 0,
    "Pid": 10,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 11,
    "IsHide": 0
  },
  {
    "Code": "\/Form\/Form",
    "Name": "表单",
    "IsButton": 0,
    "Pid": 10,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 12,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "新增",
    "IsButton": 1,
    "Pid": 4,
    "Mid": 10,
    "OrderSort": 0,
    "Icon": null,
    "Description": "新增用户",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "Func": "handleAdd",
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 13,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "Pid": 4,
    "Mid": 12,
    "OrderSort": 0,
    "Icon": null,
    "Description": "编辑用户",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "Func": "handleEdit",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 14,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "Pid": 4,
    "Mid": 11,
    "OrderSort": 0,
    "Icon": null,
    "Description": "删除用户",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "Func": "handleDel",
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 15,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 3,
    "Mid": 22,
    "OrderSort": 0,
    "Icon": null,
    "Description": "查询 角色",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "Func": "getRoles",
    "IsDeleted": 0,
    "Id": 16,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "新增",
    "IsButton": 1,
    "Pid": 3,
    "Mid": 25,
    "OrderSort": 0,
    "Icon": null,
    "Description": "新增 角色",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "Func": "handleAdd",
    "IsDeleted": 0,
    "Id": 17,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "Pid": 3,
    "Mid": 24,
    "OrderSort": 0,
    "Icon": null,
    "Description": "编辑角色",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "Func": "handleEdit",
    "IsDeleted": 0,
    "Id": 18,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "Pid": 3,
    "Mid": 23,
    "OrderSort": 0,
    "Icon": null,
    "Description": "删除角色",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "Func": "handleDel",
    "IsDeleted": 0,
    "Id": 19,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 6,
    "Mid": 13,
    "OrderSort": 0,
    "Icon": null,
    "Description": "查询 接口",
    "Func": "getModules",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 20,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "新增",
    "IsButton": 1,
    "Pid": 6,
    "Mid": 16,
    "OrderSort": 0,
    "Icon": null,
    "Description": "新增 接口",
    "Enabled": 1,
    "Func": "handleAdd",
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 21,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "Pid": 6,
    "Mid": 15,
    "OrderSort": 0,
    "Icon": null,
    "Description": "编辑 接口",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "Func": "handleEdit",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 22,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "Pid": 6,
    "Mid": 14,
    "OrderSort": 0,
    "Icon": null,
    "Description": "删除接口",
    "Enabled": 1,
    "CreateId": 18,
    "Func": "handleDel",
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 23,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 17,
    "OrderSort": 0,
    "Icon": null,
    "Description": "查询 菜单",
    "Func": "getPermissions",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 24,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "新增",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 20,
    "OrderSort": 0,
    "Icon": null,
    "Description": "新增菜单",
    "Enabled": 1,
    "Func": "handleAdd",
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 25,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 19,
    "OrderSort": 0,
    "Icon": null,
    "Description": "编辑菜单",
    "Enabled": 1,
    "Func": "handleEdit",
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 26,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 18,
    "OrderSort": 0,
    "Icon": null,
    "Description": "删除 菜单",
    "Enabled": 1,
    "CreateId": 18,
    "Func": "handleDel",
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 27,
    "IsHide": 0
  },
  {
    "Code": "\/Tibug\/Bugs",
    "Name": "TiBug",
    "IsButton": 0,
    "Pid": 42,
    "Mid": 26,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 28,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "博客管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-file-word-o",
    "Description": null,
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 29,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "Pid": 28,
    "Mid": 5,
    "OrderSort": 0,
    "Icon": null,
    "Description": "编辑 tibug ",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "Func": "handleEdit",
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 30,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "Pid": 28,
    "Mid": 6,
    "OrderSort": 0,
    "Icon": null,
    "Description": "删除 tibug",
    "Enabled": 1,
    "CreateId": 18,
    "CreateBy": "提bug账号",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "Func": "handleDel",
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 31,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 28,
    "Mid": 26,
    "OrderSort": 0,
    "Icon": null,
    "Description": "查询 tibug",
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "Func": "getBugs",
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 32,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "菜单树",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 21,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 33,
    "IsHide": 1
  },
  {
    "Code": "\/Permission\/Assign",
    "Name": "权限分配",
    "IsButton": 0,
    "Pid": 5,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 34,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "保存权限",
    "IsButton": 1,
    "Pid": 34,
    "Mid": 28,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 35,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "左侧导航",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 29,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 36,
    "IsHide": 1
  },
  {
    "Code": "-",
    "Name": "测试页面管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-flask",
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 37,
    "IsHide": 0
  },
  {
    "Code": "\/TestShow\/TestOne",
    "Name": "测试页面1",
    "IsButton": 0,
    "Pid": 37,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 38,
    "IsHide": 0
  },
  {
    "Code": "\/TestShow\/TestTwo",
    "Name": "测试页面2",
    "IsButton": 0,
    "Pid": 37,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 39,
    "IsHide": 0
  },
  {
    "Code": "\/I18n\/index",
    "Name": "国际化",
    "IsButton": 0,
    "Pid": 41,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 40,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "多语言管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-language",
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 41,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "问题管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-bug",
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 42,
    "IsHide": 0
  },
  {
    "Code": "\/Blog\/Blogs",
    "Name": "博客",
    "IsButton": 0,
    "Pid": 29,
    "Mid": 27,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 43,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "多级路由",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-sort-amount-asc",
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 44,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "Menu-1",
    "IsButton": 0,
    "Pid": 44,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 45,
    "IsHide": 0
  },
  {
    "Code": "\/Recursion\/Menu_1\/Menu_1_2",
    "Name": "Menu-1-2",
    "IsButton": 0,
    "Pid": 45,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 46,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "Menu-1-1",
    "IsButton": 0,
    "Pid": 45,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 47,
    "IsHide": 0
  },
  {
    "Code": "\/Recursion\/Menu_1\/Menu_1_1\/Menu_1_1_1",
    "Name": "Menu-1-1-1",
    "IsButton": 0,
    "Pid": 47,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 48,
    "IsHide": 0
  },
  {
    "Code": "\/Recursion\/Menu_1\/Menu_1_1\/Menu_1_1_2",
    "Name": "Menu-1-1-2",
    "IsButton": 0,
    "Pid": 47,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 49,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 50,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 51,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 52,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 53,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 54,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 55,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 56,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 57,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 58,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 59,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 60,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 61,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 62,
    "IsHide": 0
  },
  {
    "Code": "s",
    "Name": "s",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 0,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 63,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "Pid": 43,
    "Mid": 32,
    "OrderSort": 0,
    "Icon": null,
    "Description": "删除博客按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleDel",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 64,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "日志管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": "fa-diamond",
    "Description": null,
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 65,
    "IsHide": 0
  },
  {
    "Code": "\/Logs\/Index",
    "Name": "全部日志",
    "IsButton": 0,
    "Pid": 65,
    "Mid": 33,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 66,
    "IsHide": 0
  },
  {
    "Code": "\/Blog\/Detail\/:id",
    "Name": "博客详情",
    "IsButton": 0,
    "Pid": 29,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 67,
    "IsHide": 1
  },
  {
    "Code": "-",
    "Name": "系统管理",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 1,
    "Icon": "el-icon-s-operation",
    "Description": null,
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 68,
    "IsHide": 0
  },
  {
    "Code": "\/System\/My",
    "Name": "个人中心",
    "IsButton": 0,
    "Pid": 68,
    "Mid": 0,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 69,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 69,
    "Mid": 34,
    "OrderSort": 0,
    "Icon": null,
    "Description": "Agent 代理的查询接口",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 70,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 69,
    "Mid": 35,
    "OrderSort": 0,
    "Icon": null,
    "Description": "查询 部门 Department get",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 1,
    "Id": 71,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 69,
    "Mid": 36,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 72,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 43,
    "Mid": 27,
    "OrderSort": 0,
    "Icon": null,
    "Description": "查询博客按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "getBlogs",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 73,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "Pid": 43,
    "Mid": 27,
    "OrderSort": 0,
    "Icon": null,
    "Description": "编辑博客按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleEdit",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 74,
    "IsHide": 0
  },
  {
    "Code": "-",
    "Name": "任务调度",
    "IsButton": 0,
    "Pid": 0,
    "Mid": 0,
    "OrderSort": 1,
    "Icon": "fa-history",
    "Description": null,
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 75,
    "IsHide": 0
  },
  {
    "Code": "\/Task\/QuartzJob",
    "Name": "任务列表",
    "IsButton": 0,
    "Pid": 75,
    "Mid": 37,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 76,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 37,
    "OrderSort": 0,
    "Icon": null,
    "Description": "查询任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "getTasks",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 77,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "添加",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 38,
    "OrderSort": 0,
    "Icon": null,
    "Description": "添加任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleAdd",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 78,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 39,
    "OrderSort": 0,
    "Icon": null,
    "Description": "编辑任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleEdit",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 79,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "开启",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 40,
    "OrderSort": 0,
    "Icon": null,
    "Description": "开启任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleStartJob",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 80,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "停止",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 41,
    "OrderSort": 0,
    "Icon": null,
    "Description": "停止任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleStopJob",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 81,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "重启",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 42,
    "OrderSort": 0,
    "Icon": null,
    "Description": "重启任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleReCoveryJob",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 82,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 43,
    "OrderSort": 0,
    "Icon": null,
    "Description": "删除任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleDel",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 83,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "暂停",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 44,
    "OrderSort": 0,
    "Icon": null,
    "Description": "暂停任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handlePauseJob",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 84,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "恢复",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 45,
    "OrderSort": 0,
    "Icon": null,
    "Description": "恢复任务按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "handleResumeJob",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 85,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "获取任务名称",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 46,
    "OrderSort": 0,
    "Icon": null,
    "Description": "获取任务名称按钮",
    "Enabled": 1,
    "CreateId": 12,
    "CreateBy": "后台总管理员",
    "Func": "",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 86,
    "IsHide": 1
  },
  {
    "Id": 87,
    "Code": "-",
    "Name": "微信公众号管理",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 3,
    "Icon": "fa-weixin",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-21 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-13 00:00:00",
    "IsDeleted": 0,
    "Pid": 0,
    "Mid": 0
  },
  {
    "Id": 88,
    "Code": "\/WeChat\/Manager",
    "Name": "微信列表",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 1,
    "Icon": "fa-list",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-21 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 89,
    "Code": "\/WeChat\/Company",
    "Name": "微信客户",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": "2",
    "Icon": "fa-address-book",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-26 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 90,
    "Code": "\/WeChat\/Menu",
    "Name": "微信菜单",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 3,
    "Icon": "fa-sliders",
    "Description": "微信菜单设置",
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 91,
    "Code": "\/WeChat\/Template",
    "Name": "模板消息",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 4,
    "Icon": "fa-comments-o",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-08 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 92,
    "Code": "\/WeChat\/PushLog",
    "Name": "推送记录",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 8,
    "Icon": "fa-history",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-08 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 93,
    "Code": "\/WeChat\/SubUser",
    "Name": "订阅用户",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 6,
    "Icon": "fa fa-user",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-23 14:09:08",
    "ModifyId": 8,
    "ModifyBy": "test",
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 94,
    "Code": "\/WeChat\/BindUser",
    "Name": "绑定用户",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 7,
    "Icon": "fa fa-user-circle-o",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-23 16:12:52",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 95,
    "Code": "\/WeChat\/SendMessage",
    "Name": "文本消息",
    "IsButton": 0,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 5,
    "Icon": "fa fa-paper-plane",
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-24 09:05:50",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-30 00:00:00",
    "IsDeleted": 0,
    "Pid": 87,
    "Mid": 0
  },
  {
    "Id": 96,
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": "getWeChatAccount",
    "OrderSort": "0",
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-22 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-17 00:00:00",
    "IsDeleted": 0,
    "Pid": 88,
    "Mid": 54
  },
  {
    "Id": 98,
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": "handleDel",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-22 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-17 00:00:00",
    "IsDeleted": 0,
    "Pid": 88,
    "Mid": 64
  },
  {
    "Id": 99,
    "Code": " ",
    "Name": "新增",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": "handleAdd",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-24 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-17 00:00:00",
    "IsDeleted": 0,
    "Pid": 88,
    "Mid": 59
  },
  {
    "Id": 100,
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": "handleEdit",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-24 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-17 00:00:00",
    "IsDeleted": 0,
    "Pid": 88,
    "Mid": 58
  },
  {
    "Id": 101,
    "Code": " ",
    "Name": "批量删除",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": "batchRemove",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-25 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-17 00:00:00",
    "IsDeleted": 0,
    "Pid": 88,
    "Mid": 53
  },
  {
    "Id": 102,
    "Code": " ",
    "Name": "刷新Token",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": "handleRefreshWeChatToken",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-03-30 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-17 00:00:00",
    "IsDeleted": 0,
    "Pid": 88,
    "Mid": 60
  },
  {
    "Id": 103,
    "Code": " ",
    "Name": "查询",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": null,
    "Func": "getWeChatCompany",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2020-04-06 00:00:00",
    "IsDeleted": 0,
    "Pid": 89,
    "Mid": 50
  },
  {
    "Id": 104,
    "Code": " ",
    "Name": "删除",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": null,
    "Func": "handleDel",
    "OrderSort": "0",
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2020-04-06 00:00:00",
    "IsDeleted": 0,
    "Pid": 89,
    "Mid": 49
  },
  {
    "Id": 105,
    "Code": " ",
    "Name": "批量删除",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": null,
    "Func": "batchRemove",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2020-04-06 00:00:00",
    "IsDeleted": 0,
    "Pid": 89,
    "Mid": 48
  },
  {
    "Id": 106,
    "Code": " ",
    "Name": "添加",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": null,
    "Func": "handleAdd",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2020-04-06 00:00:00",
    "IsDeleted": 0,
    "Pid": 89,
    "Mid": 51
  },
  {
    "Id": 107,
    "Code": " ",
    "Name": "编辑",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": null,
    "Func": "handleEdit",
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2020-04-06 00:00:00",
    "IsDeleted": 0,
    "Pid": 89,
    "Mid": 52
  },
  {
    "Id": 108,
    "Code": " ",
    "Name": "获取菜单",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-29 00:00:00",
    "IsDeleted": 0,
    "Pid": 90,
    "Mid": 55
  },
  {
    "Id": 109,
    "Code": " ",
    "Name": "更新菜单",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-06 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-29 00:00:00",
    "IsDeleted": 0,
    "Pid": 90,
    "Mid": 61
  },
  {
    "Id": 110,
    "Code": " ",
    "Name": "获取消息模板",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-08 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-29 00:00:00",
    "IsDeleted": 0,
    "Pid": 91,
    "Mid": 57
  },
  {
    "Id": 111,
    "Code": " ",
    "Name": "获取推送记录",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-08 00:00:00",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-29 00:00:00",
    "IsDeleted": 0,
    "Pid": 92,
    "Mid": 62
  },
  {
    "Id": 112,
    "Code": " ",
    "Name": "获取订阅用户",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": null,
    "Func": null,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-23 16:21:53",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2020-04-23 00:00:00",
    "IsDeleted": 0,
    "Pid": 93,
    "Mid": 56
  },
  {
    "Id": 113,
    "Code": " ",
    "Name": "获取绑定用户",
    "IsButton": 1,
    "IsHide": 0,
    "IskeepAlive": 0,
    "Func": null,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 8,
    "CreateBy": "test",
    "CreateTime": "2020-04-23 16:22:11",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "2021-09-29 00:00:00",
    "IsDeleted": 0,
    "Pid": 94,
    "Mid": 63
  },
  //{
  //  "Id": 114,
  //  "Code": " ",
  //  "Name": "推送文字消息",
  //  "IsButton": 1,
  //  "IsHide": 0,
  //  "IskeepAlive": 0,
  //  "Func": null,
  //  "OrderSort": 0,
  //  "Icon": null,
  //  "Description": null,
  //  "Enabled": 1,
  //  "CreateId": 8,
  //  "CreateBy": "test",
  //  "CreateTime": "2020-04-23 16:22:11",
  //  "ModifyId": null,
  //  "ModifyBy": null,
  //  "ModifyTime": "2021-09-29 00:00:00",
  //  "IsDeleted": 0,
  //  "Pid": 95,
  //  "Mid": 0
  //},
  {
    "Code": "-",
    "Name": "部门权限管理",
    "IsButton": false,
    "IsHide": false,
    "IskeepAlive": false,
    "OrderSort": -10,
    "Icon": "fa-address-book",
    "Description": "",
    "Enabled": true,
    "CreateId": 12,
    "CreateBy": "blogadmin",
    "CreateTime": "2022-03-23 00:00:00",
    "ModifyTime": "2022-03-23 00:00:00",
    "IsDeleted": false,
    "PnameArr": [

    ],
    "PCodeArr": [

    ],
    "hasChildren": true,
    "Pid": 0,
    "Mid": 0,
    "Id": 114
  },
  {
    "Code": "/Department/Department",
    "Name": "部门管理",
    "IsButton": false,
    "IsHide": false,
    "IskeepAlive": false,
    "OrderSort": 0,
    "Icon": "",
    "Description": "",
    "Enabled": true,
    "CreateId": 12,
    "CreateBy": "blogadmin",
    "CreateTime": "2022-03-23 00:00:00",
    "ModifyTime": "2022-03-23 00:00:00",
    "IsDeleted": false,
    "PnameArr": [

    ],
    "PCodeArr": [

    ],
    "hasChildren": true,
    "Pid": 114,
    "Mid": 66,
    "Id": 115
  },
  {
    "Code": " ",
    "Name": "查询",
    "IsButton": true,
    "IsHide": false,
    "IskeepAlive": false,
    "Func": "handleQuery",
    "OrderSort": 0,
    "Icon": "",
    "Description": "",
    "Enabled": true,
    "CreateId": 12,
    "CreateBy": "blogadmin",
    "CreateTime": "2022-03-23 00:00:00",
    "ModifyTime": "2022-03-23 00:00:00",
    "IsDeleted": false,
    "PnameArr": [

    ],
    "PCodeArr": [

    ],
    "hasChildren": true,
    "Pid": 115,
    "Mid": 66,
    "Id": 116
  },
  {
    "Code": " ",
    "Name": "新增",
    "IsButton": true,
    "IsHide": false,
    "IskeepAlive": false,
    "Func": "handleAdd",
    "OrderSort": 0,
    "Icon": "",
    "Description": "",
    "Enabled": true,
    "CreateId": 12,
    "CreateBy": "blogadmin",
    "CreateTime": "2022-03-23 00:00:00",
    "ModifyTime": "2022-03-23 00:00:00",
    "IsDeleted": false,
    "PnameArr": [

    ],
    "PCodeArr": [

    ],
    "hasChildren": true,
    "Pid": 115,
    "Mid": 69,
    "Id": 117
  },
  {
    "Code": " ",
    "Name": "编辑",
    "IsButton": true,
    "IsHide": false,
    "IskeepAlive": false,
    "Func": "handleEdit",
    "OrderSort": 0,
    "Icon": "",
    "Description": "",
    "Enabled": true,
    "CreateId": 12,
    "CreateBy": "blogadmin",
    "CreateTime": "2022-03-23 00:00:00",
    "ModifyTime": "2022-03-23 00:00:00",
    "IsDeleted": false,
    "PnameArr": [

    ],
    "PCodeArr": [

    ],
    "hasChildren": true,
    "Pid": 115,
    "Mid": 68,
    "Id": 118
  },
  {
    "Code": " ",
    "Name": "删除",
    "IsButton": true,
    "IsHide": false,
    "IskeepAlive": false,
    "Func": "handleDel",
    "OrderSort": 0,
    "Icon": "",
    "Description": "",
    "Enabled": true,
    "CreateId": 12,
    "CreateBy": "blogadmin",
    "CreateTime": "2022-03-23 00:00:00",
    "ModifyTime": "2022-03-23 00:00:00",
    "IsDeleted": false,
    "PnameArr": [

    ],
    "PCodeArr": [

    ],
    "hasChildren": true,
    "Pid": 115,
    "Mid": 67,
    "Id": 119
  },
  {
    "Code": " ",
    "Name": "部门树",
    "IsButton": true,
    "IsHide": true,
    "IskeepAlive": false,
    "OrderSort": 0,
    "Icon": "",
    "Description": "",
    "Enabled": true,
    "CreateId": 12,
    "CreateBy": "blogadmin",
    "CreateTime": "2022-03-23 00:00:00",
    "ModifyTime": "2022-03-23 00:00:00",
    "IsDeleted": false,
    "PnameArr": [

    ],
    "PCodeArr": [

    ],
    "hasChildren": true,
    "Pid": 115,
    "Mid": 70,
    "Id": 120
  },
  {
    "Code": " ",
    "Name": "左侧导航Pro",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 71,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 121,
    "IsHide": 1
  },
  {
    "Code": " ",
    "Name": "菜单同步",
    "IsButton": 1,
    "Pid": 7,
    "Mid": 72,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "Func": "handleSync",
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 122,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "日志查询",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 73,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "Func": "handleLog",
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 123,
    "IsHide": 0
  },
  {
    "Code": " ",
    "Name": "任务概况",
    "IsButton": 1,
    "Pid": 76,
    "Mid": 74,
    "OrderSort": 0,
    "Icon": null,
    "Description": null,
    "Enabled": 1,
    "CreateId": 23,
    "Func": "handleOverview",
    "CreateBy": "后台总管理员",
    "CreateTime": "\/Date(1546272000000+0800)\/",
    "ModifyId": null,
    "ModifyBy": null,
    "ModifyTime": "\/Date(1546272000000+0800)\/",
    "IsDeleted": 0,
    "Id": 124,
    "IsHide": 0
  }
]
