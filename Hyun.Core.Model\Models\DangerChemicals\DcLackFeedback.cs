namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///缺少物品反馈表
    ///</summary>
    [SugarTable("dc_LackFeedback","缺少物品反馈表")]
    public class DcLackFeedback : BaseEntity
    {

          public DcLackFeedback()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///反馈内容
          /// </summary>
          [SugarColumn(Length = 511)]
          public string FeedbackContent { get; set; }

           /// <summary>
           ///反馈人
          /// </summary>
          public long FeedbackUserId { get; set; }

           /// <summary>
           ///反馈时间
          /// </summary>
          public DateTime FeedbackDate { get; set; }

           /// <summary>
           ///处理状态（0 : 待处理  11 : 暂缓 100 : 已处理）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///处理备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string SolveContent { get; set; }

           /// <summary>
           ///处理人
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? SolveUserId { get; set; }

           /// <summary>
           ///处理时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? SolveDate { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

