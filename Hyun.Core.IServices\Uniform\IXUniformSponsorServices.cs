﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///XUniformSponsor接口方法
    ///</summary>
    public interface IXUniformSponsorServices : IBaseServices<XUniformSponsor>
    {

        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="o">XUniformSponsor对象</param>
        /// <returns></returns>
        Task<Result<string>> InsertUpdate(XuniformSponsorModel o);

        /// <summary>
        /// 根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        Task<Result<string>> FakeDeleteById(long id);

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<XUniformSponsor> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<XUniformSponsor>> Find(Expression<Func<XUniformSponsor, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">XUniformSponsorParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<XUniformSponsor>> GetPaged(XUniformSponsorParam param);


        /// <summary>
        /// 单位资助列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XuniformSponsorDto>> GetSchoolSponsorPaged(XUniformSponsorParam param);

        /// <summary>
        /// 根据校服采购表Id获取单位资助信息
        /// </summary>
        /// <param name="uniformPurchaseId"></param>
        /// <returns></returns>
        Task<List<XUniformSponsor>> GetPagedById(long uniformPurchaseId);

    }
}

