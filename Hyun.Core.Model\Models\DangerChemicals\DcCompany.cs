namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///供应商列表
    ///</summary>
    [SugarTable("dc_Company","供应商列表")]
    public class DcCompany : BaseEntity
    {

          public DcCompany()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///供应商Id
          /// </summary>
          public long CompanyId { get; set; }

           /// <summary>
           ///供应商名称
          /// </summary>
          [SugarColumn(Length = 255)]
          public string Name { get; set; }

           /// <summary>
           ///联系人
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string LinkUser { get; set; }

           /// <summary>
           ///联系电话
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string Tel { get; set; }

           /// <summary>
           ///QQ
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string QQ { get; set; }

           /// <summary>
           ///主要经营内容
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string BusinessContent { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///状态（ 0：禁用   1：正常）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

