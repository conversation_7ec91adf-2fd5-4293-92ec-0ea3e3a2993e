namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///基础库-废弃物分类
    ///</summary>
    [SugarTable("dc_BaseWaste", "基础库-废弃物分类")]
    public class DcBaseWaste : BaseEntity
    {

        public DcBaseWaste()
        {

        }

        /// <summary>
        ///父Id
        /// </summary>
        public long Pid { get; set; }

        /// <summary>
        ///名称
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        ///编码
        /// </summary>
        [SugarColumn(Length = 15, IsNullable = true)]
        public string Code { get; set; }

        /// <summary>
        ///简称
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string Brief { get; set; }

        /// <summary>
        ///深度
        /// </summary>
        public int Depth { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        ///路径
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Path { get; set; }

        /// <summary>
        ///计量单位
        /// </summary>
        [SugarColumn(Length = 15)]
        public string UnitsMeasurement { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        ///单位名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SchoolName { get; set; }
        /// <summary>
        ///区域名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string AreaName { get; set; }
        
        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal Num { get; set; }
    }


}

