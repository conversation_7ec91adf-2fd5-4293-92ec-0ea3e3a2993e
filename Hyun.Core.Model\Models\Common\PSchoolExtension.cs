namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位信息扩展
    ///</summary>
    [SugarTable("p_SchoolExtension", "单位信息扩展")]
    public class PSchoolExtension : BaseEntity
    {

        public PSchoolExtension()
        {

        }

        /// <summary>
        ///单位id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        ///班级数
        /// </summary>
        public int ClassNum { get; set; }

        /// <summary>
        ///在校学生数
        /// </summary>
        public int StudentNum { get; set; }

        /// <summary>
        ///教职工人数
        /// </summary>
        public int TeacherNum { get; set; }

        /// <summary>
        ///占地面积
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? FloorArea { get; set; }

        /// <summary>
        ///建筑面积
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? BuildArea { get; set; }

        /// <summary>
        ///学段
        /// </summary>
        public int SchoolStage { get; set; }

        /// <summary>
        ///说明
        /// </summary>
        [SugarColumn(Length = 512, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        ///单位管理员
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string SchoolAdmin { get; set; }

        /// <summary>
        ///管理员联系号码
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string AdminMobile { get; set; }

        /// <summary>
        ///校长
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string HeadMaster { get; set; }

        /// <summary>
        ///校长联系号码
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string MsaterMobile { get; set; }

        /// <summary>
        ///是否锁定(0：否  1：是) 默认为0
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? IsLock { get; set; }

        /// <summary>
        ///单位Guid
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string SchoolGuid { get; set; }

        /// <summary>
        ///单位性质（1：公办  2：民办）
        /// </summary>
        public int SchoolNature { get; set; }

        [SugarColumn(Length = 255, IsNullable = true, ColumnDescription = "学段")]
        public string Period { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 是否为市直属
        /// </summary>
        [SugarColumn(DefaultValue = "false", IsOnlyIgnoreInsert = true, ColumnDescription = "是否为市直属")]
        public bool BeLongUnit { get; set; }

        /// <summary>
        ///其中在编教师
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int UnderTeacherNum { get; set; }
    }


}

