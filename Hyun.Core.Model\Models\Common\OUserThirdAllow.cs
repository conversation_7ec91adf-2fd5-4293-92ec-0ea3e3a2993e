namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///根据单点登录白名单，可自动启用账号赋权
    ///</summary>
    [SugarTable("o_UserThirdAllow", "根据单点登录白名单，可自动启用账号赋权")]
    public class OUserThirdAllow : BaseEntity
    {

        public OUserThirdAllow()
        {

        }

        /// <summary>
        ///单位名称
        /// </summary>
        [SugarColumn(Length = 255)]
        public string UnitName { get; set; }

        /// <summary>
        ///人员名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string UserName { get; set; }

        /// <summary>
        ///手机号码
        /// </summary>
        [SugarColumn(Length = 31)]
        public string Mobile { get; set; }

        /// <summary>
        ///角色（角色汉字支持中英文逗号分隔）
        /// </summary>
        [SugarColumn(Length = 511)]
        public string RoleNames { get; set; }

        /// <summary>
        ///类型（单位；教育局）
        /// </summary>
        [SugarColumn(Length = 63)]
        public string UnitTypeName { get; set; }

        /// <summary>
        ///角色Id集合（逗号分隔）
        /// </summary>
        [SugarColumn(Length = 511)]
        public string RoleIds { get; set; }

        /// <summary>
        ///类型Id（单位3，教育局2）
        /// </summary>
        public int UnitTypeId { get; set; }

        /// <summary>
        ///创建单位id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        ///创建人id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///创建时间（查询时候可以指定到日）
        /// </summary>
        public DateTime RegTime { get; set; }

        /// <summary>
        ///关联的UserId
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? RelationUserId { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ErrorMsg { get; set; }

    }


}

