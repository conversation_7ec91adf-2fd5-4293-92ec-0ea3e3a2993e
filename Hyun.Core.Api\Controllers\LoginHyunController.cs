using Hyun.Core.Common.DB;
using Hyun.Core.Common.ImageSharp;
using Hyun.Core.Model.ViewModels.AuthCenter;
using Hyun.Old.Util;
using Microsoft.Extensions.Caching.Memory;
using System.Data;

namespace Hyun.Core.Controllers
{
    /// <summary>
    /// Hyun登录管理【无权限】
    /// </summary>
    [Produces("application/json")]
    [Route("api/hyun/login")]
    [AllowAnonymous]
    public class LoginHyunController : BaseApiController
    {
        private readonly ISysUserInfoServices _sysUserInfoServices;
        private readonly ISysUserRoleServices _userRoleServices;
        private readonly ISysRoleServices _roleServices;
        private readonly PermissionRequirement _requirement;
        private readonly IRoleModulePermissionServices _roleModulePermissionServices;
        private readonly ILogger<LoginController> _logger;
        private readonly IVUserDetailServices _vUserManager;
        private readonly IBWebSiteConfigServices _webSiteConfigManager;
        private readonly ISysUserInfoServices _acountServices;
        private readonly IBUserActionLogServices _userActionLogManager;
        private readonly IBSmsHistoryValidateServices smsHistoryValidateManager;
        private readonly IBConfigSetServices _bconfigManager;
        protected SecurityCodeHelper _securityCode { get; set; }

        protected IMemoryCache _memoryCache { get; set; }
        private readonly IPUnitServices unitManager;
        private readonly IUser user;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="sysUserInfoServices"></param>
        /// <param name="userRoleServices"></param>
        /// <param name="roleServices"></param>
        /// <param name="requirement"></param>
        /// <param name="roleModulePermissionServices"></param>
        /// <param name="logger"></param>
        /// <param name="vUserManager"></param>
        /// <param name="webSiteConfigManager"></param>
        /// <param name="acountServices"></param>
        /// <param name="userActionLogManager"></param>
        /// <param name="memoryCache"></param>
        /// <param name="_smsHistoryValidateManager"></param>
        /// <param name="_unitManager"></param>
        /// <param name="_user"></param>
        /// <param name="bconfigManager"></param>
        public LoginHyunController(ISysUserInfoServices sysUserInfoServices, ISysUserRoleServices userRoleServices,
            ISysRoleServices roleServices, PermissionRequirement requirement,
            IRoleModulePermissionServices roleModulePermissionServices, ILogger<LoginController> logger,
            IVUserDetailServices vUserManager, IBWebSiteConfigServices webSiteConfigManager, ISysUserInfoServices acountServices,
            IBUserActionLogServices userActionLogManager, IMemoryCache memoryCache, IBSmsHistoryValidateServices _smsHistoryValidateManager,
            IPUnitServices _unitManager, IUser _user, IBConfigSetServices bconfigManager)//, SecurityCodeHelper securityCode)
        {
            _sysUserInfoServices = sysUserInfoServices;
            _userRoleServices = userRoleServices;
            _roleServices = roleServices;
            _requirement = requirement;
            _roleModulePermissionServices = roleModulePermissionServices;
            _logger = logger;
            _vUserManager = vUserManager;
            _webSiteConfigManager = webSiteConfigManager;
            _acountServices = acountServices;
            _userActionLogManager = userActionLogManager;
            _memoryCache = memoryCache;
            //_securityCode = securityCode;
            smsHistoryValidateManager = _smsHistoryValidateManager;
            unitManager = _unitManager;
            user = _user;
            _bconfigManager = bconfigManager;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="Name"></param>
        /// <param name="Pass"></param>
        /// <param name="Code"></param>
        /// <param name="Uuid"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("accountlogin")]
        public async Task<Result> GetJwtToken3(string Name = "", string Pass = "", string Code = "", string Uuid = "")

        {
            string jwtStr = string.Empty;

            Result r = new Result();

            if (string.IsNullOrEmpty(Name) || string.IsNullOrEmpty(Pass))
            {
                r.flag = 0;
                r.msg = "用户名或密码不能为空";
                return r;
            }

            var clientIp = IpHelper.GetClientIp(HttpContext);

            int errNum = 0;

            if (!string.IsNullOrEmpty(Code))
            {
                var validateCodeResult = ValidationCode(Uuid, Code);
                if (validateCodeResult == 3) //验证码错误，不计入，此处可能造成暴力猜测验证码的问题
                {
                    r.flag = 0;
                    r.msg = "验证密码错误";
                    return r;
                }
                else if (validateCodeResult == 2) //验证码过期，重新加载
                {
                    r.flag = 5;
                    var codeInfo = GetCaptchaImage();
                    r.data.footer = new { codeInfo.Img, codeInfo.Uuid };
                    r.msg = "验证密码错误";
                    return r;
                }
                else    //已经错3次，表示要一直显示验证码
                {
                    errNum = 3;
                }
            }
            else //未输入验证码，判断是否要输入验证码
            {
                var listLog = await _userActionLogManager.SearchLoginList(clientIp);
                if (listLog != null && listLog.Count >= 3)
                {
                    var list = listLog.Take(3).Where(a => a.Statuz == 0).ToList(); //前三条都没登录成功，表示连续3次失败
                    if (list.Count == 3)
                    {
                        errNum = 3;
                        r.flag = 5;
                        var codeInfo = GetCaptchaImage();
                        r.data.footer = new { codeInfo.Img, codeInfo.Uuid };
                        r.msg = "请输入验证码";
                        return r;
                    }
                }
            }

            r = await _acountServices.Login(Name, Pass, clientIp);

            if (r.flag == 1)
            {
                //var userRoles = await _sysUserInfoServices.GetUserRoleNameStr(Name, Pass, 1);
                VUserDetail user = (VUserDetail)r.data.rows;

                //获取企业是否提交审核
                if (user.AcctStatuz == 3)
                {
                    r.flag = 3;
                    r.data.other = user.UnitName;
                }

                TokenInfoViewModel token = await CatcheAndGetJwtToken(clientIp, user);

                if (token != null && !token.success)
                {
                    r.flag = 0;
                    r.msg = $"登录失败，未取得授权，请重新登录。";
                }
                else
                {
                    r.data.rows = token;
                    r.data.footer = new
                    {
                        user.Id,
                        user.AcctId,
                        user.Name,
                        user.Mobile,
                        user.IsThirdClient,
                        user.NickName,
                        user.AcctName,
                        user.RoleIds,
                        user.RoleNames,
                        user.UnitId,
                        user.UnitName,
                        user.Tel,
                        user.UnitPId,
                        user.UnitPName,
                        user.UnitType,
                        user.UserType,
                        user.AdministratorType,
                        user.AreaId,
                        user.DepartmentIds,
                        user.ZipCode,
                        user.HeadPortrait,
                        user.UnitStatus
                    };
                }
            }

            if (r.flag == 0)
            {
                errNum++;

                if (errNum >= 3)
                {
                    //生成验证码
                    r.flag = 5;
                    var codeInfo = GetCaptchaImage();
                    r.data.footer = new { codeInfo.Img, codeInfo.Uuid };
                }
            }

            return r;
        }

        [Authorize(AuthenticationSchemes = "AuthCenter")]
        [HttpGet("tokenexchange")]
        public async Task<Result> TokenExchange()
        {
            Result r = new Result();

            var result = new TData<UserInfoDto>();
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                result.Tag = 4000;
                result.Message = "无法获取用户ID";
            }

            var user = await _acountServices.GetUserDetail(long.Parse(userId));

            var tokenInfo = await CatcheAndGetJwtToken(IpHelper.GetClientIp(HttpContext), user);

            if (tokenInfo != null && !tokenInfo.success)
            {
                r.flag = 0;
                r.msg = $"登录失败，未取得授权，请重新登录。";
            }
            else
            {
                r.flag = 1;
                r.data.rows = tokenInfo;
                r.data.footer = new
                {
                    user.Id,
                    user.AcctId,
                    user.Name,
                    user.Mobile,
                    user.IsThirdClient,
                    user.NickName,
                    user.AcctName,
                    user.RoleIds,
                    user.RoleNames,
                    user.UnitId,
                    user.UnitName,
                    user.Tel,
                    user.UnitPId,
                    user.UnitPName,
                    user.UnitType,
                    user.UserType,
                    user.AdministratorType,
                    user.AreaId,
                    user.DepartmentIds,
                    user.ZipCode,
                    user.HeadPortrait,
                    user.UnitStatus
                };
            }

            return r;
        }

        private async Task<TokenInfoViewModel> CatcheAndGetJwtToken(string clientIp, VUserDetail user)
        {
            //如果是基于用户的授权策略，这里要添加用户;如果是基于角色的授权策略，这里要添加角色
            var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, user.AcctName),
                    new Claim(JwtRegisteredClaimNames.Jti, user.Id.ToString()),
                    new Claim("IsThirdClient", "false"),
                    new Claim("AcctId", user.AcctId.ToString()),
                    new Claim("AcctName", user.AcctName),
                    new Claim("UnitId", user.UnitId.ToString()),
                    new Claim("AreaId", user.AreaId.ToString()),
                    new Claim("Mobile", user.Mobile?? ""),
                    new Claim("UnitTypeId", user.UnitType.ToString()),
                    new Claim("UnitName", user.UnitName ?? ""),
                    new Claim("UnitPId", user.UnitPId.ToString()),
                    new Claim("UserRoles", string.Join(',',user.RoleNames)),
                    new Claim("ClientIp", clientIp),
                    new Claim("UserRoleIds", string.Join(',',user.RoleIds)),
                    new Claim("AdministratorType", user.AdministratorType.ToString()),
                    new Claim(JwtRegisteredClaimNames.Iat, DateTime.Now.DateToTimeStamp()),
                    new Claim(ClaimTypes.Expiration,DateTime.Now.AddSeconds(_requirement.Expiration.TotalSeconds).ToString())
                };
            claims.AddRange(user.RoleNames.Where(s => s != null).Select(s => new Claim(ClaimTypes.Role, s)));

            //需增加权限处理，将VUserDetail存储

            // ids4和jwt切换
            // jwt
            if (!Permissions.IsUseIds4)
            {
                var data = await _roleModulePermissionServices.RoleModuleMaps(user.RoleIds);
                //var permissionlist = (from item in data
                //                      where item.IsDeleted == false
                //                      orderby item.Id
                //                      select new PermissionItem
                //                      {
                //                          Url = item.Module?.LinkUrl,
                //                          Role = item.Role?.Name.ObjToString(),
                //                          IsCommon = item.Module.IsCommon
                //                      }).ToList();

                _requirement.Permissions = data;
            }

            var token = JwtToken.BuildJwtToken(claims.ToArray(), _requirement);
            return token;
        }

        [HttpPost]
        [Route("refreshcaptchaimage")]
        public async Task<Result> RefreshCaptchaImage()
        {
            var result = await Task.Run(() =>
            {
                Result r = new Result();
                r.flag = 1;
                var codeInfo = GetCaptchaImage();
                r.data.footer = new { codeInfo.Img, codeInfo.Uuid };
                return r;
            });
            return result;
        }

        /// <summary>
        /// 生成验证码
        /// </summary>
        /// <returns></returns>
        private CaptchaImageDto GetCaptchaImage()
        {
            SecurityCodeHelper _securityCode = new SecurityCodeHelper();
            var uuid = Guid.NewGuid().ToString();
            var code = _securityCode.GetRandomEnDigitalText(4);
            //将uuid与code，Redis缓存中心化保存起来，登录根据uuid比对即可
            //5分钟过期
            _memoryCache.Set($"Hyun:Captcha:{uuid}", code.ToLower(), new TimeSpan(0, 5, 0));
            var imgbyte = _securityCode.GetEnDigitalCodeByte(code);
            return new CaptchaImageDto { Img = imgbyte, Code = code, Uuid = uuid };
        }

        /// <summary>
        /// 效验登录验证码,无需和账号绑定(1:正确；2：过期；3：错误)
        /// </summary>
        private int ValidationCode(string Uuid, string Code, string Tag = "Captcha")
        {
            var value = _memoryCache.Get<string>($"Hyun:{Tag}:{Uuid}");
            if (value is not null)
            {
                if (value.Equals(Code.ToLower()))
                    return 1;
                else
                    return 3;
            }
            else
            {
                return 2;
            }
        }

        //[HttpGet]
        //[Route("getclientip1")]
        //public IActionResult GetClientIP1()
        //{
        //    var ip = HttpContext.Connection.RemoteIpAddress.ToString();
        //    return Ok(ip);
        //}

        //[HttpGet]
        //[Route("getclientip2")]
        //public IActionResult GetClientIP2()
        //{
        //    // 从HttpContext.Items中获取用户IP地址（如果中间件已设置）
        //    var clientIpAddress = HttpContext.Items["ClientIpAddress"] as string;

        //    if (string.IsNullOrEmpty(clientIpAddress))
        //    {
        //        // 如果没有设置，则回退到RemoteIpAddress
        //        clientIpAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        //    }

        //    // 使用 clientIpAddress 变量进行后续操作...

        //    return Ok($"Your IP address is: {clientIpAddress}");
        //}

        //[HttpPost]
        //[Route("testUser")]
        //public async Task<string> TestUser()
        //{
        //    return App.User.ToJson();
        //}

        [HttpPost]
        [Route("sendsecuritycode")]
        public async Task<Result> SendSecurityCode([FromBody] RegisterModel param)
        {
            Result r = new Result();
            var validateCodeResult = ValidationCode(param.uid, param.codeNum);
            if (validateCodeResult == 3) //验证码错误，不计入，此处可能造成暴力猜测验证码的问题
            {
                r.flag = 0;
                r.msg = "验证码错误";
                return r;
            }
            else if (validateCodeResult == 2) //验证码过期，重新加载
            {
                r.flag = 2;
                r.msg = "验证码错误";
                return r;
            }

            string ipAddress = IpHelper.GetClientIp(HttpContext);
            int timesPhone = int.Parse(ApplicationConfig.SendMessageTimesPhone);
            int timesIP = int.Parse(ApplicationConfig.SendMessageTimesIP);
            int waitSecond = int.Parse(ApplicationConfig.SendMessageWaitSecond);
            int timesDayFindPwd = int.Parse(ApplicationConfig.SendMessageTimesDayFindPwd);

            r = await smsHistoryValidateManager.SmsValidate(param.phoneNumber, ipAddress, 1, DateTime.Now, timesPhone, timesIP, waitSecond, timesDayFindPwd);
            if (r.flag == 1)
            {
                //产生随便6位数
                Random ran = new Random();
                string RanKey = ran.Next(100000, 999999).ToString();
                //存入Session
                var phoneId = Guid.NewGuid().ToString();
                var RankeyId = Guid.NewGuid().ToString();

                _memoryCache.Set($"Hyun:Captcha:{param.phoneNumber}", phoneId, new TimeSpan(0, 5, 0));
                _memoryCache.Set($"Hyun:Captcha:{RanKey}", RankeyId, new TimeSpan(0, 5, 0));

                string msg = ApplicationConfig.SendMessageEventRegisterSupplierCode;
                string strMessage = string.Format(msg, RanKey);
                r.data.rows = phoneId;
                r.data.footer = RankeyId;
                r.flag = 1;
                //发送短信
                await SendMessage.SendToMobile(param.phoneNumber, strMessage);
            }

            return r;
        }

        /// <summary>
        /// 单位管理：注册企业
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitenterprisesregistered")]
        public async Task<Result> Unit_EnterprisesRegistered([FromBody] CompanyRegisterModel o)
        {
            Result r = new Result();
            string codeNum = ComLib.filterSqlValue(o.codeNum);
            string phoneNumber = ComLib.filterSqlValue(o.phoneNumber);
            string AcctName = ComLib.filterSqlValue(o.AcctName);
            string Pwd = ComLib.filterSqlValue(o.Pwd);
            string CompanyName = ComLib.filterSqlValue(o.CompanyName);
            string PersonPhone = ComLib.filterSqlValue(o.PersonPhone);
            //zyf 20210816 增加服务区域Id
            int ServiceAreaId = int.Parse(ApplicationConfig.defaultSetdefaultCity);

            var valuePhoneNumber = _memoryCache.Get<string>($"Hyun:Captcha:{PersonPhone}");
            var valueRankey = _memoryCache.Get<string>($"Hyun:Captcha:{phoneNumber}");

            if (valuePhoneNumber == null || valueRankey == null)
            {
                r.flag = 0;
                r.msg = "校验码输入有误！";
                return r;
            }

            //验证短信码是否失效
            BSmsHistoryValidate smsValidate = await smsHistoryValidateManager.SearchSmsList(PersonPhone, 1);
            if (smsValidate == null)
            {
                r.flag = 0;
                r.msg = "请先获取校验码！";
                return r;
            }

            //有效期验证
            int SendMessageSmsDuration = int.Parse(ApplicationConfig.SendMessageSmsDuration);
            int waiminute = DateTime.Now.Subtract(smsValidate.RegTime).Minutes;
            if (waiminute > SendMessageSmsDuration)
            {
                r.flag = 0;
                r.msg = "校验码已经超过有效期请重新获取！";
                return r;
            }

            if (smsValidate.ErrorCount >= 5)
            {
                r.flag = 0;
                r.msg = "校验码输错5次，请重新获取校验码！";
                return r;
            }

            //判断手机号码，校验码是否正确
            if (!valuePhoneNumber.Equals(o.phoneId) && !valueRankey.Equals(o.RankeyId))
            {
                r.flag = 0;
                r.msg = "手机号码或校验码输入有误！";
                //增加错误次数
                smsValidate.ErrorCount += 1;
                await smsHistoryValidateManager.Update(smsValidate);
                return r;
            }

            //判断账号如果为纯数字，必须大于等于8位
            if (string.IsNullOrEmpty(AcctName))
            {
                r.flag = 0;
                r.msg = "账户不能为空！";
                return r;
            }

            var listAccount = await _acountServices.Find(f => f.LoginName == AcctName);
            if (listAccount.Count > 0)
            {
                r.flag = 0;
                r.msg = "该账户已存在！请重新输入！";
                return r;
            }

            var listCompany = await unitManager.Find(f => f.Name == CompanyName);
            if (listCompany.Count > 0)
            {
                r.flag = 0;
                r.msg = "该企业已注册！请重新输入！";
                return r;
            }
            int Pid = 0;                      //父节点ID
            int UnitType = 4;              //类型
            int IndustryId = 1;              //行业
            string Codes = $"G{DateTime.Now.ToString("yyyyMMddmmss")}";        //单位编号
            int Statuz = 2;               //状态
            r = await unitManager.InsertCompany(Pid, UnitType, IndustryId, Codes, CompanyName, PersonPhone, Statuz, "", AcctName, Pwd, ServiceAreaId);
            if (r.flag == 1)
            {
                _memoryCache.Remove($"Hyun:Captcha:{PersonPhone}");
                _memoryCache.Remove($"Hyun:Captcha:{phoneNumber}");

                //var clientIp = IpHelper.GetClientIp(HttpContext);
                ////模拟登录
                //r = await _acountServices.Login(AcctName, Pwd, clientIp);

                //if (r.flag == 1)
                //{
                //    //var userRoles = await _sysUserInfoServices.GetUserRoleNameStr(Name, Pass, 1);
                //    VUserDetail user = (VUserDetail)r.data.rows;

                //    //如果是基于用户的授权策略，这里要添加用户;如果是基于角色的授权策略，这里要添加角色
                //    var claims = new List<Claim>
                //    {
                //        new Claim(ClaimTypes.Name, AcctName),
                //        new Claim(JwtRegisteredClaimNames.Jti, user.Id.ToString()),
                //        new Claim("IsThirdClient", "false"),
                //        new Claim("AcctId", user.AcctId.ToString()),
                //        new Claim("AcctName", user.AcctName),
                //        new Claim("UnitId", user.UnitId.ToString()),
                //        new Claim("AreaId", user.AreaId.ToString()),
                //        new Claim("UnitTypeId", user.UnitType.ToString()),
                //        new Claim("UnitName", user.UnitName ?? ""),
                //        new Claim("UnitPId", user.UnitPId.ToString()),
                //        new Claim("UserRoles", string.Join(',',user.RoleNames)),
                //        new Claim("ClientIp", clientIp),
                //        new Claim("UserRoleIds", string.Join(',',user.RoleIds)),
                //        new Claim("AdministratorType", user.AdministratorType.ToString()),
                //        new Claim(JwtRegisteredClaimNames.Iat, DateTime.Now.DateToTimeStamp()),
                //        new Claim(ClaimTypes.Expiration,DateTime.Now.AddSeconds(_requirement.Expiration.TotalSeconds).ToString())
                //    };
                //}
            }
            return r;
        }

        /// <summary>
        /// 单位管理：企业注册保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("unitenterprisescertification")]
        public async Task<Result> Unit_EnterprisesCertification([FromBody] CompanySubmitModel o)
        {
            Result r = new Result();
            string Phone = ComLib.filterSqlValue(o.Phone);
            string LegalPerson = ComLib.filterSqlValue(o.LegalPerson);
            string OrganizationCode = ComLib.filterSqlValue(o.OrganizationCode);
            string BusinessLicense = ComLib.filterSqlValue(o.BusLinUrl);
            string Address = ComLib.filterSqlValue(o.Address);
            string AddressDetail = ComLib.filterSqlValue(o.DetailAddress);
            Address = Address + AddressDetail;
            long AreaId = o.AreaId;
            string Name = ComLib.filterSqlValue(o.Name);

            string BusLinUrl = BusinessLicense;    //营业执照图片
            //long unitId = user.UnitId;
            r = await unitManager.SubmitCertificationCompany(o.CompanyName, AreaId, LegalPerson, Address, OrganizationCode, Name, Phone, BusLinUrl);
            if (r.flag == 1)
            {
                List<BWebSiteConfig> listJs = await _webSiteConfigManager.Find(f => f.ConfigType == 10);
                if (listJs.Count > 0)
                {
                    string platformName = listJs.Find(p => p.ConfigCode == "PlatformName").ConfigValue;
                    var mobile = listJs.Find(p => p.ConfigCode == "OperatorRegMobile").ConfigValue;
                    if (!string.IsNullOrEmpty(mobile) && mobile.Length == 11)
                    {
                        await SendMessage.SendToMobile(mobile, string.Format("{0}，有企业注册，请审核", platformName), false);
                    }
                }
            }
            return r;
        }

        /// <summary>
        /// 用户管理：密码找回（根据手机号码发送短信）
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userexamineaccount")]
        public async Task<Result> User_ExamineAccount([FromBody] FindUserPwdModel o)
        {
            Result r = new Result();
            string AcctName = ComLib.filterSqlValue(o.AcctName);
            List<SysUserInfo> list = await _acountServices.Find(f => f.LoginName == AcctName && f.Mobile == o.Moble);
            if (list.Count > 0)
            {
                string ipAddress = IpHelper.GetClientIp(HttpContext);
                int timesPhone = int.Parse(ApplicationConfig.SendMessageTimesPhone);
                int timesIP = int.Parse(ApplicationConfig.SendMessageTimesIP);
                int waitSecond = int.Parse(ApplicationConfig.SendMessageWaitSecond);
                int timesDayFindPwd = int.Parse(ApplicationConfig.SendMessageTimesDayFindPwd);

                r = await smsHistoryValidateManager.SmsValidate(o.Moble, ipAddress, 2, DateTime.Now, timesPhone, timesIP, waitSecond, timesDayFindPwd);
                if (r.flag == 1)
                {
                    //产生随便6位数
                    System.Random ran = new Random();
                    string RanKey = ran.Next(100000, 999999).ToString();
                    //存入Session
                    var phoneId = Guid.NewGuid().ToString();
                    var RankeyId = Guid.NewGuid().ToString();

                    _memoryCache.Set($"Hyun:Captcha:{o.Moble}", phoneId, new TimeSpan(0, 5, 0));
                    _memoryCache.Set($"Hyun:Captcha:{RanKey}", RankeyId, new TimeSpan(0, 5, 0));

                    string msg = ApplicationConfig.SendMessageEventRegisterSupplierCode;
                    string strMessage = string.Format(msg, RanKey);
                    r.data.rows = phoneId;
                    r.data.footer = RankeyId;
                    r.flag = 1;
                    //发送短信
                    await SendMessage.SendToMobile(o.Moble, strMessage);
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "输入的手机号码和账号与原注册信息不一致，请重新输入";
            }
            return r;
        }

        /// <summary>
        /// 用户管理：密码找回
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("usereditchangepass")]
        public async Task<Result> User_EditChangePass([FromBody] FindUserPwdModel o)
        {
            Result r = new Result();
            string AcctName = ComLib.filterSqlValue(o.AcctName);
            var valuePhoneNumber = _memoryCache.Get<string>($"Hyun:Captcha:{o.Moble}");
            var valueRankey = _memoryCache.Get<string>($"Hyun:Captcha:{o.Code}");

            if (valuePhoneNumber == null || valueRankey == null)
            {
                r.flag = 0;
                r.msg = "校验码输入有误！";
                return r;
            }

            //验证短信码是否失效
            BSmsHistoryValidate smsValidate = await smsHistoryValidateManager.SearchSmsList(o.Moble, 2);
            if (smsValidate == null)
            {
                r.flag = 0;
                r.msg = "请先获取校验码！";
                return r;
            }

            //有效期验证
            int SendMessageSmsDuration = int.Parse(ApplicationConfig.SendMessageSmsDuration);
            int waiminute = DateTime.Now.Subtract(smsValidate.RegTime).Minutes;
            if (waiminute > SendMessageSmsDuration)
            {
                r.flag = 0;
                r.msg = "校验码已经超过有效期请重新获取！";
                return r;
            }

            if (smsValidate.ErrorCount >= 5)
            {
                r.flag = 0;
                r.msg = "校验码输错5次，请重新获取校验码！";
                return r;
            }

            //判断手机号码，校验码是否正确
            if (!valuePhoneNumber.Equals(o.phoneId) && !valueRankey.Equals(o.RankeyId))
            {
                r.flag = 0;
                r.msg = "手机号码或校验码输入有误！";
                //增加错误次数
                smsValidate.ErrorCount += 1;
                await smsHistoryValidateManager.Update(smsValidate);
                return r;
            }

            List<SysUserInfo> list = await _acountServices.Find(f => f.LoginName == AcctName && f.Mobile == o.Moble);
            if (list.Count > 0)
            {
                if (list.Count > 1)
                {
                    r.flag = 0;
                    r.msg = "账户异常，请联系管理员";
                }
                else
                {
                    SysUserInfo account = list[0];
                    account.LoginPWD = o.Pwd;
                    if (await _acountServices.Update(account))
                    {
                        var clientIp = IpHelper.GetClientIp(HttpContext);
                        //找回密码后解冻 （密码输入5次账号冻结）
                        await _userActionLogManager.Add(new BUserActionLog()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            UserId = account.UserExtensionId,
                            AccountName = account.LoginName,
                            UserIp = clientIp,
                            Type = 4,//4:找回密码
                            CreateTime = DateTime.Now,
                            Statuz = 1
                        });
                    }

                    r.flag = 1;
                    r.msg = "密码修改成功!";
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "密码修改失败，请检查手机与账户是否填写错误！";
            }
            return r;
        }

        /// <summary>
        /// 请求刷新Token（以旧换新）
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("refreshtoken")]
        public async Task<Result<TokenInfoViewModel>> RefreshToken(string token = "")
        {
            string jwtStr = string.Empty;

            if (string.IsNullOrEmpty(token))
                return baseFailed<TokenInfoViewModel>("token无效，请重新登录！");
            var tokenModel = JwtHelper.SerializeJwt(token);
            if (tokenModel != null && JwtHelper.customSafeVerify(token) && tokenModel.Uid > 0)
            {
                //var user = await _sysUserInfoServices.QueryById(tokenModel.Uid);
                var result = await _acountServices.GetLoginInfo(tokenModel.Uid);
                //var value = User.Claims.SingleOrDefault(s => s.Type == JwtRegisteredClaimNames.Iat)?.Value;
                //if (value != null && user.CriticalModifyTime > value.ObjToDate())
                //{
                //    return baseFailed<TokenInfoViewModel>("很抱歉,授权已失效,请重新授权！");
                //}

                if (result != null && result.flag == 1)
                {
                    VUserDetail user = (VUserDetail)result.data.rows;

                    TokenInfoViewModel refreshToken = await CatcheAndGetJwtToken(IpHelper.GetClientIp(HttpContext), user);

                    return baseSucc(refreshToken, 1);
                }
            }

            return baseFailed<TokenInfoViewModel>("认证失败！");
        }

        #region 家长、教师 注册与登录

        /// <summary>
        /// 获取手机验证码
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="validateType">传3（家长、教师登录）</param>
        /// <param name="userType">5:家长；6：班主任，1：普通用户</param>
        /// <returns>验证码序列号，total 为验证码长度</returns>
        [HttpPost]
        [Route("getvalidatecode")]
        public async Task<Result<PhoneValidateCodeDto>> GetValidateCode(string phoneNumber, int validateType, int userType = 0)
        {
            if (string.IsNullOrEmpty(phoneNumber))
            {
                return baseFailed<PhoneValidateCodeDto>("请输入手机号码");
            }
            //var IsExistAccount1 = await _acountServices.IsExistAccount(phoneNumber, userType);
            //return baseFailed<PhoneValidateCodeDto>(IsExistAccount1.ToString());
            //从b_ConfigSet中获取配置信息
            //验证码有效期  601001_VCODEPERIOD
            //验证码失效错误次数 (每个验证码出错3次，失效)  601001_VCODEERRORTIMES
            //禁止手机当日登录，每个手机号连续失效次数    601001_MOBILEERRORTIMES
            //每个IP每天允许登录手机号码数量    601001_IPALLOWMOBILENUM
            //等待120秒，才能获取验证码 601001_MOBILEWAITSECOND
            //每个手机每天允许获取验证码数量 601001_MOBILEALLOWCODENUM
            int vcodePeriod = 30;
            int vcodeErrorTimes = 3;
            int mobileErrorTimes = 3;
            int ipAllowMobileNum = 5;
            int mobileWaitSecond = 120;
            int mobileAllowCodeNum = 10;
            var smsConfigs = await _bconfigManager.GetByModule("601001", 0, 0, 0, 0);
            if (smsConfigs != null && smsConfigs.Count > 0)
            {
                BConfigSet matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_VCODEPERIOD");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out vcodePeriod);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_VCODEERRORTIMES");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out vcodeErrorTimes);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_MOBILEERRORTIMES");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out mobileErrorTimes);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_IPALLOWMOBILENUM");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out ipAllowMobileNum);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_MOBILEWAITSECOND");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out mobileWaitSecond);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_MOBILEALLOWCODENUM");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out mobileAllowCodeNum);
                }
            }

            string ipAddress = IpHelper.GetClientIp(HttpContext);

            var checkNeedValidateCode = await smsHistoryValidateManager.CheckAllowValidateCode(phoneNumber, ipAddress, validateType,
                mobileErrorTimes, ipAllowMobileNum, mobileWaitSecond, mobileAllowCodeNum);
            if (checkNeedValidateCode.flag == 1)
            {
                Random ran = new Random();
                string code = ran.Next(1000, 999999).ToString();
                var uuid = $"{checkNeedValidateCode.data.rows}_{validateType}";
                _memoryCache.Set($"Hyun:validatecode:{uuid}", code, new TimeSpan(0, vcodePeriod, 0));

                string sendMsg = string.Format(ApplicationConfig.SendMessageEventSendBaseCode, code, vcodePeriod);

                await SendMessage.SendToMobile(phoneNumber, sendMsg);
                var IsExistAccount = await _acountServices.IsExistAccount(phoneNumber, userType);
                return baseSucc<PhoneValidateCodeDto>(new PhoneValidateCodeDto()
                {
                    Uuid = uuid,
                    CodeLength = code.Length,
                    Period = vcodePeriod,
                    WaitSecond = mobileWaitSecond,
                    IsExistAccount = IsExistAccount.data.rows
                }, 1);
            }
            else
            {
                return baseFailed<PhoneValidateCodeDto>(checkNeedValidateCode.msg);
            }
        }

        /// <summary>
        /// 通过手机注册登录，仅适用班主任和家长
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="validateCode">验证码</param>
        /// <param name="uuid">验证码Id</param>
        /// <param name="userType">5:家长；6：班主任</param>
        /// <returns></returns>
        [HttpPost]
        [Route("userlogin")]
        public async Task<Result> GetUserLoginJwt(string phoneNumber, string validateCode = "", string uuid = "", int userType = 0)
        {
            string jwtStr = string.Empty;

            if (string.IsNullOrEmpty(phoneNumber) || string.IsNullOrEmpty(validateCode) || string.IsNullOrEmpty(uuid))
            {
                return baseFailed("请输入手机号码和验证码");
            }
            if (uuid.IndexOf('_') == -1)
            {
                return baseFailed("无效的登录请求，uuid不正确");
            }
            long smsId;
            long.TryParse(uuid.Split('_')[0], out smsId);
            if (smsId <= 0)
            {
                return baseFailed("无效的登录请求，uuid不正确");
            }
            var clientIp = IpHelper.GetClientIp(HttpContext);

            var validateCodeResult = ValidationCode(uuid, validateCode, "validatecode");
            if (validateCodeResult == 3) //验证码错误
            {
                if (smsId > 0)
                    await smsHistoryValidateManager.UpdateErrorCount(smsId);

                return baseFailed("验证密码错误");
            }
            else if (validateCodeResult == 2) //验证码过期，重新加载
            {
                return baseFailed("验证已经过期，请重新获取");
            }

            var r = await _acountServices.LoginByPhoneNumber(phoneNumber, smsId, clientIp, userType);

            if (r.flag == 1)
            {
                //var userRoles = await _sysUserInfoServices.GetUserRoleNameStr(Name, Pass, 1);
                VUserDetail user = (VUserDetail)r.data.rows;

                TokenInfoViewModel token = await CatcheAndGetJwtToken(clientIp, user);

                if (token != null && !token.success)
                {
                    r.flag = 0;
                    r.msg = $"登录失败，未取得授权，请重新登录。";
                }
                else
                {
                    r.data.rows = token;
                    r.data.footer = new
                    {
                        user.Id,
                        user.AcctId,
                        user.Name,
                        user.Mobile,
                        user.IsThirdClient,
                        user.NickName,
                        user.AcctName,
                        user.RoleIds,
                        user.RoleNames,
                        user.UnitId,
                        user.UnitName,
                        user.Tel,
                        user.UnitPId,
                        user.UnitPName,
                        user.UnitType,
                        user.UserType,
                        user.AdministratorType,
                        user.AreaId,
                        user.DepartmentIds,
                        user.ZipCode,
                        user.HeadPortrait,
                        user.UnitStatus
                    };
                }
            }

            return r;
        }

        #endregion 家长、教师 注册与登录

        #region 校服管理平台单位企业注册

        [HttpPost]
        [Route("xfsendcode")]
        public async Task<Result> XfSendSecurityCode([FromBody] RegisterModel param)
        {
            Result r = new Result();
            var validateCodeResult = ValidationCode(param.uid, param.codeNum);
            if (validateCodeResult == 3)
            {
                r.flag = 0;
                r.msg = "验证码错误";
                return r;
            }
            else if (validateCodeResult == 2)
            {
                r.flag = 2;
                r.msg = "验证码错误";
                return r;
            }

            string ipAddress = IpHelper.GetClientIp(HttpContext);

            //从b_ConfigSet中获取配置信息
            //验证码有效期  601001_VCODEPERIOD
            //验证码失效错误次数 (每个验证码出错3次，失效)  601001_VCODEERRORTIMES
            //禁止手机当日登录，每个手机号连续失效次数    601001_MOBILEERRORTIMES
            //每个IP每天允许登录手机号码数量    601001_IPALLOWMOBILENUM
            //等待120秒，才能获取验证码 601001_MOBILEWAITSECOND
            //每个手机每天允许获取验证码数量 601001_MOBILEALLOWCODENUM
            int vcodePeriod = 30;
            int vcodeErrorTimes = 3;
            int mobileErrorTimes = 3;
            int ipAllowMobileNum = 50;
            int mobileWaitSecond = 120;
            int mobileAllowCodeNum = 10;
            var smsConfigs = await _bconfigManager.GetByModule("601001", 0, 0, 0, 0);

            if (smsConfigs != null && smsConfigs.Count > 0)
            {
                BConfigSet matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_VCODEPERIOD");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out vcodePeriod);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_VCODEERRORTIMES");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out vcodeErrorTimes);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_MOBILEERRORTIMES");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out mobileErrorTimes);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_IPALLOWMOBILENUM");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out ipAllowMobileNum);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_MOBILEWAITSECOND");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out mobileWaitSecond);
                }
                matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_MOBILEALLOWCODENUM");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out mobileAllowCodeNum);
                }
            }

            int timesPhone = mobileAllowCodeNum;
            int timesIP = ipAllowMobileNum;
            int waitSecond = mobileWaitSecond;
            int timesDayFindPwd = mobileAllowCodeNum;

            //timesDayFindPwd  当天短信发送超出平台限制量

            r = await smsHistoryValidateManager.XfSmsValidate(param.phoneNumber, ipAddress, 1, DateTime.Now, timesPhone, timesIP, waitSecond, timesDayFindPwd);
            if (r.flag == 1)
            {
                //产生随便6位数
                Random ran = new Random();
                string RanKey = ran.Next(100000, 999999).ToString();
                //存入Session
                var phoneId = Guid.NewGuid().ToString();
                var RankeyId = Guid.NewGuid().ToString();

                _memoryCache.Set($"Hyun:Captcha:{param.phoneNumber}", phoneId, new TimeSpan(0, 5, 0));
                _memoryCache.Set($"Hyun:Captcha:{RanKey}", RankeyId, new TimeSpan(0, 5, 0));

                string msg = ApplicationConfig.SendMessageEventRegisterSupplierCode;
                string strMessage = string.Format(msg, RanKey);
                r.data.other = new { UidMobile = phoneId, UidCode = RankeyId };
                r.flag = 1;
                //发送短信
                await SendMessage.SendToMobile(param.phoneNumber, strMessage);
            }

            return r;
        }

        /// <summary>
        /// 校服管理平台单位及企业注册
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("schoolandcompanyreg")]
        public async Task<Result> XfSchoolCompany_Registered([FromBody] UnitRegisterModel o)
        {
            Result r = new Result();
            //验证码有效期  601001_VCODEPERIOD
            int vcodePeriod = 30;
            var smsConfigs = await _bconfigManager.GetByModule("601001", 0, 0, 0, 0);
            if (smsConfigs != null && smsConfigs.Count > 0)
            {
                BConfigSet matchingConfig = smsConfigs.FirstOrDefault(config => config.TypeCode == "601001_VCODEPERIOD");
                if (matchingConfig != null)
                {
                    int.TryParse(matchingConfig.ConfigValue, out vcodePeriod);
                }
            }

            string ipAddress = IpHelper.GetClientIp(HttpContext);
            var valuePhoneNumber = _memoryCache.Get<string>($"Hyun:Captcha:{o.Mobile}");
            var valueRankey = _memoryCache.Get<string>($"Hyun:Captcha:{o.Code}");

            if (valuePhoneNumber == null || valueRankey == null)
            {
                r.flag = 0;
                r.msg = "校验码输入有误！";
                return r;
            }

            //验证短信码是否失效
            BSmsHistoryValidate smsValidate = await smsHistoryValidateManager.SearchSmsList(o.Mobile, 1);
            if (smsValidate == null)
            {
                r.flag = 0;
                r.msg = "请先获取校验码！";
                return r;
            }

            //有效期验证
            int SendMessageSmsDuration = vcodePeriod;
            int waiminute = DateTime.Now.Subtract(smsValidate.RegTime).Minutes;
            if (waiminute > SendMessageSmsDuration)
            {
                r.flag = 0;
                r.msg = "校验码已经超过有效期请重新获取！";
                return r;
            }

            if (smsValidate.ErrorCount >= 5)
            {
                r.flag = 0;
                r.msg = "校验码输错5次，请重新获取校验码！";
                return r;
            }

            ////判断手机号码，校验码是否正确
            //if (!valueRankey.Equals(o.Uuid))
            //{
            //    r.flag = 0;
            //    r.msg = "手机号码或校验码输入有误！";
            //    //增加错误次数
            //    smsValidate.ErrorCount += 1;
            //    await smsHistoryValidateManager.Update(smsValidate);
            //    return r;
            //}

            //判断手机号码，校验码是否正确
            if (!valuePhoneNumber.Equals(o.UidMobile) && !valueRankey.Equals(o.UidCode))
            {
                r.flag = 0;
                r.msg = "手机号码或校验码输入有误！";
                //增加错误次数
                smsValidate.ErrorCount += 1;
                await smsHistoryValidateManager.Update(smsValidate);
                return r;
            }

            r = await unitManager.XfUnitRegister(o);
            if (r.flag == 1)
            {
                //登录
                var clientIp = IpHelper.GetClientIp(HttpContext);
                r = await _acountServices.Login(o.Mobile, o.PassWord, clientIp);
                if (r.flag == 1)
                {
                    VUserDetail user = (VUserDetail)r.data.rows;
                    TokenInfoViewModel token = await CatcheAndGetJwtToken(clientIp, user);
                    if (token != null && !token.success)
                    {
                        r.flag = 0;
                        r.msg = $"注册成功！登录失败，未取得授权，请重新登录。";
                    }
                    else
                    {
                        r.data.rows = token;
                        r.data.footer = new
                        {
                            user.Id,
                            user.AcctId,
                            user.Name,
                            user.Mobile,
                            user.IsThirdClient,
                            user.NickName,
                            user.AcctName,
                            user.RoleIds,
                            user.RoleNames,
                            user.UnitId,
                            user.UnitName,
                            user.Tel,
                            user.UnitPId,
                            user.UnitPName,
                            user.UnitType,
                            user.UserType,
                            user.AdministratorType,
                            user.AreaId,
                            user.DepartmentIds,
                            user.ZipCode,
                            user.HeadPortrait,
                            user.UnitStatus
                        };
                    }
                }
                else
                {
                    r.flag = 1;
                    r.msg = "注册成功，登录失败";
                }
            }
            return r;
        }

        #endregion 校服管理平台单位企业注册
    }
}