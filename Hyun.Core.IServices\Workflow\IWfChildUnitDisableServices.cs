﻿using Microsoft.AspNetCore.Mvc;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfChildUnitDisable接口方法
    ///</summary>
    public interface IWfChildUnitDisableServices : IBaseServices<WfChildUnitDisable>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<PageModel<UnitInfo>> GetUnitPermissionInfo(WfChildUnitDisableParam param);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> SaveUnitPermission(SetPermissionModel o);
    }
}

