﻿

namespace Hyun.Core.Model
{

    ///<summary>
    ///校服采购选用组织
    ///</summary>
    public class XUniformOrganizationDto : BaseEntity
    {

        public XUniformOrganizationDto()
        {

        }
        /// <summary>
        /// 选用组织Id
        /// </summary>
        public long XUniformOrganizationId { get; set; }
        /// <summary>
        ///年度
        /// </summary>
        public int OrganizationYear { get; set; }

        /// <summary>
        ///合同批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }
        /// <summary>
        /// 区县名称
        /// </summary>
        public string CountyName { get; set; }

        /// <summary>
        /// 区县区域Id
        /// </summary>
        public long CountyAreaId { get; set; }

        /// <summary>
        ///采购表Id
        /// </summary>
        public long UniformPurchaseId { get; set; }

        /// <summary>
        ///家长代表人数（人）
        /// </summary>
        public int ParentNum { get; set; }

        /// <summary>
        ///学生代表人数（人）
        /// </summary>
        public int StudentNum { get; set; }

        /// <summary>
        ///教师代表人数（人）
        /// </summary>
        public int TeacherNum { get; set; }

        /// <summary>
        ///单位管理人员数（人）
        /// </summary>
        public int SchoolAdminNum { get; set; }

        /// <summary>
        ///其他人员数（人）
        /// </summary>
        public int OtherNum { get; set; }

        /// <summary>
        /// 总人数
        /// </summary>
        public int TotalNum { get { return ParentNum + StudentNum + TeacherNum + SchoolAdminNum + OtherNum; } }

        /// <summary>
        ///家长代表人数（人）占比
        /// </summary>
        public double ParentNumRatio { get { return GetRatio(ParentNum, TotalNum); } }

        /// <summary>
        ///学生代表人数（人）占比
        /// </summary>
        public double StudentNumRatio { get { return GetRatio(StudentNum, TotalNum); } }

        /// <summary>
        ///教师代表人数（人）占比
        /// </summary>
        public double TeacherNumRatio { get { return GetRatio(TeacherNum, TotalNum); } }

        /// <summary>
        ///单位管理人员数（人）占比
        /// </summary>
        public double SchoolAdminNumRatio { get { return GetRatio(SchoolAdminNum, TotalNum); } }

        /// <summary>
        ///其他人员数（人）占比
        /// </summary>
        public double OtherNumRatio { get { return GetRatio(OtherNum, TotalNum); } }

        /// <summary>
        /// 其中家长和学生人数比例
        /// </summary>
        public double ParentStudentRatio { get { return GetRatio(ParentNum + StudentNum ,TotalNum); } }
        /// <summary>
        ///选用组织状态(0:待备案  10：待审核  100：已备案)
        /// </summary>
        public int Statuz { get; set; }
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatuzName { get; set; }
        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
        /// <summary>
        /// 区县id
        /// </summary>
        public long CountyId { get; set; }
        /// <summary>
        /// 单位Id
        /// </summary>
        public long SchoolId { get; set; }
        /// <summary>
        /// 附件集合
        /// </summary>
        public List<long> AttachmentIdList { get; set; }
        /// <summary>
        /// 附件表id（b_Attachment）
        /// </summary>
        public long AttachmentId { get; set; }
        /// <summary>
        /// 区县是否管理单位（0：否，1：是）
        /// </summary>
        public int IsCountyManager { get; set; }
        /// <summary>
        /// 是否审核
        /// </summary>
        public int IsFiling { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string FilingExplanation { get; set; }

        public double GetRatio(int dividend, int divisor)
        {
            double ratio = 0;
            if (dividend != 0 && divisor != 0)
            {
                ratio = Math.Round(dividend / (divisor * 1.00) * 100, 2);
            }
            return ratio;
        }
    } 
}

