﻿using Consul;
using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Old.Util;
using NPOI.Util;
using SqlSugar;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection.Metadata;

namespace Hyun.Core.Api
{

    [Route("api/hyun/prole")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PRoleController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IPRoleServices roleManager;
        private readonly ISysRoleServices sysRoleManager;
        private readonly IUser user;
        private readonly ISysUserExtensionServices userManager;
        private readonly IPUnitServices unitManager;
        private readonly ISysUserRoleServices userRoleManager;


        public PRoleController(IMapper _mapper, IWebHostEnvironment _env, IPRoleServices _roleManager, IUser _user, ISysRoleServices _sysRoleManager, ISysUserExtensionServices _userManager, IPUnitServices _unitManager, ISysUserRoleServices _userRoleManager)
        {
            mapper = _mapper;
            env = _env;
            roleManager = _roleManager;
            user = _user;
            sysRoleManager = _sysRoleManager;
            userManager = _userManager;
            unitManager = _unitManager;
            userRoleManager = _userRoleManager;
        }


        [HttpPost]
        [Route("rolefind")]
        //<used>1</used>
        public async Task<Result> Role_Find([FromBody] PRoleParam param)
        {
            Result r = new Result();
            if (param != null)
            {
                param.UnitTypeId = user.UnitTypeId;
                param.IsSystemAdmin = user.IsSystemUser;
            }
            PageModel<SysRole> pg = await sysRoleManager.GetPaged(param);

            //pg.data.ForEach(f => f.Id = f.RoleId);

            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<SysRoleDto>>(pg.data);
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }


        [HttpPost]
        [Route("rolefindmyunit")]
        //<used>1</used>
        public async Task<Result> Role_FindMyUnit()
        {
            Result r = new Result();
            int ExpertOpenStatus = 0;
            int.TryParse(ApplicationConfig.ExpertOpenStatus, out ExpertOpenStatus);
            string roleIndex = user.UserRoleIds.Substring(0, 1);
            var expression = LinqExtensions.True<SysRole>();
            expression = expression.AndNew(f => f.RoleId.ToString().StartsWith(roleIndex));
            if (ExpertOpenStatus == 2 && user.UnitTypeId == 2)
            {
                expression = expression.AndNew(f => f.RoleId == 100 || f.RoleId.ToString().StartsWith(roleIndex));
            }
            else if (user.UnitTypeId == 1 && ExpertOpenStatus != 1)
            {
                expression = expression.AndNew(f => f.RoleId != 100 && f.RoleId.ToString().StartsWith(roleIndex));
            }
            List<SortBaseModel> listModel = new List<SortBaseModel>() { new SortBaseModel { SortCode = "RoleType", SortType = "ASC" }, new SortBaseModel { SortCode = "OrderSort", SortType = "ASC" } };
            PRoleParam param = new PRoleParam();
            param.pageSize = int.MaxValue;
            param.sortModel = listModel;

            if (user.IsSystemUser)
            {
                expression = expression.AndNew(f => f.RoleType == 9 && f.RoleId != 9000);
            }
            else
            {
                expression = expression.AndNew(f => f.RoleType == user.UnitTypeId);
            }
            PageModel<SysRole> pg = await sysRoleManager.GetPaged(param, expression);

            //pg.data.ForEach(f => f.Id = f.RoleId);

            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<SysRoleDto>>(pg.data);
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 根据id获取角色信息
        /// </summary>
        /// <param name="id">用户Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("rolegetbyid")]
        //<used>1</used>
        public async Task<Result> Role_GetById(long id)
        {
            Result r = new Result();
            SysRole p = await sysRoleManager.QueryById(id);
            if (p != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.total = 1;
                r.data.rows = new { p.Id, p.Name,p.Description, p.ModuleName, p.ModuleSort, p.RoleType,p.RoleId };
            }
            else
            {
                r.flag = 0;
                r.msg = "";
                r.data.total = 0;
            }
            return r;
        }

        /// <summary>
        /// 角色增加/修改
        /// </summary>
        /// <param name="model">角色对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("roleinsertupdate")]
        //<used>1</used>
        public async Task<Result> Role_InsertUpdate([FromBody]SysRoleDto model)
        {
            Result r = new Result();
            var o = mapper.Map<SysRole>(model);
            if (!user.IsSystemUser) //是否是系统管理员
            {
                r.flag = 0;
                r.msg = "非法操作，您无权操作此功能";
                return r;
            }
            else
            {
                long id = o.Id;
                r = await sysRoleManager.InsertUpdateOld(o);
                if (r.flag > 0)
                {
                    if (o.Id > 0)
                    {
                        FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改用户角色{2}成功！", user.UnitName, user.UserName, o.Name), ApplicationConfig.CommonLogFilePath + "User\\");
                    }
                    else
                    {
                        FileLog.LogMessage(string.Format("用户：【{0}-{1}】新增用户角色{2}成功！", user.UnitName, user.UserName, o.Name), ApplicationConfig.CommonLogFilePath + "User\\");
                    }
                }
                else
                {
                    if (o.Id > 0)
                    {
                        FileLog.LogMessage(string.Format("用户：【{0}-{1}】修改用户角色{2}失败！", user.UnitName, user.UserName, o.Name), ApplicationConfig.CommonLogFilePath + "User\\");
                    }
                    else
                    {
                        FileLog.LogMessage(string.Format("用户：【{0}-{1}】新增用户角色{2}失败！", user.UnitName, user.UserName, o.Name), ApplicationConfig.CommonLogFilePath + "User\\");
                    }
                }
            }
            r.data = null;
            return r;
        }

        /// <summary>
        /// 角色删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("roledelbatch")]
        //<used>1</used>
        public async Task<Result> Role_DelBatch(string ids)
        {
            Result r = new Result();

            if (!user.IsSystemUser)
            {
                r.flag = 2;
                r.msg = "非系统管理员不可增加、修改角色信息。";
                return r;
            }

            if (!string.IsNullOrEmpty(ids) && ids.Length > 0)
            {
               object[] objUserIds = ids.Split(',').Cast<object>().ToArray();
               if(await sysRoleManager.DeleteByIds(objUserIds))
 
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】删除用户ID：【{2}】角色成功！", user.UnitName, user.UserName, ids), ApplicationConfig.CommonLogFilePath + "User\\");
                }
                else
                {
                    FileLog.LogMessage(string.Format("用户：【{0}-{1}】删除用户ID：【{2}】角色失败！", user.UnitName, user.UserName, ids), ApplicationConfig.CommonLogFilePath + "User\\");
                }
                r.data = null;
            }
            return r;
        }


        [HttpPost]
        [Route("rolefindbyunittype")]
        //<used>1</used>
        public async Task<Result> Role_FindByUnitType(int unitType)
        {
            Result r = new Result();
            //此方法存在垂直越权，根据逻辑，添加以下代码
            //校、企业 看自己的，区可以看区校，市可以看市区，超管看所有
            if (!user.IsSystemUser)
            {
                if (user.UnitTypeId == (int)UnitTypes.Company || user.UnitTypeId == (int)UnitTypes.School)
                {
                    if (unitType != user.UnitTypeId)
                    {
                        unitType = -1;
                    }
                }
                else if (user.UnitTypeId == (int)UnitTypes.Couty)
                {
                    if (unitType != (int)UnitTypes.Couty && unitType != (int)UnitTypes.School)
                    {
                        unitType = -1;
                    }
                }
                else if (user.UnitTypeId == (int)UnitTypes.City)
                {
                    if (unitType != (int)UnitTypes.Couty && unitType != (int)UnitTypes.City)
                    {
                        unitType = -1;
                    }
                }
                else
                {
                    unitType = -1;
                }
            }
            List<SysRole> list = await sysRoleManager.Query(f => f.RoleType == unitType);
            if (list.Count > 0)
            {
                list.OrderBy(f => f.RoleType).OrderBy(f => f.OrderSort);
            }
            r.flag = 1;
            r.msg = "";
            r.data.total = list.Count;
            r.data.rows = mapper.Map<List<SysRoleDto>>(list);
            return r;
        }

        /// <summary>
        ///  根据用户Id获取设置角色列表信息
        /// </summary>
        /// <param name="userId">用户Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("rolegetplatformrolelist")]
        public async Task<Result> Role_GetPlatformRoleList(long userId)
        {
            Result r = new Result();
            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CityAdmin)
               && !user.Roles.Contains(RoleTypes.CoutyAdmin))
            {
                r.flag = 0;
                r.msg = "您无权使用该功能";
                return r;
            }
            SysUserExtension obj = await userManager.GetById(userId);
            if (obj != null)
            {
                PUnit u = await unitManager.QueryById(obj.UnitId);
                if (u != null)
                {
                    int whereRoleType = -1;
                    if (user.IsSystemUser)
                    {
                        whereRoleType = u.UnitType;
                    }
                    else
                    {
                        if (user.UnitTypeId == 1)
                        {
                            whereRoleType = 2;
                        }
                        else if (user.UnitTypeId == 2)
                        {
                            whereRoleType = 3;
                        }
                        else
                        {
                            whereRoleType = -1;
                        }
                    }

                    List<SysRole> listRole = await sysRoleManager.Query(f => f.RoleType == whereRoleType);
                    if (listRole.Count > 0)
                    {
                        List<SysModleDto> listModule = listRole.GroupBy(f => new
                        {
                            f.ModuleName,
                            f.ModuleSort
                        })
                        .Select(group => new SysModleDto
                        {
                            ModuleName = group.Key.ModuleName,
                            ModuleSort = group.Key.ModuleSort,
                        }).ToList();

                        listModule = listModule.Where(f => f.ModuleName != "").ToList();

                        if (listModule.Count > 0)
                        {
                            foreach (SysModleDto i in listModule)
                            {
                                var roleList = listRole.Where(a => a.ModuleName == i.ModuleName).OrderBy(a => a.OrderSort).ToList();
                                i.ListRole = roleList;
                            }
                        }

                        r.flag = 1;
                        r.msg = "查询成功";
                        r.data.rows = listModule;
                        r.data.total = 1;
                    }

                    List<SysUserRole> listUserRole = await userRoleManager.Query(f => f.UserId == userId && f.IsDeleted == false);
                    if (listUserRole.Count > 0)
                    {
                        r.data.other = listUserRole.Select(f => f.RoleId).ToList();
                    }
                }
            }
            return r;
        }

        /// <summary>
        /// 超管根据用户Id获取角色信息
        /// </summary>
        /// <param name="userId">用户Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("rolegetsuperplatformrolelist")]
        public async Task<Result> Role_GetSuperPlatformRoleList(long userId)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "只有超级管理员才能操作";
                return r;
            }

            SysUserExtension obj = await userManager.GetById(userId);
            if (obj != null)
            {
                PUnit u = await unitManager.QueryById(obj.UnitId);
                if (u != null)
                {
                    List<SysRole> listRole = await sysRoleManager.Query(f => f.RoleType == u.UnitType);
                    List<SysModleDto> listModule = listRole.GroupBy(f => new
                    {
                        f.ModuleName,
                        f.ModuleSort
                    })
                    .OrderBy(a => a.Key.ModuleSort)
                    .Select(group => new SysModleDto
                    {
                        ModuleName = group.Key.ModuleName,
                        ModuleSort = group.Key.ModuleSort,
                    }).ToList();
                    listModule = listModule.Where(f => f.ModuleName != "").ToList();

                    if (listModule.Count > 0)
                    {
                        foreach (SysModleDto i in listModule)
                        {
                            var roleList = listRole.Where(a => a.ModuleName == i.ModuleName).OrderBy(a => a.ModuleSort).ToList();
                            i.ListRole = roleList;
                        }
                    }
                    r.flag = 1;
                    r.msg = "查询成功";
                    r.data.rows = listModule;
                    r.data.total = 1;
                }

                List<SysUserRole> listUserRole = await userRoleManager.Query(f => f.UserId == userId && f.IsDeleted == false);
                if (listUserRole.Count > 0)
                {
                    r.data.other = listUserRole.Select(f => f.RoleId).ToList();
                }
            }
            return r;
        }

        /// <summary>
        /// 修改角色分类
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// int id, string moduleName, int moduleSort
        [HttpPost]
        [Route("roleeditmodule")]
        //<used>1</used>
        public async Task<Result> Role_EditModule([FromBody]SysRoleDto model)
        {
            Result r = new Result();
            SysRole o = mapper.Map<SysRole>(model);
           
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            
            SysRole role = await sysRoleManager.QueryById(o.Id);
            if (role != null)
            {
                if (role.ModuleName != null)
                {
                    //分类名称改变
                    if (role.ModuleName != o.ModuleName || role.ModuleSort != o.ModuleSort)
                    {
                        var list = await sysRoleManager.GetPaged(new PRoleParam { ModuleName = role.ModuleName, pageIndex = 1, pageSize = int.MaxValue, IsSystemAdmin = user.UnitTypeId == 0 });
                        if (list.data.Count > 0)
                        {
                            list.data.ForEach(a => { a.ModuleName = o.ModuleName; a.ModuleSort = o.ModuleSort; });
                            foreach (var item in list.data)
                            {
                                await sysRoleManager.Update(item);
                            }
                        }
                        else
                        {
                            role.ModuleName = o.ModuleName;
                            role.ModuleSort = o.ModuleSort;
                           
                            await sysRoleManager.Update(role);
                        }
                    }
                }
                else
                {
                    role.ModuleName = o.ModuleName;
                    role.ModuleSort = o.ModuleSort;
                   
                    await sysRoleManager.Update(role);
                }

                r.flag = 1;
                r.msg = "修改成功";

            }
            return r;
        }

    }
}
