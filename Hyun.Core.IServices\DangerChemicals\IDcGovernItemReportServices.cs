﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcGovernItemReport接口方法
    ///</summary>
    public interface IDcGovernItemReportServices : IBaseServices<DcGovernItemReport>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<DcGovernItemReport> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<DcGovernItemReport>> Find(Expression<Func<DcGovernItemReport, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcGovernItemReportParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<DcGovernItemReport>> GetPaged(DcGovernItemReportParam param);

        /// <summary>
        /// 危化品治理分类填报表-初始化单位数据
        /// 危化品治理分类填报表 根据分类数据初始化单位数据
        /// </summary>
        /// <param name="schoolId">单位Id</param>
        /// <param name="governYear">整治年度</param>
        /// <param name="userId">申请人Id</param>
        /// <param name="unitId">创建单位Id</param>
        /// <param name="unitIdType">填报单位类型</param>
        /// <param name="regDate">申请时间</param>
        /// <returns></returns>
        Task<Result> Init(long schoolId, int governYear, long userId, long unitId, int unitIdType, DateTime regDate);

        #region 查询统计
        /// <summary>
        /// 申报附件列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcGovernItemReportAttachment>> GetAttachmentPaged(VDcGovernItemReportAttachmentParam param);
        /// <summary>
        /// 危化品治理申报
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcGovernItemReport>> GetStatisticsPaged(VDcGovernItemReportParam param);
        #endregion
    }
}

