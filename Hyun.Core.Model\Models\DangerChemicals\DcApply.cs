namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///物品申领
    ///</summary>
    [SugarTable("dc_Apply", "物品申领")]
    public class DcApply : BaseEntity
    {

          public DcApply()
          {

          }

           /// <summary>
           ///申请批次
          /// </summary>
          [SugarColumn(Length = 31)]
          public string BatchNo { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///分类Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///规格型号Id
          /// </summary>
          public long SchoolMaterialModelId { get; set; }

           /// <summary>
           ///品牌Id
          /// </summary>
          public long SchoolMaterialBrandId { get; set; }

           /// <summary>
           ///设备名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///设备品牌
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Brand { get; set; }

           /// <summary>
           ///规格型号
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Model { get; set; }

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Num { get; set; }

        /// <summary>
        ///已领用数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal CollarNum { get; set; }

           /// <summary>
           ///单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

           /// <summary>
           ///急用程度（1：一般；2：急用）
          /// </summary>
          public int UrgentDegree { get; set; }

           /// <summary>
           ///领用理由
          /// </summary>
          [SugarColumn(Length = 1023,IsNullable = true)]
          public string Remark { get; set; }

           /// <summary>
           ///状态
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///领用人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///是否需要归还
          /// </summary>
          public bool IsNeedRevert { get; set; }

        /// <summary>
        ///累计归还/退回数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal BackNum { get; set; }

        /// <summary>
        ///已配货数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal ConfirmedNum { get; set; }

           /// <summary>
           ///使用人类型 （1：本人 2：部门 3：他人）
          /// </summary>
          public int EmployerType { get; set; }

           /// <summary>
           ///使用部门Id
          /// </summary>
          public long DepartmentId { get; set; }

           /// <summary>
           ///使用人类型为部门时，为部门Id；使用人类型为他人时，为用户Id
          /// </summary>
          public long EmployerId { get; set; }

        /// <summary>
        ///单价（单价均价，取库存表单价均价）
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Price { get; set; }

           /// <summary>
           ///申领类型（1：申领 ，2：分发，3：自采自用）
          /// </summary>
          public int ApplyType { get; set; }

           /// <summary>
           ///申领时是否指定型号
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsAppointModel { get; set; }

           /// <summary>
           ///使用部门名称
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string DepartmentName { get; set; }

           /// <summary>
           ///使用人名称
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string EmployerName { get; set; }

           /// <summary>
           ///使用时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? UseTime { get; set; }

           /// <summary>
           ///同领用人
          /// </summary>
          public long WithUserId { get; set; }

           /// <summary>
           ///用户自定义配置，配置在b_configset表（1：单人领用，2：双人领用,双人密码，3：双人领用,单人密码）
          /// </summary>
          public int ApplyMode { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


        [SugarColumn(IsIgnore = true)]
        public long TwoCatalogId { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string UnitsMeasurement { get; set; }

        [SugarColumn(IsIgnore = true)]
        public int IsSecuritySupervise { get; set; }

        [SugarColumn(IsIgnore = true)]
        public decimal StockNum { get; set; }

        [SugarColumn(IsIgnore = true)]
        public decimal ApplyingNum { get; set; }

        /// <summary>
        /// 是否需要发送短信通知发放人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int IsNeedSendMessage { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string ImageUrl { get; set; }

        [SugarColumn(IsIgnore = true)]
        public int ApplyCount { get; set; }
    }


}

