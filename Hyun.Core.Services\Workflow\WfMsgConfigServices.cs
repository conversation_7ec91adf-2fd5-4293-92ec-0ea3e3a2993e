﻿using AutoMapper;
using Hyun.Core.Common.DB;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.Validator;
using NCalc;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;
namespace Hyun.Core.Services
{

    ///<summary>
    ///WfMsgConfig方法
    ///</summary>
    public class WfMsgConfigServices : BaseServices<WfMsgConfig>, IWfMsgConfigServices
    {

        private readonly IMapper mapper;


        public WfMsgConfigServices(IMapper _mapper)
        {
            mapper = _mapper;
        }
       
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> Add(WfProcessMsgConfigModel o)
        {
            var obj = await this.Db.Queryable<WfProcess>().Where(f => f.Id == o.ProcessId).FirstAsync();
            if (obj != null)
            {
                var objNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == o.ProcessNodeId).FirstAsync();
                if(objNode == null)
                {
                    return Result<string>.Fail("流程节点Id不存在");
                }

                if(objNode.NodeType == 1 && !o.MsgCode.Equals("100100101"))
                {
                    return Result<string>.Fail("填报节点必须选择编号“100100101”");
                }
                else if(objNode.NodeType != 1 && o.MsgCode.Equals("100100101"))
                {
                    return Result<string>.Fail("审批节点必须选择编号“100100201、100100202”");
                }

                //如果是填报只能添加一个
                if(objNode.NodeType == 1)
                {
                    var objMsgConfig = await this.Db.Queryable<WfMsgConfig>().Where(f => f.ProcessId == o.ProcessId && f.ProcessNodeId == o.ProcessNodeId && f.IsDeleted == false).FirstAsync();
                    if (objMsgConfig != null)
                    {
                        return Result<string>.Fail("此流程节点已经添加数据，不能重复添加");
                    }
                }
                else
                {
                    var objMsgConfig = await this.Db.Queryable<WfMsgConfig>().Where(f => f.ProcessId == o.ProcessId && f.ProcessNodeId == o.ProcessNodeId && f.IsDeleted == false && f.MsgCode == o.MsgCode).FirstAsync();
                    if (objMsgConfig != null)
                    {
                        return Result<string>.Fail("此流程节点已经存在此编码消息数据，不能重复添加");
                    }
                }

                var objConfig = await this.Db.Queryable<BMsgConfig>().Where(f => f.MsgCode == o.MsgCode && f.IsDeleted == false).FirstAsync();
                if (objConfig != null)
                {
                    WfMsgConfig model = new WfMsgConfig();
                    // 移除手动设置ID，让系统自动生成
                    model.ProcessId = o.ProcessId;
                    model.ModuleId = obj.ModuleId;
                    model.ProcessNodeId = o.ProcessNodeId;
                    model.ProcessNodeName = o.ProcessNodeName;
                    model.MsgCode = o.MsgCode;
                    model.MsgExplain = objConfig.MsgExplain;
                    model.MsgShowExplain = objConfig.MsgShowExplain;
                    model.MainSwitch = objConfig.MainSwitch;
                    model.MsgSwitch = objConfig.MsgSwitch;
                    model.MsgTemplate = objConfig.MsgTemplate;
                    model.WeChatSwitch = objConfig.WeChatSwitch;
                    model.WeChatTemplateId = objConfig.WeChatTemplateId;
                    model.WeChatTemplate = objConfig.WeChatTemplate;
                    model.WeChatRedirectPage = objConfig.WeChatRedirectPage;
                    model.ClientIsShow = objConfig.ClientIsShow;
                    await this.Db.Insertable<WfMsgConfig>(model).ExecuteCommandAsync();
                }
                else
                {
                    return Result<string>.Fail("基础信息表中未配置此编码");
                }
            }
            else
            {
                return Result<string>.Fail("流程Id不存在");
            }

            return Result<string>.Success("添加成功");
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        public async Task<Result<string>> Edit(WfMsgConfigDto m)
        {
            WfMsgConfig o = mapper.Map<WfMsgConfig>(m);
            var obj = await this.Db.Queryable<WfMsgConfig>().Where(f => f.Id == m.Id).FirstAsync();
            if (obj != null)
            {
                obj.MsgExplain = o.MsgExplain;
                obj.MsgShowExplain = o.MsgShowExplain;
                obj.MainSwitch = o.MainSwitch;
                obj.MsgSwitch = o.MsgSwitch;
                obj.MsgTemplate = o.MsgTemplate;
                obj.WeChatSwitch = o.WeChatSwitch;
                obj.WeChatTemplateId = o.WeChatTemplateId;
                obj.WeChatTemplate = o.WeChatTemplate;
                obj.WeChatRedirectPage = o.WeChatRedirectPage;
                obj.ClientIsShow = o.ClientIsShow;
                await this.Db.Updateable<WfMsgConfig>(obj).ExecuteCommandAsync();
                return Result<string>.Success("保存成功");
            }
            else
            {
                return Result<string>.Fail("数据信息不存在");
            }
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">WfMsgConfigParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<WfMsgConfigDto>> GetPaged(WfMsgConfigParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = "PSort ASC";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> totalCount = 0;
            PageModel<WfMsgConfigDto> pageList = new PageModel<WfMsgConfigDto>();
            var list = await this.Db.Queryable<WfMsgConfig>()
               .InnerJoin<WfModule>((MC, M) => MC.ModuleId == M.Id)
               .InnerJoin<WfProcess>((MC, M, P) => MC.ProcessId == P.Id)
               .InnerJoin<WfProcessNode>((MC, M, P, PN) => MC.ProcessNodeId == PN.Id)
               .Where((MC, M, P, PN) => MC.ProcessId == param.ProcessId)
               .WhereIF(!string.IsNullOrEmpty(param.ProcessNodeName), (MC, M, P, PN) => MC.ProcessNodeName.Contains(param.ProcessNodeName))
               .Select((MC, M, P, PN) => new WfMsgConfigDto()
               {
                   Id = MC.Id,
                   IsDeleted = MC.IsDeleted,
                   ModuleId = MC.ModuleId,
                   ProcessId = MC.ProcessId,
                   ProcessNodeId = MC.ProcessId,
                   ModuleName = M.Name,
                   ProcessName = P.ProcessName,
                   ProcessNodeName = MC.ProcessNodeName,
                   MsgCode = MC.MsgCode,
                   MsgExplain = MC.MsgExplain,
                   MsgShowExplain = MC.MsgShowExplain,
                   MainSwitch = MC.MainSwitch,
                   MsgSwitch = MC.MsgSwitch,
                   MsgTemplate = MC.MsgTemplate,
                   WeChatSwitch = MC.WeChatSwitch,
                   WeChatTemplateId = MC.WeChatTemplateId,
                   WeChatTemplate = MC.WeChatTemplate,
                   WeChatRedirectPage = MC.WeChatRedirectPage,
                   ClientIsShow = MC.ClientIsShow,
                   PSort = P.PSort
               })
               .MergeTable()
               .OrderBy(orderByFields)
               .ToPageListAsync(param.pageIndex, param.pageSize, totalCount);
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }

        /// <summary>
        /// 根据流程Id获取流程节点信息
        /// </summary>
        /// <param name="porcessId"></param>
        /// <returns></returns>
        public async Task<List<dropdownModel>> GetProcessNode(long porcessId)
        {
            List<dropdownModel> listDropNode = new List<dropdownModel>();
            List<long> listProcessNodeId = new List<long>();

            var listNodeLink = await this.Db.Queryable<WfProcessNodeLink>().Where(f => f.ProcessId == porcessId && f.IsDeleted == false).ToListAsync();
            List<long> listFromId = listNodeLink.Select(f => f.FromId.Value).ToList();
            List<long> listToId = listNodeLink.Select(f => f.ToId.Value).ToList();
            listProcessNodeId.AddRange(listFromId);
            listProcessNodeId.AddRange(listToId);
            List<long> distinctNumbers = listProcessNodeId.Distinct().ToList();

            List<WfProcessNode> listNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.IsDeleted == false).ToListAsync();
            foreach(long id in distinctNumbers)
            {
                var obj = listNode.Where(f => f.Id == id).FirstOrDefault();
                if (obj != null)
                {
                    listDropNode.Add(new dropdownModel() { value = id.ToString(), label = obj.NodeName });
                }
            }
            return listDropNode;


        }

        /// <summary>
        /// 批量设置状态
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        public async Task<Result<string>> BatchSetStatuz(MsgConfigStatuzModel m)
        {
            List<WfMsgConfig> list = await this.Db.Queryable<WfMsgConfig>().Where(f => f.IsDeleted == false && m.ListId.Contains(f.Id)).ToListAsync();
            if (m.SwitchType == 1)
            {
                list.ForEach(f => f.MainSwitch = m.Statuz);
            }
            else if (m.SwitchType == 2)
            {
                list.ForEach(f => f.MsgSwitch = m.Statuz);
            }
            else if (m.SwitchType == 3)
            {
                list.ForEach(f => f.WeChatSwitch = m.Statuz);
            }
            await this.Db.Updateable<WfMsgConfig>(list).ExecuteCommandAsync();
            return Result<string>.Success("批量设置成功");
        }

        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">WfMsgConfigParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<WfMsgConfig, bool>> ListFilter(WfMsgConfigParam param)
        {
            var expression = LinqExtensions.True<WfMsgConfig>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
            }
            return expression;
        }
        #endregion
    }
}

