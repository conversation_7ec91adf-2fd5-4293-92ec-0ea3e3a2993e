﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.IServices;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.SearchModels.Common;
using Hyun.Core.Model.SearchModels.Tasks;
using Hyun.Core.Model.ViewModels;
using Hyun.Core.Repository.UnitOfWorks;
using Hyun.Core.Services;
using Hyun.Core.Tasks;
using Hyun.Old.Util;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Quartz;
using SqlSugar;
using StackExchange.Redis;
using System.Linq.Expressions;
using System.Reflection;

namespace Hyun.Core.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TasksQzController : BaseApiController
    {
        private readonly ITasksQzServices _tasksQzServices;
        private readonly ITasksLogServices _tasksLogServices;
        private readonly ISchedulerCenter _schedulerCenter;
        private readonly IUnitOfWorkManage _unitOfWorkManage;

        public TasksQzController(ITasksQzServices tasksQzServices, ISchedulerCenter schedulerCenter, IUnitOfWorkManage unitOfWorkManage, ITasksLogServices tasksLogServices)
        {
            _unitOfWorkManage = unitOfWorkManage;
            _tasksQzServices = tasksQzServices;
            _schedulerCenter = schedulerCenter;
            _tasksLogServices = tasksLogServices;
        }

        /// <summary>
        /// 分页获取
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<Result<PageModel<TasksQz>>> Get([FromBody] TasksQzParam param)
        {
            if (string.IsNullOrEmpty(param.Name) || string.IsNullOrWhiteSpace(param.Name))
            {
                param.Name = "";
            }
            Expression<Func<TasksQz, bool>> whereExpression = a => a.IsDeleted != true && (a.Name != null && a.Name.Contains(param.Name));

            var pg = await _tasksQzServices.QueryPage(whereExpression, param.pageIndex, param.pageSize, " Id desc ");
            if (pg.dataCount > 0)
            {
                foreach (var item in pg.data)
                {
                    item.Triggers = await _schedulerCenter.GetTaskStaus(item);
                }
            }
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 添加计划任务
        /// </summary>
        /// <param name="tasksQz"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<Result<string>> Post([FromBody] TasksQz tasksQz)
        {
            var  r = new Result<string>();
            _unitOfWorkManage.BeginTran();
            tasksQz.IsDeleted = false;
            if (tasksQz.EndTime==null)
            {
                tasksQz.EndTime = DateTime.MaxValue.AddDays(-1);
            }
            if (tasksQz.JobParams==null)
            {
                tasksQz.JobParams = "";
            }
            var id = (await _tasksQzServices.Add(tasksQz));
            r.flag = (id > 0 ? 1 : 0);
            try
            {
                if (r.flag == 1)
                {
                    tasksQz.Id = id;
                    r.data.rows = id.ObjToString();
                    r.msg = "添加成功";
                    if (tasksQz.IsStart)
                    {
                        //如果是启动自动
                        var ResuleModel = await _schedulerCenter.AddScheduleJobAsync(tasksQz);
                        r.flag = (ResuleModel.success ? 1 : 0);
                        if (ResuleModel.success)
                        {
                            r.msg = $"{r.msg}=>启动成功=>{ResuleModel.msg}";
                        }
                        else
                        {
                            r.msg = $"{r.msg}=>启动失败=>{ResuleModel.msg}";
                        }
                    }
                }
                else
                {
                    r.msg = "添加失败";

                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (r.flag==1)
                    _unitOfWorkManage.CommitTran();
                else
                    _unitOfWorkManage.RollbackTran();
            }
            return baseSucc<string>(r.msg, r.flag);
        }


        /// <summary>
        /// 修改计划任务
        /// </summary>
        /// <param name="tasksQz"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<Result<string>> Put([FromBody] TasksQz tasksQz)
        {
            var r = new Result<string>();
            if (tasksQz != null && tasksQz.Id > 0)
            {
                var entity = await _tasksQzServices.QueryById(tasksQz.Id);
                _unitOfWorkManage.BeginTran();
                if(await _tasksQzServices.Update(tasksQz))
                {
                    r.flag = 1;
                }
                try
                {
                    if (r.flag == 1)
                    {
                        r.msg = "修改成功";
                        r.data.rows = tasksQz?.Id.ObjToString();
                        if (tasksQz.IsStart)
                        {
                            //如果本身未停止状态，则不需要再停止。
                            if (entity.IsStart)
                            {
                                var ResuleModelStop = await _schedulerCenter.StopScheduleJobAsync(tasksQz);
                                r.msg = $"{r.msg}=>停止:{ResuleModelStop.msg}";
                            }
                            var ResuleModelStar = await _schedulerCenter.AddScheduleJobAsync(tasksQz);
                            r.flag = (ResuleModelStar.success ? 1 : 0);
                            r.msg = $"{r.msg}=>启动:{ResuleModelStar.msg}";
                        }
                        else
                        {
                            if (entity.IsStart)
                            {
                                var ResuleModelStop = await _schedulerCenter.StopScheduleJobAsync(tasksQz);
                                r.msg = $"{r.msg}=>停止:{ResuleModelStop.msg}";
                            }
                        }
                    }
                    else
                    {
                        r.msg = "修改失败";
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (r.flag == 1)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }
            return baseSucc<string>(r.msg, r.flag);
        }
        /// <summary>
        /// 删除一个任务
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<Result<string>> Delete(long Id)
        {
            var r = new Result<string>();

            var model = await _tasksQzServices.QueryById(Id);
            if (model != null)
            {
                _unitOfWorkManage.BeginTran();
                if(await _tasksQzServices.Delete(model))
                {
                    r.flag = 1;
                }
                try
                {
                    r.data.rows = Id.ObjToString();
                    if (r.flag==1)
                    {
                        r.msg = "删除成功";
                        var ResuleModel = await _schedulerCenter.StopScheduleJobAsync(model);
                        r.msg = $"{r.msg}=>任务状态=>{ResuleModel.msg}";
                    }
                    else
                    {
                        r.msg = "删除失败";
                    }

                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (r.flag==1)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }
            else
            {
                r.msg = "任务不存在";
            }
            return baseSucc<string>(r.msg, r.flag);
        }
        /// <summary>
        /// 启动计划任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<string>> StartJob(long jobId)
        {
            var data = new MessageModel<string>();

            var model = await _tasksQzServices.QueryById(jobId);
            if (model != null)
            {
                _unitOfWorkManage.BeginTran();
                try
                {
                    model.IsStart = true;
                    data.success = await _tasksQzServices.Update(model);
                    data.response = jobId.ObjToString();
                    if (data.success)
                    {
                        data.msg = "更新成功";
                        var ResuleModel = await _schedulerCenter.AddScheduleJobAsync(model);
                        data.success = ResuleModel.success;
                        if (ResuleModel.success)
                        {
                            data.msg = $"{data.msg}=>启动成功=>{ResuleModel.msg}";

                        }
                        else
                        {
                            data.msg = $"{data.msg}=>启动失败=>{ResuleModel.msg}";
                        }
                    }
                    else
                    {
                        data.msg = "更新失败";
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (data.success)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }
            else
            {
                data.msg = "任务不存在";
            }
            return data;
        }
        /// <summary>
        /// 停止一个计划任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>        
        [HttpGet]
        public async Task<MessageModel<string>> StopJob(long jobId)
        {
            var data = new MessageModel<string>();

            var model = await _tasksQzServices.QueryById(jobId);
            if (model != null)
            {
                model.IsStart = false;
                data.success = await _tasksQzServices.Update(model);
                data.response = jobId.ObjToString();
                if (data.success)
                {
                    data.msg = "更新成功";
                    var ResuleModel = await _schedulerCenter.StopScheduleJobAsync(model);
                    if (ResuleModel.success)
                    {
                        data.msg = $"{data.msg}=>停止成功=>{ResuleModel.msg}";
                    }
                    else
                    {
                        data.msg = $"{data.msg}=>停止失败=>{ResuleModel.msg}";
                    }
                }
                else
                {
                    data.msg = "更新失败";
                }
            }
            else
            {
                data.msg = "任务不存在";
            }
            return data;
        }
        /// <summary>
        /// 暂停一个计划任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>        
        [HttpGet]
        public async Task<MessageModel<string>> PauseJob(long jobId)
        {
            var data = new MessageModel<string>();
            var model = await _tasksQzServices.QueryById(jobId);
            if (model != null)
            {
                _unitOfWorkManage.BeginTran();
                try
                {
                    data.success = await _tasksQzServices.Update(model);
                    data.response = jobId.ObjToString();
                    if (data.success)
                    {
                        data.msg = "更新成功";
                        var ResuleModel = await _schedulerCenter.PauseJob(model);
                        if (ResuleModel.success)
                        {
                            data.msg = $"{data.msg}=>暂停成功=>{ResuleModel.msg}";
                        }
                        else
                        {
                            data.msg = $"{data.msg}=>暂停失败=>{ResuleModel.msg}";
                        }
                        data.success = ResuleModel.success;
                    }
                    else
                    {
                        data.msg = "更新失败";
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (data.success)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }
            else
            {
                data.msg = "任务不存在";
            }
            return data;
        }
        /// <summary>
        /// 恢复一个计划任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>        
        [HttpGet]
        public async Task<MessageModel<string>> ResumeJob(long jobId)
        {
            var data = new MessageModel<string>();

            var model = await _tasksQzServices.QueryById(jobId);
            if (model != null)
            {
                _unitOfWorkManage.BeginTran();
                try
                {
                    model.IsStart = true;
                    data.success = await _tasksQzServices.Update(model);
                    data.response = jobId.ObjToString();
                    if (data.success)
                    {
                        data.msg = "更新成功";
                        var ResuleModel = await _schedulerCenter.ResumeJob(model);
                        if (ResuleModel.success)
                        {
                            data.msg = $"{data.msg}=>恢复成功=>{ResuleModel.msg}";
                        }
                        else
                        {
                            data.msg = $"{data.msg}=>恢复失败=>{ResuleModel.msg}";
                        }
                        data.success = ResuleModel.success;
                    }
                    else
                    {
                        data.msg = "更新失败";
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (data.success)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }
            else
            {
                data.msg = "任务不存在";
            }
            return data;
        }
        /// <summary>
        /// 重启一个计划任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<string>> ReCovery(long jobId)
        {
            var data = new MessageModel<string>();
            var model = await _tasksQzServices.QueryById(jobId);
            if (model != null)
            {

                _unitOfWorkManage.BeginTran();
                try
                {
                    model.IsStart = true;
                    data.success = await _tasksQzServices.Update(model);
                    data.response = jobId.ObjToString();
                    if (data.success)
                    {
                        data.msg = "更新成功";
                        var ResuleModelStop = await _schedulerCenter.StopScheduleJobAsync(model);
                        var ResuleModelStar = await _schedulerCenter.AddScheduleJobAsync(model);
                        if (ResuleModelStar.success)
                        {
                            data.msg = $"{data.msg}=>停止:{ResuleModelStop.msg}=>启动:{ResuleModelStar.msg}";
                            data.response = jobId.ObjToString();

                        }
                        else
                        {
                            data.msg = $"{data.msg}=>停止:{ResuleModelStop.msg}=>启动:{ResuleModelStar.msg}";
                            data.response = jobId.ObjToString();
                        }
                        data.success = ResuleModelStar.success;
                    }
                    else
                    {
                        data.msg = "更新失败";
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (data.success)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }
            else
            {
                data.msg = "任务不存在";
            }
            return data;

        }
        /// <summary>
        /// 获取任务命名空间
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public MessageModel<List<QuartzReflectionViewModel>> GetTaskNameSpace()
        {
            var baseType = typeof(IJob);
            var path = AppDomain.CurrentDomain.RelativeSearchPath ?? AppDomain.CurrentDomain.BaseDirectory;
            var referencedAssemblies = System.IO.Directory.GetFiles(path, "Hyun.Core.Tasks.dll").Select(Assembly.LoadFrom).ToArray();
            var types = referencedAssemblies
                .SelectMany(a => a.DefinedTypes)
                .Select(type => type.AsType())
                .Where(x => x != baseType && baseType.IsAssignableFrom(x)).ToArray();
            var implementTypes = types.Where(x => x.IsClass).Select(item => new QuartzReflectionViewModel { nameSpace = item.Namespace, nameClass = item.Name, remark = "" }).ToList();
            return MessageModel<List<QuartzReflectionViewModel>>.Success("获取成功", implementTypes);
        }

        /// <summary>
        /// 立即执行任务
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<string>> ExecuteJob(long jobId)
        {
            var data = new MessageModel<string>();

            var model = await _tasksQzServices.QueryById(jobId);
            if (model != null)
            {
                return await _schedulerCenter.ExecuteJobAsync(model);
            }
            else
            {
                data.msg = "任务不存在";
            }
            return data;
        }
        /// <summary>
        /// 获取任务运行日志
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<Result<PageModel<TasksLog>>> GetTaskLogs([FromBody] TasksLogParam param)
        {
            var pg = await _tasksLogServices.GetTaskLogs(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }
        /// <summary>
        /// 任务概况
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<object>> GetTaskOverview(long jobId, int page = 1, int pageSize = 10, DateTime? runTimeStart = null, DateTime? runTimeEnd = null, string type = "month")
        {
            var model = await _tasksLogServices.GetTaskOverview(jobId, runTimeStart, runTimeEnd, type);
            return MessageModel<object>.Message(true, "获取成功", model);
        }


        /// <summary>
        /// 适用于装备平台的查询所有任务计划
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        //<used>1</used>
        public async Task<Result> tasksqzfind([FromBody] TasksqzParam param)
        {
            Result r = new Result();

            PageModel<TasksQz> pg = await _tasksQzServices.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        //<used>1</used>
        public async Task<Result> deletebatch(string ids)
        {
            Result r = new Result();
            var data = new MessageModel<string>();
            if (!string.IsNullOrEmpty(ids) && ids.Length > 0)
            {
                r = await _tasksQzServices.DeleteByIds(ids);
                var idz = ids.Split(",");
                var jobArray = idz.ToArray();
                var joblist = await _tasksQzServices.QueryByIDs(jobArray);
                _unitOfWorkManage.BeginTran();
                try
                {
                    if (r.flag == 1)
                    {
                        r.msg = "删除成功";
                        if (joblist.Count > 0)
                        {
                            foreach (var jobitem in joblist)
                            {
                                var ResuleModel = await _schedulerCenter.StopScheduleJobAsync(jobitem);
                                r.msg += $"{data.msg}=>任务[{jobitem.Id}]状态=>{ResuleModel.msg}";
                            }
                        }
                    }
                    else
                    {
                        data.msg = "删除失败";
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (data.success)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }

            return r;
        }

        /// <summary>
        /// 根据id获取任务计划详情
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost]
        //<used>1</used>
        public async Task<Result> getbyid(long id)
        {
            Result r = new Result();
            TasksQz p = await _tasksQzServices.QueryById(id);
            if (p != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.total = 1;
                r.data.rows = p;
            }
            else
            {
                r.flag = 0;
                r.msg = "";
                r.data.total = 0;
            }
            return r;
        }

        /// <summary>
        /// 保存任务计划
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        //<used>1</used>
        public async Task<MessageModel<string>> save([FromBody] TasksQz model)
        {
            var data = new MessageModel<string>();
            model.BeginTime = DateTime.Now;
            model.EndTime = DateTime.Now.AddYears(50);
            model.RunTimes = 0;
            model.IntervalSecond = 0;
            model.CycleRunTimes = 0;

            _unitOfWorkManage.BeginTran();
            var id = (await _tasksQzServices.Add(model));
            data.success = id > 0;
            try
            {
                if (data.success)
                {
                    model.Id = id;
                    data.response = id.ObjToString();
                    data.msg = "添加成功";
                    if (model.IsStart)
                    {
                        //如果是启动自动
                        var ResuleModel = await _schedulerCenter.AddScheduleJobAsync(model);
                        data.success = ResuleModel.success;
                        if (ResuleModel.success)
                        {
                            data.msg = $"{data.msg}=>启动成功=>{ResuleModel.msg}";
                        }
                        else
                        {
                            data.msg = $"{data.msg}=>启动失败=>{ResuleModel.msg}";
                        }
                    }
                }
                else
                {
                    data.msg = "添加失败";
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (data.success)
                    _unitOfWorkManage.CommitTran();
                else
                    _unitOfWorkManage.RollbackTran();
            }
            return data;
        }

        /// <summary>
        /// 修改任务计划
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        //<used>1</used>
        public async Task<MessageModel<string>> update([FromBody] TasksQz model)
        {
            var data = new MessageModel<string>();
            model.BeginTime = DateTime.Now;
            model.EndTime = DateTime.Now.AddYears(50);

            if (model != null && model.Id > 0)
            {
                _unitOfWorkManage.BeginTran();
                data.success = await _tasksQzServices.Update(model);
                try
                {
                    if (data.success)
                    {
                        data.msg = "修改成功";
                        data.response = model?.Id.ObjToString();
                        if (model.IsStart)
                        {
                            var ResuleModelStop = await _schedulerCenter.StopScheduleJobAsync(model);
                            data.msg = $"{data.msg}=>停止:{ResuleModelStop.msg}";
                            var ResuleModelStar = await _schedulerCenter.AddScheduleJobAsync(model);
                            data.success = ResuleModelStar.success;
                            data.msg = $"{data.msg}=>启动:{ResuleModelStar.msg}";
                        }
                        else
                        {
                            var ResuleModelStop = await _schedulerCenter.StopScheduleJobAsync(model);
                            data.msg = $"{data.msg}=>停止:{ResuleModelStop.msg}";
                        }
                    }
                    else
                    {
                        data.msg = "修改失败";
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                finally
                {
                    if (data.success)
                        _unitOfWorkManage.CommitTran();
                    else
                        _unitOfWorkManage.RollbackTran();
                }
            }
            return data;
        }
    }
}
