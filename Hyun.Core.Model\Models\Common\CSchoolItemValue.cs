namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位标准条目结果
    ///</summary>
    [SugarTable("c_SchoolItemValue","单位标准条目结果")]
    public class CSchoolItemValue : BaseEntity
    {

          public CSchoolItemValue()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///标准条目Id
          /// </summary>
          public long StandardItemId { get; set; }

           /// <summary>
           ///对应值（1：具体值；2：布尔值；）
          /// </summary>
          [SugarColumn(Length = 255)]
          public string InputValue { get; set; }

           /// <summary>
           ///条目类型（类型为3，可以从子表中获取具体信息）
          /// </summary>
          public int StandardItemType { get; set; }

        /// <summary>
        ///装备金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Amount { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

