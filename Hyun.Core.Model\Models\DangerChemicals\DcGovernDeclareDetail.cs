namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///危化品治理申报分类明细表
    ///</summary>
    [SugarTable("dc_GovernDeclareDetail","危化品治理申报分类明细表")]
    public class DcGovernDeclareDetail : BaseEntity
    {

          public DcGovernDeclareDetail()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///整治分类表Id
          /// </summary>
          public long GovernItemId { get; set; }

           /// <summary>
           ///整治填报表Id
          /// </summary>
          public long GovernReportId { get; set; }

           /// <summary>
           ///整治填报汇总表Id
          /// </summary>
          public long DeclareSummaryId { get; set; }

           /// <summary>
           ///整治问题表Id（添加整治表的时候回写Id）
          /// </summary>
          public long GovernRectifyId { get; set; }

           /// <summary>
           ///存在问题隐患（1：是  0：否）
          /// </summary>
          public int IsTallyClaim { get; set; }

           /// <summary>
           ///整改期限
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? RectifyLimit { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///整改建议
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Suggest { get; set; }

           /// <summary>
           ///创建单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///申请时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///申请人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

