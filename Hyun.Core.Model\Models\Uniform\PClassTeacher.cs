using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hyun.Core.Model.Models.Uniform
{
    ///<summary>
    ///校服班级表
    ///</summary>
    [SugarTable("p_ClassTeacher", "校服班级班主任")]
    public class PClassTeacher : BaseEntity
    {

        public PClassTeacher()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///校服班级Id
        /// </summary>
        public long ClassInfoId { get; set; }

        /// <summary>
        ///班主任Id
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public long TeacherUserId { get; set; }

        /// <summary>
        ///班主任
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string TeacherName { get; set; }

        /// <summary>
        ///手机
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string TeacherMobile { get; set; }

        /// <summary>
        /// 是否有效（1：有效  0：无效）默认1
        /// </summary>
        [SugarColumn(DefaultValue = "1")]
        public int IsCurrent { get; set; } = 1;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

    }

}
