namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///计划录入存储临时表
    ///</summary>
    [SugarTable("dc_SchoolMaterialTemp","计划录入存储临时表")]
    public class DcSchoolMaterialTemp : BaseEntity
    {

          public DcSchoolMaterialTemp()
          {

          }

           /// <summary>
           ///采购申请单Id
          /// </summary>
          public long PurchaseOrderId { get; set; }

           /// <summary>
           ///物品采购清单Id
          /// </summary>
          public long PurchaseListId { get; set; }

           /// <summary>
           ///采购批次
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string PurchaseBatchNo { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///分类Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///规格型号Id
          /// </summary>
          public long SchoolMaterialModelId { get; set; }

           /// <summary>
           ///品牌Id
          /// </summary>
          public long SchoolMaterialBrandId { get; set; }

           /// <summary>
           ///供应商列表Id
          /// </summary>
          public long LvCompanyId { get; set; }

           /// <summary>
           ///设备名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///设备品牌
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Brand { get; set; }

           /// <summary>
           ///规格型号
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Model { get; set; }

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Num { get; set; }

           /// <summary>
           ///单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

        /// <summary>
        ///单价
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Price { get; set; }

        /// <summary>
        ///金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Sum { get; set; }

           /// <summary>
           ///质保月份
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? WarrantyMonth { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 1023,IsNullable = true)]
          public string Remark { get; set; }

           /// <summary>
           ///有效期
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? ValidDate { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///存放地点（dc_DepositAddress表Id）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? DepositAddressId { get; set; }

           /// <summary>
           ///MSDS文件
          /// </summary>
          [SugarColumn(Length = 2000,IsNullable = true)]
          public string MsdsFile { get; set; }

           /// <summary>
           ///储藏柜地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string CabinetAddress { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;
        /// <summary>
        ///危化品配置等级
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DangerChemicalsLevel { get; set; }
        /// <summary>
        ///是否变更
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int IsChange { get; set; }
    }


}

