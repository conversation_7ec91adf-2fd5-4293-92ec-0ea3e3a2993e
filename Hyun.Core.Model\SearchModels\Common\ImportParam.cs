﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hyun.Core.Model.SearchModels.Common
{
    public class ImportParam
    {
        /// <summary>
        /// 导入文件上传服务器后的路径
        /// </summary>
        public string FilePath { get; set; }
        /// <summary>
        /// 是否更新已有的数据
        /// </summary>
        public int? IsOverride { get; set; }

        /// <summary>
        /// 班级表Id
        /// </summary>
        public long UniformClassId { get; set; }
    }

    /// <summary>
    /// 项目清单导入实体
    /// </summary>
    public class ProjectListImportParam
    {

        /// <summary>
        /// 导入文件上传服务器后的路径
        /// </summary>
        public string FilePath { get; set; }
        /// <summary>
        /// 是否更新已有的数据
        /// </summary>
        public int? IsOverride { get; set; }

        /// <summary>
        /// 流程Id
        /// 流程Id
        /// </summary>
        public long ProcessId { get; set; }

        /// <summary>
        /// 流程节点Id
        /// </summary>
        public long ProcessNodeId { get; set; } = 0;

        /// <summary>
        /// 填报
        /// </summary>
        public long ProjectDeclarationId { get; set; }

        /// <summary>
        /// 字段Code编码
        /// </summary>
        public string FieldCode { get; set; }
    }

    /// <summary>
    /// 单位导入实体
    /// </summary>
    public class ImportUnitParam
    {
        /// <summary>
        /// 导入文件上传服务器后的路径
        /// </summary>
        public string FilePath { get; set; }
        /// <summary>
        /// 是否更新已有的数据
        /// </summary>
        public int? IsOverride { get; set; }
    }



}
