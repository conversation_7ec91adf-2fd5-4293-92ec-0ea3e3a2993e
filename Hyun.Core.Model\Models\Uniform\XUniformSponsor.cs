﻿namespace Hyun.Core.Model
{

    ///<summary>
    ///校服资助明细表
    ///</summary>
    [SugarTable("x_UniformSponsor", "校服资助明细表")]
    public class XUniformSponsor : BaseEntity
    {

        public XUniformSponsor()
        {

        }

        /// <summary>
        ///校服采购表Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? UniformPurchaseId { get; set; }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///每人资助金额
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? PersonSupport { get; set; }

        /// <summary>
        ///资助人数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? SponsorUserNum { get; set; }

        /// <summary>
        ///资助总金额
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "money")]
        public decimal? SponsorAmount { get; set; }

        /// <summary>
        ///资助来源名称
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string SponsorSourceName { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 附件集合
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<BAttachment> ListAttachment { get; set; }


        /// <summary>
        /// 预定人数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int OrderNum { get; set; } = 0;
    }


}

