﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcGovernRectify接口方法
    ///</summary>
    public interface IDcGovernRectifyServices : IBaseServices<DcGovernRectify>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<DcGovernRectify> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<DcGovernRectify>> Find(Expression<Func<DcGovernRectify, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">DcGovernRectifyParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<DcGovernRectify>> GetPaged(DcGovernRectifyParam param);

       //<used>1</used>
       Task<IEnumerable<object>> DcGovernRectifyList_Find(DcGovernRectifyParam param);

       //<used>1</used>
       Task<Result> Rectify(long id, long schoolId, int categoryId, int moduleType, int statuz, string measures, DateTime rectifyDate, long rectifyUserId);
        /// <summary>
        /// 问题隐患清单列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcGovernRectifyList>> GetStatisticsPaged(VDcGovernRectifyListParam param);
    }
}

