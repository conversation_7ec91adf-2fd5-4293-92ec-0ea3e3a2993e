namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///002区域b_Area
    ///</summary>
    [SugarTable("b_Area","002区域b_Area")]
    public class BArea : BaseEntity
    {

          public BArea()
          {

          }

           /// <summary>
           ///父Id
          /// </summary>
          public long Pid { get; set; }

           /// <summary>
           ///名称
          /// </summary>
          [SugarColumn(Length = 127)]
          public string Name { get; set; }

           /// <summary>
           ///简称
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string Brief { get; set; }

           /// <summary>
           ///英文
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string EnName { get; set; }

           /// <summary>
           ///英文简称
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string EnBrief { get; set; }

           /// <summary>
           ///编码
          /// </summary>
          [SugarColumn(Length = 15,IsNullable = true)]
          public string Code { get; set; }

           /// <summary>
           ///深度
          /// </summary>
          public int Depth { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          public int Sort { get; set; }

           /// <summary>
           ///路径
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Path { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;

        /// <summary>
        /// 完整路径
        /// </summary>

        [SugarColumn(IsIgnore = true)]
        public string FullName { get; set; }


    }

    /// <summary>
    /// 区域视图实体
    /// </summary>
    public class AreaView
    {
        /// <summary>
        /// 区域Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string Name { get; set; }

    }

    /// <summary>
    /// 区域视图模型
    /// </summary>

    public class AreaViewModel
    {
        /// <summary>
        /// 区域Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 子区域
        /// </summary>
        public List<AreaViewModel> Children { get; set; }
    }


}

