﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hyun.Core.Model.Models.Uniform
{
    /// <summary>
    /// 校服征用状态枚举
    /// </summary>
    public enum UniformSolicitedEnum
    {
        /// <summary>
        /// 0: 创建中
        /// </summary>
        [Description("创建中")]
        Cretae = 0,
        /// <summary>
        /// 0: 已发布
        /// </summary>
        [Description("已发布")]
        Publish = 1
    }

    /// <summary>
    /// 校服征用状态枚举
    /// </summary>
    public enum UniformSeekStatuzEnum
    {
        /// <summary>
        /// 1: 正在征求
        /// </summary>
        [Description("正在征求")]
        Ongoing = 1,
        /// <summary>
        /// 2: 征求结束
        /// </summary>
        [Description("征求结束")]
        End = 2
    }

    /// <summary>
    /// 校服备案状态枚举
    /// </summary>
    public enum UniformFilingEnum
    {
        /// <summary>
        /// 0: 待备案
        /// </summary>
        [Description("待备案")]
        SubmitNone = 0,
        /// <summary>
        /// 10: 待备查
        /// </summary>
        [Description("待备查")]
        Wait = 10,
        /// <summary>
        /// 100: 已备案
        /// </summary>
        [Description("已备案")]
        Filinged = 100
    }

    /// <summary>
    /// 校服提交审核状态枚举
    /// </summary>
    public enum XUniformAuditStatuzEnumm
    {
        /// <summary>
        /// 待提交
        /// </summary>
        [Description("待提交")]
        WaitSubmit = 0,
        /// <summary>
        /// 待审核
        /// </summary>
        [Description("待审核")]
        Wait = 10,
        /// <summary>
        /// 审核不通过
        /// </summary>
        [Description("审核不通过")]
        AuditFailed = 11,
        /// <summary>
        /// 审核通过
        /// </summary>
        [Description("审核通过")]
        AuditSuccess = 20
    }

    /// <summary>
    /// 状态意见（1：同意  2：不同意）
    /// </summary>
    public enum StatuzOpinionEnum
    {
        /// <summary>
        /// 同意
        /// </summary>
        [Description("同意")]
        Ok = 1,
        /// <summary>
        /// 不同意
        /// </summary>
        [Description("不同意")]
        Not = 2
    }
    /// <summary>
    /// 校服上架审核状态
    /// </summary>
    public enum UniformShelfAuditEnum
    {
        /// <summary>
        /// 填报中
        /// </summary>
        [Description("填报中")]
        Create = 0,
        /// <summary>
        /// 待审核
        /// </summary>
        [Description("待审核")]
        AuditWait = 10,
        /// <summary>
        /// 审核不通过
        /// </summary>
        [Description("审核不通过")]
        AuditBackout = 11,

        /// <summary>
        /// 审核通过
        /// </summary>
        [Description("审核通过")]
        AuditOk = 20,
    }

    /// <summary>
    /// 单位申请认证附件类型为110
    /// </summary>
    public enum UnitFileEnum
    {
        /// <summary>
        /// 单位法人证书
        /// </summary>
        [Description("单位法人证书")]
        Legal = 11001,

        /// <summary>
        /// 营业执照
        /// </summary>
        [Description("营业执照")]
        License = 11002,

        /// <summary>
        /// 生产许可证
        /// </summary>
        [Description("生产许可证")]
        Production = 11003,


        /// <summary>
        /// 其  它
        /// </summary>
        [Description("其  它")]
        Other = 11004,

        /// <summary>
        /// 企业logo
        /// </summary>
        [Description("企业logo")]
        Logo = 11005
    }

    /// <summary>
    /// 业务配置
    /// </summary>
    public enum ConfigEnum
    {
        /// <summary>
        /// 校服采购方式
        /// </summary>
        [Description("校服采购方式")]
        Procure = 1000,

        /// <summary>
        /// 备案信息审核
        /// </summary>
        [Description("备案信息审核")]
        Filing = 2000,

        /// <summary>
        /// 指标名称分值
        /// </summary>
        [Description("指标名称分值")]
        IndexName = 3100,

        /// <summary>
        /// 评价等级分值
        /// </summary>
        [Description("评价等级分值")]
        GradeScore = 3200,

        /// <summary>
        /// 校服展示价格
        /// </summary>
        [Description("校服展示价格")]
        Price = 4000,
    }

    /// <summary>
    /// 校服采购-采购组织形式
    /// </summary>
    public enum XUniformOrganizationFormEnum
    {
        /// <summary>
        /// 委托采购
        /// </summary>
        [Description("委托采购")]
        Delegation = 1,

        /// <summary>
        /// 自行采购
        /// </summary>
        [Description("自行采购")]
        Self = 2
    }

    /// <summary>
    /// 校服调换状态
    /// </summary>
    public enum UniformSwapEnum
    {
        /// <summary>
        /// 1: 待发起
        /// </summary>
        [Description("待发起")]
        Stay = 1,

        /// <summary>
        /// 2: 已发起
        /// </summary>
        [Description("已发起")]
        Already = 2,

        /// <summary>
        /// 3: 已结束
        /// </summary>
        [Description("已结束")]
        End = 3,

    }

    /// <summary>
    /// 校服填报状态
    /// </summary>
    public enum UniformSwapStatuzEnum
    {
        /// <summary>
        /// 1: 正在填报
        /// </summary>
        [Description("正在填报")]
        Stay = 1,

        /// <summary>
        /// 2: 调换结束
        /// </summary>
        [Description("调换结束")]
        Already = 2,

    }

    /// <summary>
    /// 校服评价状态
    /// </summary>
    public enum UniformEvaluateStatuzEnum
    {
        /// <summary>
        /// 1: 正在评价
        /// </summary>
        [Description("正在评价")]
        Stay = 1,

        /// <summary>
        /// 2: 评价结束
        /// </summary>
        [Description("评价结束")]
        Already = 2,

    }

    /// <summary>
    /// 校服平台-审核日志code枚举
    /// </summary>
    public enum UniformAuditLogCodeEnum
    {
        /// <summary>
        /// 校服选用提交、备查、退回
        /// </summary>
        [Description("校服选用")]
        Scheme = 101,

        /// <summary>
        /// 校服采购提交、备查、退回
        /// </summary>
        [Description("校服采购")]
        Purchase = 102,

        /// <summary>
        /// 校服上架审核
        /// </summary>
        [Description("校服审核")]
        Shelf = 103,

        /// <summary>
        /// 选用组织页面
        /// </summary>
        [Description("选用组织")]
        Organization = 104,

        /// <summary>
        /// 校服资助
        /// </summary>
        [Description("校服资助")]
        Sponsor = 105
    }

    /// <summary>
    /// 校服征订状态
    /// </summary>
    public enum UniformSubscriptionStatuzEnum
    {
        /// <summary>
        /// 填报中
        /// </summary>
        [Description("填报中")]
        FillIn = 0,
        /// <summary>
        /// 正在征订
        /// </summary>
        [Description("正在征订")]
        Subscription = 10,
        /// <summary>
        /// 征订结束
        /// </summary>
        [Description("征订结束")]
        Finish = 100
    }


    /// <summary>
    /// 评价人分类
    /// </summary>
    public enum EvaluateUserTypeEnum
    {
        /// <summary>
        /// 家长
        /// </summary>
        [Description("家长")]
        Parent = 1,

        /// <summary>
        /// 学生
        /// </summary>
        [Description("学生")]
        Student = 2,

        /// <summary>
        /// 教师
        /// </summary>
        [Description("教师")]
        Teacher = 3,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("其他")]
        Other = 4
    }

    /// <summary>
    /// 资讯栏目类型
    /// </summary>
    public enum CateTypeEnum
    {
        /// <summary>
        /// 1: 运营商栏目
        /// </summary>
        [Description("网站信息")]
        Operator = 1,

        /// <summary>
        /// 2:动态栏目
        /// </summary>
        [Description("资讯信息")]
        Dynamic = 2,

        /// <summary>
        /// 3:轮播图片
        /// </summary>
        [Description("轮播图片")]
        DynamicImg = 3,
    }

    /// <summary>
    /// 是否支持多条目
    /// </summary>
    public enum IsManyEnum
    {
        /// <summary>
        /// 1: 支持
        /// </summary>
        [Description("支持")]
        Support = 1,

        /// <summary>
        /// 2:不支持
        /// </summary>
        [Description("不支持")]
        NoSupport = 2,
    }

    /// <summary>
    /// 是否
    /// </summary>
    public enum YesNoEnum
    {
        /// <summary>
        /// 是
        /// </summary>
        [Description("是")]
        Yes = 1,

        /// <summary>
        /// 否
        /// </summary>
        [Description("否")]
        No = 2,
    }

}
