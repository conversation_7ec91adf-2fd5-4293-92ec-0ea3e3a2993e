namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///校服采购
    ///</summary>
    [SugarTable("x_UniformPurchase", "校服采购")]
    public class XUniformPurchase : BaseEntity
    {

        public XUniformPurchase()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///年度
        /// </summary>
        public int PurchaseYear { get; set; }

        /// <summary>
        ///合同履约批次
        /// </summary>
        [SugarColumn(Length = 31)]
        public string PurchaseNo { get; set; }

        /// <summary>
        ///采购编码
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string PurchaseCode { get; set; }

        /// <summary>
        ///是否续签合同
        /// </summary>
        public int IsContractRenewal { get; set; }

        /// <summary>
        ///合同开始时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ContractStartDate { get; set; }

        /// <summary>
        ///合同终止时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ContractEndDate { get; set; }

        /// <summary>
        ///供应商
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? SupplierId { get; set; }

        /// <summary>
        ///采购组织形式名称
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string OrganizationFormName { get; set; }

        /// <summary>
        ///采购组织形式
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? OrganizationForm { get; set; }
        /// <summary>
        /// 是否关联校服（0：未关联  1：关联（即审核校服通过，选择了该合同批次））
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int IsRelatedUniform { get; set; }
        /// <summary>
        ///备案状态(0:待备案 10:待审核 100:已备案)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? FilingStatuz { get; set; }

        /// <summary>
        ///审核时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? FilingTime { get; set; }

        /// <summary>
        /// 是否审核
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? IsFiling { get; set; }
        /// <summary>
        ///备案说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string FilingExplanation { get; set; }

        /// <summary>
        ///征订截止时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SubscriptionDeadline { get; set; }

        /// <summary>
        ///需订购人数
        /// </summary>
        public int OrderNum { get; set; }

        /// <summary>
        ///已订购人数
        /// </summary>
        public int OrderedNum { get; set; }

        /// <summary>
        ///征订状态（0：填报中  10：正在征订  100：征订结束）
        /// </summary>
        public int SubscriptionStatuz { get; set; }

        /// <summary>
        /// 调换状态（1：待发起，2：已发起）默认1
        /// </summary>
        [SugarColumn(DefaultValue = "1")]
        public int SwapStatuz { get; set; }

        /// <summary>
        ///调换开始时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SwapBegin { get; set; }

        /// <summary>
        ///调换截止时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SwapDeadline { get; set; }

        /// <summary>
        ///总调换人数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? SwapStudentNum { get; set; }

        /// <summary>
        ///资助人数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? SponsorUserNum { get; set; }

        /// <summary>
        /// 每人资助金额
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "decimal(18, 2)")]
        public decimal? PersonSupport { get; set; }

        /// <summary>
        ///资助总金额
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? SponsorAmount { get; set; }

        /// <summary>
        /// 资助占比
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? SponsorRatio { get; set; }

        /// <summary>
        ///资助来源名称
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string SponsorSourceName { get; set; }

        /// <summary>
        ///资助时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SponsorTime { get; set; }

        /// <summary>
        ///评价截止日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? EvaluateDeadline { get; set; }

        /// <summary>
        ///评价参与人数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? EvaluateNum { get; set; }

        /// <summary>
        ///评价综合得分
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? EvaluateScore { get; set; }

        /// <summary>
        ///评价创建时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? EvaluateTime { get; set; }

        /// <summary>
        /// 合同主体Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ContractMainBodyId { get; set; }

        /// <summary>
        /// 合同主体名称
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string ContractMainBody { get; set; }

        /// <summary>
        /// 签约日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ContractSignDate { get; set; }

        /// <summary>
        ///采购方式Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? PayMethodId { get; set; }

        /// <summary>
        ///采购方式
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string PayMethod { get; set; }

        /// <summary>
        ///供应商属地
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? SupplierLocationId { get; set; }

        /// <summary>
        ///供应商属地
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public string SupplierLocation { get; set; }

        /// <summary>
        ///供货期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? GoodsDeadline { get; set; }

        /// <summary>
        ///质保月
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? WarrantyMonth { get; set; }

        /// <summary>
        ///
        /// </summary>
        [SugarColumn(Length = 1027, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        ///订购人数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ContractPersonNum { get; set; }

        /// <summary>
        ///合同金额
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? ContractAmount { get; set; }

        /// <summary>
        ///校服采购表Id（x_UniformBuy）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? UniformBuyId { get; set; }
        /// <summary>
        ///校服采购批次
        /// </summary>
        [SugarColumn(Length = 31)]
        public string UniformBuyNo { get; set; }

        /// <summary>
        ///送货日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        ///验收日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? AcceptanceDate { get; set; }

        /// <summary>
        ///供应商送检日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SupplierSendTestDate { get; set; }

        /// <summary>
        ///学校送检日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SchoolSendTestDate { get; set; }
        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


    }


}

