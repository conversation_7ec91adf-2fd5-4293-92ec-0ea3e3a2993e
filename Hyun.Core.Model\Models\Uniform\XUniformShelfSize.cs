namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///校服供应商上架尺码表
    ///</summary>
    [SugarTable("x_UniformShelfSize", "上架尺码表")]
    public class XUniformShelfSize : BaseEntity
    {

        public XUniformShelfSize()
        {

        }

        /// <summary>
        ///校服Id
        /// </summary>
        public long UniformShelfId { get; set; }

        /// <summary>
        ///原尺码表Id
        /// </summary>
        public long UniformCompanySizeId { get; set; }
         
        /// <summary>
        ///规格
        /// </summary>
        [SugarColumn(Length = 51)]
        public string StandardName { get; set; }
        /// <summary>
        ///原尺码表Id
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 单位Id
        /// </summary>
        public long SchoolId { get; set; }

    }


}

