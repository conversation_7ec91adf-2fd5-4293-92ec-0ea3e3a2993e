﻿using Hyun.Core.Model.Helper;
using System.ComponentModel;
using NPOI.SS.UserModel;
using NPOI.OpenXmlFormats.Dml.WordProcessing;
using NPOI.OpenXmlFormats.Wordprocessing;
using NPOI.SS.Formula.Functions;
using NPOI.XSSF.UserModel;
using NPOI.SS.Format;
namespace Hyun.Core.Model
{

    ///<summary>
    ///方案意见列表
    ///</summary>
    public class XUniformSchemeOpinionDto : BaseEntity
    {

        public XUniformSchemeOpinionDto()
        {

        }

        /// <summary>
        ///校服方案Id
        /// </summary>
        public long UniformSchemeId { get; set; }

        /// <summary>
        ///选用结果（1：同意  2：不同意）
        /// </summary>
        public int Statuz { get; set; }
        /// <summary>
        ///选用结果（1：同意  2：不同意）
        /// </summary>
        [Description("选用意见")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string StatuzName { get; set; }

        /// <summary>
        ///选用意见
        /// </summary>
        [Description("意见")]
        [ExportExcel(HorizontalAlignment.Left, 30)]
        public string Opinion { get; set; }

        /// <summary>
        ///采购方式
        /// </summary>
        public int PurchaseMethod { get; set; }

        /// <summary>
        ///采购方式名称
        /// </summary>
        [Description("采购方式")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string PurchaseMethodName { get; set; }

        /// <summary>
        ///家长姓名
        /// </summary>
        [Description("家长姓名")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string ParentName { get; set; }

        /// <summary>
        ///学生姓名
        /// </summary>
        [Description("学生姓名")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string StudentName { get; set; }

        /// <summary>
        ///班级Id
        /// </summary>
        public long UniformClassId { get; set; }
        /// <summary>
        ///学生Id
        /// </summary>
        public long? StudentId { get; set; }
        /// <summary>
        /// 家长学生Id
        /// </summary>
        public long? ParentStudentId { get; set; }
        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        /// 区县Id
        /// </summary>
        public long CountyId { get; set; }
        /// <summary>
        /// 年级Id
        /// </summary>
        public int GradeId { get; set; }
        /// <summary>
        /// 班级Id
        /// </summary>
        public int ClassId { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        [Description("手机号码")]
        [ExportExcel(HorizontalAlignment.Center, 15)]
        public string Mobile { get; set; }
        /// <summary>
        /// 年级名称
        /// </summary>
        [Description("年级")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string GradeName { get; set; }
        /// <summary>
        /// 班级名称
        /// </summary>
        [Description("班级")]
        [ExportExcel(HorizontalAlignment.Center, 10)]
        public string ClassName { get; set; }
    }


}

