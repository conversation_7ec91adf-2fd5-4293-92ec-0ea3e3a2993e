﻿using Hyun.Core.Model.Model;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///WxUserBindOpenid接口方法
    ///</summary>
    public interface IWxUserBindOpenidServices : IBaseServices<WxUserBindOpenid>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<WxUserBindOpenid> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<WxUserBindOpenid>> Find(Expression<Func<WxUserBindOpenid, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">WxUserBindOpenidParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<WxUserBindOpenid>> GetPaged(WxUserBindOpenidParam param);

       //<used>0</used>
       Task<wx_QrcodeLogin> GetQrcodeLogin(string token);

       //<used>0</used>
       Task<DataTable> SearchUserBindOpenIdList(WxUserBindOpenidParam param);

       //<used>0</used>
       Task<List<WxUserBindOpenid>> Delete(List<WxUserBindOpenid> entityCollection);

       //<used>0</used>
       Task<DataTable> SearchUserBindRoleList(WxUserBindOpenidParam param);

       //<used>0</used>
       Task<Result> InsertUpdate(int userId, int unitId, string openId, DateTime regTime, int id);

    }
}

