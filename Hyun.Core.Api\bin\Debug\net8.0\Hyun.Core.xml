<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Hyun.Core.Api</name>
    </assembly>
    <members>
        <member name="T:Hyun.Old.Util.SendMessage">
            <summary>
            短信发送
            </summary>
        </member>
        <member name="M:Hyun.Old.Util.SendMessage.SendToMobile(System.String,System.String,System.String)">
            <summary>
            短信发送
            </summary>
            <param name="mobile">接收号码，多个请用半角逗号分隔</param>
            <param name="message">短信内容</param>
            <param name="clientsign"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Old.Util.SendMessage.SendToMobile(System.String,System.String,System.Boolean,System.String)">
            <summary>
            短信发送
            </summary>
            <param name="mobile">接收手机号码,发信发送的目的号码.多个号码之间用半角逗号隔开</param>
            <param name="message">信息内容</param>
            <param name="bLimit">true：根据配置信息确认发送与否；false：不依赖配置，无条件发送短信</param>
            <param name="clientsign">签名，不传默认ClientSign</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Old.Util.SendMessage.SendToMobileIzjun(System.String,System.String,System.Boolean,System.String)">
            <summary>
            掌骏短信发送平台
            </summary>
            <param name="mobile"></param>
            <param name="message"></param>
            <param name="bLimit"></param>
            <param name="clientsign"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Old.Util.SendMessage.GetUserReply">
            <summary>
            掌骏获取短信回复
            </summary>
            <returns></returns>
        </member>
        <member name="P:Hyun.Old.Util.MsgResult.flag">
            <summary>
            发送结果
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgResult.msg">
            <summary>
            结果
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgResult.message">
            <summary>
            返回信息
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgResult.returnstatus">
            <summary>
            返回状态值：成功返回Success 失败返回：Faild
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgResult.taskID">
            <summary>
            返回本次任务的序列ID
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgResult.successCounts">
            <summary>
            成功短信数：当成功后返回提交成功短信数
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgResult.remainpoint">
            <summary>
            返回余额
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgReply.flag">
            <summary>
            结果
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgReply.msg">
            <summary>
            结果
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.MsgReply.callboxs">
            <summary>
            回复内容
            </summary>
        </member>
        <member name="T:Hyun.Old.Util.Callbox">
            <summary>
            
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.Callbox.mobile">
            <summary>
            对应的手机号码
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.Callbox.taskid">
            <summary>
            同一批任务ID
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.Callbox.content">
            <summary>
            上行内容
            </summary>
        </member>
        <member name="P:Hyun.Old.Util.Callbox.receivetime">
            <summary>
            接收时间
            </summary>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.AnonController">
            <summary>
            匿名接口，非登录状态允许访问的，要严格限定访问内容
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AnonController.GetAccessId">
            <summary>
            获取Accessid值
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AnonController.WebSiteConfig_GetConfigById(System.String)">
            <summary>
            根据域名名称获取网站配置信息
            </summary>
            <param name="domainName"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AnonController.GetConfigByModule(System.String)">
            <summary>
            获取平台配置信息
            </summary>
            <param name="moduleCode"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AnonController.WebSiteConfig_DangerChemicals">
            <summary>
            获取配置信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AnonController.GetAreaPageList">
            <summary>
            获取省市区树形数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.AttachmentDataController">
            <summary>
            通用附件上传
            </summary>
            <remarks>
            2024-08-08
            </remarks>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AttachmentDataController.#ctor(AutoMapper.IMapper,Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.AspNetCore.Http.IHttpContextAccessor,System.IServiceProvider,Hyun.Core.Common.HttpContextUser.IUser,Hyun.Core.Repository.UnitOfWorks.IUnitOfWorkManage,Microsoft.Extensions.Logging.ILogger{Hyun.Core.Api.Controllers.AttachmentDataController},Hyun.Core.IServices.IBAttachmentServices,Hyun.Core.IServices.IBAttachmentDataServices,Hyun.Core.IServices.IBAttachmentConfigServices)">
            <summary>
            AttachmentDataController 的构造函数
            </summary>
            <param name="mapper">用于对象映射的IMapper实例</param>
            <param name="env"></param>
            <param name="configuration">应用程序的配置</param>
            <param name="logger">用于记录日志的ILogger实例</param>
            <param name="httpContextAccessor">用于访问当前HttpContext的IHttpContextAccessor实例</param>
            <param name="serviceProvider">用于解析服务的IServiceProvider实例</param>
            <param name="attachmentDataManager">用于管理附件数据的IBAttachmentDataServices实例</param>
            <param name="attachmentManager">用于管理附件数据的IBAttachmentServices实例</param>
            <param name="userContext">当前用户上下文</param>
            <param name="unitOfWork">用于管理数据库事务的IUnitOfWorkManage实例</param>
            <param name="ibattachmentconfigservicesManager"></param>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AttachmentDataController.Upload(System.Int32,Microsoft.AspNetCore.Http.IFormFileCollection)">
            <summary>
            上传附件
            </summary>
            <param name="fileCategory">文件分类编码</param>
            <param name="files">文件集合</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AttachmentDataController.Save(Hyun.Core.Model.BAttachmentDto)">
            <summary>
            上传附件
            </summary>
            <param name="model">文件</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.AttachmentDataController.Delete(Hyun.Core.Model.BAttachmentDto)">
            <summary>
            上传附件
            </summary>
            <param name="model">文件</param>
            <returns></returns>
        </member>
        <member name="F:Hyun.Core.Api.Controllers.Common.UploadController.env">
            <summary>
            
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Common.UploadController.#ctor(AutoMapper.IMapper,Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Microsoft.AspNetCore.Http.IHttpContextAccessor,Microsoft.Extensions.Configuration.IConfiguration,Hyun.Core.IServices.IBAttachmentDataServices,Hyun.Core.Repository.UnitOfWorks.IUnitOfWorkManage,Hyun.Core.Common.HttpContextUser.IUser)">
            <summary>
            
            </summary>
            <param name="_mapper"></param>
            <param name="_env"></param>
            <param name="_httpContextAccessor"></param>
            <param name="_configuration"></param>
            <param name="_attachmentDataManager"></param>
            <param name="_unitOfWorkManage"></param>
            <param name="_user"></param>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Common.UploadController.PostFile">
            <summary>
            上传文件通用方法
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Common.UploadController.DownloadFile(System.String,System.String)">
            <summary>
            导出Execl文件
            </summary>
            <param name="filePath">Execl虚拟路径</param> 
            <param name="fileName"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Common.UploadController.UploadExcel(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            上传
            </summary>
            <param name="fileList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Common.UploadController.UploadHead(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            上传头像
            </summary>
            <param name="fileList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.DepartmentController.GetTreeTable(System.Int64,System.String)">
            <summary>
            查询树形 Table
            </summary>
            <param name="f">父节点</param>
            <param name="key">关键字</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.DepartmentController.GetDepartmentTree(System.Int64)">
            <summary>
            获取部门树
            </summary>
            <param name="pid"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.NacosController">
            <summary>
            服务管理 
            </summary>
        </member>
        <member name="F:Hyun.Core.Api.Controllers.NacosController.NacosNamingService">
            <summary>
            INacosNamingService
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.NacosController.#ctor(Nacos.V2.INacosNamingService)">
            <summary>
            
            </summary>
            <param name="nacosNamingService"></param>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.NacosController.CheckSystemStartFinish">
            <summary>
            系统实例是否启动完成
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.NacosController.GetStatus">
            <summary>
            获取Nacos 状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.NacosController.Register">
            <summary>
            服务上线
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.NacosController.Deregister">
            <summary>
            服务下线
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.SignalRTestController">
            <summary>
            SignalR测试
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.SignalRTestController.SendMessageToUser(System.String,System.String)">
            <summary>
            向指定用户发送消息
            </summary>
            <param name="user"></param>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.SignalRTestController.SendMessageToRole(System.String,System.String)">
            <summary>
            向指定角色发送消息
            </summary>
            <param name="role"></param>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.SplitDemoController">
            <summary>
            分表demo
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.SplitDemoController.Get(System.DateTime,System.DateTime,System.Int32,System.String,System.Int32)">
            <summary>
            分页获取数据
            </summary>
            <param name="beginTime"></param>
            <param name="endTime"></param>
            <param name="page"></param>
            <param name="key"></param>
            <param name="pageSize"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.SplitDemoController.GetById(System.Int64)">
            <summary>
            根据ID获取信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.SplitDemoController.Post(Hyun.Core.Model.Models.SplitDemo)">
            <summary>
            添加一条测试数据
            </summary>
            <param name="splitDemo"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.SplitDemoController.Put(Hyun.Core.Model.Models.SplitDemo)">
            <summary>
            修改一条测试数据
            </summary>
            <param name="splitDemo"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.SplitDemoController.Delete(System.Int64)">
            <summary>
            根据id删除数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.Systems.CacheManageController">
            <summary>
            缓存管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.CacheManageController.Get">
            <summary>
            获取全部缓存
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.CacheManageController.Get(System.String)">
            <summary>
            获取缓存
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.CacheManageController.Post(System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            新增
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.CacheManageController.Delete">
            <summary>
            删除全部缓存
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.CacheManageController.Delete(System.String)">
            <summary>
            删除缓存
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.Systems.DataBaseController">
            <summary>
            数据库管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DataBaseController.GetAllConfig">
            <summary>
            获取库配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DataBaseController.GetTableInfoList(System.String,Hyun.Core.Model.Systems.DataBase.DataBaseReadType)">
            <summary>
            获取表信息
            </summary>
            <param name="configId">配置Id</param>
            <param name="readType">读取类型</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DataBaseController.GetColumnInfosByTableName(System.String,System.String,Hyun.Core.Model.Systems.DataBase.DataBaseReadType)">
            <summary>
            获取表字段
            </summary>
            <param name="tableName">表名</param>
            <param name="configId">ConfigId</param>
            <param name="readType">读取类型</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DataBaseController.PutTableEditRemark(Hyun.Core.Model.Systems.DataBase.EditTableInput)">
            <summary>
            编辑表备注
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DataBaseController.PutColumnEditRemark(Hyun.Core.Model.Systems.DataBase.EditColumnInput)">
            <summary>
            编辑列备注
            </summary>
            <param name="input"></param>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.Systems.DynamicCodeFirstController">
            <summary>
            动态建表 CURD
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DynamicCodeFirstController.GetDynamicType">
            <summary>
            动态type
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DynamicCodeFirstController.GetDynamicType2">
            <summary>
            动态type 继承BaseEntity
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DynamicCodeFirstController.TestCreateTable">
            <summary>
            测试建表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DynamicCodeFirstController.TestQuery">
            <summary>
            测试查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Systems.DynamicCodeFirstController.TestInsert(System.String,System.String)">
            <summary>
            测试写入
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.Tenant.TenantByDbController">
            <summary>
            多租户-多库方案 测试
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantByDbController.GetAll">
            <summary>
            获取租户下全部业务数据 <br/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantByDbController.Post(Hyun.Core.Model.Models.SubLibraryBusinessTable)">
            <summary>
            新增数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.Tenant.TenantByIdController">
            <summary>
            多租户-Id方案 测试
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantByIdController.GetAll">
            <summary>
            获取租户下全部业务数据 <br/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantByIdController.Post(Hyun.Core.Model.Models.BusinessTable)">
            <summary>
            新增业务数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.Tenant.TenantByTableController">
            <summary>
            多租户-多表方案 测试
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantByTableController.GetAll">
            <summary>
            获取租户下全部业务数据 <br/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantByTableController.Post(Hyun.Core.Model.Models.MultiBusinessTable)">
            <summary>
            新增数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.Controllers.Tenant.TenantManagerController">
            <summary>
            租户管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantManagerController.GetAll">
            <summary>
            获取全部租户
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantManagerController.GetInfo(System.Int64)">
            <summary>
            获取租户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantManagerController.Post(Hyun.Core.Model.Models.SysTenant)">
            <summary>
            新增租户信息 <br/>
            此处只做演示，具体要以实际业务为准
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantManagerController.Put(Hyun.Core.Model.Models.SysTenant)">
            <summary>
            修改租户信息 <br/>
            此处只做演示，具体要以实际业务为准
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Tenant.TenantManagerController.Delete(System.Int64)">
            <summary>
            删除租户 <br/>
            此处只做演示，具体要以实际业务为准
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.GetUniformBiddingList(Hyun.Core.Model.XUniformBiddingParam)">
            <summary>
             招标结果列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.UniformBiddingSave(Hyun.Core.Model.XUniformBiddingDto)">
            <summary>
            保存修改招标结果
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.UniformBiddingGetById(System.Int64)">
            <summary>
            根据Id获取招标结果数据信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.UniformBiddingSubmit(System.Int64)">
            <summary>
            提交招标结果
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.UniformBiddingDeleteById(System.Int64)">
            <summary>
            招标结果删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.UniformBiddingAudit(Hyun.Core.Model.BuyAuditModel)">
            <summary>
            招标结果审核
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.UniformBiddingRevoke(Hyun.Core.Model.BuyRevokeModel)">
            <summary>
            招标结果撤销、退回
            OptType（1：撤销 2：退回）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.DeleteAttachmentById(System.Int64,System.Int64)">
            <summary>
            删除附件
            </summary>
            <param name="id"></param>
            <param name="attid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.UniformBiddingController.GetAreaName(System.Int64,System.String,System.Collections.Generic.List{Hyun.Core.Model.Models.BArea})">
            <summary>
            获取区域名称
            </summary>
            <param name="countyAreaId">区县区域Id</param>
            <param name="name">区县单位名称，默认值</param>
            <param name="listArea">区域集合</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.GetUniformBuyList(Hyun.Core.Model.XUniformBuyParam)">
            <summary>
             采购申请列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.UniformBuySave(Hyun.Core.Model.XUniformBuyDto)">
            <summary>
            保存修改采购申请
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.UniformBuyGetById(System.Int64)">
            <summary>
            根据Id获取采购申请数据信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.UniformBuySubmit(System.Int64)">
            <summary>
            提交采购申请
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.UniformBuyDeleteById(System.Int64)">
            <summary>
            采购申请删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.UniformBuyAudit(Hyun.Core.Model.BuyAuditModel)">
            <summary>
            采购申请审核
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.UniformBuyRevoke(Hyun.Core.Model.BuyRevokeModel)">
            <summary>
            采购申请撤销、退回
            OptType（1：撤销 2：退回）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Uniform.XUniformBuyController.DeleteAttachmentById(System.Int64,System.Int64)">
            <summary>
            删除附件
            </summary>
            <param name="id"></param>
            <param name="attid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindModuleList(Hyun.Core.Model.WfModuleParam)">
            <summary>
            查询模块列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ModuleInsertUpdate(Hyun.Core.Model.WfModuleDto)">
            <summary>
            新增修改模块信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ModuleFindById(System.Int64)">
            <summary>
            根据Id获取模块信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ModuleSetStatuz(System.Int64)">
            <summary>
            启用/禁用模块信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindProcessList(Hyun.Core.Model.WfProcessParam)">
            <summary>
            流程管理：获取流程列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessInsertUpdate(Hyun.Core.Model.WfProcessDto)">
            <summary>
            流程管理：保存流程信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessFindById(System.Int64)">
            <summary>
            流程管理：根据Id获取流程信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessSetStatuz(System.Int64)">
            <summary>
            流程管理：启用/禁用流程
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessDeleteById(System.Int64)">
            <summary>
            流程管理：删除流程信息（根据id）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessSetById(System.Int64)">
            <summary>
            流程管理：根据Id设置流程信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessNodeLinkById(System.Int64)">
            <summary>
            流程管理：根据Id获取流程节点关系信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessSetSave(Hyun.Core.Model.WfProcessSetConfigModel)">
            <summary>
            流程管理：提交流程信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessLinkDeleteById(System.Int64)">
            <summary>
            流程管理：根据Id删除节点连线
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessNodeDeleteById(System.Int64)">
            <summary>
            流程管理：根据Id删除节点
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindNodeList(Hyun.Core.Model.WfProcessNodeParam)">
            <summary>
            流程管理：获取节点列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.NodeInsertUpdate(Hyun.Core.Model.WfProcessNodeDto)">
            <summary>
            流程管理：保存节点信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.NodeFindById(System.Int64)">
            <summary>
            流程管理：根据Id获取节点详情信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.NodeJsonFindById(System.Int64)">
            <summary>
            流程管理：根据Id获取节点Json数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.NodeDeleteById(System.Int64)">
            <summary>
            流程管理：删除节点信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.NodeConfigSave(Hyun.Core.Model.WfNodeConfigModel)">
            <summary>
            流程管理：保存节点Json数据
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetNextProcessNode(System.Int64,System.Int64)">
            <summary>
            流程管理：根据模块Id和流程节点Id获取该模块下所有节点数据
            创建时流程节点Id传0，修改时节点Id传具体Id值
            </summary>
            <param name="moduleId">模块Id</param>
            <param name="processNodeId">流程节点Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindProcessStatuzList(Hyun.Core.Model.WfProcessStatuzParam)">
            <summary>
            流程管理：获取流程状态列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProcessStatuzEdit(Hyun.Core.Model.WfProcessStatuzDto)">
            <summary>
            流程管理：修改状态值
            只能修改排序值和状态描述
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindFieldList(Hyun.Core.Model.WfProcessFieldParam)">
            <summary>
            流程管理：字段配置列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FieldInsertUpdate(Hyun.Core.Model.WfProcessFieldDto)">
            <summary>
            流程管理：保存字段配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FieldFindById(System.Int64)">
            <summary>
            流程管理：获取字段配置详情信息（根据id）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FieldSetStatuz(System.Int64)">
            <summary>
            流程管理：设置字段状态（启用、禁用）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FieldDeleteById(System.Int64)">
            <summary>
            流程管理：删除字段配置信息（根据id）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindDictionaryList(Hyun.Core.Model.WfDictionaryParam)">
            <summary>
            流程管理：获取审批字典列表（wfdictionary）
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.DictionaryFindById(System.Int64)">
            <summary>
            流程管理：根据id获取字典信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.DictionaryInsertUpdate(Hyun.Core.Model.WfDictionaryModel)">
            <summary>
            流程管理：新增修改字典信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.DictionarySetStatuz(System.Int64)">
            <summary>
            流程管理：设置流程字典状态（启用、禁用）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetGenerateCodeSet(System.Int64,System.String)">
            <summary>
            流程管理：根据节点Id及编码获取编码配置信息
            </summary>
            <param name="processNodeId"></param>
            <param name="fieldCode"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostSaveGenerateCodeSet(Hyun.Core.Model.WfCodeGenerateSetDto)">
            <summary>
            流程管理：保存编码配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostLinkAgeHostList(Hyun.Core.Model.WfLinkAgeHostParam)">
            <summary>
            流程管理：获取联动数据配置列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetLinkAgeHostDetailList(System.Int64)">
            <summary>
            流程管理：根据审批联动主表Id获取联动父子联动数据及关联关系
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetDetailSetList(System.Int64)">
            <summary>
            流程管理：根据联动主表Id获取配置列表信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetDetaiByParentId(System.Int64,System.Int64)">
            <summary>
            流程管理：根据联动主表Id父级Id获取选中子集数据信息
            </summary>
            <param name="id">点配置带过来的Id</param>
            <param name="parentId">选中项的Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostSaveLinkAgeDetail(Hyun.Core.Model.LinkAgeDetailModel)">
            <summary>
            流程管理：保存联动关系数据信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindProjectListFieldSetList(Hyun.Core.Model.WfProjectListFieldSetParam)">
            <summary>
            流程审批：查询审批预算清单配置列表（后台）
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProjectListFieldSetById(System.Int64)">
            <summary>
            流程审批：根据Id查询审批预算清单配置数据（后台）
            </summary>
            <param name="id">预算清单配置Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ProjectListFieldSetSave(Hyun.Core.Model.WfProjectListFieldSetDto)">
            <summary>
            流程审批：保存审批预算清单配置（后台）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetFieldCodeListByModuleId(System.Int64)">
            <summary>
            流程审批：根据模块Id获取项目清单Code集合
            </summary>
            <param name="moduleId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetDataSource">
            <summary>
            流程审批：获取字典数据源信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetDataSourceByDicValue(System.String)">
            <summary>
            流程审批：根据类型编码获取字典值数据信息
            </summary>
            <param name="code">编码</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.LoadDataSource(Hyun.Core.Model.DropListParam)">
            <summary>
            根据参数查询页面显示数据
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindSourceFundSetList(Hyun.Core.Model.WfSourceFundSetParam)">
            <summary>
            流程审批：查询资金来源配置列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.SourceFundById(System.Int64)">
            <summary>
            流程审批：根据Id查询资金来源配置信息
            </summary>
            <param name="id">资金来源配置Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.SourceFundSetSave(Hyun.Core.Model.WfSourceFundSetDto)">
            <summary>
            流程审批：保存资金来源配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FieldSelectList(Hyun.Core.Model.WfProcessFieldParam)">
            <summary>
            流程审批：获取字段选择列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindSourceFundList(Hyun.Core.Model.WfSourceFundParam)">
            <summary>
            流程审批：获取资金来源显示列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindPageDefinitionList(Hyun.Core.Model.WfPageDefinitionParam)">
            <summary>
            流程管理：获取查询统计配置列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetPageDefinitionById(System.Int64)">
            <summary>
            流程管理：根据Id查询统计页面表配置信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.DelPageDefinitionById(System.Int64)">
            <summary>
            流程管理：根据Id删除查询统计配置
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PageDefinitionSave(Hyun.Core.Model.WfPageDefinitionDto)">
            <summary>
            流程管理：保存查询统计页面
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PageDefinitionStatuzSet(System.Int64,System.Int32)">
            <summary>
            流程管理：禁用启用及提交处理
            </summary>
            <param name="id">查询统计页面表Id</param>
            <param name="operateType">1：处理禁用启用,2：处理提交</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindPageColumnConfigList(Hyun.Core.Model.WfPageColumnConfigParam)">
            <summary>
            流程管理节点列表设置：获取显示列列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindChooseColumnPageList(Hyun.Core.Model.WfProcessFieldParam)">
            <summary>
            流程管理节点列表设置：获取选择列列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ChooseColumnSave(Hyun.Core.Model.ChoseColumnModel)">
            <summary>
            流程管理节点列表设置：选择列确认
            </summary>
            <param name="choseColumn">
            Id必传
            ModuleId必传
            ModeType必传：查询统计的传1；节点列设置传2
            MenuType必传：查询统计可以不传，用于节点，待处理传1，已处理传2
            FieldCode必传
            PageDefinitionId(查询统计必传)：节点列设置不需要传
            ProcessNodeId(节点列必传)：查询统计列设置不需要传
            ConfigType(查询统计必传)
            ListFieldType：（1：列  2：查询）
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ListColumnSet(System.Int64)">
            <summary>
            流程管理节点列表设置：列启用禁用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ListColumnGetById(System.Int64)">
            <summary>
            流程管理节点列表设置：根据Id获取列信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ListColumnInsertUpdate(Hyun.Core.Model.WfPageColumnConfigDto)">
            <summary>
            流程管理节点列表设置：新增修改列数据信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ListColumnBatchUpdate(System.Collections.Generic.List{Hyun.Core.Model.WfPageColumnConfigDto})">
            <summary>
            流程管理节点列表设置：批量修改列数据信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetPageColumnConfigById(System.Int64)">
            <summary>
            流程管理：根据页面列配置表Id获取信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PageColumnConfigSave(Hyun.Core.Model.WfPageColumnConfigDto)">
            <summary>
            流程管理：新增修改查询统计页面
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.SetPageColumnConfigById(System.Int64)">
            <summary>
            流程管理：根据Id设置是否需要
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PageColumnConfigSync(Hyun.Core.Model.WfSyncDataModel)">
            <summary>
             流程管理：同步字段配置设置值
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindChooseFundPageList(Hyun.Core.Model.WfFundFieldSetParam)">
            <summary>
            流程管理：项目库字段管理选择列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ChooseFundSave(Hyun.Core.Model.ChoseColumnModel)">
            <summary>
            流程管理：项目库字段管理字段选择确认
            </summary>
            <param name="choseColumn"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.SetSourceFundField(System.Int64,System.Int64)">
            <summary>
            流程管理：项目库字段绑定
            </summary>
            <param name="pageColumnConfigId"></param>
            <param name="processFieldId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.SourceFundInputDataSet(Hyun.Core.Model.SourceFundInputModel)">
            <summary>
            流程管理：修改流程资金来源信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.FindProjectColumnPageList(Hyun.Core.Model.WfProcessFieldParam)">
            <summary>
            流程管理：根据查询统计页面表Id获取审批预算清单选择的列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.ChooseProjectListColumnSave(Hyun.Core.Model.ChoseColumnModel)">
            <summary>
            流程管理：项目清单查询统计选择清单列功能
            传参：ModuleId 必传,PageDefinitionId 必传,ListColumn:[{Id:11,FieldCode:"aa"},{Id:22,FieldCode:"ff"}]
            </summary>
            <param name="choseColumn"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetProcessReturnSet(System.Int64,System.Int64)">
            <summary>
            流程管理：点击节点显示退回方式数据
            </summary>
            <param name="processId"></param>
            <param name="processNodeId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.SetProcessReturn(Hyun.Core.Model.WfProcessReturnSetDto)">
            <summary>
            流程管理：保存退回方式
            </summary>
            <param name="o">
            数据类型【{Id:13121212121,BackWay:3,Sort:5}】
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostControlList(Hyun.Core.Model.WfControlDetailParam)">
            <summary>
            流程管理：获取主控制列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostControlSetList(Hyun.Core.Model.WfControlDetailParam)">
            <summary>
            流程管理：根据节点Id主控分组值获取设置控制列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetSingleControlSetList(Hyun.Core.Model.SingleControlModel)">
            <summary>
            流程管理：根据左侧点击获取右侧字段选中信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostSaveControlData(Hyun.Core.Model.ControlDetailModel)">
            <summary>
            流程管理：保存字段配置信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.DelAmountControlById(System.Int64)">
            <summary>
            流程管理：根据金额控制主表Id删除数据信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetGroupSetList(Hyun.Core.Model.WfGroupProcessSetParam)">
            <summary>
            流程管理：分组查询列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GroupSetInsertUpdate(Hyun.Core.Model.WfGroupProcessSetModel)">
            <summary>
            流程管理：添加修改分组信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GroupSetDeleteById(System.Int64)">
            <summary>
            流程管理：根据Id删除分组信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetUnitGroupSetList(Hyun.Core.Model.WfGroupUnitSetParam)">
            <summary>
            流程管理：单位分组设置列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetGroupItemList(System.Int64,System.Int64,System.Int32)">
            <summary>
            流程管理：根据分组Id获取分组中每项的信息
            </summary>
            <param name="processId">流程Id</param>
            <param name="groupId">分类编码</param>
            <param name="typeBox">输入框类型（2:基础字典表，3：审批字典表）</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.UnitGroupBatchSet(Hyun.Core.Model.UnitGroupBatchModel)">
            <summary>
            流程管理：批量保存单位分组信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GroupItemCheck">
            <summary>
            流程管理：检测是否填写完成
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetUnitPermissionInfo(Hyun.Core.Model.WfChildUnitDisableParam)">
            <summary>
            流程管理：获取单位控制数据信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.SetUnitPermission(Hyun.Core.Model.SetPermissionModel)">
            <summary>
            流程管理：配置单位控制数据
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostMsgConfigList(Hyun.Core.Model.BMsgConfigParam)">
            <summary>
            标准消息配置：消息配置列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetMsgConfigById(System.Int64)">
            <summary>
            标准消息配置：根据Id获取消息配置信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostMsgConfigInsertUpdate(Hyun.Core.Model.BMsgConfigDto)">
            <summary>
            标准消息配置：新增修改消息配置
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostMsgConfigSetSatuz(Hyun.Core.Model.MsgConfigStatuzModel)">
            <summary>
            标准消息配置：批量开启关闭
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostProcessMsgConfigList(Hyun.Core.Model.WfMsgConfigParam)">
            <summary>
            审批消息配置：消息配置列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostProcessMsgConfigAdd(Hyun.Core.Model.WfProcessMsgConfigModel)">
            <summary>
            审批消息配置：添加配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostProcessMsgConfigEdit(Hyun.Core.Model.WfMsgConfigDto)">
            <summary>
            审批消息配置：修改配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.GetProcessMsgConfigById(System.Int64)">
            <summary>
            审批消息配置：根据Id获取信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.PostProcessMsgConfigSetSatuz(Hyun.Core.Model.MsgConfigStatuzModel)">
            <summary>
            标准消息配置：批量开启关闭
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.BuildDictionaryTree(System.Collections.Generic.List{Hyun.Core.Model.Models.WfDictionary})">
            <summary>
            
            </summary>
            <param name="flatList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ConfigurationController.BuildDropDownModelTree(System.Collections.Generic.List{Hyun.Core.Model.dropdownModel})">
            <summary>
            
            </summary>
            <param name="flatList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetProcessAuditUserList">
            <summary>
            流程管理：权限设置列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetSetUserList(System.Int64,System.Int64)">
            <summary>
            流程管理：获取设置的审核审批人数据信息
            </summary>
            <param name="processId"></param>
            <param name="processNodeId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.SetProcessNodeAuditUser(Hyun.Core.Model.UserListModel)">
            <summary>
            流程管理：设置节点审核审批人
            </summary>
            <param name="userModel"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetFillingInfo(Hyun.Core.Model.DropListParam)">
            <summary>
            流程审批：获取填报页面信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetLinkAgeDataSource(System.String)">
            <summary>
            流程审批：根据父级编码获取子级数据信息
            </summary>
            <param name="code">格式：节点Id_输入框类型_父级Code编码_分类编码_父级选择值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetProjectCode(System.Int64)">
            <summary>
            流程审批：根据流程节点Id获取项目编号
            </summary>
            <param name="projectNodeId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetProjectDeclarationCodeName(System.Int64,System.Int64)">
            <summary>
            流程审批：根据一级二级分类Id获取项目名称及编码
            </summary>
            <param name="oneClassId">一级分类Id</param>
            <param name="twoClassId">二级分类Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetControlDetailDataSource(Hyun.Core.Model.ReceptionControlMode)">
            <summary>
            流程审批：根据编码获取被控制字段显示信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.SaveFillingInfo(Hyun.Core.Model.FillingModel)">
            <summary>
            流程审批：填报页面保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.SubmitFillingInfo(Hyun.Core.Model.FillingModel)">
            <summary>
            流程审批：填报页面提交
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.DelFillingInfo(System.Int64)">
            <summary>
            流程审批：删除填报数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.FillingInfoDetail(System.Int64)">
            <summary>
            流程审批：根据填报Id获取详情信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetListSumByProjectDeclarationId(System.Int64)">
            <summary>
            流程审批：根据填报Id获取清单总金额
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetFillSearchList(Hyun.Core.Model.WfPageDefinitionParam)">
            <summary>
            流程审批：获取待处理列表数据
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetProcessedList(Hyun.Core.Model.WfPageDefinitionParam)">
            <summary>
            流程审批：获取已处理列表数据
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.FillingInfoAudit(System.Int64,System.Int64)">
            <summary>
            流程审批：审核审批页面信息
            </summary>
            <param name="id">填报Id</param>
            <param name="processNodeId">流程节点Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.SaveApproval(Hyun.Core.Model.FillingModel)">
            <summary>
            流程审批：暂存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.SubmitApproval(Hyun.Core.Model.FillingModel)">
            <summary>
             流程审批：审核审批
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.OperateRevoke(System.Int64,System.Int64,System.Int64)">
            <summary>
            流程审批：操作撤销
            </summary>
            <param name="projectAuditId">审核表Id</param>
            <param name="projectDeclarationId">填报表Id</param>
            <param name="processNodeId">流程节点Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.FindProjectListFieldSetList(Hyun.Core.Model.WfProjectListParam)">
            <summary>
            流程审批：查询审批预算清单表数据
            </summary>
            <param name="param">
            ProjectDeclarationId:立项填报Id必传
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.ProjectListSave(Hyun.Core.Model.WfProjectListDto)">
            <summary>
            流程审批：保存预算清单信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.ProjectListById(System.Int64)">
            <summary>
            流程审批：根据Id查询预算清单信息
            </summary>
            <param name="id">预算清单Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.ProjectListDeleteById(System.Int64)">
            <summary>
            流程审批：根据Id删除预算清单信息
            </summary>
            <param name="id">预算清单Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.ProjectListDeleteByIds(System.String)">
            <summary>
            流程审批：根据Id集合删除预算清单信息
            </summary>
            <param name="ids">Id集合，逗号分割</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.DownLoadProjectList(Hyun.Core.Model.WfProjectListParam)">
            <summary>
            流程审批：下载项目清单Execl
            </summary>
            <param name="param">
            ProcessId：必传
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.ProjectListImport(Hyun.Core.Model.SearchModels.Common.ProjectListImportParam)">
            <summary>
            流程审批：项目清单导入
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.FindAuditProjectList(Hyun.Core.Model.WfProjectListReviewParam)">
            <summary>
            流程审批：项目清单审核列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.SaveProjectListReview(Hyun.Core.Model.AuditModel)">
            <summary>
            流程审批：项目清单保存、删除、审核处理
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.FindHistoryProjectList(Hyun.Core.Model.WfProjectListHistoryParam)">
            <summary>
            流程审批：获取项目清单历史审批记录数据
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.FindPageDefinitionList(System.Int64)">
            <summary>
            流程审批：查询统计列表页面
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetSearchList(Hyun.Core.Model.WfPageDefinitionParam)">
            <summary>
            流程审批：动态生成查询列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.DownLoadSearchList(Hyun.Core.Model.WfPageDefinitionParam)">
            <summary>
            流程审批：查询统计导出Execl
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.FindSourceFundList(Hyun.Core.Model.WfSourceFundParam)">
            <summary>
            流程审批：资金来源显示列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetGroupItemUserList(Hyun.Core.Model.WfGroupUnitSetParam)">
            <summary>
            流程管理：分级授权列表
            </summary>
            <param name="param">
             ProcessId:流程Id
             ProcessNodeId：流程节点Id
             GroupId:对应UseGroupValue值,待处理列表启用分组筛选值
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetWaitAuditUserList(System.Int64,System.Int64)">
            <summary>
            流程管理：获取待授权用户列表信息
            </summary>
            <param name="processId">流程Id</param>
            <param name="processNodeId">流程节点Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.SetGroupUnitAuditUser(Hyun.Core.Model.UserListModel)">
            <summary>
            流程管理：设置分组项对应人员信息
            </summary>
            <param name="userModel"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetProcessIndexInfo">
            <summary>
            流程审批：根据当前用户获取流程审批数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.BuildDropDownModelTree(System.Collections.Generic.List{Hyun.Core.Model.dropdownModel})">
            <summary>
            获取树数据
            </summary>
            <param name="flatList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.CreateExportMemoryStream(System.String,System.String,System.Collections.Generic.List{Hyun.Core.Model.Models.WfProjectListFieldSet})">
            <summary>
            生成Execl数据
            </summary>
            <param name="sheetName">execl底部名称</param>
            <param name="headText">标题文字内容</param>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.CreateQueryMemoryStream(System.String,Hyun.Core.Model.PageModel{System.Object},System.Collections.Generic.List{Hyun.Core.Model.Models.WfPageColumnConfig},System.String)">
            <summary>
            动态导出查询统计数据
            </summary>
            <param name="sheetName"></param>
            <param name="pg"></param>
            <param name="listColumn"></param>
            <param name="totalShowName"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.GetPropertyValue(System.Object,System.String)">
            <summary>
            读取动态对象
            </summary>
            <param name="item"></param>
            <param name="propertyName"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.Controllers.Workflow.ProcessController.ProcessMsgSend(System.Int64,System.Int64,System.Int64,System.String)">
            <summary>
            审核审批消息发送
            </summary>
            <param name="processId">流程Id</param>
            <param name="processNodeId">流程节点Id</param>
            <param name="projectDeclarationId">填报Id</param>
            <param name="msgCode">消息编号</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.Address_Find(Hyun.Core.Model.VAddressListParam)">
            <summary>
            获取地址信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.Address_GetById(System.Int64)">
            <summary>
            根据地址Id获取地址信息
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.Address_InsertUpdate(Hyun.Core.Model.BAddressDto)">
            <summary>
            添加修改地址信息(单位)
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.Address_DelBatch(System.String)">
            <summary>
            删除地址信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.AddressDepartment_UserBatchSet(System.String,System.Int64)">
            <summary>
            批量设置部门信息
            </summary>
            <param name="strAddress"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.AddressUser_BatchSet(System.String,System.Int64)">
            <summary>
            批量设置地址管理人
            </summary>
            <param name="strAddress"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.UploadAddressFile(Hyun.Core.Model.SearchModels.Common.ImportParam)">
            <summary>
            地址管理：地址导入
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.AddressExport(Hyun.Core.Model.VAddressListParam)">
            <summary>
            地址管理：地址导出（本单位）
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAddressController.BuildAddressTree(System.Collections.Generic.List{Hyun.Core.Model.Models.BAddress})">
            <summary>
            地址树信息
            </summary>
            <param name="flatList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAreaController.Area_Find">
            <summary>
            根据当前单位Id，获取下属单位信息，如果为超管则获取市、区
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAreaController.GetAreaName_Find">
            <summary>
            获取区县名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAreaController.Area_GetById(System.Int64)">
            <summary>
            根据区域Id查询区域完整名称
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAreaController.Area_GetByPid(System.Int64)">
            <summary>
            根据区域父级Id查询区域信息
            </summary>
            <param name="pid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAreaController.GetAreaByUnitId(System.Int64)">
            <summary>
            根据单位Id获取单位所属区域信息
            </summary>
            <param name="UnitId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAreaController.BuildDropDownModelTree(System.Collections.Generic.List{Hyun.Core.Model.dropdownModel})">
            <summary>
            获取树数据
            </summary>
            <param name="flatList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.BConfigSet_Find(Hyun.Core.Model.VConfigSetParam)">
            <summary>
            获取配置信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.BConfigSet_GetById(System.Int64)">
            <summary>
            根据Id获取配置信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.BConfigSet_InsertUpdate(Hyun.Core.Model.BConfigSetDto)">
            <summary>
            新增修改参数配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.BConfigSet_Get(System.String,System.String)">
            <summary>
            根据TypeCode获取配置信息
            </summary>
            <param name="moduleCode"></param>
            <param name="typeCode"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.BConfigSet_GetPUnit(System.String,System.String)">
            <summary>
            根据TypeCode获取上级单位配置信息
            </summary>
            <param name="moduleCode"></param>
            <param name="typeCode"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessConfig_Find(Hyun.Core.Model.BModuleProcessConfigParam)">
            <summary>
            获取首页待处理列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessConfig_Save(Hyun.Core.Model.Models.BModuleProcessConfig)">
            <summary>
            新增修改首页待处理事项
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessConfigNext(System.Int64)">
            <summary>
            获取下一个模块
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessConfig_DeleteById(System.Int64)">
            <summary>
            根据Id删除模块流程配置信息
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessConfig_SetStatuz(System.Int64)">
            <summary>
            模块流程配置设置状态
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessConfig_GetById(System.Int64)">
            <summary>
            根据Id获取模块流程配置信息
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessStatuzConfig_Find(Hyun.Core.Model.BModuleProcessStatuzConfigParam)">
            <summary>
            获取模块流程各状态值列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessStatuzConfig_Save(Hyun.Core.Model.BModuleProcessStatuzConfigDto)">
            <summary>
            保存模块流程状态值信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessStatuzConfig_DeleteById(System.Int64)">
            <summary>
            根据Id删除模块状态值配置信息
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessStatuzConfig_BatchDelete(System.String)">
            <summary>
            批量删除模块状态值信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessStatuzConfig_GetById(System.Int64)">
            <summary>
            根据Id获取模块状态值信息
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessStatuzConfig_SetStatuz(System.Int64)">
            <summary>
            根据Id设置状态
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.ModuleProcessData_Find">
            <summary>
            审核审批配置：获取流程模块列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BConfigSetController.Random(System.Int32,System.Boolean)">
            <summary>
            生成随机码
            </summary>
            <param name="length"></param>
            <param name="isHas"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCodeExtension_Find(Hyun.Core.Model.VDeviceCodeExtensionParam)">
            <summary>
            设备管理：获取设备列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCodeExtensionVersion_Find">
            <summary>
            设备管理：获取设备类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCodeClass_Find">
            <summary>
            设备管理：获取设备一级分类集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCode_Find(System.Int64)">
            <summary>
            设备管理：获取设备集合（根据父级Id获取子集集合）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCodeExtesion_InsertUpdate(Hyun.Core.Api.Models.DeviceCodeExtesionModel)">
            <summary>
            设备管理：保存设备信息（修改、添加）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCodeExtesion_GetById(System.Int64)">
            <summary>
            设备管理：获取设备详情信息（根据id）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCodeExtesion_Save(Hyun.Core.Model.Models.BDeviceCodeExtension)">
            <summary>
            设备管理：保存设备信息（修改：名称、备注、排序）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDeviceCodeExtensionController.DeviceCodeExtesiom_Delete(System.Int64)">
            <summary>
            设备管理：删除设备信息（根据id）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.#ctor(AutoMapper.IMapper,Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Hyun.Core.IServices.IBDictionaryServices,Hyun.Core.IServices.IVAreaDictionaryServices,Hyun.Core.IServices.IVSysStatServices,Hyun.Core.IServices.IBAreaDictionaryServices,Hyun.Core.IServices.IPUnitServices,Hyun.Core.IServices.IVXaDictionaryUnitServices,Hyun.Core.IServices.IBAttachmentDataServices,Microsoft.Extensions.Configuration.IConfiguration,Hyun.Core.Common.HttpContextUser.IUser)">
            <summary>
            
            </summary>
            <param name="_mapper"></param>
            <param name="_env"></param>
            <param name="_dictionaryManager"></param>
            <param name="_projectConfigDetailManager"></param>
            <param name="_projectManager"></param>
            <param name="_projectConfigNodeManager"></param>
            <param name="_projectNodeDefinitionManager"></param>
            <param name="_vAreaDictionaryManager"></param>
            <param name="_vSysStatManager"></param>
            <param name="_areaDictionaryManager"></param>
            <param name="_unitManager"></param>
            <param name="_vXaDictionaryUnitManager"></param>
            <param name="_attachmentDataManager"></param>
            <param name="_configuration"></param>
            <param name="_assetsEquipmentListManager"></param>
            <param name="_user"></param>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.Dictionary_Find(Hyun.Core.Model.BDictionaryParam)">
            <summary>
            获取字典列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.Dictionary_GetById(System.Int64)">
            <summary>
            根据Id查询字典信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.Dictionary_InsertUpdate(Hyun.Core.Model.Models.BDictionary)">
            <summary>
            新增修改字典值信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.Dictionary_SetStatuz(System.Int64)">
            <summary>
            字典值启用禁用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.AreaDictionary_Find(Hyun.Core.Model.VAreaDictionaryParam)">
            <summary>
            获取基础数据列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.GetTypeName_Combox">
            <summary>
            获取基础数据下拉框数据信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.AreaDictionary_Save(Hyun.Core.Model.Models.BAreaDictionary)">
            <summary>
            保存基础信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.AreaDictionary_GetById(System.Int64)">
            <summary>
            根据Id查询基础信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.GetDictionary_Combox(System.String)">
            <summary>
            根据字典编号获取字段信息
            </summary>
            <param name="typeCode"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BDictionaryController.DownLoadExeclFile(Hyun.Core.Model.BDictionaryParam)">
            <summary>
            下载Execl
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintConfig_Find(Hyun.Core.Model.BPrintConfigParam)">
            <summary>
            获取打印配置列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintConfig_Update(Hyun.Core.Model.BPrintConfigDto)">
            <summary>
            新增修改打印配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintSetting_Find(Hyun.Core.Model.VPrintSettingListParam)">
            <summary>
            获取打印配置信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintSetting_Add(Hyun.Core.Model.Models.BPrintSetting)">
            <summary>
            设置打印关联表信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintSetting_Delete(System.Int64)">
            <summary>
            删除打印关联表信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintPosition_Find(Hyun.Core.Model.BPrintPositionParam)">
            <summary>
            获取打印位置信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintPage_Find(Hyun.Core.Model.VPrintPageListParam)">
            <summary>
            获取打印页面信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintPosition_InsertUpdate(Hyun.Core.Model.BPrintPositionDto)">
            <summary>
            新增修改打印位置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BPrintConfigController.PrintPage_InsertUpdate(Hyun.Core.Model.BPrintPageDto)">
            <summary>
            新增修改打印页面信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BWebSiteConfigController.WebSiteConfig_Find(Hyun.Core.Model.BWebSiteConfigParam)">
            <summary>
            获取网站配置列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BWebSiteConfigController.WebSiteConfig_Save(Hyun.Core.Model.BWebSiteConfigDto)">
            <summary>
            新增修改网站配置信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BWebSiteConfigController.WebSiteConfig_GetById(System.Int64)">
            <summary>
            根据Id获取网站配置信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_FindPerson(Hyun.Core.Model.DArticleParam)">
            <summary>
            获取资讯列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_GetById(System.Int64)">
            <summary>
            根据id获取获取资讯详细信息 (查看专用) 
            </summary>
            <param name="Id">资讯编号</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_GetPersonById(System.Int64)">
            <summary>
            获取资讯以及发布人的信息
            </summary>
            <param name="Id">资讯id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.getPublishInfo(Hyun.Core.Model.Result,Hyun.Core.Model.Models.DArticle)">
            <summary>
            获取发布人信息
            </summary>
            <param name="r"></param>
            <param name="p"></param>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_Recommend(Hyun.Core.Model.DArticleDto)">
            <summary>
            资讯推荐,取消推荐 
            </summary>
            <param name="model">资讯实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_DelBatch(System.String)">
            <summary>
            批量删除资讯
            </summary>
            <param name="ids">资讯Id，逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.ArticleImage_GetByArticleId(System.Int64)">
            <summary>
            获取资讯图片
            </summary>
            <param name="articleId">资讯id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.ArticleExtend_GetByArticleId(System.Int64)">
            <summary>
            获取资讯扩展属性
            </summary>
            <param name="articleId">资讯id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.DArticleCategoryList_Find(Hyun.Core.Model.VArticleCategoryParam)">
            <summary>
            查看资讯分类列表
            </summary>
            <param name="param">资讯分类查询实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.DArticleCategoryList_GetById(System.Int64)">
            <summary>
            获取资讯分类实体对象
            </summary>
            <param name="id">分类Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.DArticleCategoryList_InsertUpdate(Hyun.Core.Model.DArticleCategoryDto)">
            <summary>
            资讯分类 添加/修改
            </summary>
            <param name="model">分类对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.DArticleCategoryList_Delete(Hyun.Core.Model.DArticleCategoryDto)">
            <summary>
            资讯分类删除
            </summary>
            <param name="model">资讯分类实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_CategoryGetAllList(System.Int32)">
            <summary>
            获取资讯分类
            </summary>
            <param name="type">type:4 企业</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_FindManage(Hyun.Core.Model.VArticleParam)">
            <summary>
            获取待管理资讯列表
            </summary>
            <param name="param">查询实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_InsertUpdate2_0(Hyun.Core.Model.DArticleDto)">
            <summary>
             资讯发布(状态:0:保存；1：区县审核通过；2：系统审核通过；3：区县审核不通过；4：系统审核不通过)       
            </summary>
            <param name="model">资讯对象</param>
            <users>
            系统超管,校管理员
            </users>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_Save2_0(Hyun.Core.Model.DArticleDto)">
            <summary>
            资讯保存(状态:0:保存；)
            </summary>
            <param name="model">资讯对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Company_SubmitArticle(Hyun.Core.Model.Models.DArticle)">
            <summary>
            企业资讯发布(状态:0:保存；1：区县审核通过；2：系统审核通过；3：区县审核不通过；4：系统审核不通过)
            </summary>
            <param name="o">资讯对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_Audit2_0(Hyun.Core.Model.Models.DArticle)">
            <summary>
             资讯审核（管理员审核，可以进行修改）  
            </summary>  
            <users>
            系统超管
            </users>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_GetFullById(System.Int64,System.Boolean)">
            <summary>
            根据id获取获取资讯详细信息 (查看专用)
            </summary>
            <param name="Id"></param>
            <param name="isPreveiw"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.GetDArticleCategoryList(System.Int64)">
            <summary>
            资讯分类查询
            </summary>
            <param name="pid">父级Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.VCatalogArticle_Find(Hyun.Core.Model.VCategoryArticleListParam)">
            <summary>
            获取首页资讯列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.Article_AttachmentDown(System.String,System.Int64)">
            <summary>
            资讯管理：资讯附件下载
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DArticleController.IsReadLogin">
            <summary>
            判断用户是否弹出版本更新信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ECityCodeController.ECityCode_Find(Hyun.Core.Model.VCityCodeParam)">
            <summary>
            单位管理：获取市级编码列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ECityCodeController.ECityCode_GetById(System.Int64)">
            <summary>
            单位管理：获取市级编码详情信息（根据id）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PAccountController.Account_Change(System.Int64)">
            <summary>
            切换用户
            </summary>
            <param name="uid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PGropAuthorController.GropAuthorListFind(Hyun.Core.Model.VGropAuthorParam)">
            <summary>
            获取业务授权列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PGropAuthorController.GropAuthorSelectUser_Find(System.Int32,Hyun.Core.Model.SysUserExtensionParam)">
            <summary>
            获取选择用户信息
            </summary>
            <param name="GropAuthorId"></param>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PGropAuthorController.GropAuthorUserList_Find(Hyun.Core.Model.VGropAuthorUserParam)">
            <summary>
            获取用户所属系部列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PRoleController.Role_GetById(System.Int64)">
            <summary>
            根据id获取角色信息
            </summary>
            <param name="id">用户Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PRoleController.Role_InsertUpdate(Hyun.Core.Model.SysRoleDto)">
            <summary>
            角色增加/修改
            </summary>
            <param name="model">角色对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PRoleController.Role_DelBatch(System.String)">
            <summary>
            角色删除
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PRoleController.Role_GetPlatformRoleList(System.Int64)">
            <summary>
             根据用户Id获取设置角色列表信息
            </summary>
            <param name="userId">用户Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PRoleController.Role_GetSuperPlatformRoleList(System.Int64)">
            <summary>
            超管根据用户Id获取角色信息
            </summary>
            <param name="userId">用户Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PRoleController.Role_EditModule(Hyun.Core.Model.SysRoleDto)">
            <summary>
            修改角色分类
            </summary>
            <param name="model"></param>
            <returns></returns>
            int id, string moduleName, int moduleSort
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.Unit_Add(Hyun.Core.Model.PUnitDto)">
            <summary>
            新增单位信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.Unit_GetSchoolInfo">
            <summary>
            Unit_GetSchoolInfo 获取当前单位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.Unit_DelUnitInfo(System.Int64)">
            <summary>
            单位管理：删除单位信息（运维、区县管理员），未审核、未禁用的可删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.Unit_FindChildren(Hyun.Core.Model.VUnitParam)">
            <summary>
            下属单位信息列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.Unit_FindIdName(Hyun.Core.Model.PUnitParam)">
            <summary>
            
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.Unit_Examine(Hyun.Core.Model.Models.PUnit)">
            <summary>
            单位管理：审核单位信息，发送审核结果短信
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.Unit_GetCompanyImage(System.Int64)">
            <summary>
            单位管理：获取企业证件信息
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.PUnit_GetSchoolByCountyId(System.Int64)">
            <summary>
            获取单位
            </summary>
            <param name="CountyId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.PUnit_GetCountyByCityId(System.Int64)">
            <summary>
            单位管理：获取区县、单位集合
            </summary>
            <param name="CityId">市Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.PUnit_GetCountyByAdmin">
            <summary>
            单位管理：获取区县集合（运维）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.CountyUnit_Find(Hyun.Core.Model.VUnitParam)">
            <summary>
            单位管理：获取当前单位、下属单位和企业集合
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.DepartmentModule_Get(System.Int64)">
            <summary>
            单位管理：获取部门管理集合列表
            </summary>
            <param name="departId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.SaveSchoolGuid(Hyun.Core.Api.Models.SchoolGuidModel)">
            <summary>
            单位管理：保存更新单位Guid
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.SchoolInfo_Find(Hyun.Core.Model.VSchoolInfoParam)">
            <summary>
            单位信息管理列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.UserRole_BatchSet(Hyun.Core.Api.Models.RoleBatchSetModel)">
            <summary>
            用户管理：保存批量设置用户角色（管理员）
            </summary>
            <param name="m"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.UserRoleChildUnit_BatchSet(Hyun.Core.Api.Models.RoleBatchSetModel)">
            <summary>
            用户管理：保存批量设置用户角色（管理员）
            </summary>
            <param name="m"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.SuperAdminUploadUnitFile(Hyun.Core.Model.SearchModels.Common.ImportUnitParam)">
            <summary>
            导入单位信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.SaveData(System.Data.DataTable,System.Int64,System.Int32,System.Int32,System.Int64)">
            <summary>
            保存数据
            </summary>
            <param name="sheet"></param>
            <param name="pid"></param>
            <param name="industryId">1普教、2高教、3职教、4机关、5家庭</param>
            <param name="unitType">1市级、2区县、3单位、4企业</param>
            <param name="cityId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.SaveSchoolData(System.Data.DataTable,System.Int64,System.Int32,System.Int32,System.Int64)">
            <summary>
            保存导入的单位数据信息
            </summary>
            <param name="sheet"></param>
            <param name="pid"></param>
            <param name="industryId"></param>
            <param name="unitType"></param>
            <param name="cityId"></param>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.OUserThirdAllow_Find(Hyun.Core.Model.OUserThirdAllowParam)">
            <summary>
            用户白名单列表
            </summary>
            <users>
            适用角色：超级管理员、区县管理员
            </users>
            <remarks>
            功能说明：
            作   者：李世申
            创建日期：2022-04-26
            修改说明：
            </remarks>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.OUserThirdAllow_GetById(System.Int64)">
            <summary>
            用户白名单实体信息
            </summary>
            <users>
            适用角色：超级管理员、区县管理员
            </users>
            <remarks>
            功能说明：
            作   者：李世申
            创建日期：2022-04-26
            修改说明：
            </remarks>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.OUserThirdAllow_Delete(System.Int64)">
            <summary>
            用户白名单删除
            </summary>
            <users>
            适用角色：超级管理员、区县管理员
            </users>
            <remarks>
            功能说明：
            作   者：李世申
            创建日期：2022-04-26
            修改说明：
            </remarks>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.OUserThirdAllow_Save(Hyun.Core.Model.Models.OUserThirdAllow)">
            <summary>
            保存用户白名单
            </summary>
            <users>
            适用角色：超级管理员、区县管理员
            </users>
            <remarks>
            功能说明：1:验证手机号唯一（账号是否要验证。）
            作   者：李世申
            创建日期：2022-04-26
            修改说明：
            </remarks>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.GetRoleIds(System.String,System.Int32)">
            <summary>
            根据角色名称转换成角色Id集合方法。
            </summary>
            <param name="names">角色名称</param>
            <param name="unittypeid">角色了类型</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.UploadUserThirdFile">
            <summary>
            白名单导入
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.UseUnitAll_Find">
            <summary>
            单位管理：获取所有单位集合（除企业）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.GetInfoById(System.Int64)">
            <summary>
            方案选用-根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.GetXfUnitInfo">
            <summary>
            获取区县、市级单位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.SetXfUnitInfo(Hyun.Core.Model.PUnitSetDto)">
            <summary>
            设置区县、市级单位信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.GetSchoolUnitInfo">
            <summary>
            单位管理：获取学校
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.SaveSchoolUnitInfo(Hyun.Core.Model.SchoolUnitInfoModel)">
            <summary>
            单位管理：保存学校信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUnitController.GetSupplierFindPage(Hyun.Core.Model.PUnitParam)">
            <summary>
            单位管理：获取供应商列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_InsertUpdate(Hyun.Core.Model.VUserDetail)">
            <summary>
            私有方法保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_Find(Hyun.Core.Model.VUserListParam)">
            <summary>
            超管所有单位用户列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_FindChildren(Hyun.Core.Model.VUserListParam)">
            <summary>
            下属单位用户列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_FindCompany(Hyun.Core.Model.VUserListParam)">
            <summary>
            用户管理：获取企业单位用户列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_FindMyUnitUserName(Hyun.Core.Model.VUserListParam)">
            <summary>
            用户管理：获取用户列表（所有）
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_FindMyUnitUserNameByRoleId(System.String)">
            <summary>
            用户管理：根据指定角色获取本单位所有用户集合
            </summary>
            <param name="rids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_GetPubById(System.Int64)">
            <summary>
            用户管理：获取用户详情信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_GetNameByUnitId(System.Int64)">
            <summary>
            用户管理：获取本单位普通用户集合
            </summary>
            <param name="UnitId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_FindAdmin">
            <summary>
            用户管理：获取所有管理员
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.UserT_Find(Hyun.Core.Model.VUserListTempParam)">
            <summary>
            用户查询，后台调用
            </summary>
            <param name="param">查询实体</param>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.User_GetWithMultAccountInfo">
            <summary>
            获取Home页面用户名及关联账号列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.WebSiteProfiles_Get">
            <summary>
            运维管理：获取平台配置信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.WebSiteProfiles_Save(Hyun.Core.Api.Models.WebSiteProfilesModel)">
            <summary>
            运维管理：保存平台配置信息logo、名称
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.SetDefaultLog(System.Int64,System.String)">
            <summary>
            运维管理：保存平台域名信息
            </summary>
            <param name="IdLogo"></param>
            <param name="DomainName"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.WebSiteConfigSuperManager_Find(Hyun.Core.Model.VWebSiteConfigParam)">
            <summary>
            运维管理：获取平台配置信息列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.GetWebSiteConfig_Combox(System.Int32)">
            <summary>
            运维管理：获取平台配置集合（根据配置类型type）
            </summary>
            <param name="configType"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.WebSiteConfigSuperManager_Save(Hyun.Core.Api.Models.WebSiteConfigSuperModel)">
            <summary>
            运维管理：保存平台配置
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.WebSiteConfigSuperManager_Edit(Hyun.Core.Api.Models.WebSiteConfigModel)">
            <summary>
            运维管理：保存平台配置（运维）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.WebSiteConfigSuperManager_GetById(System.Int64)">
            <summary>
            运维管理：获取配置详情信息（根据配置id）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.GetLockState">
            <summary>
            单位管理：获取单位扩展信息锁的状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.SchoolInfoAdmin_Find(Hyun.Core.Model.VSchoolInfoParam)">
            <summary>
            下属单位信息管理列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.SchoolInfo_GetById(System.Int64)">
            <summary>
            查看单位明细信息
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.UserAccount_UnLock(System.Int64)">
            <summary>
            解冻
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.SchoolBankAccountList_Find(Hyun.Core.Model.PUnitBankAccountParam)">
            <summary>
            履约验收：获取单位账号信息列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.GetRoleRemark">
            <summary>
            角色管理：获取用户角色集合（本单位）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.UnitExchange_IsShow">
            <summary>
            单位管理：获取调入单位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.UnitExchange_Judge(Hyun.Core.Api.Models.JudgeModel)">
            <summary>
            单位管理：审核用户调用（单位单位管理员）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.UploadUserFile(Hyun.Core.Model.SearchModels.Common.ImportUnitParam)">
            <summary>
            用户管理：用户导入下级单位超管信息（包含超管、市级、区县）
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.SaveChildData(System.Data.DataTable,System.Int32)">
            <summary>
            上级单位导入下级单位超管账户或平台超级管理员导入市级或企业账号
            </summary>
            <param name="sheet"></param>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.SaveSelfData(System.Data.DataTable)">
            <summary>
            单位保存本单位用户账号信息(单位、企业)
            </summary>
            <param name="sheet"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.GetUserInfo">
            <summary>
            获取当前用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.SetUserInfo(Hyun.Core.Model.UserModel)">
            <summary>
            设置当前用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.GetXfUserPaged(Hyun.Core.Model.VUserListParam)">
            <summary>
            获取校服管理平台家长、班主任用户信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.AmendPswd(Hyun.Core.Model.UserXfPswdModel)">
            <summary>
            校服管理平台超管重置家长、班主任密码
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PUserController.ExportChildUser(Hyun.Core.Model.VUserListParam)">
            <summary>
            平台设置：下属单位用户导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WeixinBinderController.WxUserBindOpenid_Find(Hyun.Core.Model.WxUserBindOpenidParam)">
            <summary>
            微信管理：获取绑定的OpenId列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxAppManageController.AppManage_Find(Hyun.Core.Model.WxAppManageParam)">
            <summary>
            微信管理：获取WxApp列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxAppManageController.AppManage_GetById(System.Int64)">
            <summary>
            微信管理：获取指定WxApp信息详情
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxAppManageController.AppManage_InsertUpdate(Hyun.Core.Model.WxAppManageDto)">
            <summary>
            微信管理：保存WxApp信息（添加、修改）
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxAppManageController.AppManage_DelBatch(System.String)">
            <summary>
            微信管理：删除WxApp信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxAppManageController.AppManage_SyncUser(System.Int64)">
            <summary>
            微信管理：用户绑定微信功能
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxMenuController.WxMenu_Find(Hyun.Core.Model.WxMenuParam)">
            <summary>
            微信管理：获取微信菜单列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxMenuController.WxMenuTree_Find(Hyun.Core.Model.WxMenuParam)">
            <summary>
            流程管理：获取菜单树集合
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxMenuController.WxMenu_InsertUpdate(Hyun.Core.Model.WxMenuDto)">
            <summary>
            微信管理：保存菜单信息
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxMenuController.WxMenu_DelBatch(System.String)">
            <summary>
            微信管理：删除菜单信息（根据Ids）
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxMenuController.MenuPermission_Save(Hyun.Core.Api.Models.MenuPermissionModel)">
            <summary>
            微信管理：保存菜单权限配置
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxMenuController.MenuPermission_Find(Hyun.Core.Model.WxMenuPermissionParam)">
            <summary>
            微信管理：获取菜单权限配置列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.WxMenuController.GetMenu">
            <summary>
            运维管理：获取微信小程序菜单【小程序使用】
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyList_Find(Hyun.Core.Model.VDcApplyParam)">
            <summary>
            危化品管理：已填报列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAfterConfirm(Hyun.Core.Api.Models.ApplyAfterConfirmModel)">
            <summary>
            危化品管理：申请确认
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyUpdateBackNum(System.Int64,System.Decimal)">
            <summary>
            危化品管理：更新退回数量
            </summary>
            <param name="id"></param>
            <param name="backNum"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyCarList_Find(Hyun.Core.Model.VDcApplyAuditParam)">
            <summary>
            危化品管理：领用待审核列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_FindById(System.Int64)">
            <summary>
            危化品管理：根据Id查询申领数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_InsertUpdate(Hyun.Core.Model.DcApplyDto)">
            <summary>
            危化品管理：申领新增修改
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAdd(Hyun.Core.Model.DcApplyDto)">
            <summary>
            危化品管理：申请添加
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyBatchAdd(System.Collections.Generic.List{Hyun.Core.Model.DcApplyDto})">
            <summary>
            危化品管理：申请批量添加
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_Apply(Hyun.Core.Model.DcApplyDto)">
            <summary>
            危化品管理：领用申请
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_BatchApply(System.String,System.DateTime,System.Int64)">
            <summary>
            危化品管理：批量领用申请
            </summary>
            <param name="ids"></param>
            <param name="useTime"></param>
            <param name="withUserId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_DirectApply(Hyun.Core.Model.DcApplyDto)">
            <summary>
            危化品管理：批量领用申请
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_Delete(System.Int64)">
            <summary>
            危化品管理：根据Id申领删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_View(System.Int64,System.Int32)">
            <summary>
             危化品管理：申请查看
            </summary>
            <param name="id"></param>
            <param name="viewType"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_GetStockNum(System.Int64,System.Int64,System.Int64)">
            <summary>
            危化品管理：获取库存数量
            </summary>
            <param name="schoolCatalogId"></param>
            <param name="schoolMaterialModelId"></param>
            <param name="schoolMaterialBrandId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAuditGroupList_Find(Hyun.Core.Model.VDcApplyAuditGroupListParam)">
            <summary>
            危化品管理：领用审核列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAuditList_Find(Hyun.Core.Model.VDcApplyAuditParam)">
            <summary>
            危化品管理：通用领用待审核列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAuditedList_Find(Hyun.Core.Model.VDcApplyAuditedParam)">
            <summary>
            危化品管理：领用通用已审核列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAudit(Hyun.Core.Api.Models.ApplyAuditModel)">
            <summary>
            危化品管理：领用审核
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyBatchAudit(Hyun.Core.Api.Models.ApplyBatchAuditModel)">
            <summary>
            危化品管理：领用批量审核
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyNoPassReasonGet(System.Int64)">
            <summary>
            危化品管理：根据Id获取领用审批信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplySchoolMaterial_Find(Hyun.Core.Model.DcSchoolMaterialParam)">
            <summary>
            危化品管理：获取单位物品库列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAdjust(System.Collections.Generic.List{Hyun.Core.Model.Model.LvApplyAdjust})">
            <summary>
            危化品管理：领用配货
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyConfirmDetail_Delete(System.Int64)">
            <summary>
            危化品管理：根据Id删除申领配货信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyAdjust_Find(Hyun.Core.Model.VDcApplyAdjustParam)">
            <summary>
            危化品管理：领用配货数据列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_Confirm(System.Int64,System.Decimal,System.String)">
            <summary>
            危化品管理：领用确认
            </summary>
            <param name="id"></param>
            <param name="num"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_BatchConfirm(System.String,System.String)">
            <summary>
            危化品管理：批量领用确认
            </summary>
            <param name="ids"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyPrintList_Find(Hyun.Core.Model.VDcApplyConfirmDetailPrintListParam)">
            <summary>
            危化品管理：获取领用清单打印列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_LookAuditUser(System.Int64)">
            <summary>
            危化品管理：查询当前领用状态的审核人
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_Revoke(System.String)">
            <summary>
            危化品管理：批量撤回已填报危化品信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyFillList_Find(Hyun.Core.Model.VDcApplyFillListParam)">
            <summary>
            危化品管理：领用填报列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyOftenUseList_Find(Hyun.Core.Model.DcApplyParam)">
            <summary>
            危化品管理：获取前6条数据信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyConfirmDetail_ResendCode(System.Int64,System.String,System.Int32)">
            <summary>
            危化品管理：短信发送
            </summary>
            <param name="id"></param>
            <param name="mobile"></param>
            <param name="messageType"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyConfirmDetail_Collar(Hyun.Core.Api.Models.CollarModel)">
            <summary>
            危化品管理：领用发放
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_Find(Hyun.Core.Model.VDcMaterialApplyDetailListParam)">
            <summary>
            危化品管理：单位领用列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.ExportDcApply_Find(Hyun.Core.Model.VDcMaterialApplyDetailListParam)">
            <summary>
            导出领用台账
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyConfirmDetail_GetById(System.Int64)">
            <summary>
            危化品管理：根据Id查询领用详情信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcMaterialRevert_BackConfirm(System.Int64,System.Int32)">
            <summary>
            危化品管理：危化品退回
            </summary>
            <param name="id"></param>
            <param name="isMayUse"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcMaterialBack_Find(Hyun.Core.Model.VDcMaterialBackListParam)">
            <summary>
            危化品管理：单位危化品退回列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApplyEasyList_Find(Hyun.Core.Model.DcApplyParam)">
            <summary>
            危化品管理：简易申领列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_EasyApply(Hyun.Core.Model.DcApplyDto)">
            <summary>
            危化品管理：简易领用
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_EasyBack(System.Int64,System.Decimal,System.Int32)">
            <summary>
            危化品管理：简易退回
            </summary>
            <param name="applyConfirmDetailId"></param>
            <param name="backNum"></param>
            <param name="isMayUse"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.DcApply_EasyGrant(System.Int64,System.Decimal,System.Int32)">
            <summary>
            危化品管理：简易发放
            </summary>
            <param name="id"></param>
            <param name="num"></param>
            <param name="isNeedSendMessage"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsApplyController.CreateRandomNumCode(System.Int32)">
            <summary>
            
            </summary>
            <param name="codeCount"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcAuditCondition_Find">
            <summary>
            审核审批配置-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcAuditCondition_InsertUpdate(System.Collections.Generic.List{Hyun.Core.Model.DcAuditConditionDto})">
            <summary>
            审核审批设置-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcApplyGrantUser_Find(Hyun.Core.Model.DcApplyGrantUserParam)">
            <summary>
            获取领用人、发送人列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcApplyGrantUser_Add(Hyun.Core.Model.DcApplyGrantUserDto)">
            <summary>
            添加领用人、发放人-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcApplyGrantUser_Delete(System.Int64)">
            <summary>
            删除领用人、发放人-删除
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcApplyGrantUserCombo_Get(System.Int32)">
            <summary>
            获取同领用人、发放人下拉框数据--查询
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWorkGuide_Find(Hyun.Core.Model.BAttachmentParam)">
            <summary>
            获取工作指导文件列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcDepositAddress_Find(Hyun.Core.Model.DcDepositAddressParam)">
            <summary>
            存放地点-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcDepositAddressList_Find(Hyun.Core.Model.DcDepositAddressParam)">
            <summary>
            获取地址配置列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcDepositAddress_InsertUpdate(Hyun.Core.Model.DcDepositAddressDto)">
            <summary>
            存放地点设置-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcDepositAddress_Delete(System.Int64)">
            <summary>
            存放地点-删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcDepositAddress_GetById(System.Int64)">
            <summary>
            获取地址信息-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCompany_Find(Hyun.Core.Model.DcCompanyParam)">
            <summary>
            供应商管理列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCompany_GetById(System.Int64)">
            <summary>
            根据Id获取供应商信息-查询
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCompany_InsertUpdate(Hyun.Core.Model.DcCompanyDto)">
            <summary>
            供应商新增修改-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCompany_DeleteById(System.Int64)">
            <summary>
            供应商删除-删除
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCompany_Enabled(System.Int64)">
            <summary>
            供应商启用禁用-设置
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcUnitLicenseInfo_GetCommany(System.Int64,System.Int32)">
            <summary>
            单位许可证信息-查询
            </summary>
            <param name="id">供应商表Id</param>
            <param name="unittype">类型3：单位信息  4：供应商信息</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcUnitLicenseInfo_Save(Hyun.Core.Model.DcUnitLicenseInfoDto)">
            <summary>
            保存供应商、单位单位证书信息-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogInit_Find(Hyun.Core.Model.VUnitListParam)">
            <summary>
            单位基础库管理-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogInit_Save(System.Int64)">
            <summary>
            单位基础库-保存
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogInit_Update(System.Int64)">
            <summary>
            修改单位基础库-保存
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogParent_Get">
            <summary>
            物品一二级分类下拉框数据-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalog_InsertUpdate(Hyun.Core.Model.DcSchoolCatalogDto)">
            <summary>
            单位物品分类添加-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalog_GetById(System.Int64)">
            <summary>
            根据id获取物品详细信息-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialStatistics_Find(Hyun.Core.Model.VDcMaterialStatisticsListParam)">
            <summary>
             单位库基础库,物品汇总列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialCatalog_Find(Hyun.Core.Model.VDcSchoolMaterialListParam)">
            <summary>
            单位库基础库,物品汇总列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCompanyCombox_Find(Hyun.Core.Model.DcCompanyParam)">
            <summary>
            获取单位供应商信息-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialCatalog_GetById(System.Int64)">
            <summary>
            根据Id获取“单位物品库”信息-查询
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterial_BatchAudit(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量审核-审核
            </summary>
            <param name="listUserId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterial_DeleteById(System.Int64)">
            <summary>
            入库审核物品删除-删除
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalog_Find(Hyun.Core.Model.VDcSchoolCatalogParam)">
            <summary>
            用户库基础库,物品管理启用、禁用、是否归还-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalog_SetStatuz(System.String,System.Int32)">
            <summary>
            用户库基础库,物品管理启用、禁用-设置
            </summary>
            <param name="ids"></param>
            <param name="statuz"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalog_SetIsCommonUse(System.String,System.Int32)">
            <summary>
            危化品选品-设置
            </summary>
            <param name="ids"></param>
            <param name="isCommonUse"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModel_SetStatuz(System.String,System.Int32)">
            <summary>
            用户库基础型号库管理启用、禁用-设置
            </summary>
            <param name="ids"></param>
            <param name="statuz"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModel_GetById(System.Int64)">
            <summary>
            物品规格型号，获取实体-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModel_Save(Hyun.Core.Model.Models.DcSchoolMaterialModel)">
            <summary>
            添加物品规格型号-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModel_Get(System.Int32,Hyun.Core.Model.DcApplyParam,System.Int32)">
            <summary>
            获取物品规格型号-查询
            </summary>
            <param name="catelogId"></param>
            <param name="param"></param>
            <param name="isNoSet"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModel_GetV2(System.Int32)">
            <summary>
            获取物品规格型号-查询2
            </summary>
            <param name="catelogId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolModelBrand_Set(System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            物品规格品牌配置-设置
            </summary>
            <param name="schoolCatalogId"></param>
            <param name="modelId"></param>
            <param name="baseModelId"></param>
            <param name="brandIds"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialBrand_Save(Hyun.Core.Model.Models.DcSchoolMaterialBrand)">
            <summary>
            用户库基础库,物品管理启用、禁用-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialBrand_Get(System.Int32,Hyun.Core.Model.DcApplyParam,System.Int32)">
            <summary>
            获取物品库品牌信息-查询
            </summary>
            <param name="catelogId"></param>
            <param name="param"></param>
            <param name="isNoSet"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialBrand_GetV2(System.Int32,System.Int32)">
            <summary>
            获取物品库品牌信息-查询2
            </summary>
            <param name="catelogId"></param>
            <param name="modelId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModelConfig_Find(Hyun.Core.Model.VDcSchoolMaterialModelConfigParam)">
            <summary>
            查询物品型号预警设置列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModelConfig_GetById(System.Int64)">
            <summary>
            物品预警获取实体信息-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModelConfig_Save(Hyun.Core.Model.DcSchoolMaterialModelBatchParam)">
            <summary>
            物品型号配置参数保存-保存
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWarningStockList_Find(Hyun.Core.Model.VDcWarningStockListParam)">
            <summary>
            物品超量预警列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWarningValidityList_Find(Hyun.Core.Model.VDcWarningValidityListParam)">
            <summary>
            物品超期预警列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWarningUseLifeList_Find(Hyun.Core.Model.VDcWarningUseLifeListParam)">
            <summary>
            使用年限预警列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcScrapList_Find(Hyun.Core.Model.VDcSchoolMaterialListParam)">
            <summary>
            存量预警查看-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcStorageLogPrint_UpdateStatuz(Hyun.Core.Model.VDcSchoolMaterialListParam)">
            <summary>
             入库记录打印，更新打印状态-设置
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterial_GetById(System.Int64)">
            <summary>
             物品报废-获取物品实体方法-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcScrapedList_Find(Hyun.Core.Model.VDcScrapListParam)">
            <summary>
            存量预警查看-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcMaterial_SetIsMayUse(System.Int64,System.Int32)">
            <summary>
            设置是否可二次使用-设置
            </summary>
            <param name="id"></param>
            <param name="isMayUse"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcInventory_Save(System.Int64,System.Int32)">
            <summary>
            物品盘点-盘盈，盘亏-保存
            </summary>
            <param name="id"></param>
            <param name="statuz"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcInventoryRecord_Find(Hyun.Core.Model.VDcInventoryListParam)">
            <summary>
            存量预警查看-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcMaterialsNumAudit_Add(Hyun.Core.Model.Models.DcMaterialsNumAudit)">
            <summary>
            退货、盘点、报废审核表新增-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcMaterialsNumAudit_Find(Hyun.Core.Model.VDcMaterialsNumAuditParam)">
            <summary>
            盘点、退货、报废审核表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialOpt_Find(Hyun.Core.Model.VDcSchoolMaterialOptListParam)">
            <summary>
            危化品存量库-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.ExportDcSchoolMaterialOpt_Find(Hyun.Core.Model.VDcSchoolMaterialOptListParam)">
            <summary>
            导出盘点表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcInputMaterialByPlan_Find(Hyun.Core.Model.VDcInputMaterialByPlanParam)">
            <summary>
            按计划录入入库列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcMaterialByPlan_Save(Hyun.Core.Model.ViewModels.LvPlanInputDto)">
            <summary>
            按计划录入-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcMaterialByPlan_Input(Hyun.Core.Model.ViewModels.LvPlanInputDto)">
            <summary>
            按计划录入入库-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseOrder_FinishInputStatuz(System.Int64)">
            <summary>
            结束入库-设置
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialTemp_Delete(System.Int64)">
            <summary>
            清除物品入库保存的临时数据-删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseCatalog_Find">
            <summary>
            基础分类数据列表-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseCatalogCmbo_Get(System.Int32,System.Int32)">
            <summary>
            获取危化品分类一二级下拉框-查询
            </summary>
            <param name="depth"></param>
            <param name="pid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseCatalog_InsertUpdate(Hyun.Core.Model.DcBaseCatalogDto)">
            <summary>
            保存危化品分类-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseCatalog_Delete(System.Int64)">
            <summary>
            基础分类-删除
            </summary>
            <param name="id">分类Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseCatalog_SetStatuz(System.Int64,System.Int32)">
            <summary>
            分类启用、禁用-设置
            </summary>
            <param name="id"></param>
            <param name="statuz"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseModelExtension_Find(Hyun.Core.Model.DcBaseModelExtensionParam)">
            <summary>
            基础规格参数列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseModelExtension_Save(Hyun.Core.Model.Models.DcBaseModelExtension)">
            <summary>
            添加、修改基础规格参数-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcBaseModelExtension_GetById(System.Int64)">
            <summary>
            获取基础规格参数信息-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcDcBaseModelExtensionConfig_Save(System.String,System.Int32,System.String)">
            <summary>
            基础规格配置参数-保存
            </summary>
            <param name="arrId"></param>
            <param name="type"></param>
            <param name="setValue"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCabinetAddress_Get">
            <summary>
            获取存储柜地址下拉列表-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseList_Find(Hyun.Core.Model.DcPurchaseListParam)">
            <summary>
            采购清单列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseList_ExportExcel(Hyun.Core.Model.DcPurchaseListParam)">
            <summary>
            采购清单列表-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseDetailList_Find(Hyun.Core.Model.VDcPurchaseDetailListParam)">
            <summary>
            采购物品明细列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseDetailList_Export(Hyun.Core.Model.VDcPurchaseDetailListParam)">
            <summary>
            采购物品明细列表-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseList_FindById(System.Int64)">
            <summary>
            根据Id查询采购清单详细数据-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseOrder_Find(Hyun.Core.Model.VDcPurchaseOrderListParam)">
            <summary>
            已填报列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseByCatalog_Find(Hyun.Core.Model.DcSchoolCatalogParam)">
            <summary>
            从基础库添加列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseBatchAdd(System.Collections.Generic.List{Hyun.Core.Model.DcPurchaseListDto})">
            <summary>
            物品批量添加采购车-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseAdd(Hyun.Core.Model.DcPurchaseListDto)">
            <summary>
            物品单条加入采购车-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseApply(System.Int64,System.String)">
            <summary>
            物品采购申请-保存
            </summary>
            <param name="purchaseOrderId"></param>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseListDelete(System.Int64)">
            <summary>
            物品采购清单-删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseOrderDelete(System.Int64)">
            <summary>
            物品采购单-删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseListUpdate(Hyun.Core.Model.DcPurchaseListDto)">
            <summary>
            采购物品清单修改-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseAudit_Find(Hyun.Core.Model.VDcPurchaseOrderListParam)">
            <summary>
            通用物品待审批列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseAudited_Find(Hyun.Core.Model.VDcPurchaseOrderAuditedParam)">
            <summary>
            通用物品已审批列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseAudit(Hyun.Core.Model.ViewModels.PurchaseResultDto)">
            <summary>
            通用物品采购审批-审核
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseWithdraw(System.Int64,System.Int64)">
            <summary>
            通用物品采购审批-撤销
            </summary>
            <param name="id"></param>
            <param name="purchaseOrderId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseNoPassReasonGet(System.Int64)">
            <summary>
            获取退回原因-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseEnd_Find(Hyun.Core.Model.VDcPurchaseOrderListParam)">
            <summary>
            采购已生成计划列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseEnd_GetById(System.Int64)">
            <summary>
            采购已生成计划实体-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseEnd_Export(Hyun.Core.Model.DcPurchaseListParam)">
            <summary>
            采购已生成计划列表-导出
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchase_Revoke(System.Int64)">
            <summary>
            物品采购-撤销
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolPurchaseSummaryList_Find(Hyun.Core.Model.DcPurchaseOrderParam)">
            <summary>
            单位采购汇总表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolPurchaseSummaryList_Export(Hyun.Core.Model.DcPurchaseOrderParam)">
            <summary>
            单位采购汇总表-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCatalog_GetByPid(System.Int64)">
            <summary>
            根据上级Id获取分类-查询
            </summary>
            <param name="pid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCatalog_GetClassTwo(System.Int64)">
            <summary>
            获取维护品二级分类-查询
            </summary>
            <param name="unitId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogModel_GetV2(System.Int64)">
            <summary>
            获取可使用的物品型号-查询
            </summary>
            <param name="id"></param>
            <remarks>
            注意：只有物品领用时使用(包含多余库存的数据)
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogBrand_GetByModelIdV2(System.Int64,System.Int64)">
            <summary>
            根据型号获取可用品牌-查询 
            </summary>
            <param name="schoolCatalogId"></param>
            <param name="modelId"></param>
            <remarks>
            注意：只有物品领用时使用(包含多余库存的数据)
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialModel_GetByCatalogId(System.Int64)">
            <summary>
            获取物品型号-查询
            </summary>
            <param name="schoolCatalodId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolModelBrand_GetBrand(System.Int64,System.Int64)">
            <summary>
            获取物品可使用的品牌-查询
            </summary>
            <param name="schoolCatalogId"></param>
            <param name="modelId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterial_InsertUpdate(Hyun.Core.Model.DcSchoolMaterialDto)">
            <summary>
            物品-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogCommonUse_Find(Hyun.Core.Model.VDcSchoolCatalogListParam)">
            <summary>
            物品分类搜索-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalogCombobox_Get">
            <summary>
            物品分类下拉框数据-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalog_FindCommonUseAll(System.Int32)">
            <summary>
            获取单位物品类别选择-查询
            </summary>
            <param name="commonUse"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolCatalog_Record(System.Int64,System.Int64)">
            <summary>
            入库记录变更记录信息-查询
            </summary>
            <param name="SchoolMaterialId"></param>
            <param name="PurchaseListId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseBatchNo_Find">
            <summary>
            获取采购批次下拉框数据-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterial_Next(System.Int64)">
            <summary>
            入库审核 下一条显示-查询
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterial_ReturnLibrary(System.Int64,System.Int32)">
            <summary>
            物品退货-保存
            </summary>
            <param name="id"></param>
            <param name="statuz"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolMaterialBackLog_Find(Hyun.Core.Model.VDcSchoolMaterialBackLogParam)">
            <summary>
            已退货清单列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcSchoolModelBrand_SetDetail(System.Int32,System.String,System.String)">
            <summary>
            添加物品详细描述-设置
            </summary>
            <param name="id"></param>
            <param name="imageUrl"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseOrder_UploadFile(Hyun.Core.Model.AttachmentModel)">
            <summary>
            采购申请单上传公安报备文件-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseOrder_GetById(System.Int64)">
            <summary>
            根据Id采购申请单-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcMaterialAddress_BatchEdit(System.String,System.Int64,System.String)">
            <summary>
            批量修改存放地点-设置
            </summary>
            <param name="ids"></param>
            <param name="addressId"></param>
            <param name="cabinetAddress"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.SchoolMaterial_MsdsFileEdit(System.Int64,System.String)">
            <summary>
            根据Id修改MSDS文件-保存
            </summary>
            <param name="id"></param>
            <param name="msdsFile"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.BaseFieldConfig_Find(System.Int64)">
            <summary>
            加载制度与队伍建设数据信息-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.BaseFieldConfig_SaveFile(Hyun.Core.Model.DcBaseFieldConfigDto)">
            <summary>
            附件信息-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.BaseFieldConfig_DeleteFile(System.Int64)">
            <summary>
            删除附件-删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.BaseFieldConfig_SaveList(Hyun.Core.Model.DcBaseFieldConfigModel)">
            <summary>
            批量保存制度与队伍建设文本信息-保存
            </summary>
            <param name="model">{list:数据集合,attrs:附件集合[{Id:历史Id(新增的附件为0),RelationId:附件标识编码,FilePath:附件路径,Title:附件标题}]}</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.TrainSafeEducation_Find(Hyun.Core.Model.DcTrainSafeEducationParam)">
            <summary>
            培训与安全教育列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.TrainSafeEducation_InsertUpdate(Hyun.Core.Model.DcTrainSafeEducationDto)">
            <summary>
            保存培训与安全教育信息-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.TrainSafeEducation_Edit(System.Int64)">
            <summary>
            培训与安全教育修改内容获取-查询
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.TrainSafeEducation_Delete(System.Int64)">
            <summary>
            培训与安全教育-删除
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.EmergencyPlan_Find(Hyun.Core.Model.DcEmergencyPlanParam)">
            <summary>
            应急预案与演练列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.EmergencyPlan_InsertUpdate(Hyun.Core.Model.DcEmergencyPlanDto)">
            <summary>
            保存应急预案与演练信息-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.EmergencyPlan_Edit(System.Int64)">
            <summary>
            应急预案与演练修改内容获取-查询
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.EmergencyPlan_Delete(System.Int64)">
            <summary>
            培训与安全教育-删除
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcGuaranteeClaimList_Find(Hyun.Core.Model.DcTrainSafeEducationParam)">
            <summary>
            保障要求查看列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteBase_Find">
            <summary>
            废弃物基础分类列表-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposal_Find">
            <summary>
            废弃物处置填报列表-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasetDisposal_Save(Hyun.Core.Model.Model.DcWasteDisposalSaveModel)">
            <summary>
            废弃物处置申请-保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasetDisposal_GetIsExists">
            <summary>
            查询是否存在废弃物-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposalAudit_Find(Hyun.Core.Model.VDcWasteDisposalAuditParam)">
            <summary>
            废弃物处置审核列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposalDetail_Find(Hyun.Core.Model.VDcWasteDisposalDetailParam)">
            <summary>
            废弃物明细列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.ExportDcWasteDisposalDetailFind(Hyun.Core.Model.VDcWasteDisposalDetailParam)">
            <summary>
            导出已处置危废物
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposal_Audit(System.Int64,System.Int32,System.String)">
            <summary>
            处置-审核
            </summary>
            <param name="id"></param>
            <param name="statuz"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposal_Report(Hyun.Core.Model.DcWasteDisposalDto)">
            <summary>
            处置信息-填报
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposal_GetById(System.Int64)">
            <summary>
            根据Id查询废弃物处置信息-查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteClass_Get(System.Int64,System.Int64,System.Int32)">
            <summary>
            获取废弃物分类-查询
            </summary>
            <param name="pid"></param>
            <param name="id"></param>
            <param name="depth"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteRecord_Find(Hyun.Core.Model.VDcWasteRecordParam)">
            <summary>
            实验后废弃物明细列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcScrap_Disposal(System.String)">
            <summary>
            报废危化品处置申请-保存
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.GetDcApplyStatisticsYear">
            <summary>
            获取领用查询年度列表-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.GetDcApplyStatisticsUser">
            <summary>
            获取领用查询年度列表-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcApplyUserNumStatistics_Find(Hyun.Core.Model.VDcApplyStatisticsParam)">
            <summary>
            物品领用按领用人统计-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCountyStockNumStatistics_Find(Hyun.Core.Model.VDcCountyStockNumStatisticsParam)">
            <summary>
            库存数量统计-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCountyStockNumStatistics_Exort(Hyun.Core.Model.VDcCountyStockNumStatisticsParam)">
            <summary>
            库存数量统计-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.GetDcPurchaseStatisticsYear">
            <summary>
            获取采购查询年度列表-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposalStatistics_Find(Hyun.Core.Model.DcWasteDisposalDetailParam)">
            <summary>
            废弃物存量统计-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcPurchaseNumStatistics_Find(Hyun.Core.Model.ViewModels.DcSchoolMaterialStatisticsModel)">
            <summary>
            采购数量统计-查询
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcApplyNumStatistics_Find(Hyun.Core.Model.VDcApplyStatisticsParam)">
            <summary>
            领用数量统计-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposalNumStatistics_Find(Hyun.Core.Model.VDcWasteDisposalDetailParam)">
            <summary>
            处置数量统计-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcWasteDisposalNumStatistics_Export(Hyun.Core.Model.VDcWasteDisposalDetailParam)">
            <summary>
            处置数量统计-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.GetDcWasteDisposalStatisticsYear">
            <summary>
            获取处置数量统计年度-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcClassifyStockNumStatistics_Find(Hyun.Core.Model.VDcClassifyStockNumStatisticsParam)">
            <summary>
            按属性统计数量-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcClassifyStockNumStatistics_Export(Hyun.Core.Model.VDcClassifyStockNumStatisticsParam)">
            <summary>
            按属性统计数量-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCityStockNumStatistics_Find(Hyun.Core.Model.DcSchoolMaterialParam)">
            <summary>
            市级库存数量统计-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCityStockNumStatistics_Export(Hyun.Core.Model.DcSchoolMaterialParam)">
            <summary>
            市级库存数量统计-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCityClassifyStockNumStatistics_Find(Hyun.Core.Model.VDcCityClassifyStockStatisticsParam)">
            <summary>
            市级按属性统计数量-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCityClassifyStockNumStatisticsExport(Hyun.Core.Model.VDcCityClassifyStockStatisticsParam)">
            <summary>
            市级按属性统计数量-导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCityPurchaseNumStatistics_Find(System.String,System.Int32,System.String,System.String,System.Int32)">
            <summary>
            市级采购数量统计-查询
            </summary>
            <param name="years"></param>
            <param name="classTwoId"></param>
            <param name="name"></param>
            <param name="model"></param>
            <param name="countyId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCityApplyNumStatistics_Find(System.String,System.Int32,System.String,System.String,System.Int32)">
            <summary>
            市级领用数量统计-查询
            </summary>
            <param name="years"></param>
            <param name="classTwoId"></param>
            <param name="name"></param>
            <param name="model"></param>
            <param name="countyId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcCityWasteDisposalStatistics_Find(Hyun.Core.Model.DcWasteDisposalDetailParam)">
            <summary>
            市级废弃物存量统计-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcStandbookPurchase_Find(Hyun.Core.Model.DcSchoolMaterialModelParam)">
            <summary>
            采购台账-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.ExportDcStandbookPurchase_Find(Hyun.Core.Model.DcSchoolMaterialModelParam)">
            <summary>
            采购台账导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcStandbookStock_Find(Hyun.Core.Model.DcSchoolMaterialModelParam)">
            <summary>
            存量台账-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.DcStandbookWaste_Find(Hyun.Core.Model.DcWasteDisposalDetailParam)">
            <summary>
            处置台账-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.ExportDcStandbookStock_Find(Hyun.Core.Model.DcSchoolMaterialModelParam)">
            <summary>
            存量台账导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.ExportDcStandbookWaste_Find(Hyun.Core.Model.DcWasteDisposalDetailParam)">
            <summary>
            导出处置台账
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsController.ExportDcScrapList(Hyun.Core.Model.VDcSchoolMaterialListParam)">
            <summary>
            导出危化品存量库(个性化处理)
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.GetZbDanweibeianInfo(System.String)">
            <summary>
            获取单位备案信息接口
            </summary>
            <param name="thirdUnitId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.GetZbRenyuanbeianInfo(System.String)">
            <summary>
            获取人员备案信息接口
            </summary>
            <param name="thirdUnitId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.DcAutoBindIsSecuritySupervise">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.ZbGetMaterial">
            <summary>
            获取化学品库信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.GetHeader">
            <summary>
            获取接口头信
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.GetSm2(System.Object)">
            <summary>
            获取参数sm2加密后结果
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.GetZbSaleUnitList(System.String)">
            <summary>
            获取销售单位接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.GrantCheckMessage(System.String,Hyun.Core.Model.Model.ZbDataStoragePlace,Hyun.Core.Model.Model.ZbDataUser)">
            <summary>
            发放校验及获取第三方接口数据
            </summary>
            <param name="thirdUnitId"></param>
            <param name="storagePlace"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcDangerChemicalsZbController.GetThirdUnitId">
            <summary>
            获取当前单位对应的第三方Id
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.DcSchoolMaterialBindCode">
            <summary>
            物品绑定对应编码
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcSchoolMaterialBindCode.SchoolMaterialId">
            <summary>
            单位物品Id
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcSchoolMaterialBindCode.CodeList">
            <summary>
            编码集合
            </summary>
        </member>
        <member name="T:Hyun.Core.Api.DcMaterialCode">
            <summary>
            编码及对应数量
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcMaterialCode.Num">
            <summary>
            对应数量
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcMaterialCode.ThirdCode">
            <summary>
            第三方标识码
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcMaterialCode.ThirdMaterialId">
            <summary>
            第三方物品Id
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcMaterialCode.ThirdMaterialName">
            <summary>
            第三方物品名称
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcMaterialCode.ThirdNum">
            <summary>
            第三方数量
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.DcMaterialCode.ThirdUnitName">
            <summary>
            第三方计量单位
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernItemReport_GetById(Hyun.Core.Model.VDcGovernItemReportParam)">
            <summary>
            查询周天排查列表-查询
            </summary>
            <param name="param">查询实体</param>
            <users>
            适用角色：单位排查员
            </users>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernItemReport_Save(Hyun.Core.Model.ViewModels.Common.DcGovernItemReportSaveDto)">
            <summary>
            整改申报-保存
            </summary>
            <param name="o">填报实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernDeclareSummaryWeek_Find(Hyun.Core.Model.VDcGovernDeclareSummaryWeekParam)">
            <summary>
            查询周申报列表-查询
            </summary>
            <param name="param">周申报列表查询实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernDeclareSummaryMonth_Find(Hyun.Core.Model.VDcGovernDeclareSummaryMonthParam)">
            <summary>
            查询月申报列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernDeclareDetail_Find(Hyun.Core.Model.VDcGovernDeclareDetailParam)">
            <summary>
            查询填报详情-查询
            </summary>
            <param name="param">查询实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.GetAttachemntList(Hyun.Core.Model.PageModel{Hyun.Core.Model.VDcGovernItemDetailAttachment},System.Int64)">
            <summary>
            处理附件
            </summary>
            <param name="data"></param>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernRectifyList_Find(Hyun.Core.Model.DcGovernRectifyParam)">
            <summary>
            问题隐患清单-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernRectify_GetById(System.Int64,System.Int32)">
            <summary>
            获取申报整改信息-查询
            </summary>
            <param name="id">整改表Id</param>
            <param name="opttype">查看类型，1：查看申报信息 2：查看整改信息</param>
            <users>
            适用角色：单位排查员
            </users>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.GovernDeclareDetail_GetById(System.Int64,System.Int32)">
            <summary>
            查看检查说明-查询
            </summary>
            <param name="id">危化品治理申报分类明细表Id</param>
            <param name="opttype">查看类型，1：查看申报信息 2：查看整改信息</param>
            <users>
            适用角色：单位排查员
            </users>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernRectify_RectifySave(Hyun.Core.Model.DcGovernRectifyDto)">
            <summary>
            危化品整改-保存
            </summary>
            <param name="o">参数</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.DcGovernDeclareSummary_Find(Hyun.Core.Model.VDcGovernDeclareSummaryParam)">
            <summary>
            危化品治理 - 区、市周、月统计列表-查询
            </summary>
            <param name="param">查询参数实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_GovernDeclareUnitConfig_Find(Hyun.Core.Model.VDcGovernDeclareUnitConfigParam)">
            <summary>
            申报单位管理列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_GovernDeclareUnitConfig_Set(System.Int64,System.Int32)">
            <summary>
            设置单位是否需要上报-保存
            </summary>
            <param name="id"></param>
            <param name="isNeedReporting"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_GovernDeclareUnitConfig_BatchSet(System.String,System.Int32)">
            <summary>
            批量设置单位上报-保存批量
            </summary>
            <param name="SchoolIds"></param>
            <param name="isNeedReporting"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_GovernSet_Get">
            <summary>
            危化品打印输出单位信息-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_GovernUnitSet_Save(Hyun.Core.Model.DcGovernSetDto)">
            <summary>
            危化品打印输出单位信息-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_GovernDateTimeSet_Save(Hyun.Core.Model.DcGovernSetDto)">
            <summary>
            保存申报时间设置-保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_GovernItemInfo">
            <summary>
            获取危化品治理打印表数据-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernController.Dc_ProblemGovernRectifyList_Print(Hyun.Core.Model.PrintWhereModel)">
            <summary>
            问题与隐患清单检查记录表打印-查询
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskUnitByTaskName_FindbyId">
            <summary>
            获取当前危化品治理任务信息-查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskUnitList_Find(Hyun.Core.Model.VDcGovernTaskSummaryListParam)">
            <summary>
            被检查单位列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskSchoolList_Find(Hyun.Core.Model.VCountyDeclareUnitParam)">
            <summary>
            选择单位列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskSchool_Add(Hyun.Core.Model.DcGovernTaskUnitDto)">
            <summary>
            添加单位(废除，都用批量添加)
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskSchool_BatchAdd(Hyun.Core.Model.GovernTaskModelDto)">
            <summary>
            批量添加单位
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskSchool_Delete(Hyun.Core.Model.GovernTaskDelDto)">
            <summary>
            删除单位
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskUnit_GetById(System.Int64)">
            <summary>
            获取危化品治理任务单位数据-查询
            </summary>
            <param name="GovernTaskUnitId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskUnit_Update(Hyun.Core.Model.DcGovernTaskUnitDto)">
            <summary>
            保存检查基本信息-保存
            </summary>
            <param name="model">参数实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTask_Finish(System.Int64,System.String)">
            <summary>
            危化品检查结束任务-修改
            </summary>
            <param name="taskId"></param>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskedList_Find(Hyun.Core.Model.DcGovernTaskParam)">
            <summary>
            已创建检查记录列表-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.ProblemGovernRectifyList_Find(Hyun.Core.Model.VDcProblemGovernRectifyListParam)">
            <summary>
            问题隐患清单-查询
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.GovernTaskUnitList_Print(System.Int64)">
            <summary>
            单位危险化学品安全综合治理检查记录表打印-打印
            </summary>
            <param name="GovernTaskId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.ProblemDangerStatisticsPrint_Print(Hyun.Core.Model.VDcGovernDeclareSummaryParam)">
            <summary>
            查询问题与隐患清单周/月统计表-打印
            </summary>
            <param name="param">查询实体</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.DcGovernTaskController.Print_Check">
            <summary>
            验证单位信息是否已经设置-保存
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategoryGetPaged(Hyun.Core.Model.ThEquipmentCategoryParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">ThEquipmentCategoryParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategory">
            <summary>
            获取当前单位分类集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategoryById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategoryInsertUpdate(Hyun.Core.Model.ThEquipmentCategoryDto)">
            <summary>
            新增修改
            </summary>
            <param name="model">ThEquipmentCategoryDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategorySetStatuz(Hyun.Core.Model.ThEquipmentCategoryDto)">
            <summary>
            新增修改
            </summary>
            <param name="model">ThEquipmentCategoryDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategoryDelById(System.Int64)">
            <summary>
            根据Id删除
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategoryUploadFile(Hyun.Core.Model.SearchModels.Common.ImportUnitParam)">
            <summary>
            导入装备分类信息
            </summary>
            <param name="param"></param>
            <remarks>
            根据分类、名称一致，禁止导入，重复已存在。
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategoryGetSchoolPaged(Hyun.Core.Model.ThEquipmentCategoryParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">ThEquipmentCategoryParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.ThEquipmentCategoryController.ThEquipmentCategorySchoolSave(System.Collections.Generic.List{Hyun.Core.Model.ThEquipmentSchoolInfoDto})">
            <summary>
            新增修改
            </summary>
            <param name="list">保存数据集合</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.BAttachmentConfigController">
            <summary>
            备案附件管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.BAttachmentConfigController.BAttachmentConfigGetPaged(Hyun.Core.Model.BAttachmentConfigParam)">
            <summary>
            查询备案附件管理列表
            </summary>
            <param name="param">
            BAttachmentConfigParam对象
            IsFilled： 是否必填（1：是，0：否）
            Key：关键字（模块名称、附件名称、备注文字）
            other中返回是否必填数据
            </param>
            <returns></returns>
            <getpaged>查询备案附件管理列表</getpaged>
        </member>
        <member name="M:Hyun.Core.Api.BAttachmentConfigController.BAttachmentConfigGetPagedByType(System.Int32)">
            <summary>
            根据模块类型获取备案附件信息
            </summary>
            <param name="moduleType">
            101：校服选用
            102：校服采购
            103：校服录入
            104：选用组织
            105：校服资助
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAttachmentConfigController.BAttachmentConfigById(System.Int64,System.Boolean)">
            <summary>
            根据Id获取备案附件信息
            备注：id传0,isFirst传true查询模块名称下拉框数据
            </summary>
            <param name="id">备案附件Id</param>
            <param name="isFirst">是否第一次调用</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAttachmentConfigController.BAttachmentConfigAdd(Hyun.Core.Model.BAttachmentConfigDto)">
            <summary>
            添加备案附件信息
            </summary>
            <param name="obj">
            BAttachmentConfigDto对象
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAttachmentConfigController.BAttachmentConfigEdit(Hyun.Core.Model.BAttachmentConfigDto)">
            <summary>
            修改备案附件信息(对象中Id必传)
            </summary>
            <param name="obj">
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAttachmentConfigController.BAttachmentConfigDeleteById(System.Int64)">
            <summary>
            根据Id删除备案附件信息
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.BAttachmentConfigController.BAttachmentConfigSetStatuz(System.Int64)">
            <summary>
            根据Id启用禁用备案附件
            </summary>
            <param name="id">备案附件Id</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.PClassInfoController">
            <summary>
            年级班级管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.PClassInfoController.PClassInfoAdd(Hyun.Core.Model.PClassInfoDto)">
            <summary>
            新增班级
            </summary>
            <param name="obj">PClassInfoDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PClassInfoController.PClassInfoEdit(Hyun.Core.Model.PClassInfoDto)">
            <summary>
            修改班级
            </summary>
            <param name="obj">PClassInfoDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PClassInfoController.PClassInfoByEditId(System.Int64,System.Boolean)">
            <summary>
            根据Id查询班级信息
            </summary>
            <param name="id">班级Id</param>
            <param name="isFirst">是否第一次调用(true/false)</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PClassInfoController.PClassInfoDeleteById(System.Int64)">
            <summary>
            根据班级Id删除班级信息
            </summary>
            <param name="id">班级Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PClassInfoController.PClassInfoGetPaged(Hyun.Core.Model.PClassInfoParam)">
            <summary>
            查询年级班级列表
            </summary>
            <param name="param">PClassInfoParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PClassInfoController.PClassInfoImport(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入班级数据信息
            </summary>
            <param name="fileList"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PClassInfoController.GradeUpgrade">
            <summary>
            年级升级
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PParentStudentController.PStudentGetPaged">
            <summary>
            家长获取学生列表信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PParentStudentController.InsertUpdate(Hyun.Core.Model.PParentStudentModel)">
            <summary>
            添加学生信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PParentStudentController.DeleteById(System.Int64)">
            <summary>
            删除学生信息
            </summary>
            <param name="id">家长学生表Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PParentStudentController.GetParentUniformList(Hyun.Core.Model.PParentStudentParam)">
            <summary>
            获取家长全部订单数据信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.PStudentController">
            <summary>
            学生管理-班主任事务
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.PStudentAdd(Hyun.Core.Model.PStudentDto)">
            <summary>
            单位管理员新增学生信息
            </summary>
            <param name="obj">PStudentDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.PStudentEdit(Hyun.Core.Model.PStudentDto)">
            <summary>
            修改学生信息
            </summary>
            <param name="obj">PStudentDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.PStudentByEditId(System.Int64)">
            <summary>
            根据Id查询学生信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.PStudentDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.PStudentGetPaged(Hyun.Core.Model.PStudentParam)">
            <summary>
            区县、市级查询列表
            </summary>
            <param name="param">PStudentParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.PStudentImport(Hyun.Core.Model.SearchModels.Common.ImportParam)">
            <summary>
            单位管理员导入班级学生信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.ExportSchoolStudent(Hyun.Core.Model.PStudentParam)">
            <summary>
            单位导出学生信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.GetPagedByTeacher(Hyun.Core.Model.PStudentParam)">
            <summary>
            班主任学生信息列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.TeacherStudentAdd(Hyun.Core.Model.PStudentModel)">
            <summary>
            班主任添加学生信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.TeacherStudentEdit(Hyun.Core.Model.PStudentModel)">
            <summary>
            班主任修改学生信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.TeacherStudentDeleteById(System.Int64)">
            <summary>
            班主任删除学生信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.TeacherImportStudent(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            班主任导入学生信息
            </summary>
            <param name="fileList"></param>
            <returns></returns>x
        </member>
        <member name="M:Hyun.Core.Api.PStudentController.ExportTeacherStudent(Hyun.Core.Model.PStudentParam)">
            <summary>
            班主任导出学生信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.PSupplierSchoolAuditController">
            <summary>
            单位和企业认证审核
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.PSupplierSchoolAuditController.PSupplierSchoolAuditGetPaged(Hyun.Core.Model.PSupplierSchoolAuditParam)">
            <summary>
            超管查询待审核认证信息列表
            </summary>
            <param name="param">PSupplierSchoolAuditParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PSupplierSchoolAuditController.PSupplierSchoolApplyCertification(Hyun.Core.Model.PSupplierSchoolAuditDto)">
            <summary>
            单位、企业申请认证
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PSupplierSchoolAuditController.PSupplierSchoolMyUnit">
            <summary>
            获取当前单位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PSupplierSchoolAuditController.PSupplierSchoolAuditById(System.Int64)">
            <summary>
            根据认证表Id获取待审核信息
            </summary>
            <param name="id">认证表Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PSupplierSchoolAuditController.UnitAudit(Hyun.Core.Model.SuperAuditDto)">
            <summary>
            超管审核不修改内容
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.PSupplierSchoolAuditController.DeleteFileById(System.Int64)">
            <summary>
            删除单位企业申请认证附件
            </summary>
            <param name="id">附件Id</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.XUniformArticleController">
            <summary>
            资讯管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetXfCategoryPaged(Hyun.Core.Model.XfCategoryParam)">
            <summary>
            获取资讯类别列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleCategoryAdd(Hyun.Core.Model.DArticleCategoryModel)">
            <summary>
            添加修改资讯类别
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleCategoryEditById(System.Int64)">
            <summary>
            根据资讯分类Id查询资讯类别信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleCategoryEdit(Hyun.Core.Model.DArticleCategoryModel)">
            <summary>
            修改资讯类别
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleCategoryDel(System.Int64)">
            <summary>
            删除资讯类别
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetXfCategoryPaged(Hyun.Core.Model.XfArticleParam)">
            <summary>
            获取资讯列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleAdd(Hyun.Core.Model.Models.DArticleModel)">
            <summary>
            添加资讯
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleEditById(System.Int64)">
            <summary>
            根据资讯Id查询资讯信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleEdit(Hyun.Core.Model.Models.DArticleModel)">
            <summary>
            修改资讯信息
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticleDel(System.Int64)">
            <summary>
            删除资讯信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.ArticlePub(System.Int64)">
            <summary>
            发布/取消发布
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetInformation(System.Int32)">
            <summary>
            获取资讯首页信息
            </summary>
            <param name="code">对应具体的编码</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetInformationListByCid(System.Int64)">
            <summary>
            根据资讯分类Id查询资讯信息
            </summary>
            <param name="cid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetInformationDetail(System.Int64)">
            <summary>
            根据资讯Id查询资讯详情信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetOperator(System.Int64,System.Int32)">
            <summary>
            获取资讯分类及资讯分类第一条对应的资讯信息数据
            </summary>
            <param name="cid">资讯首页点更多传0，底部具体的分类点击传其Id</param>
            <param name="code">编码</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetOperatorList(System.Int64)">
            <summary>
            运营商资讯根据分类Id查询资讯信息
            </summary>
            <param name="cid">资讯分类Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetInformationByCateType(System.Int32,System.Int32)">
            <summary>
            根据资讯分类类型查询资讯信息(主要用于查询滚动图片)
            </summary>
            <param name="cateType">滚动图片传3</param>
            <param name="topCount">获取前多少条，0 默认获取所有</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformArticleController.GetBottomCateType(System.Int32)">
            <summary>
            获取底部资讯分类信息
            </summary>
            <param name="topCount">获取前多少条数据，0 默认获取所有</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformAuditLogController.XUniformAuditLogGetPaged(Hyun.Core.Model.XUniformAuditLogParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformAuditLogParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformAuditLogController.XUniformAuditLogById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformAuditLogController.XUniformAuditLogInsertUpdate(Hyun.Core.Model.XUniformAuditLogDto)">
            <summary>
            新增修改
            </summary>
            <param name="obj">XUniformAuditLogDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformAuditLogController.XUniformAuditLogFakeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformAuditLogController.XUniformAuditLogFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformAuditLogController.XUniformAuditLogDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformAuditLogController.XUniformAuditLogDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.XUniformCompanyController">
            <summary>
            校服维护
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.GetPaged(Hyun.Core.Model.XUniformCompanyParam)">
            <summary>
            校服采购-校服管理-查询列表（校服录入）
            </summary>
            <param name="param">XUniformListParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.GetEditById(System.Int64)">
            <summary>
            校服采购-校服管理-根据Id获取详情（供应商）
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.Add(Hyun.Core.Model.XUniformCompanyDto)">
            <summary>
            校服采购-校服管理-新增（供应商）
            </summary>
            <param name="model">XUniformListDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.Edit(Hyun.Core.Model.XUniformCompanyDto)">
            <summary>
            校服采购-校服管理-修改（供应商）
            </summary>
            <param name="model">XUniformListDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.Copy(System.Int64)">
            <summary>
            方案选用-复制
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.DeleteById(System.Int64)">
            <summary>
            校服采购- 校服管理- 根据Id删除数据（供应商）
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.DeleteAttachmentById(System.Int64,System.Int64)">
            <summary>
            校服管理-校服录入-删除附件
            </summary>
            <param name="id">校服表Id</param>
            <param name="attid">附件Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.DeleteSizeById(System.Int64,System.Int64)">
            <summary>
            校服管理-校服录入-删除尺码
            </summary>
            <param name="id">校服表Id</param>
            <param name="sizeid">尺码表id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.SetSattuz(System.String,System.Int32)">
            <summary>
            校服采购- 校服管理- 设置状态（供应商）
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <param name="statuz">更新的状态</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformCompanyController.Submit(System.String)">
            <summary>
            校服采购- 校服管理- 提交审核（供应商）
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.XUniformConfigController">
            <summary>
            业务配置
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.XUniformConfigController.XUniformConfigGetPaged(System.String)">
            <summary>
            根据类型查询
            </summary>
            <param name="code">
            配置编码
            校服采购方式：1000，备案信息审核：2000，校服评价指标：3100，校服展示价格：4000
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformConfigController.XUniformConfigEdit(Hyun.Core.Model.XUniformConfigEntity)">
            <summary>
            修改校服评价指标
            </summary>
            <param name="o">
            XUniformConfig对象
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformConfigController.XUniformConfigSetId(System.Int64)">
            <summary>
            设置采购方式、备案信息审核、校服展示价格
            </summary>
            <param name="id">id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformConfigController.XUniformConfigById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.XUniformEvaluateController">
            <summary>
            校服评价
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.GetEvaluatePaged(Hyun.Core.Model.XUniformEvaluateParam)">
            <summary>
            查询校服评价列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.EvaluateLaunch(Hyun.Core.Model.Models.Uniform.XUniformEvaluateModel)">
            <summary>
            创建修改评价截止时间
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.GetEvaluateList(Hyun.Core.Model.XUniformEvaluateParam)">
            <summary>
            单位查看评价列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.GetEvaluateDetailList(Hyun.Core.Model.XUniformEvaluateDetailParam)">
            <summary>
            获取评价明细列表信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.ExportEvaluateDetailList(Hyun.Core.Model.XUniformEvaluateDetailParam)">
            <summary>
            导出评价明细信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.GetEvaluateByPurchaseId(System.Int64)">
            <summary>
            根据采购单Id获取需要填写的评价信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.UserEvaluate(Hyun.Core.Model.UniformEvaluateModel)">
            <summary>
            用户评价(无需登录)
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.GetCreateOrder(System.Int64)">
            <summary>
            校服明细查看
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.UserLoginEvaluate(Hyun.Core.Model.UniformEvaluateModel)">
            <summary>
            用户评价(需登录)
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateController.GetAreaName(System.Int64,System.String,System.Collections.Generic.List{Hyun.Core.Model.Models.BArea})">
            <summary>
            获取区域名称
            </summary>
            <param name="countyAreaId">区县区域Id</param>
            <param name="name">区县单位名称，默认值</param>
            <param name="listArea">区域集合</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateDetailController.XUniformEvaluateDetailGetPaged(Hyun.Core.Model.XUniformEvaluateDetailParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformEvaluateDetailParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateDetailController.XUniformEvaluateDetailById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateDetailController.XUniformEvaluateDetailInsertUpdate(Hyun.Core.Model.XUniformEvaluateDetailDto)">
            <summary>
            新增修改
            </summary>
            <param name="obj">XUniformEvaluateDetailDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateDetailController.XUniformEvaluateDetailFakeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateDetailController.XUniformEvaluateDetailFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateDetailController.XUniformEvaluateDetailDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformEvaluateDetailController.XUniformEvaluateDetailDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformHomeController.GetPageList(Hyun.Core.Model.XUniformShelfParam)">
            <summary>
            校服展示列表-根据查询条件获取数据集合
            </summary>
            <param name="param">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformHomeController.GetSexName(System.Int32)">
            <summary>
            获取性别名称0:未知  1：男  2：女 3：男/女
            </summary>
            <param name="sex"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrderDetailController.XUniformOrderDetailGetPaged(Hyun.Core.Model.XUniformOrderDetailParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformOrderDetailParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrderDetailController.XUniformOrderDetailById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrderDetailController.XUniformOrderDetailInsertUpdate(Hyun.Core.Model.XUniformOrderDetailDto)">
            <summary>
            新增修改
            </summary>
            <param name="obj">XUniformOrderDetailDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrderDetailController.XUniformOrderDetailFakeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrderDetailController.XUniformOrderDetailFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrderDetailController.XUniformOrderDetailDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrderDetailController.XUniformOrderDetailDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.XUniformOrganizationGetPaged(Hyun.Core.Model.XUniformOrganizationParam)">
            <summary>
            采购管理-选用组织-查询列表
            </summary>
            <param name="param">XUniformOrganizationParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.GetAreaName(System.Int64,System.String,System.Collections.Generic.List{Hyun.Core.Model.Models.BArea})">
            <summary>
            采购管理-选用组织-查询列表获取区域名称
            </summary>
            <param name="countyAreaId">区县区域Id</param>
            <param name="name">区县单位名称，默认值</param>
            <param name="listArea">区域集合</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.GetEditById(System.Int64)">
            <summary>
            采购管理-选用组织-根据Id获取详情信息
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.SaveEdit(Hyun.Core.Model.XUniformOrganizationDto)">
            <summary>
            采购管理-选用组织-保存信息
            </summary>
            <param name="model">XUniformOrganizationDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.DeleteAttachmentById(System.Int64,System.Int64)">
            <summary>
            方案选用-根据Id删除附件数据
            </summary>
            <param name="id">校服选用表Id</param>
            <param name="attid">附件Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.FilingSubmit(System.Int64)">
            <summary>
            方案选用-提交备案
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.FilingConfirm(System.Int64,System.Int32,System.String)">
            <summary>
            方案选用-区县审核
            </summary>
            <param name="id">方案标识</param>
            <param name="statuz">状态 1：通过  2：退回</param>
            <param name="explanation">说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformOrganizationController.FilingBackout(System.Int64,System.String)">
            <summary>
            方案选用-区县退回
            </summary>
            <param name="id">方案标识</param>
            <param name="explanation">说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformParentPurchaseController.XUniformPurchaseDetailGetPaged(Hyun.Core.Model.XUniformParentPurchaseParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformPurchaseDetailParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformParentPurchaseController.GetStudentPaged(Hyun.Core.Model.XUniformPurchaseGradeParam)">
            <summary>
            校服采购-校服征订-年级班级学生征订详情列表
            </summary>
            <param name="param">XUniformPurchaseClassParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformParentPurchaseController.GetParentPurchaseInfo(System.Int64,System.Int64)">
            <summary>
            校服采购-校服征订-获取家长征订学生校服信息
            </summary>
            <param name="id">采购征订表Id</param>
            <param name="studentid">学生Id，多个学生切换的时候会用到</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformParentPurchaseController.GetSexName(System.Int32)">
            <summary>
            获取性别名称0:未知  1：男  2：女 3：男/女
            </summary>
            <param name="sex"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformParentPurchaseController.GetTeacherStudentPaged(Hyun.Core.Model.XUniformPurchaseGradeParam)">
            <summary>
            校服采购-校服征订-年级班级学生征订详情列表（班主任）
            </summary>
            <param name="param">XUniformPurchaseClassParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformParentPurchaseController.ExportStudent(Hyun.Core.Model.XUniformPurchaseGradeParam)">
            <summary>
            校服采购-校服征订-导出学生征订信息
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformParentPurchaseController.ParentSubmit(Hyun.Core.Model.XUniformParentPurchaseDto)">
            <summary>
            校服管理-校服征订-家长征订
            </summary>
            <param name="model">XUniformPurchaseClassDto对象</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.XUniformPurchaseController">
            <summary>
            校服采购-采购管理-校服调换-校服资助
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetPaged(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            校服采购-采购管理-查询分页列表
            </summary>
            <param name="param">XUniformPurchaseParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetById(System.Int64)">
            <summary>
            校服采购-采购管理-根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetByIdInfo(System.Int64)">
            <summary>
            校服采购-采购管理-根据Id获取采购详情
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.Add(Hyun.Core.Model.XUniformPurchaseDto)">
            <summary>
            校服采购-采购管理-添加
            </summary>
            <param name="model">XUniformPurchaseDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.Edit(Hyun.Core.Model.XUniformPurchaseDto)">
            <summary>
            校服采购-采购管理-修改
            </summary>
            <param name="model">XUniformPurchaseDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.Submit(Hyun.Core.Model.XUniformPurchaseDto)">
            <summary>
            校服采购-采购管理-提交
            </summary>
            <param name="model">XUniformPurchaseDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.DeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.DeleteAttachmentById(System.Int64,System.Int64)">
            <summary>
            校服采购-根据Id删除附件数据
            </summary>
            <param name="id">校服选用表Id</param>
            <param name="attid">附件Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.FilingConfirm(System.Int64,System.Int32,System.String)">
            <summary>
            校服采购-区县审核
            </summary>
            <param name="id">方案标识</param>
            <param name="statuz">状态 1：通过  2：退回</param>
            <param name="explanation">说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.FilingBackout(System.Int64,System.String)">
            <summary>
            方案选用-区县退回
            </summary>
            <param name="id">方案标识</param>
            <param name="explanation">说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.FilingRevoked(System.Int64,System.String)">
            <summary>
            方案选用-区县撤回（撤回到上一级）
            </summary>
            <param name="id">方案标识</param>
            <param name="explanation">说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetSwapPaged(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            调换发起列表
            </summary>
            <param name="param">
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.SwapLaunch(Hyun.Core.Model.XUniformPurchaseSwapModel)">
            <summary>
            校服调换（发起-修改）
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetExchangeOrder(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            单位查看调换单
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetManagerOrder(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            供应商管理调换单
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetCompanySummaryPaged(Hyun.Core.Model.XUniformSwapDetailParam)">
            <summary>
            企业按汇总查看调换单
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.ExportCompanySummary(Hyun.Core.Model.XUniformSwapDetailParam)">
            <summary>
            企业导出汇总调换明细
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetSwapDetail(Hyun.Core.Model.XUniformSwapDetailParam)">
            <summary>
            单位企业根据采购Id查看调换明细(采购Id必传)【单位、企业使用】
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.ExportSwapDetail(Hyun.Core.Model.XUniformSwapDetailParam)">
            <summary>
            单位企业导出调换明细
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetTeacherOrder(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            班主任校服调换列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetSwapDetailByTeacher(Hyun.Core.Model.XUniformSwapDetailParam)">
            <summary>
            班主任根据采购Id查看调换明细(采购Id必传)【班主任使用】
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.ExportSwapDetailByTeacher(Hyun.Core.Model.XUniformSwapDetailParam)">
            <summary>
            班主任导出调换明细
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetChangeParentList">
            <summary>
            获取家长调换批次列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.UniformChange(Hyun.Core.Model.UniformChangeModel)">
            <summary>
            家长单条调换校服
            </summary>
            <param name="o">
            UniformChangeModel 实体
            </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.CompanyConfirm(System.Int64)">
            <summary>
            企业确认调换
            </summary>
            <param name="uniformPurchaseId">校服采购表Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetSchoolSponsorPaged(Hyun.Core.Model.XUniformSponsorParam)">
            <summary>
            单位校服资助列表显示
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetSponsorPaged(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            校服资助列表区县、市级
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetSponsorDetailPaged(System.Int64)">
            <summary>
            根据校服采购表Id获取校服资助表明细信息
            </summary>
            <param name="uniformPurchaseId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.SponsorAdd(Hyun.Core.Model.XuniformSponsorModel)">
            <summary>
            创建资助信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.SponsorEdit(Hyun.Core.Model.XuniformSponsorModel)">
            <summary>
            修改资助信息
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetSponsorById(System.Int64,System.Boolean)">
            <summary>
            根据校服采购Id获取校服资助详情
            </summary>
            <param name="id">校服采购id</param>
            <param name="isFirst">是否第一次调用</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.DeleteSponsorById(System.Int64)">
            <summary>
            删除资助信息
            </summary>
            <param name="id">校服采购Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.DeleteFileById(System.Int64)">
            <summary>
            删除校服资助附件
            </summary>
            <param name="id">附件Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetEditOrderPaged(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            校服征订-生成征订单
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetCreateOrder(System.Int64)">
            <summary>
            校服征订-获取征订单信息
            </summary>
            <param name="id">采购id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.SaveEditOrder(Hyun.Core.Model.XUniformPurchaseDto)">
            <summary>
            校服征订-保存征订单信息
            </summary>
            <param name="model">生成征订单信息{id:采购表Id ,GradeList选中年级Id集合}</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetOrderPaged(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            校服征订-查看征订单
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetAreaName(System.Int64,System.String,System.Collections.Generic.List{Hyun.Core.Model.Models.BArea})">
            <summary>
            获取区域名称
            </summary>
            <param name="countyAreaId">区县区域Id</param>
            <param name="name">区县单位名称，默认值</param>
            <param name="listArea">区域集合</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetParentPaged(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            校服征订-采购结果        
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetFilingStatuzPublicityName(Hyun.Core.Model.XUniformPurchaseDto)">
            <summary>
            方案选用-获取当前校服选用公示状态名称
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetOrderTeacherPaged(Hyun.Core.Model.XUniformPurchaseParam)">
            <summary>
            校服征订-查看征订单(班主任)
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetPurchaseListPaged(Hyun.Core.Model.XUniformPurchaseListParam)">
            <summary>
            合同履约校服采购清单-校服履约管理-查询分页列表
            </summary>
            <param name="param">XUniformPurchaseParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.GetPurchaseListById(System.Int64)">
            <summary>
            合同履约校服信息-合同履约-根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.PurchaseListEdit(Hyun.Core.Model.XUniformPurchaseListDto)">
            <summary>
            校服采购-采购管理-添加
            </summary>
            <param name="model">XUniformPurchaseDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseController.DeletePurchaseListById(System.Int64,System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <param name="purchaseid">采购表Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformPurchaseGradeController.GetPaged(Hyun.Core.Model.XUniformPurchaseGradeParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformPurchaseClassParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformQrCodeController.XUniformQrCodeGetPaged(Hyun.Core.Model.XUniformQrCodeParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformQrCodeParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformQrCodeController.XUniformQrCodeById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformQrCodeController.XUniformQrCodeInsertUpdate(Hyun.Core.Model.XUniformQrCodeDto)">
            <summary>
            新增修改
            </summary>
            <param name="obj">XUniformQrCodeDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformQrCodeController.XUniformQrCodeFakeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformQrCodeController.XUniformQrCodeFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformQrCodeController.XUniformQrCodeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformQrCodeController.XUniformQrCodeDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Api.XUniformSchemeController">
            <summary>
            校服选购
            </summary>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetPaged(Hyun.Core.Model.XUniformSchemeParam)">
            <summary>
            方案选用-查询
            </summary>
            <param name="param">XUniformSchemeParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetAreaName(System.Int64,System.String,System.Collections.Generic.List{Hyun.Core.Model.Models.BArea})">
            <summary>
            获取区域名称
            </summary>
            <param name="countyAreaId">区县区域Id</param>
            <param name="name">区县单位名称，默认值</param>
            <param name="listArea">区域集合</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetParentPaged(Hyun.Core.Model.XUniformSchemeParam)">
            <summary>
            方案选用-查询(家长展示公示结果)
            </summary>
            <param name="param">XUniformSchemeParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetFilingStatuzPublicityName(Hyun.Core.Model.XUniformSchemeDto)">
            <summary>
            方案选用-获取当前校服选用公示状态名称
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetFilingStatuzName(Hyun.Core.Model.XUniformSchemeDto)">
            <summary>
            方案选用-获取当前校服选用备案状态名称
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetSolicitedStatuzName(Hyun.Core.Model.XUniformSchemeDto)">
            <summary>
            方案选用-获取当前校服选用状态名称
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetById(System.Int64)">
            <summary>
            方案选用-根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetSolicitedById(System.Int64)">
            <summary>
            方案选用-家长扫码填报获取详情
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetPreview(System.Int64)">
            <summary>
            方案选用-扫码填报获取详情预览
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetParentInfo(System.Int64)">
            <summary>
            方案选用-家长查看公示结果
            </summary>
            <param name="id">选用方案Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetPurchaseMethod(System.Int64)">
            <summary>
            方案选用-获取采购方式。
            </summary>
            <param name="id">单位Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.GetOpinionPaged(Hyun.Core.Model.XUniformSchemeOpinionParam)">
            <summary>
            方案选用-查询意见详情列表
            </summary>
            <param name="param">XUniformSchemeParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.ExportOpinion(Hyun.Core.Model.XUniformSchemeOpinionParam)">
            <summary>
            方案选用-导出意见详情列表
            </summary>
            <param name="param">XUniformSchemeParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.Add(Hyun.Core.Model.XUniformSchemeDto)">
            <summary>
            方案选用-保存（添加）OptStatuz = 0：添加
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.Edit(Hyun.Core.Model.XUniformSchemeDto)">
            <summary>
            方案选用-保存（修改）OptStatuz = 0：修改 保存
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.Post(Hyun.Core.Model.XUniformSchemeDto)">
            <summary>
            方案选用-保存（发布）OptStatuz = 1：发布
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.DeleteById(System.Int64)">
            <summary>
            方案选用-根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.DeleteAttachmentById(System.Int64,System.Int64)">
            <summary>
            方案选用-根据Id删除附件数据
            </summary>
            <param name="id">校服选用表Id</param>
            <param name="attid">附件Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.PublishCancel(System.Int64)">
            <summary>
            方案选用-发布撤销
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.Copy(System.Int64)">
            <summary>
            方案选用-复制
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.PostOpinion(Hyun.Core.Model.XUniformSchemeOpinionDto)">
            <summary>
            方案选用-保存家长意见（添加、修改）
            </summary>
            <param name="request"></param>
            <remarks>
            2025-09-04 by  lss 不登录，
            1：直接存在班级学生姓名、手机号，不去找学生匹配
            2：是否还需要修改，什么情况下修改
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.FilingSubmit(System.Int64)">
            <summary>
            方案选用-提交备案
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.FilingConfirm(System.Int64,System.Int32,System.String)">
            <summary>
            方案选用-区县审核
            </summary>
            <param name="id">方案标识</param>
            <param name="statuz">状态 1：通过  2：退回</param>
            <param name="explanation">说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeController.FilingBackout(System.Int64,System.String)">
            <summary>
            方案选用-区县退回
            </summary>
            <param name="id">方案标识</param>
            <param name="explanation">说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeOpinionController.XUniformSchemeOpinionFind(Hyun.Core.Model.XUniformSchemeOpinionParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformSchemeOpinionParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeOpinionController.XUniformSchemeOpinionById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeOpinionController.XUniformSchemeOpinionFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeOpinionController.XUniformSchemeOpinionDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSchemeOpinionController.XUniformSchemeOpinionDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.XUniformShowGetPaged(Hyun.Core.Model.XUniformShelfParam)">
            <summary>
            校服采购-校服管理-已提交列表（供应商）
            </summary>
            <param name="param">XUniformShowParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.XUniformShowGetWaitAuditPaged(Hyun.Core.Model.XUniformShelfParam)">
            <summary>
            校服采购-校服管理-待审核列表（单位）
            </summary>
            <param name="param">XUniformShowParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.XUniformShowGetAuditedPaged(Hyun.Core.Model.XUniformShelfParam)">
            <summary>
            校服采购-校服管理-已审核列表（单位）
            </summary>
            <param name="param">XUniformShowParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.GetPageList(Hyun.Core.Model.XUniformShelfParam)">
            <summary>
            校服展示列表-根据查询条件获取数据集合
            </summary>
            <param name="param">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.GetSexName(System.Int32)">
            <summary>
            获取性别名称0:未知  1：男  2：女 3：男/女
            </summary>
            <param name="sex"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.XUniformShowById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.GetById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.SetSortById(System.Int64,System.Int32)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <param name="sort">排序</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.Audit(System.Int64,System.Int32,System.Int64,System.String)">
            <summary>
            校服审核-审核（单位）
            </summary>
            <param name="id">校服上架表Id</param>
            <param name="statuz">审核状态1：通过  0或2审核不通过</param>
            <param name="purchaseid">合同批次Id</param>
            <param name="explain">审核说明</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.DeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.Revoked(System.Int64)">
            <summary>
            校服管理-已审审核列表-撤销
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.SetStatuz(System.Int64,System.Int32)">
            <summary>
            校服管理-已提交审核列表-设置状态（启用禁用）
            </summary>
            <param name="id">Id值</param>
            <param name="statuz">1：启用  2：禁用</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformShelfController.SetShowStatuz(System.Int64,System.Int32)">
            <summary>
            校服管理-管理员设置是否展示
            </summary>
            <param name="id">Id值</param>
            <param name="statuz">1：展示  2：否不展示</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeController.XUniformSizeGetPaged(Hyun.Core.Model.XUniformSizeParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformSizeParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeController.XUniformSizeById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeController.XUniformSizeFakeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeController.XUniformSizeFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeController.XUniformSizeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeController.XUniformSizeDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeShowController.XUniformSizeShowGetPaged(Hyun.Core.Model.XUniformSizeShowParam)">
            <summary>
            查询列表
            </summary>
            <param name="param">XUniformSizeShowParam对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeShowController.XUniformSizeShowById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeShowController.XUniformSizeShowFakeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeShowController.XUniformSizeShowFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeShowController.XUniformSizeShowDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSizeShowController.XUniformSizeShowDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSwapDetailController.XUniformSwapDetailById(System.Int64)">
            <summary>
            根据Id获取对象
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSwapDetailController.XUniformSwapDetailInsertUpdate(Hyun.Core.Model.XUniformChangeDto)">
            <summary>
            新增修改
            </summary>
            <param name="obj">XUniformSwapDetailDto对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSwapDetailController.XUniformSwapDetailFakeDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【假删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSwapDetailController.XUniformSwapDetailFakeDeleteByIds(System.String)">
            <summary>
            根据Id集合删除数据【假删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSwapDetailController.XUniformSwapDetailDeleteById(System.Int64)">
            <summary>
            根据Id删除数据【真删除】
            </summary>
            <param name="id">Id值</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformSwapDetailController.XUniformSwapDetailDeleteByIds(System.String)">
            <summary>
            根据Id集合批量删除数据【真删除】
            </summary>
            <param name="ids">Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformUserController.MyUnitAddUser(Hyun.Core.Model.Models.SysUserInfo)">
            <summary>
            新增本单位用户
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformUserController.MyUnitEditUser(Hyun.Core.Model.Models.SysUserInfo)">
            <summary>
            修改本单位用户
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformUserController.UserMyUnit_UpdateUserStatuz(System.Int64)">
            <summary>
            启用禁单位用户
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Api.XUniformUserController.UserMyUnit_DeleteUser(System.Int64)">
            <summary>
            删除用户
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="P:Hyun.Core.Api.Models.UserValidateModel.AccountId">
            <summary>
            账号Id
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.UserValidateModel.UserValidate">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.RoleSetModel.ChkRoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.RoleSetModel.RoleIds">
            <summary>
            角色Id集合
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.RoleSetModel.UnitType">
            <summary>
            单位类别
            </summary>
        </member>
        <member name="T:Hyun.Core.Api.Models.ThirdEasyGrantModel">
            <summary>
            
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.TeamBuid.Id">
            <summary>
            Id值
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.TeamBuid.TxtValue">
            <summary>
            文本内容值
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.CompanySubmitModel.DetailAddress">
            <summary>
            详细地址
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.WxWorkTokenResult.errcode">
            <summary>
            错误编码
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.WxWorkTokenResult.errmsg">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.WxWorkTokenResult.access_token">
            <summary>
            token值
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.WxWorkTokenResult.expires_in">
            <summary>
            过期时长
            </summary>
        </member>
        <member name="T:Hyun.Core.Api.Models.WxWorkUserListResult">
            <summary>
            获取企业微信用户列表接口返回实体
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.WxWorkUserListResult.errcode">
            <summary>
            错误编码
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.WxWorkUserListResult.errmsg">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.WxWorkUserListResult.dept_user">
            <summary>
            用户列表
            </summary>
        </member>
        <member name="T:Hyun.Core.Api.Models.Dept_userItem">
            <summary>
            企业微信用户
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.Dept_userItem.userid">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:Hyun.Core.Api.Models.Dept_userItem.department">
            <summary>
            部门id
            </summary>
        </member>
        <member name="T:Hyun.Core.Controllers.BlogController">
            <summary>
            博客管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.#ctor(Microsoft.Extensions.Logging.ILogger{Hyun.Core.Controllers.BlogController})">
            <summary>
            构造函数
            </summary>
            <param name="logger"></param>
            
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.Get(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获取博客列表【无权限】
            </summary>
            <param name="id"></param>
            <param name="page"></param>
            <param name="bcategory"></param>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.Get(System.Int64)">
            <summary>
            获取博客详情
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.DetailNuxtNoPer(System.Int64)">
            <summary>
            获取详情【无权限】
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.V2_Blogtest">
            <summary>
            获取博客测试信息 v2版本
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.Post(Hyun.Core.Model.Models.BlogArticle)">
            <summary>
            添加博客【无权限】
            </summary>
            <param name="blogArticle"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.AddForMVP(Hyun.Core.Model.Models.BlogArticle)">
            <summary>
            
            </summary>
            <param name="blogArticle"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.Put(Hyun.Core.Model.Models.BlogArticle)">
            <summary>
            更新博客信息
            </summary>
            <param name="BlogArticle"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.Delete(System.Int64)">
            <summary>
            删除博客
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.BlogController.ApacheTestUpdate">
            <summary>
            apache jemeter 压力测试
            更新接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.#ctor(SqlSugar.ISqlSugarClient,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.GetFrameFiles">
            <summary>
            获取 整体框架 文件(主库)(一般可用第一次生成)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.GetFrameFilesByTableNames(System.String[],System.String)">
            <summary>
            获取仓储层和服务层(需指定表名和数据库)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.GetFrameFilesByTableNamesForEntity(System.String[],System.String)">
            <summary>
            获取实体(需指定表名和数据库)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.GetFrameFilesByTableNamesForController(System.String[],System.String)">
            <summary>
            获取控制器(需指定表名和数据库)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.GetAllFrameFilesByTableNames(System.String[],System.String)">
            <summary>
            DbFrist 根据数据库表名 生成整体框架,包含Model层(一般可用第一次生成)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.GetModelsByTableNames(System.String,System.String,System.String,System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            根据装备平台表生成InputModel、ViewModel、ParamModel、接口、Service方法
            </summary>
            <param name="tableName">【非必填】表名称，默认生成所有表。1).可以模糊表名；例如：填写“lv_”会把表名称中包含“lv_”的表都生成；2).多个表名可以用“,”隔开，例如：dc_ModelStock,dc_ModelStockHistory,dc_Inventory</param>
            <param name="ConnID">【非必填】数据库链接ID</param>
            <param name="filePath">【非必填】文件存储路径，默认存储“C:\\MyFile”下；例如：可填写“d:\test”</param>
            <param name="isGenerateView">【非必填】是否生成视图（默认不生成）</param>
            <param name="inputSpace">【非必填】InputModel实体生成命名空间，默认：Hyun.Core.Model.InputModels</param>
            <param name="viewSpace">【非必填】ViewMolde实体生成命名空间，默认：Hyun.Core.Model.ViewModels</param>
            <param name="paramSpace">【非必填】ParamModel实体生成命名空间，默认：Hyun.Core.Model.SearchModels</param>
            <param name="interfaceSpace">【非必填】接口生成命名空间，默认：Hyun.Core.IServices</param>
            <param name="serviceSpace">【非必填】Service生成命名空间，默认：Hyun.Core.Services.Common</param>
            <param name="controllerSpace">【非必填】Controller生成命名空间，默认：Hyun.Core.Api.Controllers</param>
            <param name="validatorSpace">Model验证命名空间，默认：Hyun.Core.Model.Validator</param>
            <param name="priority">【非必填】默认值1生成基础类Controller， 生成Controller等级,根据表“t_WebServiceMain”中字段“PRIORITY”控制生成控制器</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.DbFirstController.GetCommonModelsByTableNames(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            通用平台表生成InputModel、ViewModel、ParamModel、接口、Service方法
            </summary>
            <param name="tableName">【非必填】表名称，默认生成所有表。1).可以模糊表名；例如：填写“lv_”会把表名称中包含“lv_”的表都生成；2).多个表名可以用“,”隔开，例如：dc_ModelStock,dc_ModelStockHistory,dc_Inventory</param>
            <param name="ConnID">【非必填】数据库链接ID</param>
            <param name="filePath">【非必填】文件存储路径，默认存储“C:\\MyFile”下；例如：可填写“d:\test”</param>
            <param name="inputSpace">【非必填】InputModel实体生成命名空间，默认：Hyun.Core.Model.InputModels</param>
            <param name="viewSpace">【非必填】ViewMolde实体生成命名空间，默认：Hyun.Core.Model.ViewModels</param>
            <param name="paramSpace">【非必填】ParamModel实体生成命名空间，默认：Hyun.Core.Model.SearchModels</param>
            <param name="interfaceSpace">【非必填】接口生成命名空间，默认：Hyun.Core.IServices</param>
            <param name="serviceSpace">【非必填】Service生成命名空间，默认：Hyun.Core.Services.Common</param>
            <param name="controllerSpace">【非必填】Controller生成命名空间，默认：Hyun.Core.Api.Controllers</param>
            <param name="validatorSpace">Model验证命名空间，默认：Hyun.Core.Model.Validator</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.MigrateController.DataMigrateFromOld2New">
            <summary>
            获取权限部分Map数据（从库）
            迁移到新库（主库）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.MigrateController.SaveData2TsvAsync">
            <summary>
            权限数据库导出tsv
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.MigrateController.SaveData2ExcelAsync">
            <summary>
            权限数据库导出excel
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.HealthCheckController">
            <summary>
            健康检查
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.HealthCheckController.Get">
            <summary>
            健康检查接口
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.ImgController">
            <summary>
            图片管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.ImgController.DownImg">
            <summary>
            下载图片（支持中文字符）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ImgController.InsertPicture(Hyun.Core.Model.ViewModels.UploadFileDto)">
            <summary>
            上传图片,多文件
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.LoginHyunController">
            <summary>
            Hyun登录管理【无权限】
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.#ctor(Hyun.Core.IServices.ISysUserInfoServices,Hyun.Core.IServices.ISysUserRoleServices,Hyun.Core.IServices.ISysRoleServices,Hyun.Core.Model.PermissionRequirement,Hyun.Core.IServices.IRoleModulePermissionServices,Microsoft.Extensions.Logging.ILogger{Hyun.Core.Controllers.LoginHyunController},Hyun.Core.IServices.IVUserDetailServices,Hyun.Core.IServices.IBWebSiteConfigServices,Hyun.Core.IServices.ISysUserInfoServices,Hyun.Core.IServices.IBUserActionLogServices,Microsoft.Extensions.Caching.Memory.IMemoryCache,Hyun.Core.IServices.IBSmsHistoryValidateServices,Hyun.Core.IServices.IPUnitServices,Hyun.Core.Common.HttpContextUser.IUser,Hyun.Core.IServices.IBConfigSetServices)">
            <summary>
            构造函数注入
            </summary>
            <param name="sysUserInfoServices"></param>
            <param name="userRoleServices"></param>
            <param name="roleServices"></param>
            <param name="requirement"></param>
            <param name="roleModulePermissionServices"></param>
            <param name="logger"></param>
            <param name="vUserManager"></param>
            <param name="webSiteConfigManager"></param>
            <param name="acountServices"></param>
            <param name="userActionLogManager"></param>
            <param name="memoryCache"></param>
            <param name="_smsHistoryValidateManager"></param>
            <param name="_unitManager"></param>
            <param name="_user"></param>
            <param name="bconfigManager"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.GetJwtToken3(System.String,System.String,System.String,System.String)">
            <summary>
            用户登录
            </summary>
            <param name="Name"></param>
            <param name="Pass"></param>
            <param name="Code"></param>
            <param name="Uuid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.GetCaptchaImage">
            <summary>
            生成验证码
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.ValidationCode(System.String,System.String,System.String)">
            <summary>
            效验登录验证码,无需和账号绑定(1:正确；2：过期；3：错误)
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.Unit_EnterprisesRegistered(Hyun.Core.Api.Models.CompanyRegisterModel)">
            <summary>
            单位管理：注册企业
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.Unit_EnterprisesCertification(Hyun.Core.Api.Models.CompanySubmitModel)">
            <summary>
            单位管理：企业注册保存
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.User_ExamineAccount(Hyun.Core.Api.Models.FindUserPwdModel)">
            <summary>
            用户管理：密码找回（根据手机号码发送短信）
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.User_EditChangePass(Hyun.Core.Api.Models.FindUserPwdModel)">
            <summary>
            用户管理：密码找回
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.RefreshToken(System.String)">
            <summary>
            请求刷新Token（以旧换新）
            </summary>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.GetValidateCode(System.String,System.Int32,System.Int32)">
            <summary>
            获取手机验证码
            </summary>
            <param name="phoneNumber"></param>
            <param name="validateType">传3（家长、教师登录）</param>
            <param name="userType">5:家长；6：班主任，1：普通用户</param>
            <returns>验证码序列号，total 为验证码长度</returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.GetUserLoginJwt(System.String,System.String,System.String,System.Int32)">
            <summary>
            通过手机注册登录，仅适用班主任和家长
            </summary>
            <param name="phoneNumber"></param>
            <param name="validateCode">验证码</param>
            <param name="uuid">验证码Id</param>
            <param name="userType">5:家长；6：班主任</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.LoginHyunController.XfSchoolCompany_Registered(Hyun.Core.Model.UnitRegisterModel)">
            <summary>
            校服管理平台单位及企业注册
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.ModuleController">
            <summary>
            接口管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.Get(System.Int32,System.String)">
            <summary>
            获取全部接口api
            </summary>
            <param name="page"></param>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.Post(Hyun.Core.Model.Models.SysModules)">
            <summary>
            添加一条接口信息
            </summary>
            <param name="module"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.save(Hyun.Core.Model.Models.SysModules)">
            <summary>
            保存接口权限信息
            </summary>
            <param name="module"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.Put(Hyun.Core.Model.Models.SysModules)">
            <summary>
            更新接口信息
            </summary>
            <param name="module"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.Delete(System.Int64)">
            <summary>
            删除一条接口
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.BatchPost(System.Collections.Generic.List{Hyun.Core.Model.Models.SysModules})">
            <summary>
            导入多条接口信息
            </summary>
            <param name="modules"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.modulefind(Hyun.Core.Model.SysModulesParam)">
            <summary>
            适用于装备平台的查询全部接口api方法
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.deletebatch(System.String)">
            <summary>
            批量删除
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.getbyid(System.Int64)">
            <summary>
            根据id获取接口信息
            </summary>
            <param name="id">用户Id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.update(Hyun.Core.Model.Models.SysModules)">
            <summary>
            更新接口信息
            </summary>
            <param name="module"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ModuleController.syncapimodules">
            <summary>
            同步系统接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.MonitorController.Server">
            <summary>
            服务器配置信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.MonitorController.Get">
            <summary>
            SignalR send data
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.PayController">
            <summary>
            建行聚合支付类
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.PayController.#ctor(Microsoft.Extensions.Logging.ILogger{Hyun.Core.Controllers.PayController},Hyun.Core.IServices.IPayServices)">
            <summary>
            构造函数
            </summary> 
            <param name="logger"></param>
            <param name="payServices"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.PayController.PayGet(Hyun.Core.Model.ViewModels.PayNeedModel)">
            <summary>
            被扫支付
            </summary>
            <param name="payModel"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PayController.PayPost(Hyun.Core.Model.ViewModels.PayNeedModel)">
            <summary>
            被扫支付
            </summary>
            <param name="payModel"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PayController.PayCheckGet(Hyun.Core.Model.ViewModels.PayNeedModel)">
            <summary>
            支付结果查询-轮询
            </summary>
            <param name="payModel"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PayController.PayCheckPost(Hyun.Core.Model.ViewModels.PayNeedModel)">
            <summary>
            支付结果查询-轮询
            </summary>
            <param name="payModel"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PayController.PayRefundGet(Hyun.Core.Model.ViewModels.PayRefundNeedModel)">
            <summary>
            退款
            </summary>
            <param name="payModel"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PayController.PayRefundPost(Hyun.Core.Model.ViewModels.PayRefundNeedModel)">
            <summary>
            退款
            </summary>
            <param name="payModel"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.PermissionController">
            <summary>
            菜单管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.Get(System.Int32,System.String,System.Int32)">
            <summary>
            获取菜单
            </summary>
            <param name="page"></param>
            <param name="key"></param>
            <param name="pageSize"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.GetTreeTable(System.Int64,System.String)">
            <summary>
            查询树形 Table
            </summary>
            <param name="f">父节点</param>
            <param name="key">关键字</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.Post(Hyun.Core.Model.Models.SysPermission)">
            <summary>
            添加一个菜单
            </summary>
            <param name="permission"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.Assign(Hyun.Core.Controllers.AssignView)">
            <summary>
            保存菜单权限分配
            </summary>
            <param name="assignView"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.GetPermissionTree(System.Int64,System.Boolean)">
            <summary>
            获取菜单树
            </summary>
            <param name="pid"></param>
            <param name="needbtn"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.GetNavigationBar(System.Int64,System.Int32,System.Collections.Generic.List{System.Int32})">
            <summary>
            获取路由树
            </summary>
            <param name="uid"></param>
            <param name="appType">菜单类型（1：PC端 2：移动）</param>
            <param name="platformType">平台类型（危化品、校服、低值易耗品）暂未支持，先默认0</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.GetNavigationBarPro(System.Int64)">
            <summary>
            获取路由树
            </summary>
            <param name="uid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.GetPermissionIdByRoleId(System.Int64)">
            <summary>
            通过角色获取菜单
            </summary>
            <param name="rid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.Put(Hyun.Core.Model.Models.SysPermission)">
            <summary>
            更新菜单
            </summary>
            <param name="permission"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.Delete(System.Int64)">
            <summary>
            删除菜单
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.BatchPost(System.Collections.Generic.List{Hyun.Core.Model.Models.SysPermission})">
            <summary>
            导入多条菜单信息
            </summary>
            <param name="permissions"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.GetModule(System.Int32,System.Int32,System.String,System.Int64)">
            <summary>
            获取页面添加接口api列表（排查已添加的）
            </summary>
            <param name="page"></param>
            <param name="pageSize"></param>
            <param name="key"></param>
            <param name="permisionid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.PostApi(System.Int64,System.Int64)">
            <summary>
            添加菜单Api
            </summary>
            <param name="permissionid"></param>
            <param name="moduleid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.DeleteApi(System.Int64,System.Int64)">
            <summary>
            删除菜单Api
            </summary>
            <param name="permissionid"></param>
            <param name="moduleid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.PermissionController.MigratePermission(System.String,System.String,System.String,System.String,System.String,System.Int64,System.Boolean)">
            <summary>
            系统接口菜单同步接口
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.RoleController">
            <summary>
            角色管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.RoleController.Get(System.Int32,System.Int32,System.String)">
            <summary>
            获取全部角色
            </summary>
            <param name="page"></param>
            <param name="key"></param>
            <param name="pageSize"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.RoleController.Post(Hyun.Core.Model.SysRoleDto)">
            <summary>
            添加角色
            </summary>
            <param name="role"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.RoleController.Put(Hyun.Core.Model.SysRoleDto)">
            <summary>
            更新角色
            </summary>
            <param name="role"></param>
            <returns></returns>+
        </member>
        <member name="M:Hyun.Core.Controllers.RoleController.Delete(System.Int64)">
            <summary>
            删除角色
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.Get(Hyun.Core.Model.SearchModels.Tasks.TasksQzParam)">
            <summary>
            分页获取
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.Post(Hyun.Core.Model.Models.TasksQz)">
            <summary>
            添加计划任务
            </summary>
            <param name="tasksQz"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.Put(Hyun.Core.Model.Models.TasksQz)">
            <summary>
            修改计划任务
            </summary>
            <param name="tasksQz"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.Delete(System.Int64)">
            <summary>
            删除一个任务
            </summary>
            <param name="Id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.StartJob(System.Int64)">
            <summary>
            启动计划任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.StopJob(System.Int64)">
            <summary>
            停止一个计划任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>        
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.PauseJob(System.Int64)">
            <summary>
            暂停一个计划任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>        
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.ResumeJob(System.Int64)">
            <summary>
            恢复一个计划任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>        
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.ReCovery(System.Int64)">
            <summary>
            重启一个计划任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.GetTaskNameSpace">
            <summary>
            获取任务命名空间
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.ExecuteJob(System.Int64)">
            <summary>
            立即执行任务
            </summary>
            <param name="jobId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.GetTaskLogs(Hyun.Core.Model.SearchModels.Tasks.TasksLogParam)">
            <summary>
            获取任务运行日志
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.GetTaskOverview(System.Int64,System.Int32,System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String)">
            <summary>
            任务概况
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.tasksqzfind(Hyun.Core.Model.SearchModels.Common.TasksqzParam)">
            <summary>
            适用于装备平台的查询所有任务计划
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.deletebatch(System.String)">
            <summary>
            批量删除
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.getbyid(System.Int64)">
            <summary>
            根据id获取任务计划详情
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.save(Hyun.Core.Model.Models.TasksQz)">
            <summary>
            保存任务计划
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TasksQzController.update(Hyun.Core.Model.Models.TasksQz)">
            <summary>
            修改任务计划
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.TopicController">
            <summary>
            类别管理【无权限】
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicController.#ctor(Hyun.Core.IServices.ITopicServices)">
            <summary>
            构造函数
            </summary>
            <param name="topicServices"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicController.Get">
            <summary>
            获取Tibug所有分类
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.TopicDetailController">
            <summary>
            Tibug 管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicDetailController.#ctor(Hyun.Core.IServices.ITopicServices,Hyun.Core.IServices.ITopicDetailServices)">
            <summary>
            构造函数
            </summary>
            <param name="topicServices"></param>
            <param name="topicDetailServices"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicDetailController.Get(System.Int32,System.String,System.String,System.Int32)">
            <summary>
            获取Bug数据列表（带分页）
            【无权限】
            </summary>
            <param name="page">页数</param>
            <param name="tname">专题类型</param>
            <param name="key">关键字</param>
            <param name="intPageSize"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicDetailController.Get(System.Int64)">
            <summary>
            获取详情【无权限】
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicDetailController.Post(Hyun.Core.Model.Models.TopicDetail)">
            <summary>
            添加一个 BUG 【无权限】
            </summary>
            <param name="topicDetail"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicDetailController.Update(Hyun.Core.Model.Models.TopicDetail)">
            <summary>
            更新 bug
            </summary>
            <param name="topicDetail"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TopicDetailController.Delete(System.Int64)">
            <summary>
            删除 bug
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.TransactionController.Delete(System.Int64)">
            <summary>
            测试事务在AOP中的使用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.UserController">
            <summary>
            用户管理
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.UserController.#ctor(Hyun.Core.Repository.UnitOfWorks.IUnitOfWorkManage,Hyun.Core.IServices.ISysUserInfoServices,Hyun.Core.IServices.ISysUserRoleServices,Hyun.Core.IServices.ISysRoleServices,Hyun.Core.IServices.IDepartmentServices,Hyun.Core.Common.HttpContextUser.IUser,AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{Hyun.Core.Controllers.UserController})">
            <summary>
            构造函数
            </summary>
            <param name="unitOfWorkManage"></param>
            <param name="sysUserInfoServices"></param>
            <param name="userRoleServices"></param>
            <param name="roleServices"></param>
            <param name="departmentServices"></param>
            <param name="user"></param>
            <param name="mapper"></param>
            <param name="logger"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.UserController.Get(System.Int32,System.String)">
            <summary>
            获取全部用户
            </summary>
            <param name="page"></param>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserController.GetInfoByToken(System.String)">
            <summary>
            获取用户详情根据token
            【无权限】
            </summary>
            <param name="token">令牌</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserController.Post(Hyun.Core.Model.ViewModels.SysUserInfoDto)">
            <summary>
            添加一个用户
            </summary>
            <param name="sysUserInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserController.Put(Hyun.Core.Model.ViewModels.SysUserInfoDto)">
            <summary>
            更新用户与角色
            </summary>
            <param name="sysUserInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserController.Delete(System.Int64)">
            <summary>
            删除用户
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserController.test">
            <summary>
            测试
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.UserRoleController">
            <summary>
            用户角色关系
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.UserRoleController.#ctor(Hyun.Core.IServices.ISysUserInfoServices,Hyun.Core.IServices.ISysUserRoleServices,AutoMapper.IMapper,Hyun.Core.IServices.ISysRoleServices)">
            <summary>
            构造函数
            </summary>
            <param name="sysUserInfoServices"></param>
            <param name="userRoleServices"></param>
            <param name="mapper"></param>
            <param name="roleServices"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.UserRoleController.AddUser(System.String,System.String)">
            <summary>
            新建用户
            </summary>
            <param name="loginName"></param>
            <param name="loginPwd"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserRoleController.AddRole(System.String)">
            <summary>
            新建Role
            </summary>
            <param name="roleName"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserRoleController.AddUserRole(System.Int64,System.Int64)">
            <summary>
            新建用户角色关系
            </summary>
            <param name="uid"></param>
            <param name="rid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.UserRoleController.SaveRole(Hyun.Core.Model.SysRoleDto)">
            <summary>
            保存Role
            </summary>
            <param name="r"></param>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.ValuesController">
            <summary>
            Values控制器
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.#ctor(Hyun.Core.IServices.IBlogArticleServices,AutoMapper.IMapper,Hyun.Core.IServices.IAdvertisementServices,Hyun.Core.Model.Love,Hyun.Core.IServices.IRoleModulePermissionServices,Hyun.Core.Common.HttpContextUser.IUser,Hyun.Core.IServices.IPasswordLibServices,Hyun.Core.Common.Https.HttpPolly.IHttpPollyHelper,Hyun.Core.EventBus.IRabbitMQPersistentConnection,Microsoft.Extensions.Options.IOptions{Hyun.Core.Common.Option.SeqOptions})">
            <summary>
            ValuesController
            </summary>
            <param name="blogArticleServices"></param>
            <param name="mapper"></param>
            <param name="advertisementServices"></param>
            <param name="love"></param>
            <param name="roleModulePermissionServices"></param>
            <param name="user"></param>
            <param name="passwordLibServices"></param>
            <param name="httpPollyHelper"></param>
            <param name="persistentConnection"></param>
            <param name="seqOptions"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.TestRabbitMqPublish">
            <summary>
            测试Rabbit消息队列发送
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.TestRabbitMqSubscribe">
            <summary>
            测试Rabbit消息队列订阅
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.TestSqlsugarWithCache">
            <summary>
            测试SqlSugar二级缓存
            可设置过期时间
            或通过接口方式更新该数据，也会离开清除缓存
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.Get">
            <summary>
            Get方法
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.RedisMq(Hyun.Core.Extensions.IRedisBasketRepository)">
            <summary>
            测试Redis消息队列
            </summary>
            <param name="_redisBasketRepository"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.EventBusTry(Hyun.Core.EventBus.IEventBus,System.String)">
            <summary>
            测试RabbitMQ事件总线
            </summary>
            <param name="_eventBus"></param>
            <param name="blogId"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.Get(System.Int32)">
            <summary>
            Get(int id)方法
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.RequiredP(System.String)">
            <summary>
            测试参数是必填项
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.GetUserInfo(System.String)">
            <summary>
            通过 HttpContext 获取用户信息
            </summary>
            <param name="ClaimType">声明类型，默认 jti </param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.Source">
            <summary>
            to redirect by route template name.
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.Destination">
            <summary>
            route with template name.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.Post(Hyun.Core.Model.Models.BlogArticle,System.Int32)">
            <summary>
            测试 post 一个对象 + 独立参数
            </summary>
            <param name="blogArticle">model实体类参数</param>
            <param name="id">独立参数</param>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.TestPostPara(System.String)">
            <summary>
            测试 post 参数
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.TestMutiDBAPI">
            <summary>
            测试多库连接
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.FluentVaTest(Hyun.Core.Filter.UserRegisterVo)">
            <summary>
            测试Fulent做参数校验
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.Put(System.Int32,System.String)">
            <summary>
            Put方法
            </summary>
            <param name="id"></param>
            <param name="value"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.Delete(System.Int32)">
            <summary>
            Delete方法
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.GetAllConfigByAppllo(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            测试接入Apollo获取配置信息
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.ValuesController.GetConfigByAppllo(System.String)">
            <summary>
            通过此处的key格式为 xx:xx:x
            </summary>
        </member>
        <member name="T:Hyun.Core.Controllers.WeChatCompanyController">
            <summary>
            WeChatCompanyController
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatCompanyController.#ctor(Hyun.Core.IServices.IWeChatCompanyServices)">
            <summary>
            构造函数
            </summary> 
            <param name="iWeChatCompanyServices"></param> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatCompanyController.Get(Hyun.Core.Model.PaginationModel)">
            <summary>
            获取
            </summary>
            <param name="pagination">分页条件</param> 
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatCompanyController.Get(System.String)">
            <summary>
            获取(id)
            </summary>
            <param name="id">主键ID</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatCompanyController.Post(Hyun.Core.Model.Models.WeChatCompany)">
            <summary>
            添加
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatCompanyController.Put(Hyun.Core.Model.Models.WeChatCompany)">
            <summary>
            更新
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatCompanyController.Delete(System.String)">
            <summary>
            删除
            </summary> 
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatCompanyController.BatchDelete(System.String)">
            <summary>
            批量删除
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.WeChatConfigController">
            <summary>
            WeChatConfigController
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatConfigController.#ctor(Hyun.Core.IServices.IWeChatConfigServices)">
            <summary>
            构造函数
            </summary> 
            <param name="iWeChatConfigServices"></param> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatConfigController.Get(Hyun.Core.Model.PaginationModel)">
            <summary>
            获取
            </summary>
            <param name="pagination">分页条件</param> 
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatConfigController.Get(System.String)">
            <summary>
            获取(id)
            </summary>
            <param name="id">主键ID</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatConfigController.Post(Hyun.Core.Model.Models.WeChatConfig)">
            <summary>
            添加
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatConfigController.Put(Hyun.Core.Model.Models.WeChatConfig)">
            <summary>
            更新
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatConfigController.Delete(System.String)">
            <summary>
            删除
            </summary> 
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatConfigController.BatchDelete(System.String)">
            <summary>
            批量删除
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.WeChatController">
            <summary>
            微信公众号管理 
            </summary>   
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.#ctor(Hyun.Core.IServices.IWeChatConfigServices,Microsoft.Extensions.Logging.ILogger{Hyun.Core.Controllers.WeChatController})">
            <summary>
            构造函数
            </summary>  
            <param name="weChatConfigServices"></param>
            <param name="logger"></param>   
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.GetToken(System.String)">
            <summary>
            更新Token
            </summary>
            <param name="id"></param>
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.RefreshToken(System.String)">
            <summary>
            刷新Token
            </summary>
            <param name="id"></param>
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.GetTemplate(System.String)">
            <summary>
            获取模板
            </summary>
            <param name="id"></param>
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.GetMenu(System.String)">
            <summary>
            获取菜单
            </summary>
            <param name="id"></param>
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.UpdateMenu(Hyun.Core.Model.ViewModels.WeChatApiDto)">
            <summary>
            更新菜单
            </summary>
            <param name="menu"></param>
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.GetSubUsers(System.String)">
            <summary>
            获取订阅用户(所有)
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.Valid(Hyun.Core.Model.ViewModels.WeChatValidDto)">
            <summary>
            入口
            </summary>
            <param name="validDto"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.GetSubUser(System.String,System.String)">
            <summary>
            获取订阅用户
            </summary>
            <param name="id"></param>
            <param name="openid"></param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.GetQRBind(Hyun.Core.Model.ViewModels.WeChatUserInfo)">
            <summary>
            获取一个绑定员工公众号二维码
            </summary>
            <param name="info">消息</param> 
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.PushCardMsg(Hyun.Core.Model.ViewModels.WeChatCardMsgDataDto)">
            <summary>
            推送卡片消息接口
            </summary>
            <param name="msg">卡片消息对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.PushCardMsgGet(Hyun.Core.Model.ViewModels.WeChatCardMsgDataDto)">
            <summary>
            推送卡片消息接口
            </summary>
            <param name="msg">卡片消息对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.PushTxtMsg(Hyun.Core.Model.ViewModels.WeChatPushTestDto)">
            <summary>
            推送文本消息
            </summary>
            <param name="msg">消息对象</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.GetBindUserInfo(Hyun.Core.Model.ViewModels.WeChatUserInfo)">
            <summary>
            通过绑定用户获取微信用户信息(一般用于初次绑定检测)
            </summary>
            <param name="info">信息</param> 
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatController.UnBind(Hyun.Core.Model.ViewModels.WeChatUserInfo)">
            <summary>
            用户解绑
            </summary>
            <param name="info">消息</param> 
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.WeChatPushLogController">
            <summary>
            WeChatPushLogController
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatPushLogController.#ctor(Hyun.Core.IServices.IWeChatPushLogServices)">
            <summary>
            构造函数
            </summary> 
            <param name="iWeChatPushLogServices"></param> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatPushLogController.Get(Hyun.Core.Model.PaginationModel)">
            <summary>
            获取
            </summary>
            <param name="pagination">分页条件</param> 
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatPushLogController.Get(System.String)">
            <summary>
            获取(id)
            </summary>
            <param name="id">主键ID</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatPushLogController.Post(Hyun.Core.Model.Models.WeChatPushLog)">
            <summary>
            添加
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatPushLogController.Put(Hyun.Core.Model.Models.WeChatPushLog)">
            <summary>
            更新
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatPushLogController.Delete(System.String)">
            <summary>
            删除
            </summary> 
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatPushLogController.BatchDelete(System.String)">
            <summary>
            批量删除
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.Controllers.WeChatSubController">
            <summary>
            WeChatSubController
            </summary>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatSubController.#ctor(Hyun.Core.IServices.IWeChatSubServices)">
            <summary>
            构造函数
            </summary> 
            <param name="iWeChatSubServices"></param> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatSubController.Get(Hyun.Core.Model.PaginationModel)">
            <summary>
            获取
            </summary>
            <param name="pagination">分页条件</param> 
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatSubController.Get(System.String)">
            <summary>
            获取(id)
            </summary>
            <param name="id">主键ID</param>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatSubController.Post(Hyun.Core.Model.Models.WeChatSub)">
            <summary>
            添加
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatSubController.Put(Hyun.Core.Model.Models.WeChatSub)">
            <summary>
            更新
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatSubController.Delete(System.String)">
            <summary>
            删除
            </summary> 
            <returns></returns> 
        </member>
        <member name="M:Hyun.Core.Controllers.WeChatSubController.BatchDelete(System.String)">
            <summary>
            批量删除
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hyun.Core.SwaggerHelper.CustomRouteAttribute">
            <summary>
            自定义路由 /api/{version}/[controler]/[action]
            </summary>
        </member>
        <member name="P:Hyun.Core.SwaggerHelper.CustomRouteAttribute.GroupName">
            <summary>
            分组名称,是来实现接口 IApiDescriptionGroupNameProvider
            </summary>
        </member>
        <member name="M:Hyun.Core.SwaggerHelper.CustomRouteAttribute.#ctor(System.String)">
            <summary>
            自定义路由构造函数，继承基类路由
            </summary>
            <param name="actionName"></param>
        </member>
        <member name="M:Hyun.Core.SwaggerHelper.CustomRouteAttribute.#ctor(Hyun.Core.Extensions.CustomApiVersion.ApiVersions,System.String)">
            <summary>
            自定义版本+路由构造函数，继承基类路由
            </summary>
            <param name="actionName"></param>
            <param name="version"></param>
        </member>
        <member name="T:Hyun.Core.Filter.GlobalRouteAuthorizeConvention">
            <summary>
            Summary:全局路由权限公约
            Remarks:目的是针对不同的路由，采用不同的授权过滤器
            如果 controller 上不加 [Authorize] 特性，默认都是 Permission 策略
            否则，如果想特例其他授权机制的话，需要在 controller 上带上  [Authorize]，然后再action上自定义授权即可，比如 [Authorize(Roles = "Admin")]
            </summary>
        </member>
        <member name="T:Hyun.Core.Filter.GlobalAuthorizeFilter">
            <summary>
            全局权限过滤器【无效】
            </summary>
        </member>
        <member name="T:Hyun.Core.Filter.GlobalExceptionsFilter">
            <summary>
            全局异常错误日志
            </summary>
        </member>
        <member name="M:Hyun.Core.Filter.GlobalExceptionsFilter.WriteLog(System.String,System.Exception)">
            <summary>
            自定义返回格式
            </summary>
            <param name="throwMsg"></param>
            <param name="ex"></param>
            <returns></returns>
        </member>
        <member name="P:Hyun.Core.Filter.JsonErrorResponse.Message">
            <summary>
            生产环境的消息
            </summary>
        </member>
        <member name="P:Hyun.Core.Filter.JsonErrorResponse.DevelopmentMessage">
            <summary>
            开发环境的消息
            </summary>
        </member>
        <member name="T:Hyun.Core.Filter.GlobalRoutePrefixFilter">
            <summary>
            全局路由前缀公约
            </summary>
        </member>
    </members>
</doc>
