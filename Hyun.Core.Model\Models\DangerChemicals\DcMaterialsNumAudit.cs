namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///退货、盘点、报废审核表
    ///</summary>
    [SugarTable("dc_MaterialsNumAudit","退货、盘点、报废审核表")]
    public class DcMaterialsNumAudit : BaseEntity
    {

          public DcMaterialsNumAudit()
          {

          }

           /// <summary>
           ///单位物品表Id
          /// </summary>
          public long SchoolMaterialId { get; set; }

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal OptNum { get; set; }

           /// <summary>
           ///理由
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string OptReason { get; set; }

           /// <summary>
           ///时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? OptTime { get; set; }

           /// <summary>
           ///状态（0：待审核，1：审核通过，2：审核不通过）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///类型（1：盘点，2：报废，3：退货）
          /// </summary>
          public int OptType { get; set; }

           /// <summary>
           ///操作单位
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///操作人
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///操作时间
          /// </summary>
          public DateTime RegTime { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

