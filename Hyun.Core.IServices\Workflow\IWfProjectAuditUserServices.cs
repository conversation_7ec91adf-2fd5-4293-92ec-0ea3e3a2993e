﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfProjectAuditUser接口方法
    ///</summary>
    public interface IWfProjectAuditUserServices : IBaseServices<WfProjectAuditUser>
    {


        /// <summary>
        /// 
        /// </summary>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        Task<PageModel<ModuleAuditModel>> GetProcessAuditUserList();

        /// <summary>
        /// 
        /// </summary>
        /// <param name="processId"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        Task<List<SetUserListModel>> GetSetUserList(long processId, long processNodeId);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="userModel"></param>
        /// <returns></returns>
        Task<Result<string>> SetUserList(UserListModel userModel);


        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<ModuleMenuModel>> GetModuleMenuList();


        Task<bool> IsExistGroupProcessSet();

        Task<bool> IsExistPermissionSet();
    }
}

