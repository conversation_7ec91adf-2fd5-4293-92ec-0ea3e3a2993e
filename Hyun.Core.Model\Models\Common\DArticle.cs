namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///资讯表
    ///</summary>
    [SugarTable("d_Article", "资讯表")]
    public class DArticle : BaseEntity
    {

        public DArticle()
        {

        }

        /// <summary>
        ///资讯类型ID
        /// </summary>
        public long Cid { get; set; }

        /// <summary>
        ///所属专题ID
        /// </summary>
        public long Sid { get; set; }

        /// <summary>
        ///标题
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string Title { get; set; }

        /// <summary>
        ///副标题
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string Subtitle { get; set; }

        /// <summary>
        ///资讯短标题
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string ShortTitle { get; set; }

        /// <summary>
        ///内容
        /// </summary>
        [SugarColumn(IsNullable = true,ColumnDataType = "ntext")]
        public string Remark { get; set; }

        /// <summary>
        ///资讯类型(0:普通1:图片)
        /// </summary>
        public int ArticleType { get; set; }

        /// <summary>
        ///是否有图片
        /// </summary>
        public bool HasImage { get; set; }

        /// <summary>
        ///图片路径
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string ImageUrl { get; set; }

        /// <summary>
        ///图片描述
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string ImgAlt { get; set; }

        /// <summary>
        ///附件
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Attachment { get; set; }

        /// <summary>
        ///资讯来源
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string Source { get; set; }

        /// <summary>
        ///作者
        /// </summary>
        [SugarColumn(Length = 63, IsNullable = true)]
        public string Author { get; set; }

        /// <summary>
        ///关键字
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Keywords { get; set; }

        /// <summary>
        ///是否推荐
        /// </summary>
        public bool IsRecommend { get; set; }

        /// <summary>
        ///发布范围0：全部；1 全区
        /// </summary>
        public int AreaId { get; set; }

        /// <summary>
        ///是否允许企业查看
        /// </summary>
        public bool IsAllowCompany { get; set; }

        /// <summary>
        ///发布者
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        ///发布时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///是否热门(1：是，0：否)
        /// </summary>
        public bool IsHot { get; set; }

        /// <summary>
        ///是否焦点(1：是，0：否)
        /// </summary>
        public bool IsFoc { get; set; }

        /// <summary>
        ///状态（0:：保存；1：提交；2：发布；3：区县审核不通过；4：系统审核不通过）
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        ///点击量
        /// </summary>
        public int Hits { get; set; }

        /// <summary>
        ///附件下载数
        /// </summary>
        public int AttachDownCount { get; set; }

        /// <summary>
        ///评论数
        /// </summary>
        public int CommentCount { get; set; }

        /// <summary>
        ///审核人
        /// </summary>
        public long AuditorId { get; set; }

        /// <summary>
        ///审核时间
        /// </summary>
        public DateTime AuditDate { get; set; }

        /// <summary>
        ///审核说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Description { get; set; }

        /// <summary>
        ///显示时间
        /// </summary>
        public DateTime BeginTime { get; set; }

        /// <summary>
        ///到期时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        ///单位Id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        ///上级单位Id
        /// </summary>
        public long UnitPid { get; set; }

        /// <summary>
        ///项目名称
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string ExtProjectName { get; set; }

        /// <summary>
        ///建设时间
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string ExtConstDime { get; set; }

        /// <summary>
        ///实施单位
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string ExtImpleSchool { get; set; }

        /// <summary>
        ///项目造价
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string ExtProjectCost { get; set; }

        /// <summary>
        ///承建企业
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string ExtConstrucCompany { get; set; }

        /// <summary>
        ///品牌支持
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string ExtBrandSupport { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 分类名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string CategoryName { get; set; }

        /// <summary>
        /// 资讯分类类型（1:运营商栏目;2:动态栏目）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int CateType { get; set; }

        /// <summary>
        /// 资讯分类编码
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int Code { get; set; }


        /// <summary>
        /// 是否支持多条资讯（1：支持 0：不支持）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int IsMany { get; set; }

    }

    /// <summary>
    /// 资讯信息
    /// </summary>
    public class DArticleModel
    {
        /// <summary>
        /// 资讯Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        ///资讯分类Id
        /// </summary>
        public long Cid { get; set; }

        /// <summary>
        ///标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        ///资讯短标题
        /// </summary>
        public string ShortTitle { get; set; }

        /// <summary>
        ///内容
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        ///是否有图片
        /// </summary>
        public bool HasImage { get; set; }

        /// <summary>
        ///图片路径
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        ///附件路径
        /// </summary>
        public string Attachment { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime BeginTime { get; set; }

        /// <summary>
        /// 状态（0：保存，2：发布）
        /// </summary>
        public int Statuz { get; set; }


    }

}

