{"version": 2, "dgSpecHash": "gItvVCD7Qkc=", "success": true, "projectFilePath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Extensions\\Hyun.Core.Extensions.csproj", "expectedPackageFiles": ["C:\\NuGetPackages\\antlr3.runtime\\3.5.1\\antlr3.runtime.3.5.1.nupkg.sha512", "C:\\NuGetPackages\\aspnetcoreratelimit\\5.0.0\\aspnetcoreratelimit.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\autofac\\6.5.0\\autofac.6.5.0.nupkg.sha512", "C:\\NuGetPackages\\autofac.extensions.dependencyinjection\\8.0.0\\autofac.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\autofac.extras.dynamicproxy\\7.1.0\\autofac.extras.dynamicproxy.7.1.0.nupkg.sha512", "C:\\NuGetPackages\\automapper\\13.0.1\\automapper.13.0.1.nupkg.sha512", "C:\\NuGetPackages\\awssdk.core\\**********\\awssdk.core.**********.nupkg.sha512", "C:\\NuGetPackages\\awssdk.securitytoken\\**********\\awssdk.securitytoken.**********.nupkg.sha512", "C:\\NuGetPackages\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "C:\\NuGetPackages\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "C:\\NuGetPackages\\bouncycastle.cryptography\\2.3.1\\bouncycastle.cryptography.2.3.1.nupkg.sha512", "C:\\NuGetPackages\\castle.core\\5.1.1\\castle.core.5.1.1.nupkg.sha512", "C:\\NuGetPackages\\com.ctrip.framework.apollo\\2.10.0\\com.ctrip.framework.apollo.2.10.0.nupkg.sha512", "C:\\NuGetPackages\\com.ctrip.framework.apollo.configuration\\2.10.2\\com.ctrip.framework.apollo.configuration.2.10.2.nupkg.sha512", "C:\\NuGetPackages\\confluent.kafka\\2.5.2\\confluent.kafka.2.5.2.nupkg.sha512", "C:\\NuGetPackages\\consul\\********\\consul.********.nupkg.sha512", "C:\\NuGetPackages\\dnsclient\\1.6.1\\dnsclient.1.6.1.nupkg.sha512", "C:\\NuGetPackages\\dynamicexpresso.core\\2.3.3\\dynamicexpresso.core.2.3.3.nupkg.sha512", "C:\\NuGetPackages\\easycaching.core\\1.9.2\\easycaching.core.1.9.2.nupkg.sha512", "C:\\NuGetPackages\\easycaching.inmemory\\1.9.2\\easycaching.inmemory.1.9.2.nupkg.sha512", "C:\\NuGetPackages\\enums.net\\4.0.1\\enums.net.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\extendednumerics.bigdecimal\\2025.1001.2.129\\extendednumerics.bigdecimal.2025.1001.2.129.nupkg.sha512", "C:\\NuGetPackages\\fluentvalidation\\11.8.0\\fluentvalidation.11.8.0.nupkg.sha512", "C:\\NuGetPackages\\fluentvalidation.aspnetcore\\11.3.0\\fluentvalidation.aspnetcore.11.3.0.nupkg.sha512", "C:\\NuGetPackages\\fluentvalidation.dependencyinjectionextensions\\11.5.1\\fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512", "C:\\NuGetPackages\\google.protobuf\\3.21.2\\google.protobuf.3.21.2.nupkg.sha512", "C:\\NuGetPackages\\grpc.core\\2.46.3\\grpc.core.2.46.3.nupkg.sha512", "C:\\NuGetPackages\\grpc.core.api\\2.46.3\\grpc.core.api.2.46.3.nupkg.sha512", "C:\\NuGetPackages\\initq\\********\\initq.********.nupkg.sha512", "C:\\NuGetPackages\\ipaddressrange\\6.0.0\\ipaddressrange.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\librdkafka.redist\\2.5.0\\librdkafka.redist.2.5.0.nupkg.sha512", "C:\\NuGetPackages\\log4net\\2.0.17\\log4net.2.0.17.nupkg.sha512", "C:\\NuGetPackages\\magicodes.ie.core\\*******\\magicodes.ie.core.*******.nupkg.sha512", "C:\\NuGetPackages\\magicodes.ie.epplus\\*******\\magicodes.ie.epplus.*******.nupkg.sha512", "C:\\NuGetPackages\\magicodes.ie.excel\\*******\\magicodes.ie.excel.*******.nupkg.sha512", "C:\\NuGetPackages\\mapster\\7.4.0\\mapster.7.4.0.nupkg.sha512", "C:\\NuGetPackages\\mapster.core\\1.2.1\\mapster.core.1.2.1.nupkg.sha512", "C:\\NuGetPackages\\mathnet.numerics.signed\\5.0.0\\mathnet.numerics.signed.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.authentication.jwtbearer\\8.0.4\\microsoft.aspnetcore.authentication.jwtbearer.8.0.4.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.authorization\\2.2.0\\microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.authorization.policy\\2.2.0\\microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.connections.abstractions\\8.0.13\\microsoft.aspnetcore.connections.abstractions.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.http\\2.2.0\\microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.http.connections\\1.1.0\\microsoft.aspnetcore.http.connections.1.1.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.http.connections.common\\1.1.0\\microsoft.aspnetcore.http.connections.common.1.1.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.jsonpatch\\8.0.13\\microsoft.aspnetcore.jsonpatch.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.middlewareanalysis\\8.0.0\\microsoft.aspnetcore.middlewareanalysis.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.mvc.newtonsoftjson\\8.0.13\\microsoft.aspnetcore.mvc.newtonsoftjson.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.routing\\2.2.0\\microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.routing.abstractions\\2.2.0\\microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.signalr\\1.1.0\\microsoft.aspnetcore.signalr.1.1.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.signalr.common\\8.0.13\\microsoft.aspnetcore.signalr.common.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.signalr.core\\1.1.0\\microsoft.aspnetcore.signalr.core.1.1.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.signalr.protocols.json\\1.1.0\\microsoft.aspnetcore.signalr.protocols.json.1.1.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.signalr.protocols.newtonsoftjson\\8.0.13\\microsoft.aspnetcore.signalr.protocols.newtonsoftjson.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.websockets\\2.2.0\\microsoft.aspnetcore.websockets.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.bcl.memory\\9.0.0\\microsoft.bcl.memory.9.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlclient\\5.2.2\\microsoft.data.sqlclient.5.2.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlclient.sni.runtime\\5.2.0\\microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlite\\9.0.0\\microsoft.data.sqlite.9.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlite.core\\9.0.0\\microsoft.data.sqlite.core.9.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.ambientmetadata.application\\8.10.0\\microsoft.extensions.ambientmetadata.application.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.caching.memory\\8.0.1\\microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.caching.stackexchangeredis\\8.0.13\\microsoft.extensions.caching.stackexchangeredis.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.compliance.abstractions\\8.10.0\\microsoft.extensions.compliance.abstractions.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration\\8.0.0\\microsoft.extensions.configuration.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.binder\\8.0.2\\microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.fileextensions\\8.0.0\\microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.json\\8.0.0\\microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection\\8.0.1\\microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection.autoactivation\\8.10.0\\microsoft.extensions.dependencyinjection.autoactivation.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencymodel\\8.0.1\\microsoft.extensions.dependencymodel.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.diagnosticadapter\\3.1.32\\microsoft.extensions.diagnosticadapter.3.1.32.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.diagnostics\\8.0.1\\microsoft.extensions.diagnostics.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.diagnostics.abstractions\\8.0.1\\microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.diagnostics.exceptionsummarization\\8.10.0\\microsoft.extensions.diagnostics.exceptionsummarization.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.features\\8.0.13\\microsoft.extensions.features.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.hosting\\2.1.0\\microsoft.extensions.hosting.2.1.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.hosting.abstractions\\8.0.1\\microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http\\8.0.1\\microsoft.extensions.http.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http.diagnostics\\8.10.0\\microsoft.extensions.http.diagnostics.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http.polly\\8.0.13\\microsoft.extensions.http.polly.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http.resilience\\8.10.0\\microsoft.extensions.http.resilience.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging\\8.0.1\\microsoft.extensions.logging.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging.abstractions\\8.0.3\\microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging.configuration\\8.0.1\\microsoft.extensions.logging.configuration.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.objectpool\\8.0.10\\microsoft.extensions.objectpool.8.0.10.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options\\8.0.2\\microsoft.extensions.options.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.resilience\\8.10.0\\microsoft.extensions.resilience.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.telemetry\\8.10.0\\microsoft.extensions.telemetry.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.telemetry.abstractions\\8.10.0\\microsoft.extensions.telemetry.abstractions.8.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.abstractions\\8.4.0\\microsoft.identitymodel.abstractions.8.4.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.jsonwebtokens\\8.4.0\\microsoft.identitymodel.jsonwebtokens.8.4.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.logging\\8.4.0\\microsoft.identitymodel.logging.8.4.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.protocols\\8.4.0\\microsoft.identitymodel.protocols.8.4.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.protocols.openidconnect\\7.1.2\\microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.tokens\\8.4.0\\microsoft.identitymodel.tokens.8.4.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.io.recyclablememorystream\\3.0.0\\microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.net.http.headers\\8.0.13\\microsoft.net.http.headers.8.0.13.nupkg.sha512", "C:\\NuGetPackages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\NuGetPackages\\microsoft.openapi\\1.6.14\\microsoft.openapi.1.6.14.nupkg.sha512", "C:\\NuGetPackages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.primitives\\4.0.1\\microsoft.win32.primitives.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\miniprofiler.aspnetcore\\4.3.8\\miniprofiler.aspnetcore.4.3.8.nupkg.sha512", "C:\\NuGetPackages\\miniprofiler.aspnetcore.mvc\\4.3.8\\miniprofiler.aspnetcore.mvc.4.3.8.nupkg.sha512", "C:\\NuGetPackages\\miniprofiler.shared\\4.3.8\\miniprofiler.shared.4.3.8.nupkg.sha512", "C:\\NuGetPackages\\mongodb.bson\\2.28.0\\mongodb.bson.2.28.0.nupkg.sha512", "C:\\NuGetPackages\\mongodb.driver\\2.28.0\\mongodb.driver.2.28.0.nupkg.sha512", "C:\\NuGetPackages\\mongodb.driver.core\\2.28.0\\mongodb.driver.core.2.28.0.nupkg.sha512", "C:\\NuGetPackages\\mongodb.libmongocrypt\\1.11.0\\mongodb.libmongocrypt.1.11.0.nupkg.sha512", "C:\\NuGetPackages\\mysqlconnector\\2.2.5\\mysqlconnector.2.2.5.nupkg.sha512", "C:\\NuGetPackages\\nacos-sdk-csharp\\1.3.7\\nacos-sdk-csharp.1.3.7.nupkg.sha512", "C:\\NuGetPackages\\nacos-sdk-csharp-unofficial\\0.8.5\\nacos-sdk-csharp-unofficial.0.8.5.nupkg.sha512", "C:\\NuGetPackages\\nacos-sdk-csharp-unofficial.aspnetcore\\0.8.5\\nacos-sdk-csharp-unofficial.aspnetcore.0.8.5.nupkg.sha512", "C:\\NuGetPackages\\nacos-sdk-csharp-unofficial.extensions.configuration\\0.8.5\\nacos-sdk-csharp-unofficial.extensions.configuration.0.8.5.nupkg.sha512", "C:\\NuGetPackages\\nacos-sdk-csharp-unofficial.iniparser\\0.8.5\\nacos-sdk-csharp-unofficial.iniparser.0.8.5.nupkg.sha512", "C:\\NuGetPackages\\nacos-sdk-csharp.aspnetcore\\1.3.7\\nacos-sdk-csharp.aspnetcore.1.3.7.nupkg.sha512", "C:\\NuGetPackages\\ncalc.netcore\\1.0.1\\ncalc.netcore.1.0.1.nupkg.sha512", "C:\\NuGetPackages\\netdevpack.security.jwtextensions\\8.0.0\\netdevpack.security.jwtextensions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\netstandard.library\\1.6.0\\netstandard.library.1.6.0.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\NuGetPackages\\npgsql\\5.0.18\\npgsql.5.0.18.nupkg.sha512", "C:\\NuGetPackages\\npoi\\2.7.1\\npoi.2.7.1.nupkg.sha512", "C:\\NuGetPackages\\ocelot\\23.3.3\\ocelot.23.3.3.nupkg.sha512", "C:\\NuGetPackages\\openiddict\\6.2.0\\openiddict.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.abstractions\\6.2.0\\openiddict.abstractions.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.aspnetcore\\6.2.0\\openiddict.aspnetcore.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.client\\6.2.0\\openiddict.client.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.client.aspnetcore\\6.2.0\\openiddict.client.aspnetcore.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.client.dataprotection\\6.2.0\\openiddict.client.dataprotection.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.client.systemintegration\\6.2.0\\openiddict.client.systemintegration.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.client.systemnethttp\\6.2.0\\openiddict.client.systemnethttp.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.client.webintegration\\6.2.0\\openiddict.client.webintegration.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.core\\6.2.0\\openiddict.core.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.quartz\\6.2.0\\openiddict.quartz.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.server\\6.2.0\\openiddict.server.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.server.aspnetcore\\6.2.0\\openiddict.server.aspnetcore.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.server.dataprotection\\6.2.0\\openiddict.server.dataprotection.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.validation\\6.2.0\\openiddict.validation.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.validation.aspnetcore\\6.2.0\\openiddict.validation.aspnetcore.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.validation.dataprotection\\6.2.0\\openiddict.validation.dataprotection.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.validation.serverintegration\\6.2.0\\openiddict.validation.serverintegration.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\openiddict.validation.systemnethttp\\6.2.0\\openiddict.validation.systemnethttp.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\oracle.manageddataaccess.core\\3.21.100\\oracle.manageddataaccess.core.3.21.100.nupkg.sha512", "C:\\NuGetPackages\\oscar.data.sqlclient\\4.0.4\\oscar.data.sqlclient.4.0.4.nupkg.sha512", "C:\\NuGetPackages\\pinyinconvertercore\\1.0.2\\pinyinconvertercore.1.0.2.nupkg.sha512", "C:\\NuGetPackages\\pipelines.sockets.unofficial\\2.2.8\\pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "C:\\NuGetPackages\\polly\\8.4.1\\polly.8.4.1.nupkg.sha512", "C:\\NuGetPackages\\polly.core\\8.4.2\\polly.core.8.4.2.nupkg.sha512", "C:\\NuGetPackages\\polly.extensions\\8.4.2\\polly.extensions.8.4.2.nupkg.sha512", "C:\\NuGetPackages\\polly.extensions.http\\3.0.0\\polly.extensions.http.3.0.0.nupkg.sha512", "C:\\NuGetPackages\\polly.ratelimiting\\8.4.2\\polly.ratelimiting.8.4.2.nupkg.sha512", "C:\\NuGetPackages\\protobuf-net\\3.2.30\\protobuf-net.3.2.30.nupkg.sha512", "C:\\NuGetPackages\\protobuf-net.core\\3.2.30\\protobuf-net.core.3.2.30.nupkg.sha512", "C:\\NuGetPackages\\quartz\\3.14.0\\quartz.3.14.0.nupkg.sha512", "C:\\NuGetPackages\\quartz.extensions.dependencyinjection\\3.14.0\\quartz.extensions.dependencyinjection.3.14.0.nupkg.sha512", "C:\\NuGetPackages\\quartz.extensions.hosting\\3.14.0\\quartz.extensions.hosting.3.14.0.nupkg.sha512", "C:\\NuGetPackages\\rabbitmq.client\\6.8.1\\rabbitmq.client.6.8.1.nupkg.sha512", "C:\\NuGetPackages\\restsharp\\111.4.1\\restsharp.111.4.1.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system.net.http\\4.0.1\\runtime.native.system.net.http.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system.security.cryptography\\4.0.0\\runtime.native.system.security.cryptography.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\scrutor\\3.3.0\\scrutor.3.3.0.nupkg.sha512", "C:\\NuGetPackages\\serilog\\4.0.1\\serilog.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\serilog.aspnetcore\\8.0.2\\serilog.aspnetcore.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\serilog.expressions\\5.0.0\\serilog.expressions.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.extensions.hosting\\8.0.0\\serilog.extensions.hosting.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.extensions.logging\\8.0.0\\serilog.extensions.logging.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.formatting.compact\\2.0.0\\serilog.formatting.compact.2.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.settings.configuration\\8.0.2\\serilog.settings.configuration.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\serilog.sinks.async\\2.0.0\\serilog.sinks.async.2.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.sinks.console\\6.0.0\\serilog.sinks.console.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.sinks.file\\6.0.0\\serilog.sinks.file.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.sinks.periodicbatching\\5.0.0\\serilog.sinks.periodicbatching.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\serilog.sinks.seq\\8.0.0\\serilog.sinks.seq.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\sharpcompress\\0.30.1\\sharpcompress.0.30.1.nupkg.sha512", "C:\\NuGetPackages\\sharpziplib\\1.4.2\\sharpziplib.1.4.2.nupkg.sha512", "C:\\NuGetPackages\\sixlabors.fonts\\2.0.4\\sixlabors.fonts.2.0.4.nupkg.sha512", "C:\\NuGetPackages\\sixlabors.imagesharp\\3.1.5\\sixlabors.imagesharp.3.1.5.nupkg.sha512", "C:\\NuGetPackages\\sixlabors.imagesharp.drawing\\2.1.4\\sixlabors.imagesharp.drawing.2.1.4.nupkg.sha512", "C:\\NuGetPackages\\skiasharp\\2.88.6\\skiasharp.2.88.6.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.nativeassets.linux.nodependencies\\2.88.6\\skiasharp.nativeassets.linux.nodependencies.2.88.6.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.nativeassets.macos\\2.88.6\\skiasharp.nativeassets.macos.2.88.6.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.nativeassets.win32\\2.88.6\\skiasharp.nativeassets.win32.2.88.6.nupkg.sha512", "C:\\NuGetPackages\\snappier\\1.0.0\\snappier.1.0.0.nupkg.sha512", "C:\\NuGetPackages\\sqlitepclraw.bundle_e_sqlite3\\2.1.10\\sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "C:\\NuGetPackages\\sqlitepclraw.core\\2.1.10\\sqlitepclraw.core.2.1.10.nupkg.sha512", "C:\\NuGetPackages\\sqlitepclraw.lib.e_sqlite3\\2.1.10\\sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "C:\\NuGetPackages\\sqlitepclraw.provider.e_sqlite3\\2.1.10\\sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "C:\\NuGetPackages\\sqlsugarcore\\5.1.4.193\\sqlsugarcore.5.1.4.193.nupkg.sha512", "C:\\NuGetPackages\\sqlsugarcore.dm\\8.8.0\\sqlsugarcore.dm.8.8.0.nupkg.sha512", "C:\\NuGetPackages\\sqlsugarcore.kdbndp\\9.3.7.428\\sqlsugarcore.kdbndp.9.3.7.428.nupkg.sha512", "C:\\NuGetPackages\\stackexchange.redis\\2.8.0\\stackexchange.redis.2.8.0.nupkg.sha512", "C:\\NuGetPackages\\swashbuckle.aspnetcore\\6.7.0\\swashbuckle.aspnetcore.6.7.0.nupkg.sha512", "C:\\NuGetPackages\\swashbuckle.aspnetcore.filters\\8.0.2\\swashbuckle.aspnetcore.filters.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\swashbuckle.aspnetcore.filters.abstractions\\8.0.2\\swashbuckle.aspnetcore.filters.abstractions.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\swashbuckle.aspnetcore.newtonsoft\\6.7.0\\swashbuckle.aspnetcore.newtonsoft.6.7.0.nupkg.sha512", "C:\\NuGetPackages\\swashbuckle.aspnetcore.swagger\\6.7.0\\swashbuckle.aspnetcore.swagger.6.7.0.nupkg.sha512", "C:\\NuGetPackages\\swashbuckle.aspnetcore.swaggergen\\6.7.0\\swashbuckle.aspnetcore.swaggergen.6.7.0.nupkg.sha512", "C:\\NuGetPackages\\swashbuckle.aspnetcore.swaggerui\\6.7.0\\swashbuckle.aspnetcore.swaggerui.6.7.0.nupkg.sha512", "C:\\NuGetPackages\\system.appcontext\\4.1.0\\system.appcontext.4.1.0.nupkg.sha512", "C:\\NuGetPackages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\NuGetPackages\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.immutable\\7.0.0\\system.collections.immutable.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.configuration.configurationmanager\\8.0.0\\system.configuration.configurationmanager.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.console\\4.0.0\\system.console.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.data.common\\4.3.0\\system.data.common.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.eventlog\\8.0.0\\system.diagnostics.eventlog.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.performancecounter\\6.0.1\\system.diagnostics.performancecounter.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.stacktrace\\4.3.0\\system.diagnostics.stacktrace.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.tools\\4.0.1\\system.diagnostics.tools.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.directoryservices\\6.0.1\\system.directoryservices.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.directoryservices.protocols\\6.0.1\\system.directoryservices.protocols.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.formats.asn1\\8.0.0\\system.formats.asn1.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.globalization.calendars\\4.0.1\\system.globalization.calendars.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.globalization.extensions\\4.0.1\\system.globalization.extensions.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.identitymodel.tokens.jwt\\8.0.1\\system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.compression.zipfile\\4.0.1\\system.io.compression.zipfile.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.linq.dynamic.core\\1.4.4\\system.linq.dynamic.core.1.4.4.nupkg.sha512", "C:\\NuGetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\NuGetPackages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\NuGetPackages\\system.net.http\\4.1.0\\system.net.http.4.1.0.nupkg.sha512", "C:\\NuGetPackages\\system.net.http.json\\6.0.0\\system.net.http.json.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.net.primitives\\4.0.11\\system.net.primitives.4.0.11.nupkg.sha512", "C:\\NuGetPackages\\system.net.sockets\\4.1.0\\system.net.sockets.4.1.0.nupkg.sha512", "C:\\NuGetPackages\\system.net.websockets.websocketprotocol\\4.5.1\\system.net.websockets.websocketprotocol.4.5.1.nupkg.sha512", "C:\\NuGetPackages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\NuGetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.metadata\\1.4.1\\system.reflection.metadata.1.4.1.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.caching\\8.0.0\\system.runtime.caching.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.interopservices.runtimeinformation\\4.0.0\\system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.numerics\\4.0.1\\system.runtime.numerics.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.algorithms\\4.2.0\\system.security.cryptography.algorithms.4.2.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.cng\\4.2.0\\system.security.cryptography.cng.4.2.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.csp\\4.0.0\\system.security.cryptography.csp.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.encoding\\4.0.0\\system.security.cryptography.encoding.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.openssl\\4.0.0\\system.security.cryptography.openssl.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.pkcs\\8.0.0\\system.security.cryptography.pkcs.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.primitives\\4.0.0\\system.security.cryptography.primitives.4.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.protecteddata\\8.0.0\\system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.x509certificates\\4.1.0\\system.security.cryptography.x509certificates.4.1.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding.codepages\\8.0.0\\system.text.encoding.codepages.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.json\\8.0.4\\system.text.json.8.0.4.nupkg.sha512", "C:\\NuGetPackages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "C:\\NuGetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.channels\\7.0.0\\system.threading.channels.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.ratelimiting\\8.0.0\\system.threading.ratelimiting.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks.parallel\\4.3.0\\system.threading.tasks.parallel.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.timer\\4.0.1\\system.threading.timer.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.readerwriter\\4.0.11\\system.xml.readerwriter.4.0.11.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xdocument\\4.0.11\\system.xml.xdocument.4.0.11.nupkg.sha512", "C:\\NuGetPackages\\yitter.idgenerator\\1.0.14\\yitter.idgenerator.1.0.14.nupkg.sha512", "C:\\NuGetPackages\\zstdsharp.port\\0.7.3\\zstdsharp.port.0.7.3.nupkg.sha512"], "logs": []}