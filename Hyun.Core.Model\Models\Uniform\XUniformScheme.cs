using System.ComponentModel.DataAnnotations.Schema;

namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///方案选用
    ///</summary>
    [SugarTable("x_UniformScheme", "方案选用")]
    public class XUniformScheme : BaseEntity
    {

        public XUniformScheme()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///年度
        /// </summary>
        public int SchemeYear { get; set; }

        /// <summary>
        ///选用批次
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string SchemeNo { get; set; }

        /// <summary>
        ///应征求人数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? SolicitedNum { get; set; }

        /// <summary>
        ///响应填报人数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ResponseNum { get; set; }

        /// <summary>
        ///征求意见截止时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? SolicitedDeadline { get; set; }

        /// <summary>
        ///发布时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        ///征求状态
        /// </summary>
        public int SolicitedStatuz { get; set; }


        /// <summary>
        ///同意选用数
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AgreeNum { get; set; }

        /// <summary>
        ///同意率
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? AgreeRate { get; set; }

        /// <summary>
        ///采购方式
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? PurchaseMethod { get; set; }

        /// <summary>
        ///采购方式名称
        /// </summary>
        [SugarColumn(Length = 51, IsNullable = true)]
        public string PurchaseMethodName { get; set; }

        /// <summary>
        ///备案状态(0:待备案 10:待备查 100:已备案)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? FilingStatuz { get; set; }

        /// <summary>
        ///备查时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? FilingTime { get; set; }

        /// <summary>
        /// 是否备查
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? IsFiling { get; set; }

        /// <summary>
        ///备案说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string FilingExplanation { get; set; }

        /// <summary>
        ///家长意见书
        /// </summary>
        [SugarColumn(Length = int.MaxValue, IsNullable = true)]
        public string ParentOpinion { get; set; }

        /// <summary>
        ///
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        ///本次使用采购方式集合
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string PurchaseMethodIds { get; set; }

        /// <summary>
        ///本次使用采购方式集合名称
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string PurchaseMethodNames { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;

        /// <summary>
        /// 提交操作状态。
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int OptStatuz { get; set; }
    }


}

