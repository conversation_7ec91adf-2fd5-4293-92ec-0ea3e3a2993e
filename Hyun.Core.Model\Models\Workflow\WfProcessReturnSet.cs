﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批流程节点退回方式设置表
    ///</summary>
    [SugarTable("wf_ProcessReturnSet","审批流程节点退回方式设置表")]
    public class WfProcessReturnSet : BaseEntity
    {

          public WfProcessReturnSet()
          {

          }

           /// <summary>
           ///对应模块表xa_Module中Id
          /// </summary>
          public long ModuleId { get; set; }

           /// <summary>
           ///审批流程配置Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ProcessId { get; set; }

           /// <summary>
           ///节点Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ProcessNodeId { get; set; }

           /// <summary>
           ///退回方式（1：退到源头，2：退到上一级）
          /// </summary>
          public int BackWay { get; set; } = 1;

           /// <summary>
           ///排序值（值从小到大排）
          /// </summary>
          public long? Sort { get; set; } = 0;

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

