﻿namespace Hyun.Core.Model
{

    ///<summary>
    ///招标结果
    ///</summary>
    public class XUniformBiddingParam : BaseSearch
    {

        public XUniformBiddingParam()
        {

        }

        /// <summary>
        ///Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        ///Name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        ///Ids
        /// </summary>
        public string Ids { get; set; }

        /// <summary>
        /// 采购批次
        /// </summary>
        public string PurchaseNo { get; set; }

        /// <summary>
        /// 年度
        /// </summary>
        public int PlanYear { get; set; } = 0;

        /// <summary>
        /// 采购方式
        /// </summary>
        public int Method { get; set; } = 0;

        /// <summary>
        /// 备案状态
        /// </summary>
        public int BiddingStatuz { get; set; } = -1;

        /// <summary>
        /// 采购有效年限
        /// </summary>
        public int ValidityPeriod { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        /// 区域Id
        /// </summary>
        public long CountyId { get; set; }
    }


}

