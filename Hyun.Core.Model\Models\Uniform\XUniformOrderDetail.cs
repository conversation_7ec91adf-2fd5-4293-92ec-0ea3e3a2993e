namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///校服订单明细列表
    ///</summary>
    [SugarTable("x_UniformOrderDetail","校服订单明细列表")]
    public class XUniformOrderDetail : BaseEntity
    {

          public XUniformOrderDetail()
          {

          }

           /// <summary>
           ///校服展示表Id
          /// </summary>
          public long UniformShelfId { get; set; }

           /// <summary>
           ///采购表Id
          /// </summary>
          public long UniformPurchaseId { get; set; }

           /// <summary>
           ///适用单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///种类
          /// </summary>
          [SugarColumn(Length = 51,IsNullable = true)]
          public string Uniformtype { get; set; }

           /// <summary>
           ///品名
          /// </summary>
          [SugarColumn(Length = 51,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///品牌
          /// </summary>
          [SugarColumn(Length = 51,IsNullable = true)]
          public string Brand { get; set; }

           /// <summary>
           ///具体参数
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Parameter { get; set; }

           /// <summary>
           ///适合性别(0:未知  1：男  2：女 3：男/女)
          /// </summary>
          public int Sex { get; set; }

           /// <summary>
           ///单价（元）
          /// </summary>
          public decimal Price { get; set; }

           /// <summary>
           ///标配数量
          /// </summary>
          public int StandardNum { get; set; }

           /// <summary>
           ///单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

           /// <summary>
           ///生产厂商
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string Producer { get; set; }

           /// <summary>
           ///产地
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string OriginAddress { get; set; }

           /// <summary>
           ///安全等级
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string SecurityLevel { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? Sort { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

