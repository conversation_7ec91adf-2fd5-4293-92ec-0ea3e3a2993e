﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///装备分类
    ///</summary>
    [SugarTable("th_EquipmentCategory","装备分类")]
    public class ThEquipmentCategory : BaseEntity
    {

          public ThEquipmentCategory()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///装备分类
          /// </summary>
          [SugarColumn(Length = 127)]
          public string CategoryName { get; set; }

           /// <summary>
           ///装备名称
          /// </summary>
          [SugarColumn(Length = 127)]
          public string EquipmentName { get; set; }

           /// <summary>
           ///适用学段
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string SchoolStageName { get; set; }

           /// <summary>
           ///状态（1：启用 2：禁用）
          /// </summary>
          public int Statuz { get; set; } = 1;

           /// <summary>
           ///单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          public int Sort { get; set; } = 0;

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

