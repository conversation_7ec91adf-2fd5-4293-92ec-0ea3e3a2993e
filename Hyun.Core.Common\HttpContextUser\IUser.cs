﻿using System.Collections.Generic;
using System.Security.Claims;
using Hyun.Core.Model;

namespace Hyun.Core.Common.HttpContextUser
{
    public interface IUser
    {
        string Name { get; }
        long ID { get; }
        long TenantId { get; }
        bool IsAuthenticated();
        IEnumerable<Claim> GetClaimsIdentity();
        List<string> GetClaimValueByType(string ClaimType);

        string GetToken();
        List<string> GetUserInfoFromToken(string ClaimType);

        MessageModel<string> MessageModel { get; set; }

        /// <summary>
        /// 用户 ID
        /// </summary>
        long UserId { get; }
        /// <summary>
        /// 用户名称
        /// </summary>
        string UserName { get; }
        /// <summary>
        /// 帐号 ID
        /// </summary>
        long AcctId { get; }
        /// <summary>
        /// 帐号名称
        /// </summary>
        string AcctName { get; }
        /// <summary>
        /// 手机号码
        /// </summary>
        string Mobile { get; }
        /// <summary>
        ///单位 ID
        /// </summary>
        long UnitId { get; }

        /// <summary>
        ///上级单位 ID
        /// </summary>
        long UnitPId { get; }

        /// <summary>
        ///区县 ID
        /// </summary>
        long AreaId { get; }
        /// <summary>
        /// 用户 单位 类型
        /// </summary>
        int UnitTypeId { get; }
        /// <summary>
        /// 用户 单位 名称
        /// </summary>
        string UnitName { get; }
        /// <summary>
        /// 用户角色
        /// </summary>
        List<RoleTypes> Roles { get; }
        /// <summary>
        ///  客户端 IP 地址
        /// </summary>
        string ClientIP { get; }

        /// <summary>
        /// 用户所有角色名称
        /// </summary>
        string UserRoles { get; }
        /// <summary>
        /// 用户所有角色Id
        /// </summary>
        string UserRoleIds { get; }
        /// <summary>
        /// 管理员类型（0：运维管理员  1：运维人员 2：其它用户）注：主要用于区分运维管理员与运维人员
        /// </summary>
        int AdministratorType { get; }

        bool IsThirdClient { get; }

        /// <summary>
        /// 是否系统运维管理员或系统运维人员
        /// </summary>
        bool IsSystemUser { get; }
    }


    public enum RoleTypes
    {
        /// <summary>
        /// -1: 匿名用户
        /// </summary>
        Anonymous = -1,

        /// <summary>
        /// 0改成9000: 系统运维管理员
        /// </summary> 
        SystemAdmin = 9000,

        /// <summary>
        /// 运维人员
        /// </summary>
        SystemOps = 9001,    
        /// <summary>
        /// 10: 市管理员
        /// </summary> 
        CityAdmin = 10,
        /// <summary>
        /// 11: 市级用户(维修管理员)
        /// </summary>
        CityUser = 11,

        /// <summary>
        /// 12: 市级查看员（现已不存在）
        /// </summary>
        CityReader = 12,

        /// <summary>
        /// 14: 市级达标评估员
        /// </summary>
        CityCollection = 14,

        /// <summary>
        /// 15: 市级项目负责人(履约)
        /// </summary>
        CityProject = 15,

        /// <summary>
        /// 16: 市级资产管理员
        /// </summary>
        CityAssets = 16,

        /// <summary>
        /// 17: 市级统计查询员
        /// </summary>
        CityStatistics = 17,

        /// <summary>
        /// 18: 市级项目分管负责人
        /// </summary>
        CityProjectRegulator = 18,

        /// <summary>
        /// 100: 特聘专家
        /// </summary>
        CitySpecialExpert = 100,

        /// <summary>
        /// 190: 计划审核人
        /// </summary>
        CityPlanAudit = 190,

        /// <summary>
        /// 191: 计划审批人
        /// </summary>
        CityPlanApproval = 191,

        /// <summary>
        /// 192: 方案审批人
        /// </summary>
        CityProgramApproval = 192,

        /// <summary>
        /// 20: 区县管理员
        /// </summary> 
        CoutyAdmin = 20,

        /// <summary>
        /// 21: 区县用户（维修管理员）
        /// </summary>
        CoutyUser = 21,

        /// <summary>
        /// 22: 区县查看员（计划审核人）
        /// </summary>
        CoutyReader = 22,

        /// <summary>
        /// 23: 计划审批人
        /// </summary>
        CoutyPlanApproval = 23,

        /// <summary>
        /// 24: 达标评估员
        /// </summary>
        CoutyCollection = 24,

        /// <summary>
        /// 25: 履约项目负责人
        /// </summary>
        CoutyProject = 25,

        /// <summary>
        /// 26: 资产管理员
        /// </summary>
        CoutyAssets = 26,

        /// <summary>
        /// 27: 统计查询员
        /// </summary>
        CoutyStatistics = 27,

        /// <summary>
        /// 28: 项目分管负责人
        /// </summary>
        CoutyProjectRegulator = 28,

        /// <summary>
        /// 210: 方案审批人
        /// </summary>
        CoutyProgramApproval = 210,

        /// <summary>
        /// 211: 计划分项审核人
        /// </summary>
        CoutyPlanSubAudit = 211,

        /// <summary>
        /// 212: 计划初审人
        /// </summary>
        CoutyPlanFirstAudit = 212,

        /// <summary>
        /// 213: 合同送审员
        /// </summary>
        CoutyContractReview = 213,

        /// <summary>
        /// 214: 方案初审人
        /// </summary>
        CoutyProgramFirstAudit = 214,

        /// <summary>
        /// 215: 方案审核人
        /// </summary>
        CoutyProgramAudit = 215,

        /// <summary>
        /// 216: 基建审核人
        /// </summary>
        CoutyGroupAudit = 216,

        /// <summary>
        /// 218: 结算审核员
        /// </summary>
        CoutySettlementAudit = 218,

        /// <summary>
        /// 219: 纪检监察
        /// </summary>
        CoutyDisciplineInspection = 219,

        /// <summary>
        /// 230: 招标管理员
        /// </summary>
        CoutyTender = 230,

        /// <summary>
        /// 30: 校管理员
        /// </summary> 
        SchoolAdmin = 30,
        /// <summary>
        /// 31: 校长(维修审批员)
        /// </summary> 
        SchoolMaster = 31,
        /// <summary>
        /// 32: 校维修员
        /// </summary> 
        SchoolRepair = 32,
        /// <summary>
        /// 33: 教师(一般职工)
        /// </summary> 
        Teacher = 33,

        /// <summary>
        /// 34: 达标评估员
        /// </summary> 
        SchoolCollection = 34,

        /// <summary>
        /// 35: 履约项目负责人
        /// </summary> 
        SchoolProject = 35,

        /// <summary>
        /// 36: 资产管理员
        /// </summary> 
        SchoolAssets = 36,

        /// <summary>
        /// 37: 统计查询员
        /// </summary> 
        SchoolStatistics = 37,

        /// <summary>
        /// 38: 计划申报人
        /// </summary> 
        SchoolPlanDeclare = 38,

        /// <summary>
        /// 39: 计划审批人
        /// </summary> 
        SchoolPlanApproval = 39,

        /// <summary>
        /// 310: 方案申报人
        /// </summary> 
        SchoolProgramDeclare = 310,

        /// <summary>
        /// 315: 财务审批人
        /// </summary> 
        SchoolFinance = 315,

        /// <summary>
        /// 320: 单位方案审批人
        /// </summary> 
        SchoolApproval = 320,

        /// <summary>
        /// 330: 仓库管理员
        /// </summary> 
        SchoolCatalog = 330,

        /// <summary>
        /// 331: 物品采购审核员
        /// </summary> 
        SchoolPurchaseAudit = 331,

        /// <summary>
        /// 332: 物品采购审批员
        /// </summary> 
        SchoolPurchaseApproval = 332,

        /// <summary>
        /// 333: 物品领用审核人
        /// </summary> 
        SchoolApplyAudit = 333,

        /// <summary>
        /// 334: 物品领用审批人
        /// </summary> 
        SchoolApplyApproval = 334,

        /// <summary>
        /// 335: 物品采购员
        /// </summary> 
        SchoolPurchaseDeclare = 335,

        /// <summary>
        /// 340: 结算审核员
        /// </summary> 
        SchoolSettlementAudit = 340,

        /// <summary>
        /// 341: 招标管理员
        /// </summary> 
        SchoolTender = 341,

        /// <summary>
        /// 40: 企业管理员
        /// </summary> 
        CompanyAdmin = 40,

        /// <summary>
        /// 41: 企业派工员
        /// </summary> 
        CompanyDispatcher = 41,

        /// <summary>
        /// 42: 企业维修员
        /// </summary> 
        CompanyRepair = 42,

        /// <summary>
        /// 45: 履约项目负责人
        /// </summary> 
        CompanyProject = 45,

        /// <summary>
        /// 48: 投标管理员
        /// </summary> 
        CompanyTender = 48,

        /// <summary>
        /// 51: 账号管理员
        /// </summary> 
        SysAccount = 51,

        /// <summary>
        /// 55: 设备分类员
        /// </summary> 
        SysDeviceClass = 55,

        /// <summary>
        /// 家长——校服管理平台
        /// </summary>
        Parent = 500,

        /// <summary>
        /// 班主任——校服管理平台
        /// </summary>
        ClassTeacher = 370,


    }
}