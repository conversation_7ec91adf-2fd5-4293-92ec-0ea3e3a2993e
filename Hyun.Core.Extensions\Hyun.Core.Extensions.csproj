﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\build\common.targets" />

  <ItemGroup>
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Autofac.Extras.DynamicProxy" Version="7.1.0" />
    <PackageReference Include="Com.Ctrip.Framework.Apollo" Version="2.10.0" />
    <PackageReference Include="Com.Ctrip.Framework.Apollo.Configuration" Version="2.10.2" />
    <PackageReference Include="Consul" Version="********" />
  
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.19" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="8.0.13" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.13" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.13" />
    
    
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.3.8" />
    <PackageReference Include="NetDevPack.Security.JwtExtensions" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.2" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="8.0.0" />
	<PackageReference Include="OpenIddict.AspNetCore" Version="6.2.0" />
	<PackageReference Include="OpenIddict.Quartz" Version="6.2.0" />
	<PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
    <PackageReference Include="nacos-sdk-csharp-unofficial" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.AspNetCore" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.Extensions.Configuration" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.IniParser" Version="0.8.5" />
    <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.7" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.7.0" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hyun.Core.EventBus\Hyun.Core.EventBus.csproj" />
    <ProjectReference Include="..\Hyun.Core.Serilog\Hyun.Core.Serilog.csproj" />
    <ProjectReference Include="..\Hyun.Core.Services\Hyun.Core.Services.csproj" />
    <ProjectReference Include="..\Hyun.Core.Tasks\Hyun.Core.Tasks.csproj" />
    <ProjectReference Include="..\OpenIddict.SqlSugar\OpenIddict.SqlSugar.csproj" />
  </ItemGroup>

</Project>
