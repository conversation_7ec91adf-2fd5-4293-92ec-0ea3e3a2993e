﻿using Hyun.Core;
using Hyun.Core.Common;
using Hyun.Core.Model;
namespace Hyun.Old.Util
{
    public static class ApplicationConfig
    {
        /// <summary> 
        /// Cookie 加密key和向量 
        /// </summary> 
        public readonly static string CookieKey = AppSettings.app(new string[] { "hyun", "Cookie.Key" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string CookieIV = AppSettings.app(new string[] { "hyun", "Cookie.IV" }).ObjToString();
        /// <summary> 
        /// 分页 最大记录数 
        /// </summary>         /// </summary> 
        public readonly static string PaginationPageSize = AppSettings.app(new string[] { "hyun", "Pagination.PageSize" }).ObjToString();
        /// <summary> 
        /// 系统首页 
        /// </summary> 
        public readonly static string PageIndex = AppSettings.app(new string[] { "hyun", "Page.Index" }).ObjToString();
        /// <summary> 
        /// 系统登录页面 
        /// </summary> 
        public readonly static string PageLogin = AppSettings.app(new string[] { "hyun", "Page.Login" }).ObjToString();
        /// <summary> 
        /// 系统错误页 
        /// </summary> 
        public readonly static string PageError = AppSettings.app(new string[] { "hyun", "Page.Error" }).ObjToString();
        /// <summary> 
        /// 系统退出页面 
        /// </summary> 
        public readonly static string PageLogout = AppSettings.app(new string[] { "hyun", "Page.Logout" }).ObjToString();
        /// <summary> 
        /// 权限验证是否采用，正式发布时改成：true 
        /// </summary> 
        public readonly static string PermissionPageEnabled = AppSettings.app(new string[] { "hyun", "Permission.Page.Enabled" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string PermissionEnabled = AppSettings.app(new string[] { "hyun", "Permission.Enabled" }).ObjToString();
        /// <summary> 
        /// 页面白名单 
        /// </summary> 
        public readonly static string PermissionWhiteList = AppSettings.app(new string[] { "hyun", "Permission.WhiteList" }).ObjToString();
        /// <summary> 
        /// 单位导入 Excel 标题列：普通类型 
        /// </summary> 
        public readonly static string ImportUnit = AppSettings.app(new string[] { "hyun", "Import.Unit" }).ObjToString();
        /// <summary> 
        /// 单位导入 Excel 标题列：单位类型 
        /// </summary> 
        public readonly static string ImportUnitSchool = AppSettings.app(new string[] { "hyun", "Import.Unit.School" }).ObjToString();
        /// <summary> 
        /// 单位导入 Excel 标题列：企业类型 
        /// </summary> 
        public readonly static string ImportUnitCompany = AppSettings.app(new string[] { "hyun", "Import.Unit.Company" }).ObjToString();
        /// <summary> 
        /// 用户导入 Excel 标题列：带单位信息 
        /// </summary> 
        public readonly static string ImportUser = AppSettings.app(new string[] { "hyun", "Import.User" }).ObjToString();
        /// <summary> 
        /// 用户导入 Excel 标题列：本单位用户 
        /// </summary> 
        public readonly static string ImportUserMine = AppSettings.app(new string[] { "hyun", "Import.User.Mine" }).ObjToString();
        /// <summary> 
        /// 单位导入 Excel 标题列：本单位办公场所 
        /// </summary> 
        public readonly static string ImportAddress = AppSettings.app(new string[] { "hyun", "Import.Address" }).ObjToString();
        /// <summary> 
        /// 用户导入 Excel 标题列：招标企业服务项目、服务地区 
        /// </summary> 
        public readonly static string ImportCompanyAreaService = AppSettings.app(new string[] { "hyun", "Import.CompanyAreaService" }).ObjToString();
        /// <summary> 
        /// 设备库导入 Excel 标题列：设备名称,设备品牌,规格型号,具体配置或材质说明,数量,单位,单价,金额,设备属性 
        /// </summary> 
        public readonly static string ImportEquipmentList = AppSettings.app(new string[] { "hyun", "Import.EquipmentList" }).ObjToString();
        /// <summary> 
        /// 新版设备库导入 Excel 标题列：设备名称,设备品牌,规格型号,具体配置或材质说明,数量,单位,单价,金额,设备属性 
        /// </summary> 
        public readonly static string ImportProjectEquipmentList = AppSettings.app(new string[] { "hyun", "Import.Project.EquipmentList" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ImportProjectCompanyEquipmentList = AppSettings.app(new string[] { "hyun", "Import.Project.Company.EquipmentList" }).ObjToString();
        /// <summary> 
        /// 单位设备导入 Excel 标题列：供货单位,设备名称,设备品牌,设备型号,设备配置或材质说明,设备数量,计量单位,设备单价,采购时间,质保期（月）,经手人 
        /// </summary> 
        public readonly static string ImportSchoolEquipment = AppSettings.app(new string[] { "hyun", "Import.SchoolEquipment" }).ObjToString();
        /// <summary> 
        /// 单位设备导入 Excel 标题列：供货单位,设备名称,设备品牌,设备型号,设备配置或材质说明,设备数量,计量单位,设备单价,采购时间,质保期（月）,经手人 
        /// </summary> 
        public readonly static string ImportSchoolEquipmentV2 = AppSettings.app(new string[] { "hyun", "Import.SchoolEquipmentV2" }).ObjToString();
        /// <summary> 
        /// 合同设备导入 Excel 标题列：序号|设备名称|设备品牌|设备型号|具体配置或材质说明|数量|单位|单价(元）|金额（元）|质保期（月） 
        /// </summary> 
        public readonly static string ImportContractEquipmentList = AppSettings.app(new string[] { "hyun", "Import.Contract.EquipmentList" }).ObjToString();
        /// <summary> 
        /// 采购立项 
        /// </summary> 
        public readonly static string ImportContractProjectList = AppSettings.app(new string[] { "hyun", "Import.Contract.ProjectList" }).ObjToString();
        /// <summary> 
        /// 采购立项（吴江版） 
        /// </summary> 
        public readonly static string ImportContractProjectListWj = AppSettings.app(new string[] { "hyun", "Import.Contract.ProjectListWj" }).ObjToString();
        /// <summary> 
        /// 项目预算明细（北京预算编制） 
        /// </summary> 
        public readonly static string ImportPlanProjectListBj = AppSettings.app(new string[] { "hyun", "Import.Plan.ProjectListBj" }).ObjToString();
        /// <summary> 
        /// 项目清单导入（北京预算编制V3版本） 
        /// </summary> 
        public readonly static string ImportPlanProjectListBjV3 = AppSettings.app(new string[] { "hyun", "Import.Plan.ProjectListBjV3" }).ObjToString();
        /// <summary> 
        /// 采购立项 
        /// </summary> 
        public readonly static string ImportContractProjectListHc = AppSettings.app(new string[] { "hyun", "Import.Contract.ProjectListHc" }).ObjToString();
        /// <summary> 
        /// 项目清单导入（富阳方案审批v4版本） 
        /// </summary> 
        public readonly static string ImportEquipmentProjectListFy4 = AppSettings.app(new string[] { "hyun", "Import.Equipment.ProjectListFy4" }).ObjToString();
        /// <summary> 
        /// 南昌审批定制：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSettingNc = AppSettings.app(new string[] { "hyun", "Import.CapitalSettingNc" }).ObjToString();
        /// <summary> 
        /// 吴江审批定制：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSettingWj = AppSettings.app(new string[] { "hyun", "Import.CapitalSettingWj" }).ObjToString();
        /// <summary> 
        /// 计划编制V3：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSettingPlanV3 = AppSettings.app(new string[] { "hyun", "Import.CapitalSettingPlanV3" }).ObjToString();
        /// <summary> 
        /// 通州审批定制：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSettingTz = AppSettings.app(new string[] { "hyun", "Import.CapitalSettingTz" }).ObjToString();
        /// <summary> 
        /// 淮安审批定制：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSettingHa = AppSettings.app(new string[] { "hyun", "Import.CapitalSettingHa" }).ObjToString();
        /// <summary> 
        /// 海沧审批定制：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSettingHc = AppSettings.app(new string[] { "hyun", "Import.CapitalSettingHc" }).ObjToString();
        /// <summary> 
        /// Process3_0：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSourceJbxq = AppSettings.app(new string[] { "hyun", "Import.CapitalSourceJbxq" }).ObjToString();
        /// <summary> 
        /// Process3_0：导入资金来源 
        /// </summary> 
        public readonly static string ImportCapitalSourceV3 = AppSettings.app(new string[] { "hyun", "Import.CapitalSourceV3" }).ObjToString();
        /// <summary> 
        /// 单位计划导入 
        /// </summary> 
        public readonly static string ImportPlanSchoolLevel1 = AppSettings.app(new string[] { "hyun", "Import.Plan.School.Level1" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ImportPlanSchoolLevel2 = AppSettings.app(new string[] { "hyun", "Import.Plan.School.Level2" }).ObjToString();
        /// <summary> 
        /// 配件库导入 
        /// </summary> 
        public readonly static string ImportPartsItem = AppSettings.app(new string[] { "hyun", "Import.PartsItem" }).ObjToString();
        /// <summary> 
        /// 服务项目类别导入 
        /// </summary> 
        public readonly static string ImportServiceItem = AppSettings.app(new string[] { "hyun", "Import.ServiceItem" }).ObjToString();
        /// <summary> 
        /// 低值易耗批量导入 Excel 标题列： 
        /// </summary> 
        public readonly static string ImportSchoolMaterial = AppSettings.app(new string[] { "hyun", "Import.SchoolMaterial" }).ObjToString();
        /// <summary> 
        /// 低值易耗存量导入 Excel 标题列： 
        /// </summary> 
        public readonly static string ImportLowValueStockMaterial = AppSettings.app(new string[] { "hyun", "Import.LowValue.StockMaterial" }).ObjToString();
        /// <summary> 
        /// 富阳应急预算： 
        /// </summary> 
        public readonly static string ImportBudgetFy = AppSettings.app(new string[] { "hyun", "Import.BudgetFy" }).ObjToString();
        /// <summary> 
        /// 下属单位信息导入(达标评估数据) 
        /// </summary> 
        public readonly static string ImportCollection = AppSettings.app(new string[] { "hyun", "Import.Collection" }).ObjToString();
        /// <summary> 
        /// 计划编制导入修正金额 
        /// </summary> 
        public readonly static string ImportPlanPurchaseRevise = AppSettings.app(new string[] { "hyun", "Import.PlanPurchaseRevise" }).ObjToString();
        /// <summary> 
        /// 计划审核 
        /// </summary> 
        public readonly static string ImportPlanAudit = AppSettings.app(new string[] { "hyun", "Import.PlanAudit" }).ObjToString();
        /// <summary> 
        /// 北京资产处置，清单导入 
        /// </summary> 
        public readonly static string ImportBjAssetDisposalList = AppSettings.app(new string[] { "hyun", "Import.BjAssetDisposalList" }).ObjToString();
        /// <summary> 
        /// 张家港计划编制：预算明细导入 
        /// </summary> 
        public readonly static string ImportPlanProjectListZjg = AppSettings.app(new string[] { "hyun", "Import.Plan.ProjectListZjg" }).ObjToString();
        /// <summary> 
        /// 扬州广陵采购审批：采购明细导入 
        /// </summary> 
        public readonly static string ImportProcessProjectListYzGl = AppSettings.app(new string[] { "hyun", "Import.Process.ProjectListYzGl" }).ObjToString();
        /// <summary> 
        /// 区县配置立项清单 
        /// </summary> 
        public readonly static string ImportContractProjectListConfig = AppSettings.app(new string[] { "hyun", "Import.Contract.ProjectListConfig" }).ObjToString();
        /// <summary> 
        /// 危化品管理：单位存量数据导入 
        /// </summary> 
        public readonly static string ImportDangerChemicalsSchoolMaterial = AppSettings.app(new string[] { "hyun", "Import.DangerChemicals.SchoolMaterial" }).ObjToString();
        /// <summary> 
        /// 张家港集中采购审批：区县标准库导入 
        /// </summary> 
        public readonly static string ImportProcessZjgItemStandard = AppSettings.app(new string[] { "hyun", "Import.ProcessZjg.ItemStandard" }).ObjToString();
        /// <summary> 
        /// 用户导入 Excel 标题列：第三方用户白名单 
        /// </summary> 
        public readonly static string ImportUserThirdAllow = AppSettings.app(new string[] { "hyun", "Import.User.ThirdAllow" }).ObjToString();
        /// <summary> 
        /// 富阳魔方导入资产数据验证必须包含以下字段名称 
        /// </summary> 
        public readonly static string ImportFyAssetClear = AppSettings.app(new string[] { "hyun", "Import.FyAssetClear" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventSubmit = AppSettings.app(new string[] { "hyun", "SendMessage.Event.Submit" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventRepairNeedInSchool = AppSettings.app(new string[] { "hyun", "SendMessage.Event.RepairNeed.InSchool" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventDispatching = AppSettings.app(new string[] { "hyun", "SendMessage.Event.Dispatching" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventProjectAcceptance = AppSettings.app(new string[] { "hyun", "SendMessage.Event.ProjectAcceptance" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventProjectCodePost = AppSettings.app(new string[] { "hyun", "SendMessage.Event.ProjectCodePost" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventAuditEnterprise = AppSettings.app(new string[] { "hyun", "SendMessage.Event.AuditEnterprise" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventSendCode = AppSettings.app(new string[] { "hyun", "SendMessage.Event.SendCode" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventRegisterSchoolCode = AppSettings.app(new string[] { "hyun", "SendMessage.Event.RegisterSchoolCode" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventRegisterSupplierCode = AppSettings.app(new string[] { "hyun", "SendMessage.Event.RegisterSupplierCode" }).ObjToString();
        /// <summary> 
        ///  通用验证码格式
        /// </summary> 
        public readonly static string SendMessageEventSendBaseCode = AppSettings.app(new string[] { "hyun", "SendMessage.Event.SendBaseCode" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageEventBorrowCode = AppSettings.app(new string[] { "hyun", "SendMessage.Event.BorrowCode" }).ObjToString();
        /// <summary> 
        /// 南昌合同送审，领取合同、领取验收单发送验证码内容 
        /// </summary> 
        public readonly static string SeneMessageEventNcContractSendCheckCode = AppSettings.app(new string[] { "hyun", "SeneMessage.Event.NcContractSendCheckCode" }).ObjToString();
        /// <summary> 
        /// 南昌版合同送审，接收合同 
        /// </summary> 
        public readonly static string SendMessageEventContractReceive = AppSettings.app(new string[] { "hyun", "SendMessage.Event.ContractReceive" }).ObjToString();
        /// <summary> 
        /// 低值易耗，领取物品、领取验收单发送验证码内容 
        /// </summary> 
        public readonly static string SeneMessageEventLvLowValueCollarCheckCode = AppSettings.app(new string[] { "hyun", "SeneMessage.Event.LvLowValueCollarCheckCode" }).ObjToString();
        /// <summary> 
        /// 低值易耗，批量领取物品、领取验收单发送验证码内容 
        /// </summary> 
        public readonly static string SeneMessageEventLvLowValueBatchCollarCheckCode = AppSettings.app(new string[] { "hyun", "SeneMessage.Event.LvLowValueBatchCollarCheckCode" }).ObjToString();
        /// <summary> 
        /// 低值易耗，领取物品、物品归还提示信息 
        /// </summary> 
        public readonly static string SeneMessageEventLvLowValueRevertMsg = AppSettings.app(new string[] { "hyun", "SeneMessage.Event.LvLowValueRevertMsg" }).ObjToString();
        /// <summary> 
        /// 低值易耗，物流柜取货短信重发 
        /// </summary> 
        public readonly static string SeneMessageEventTerminalBox = AppSettings.app(new string[] { "hyun", "SeneMessage.Event.TerminalBox" }).ObjToString();
        /// <summary> 
        /// 危化品管理配货验证码 
        /// </summary> 
        public readonly static string SeneMessageEventDcDangerChemicalsDistributeCode = AppSettings.app(new string[] { "hyun", "SeneMessage.Event.DcDangerChemicalsDistributeCode" }).ObjToString();
        /// <summary> 
        /// 申请验收 
        /// </summary> 
        public readonly static string SendMessageEventAcceptanceApplication = AppSettings.app(new string[] { "hyun", "SendMessage.Event.AcceptanceApplication" }).ObjToString();
        /// <summary> 
        /// 提交验收文档 
        /// </summary> 
        public readonly static string SendMessageEventAcceptanceDocuments = AppSettings.app(new string[] { "hyun", "SendMessage.Event.AcceptanceDocuments" }).ObjToString();
        /// <summary> 
        /// 变更申请 
        /// </summary> 
        public readonly static string SendMessageEventApply = AppSettings.app(new string[] { "hyun", "SendMessage.Event.Apply" }).ObjToString();
        /// <summary> 
        /// 项目整改 
        /// </summary> 
        public readonly static string SendMessageEventProjectRectification = AppSettings.app(new string[] { "hyun", "SendMessage.Event.ProjectRectification" }).ObjToString();
        /// <summary> 
        /// 审核单位退回给企业（用于北京） 
        /// </summary> 
        public readonly static string SendMessageEventAuditBackBJ = AppSettings.app(new string[] { "hyun", "SendMessage.Event.AuditBackBJ" }).ObjToString();
        /// <summary> 
        /// 南昌版本审核审批，已采购给项目申报人发送短信 
        /// </summary> 
        public readonly static string SendMessageEventProjectPurchasedNc = AppSettings.app(new string[] { "hyun", "SendMessage.Event.ProjectPurchasedNc" }).ObjToString();
        /// <summary> 
        /// 南昌版本审核审批，审批不通过退回申报人给项目申报人发送短信 
        /// </summary> 
        public readonly static string SendMessageEventApprovalNoPassNc = AppSettings.app(new string[] { "hyun", "SendMessage.Event.ApprovalNoPassNc" }).ObjToString();
        /// <summary> 
        /// 镇江版本方案审批，转交下一步给项目分管领导发送短信 
        /// </summary> 
        public readonly static string SendMessageEventApprovalNext = AppSettings.app(new string[] { "hyun", "SendMessage.Event.ApprovalNext" }).ObjToString();
        /// <summary> 
        /// 海沧区招标管理，招标公示更改给投标企业发送短信 
        /// </summary> 
        public readonly static string SendMessageEventHcTenderUpdate = AppSettings.app(new string[] { "hyun", "SendMessage.Event.HcTenderUpdate" }).ObjToString();
        /// <summary> 
        /// 海沧区招标管理，招标公示撤销给投标企业发送短信 
        /// </summary> 
        public readonly static string SendMessageEventHcTenderRevoke = AppSettings.app(new string[] { "hyun", "SendMessage.Event.HcTenderRevoke" }).ObjToString();
        /// <summary> 
        /// 审批V3通用短信默默{项目名称}，{项目状态}， 请您及时处理 
        /// </summary> 
        public readonly static string SendMessageProcessV3TurnNextMsg = AppSettings.app(new string[] { "hyun", "SendMessage.ProcessV3.TurnNextMsg" }).ObjToString();
        /// <summary> 
        /// 危化品管理领用简易版领用通知 
        /// </summary> 
        public readonly static string SendMessageEventDcApplyMsg = AppSettings.app(new string[] { "hyun", "SendMessage.Event.DcApplyMsg" }).ObjToString();
        /// <summary> 
        /// 危化品管理领用简易版发放通知 
        /// </summary> 
        public readonly static string SendMessageEventDcGrantMsg = AppSettings.app(new string[] { "hyun", "SendMessage.Event.DcGrantMsg" }).ObjToString();
        /// <summary> 
        /// 低值易耗，物品存量库预警发送短信(0:手机号码  1：编码) 
        /// </summary> 
        public readonly static string SeneMessageEventLvLowValueStockWarningMsg = AppSettings.app(new string[] { "hyun", "SeneMessage.Event.LvLowValueStockWarningMsg" }).ObjToString();
        /// <summary> 
        /// 富阳方案审批向专家发放评审通知短信 
        /// </summary> 
        public readonly static string SendMessageEventFyProcessExpertReview = AppSettings.app(new string[] { "hyun", "SendMessage.Event.FyProcess.ExpertReview" }).ObjToString();
        /// <summary> 
        /// 富阳方案审批向专家发放签字通知短信 
        /// </summary> 
        public readonly static string SendMessageEventFyProcessExpertReviewSign = AppSettings.app(new string[] { "hyun", "SendMessage.Event.FyProcess.ExpertReviewSign" }).ObjToString();
        /// <summary> 
        /// 资产监管V6：存量资产导入 
        /// </summary> 
        public readonly static string ImportAssetsStockAsset = AppSettings.app(new string[] { "hyun", "Import.Assets.StockAsset" }).ObjToString();
        /// <summary> 
        /// 资产监管V6：单位增量资产导入 
        /// </summary> 
        public readonly static string ImportAssetsSchoolAsset = AppSettings.app(new string[] { "hyun", "Import.Assets.SchoolAsset" }).ObjToString();
        /// <summary> 
        /// 资产监管V6：处置清单导入 
        /// </summary> 
        public readonly static string ImportAssetsManagement = AppSettings.app(new string[] { "hyun", "Import.Assets.Management" }).ObjToString();
        /// <summary> 
        /// 资讯缩略图大小设置 
        /// </summary> 
        public readonly static string ThumbnailsArticlWidth = AppSettings.app(new string[] { "hyun", "Thumbnails.Articl.Width" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ThumbnailsArticlHeight = AppSettings.app(new string[] { "hyun", "Thumbnails.Articl.Height" }).ObjToString();
        /// <summary> 
        /// 上传文件类型控制 txt:60115 
        /// </summary> 
        public readonly static string FileUploadExt = AppSettings.app(new string[] { "hyun", "File.Upload.Ext" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FileUploadZipExt = AppSettings.app(new string[] { "hyun", "File.Upload.ZipExt" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FileUploadExtCode = AppSettings.app(new string[] { "hyun", "File.Upload.ExtCode" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string EncryptPublicKey = AppSettings.app(new string[] { "hyun", "Encrypt.PublicKey" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string EncryptPrivateKey = AppSettings.app(new string[] { "hyun", "Encrypt.PrivateKey" }).ObjToString();
        /// <summary> 
        /// 密码解密私钥 
        /// </summary> 
        public readonly static string EncryptPwdPrivateKey = AppSettings.app(new string[] { "hyun", "Encrypt.Pwd.PrivateKey" }).ObjToString();
        /// <summary> 
        /// 密码验证规则 
        /// </summary> 
        public readonly static string EncryptPwdRegex = AppSettings.app(new string[] { "hyun", "Encrypt.Pwd.Regex" }).ObjToString();
        /// <summary> 
        /// 验证码等待时间(秒) 
        /// </summary> 
        public readonly static string SendMessageWaitSecond = AppSettings.app(new string[] { "hyun", "SendMessage.WaitSecond" }).ObjToString();
        /// <summary> 
        /// 限制手机当天最多使用次数 
        /// </summary> 
        public readonly static string SendMessageTimesPhone = AppSettings.app(new string[] { "hyun", "SendMessage.TimesPhone" }).ObjToString();
        /// <summary> 
        /// 限制IP地址当天最多使用次数 
        /// </summary> 
        public readonly static string SendMessageTimesIP = AppSettings.app(new string[] { "hyun", "SendMessage.TimesIP" }).ObjToString();
        /// <summary> 
        /// 注册、找回密码当天限制次数 
        /// </summary> 
        public readonly static string SendMessageTimesDayFindPwd = AppSettings.app(new string[] { "hyun", "SendMessage.TimesDayFindPwd" }).ObjToString();
        /// <summary> 
        /// 控制短信验证码失效时长(分钟) 
        /// </summary> 
        public readonly static string SendMessageSmsDuration = AppSettings.app(new string[] { "hyun", "SendMessage.SmsDuration" }).ObjToString();
        /// <summary> 
        /// 开启个性化域名（0：禁用；1：开启） 
        /// </summary> 
        public readonly static string PersonalityDomain = AppSettings.app(new string[] { "hyun", "Personality.Domain" }).ObjToString();
        /// <summary> 
        /// 开启区县部门管理（0：禁用；1：开启） 
        /// </summary> 
        public readonly static string OpenDepartCounty = AppSettings.app(new string[] { "hyun", "Open.Depart.County" }).ObjToString();
        /// <summary> 
        /// 开启市级部门管理（0：禁用；1：开启） 
        /// </summary> 
        public readonly static string OpenDepartCity = AppSettings.app(new string[] { "hyun", "Open.Depart.City" }).ObjToString();
        /// <summary> 
        /// 资产监管，是否开启小数必须转换才可审核入资产库 
        /// </summary> 
        public readonly static string AssetIsInAssets = AppSettings.app(new string[] { "hyun", "Asset.IsInAssets" }).ObjToString();
        /// <summary> 
        /// 发送短信,开启状态：1：开启，0：关闭；通道：（1：掌骏）用户名、密码、地址 
        /// </summary> 
        public readonly static string SendMessageConfigOpenStatus = AppSettings.app(new string[] { "hyun", "SendMessage.Config.OpenStatus" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageConfigSendWay = AppSettings.app(new string[] { "hyun", "SendMessage.Config.SendWay" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageConfigUserId = AppSettings.app(new string[] { "hyun", "SendMessage.Config.UserId" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageConfigUserName = AppSettings.app(new string[] { "hyun", "SendMessage.Config.UserName" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageConfigPassword = AppSettings.app(new string[] { "hyun", "SendMessage.Config.Password" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageConfigUrl = AppSettings.app(new string[] { "hyun", "SendMessage.Config.Url" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SendMessageConfigClientSign = AppSettings.app(new string[] { "hyun", "SendMessage.Config.ClientSign" }).ObjToString();
        /// <summary> 
        /// 日志 
        /// </summary> 
        public readonly static string CommonLogFilePath = AppSettings.app(new string[] { "hyun", "CommonLog.FilePath" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ErrorLogFilePath = AppSettings.app(new string[] { "hyun", "ErrorLog.FilePath" }).ObjToString();
        /// <summary> 
        /// 项目审批版本控制（0：默认；1：徐州；2：北京；3：南昌；4：吴江；5：富阳；7:建邺  8:镇江 9:通州(阆中)  10:厦门 11:淮安经开区 12:淮安高职校 13:南京 14：宿城区 15:德兴市 16：金陵高中 18:启东） 
        /// </summary> 
        public readonly static string ProcessVersion = AppSettings.app(new string[] { "hyun", "Process.Version" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string PlatformCode = AppSettings.app(new string[] { "hyun", "Platform.Code" }).ObjToString();
        /// <summary> 
        /// 专家配置（0：无；1：市级；2：区级） 
        /// </summary> 
        public readonly static string ExpertOpenStatus = AppSettings.app(new string[] { "hyun", "Expert.OpenStatus" }).ObjToString();
        /// <summary> 
        /// 危化品管理模块级别（2：区级 ；3：校级） 
        /// </summary> 
        public readonly static string DangerChemicalsLevel = AppSettings.app(new string[] { "hyun", "DangerChemicals.Level" }).ObjToString();
        /// <summary> 
        /// 修改履约验收项目，根据不同配置跳转到不同页面；-1：企业创建项目；0：不关联；2：北京(f_ProjectDeclaration)；5：富阳(fy_PlanBudget)；8：镇江(zj_ProjectPurchase)；9：通州、厦门、吴江、阆中、宿城区(xa_ProjectPurchase) 
        /// </summary> 
        public readonly static string MProjectRelationDataSource = AppSettings.app(new string[] { "hyun", "MProject.RelationDataSource" }).ObjToString();
        /// <summary> 
        /// 控制首页履约验收图表显示（200：多校标准版、220：北京版、250：富阳版） 
        /// </summary> 
        public readonly static string ChartIndexProject = AppSettings.app(new string[] { "hyun", "Chart.IndexProject" }).ObjToString();
        /// <summary> 
        /// 地区配置 
        /// </summary> 
        public readonly static string defaultSetdefaultProvince = AppSettings.app(new string[] { "hyun", "defaultSet.defaultProvince" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string defaultSetdefaultCity = AppSettings.app(new string[] { "hyun", "defaultSet.defaultCity" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string defaultSetdefaultCounty = AppSettings.app(new string[] { "hyun", "defaultSet.defaultCounty" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string CenterMobileServerAddress = AppSettings.app(new string[] { "hyun", "Center.Mobile.Server.Address" }).ObjToString();
        /// <summary> 
        /// 二维码标题 
        /// </summary> 
        public readonly static string SnCodeTitle = AppSettings.app(new string[] { "hyun", "Sn.Code.Title" }).ObjToString();
        
        /// <summary> 
        /// 是否启用复杂验证码 
        /// </summary> 
        public readonly static string IsEnableMultVerificationCode = AppSettings.app(new string[] { "hyun", "IsEnableMultVerificationCode" }).ObjToString();
        /// <summary> 
        /// 履约验收打印项目和单位对应关系(1:一对一 0：一对多) 
        /// </summary> 
        public readonly static string ProjectIsSingleSchool = AppSettings.app(new string[] { "hyun", "Project.IsSingleSchool" }).ObjToString();
        /// <summary> 
        /// 二维码发放方式（1：单位代发；2：运营商发放; 
        /// </summary> 
        public readonly static string SnCodeSendWay = AppSettings.app(new string[] { "hyun", "SnCode.SendWay" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string CenterClientKey = AppSettings.app(new string[] { "hyun", "Center.ClientKey" }).ObjToString();
    
        /// <summary> 
        /// 是否开启远程分类  远程分类服务器地址:http://a.cneefix.com。  服务器公钥 
        /// </summary> 
        public readonly static string CurrentClassifyServerIsOpenRemotelyClassify = AppSettings.app(new string[] { "hyun", "Current.Classify.Server.IsOpenRemotelyClassify" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string CenterClassifyServerAddress = AppSettings.app(new string[] { "hyun", "Center.Classify.Server.Address" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ClassifyServerPublicKey = AppSettings.app(new string[] { "hyun", "Classify.Server.PublicKey" }).ObjToString();
        /// <summary> 
        /// 是否开启向中心获取短信回复结果 
        /// </summary> 
        public readonly static string CenterSmsServerIsOpenGetResult = AppSettings.app(new string[] { "hyun", "Center.Sms.Server.IsOpenGetResult" }).ObjToString();
        /// <summary> 
        /// 富阳钉钉对接 
        /// </summary> 
        public readonly static string ddAgentId = AppSettings.app(new string[] { "hyun", "dd.AgentId" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ddAppKey = AppSettings.app(new string[] { "hyun", "dd.AppKey" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ddAppSecret = AppSettings.app(new string[] { "hyun", "dd.AppSecret" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string ddCorpid = AppSettings.app(new string[] { "hyun", "dd.Corpid" }).ObjToString();
        /// <summary> 
        /// 南京十三中 微研平台 
        /// </summary> 
        public readonly static string dysoftauthauthHost = AppSettings.app(new string[] { "hyun", "dysoftauth.authHost" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string dysoftauthdataHost = AppSettings.app(new string[] { "hyun", "dysoftauth.dataHost" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string dysoftauthclientID = AppSettings.app(new string[] { "hyun", "dysoftauth.clientID" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string dysoftauthclientSecret = AppSettings.app(new string[] { "hyun", "dysoftauth.clientSecret" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string dysoftauthcallBackUrl = AppSettings.app(new string[] { "hyun", "dysoftauth.callBackUrl" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string dysoftauthLogout = AppSettings.app(new string[] { "hyun", "dysoftauth.Logout" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string SSOClientUrl2 = AppSettings.app(new string[] { "hyun", "SSO.Client.Url2" }).ObjToString();
        /// <summary> 
        /// 资产监管开放采购入库接口 0：不接入  1：接入 
        /// </summary> 
        public readonly static string AssetsOpenEntrance = AppSettings.app(new string[] { "hyun", "Assets.OpenEntrance" }).ObjToString();
        /// <summary> 
        /// 资产监管开放接口URL(南京、富阳共用) 
        /// </summary> 
        public readonly static string AssetsOpenUrl = AppSettings.app(new string[] { "hyun", "Assets.OpenUrl" }).ObjToString();
        /// <summary> 
        /// 资产监管开放接口授权码(南京、富阳共用，对应富阳sysCode值) 
        /// </summary> 
        public readonly static string AssetsOpenCode = AppSettings.app(new string[] { "hyun", "Assets.OpenCode" }).ObjToString();
        /// <summary> 
        /// 富阳固定资产Authority值 
        /// </summary> 
        public readonly static string AssetsAuthority = AppSettings.app(new string[] { "hyun", "Assets.Authority" }).ObjToString();
        /// <summary> 
        /// 富阳rgCode值 
        /// </summary> 
        public readonly static string AssetsRgCode = AppSettings.app(new string[] { "hyun", "Assets.RgCode" }).ObjToString();
        /// <summary> 
        /// 固定资产审核入库界定日期 
        /// </summary> 
        public readonly static string AssetsBeginDate = AppSettings.app(new string[] { "hyun", "Assets.BeginDate" }).ObjToString();
        /// <summary> 
        /// 企业二维码申领界定日期 
        /// </summary> 
        public readonly static string ProjectDefinitionDate = AppSettings.app(new string[] { "hyun", "Project.DefinitionDate" }).ObjToString();
        /// <summary> 
        /// 物流柜接口URL(Socket请求) 
        /// </summary> 
        public readonly static string LowValueOpenUrl = AppSettings.app(new string[] { "hyun", "LowValue.OpenUrl" }).ObjToString();
        /// <summary> 
        /// 物流柜上传存放物品信息URL(Http请求) 
        /// </summary> 
        public readonly static string LowValueDataLoadUrl = AppSettings.app(new string[] { "hyun", "LowValue.DataLoadUrl" }).ObjToString();
        /// <summary> 
        /// 中爆危化品平台接口URL(Http请求) 
        /// </summary> 
        public readonly static string DangerChemicalsZbUrl = AppSettings.app(new string[] { "hyun", "DangerChemicals.ZbUrl" }).ObjToString();
        /// <summary> 
        /// 中爆危化品平台接口Id 
        /// </summary> 
        public readonly static string DangerChemicalsZbId = AppSettings.app(new string[] { "hyun", "DangerChemicals.ZbId" }).ObjToString();
        /// <summary> 
        /// 中爆危化品平台接口用户名 
        /// </summary> 
        public readonly static string DangerChemicalsZbUserName = AppSettings.app(new string[] { "hyun", "DangerChemicals.ZbUserName" }).ObjToString();
        /// <summary> 
        /// 中爆危化品平台接口密码 
        /// </summary> 
        public readonly static string DangerChemicalsZbPwd = AppSettings.app(new string[] { "hyun", "DangerChemicals.ZbPwd" }).ObjToString();
        /// <summary> 
        /// 中爆危化品平台接口密码 
        /// </summary> 
        public readonly static string DangerChemicalsApiCallType = AppSettings.app(new string[] { "hyun", "DangerChemicals.ApiCallType" }).ObjToString();
        /// <summary> 
        /// 是否开启微信小程序消息推送.1：推送；0：不推送 
        /// </summary> 
        public readonly static string WeixinMessageSendIsOpen = AppSettings.app(new string[] { "hyun", "WeixinMessageSend.IsOpen" }).ObjToString();
        /// <summary> 
        ///  微信小程序秘钥信息  
        /// </summary> 
        public readonly static string WxOpenAppId = AppSettings.app(new string[] { "hyun", "WxOpenAppId" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string WxOpenAppSecret = AppSettings.app(new string[] { "hyun", "WxOpenAppSecret" }).ObjToString();
        /// <summary> 
        /// 富阳魔方、图书、资产 
        /// </summary> 
        public readonly static string FyMfOpenUrl = AppSettings.app(new string[] { "hyun", "Fy.Mf.OpenUrl" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyMfAuthorization = AppSettings.app(new string[] { "hyun", "Fy.Mf.Authorization" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyMfApiToken = AppSettings.app(new string[] { "hyun", "Fy.Mf.ApiToken" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyTsOpenUrl = AppSettings.app(new string[] { "hyun", "Fy.Ts.OpenUrl" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyTsAppKey = AppSettings.app(new string[] { "hyun", "Fy.Ts.AppKey" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyTsAppSecret = AppSettings.app(new string[] { "hyun", "Fy.Ts.AppSecret" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyZcOpenUrl = AppSettings.app(new string[] { "hyun", "Fy.Zc.OpenUrl" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyZcAppKey = AppSettings.app(new string[] { "hyun", "Fy.Zc.AppKey" }).ObjToString();
        /// <summary> 
        ///  
        /// </summary> 
        public readonly static string FyZcAppSecret = AppSettings.app(new string[] { "hyun", "Fy.Zc.AppSecret" }).ObjToString();

    }
}