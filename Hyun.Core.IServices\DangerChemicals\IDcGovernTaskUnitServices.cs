﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcGovernTaskUnit接口方法
    ///</summary>
    public interface IDcGovernTaskUnitServices : IBaseServices<DcGovernTaskUnit>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<DcGovernTaskUnit> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<DcGovernTaskUnit>> Find(Expression<Func<DcGovernTaskUnit, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcGovernTaskUnitParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<DcGovernTaskUnit>> GetPaged(DcGovernTaskUnitParam param);

        /// <summary>
        /// 危化品检查添加单位
        /// </summary>
        /// <param name="schoolId">单位ID</param>
        /// <param name="name">任务名称</param>
        /// <param name="governYear">整治年度</param>
        /// <param name="userId">创建人</param>
        /// <param name="unitId">创建单位</param>
        /// <param name="unitType">单位类型</param>
        /// <returns></returns>
        //<used>1</used>
        Task<Result> SchoolInsert(long schoolId, string name, int governYear, long userId, long unitId, int unitType);

        /// <summary>
        /// 删除单位
        /// </summary>
        /// <param name="governTaskId"></param>
        /// <param name="schoolId"></param>
        /// <param name="userId"></param>
        /// <param name="unitId"></param>
        /// <param name="unitType"></param>
        /// <returns></returns>
        //<used>1</used>
        Task<Result> SchoolDelete(long governTaskId, long schoolId, long userId, long unitId, int unitType);

        //<used>1</used>
        Task<Result> InsertUpdate(long governTaskId, DateTime checkingDate, string leaderusers, int leaderusersCount, string expertUsers, int expertUsersCount, string workUsers, int workUsersCount, int checkingUserNum, long userId, long unitId, long id, List<long> listAttach);

    }
}

