namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///校服班级学生
    ///</summary>
    [SugarTable("p_Student","校服班级学生")]
    public class PStudent : BaseEntity
    {

          public PStudent()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///班级Id
          /// </summary>
          public long UniformClassId { get; set; }

           /// <summary>
           ///学号
          /// </summary>
          [SugarColumn(Length = 31)]
          public string StudentNo { get; set; }

           /// <summary>
           ///姓名
          /// </summary>
          [SugarColumn(Length = 31)]
          public string StudentName { get; set; }

           /// <summary>
           ///性别
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string Sex { get; set; }


        /// <summary>
        /// 身份证号码
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string IDCard { get; set; }

          /// <summary>
          ///身份证后6位
          /// </summary>
          [SugarColumn(Length = 31, IsNullable = true)]
          public string IDCard6 { get; set; }

           /// <summary>
           ///家长联系手机号
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string ParentMobile { get; set; }

           /// <summary>
           ///家长Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ParentUserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

