﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///BDictionaryRelation接口方法
    ///</summary>
    public interface IBDictionaryRelationServices : IBaseServices<BDictionaryRelation>
    {

       /// <summary>
       /// 新增修改
       /// </summary>
       /// <param name="o">BDictionaryRelation对象</param>
       /// <returns></returns>
       Task<Result> InsertUpdate(BDictionaryRelation o);

       /// <summary>
       /// 批量新增
       /// </summary>
       /// <param name="o">List<BDictionaryRelation>对象</param>
       /// <returns></returns>
       Task<Result> BatchAdd(List<BDictionaryRelation> list);

       /// <summary>
       /// 根据Id删除数据【假删除】
       /// </summary>
       /// <param name="id">Id值</param>
       /// <returns></returns>
       Task<Result> FakeDeleteById(long id);

       /// <summary>
       /// 根据Id集合批量删除数据【假删除】
       /// </summary>
       ///  <param name="ids">id集合逗号分隔</param>
       /// <returns></returns>
       Task<Result> FakeDeleteByIds(string ids);

       /// <summary>
       /// 根据Id删除数据【真删除】
       /// </summary>
       /// <param name="id">Id值</param>
       /// <returns></returns>
       Task<Result> DeleteById(long id);

       /// <summary>
       /// 根据Id集合批量删除数据【真删除】
       /// </summary>
       /// <param name="ids">id集合逗号分隔</param>
       /// <returns></returns>
       Task<Result> DeleteByIds(string ids);

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<BDictionaryRelation> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<BDictionaryRelation>> Find(Expression<Func<BDictionaryRelation, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
       /// <param name="param">BDictionaryRelationParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<BDictionaryRelation>> GetPaged(BDictionaryRelationParam param);

    }
}

