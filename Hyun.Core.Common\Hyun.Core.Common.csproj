﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\build\common.targets" />

  <ItemGroup>
    <Compile Remove="HttpRestSharp\**" />
    <EmbeddedResource Remove="HttpRestSharp\**" />
    <None Remove="HttpRestSharp\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="LogHelper\ILoggerHelper.cs" />
    <Compile Remove="LogHelper\LogHelper.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="ImageSharp\fonts\Candara.ttf" />
    <None Remove="ImageSharp\fonts\impact.ttf" />
    <None Remove="ImageSharp\fonts\monbaiti.ttf" />
    <None Remove="ImageSharp\fonts\STCAIYUN.ttf" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="Magicodes.IE.Excel" Version="2.7.5.1" />
    <PackageReference Include="InitQ" Version="********" />
    <PackageReference Include="log4net" Version="2.0.17" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.Core" Version="1.2.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="PinYinConverterCore" Version="1.0.2" />
    <PackageReference Include="MiniProfiler.Shared" Version="4.3.8" />
    <PackageReference Include="RestSharp" Version="111.4.1" />
    <PackageReference Include="Serilog.Expressions" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="SixLabors.Fonts" Version="2.0.4" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.4" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />

    <PackageReference Include="Serilog" Version="4.0.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.4.4" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hyun.Core.Model\Hyun.Core.Model.csproj" />
    <ProjectReference Include="..\Hyun.Core.Serilog.Es\Hyun.Core.Serilog.Es.csproj" />
    <ProjectReference Include="..\Ocelot.Provider.Nacos\Ocelot.Provider.Nacos.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Core\" />
    <Folder Include="HyunUtil\Enum\" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="ImageSharp\fonts\Candara.ttf" />
    <EmbeddedResource Include="ImageSharp\fonts\impact.ttf" />
    <EmbeddedResource Include="ImageSharp\fonts\monbaiti.ttf" />
    <EmbeddedResource Include="ImageSharp\fonts\STCAIYUN.ttf" />
  </ItemGroup>

</Project>
