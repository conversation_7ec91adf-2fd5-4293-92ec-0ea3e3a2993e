﻿using Hyun.Core.Common.DB;
using Hyun.Core.Model.Validator;
namespace Hyun.Core.Services
{

    ///<summary>
    ///BDictionaryRelation方法
    ///</summary>
    public class BDictionaryRelationServices :  BaseServices<BDictionaryRelation>, IBDictionaryRelationServices
    {

          public BDictionaryRelationServices()
          {

          }
         /// <summary>
         /// 新增修改
         /// </summary>
         /// <param name="o">BDictionaryRelation对象</param>
         /// <returns></returns>
         public async Task<Result> InsertUpdate(BDictionaryRelation o)
         {
                 Result r = new Result();


                 if (o.Id > 0)
                 {
                         await base.Update(o);
                         r.flag = 1;
                         r.msg = "修改成功";
                 }
                 else
                 {
                         o.Id = BaseDBConfig.GetYitterId();
                         await base.Add(o);
                         r.flag = 1;
                         r.msg = "保存成功";
                 }
                 return r;
          }


         /// <summary>
         /// 批量添加
         /// </summary>
         /// <param name="o">List<BDictionaryRelation>对象</param>
         /// <returns></returns>
         public async Task<Result> BatchAdd(List<BDictionaryRelation> list)
         {
                 Result r = new Result();

                 foreach (BDictionaryRelation l in list)
                 {
                         l.Id = BaseDBConfig.GetYitterId();
                 }
                 await base.Add(list);
                 r.flag = 1;
                 r.msg = "批量添加成功";
                 return r;
          }


         /// <summary>
         /// 根据Id删除数据【假删除】
         /// </summary>
         /// <param name="id">Id值</param>
         /// <returns></returns>
         public async Task<Result> FakeDeleteById(long id)
         {
                 Result r = new Result();
                 await this.Db.Updateable<BDictionaryRelation>().SetColumns(f => new BDictionaryRelation() {IsDeleted = true}).Where(f => f.Id == id).ExecuteCommandAsync();
                 return r;
         }


         /// <summary>
         /// 根据Id集合批量删除数据【假删除】
         /// </summary>
         /// <param name="ids">id集合逗号分隔</param>
         /// <returns></returns>
         public async Task<Result> FakeDeleteByIds(string ids)
         {
                 Result r = new Result();
                 List<long> listId = ids.Split(',').Select(long.Parse).ToList();
                 await this.Db.Updateable<BDictionaryRelation>().SetColumns(f => new BDictionaryRelation() {IsDeleted = true}).Where(f => listId.Contains(f.Id)).ExecuteCommandAsync();
                 return r;
          }


         /// <summary>
         /// 根据Id删除数据【真删除】
         /// </summary>
         /// <param name="id">Id值</param>
         /// <returns></returns>
        public async Task<Result> DeleteById(long id)
         {
                 Result r = new Result();
                 if(await base.DeleteById(id))
                 {
                          r.flag = 1;
                          r.msg = "删除成功";
                  }
                 return r;
          }


         /// <summary>
         /// 根据Id集合批量删除数据【真删除】
         /// </summary>
         /// <param name="ids">id集合逗号分隔</param>
         /// <returns></returns>
         public async Task<Result> DeleteByIds(string ids)
         {
                 Result r = new Result();
                 object[] listId = ids.Split(',').Cast<object>().ToArray();
                 await base.DeleteByIds(listId);
                 r.flag = 1;
                 r.msg = "批量删除成功";
                 return r;
         }



         #region 查询数据
         /// <summary>
         /// 根据Id查询对象
         /// </summary>
         /// <param name="id">Id</param>
         /// <returns></returns>
         public async Task<BDictionaryRelation> GetById(long id)
         {
             return await base.QueryById(id);
         }

         /// <summary>
         /// 根据查询条件获取数据集合
         /// </summary>
         /// <param name="expression">表达式</param>
         /// <returns></returns>
         public async Task<List<BDictionaryRelation>> Find(Expression<Func<BDictionaryRelation, bool>> expression)
         {
             return await base.Query(expression);
         }

         /// <summary>
         /// 适应装备平台查询方法
         /// </summary>
         /// <param name="param">BDictionaryRelationParam实体参数</param>
         /// <param name="page">页码</param>
         /// <param name="pageLength">每页显示条数</param>
         /// <param name="orderBy">排序，例如 Id DESC</param>
         /// <returns></returns>
         public async Task<PageModel<BDictionaryRelation>> GetPaged(BDictionaryRelationParam param)
         {
             var expression = ListFilter(param);
             string orderByFields = string.Empty;
             if (param.sortModel != null && param.sortModel.Count > 0)
             {
                 orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
             }
             return await base.QueryPage(expression, param.pageIndex,param.pageSize, orderByFields);
         }
         #endregion

         #region 私有方法
         /// <summary>
         /// 组装查询条件
         /// </summary>
         /// <param name="param">BDictionaryRelationParam实体参数</param>
         /// <returns></returns>
         private Expression<Func<BDictionaryRelation, bool>> ListFilter(BDictionaryRelationParam param)
         {
             var expression = LinqExtensions.True<BDictionaryRelation>();
             if (param != null)
             {
                 if (!string.IsNullOrEmpty(param.Ids))
                 {
                     long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                     expression = expression.AndNew(t => idArr.Contains(t.Id));
                  }
              }
              return expression;
           }
           #endregion
    }
}

