namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///企业银行账号信息
    ///</summary>
    [SugarTable("p_UnitBankAccount","企业银行账号信息")]
    public class PUnitBankAccount : BaseEntity
    {

          public PUnitBankAccount()
          {

          }

           /// <summary>
           ///性质类型：1市级、2区县、3单位、4企业
          /// </summary>
          public int UnitType { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///开户行
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string BankName { get; set; }

           /// <summary>
           ///账号
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string BankAccount { get; set; }

           /// <summary>
           ///行号
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string BankNumber { get; set; }

           /// <summary>
           ///电话
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string TelNumber { get; set; }

           /// <summary>
           ///统一社会信用代码
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string SocialCreditCode { get; set; }

           /// <summary>
           ///户主名称
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string UserName { get; set; }

           /// <summary>
           ///添加时间
          /// </summary>
          public DateTime RegTime { get; set; }

           /// <summary>
           ///添加人
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///添加单位
          /// </summary>
          public long AddUnitId { get; set; }

           /// <summary>
           ///是否默认（1：是 0：否）默认1
          /// </summary>
          public int IsDefault { get; set; }

           /// <summary>
           ///说明文字
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

