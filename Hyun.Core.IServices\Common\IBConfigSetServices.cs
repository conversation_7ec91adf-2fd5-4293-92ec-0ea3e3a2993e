﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///BConfigSet接口方法
    ///</summary>
    public interface IBConfigSetServices : IBaseServices<BConfigSet>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<BConfigSet> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<BConfigSet>> Find(Expression<Func<BConfigSet, bool>> expression);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">BConfigSetParam实体参数</param>
        /// <returns></returns>
        Task<List<BConfigSet>> Find(BConfigSetParam param);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">BConfigSetParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<BConfigSet>> GetPaged(BConfigSetParam param);
        //<used>1</used>
        /// <summary>
        /// 
        /// </summary>
        /// <param name="moduleCode">模块编号</param>
        /// <param name="unitType">当前单位类型</param>
        /// <param name="unitId">单位Id</param>
        /// <param name="userId">用户Id</param>
        /// <param name="optType">操作类型，可扩展，@OptType = 1 下一年恢复默认值</param>
        /// <returns></returns>
        Task<List<BConfigSet>> GetByModule(string moduleCode, int unitType, long unitId, long userId, int optType);

        //<used>0</used>
        Task<List<BConfigSet>> Insert(List<BConfigSet> entityCollection);

        //<used>0</used>
        Task<bool> PushData(long sourceId, long unityId, string typeCode);

       //<used>1</used>
       Task<Result> InsertUpdate(string moduleCode, long configSetId, string configValue, int unitType, long unitId, long userId);

    }
}

