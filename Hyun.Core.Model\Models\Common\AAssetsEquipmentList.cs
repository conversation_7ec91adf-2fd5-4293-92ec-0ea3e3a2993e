namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///资产清单（调出、报废等数据不在单位资产表中显示，但是可以从调出历史、报废历史中查看）
    ///</summary>
    [SugarTable("a_AssetsEquipmentList","资产清单（调出、报废等数据不在单位资产表中显示，但是可以从调出历史、报废历史中查看）")]
    public class AAssetsEquipmentList : BaseEntity
    {

          public AAssetsEquipmentList()
          {

          }

           /// <summary>
           ///企业Id
          /// </summary>
          public long CompanyId { get; set; }

           /// <summary>
           ///项目Id(没有填0)
          /// </summary>
          public long ProjectId { get; set; }

           /// <summary>
           ///分项Id（没有填0）
          /// </summary>
          public long ProjectItemId { get; set; }

           /// <summary>
           ///项目名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ProjectName { get; set; }

           /// <summary>
           ///项目设备列表Id
          /// </summary>
          public long EquipmentListId { get; set; }

           /// <summary>
           ///设备类型（0：设备；1：耗材； 2：工程；3：服务）
          /// </summary>
          public int EquipmentType { get; set; }

           /// <summary>
           ///设备名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///设备品牌
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Brand { get; set; }

           /// <summary>
           ///规格型号
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Model { get; set; }

           /// <summary>
           ///说明
          /// </summary>
          [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
          public string Explain { get; set; }

           /// <summary>
           ///数量
          /// </summary>
          public decimal Num { get; set; }

           /// <summary>
           ///单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

        /// <summary>
        ///单价
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Price { get; set; }

        /// <summary>
        ///金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Sum { get; set; }

           /// <summary>
           ///质保月份
          /// </summary>
          public int WarrantyMonth { get; set; }

           /// <summary>
           ///报废期
          /// </summary>
          public int ScrapMonth { get; set; }

           /// <summary>
           ///质保截止日期
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? Warranty { get; set; }

           /// <summary>
           ///报废期截止日期
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? Scrap { get; set; }

           /// <summary>
           ///设备类别Id
          /// </summary>
          public long ClassId { get; set; }

           /// <summary>
           ///设备类别名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ClassName { get; set; }

           /// <summary>
           ///标准设备名称Id
          /// </summary>
          public long DeviceId { get; set; }

           /// <summary>
           ///标准设备名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string DeviceName { get; set; }

           /// <summary>
           ///验收日期/采购日期
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? CheckDate { get; set; }

           /// <summary>
           ///调拨标识（0：正常；1：调入；2：拨出；3：预调拨）(调出不显示在资产表)
          /// </summary>
          public int AllocationStatuz { get; set; }

           /// <summary>
           ///报废数量
          /// </summary>
          public decimal ScrapNum { get; set; }

           /// <summary>
           ///剩余数量（入室后剩余数量）（作废）
          /// </summary>
          public decimal LeftNum { get; set; }

           /// <summary>
           ///设备来源（1：履约企业录入；2：资产监管 3：履约单位录入  4：存量入库）
          /// </summary>
          public int SourceType { get; set; }

           /// <summary>
           ///经办人
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string Attn { get; set; }

           /// <summary>
           ///录入时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///使用对象（0：所有人；1：教学；2：办公   ）
          /// </summary>
          public int UseType { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///报废标识（0：未报废；1：已报废；2：待报废）
          /// </summary>
          public int IsScrap { get; set; }

           /// <summary>
           ///已借用数量
          /// </summary>
          public decimal BorrowedNum { get; set; }

           /// <summary>
           ///原资产清单Id(调拨源头)
          /// </summary>
          public long ForeAssetsListId { get; set; }

           /// <summary>
           ///固定资产表Id
          /// </summary>
          public long PermanentAssetId { get; set; }

           /// <summary>
           ///使用人Id
          /// </summary>
          public long UseUserId { get; set; }

           /// <summary>
           ///使用部门Id
          /// </summary>
          public long UseDepartmentId { get; set; }

           /// <summary>
           ///数量
          /// </summary>
          public int AssetNum { get; set; }

           /// <summary>
           ///资产单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string AssetUnitName { get; set; }

           /// <summary>
           ///上传状态(0：已上传，其它数字代表失败次数，默认为NULL)
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? UploadStatuz { get; set; }

           /// <summary>
           ///管理部门Id
          /// </summary>
          public long ManagerDepartmentId { get; set; }

           /// <summary>
           ///管理人Id
          /// </summary>
          public long ManagerUserId { get; set; }

           /// <summary>
           ///价值类型（包括：原值 、暂估值、重置值、评估值、无价值）默认原值
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string ValueType { get; set; }

           /// <summary>
           ///使用状况（包括：在用、出租出借、闲置、毁损待报废、对外投资、担保、其他）默认在用
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UsageStatus { get; set; }

           /// <summary>
           ///使用方向（包括：教学、科研、行政、其他）默认教学
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UseDirection { get; set; }

           /// <summary>
           ///取得方式（包括：新购、调拨、接受捐赠、置换、盘盈、自建、其他）默认新购
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string AcquireWay { get; set; }

           /// <summary>
           ///国家或地区 默认：中国
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string CountryOrRegion { get; set; }

           /// <summary>
           ///发票号
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string Invoice { get; set; }

           /// <summary>
           ///产权是否清晰（是   否）默认是
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string PropertyRight { get; set; }

           /// <summary>
           ///记账凭证号
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string VoucherNo { get; set; }

           /// <summary>
           ///财务入账账期
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? FinanceDate { get; set; }

           /// <summary>
           ///文物等级（包括：一级、二级、三级、一般）默认空白
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string CulturalRelicGrade { get; set; }

           /// <summary>
           ///文物来源地
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string CulturalRelicPlace { get; set; }

           /// <summary>
           ///藏品年代
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string CollectionAge { get; set; }

           /// <summary>
           ///合同编号
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string ContractNo { get; set; }

           /// <summary>
           ///财政编码
          /// </summary>
          [SugarColumn(Length = 15,IsNullable = true)]
          public string StandardCode { get; set; }

           /// <summary>
           ///取得日期
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? BuildDate { get; set; }

           /// <summary>
           ///第三方Id
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string ThirdKeyId { get; set; }

           /// <summary>
           ///资金来源
          /// </summary>
          public int SourceFund { get; set; }

           /// <summary>
           ///资金备注
          /// </summary>
          [SugarColumn(Length = 511)]
          public string FundRemark { get; set; }

           /// <summary>
           ///盘点人
          /// </summary>
          public long InventoryUserId { get; set; }

           /// <summary>
           ///领用时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? CollectionTime { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

