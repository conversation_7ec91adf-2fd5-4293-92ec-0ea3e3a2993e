namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批条件设置
    ///</summary>
    [SugarTable("dc_AuditCondition","审批条件设置")]
    public class DcAuditCondition : BaseEntity
    {

          public DcAuditCondition()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///审批流程编号，自定义（1001：采购审核 ，1002：采购审批，2001,：领用审核，2002：领用审批）
          /// </summary>
          public int AuditCode { get; set; }

        /// <summary>
        ///最小值
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal MinValue { get; set; }

        /// <summary>
        ///最大值
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal MaxValue { get; set; }

           /// <summary>
           ///操作人
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///操作时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

