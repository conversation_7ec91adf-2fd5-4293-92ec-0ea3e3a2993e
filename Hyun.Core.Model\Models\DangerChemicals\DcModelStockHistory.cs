namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///型号库存历史表
    ///</summary>
    [SugarTable("dc_ModelStockHistory","型号库存历史表")]
    public class DcModelStockHistory : BaseEntity
    {

          public DcModelStockHistory()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///月度
          /// </summary>
          public int HistoryMonth { get; set; }

           /// <summary>
           ///分类Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///规格型号Id
          /// </summary>
          public long SchoolMaterialModelId { get; set; }

        /// <summary>
        ///库存量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal StockNum { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

