namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位物品规格型号
    ///</summary>
    [SugarTable("dc_SchoolMaterialModel","单位物品规格型号")]
    public class DcSchoolMaterialModel : BaseEntity
    {

          public DcSchoolMaterialModel()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///单位库Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///型号基础库Id（dc_BaseModelExtension表Id）
          /// </summary>
          public long BaseModelBrandId { get; set; }

           /// <summary>
           ///规格型号
          /// </summary>
          [SugarColumn(Length = 511)]
          public string Model { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Remark { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///教育部代码
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string EduCode { get; set; }

           /// <summary>
           ///CAS号
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string Cas { get; set; }

           /// <summary>
           ///最高限量
          /// </summary>
          [SugarColumn(IsNullable = true, ColumnDataType = "money")]
          public decimal? Limited { get; set; }

           /// <summary>
           ///适用学段
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string SchoolStagez { get; set; }

           /// <summary>
           ///是否需要审批
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsNeedApproval { get; set; }

           /// <summary>
           ///是否需要报备
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsNeedReport { get; set; }

           /// <summary>
           ///是否易制爆
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsDetonate { get; set; }

           /// <summary>
           ///是否易制毒
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsPoison { get; set; }

           /// <summary>
           ///是否易燃
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsBurn { get; set; }

           /// <summary>
           ///是否易爆
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsBlast { get; set; }

           /// <summary>
           ///是否有毒
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsToxic { get; set; }

           /// <summary>
           ///是否剧毒
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsHyperToxic { get; set; }

           /// <summary>
           ///是否腐蚀
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsCorrode { get; set; }

           /// <summary>
           ///是否其它
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsOther { get; set; }

        /// <summary>
        ///存量预警值
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal WarningValue { get; set; }

           /// <summary>
           ///有效期预警值（天)
          /// </summary>
          
          public decimal ValidityValue { get; set; }

           /// <summary>
           ///使用年限（月）
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? UseLife { get; set; }

           /// <summary>
           ///状态（-1：已经删除；1：启用；2：禁用）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///计量值
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public decimal? MeasuredValue { get; set; }

           /// <summary>
           ///是否需要公安监管
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? IsSecuritySupervise { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

