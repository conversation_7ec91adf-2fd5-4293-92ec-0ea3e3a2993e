﻿using Hyun.Core.Model.Model;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcApply接口方法
    ///</summary>
    public interface IDcApplyServices : IBaseServices<DcApply>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<DcApply> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<DcApply>> Find(Expression<Func<DcApply, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcApplyParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<DcApply>> GetPaged(DcApplyParam param);

        //<used>0</used>
        Task<List<ModelBrandConfigListViewModel>> GetModelConfigList(DcApplyParam param);

        //<used>0</used>
        Task<List<ModelBrandConfigListViewModel>> GetBrandConfigList(DcApplyParam param);

        //<used>0</used>
        Task<DataTable> DcApplyNumStatistics(string fileds, string pivotFileds, int unitType, int unitId, int schoolId = 0, int twoCatalogId = 0, string name = "", string model = "", int countyId = 0);

        //<used>0</used>
        Task<DataTable> DcCityApplyNumStatistics(string fileds, string pivotFileds, int unitId, int countyId = 0, int twoCatalogId = 0, string name = "", string model = "");

        //<used>0</used>
        Task<Result<string>> InsertUpdate(long id, long schoolCatalogId, long schoolMaterialModelId, long schoolMaterialBrandId, decimal num, string remark, DateTime? useTime, int optType, long withUserId, long unitId, long userId);

        //<used>0</used>
        Task<Result<string>> Apply(long id, long unitId, long userId);

        //<used>1</used>
        Task<Result<string>> DeleteConfirm(long id, long unitId, long userId);

        //<used>0</used>
        Task<Result> IsApplyMsg(string ids, long unitId, long userId);

        //<used>0</used>
        Task<Result> BatchApply(string ids, DateTime useTime, long withUserId, long unitId, long userId);

        //<used>0</used>
        Task<List<DcApply>> Delete(List<DcApply> entityCollection);

        //<used>0</used>
        Task<Result> UpdateModel(int applyId, int schoolMaterialModelId, int unitId, int userId);

        //<used>0</used>
        Task<Result<List<DcUserInfo>>> SearchAuditUser(long id, long unitId, long userId);

        //<used>0</used>
        Task<Result<string>> Revoke(string ids, long unitId, long userId);

        //<used>0</used>
        Task<DataTable> DcApplyOftenUseList(DcApplyParam param);

        //<used>0</used>
        Task<PageModel<DcApply>> DcApplyEasyList_Find(DcApplyParam param);

        //<used>0</used>
        Task<Result> EasyApply(long schoolCatalogId, long schoolMaterialModelId, long schoolMaterialBrandId, decimal num, string remark, DateTime useTime, long withUserId, long unitId, long userId, int isSendMsg);

        //<used>1</used>
        Task<Result<string>> EasyBack(long applyConfirmDetailId, decimal backNum, int isMayUse, long unitId, long userId);


        //<used>0</used>
        Task<List<DcApply>> Insert(List<DcApply> entityCollection);

        //<used>1</used>
        Task<Result> ThirdEasyApply(DcApplyDto o);

        //<used>1</used>
        Task<decimal> GetStockNum(long unitId, long schoolCatalogId, long schoolMaterialModelId, long schoolMaterialBrandId);

        //<used>1</used>
        Task<Result> BatchConfirm(string applyIds, int approvalStatuz, string approvalRemark, int processNumber, int isWithdraw, long userId, long unitId);

        //<used>1</used>
        Task<Result<string>> BatchSubmit(string applyIds, int approvalStatuz, string approvalRemark, int processNumber, int currentStatuz, int isWithdraw, long userId, long unitId);

        //<used>1</used>
        Task<Result<string>> ApplyApprovalInsertUpdate(long applyId, int processNumber, int approvalStatuz, string approvalRemark, long userId, long unitId, int isWithdraw);

        //<used>1</used>
        Task<Result> Confirm(long applyId, decimal confirmNum, int approvalStatuz, string approvalRemark, int processNumber, int isWithdraw, long userId, long unitId);

        //<used>1</used>
        Task<Result> Adjust(long applyId, long schoolMaterialId, decimal num, long userId, long unitId);
        
        //<used>1</used>
        Task<Result<string>> Collar(long confirmDetailId, string checkCode, string withCode, long withUserId, decimal num, long userId, long unitId);

        //<used>1</used>
        Task<Result> ResendCode(long id, string mobile, int messageType);

        //<used>1</used>
        Task<Result> ThirdEasyGrant(long id, int isNeedSendMessage, List<DcThirdMaterialIdGrant> list);

        Task<Result> EasyGrant(long id, decimal num, int isNeedSendMessage);

        Task<Result<string>> BackConfirm(long backId, int isMayUse, decimal backNum, string cabinetAddress, long userId, long unitId);
        /// <summary>
        /// 库存触发器。
        /// </summary>
        /// <param name="schoolMaterialId"></param>
        void AutoStockNumUpdate(long schoolMaterialId);

        #region 配货方法

        /// <summary>
        /// 领用配货数据列表
        /// </summary>
        /// <param name="param">VDcApplyAdjustParam实体参数</param>
        /// <returns></returns>
        Task<PageModel<VDcApplyAdjust>> GetAdjustPaged(VDcApplyAdjustParam param);


        #endregion

        #region 申领物品审核
        /// <summary>
        /// 危化品领用通用已审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcApplyAudited>> GetAuditedPaged(VDcApplyAuditedParam param);

        /// <summary>
        /// 领用审核列表(从审批组读取)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcApplyAuditGroupList>> GetAuditGroupPaged(VDcApplyAuditGroupListParam param);

        /// <summary>
        /// 危化品领用通用待审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcApplyAudit>> GetAuditPaged(VDcApplyAuditParam param);
        /// <summary>
        /// 领用清单打印列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcApplyConfirmDetailPrintList>> GetConfirmDetailPrintPaged(VDcApplyConfirmDetailPrintListParam param);
        /// <summary>
        /// 领用填报列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcApplyFillList>> GetFillPaged(VDcApplyFillListParam param);
        /// <summary>
        /// 已填报列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcApply>> GetApplyedPaged(VDcApplyParam param);
        #endregion

        #region 领用查询统计
        /// <summary>
        /// 已领用统计
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcApplyStatistics>> GetStatisticsPaged(VDcApplyStatisticsParam param);
        /// <summary>
        /// 领用数量统计(单位、区县)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<List<object>> ApplyNumStatistics(VDcApplyStatisticsParam param);
        /// <summary>
        /// 领用数量统计(市级)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<List<object>> CityApplyNumStatistics(VDcApplyStatisticsParam param);
        /// <summary>
        /// 按领用人统计
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<List<object>> ApplyUserNumStatistics(VDcApplyStatisticsParam param, List<PUserDto> listUser);
        /// <summary>
        /// 审批组列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcAuditGroup>> GetAuditGroupPaged(VDcAuditGroupParam param);
        /// <summary>
        /// 组员设置列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcAuditMember>> GetAuditMemberPaged(VDcAuditMemberParam param);
        #endregion
    }
}

