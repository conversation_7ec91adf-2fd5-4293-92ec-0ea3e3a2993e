namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批组
    ///</summary>
    [SugarTable("dc_AuditGroup","审批组")]
    public class DcAuditGroup : BaseEntity
    {

          public DcAuditGroup()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///审批组名称
          /// </summary>
          [SugarColumn(Length = 31)]
          public string GroupName { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

