{"Version": 1, "Hash": "J9NxA5/ZMBxt7UXcJnzeU+DFmxqO7NlqsGc6XYDnDPo=", "Source": "Hyun.Core.Api", "BasePath": "_content/Hyun.Core.Api", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Hyun.Core.Api\\wwwroot", "Source": "Hyun.Core.Api", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "Pattern": "**"}], "Assets": [{"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\CorsPost.html", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "CorsPost.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\CorsPost.html"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\css\\site.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\css\\style.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "css/style.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\style.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\班级导入信息.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/班级导入信息.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\班级导入信息.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\关于征订校服的征求家长意见书.docx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/关于征订校服的征求家长意见书.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\关于征订校服的征求家长意见书.docx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\学生导入信息.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/学生导入信息.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\学生导入信息.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\SchoolAddress.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/SchoolAddress.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\SchoolAddress.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\SchoolUserThirdAllow.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/SchoolUserThirdAllow.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\SchoolUserThirdAllow.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\ThEquipmentCategory.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/ThEquipmentCategory.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\ThEquipmentCategory.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\Unit.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/Unit.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\Unit.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UnitCompany.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/UnitCompany.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\UnitCompany.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UnitSchool.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/UnitSchool.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\UnitSchool.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\User.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/User.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\User.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UserMine.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/UserMine.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\UserMine.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UserThirdAllow.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Download/UserThirdAllow.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Download\\UserThirdAllow.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Department.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.excel/Department.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.excel\\Department.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Modules.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.excel/Modules.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.excel\\Modules.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Permission.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.excel/Permission.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.excel\\Permission.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Role.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.excel/Role.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.excel\\Role.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\RoleModulePermission.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.excel/RoleModulePermission.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.excel\\RoleModulePermission.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\SysUserInfo.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.excel/SysUserInfo.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.excel\\SysUserInfo.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\UserRole.xlsx", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.excel/UserRole.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.excel\\UserRole.xlsx"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\BlogArticle.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/BlogArticle.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\BlogArticle.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Department.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/Department.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\Department.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Modules.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/Modules.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\Modules.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Permission.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/Permission.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\Permission.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Role.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/Role.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\Role.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\RoleModulePermission.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/RoleModulePermission.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\RoleModulePermission.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\sysUserInfo.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/sysUserInfo.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\sysUserInfo.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\TasksQz.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/TasksQz.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\TasksQz.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Topic.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/Topic.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\Topic.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\TopicDetail.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/TopicDetail.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\TopicDetail.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\UserRole.tsv", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "HyunCore.Data.json/UserRole.tsv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\HyunCore.Data.json\\UserRole.tsv"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\index.html", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "index.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\JMeterTest.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "JMeterTest.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\JMeterTest.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\js\\anime.min.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "js/anime.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\anime.min.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\js\\jquery-3.3.1.min.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "js/jquery-3.3.1.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\jquery-3.3.1.min.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\js\\site.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\logo.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "logo.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\logo.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\logo\\favicon-32x32.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "logo/favicon-32x32.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\logo\\favicon-32x32.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Resource\\Export\\Excel\\预算清单.xls", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "Resource/Export/Excel/预算清单.xls", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Resource\\Export\\Excel\\预算清单.xls"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\swg-login.html", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "swg-login.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\swg-login.html"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\101001\\20241218\\2412183a79082c364e45fe8d3a94764114afe3.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/101001/20241218/2412183a79082c364e45fe8d3a94764114afe3.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\101001\\20241218\\2412183a79082c364e45fe8d3a94764114afe3.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\101001\\20241218\\24121873205bc2bf804e0da229dfc805527d8d.jpeg", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/101001/20241218/24121873205bc2bf804e0da229dfc805527d8d.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\101001\\20241218\\24121873205bc2bf804e0da229dfc805527d8d.jpeg"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\101001\\20241218\\2412189e78b12084924d4681c65e81c7b1e001.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/101001/20241218/2412189e78b12084924d4681c65e81c7b1e001.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\101001\\20241218\\2412189e78b12084924d4681c65e81c7b1e001.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\104001\\20250506\\250506254352c38b6f4d9a9c84e2d965a2ee66.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/104001/20250506/250506254352c38b6f4d9a9c84e2d965a2ee66.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\104001\\20250506\\250506254352c38b6f4d9a9c84e2d965a2ee66.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600001\\20250805\\2508051c8c56052f414adbbb7d100e162b5367.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/600001/20250805/2508051c8c56052f414adbbb7d100e162b5367.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\600001\\20250805\\2508051c8c56052f414adbbb7d100e162b5367.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600001\\20250805\\25080527e597a48e54481aa6d3ae2b4d554d9c.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/600001/20250805/25080527e597a48e54481aa6d3ae2b4d554d9c.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\600001\\20250805\\25080527e597a48e54481aa6d3ae2b4d554d9c.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600002\\20250805\\250805207f0c42701c49a281162398af8ce25c.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/600002/20250805/250805207f0c42701c49a281162398af8ce25c.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\600002\\20250805\\250805207f0c42701c49a281162398af8ce25c.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600002\\20250805\\2508052db62f35d6c749e6a07ecbb5b4865268.png", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "uploadfile/600002/20250805/2508052db62f35d6c749e6a07ecbb5b4865268.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploadfile\\600002\\20250805\\2508052db62f35d6c749e6a07ecbb5b4865268.png"}, {"Identity": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\web.config", "SourceId": "Hyun.Core.Api", "SourceType": "Discovered", "ContentRoot": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\", "BasePath": "_content/Hyun.Core.Api", "RelativePath": "web.config", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\web.config"}]}