{"version": 2, "dgSpecHash": "Ir0EIG4M50c=", "success": true, "projectFilePath": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Ocelot.Provider.Nacos\\Ocelot.Provider.Nacos.csproj", "expectedPackageFiles": ["C:\\NuGetPackages\\easycaching.core\\1.9.2\\easycaching.core.1.9.2.nupkg.sha512", "C:\\NuGetPackages\\easycaching.inmemory\\1.9.2\\easycaching.inmemory.1.9.2.nupkg.sha512", "C:\\NuGetPackages\\fluentvalidation\\11.11.0\\fluentvalidation.11.11.0.nupkg.sha512", "C:\\NuGetPackages\\google.protobuf\\3.21.2\\google.protobuf.3.21.2.nupkg.sha512", "C:\\NuGetPackages\\grpc.core\\2.46.3\\grpc.core.2.46.3.nupkg.sha512", "C:\\NuGetPackages\\grpc.core.api\\2.46.3\\grpc.core.api.2.46.3.nupkg.sha512", "C:\\NuGetPackages\\ipaddressrange\\6.2.0\\ipaddressrange.6.2.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.jsonpatch\\8.0.15\\microsoft.aspnetcore.jsonpatch.8.0.15.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.middlewareanalysis\\8.0.15\\microsoft.aspnetcore.middlewareanalysis.8.0.15.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.mvc.newtonsoftjson\\8.0.15\\microsoft.aspnetcore.mvc.newtonsoftjson.8.0.15.nupkg.sha512", "C:\\NuGetPackages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.binder\\6.0.0\\microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencymodel\\6.0.0\\microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.diagnosticadapter\\3.1.32\\microsoft.extensions.diagnosticadapter.3.1.32.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.diagnostics.abstractions\\8.0.0\\microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.hosting.abstractions\\8.0.0\\microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http\\6.0.0\\microsoft.extensions.http.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging\\6.0.0\\microsoft.extensions.logging.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options\\8.0.0\\microsoft.extensions.options.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options.configurationextensions\\6.0.0\\microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\nacos-sdk-csharp\\1.3.7\\nacos-sdk-csharp.1.3.7.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\NuGetPackages\\ocelot\\24.0.1\\ocelot.24.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.diagnosticsource\\8.0.0\\system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.json\\6.0.0\\system.text.json.6.0.0.nupkg.sha512"], "logs": []}