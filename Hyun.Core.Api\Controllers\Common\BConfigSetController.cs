﻿using Hyun.Core.Model.Model;
using Hyun.Old.Util;
using System.Transactions;

namespace Hyun.Core.Api
{

    [Route("api/hyun/bconfigset")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BConfigSetController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IVConfigSetServices vConfigSetManager;
        private readonly IBConfigSetServices configSetManager;
        private readonly IBDictionaryServices dictionaryManager;
        //private readonly IBIndexChartIsShowServices indexChartIsShowManager;
        private readonly IBModuleProcessConfigServices moduleProcessConfigManager;
        private readonly IBModuleProcessStatuzConfigServices moduleProcessStatuzConfigManager;
        private readonly IBAttachmentServices attachmentManager;
        //private readonly IMProjectNodeDefinitionServices projectNodeDefinitionManager;
        //private readonly IApiAppSecretServices apiAppSecretManager;
        private readonly IBModuleProcessDataServices moduleProcessDataManager;
        private readonly IUser user;
        private readonly IPUnitServices unitManager;
        public BConfigSetController(IMapper _mapper, IWebHostEnvironment _env, IVConfigSetServices _vConfigSetManager, IBConfigSetServices _configSetManager, IBDictionaryServices _dictionaryManager, /*IBIndexChartIsShowServices _indexChartIsShowManager,*/ IBModuleProcessConfigServices _moduleProcessConfigManager, IBModuleProcessStatuzConfigServices _moduleProcessStatuzConfigManager, IBAttachmentServices _attachmentManager, /*IMProjectNodeDefinitionServices _projectNodeDefinitionManager,*/ /*IApiAppSecretServices _apiAppSecretManager,*/ IBModuleProcessDataServices _moduleProcessDataManager, IUser _user, IPUnitServices _unitManager)
        {
            mapper = _mapper;
            env = _env;
            vConfigSetManager = _vConfigSetManager;
            configSetManager = _configSetManager;
            dictionaryManager = _dictionaryManager;
            //indexChartIsShowManager = _indexChartIsShowManager;
            moduleProcessConfigManager = _moduleProcessConfigManager;
            moduleProcessStatuzConfigManager = _moduleProcessStatuzConfigManager;
            attachmentManager = _attachmentManager;
            //projectNodeDefinitionManager = _projectNodeDefinitionManager;
            //apiAppSecretManager = _apiAppSecretManager;
            moduleProcessDataManager = _moduleProcessDataManager;
            user = _user;
            unitManager = _unitManager;
        }


        /// <summary>
        /// 获取配置信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("bconfigsetfind")]
        //<used>1</used>
        public async Task<Result> BConfigSet_Find([FromBody] VConfigSetParam param)
        {
            Result r = new Result();
            PageModel<VConfigSet> pg = await vConfigSetManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 根据Id获取配置信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("bconfigsetgetbyid")]
        //<used>1</used>
        public async Task<Result> BConfigSet_GetById(long id)
        {
            Result r = new Result();
            var model =await configSetManager.GetById(id);
            if (model != null)
            {
                r.flag = 1;
                r.msg = "查询成功。";
                r.data.rows = mapper.Map<BConfigSetDto>(model);
            }
            return r;
        }

        /// <summary>
        /// 新增修改参数配置信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("bconfigsetinsertupdate")]
        //<used>1</used>
        public async Task<Result> BConfigSet_InsertUpdate([FromBody] BConfigSetDto o)
        {
            Result r = new Result();
            if (!string.IsNullOrEmpty(o.Remark) && o.Remark.Length > 255)
            {
                r.flag = 0;
                r.msg = "说明不能大于255个字符";
                return r;
            }
            var module = await dictionaryManager.Find(f => f.TypeCode == "9001" && f.DicValue == o.ModuleCode);
            if (module == null)
            {
                r.flag = 0;
                r.msg = "您所选择的模块不存在。";
                return r;
            }
            o.ModuleName = module[0].DicName;
            var list = await configSetManager.Find(f => f.Id != o.Id && f.ModuleCode == o.ModuleCode && f.TypeCode == o.TypeCode && (f.ConfigType == 0 || f.ConfigType == 1) && f.UnitId == o.UnitId);
            if (o.ConfigType == 0)
            {
                list = list.Where(f => f.ConfigType == 0).ToList();
            }
            else
            {
                list = list.Where(f => f.ConfigType == 1).ToList();
            }
            
            if (list.Count > 0)
            {
                r.flag = 0;
                r.msg = "该模块中已存在该类型的配置，请勿重复配置。";
                return r;
            }
            if (o.Id > 0)
            {
                var model =await configSetManager.GetById(o.Id);
                if (model == null)
                {
                    r.flag = 0;
                    r.msg = "找不到您需要修改的数据。";
                }
                else
                {
                    model.ModuleName = o.ModuleName;
                    model.ModuleCode = o.ModuleCode;
                    model.TypeName = o.TypeName;
                    model.TypeCode = o.TypeCode;
                    model.ConfigType = o.ConfigType;
                    model.UnitType = o.UnitType;
                    model.ConfigValue = o.ConfigValue;
                    model.UnitId = o.ConfigType == 0 ? o.UnitId : model.UnitId;
                    model.UserId = user.ID;
                    model.RegDate = DateTime.Now;
                    model.ValueType = o.ValueType;
                    model.ComboValues = o.ComboValues;
                    model.Remark = o.Remark;
                    await configSetManager.Update(model);
                    o.UnitId = model.UnitId;
                    r.flag = 1;
                    r.msg = "修改成功。";
                }
            }
            else
            {
                BConfigSet configSet = mapper.Map<BConfigSet>(o);
                configSet.Statuz = 1;
                await configSetManager.Add(configSet);
                r.flag = 1;
                r.msg = "添加成功。";
            }

            if (o.PushType == 1)
            {
                await configSetManager.PushData(o.Id, o.UnitId, o.TypeCode);
            }
            return r;
        }


        [HttpPost]
        [Route("bconfigsetgetbyunit")]
        //<used>1</used>
        public async Task<Result> BConfigSet_GetByUnit(string moduleCode, int optType = 0)
        {
            Result r = new Result();
            var list = await  configSetManager.GetByModule(moduleCode, user.UnitTypeId, user.UnitId, user.UserId, optType);
            r.data.rows= mapper.Map<List<BConfigSetDto>>(list);
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }


        [HttpPost]
        [Route("bconfigsetsavebyunit")]
        //<used>1</used>
        public async Task<Result> BConfigSet_SaveByUnit([FromBody] BConfigSetModel model)
        {
            Result r = new Result();
            int errorNum = 0;
            if (model.List != null && model.List.Count > 0)
            {
                foreach (var item in model.List)
                {
                    r = await configSetManager.InsertUpdate(model.ModuleCode, item.ConfigSetId, item.ConfigValue, user.UnitTypeId, user.UnitId, user.UserId);
                    if (r.flag == 0)
                    {
                        errorNum += 1;
                    }
                }
                r.flag = 1;
                r.msg = "保存成功。";
                if (errorNum > 0)
                {
                    r.flag = 1;
                    r.msg = "配置参数异常。";
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "没有要更新的配置参数。";
            }
            return r;
        }

        /// <summary>
        /// 根据TypeCode获取配置信息
        /// </summary>
        /// <param name="moduleCode"></param>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("bconfigsetget")]
        //<used>1</used>
        public async Task<Result> BConfigSet_Get(string moduleCode, string typeCode)
        {
            Result r = new Result();
            BConfigSetParam param = new BConfigSetParam();
            param.UnitTypeId = user.UnitTypeId;//
            param.ModuleCode = moduleCode;
            param.TypeCode = typeCode;
            param.Statuz = 1;
            var list = await configSetManager.Find(param);

            if (list != null && list.Count > 0)
            {
                var listTemp = list.Where(m => m.UnitId == user.UnitId);
                if (!(listTemp != null && listTemp.Count() > 0))
                {
                    var entityUnit = await unitManager.QueryById(user.UnitId);
                    if (entityUnit != null)
                    {
                        listTemp = list.Where(m => m.UnitId == entityUnit.PId);
                    }
                }
                if (!(listTemp != null && listTemp.Count() > 0))
                {
                    listTemp = list.Where(m => m.UnitId == 0);
                }
                if (listTemp != null && listTemp.Count() > 0)
                {
                    r.data.rows = listTemp.FirstOrDefault().ConfigValue;
                }
            }
            r.flag = 1;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 根据TypeCode获取上级单位配置信息
        /// </summary>
        /// <param name="moduleCode"></param>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("bconfigsetgetpunit")]
        //<used>1</used>
        public async Task<Result> BConfigSet_GetPUnit(string moduleCode, string typeCode)
        {
            Result r = new Result();
            var param = new BConfigSetParam();
            param.Statuz = 1;
            param.ModuleCode = moduleCode;
            param.TypeCode = typeCode;
            long unitPid = 0;
            var entityUnit = await unitManager.QueryById(user.UnitId);
            if (entityUnit != null)
            {
                unitPid = entityUnit.PId;
            }
            var list = await configSetManager.Find(param);
            if (list.Count > 0)
            {
                //查找上级单位自定义配置
                var unitConfig = list.Find(f => f.ConfigType == 1 && f.UnitId == unitPid);
                if (unitConfig != null)
                {
                    r.data.rows = unitConfig.ConfigValue;
                }
                else
                {
                    //单位未配置时，读取系统默认配置
                    var systemConfig = list.Find(f => f.ConfigType == 0);
                    if (systemConfig != null)
                    {
                        r.data.rows = systemConfig.ConfigValue;
                    }
                }
                r.flag = 1;
                r.msg = "查询成功。";
            }
            return r;
        }

        /// <summary>
        /// 获取首页待处理列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessconfigfind")]
        //<used>1</used>
        public async Task<Result> ModuleProcessConfig_Find([FromBody] BModuleProcessConfigParam param)
        
        {
            Result r = new Result();
            PageModel<BModuleProcessConfig> pg = await moduleProcessConfigManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<BModuleProcessConfigDto>>(pg.data);
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 新增修改首页待处理事项
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessconfigsave")]
        //<used>1</used>
        public async Task<Result> ModuleProcessConfig_Save([FromBody] BModuleProcessConfig o)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }

            if (!string.IsNullOrEmpty(o.GroupName) && o.GroupName.Length > 63)
            {
                r.flag = 0;
                r.msg = "流程组合名称不能大于63个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.Name) && o.Name.Length > 63)
            {
                r.flag = 0;
                r.msg = "流程名称不能大于63个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.ShowName) && o.ShowName.Length > 63)
            {
                r.flag = 0;
                r.msg = "流程显示名称不能大于63个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.ModuleCode) && o.ModuleCode.Length > 255)
            {
                r.flag = 0;
                r.msg = "模块编号不能大于255个字符";
                return r;
            }

            if (o.Id > 0)
            {
                BModuleProcessConfig processDefin =await moduleProcessConfigManager.GetById(o.Id);
                if (processDefin != null)
                {
                    List<BModuleProcessConfig> list = await moduleProcessConfigManager.Find(f => f.Id != o.Id && f.ModuleCode == o.ModuleCode);
                    //先判断是否存在开始节点,或者名字有相同。
                    if (list != null && list.Count > 0)
                    {
                        r.msg = "节点名称已经存在！";
                        return r;
                    }
                    if (await moduleProcessConfigManager.Update(o))
                    {
                        r.flag = 1;
                        r.msg = "修改成功";
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "修改失败";
                    }
                }
            }
            else
            {
                //判断ModuleCode是否已经存在
                List<BModuleProcessConfig> list = await moduleProcessConfigManager.Find(f => f.ModuleCode == o.ModuleCode);
                if (list != null && list.Count > 0)
                {
                    r.msg = "流程模块编号已经存在！";
                    return r;
                }
                o.Statuz = 1;
                if (await moduleProcessConfigManager.Add(o) > 0)
                {
                    r.flag = 1;
                    r.msg = "保存成功";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "保存失败";
                }
            }
            return r;
        }


        /// <summary>
        /// 获取下一个模块
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessconfignext")]
        //<used>1</used>
        public async Task<Result> ModuleProcessConfigNext(long Id)
        {
            Result r = new Result();

            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            List<BModuleProcessConfig> list = new List<BModuleProcessConfig>();
            if (Id > 0)
            {
                list = await moduleProcessConfigManager.Find(f => f.Id != Id);
            }
            list = await moduleProcessConfigManager.Find(null);
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = mapper.Map<List<BModuleProcessConfigDto>>(list);
            return r;
        }

        /// <summary>
        /// 根据Id删除模块流程配置信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessconfigdeletebyid")]
        //<used>1</used>
        public async Task<Result> ModuleProcessConfig_DeleteById(long Id)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            var obj =await moduleProcessConfigManager.GetById(Id);
            if (obj != null)
            {
                using (TransactionScope transaction = new TransactionScope())
                {
                    try
                    {
                        List<BModuleProcessStatuzConfig> list = await moduleProcessStatuzConfigManager.Find(f => f.ModuleProcessConfigId == Id);
                        if (list.Count > 0)
                        {
                            var listId = list.Select(f => f.Id.ToString()).ToList().ToArray();
                            await moduleProcessStatuzConfigManager.DeleteByIds(listId);
                        }

                        await moduleProcessConfigManager.DeleteById(obj.Id);
                        r.flag = 1;
                        r.msg = "删除成功";

                        transaction.Complete();
                    }
                    catch
                    {
                        r.flag = 0;
                        r.msg = "程序异常，删除模块流程配置失败。";
                    }
                }
            }
            return r;
        }

        /// <summary>
        /// 模块流程配置设置状态
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessconfigsetstatuz")]
        //<used>1</used>
        public async Task<Result> ModuleProcessConfig_SetStatuz(long Id)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            var obj =await moduleProcessConfigManager.GetById(Id);
            if (obj != null)
            {
                if (obj.Statuz == 1)
                {
                    obj.Statuz = 2;
                }
                else
                {
                    obj.Statuz = 1;
                }
                if (await moduleProcessConfigManager.Update(obj))
                {
                    r.flag = 1;
                    r.msg = "设置成功";
                }
            }
            return r;
        }

        /// <summary>
        /// 根据Id获取模块流程配置信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessconfiggetbyid")]
        //<used>1</used>
        public async Task<Result> ModuleProcessConfig_GetById(long Id)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            BModuleProcessConfig obj =await moduleProcessConfigManager.GetById(Id);
            if (obj != null)
            {
                r.flag = 1;
                r.msg = "";
                r.data.rows = mapper.Map<BModuleProcessConfigDto>(obj);
            }
            return r;
        }


        /// <summary>
        /// 获取模块流程各状态值列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessstatuzconfigfind")]
        //<used>1</used>
        public async Task<Result> ModuleProcessStatuzConfig_Find([FromBody] BModuleProcessStatuzConfigParam param)
        {
            Result r = new Result();
            PageModel<BModuleProcessStatuzConfig> pg = await moduleProcessStatuzConfigManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<BModuleProcessStatuzConfigDto>>(pg.data);
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 保存模块流程状态值信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessstatuzconfigsave")]
        //<used>1</used>
        public async Task<Result> ModuleProcessStatuzConfig_Save([FromBody] BModuleProcessStatuzConfigDto o)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            if (!string.IsNullOrEmpty(o.StatuzDesc) && o.StatuzDesc.Length > 31)
            {
                r.flag = 0;
                r.msg = "状态描述不能大于31个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.DetailUrl1) && o.DetailUrl1.Length > 255)
            {
                r.flag = 0;
                r.msg = "市级详情链接地址不能大于255个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.ListUrl1) && o.ListUrl1.Length > 255)
            {
                r.flag = 0;
                r.msg = "市级列表链接地址不能大于255个字符";
                return r;
            }

            if (!string.IsNullOrEmpty(o.DetailUrl2) && o.DetailUrl2.Length > 255)
            {
                r.flag = 0;
                r.msg = "区县详情链接地址不能大于255个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.ListUrl2) && o.ListUrl2.Length > 255)
            {
                r.flag = 0;
                r.msg = "区县列表链接地址不能大于255个字符";
                return r;
            }

            if (!string.IsNullOrEmpty(o.DetailUrl3) && o.DetailUrl3.Length > 255)
            {
                r.flag = 0;
                r.msg = "单位详情链接地址不能大于255个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.ListUrl3) && o.ListUrl3.Length > 255)
            {
                r.flag = 0;
                r.msg = "单位列表链接地址不能大于255个字符";
                return r;
            }

            if (!string.IsNullOrEmpty(o.DetailUrl4) && o.DetailUrl4.Length > 255)
            {
                r.flag = 0;
                r.msg = "企业详情链接地址不能大于255个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.ListUrl4) && o.ListUrl4.Length > 255)
            {
                r.flag = 0;
                r.msg = "企业列表链接地址不能大于255个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(o.Remark) && o.Remark.Length > 1024)
            {
                r.flag = 0;
                r.msg = "备注不能大于1024个字符";
                return r;
            }

            BModuleProcessStatuzConfig dto = mapper.Map<BModuleProcessStatuzConfig>(o);
            if (o.Id > 0)
            {
                BModuleProcessStatuzConfig processDefin =await moduleProcessStatuzConfigManager.GetById(o.Id);
                if (processDefin != null)
                {
                    if (await moduleProcessStatuzConfigManager.Update(o))
                    {
                        r.flag = 1;
                        r.msg = "修改成功";
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "修改失败";
                    }
                }
            }
            else
            {
                if (await moduleProcessStatuzConfigManager.Add(dto) > 0)
                {
                    r.flag = 1;
                    r.msg = "保存成功";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "保存失败";
                }
            }
            return r;
        }


        /// <summary>
        /// 根据Id删除模块状态值配置信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessstatuzconfigdeletebyid")]
        //<used>1</used>
        public async Task<Result> ModuleProcessStatuzConfig_DeleteById(long Id)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            if (await moduleProcessStatuzConfigManager.DeleteById(Id))
            {
                r.flag = 1;
                r.msg = "删除成功";
            }
            return r;
        }


        /// <summary>
        /// 批量删除模块状态值信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>

        [HttpPost]
        [Route("moduleprocessstatuzconfigbatchdelete")]
        //<used>1</used>
        public async Task<Result> ModuleProcessStatuzConfig_BatchDelete(string ids)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            if (string.IsNullOrEmpty(ids))
            {
                r.flag = 0;
                r.msg = "请至少选择一项删除";
                return r;
            }

            object[] listId = ids.Split(',').Cast<object>().ToArray();
            await moduleProcessStatuzConfigManager.DeleteByIds(listId);
            r.flag = 1;
            r.msg = "批量删除成功";
            return r;
        }


        /// <summary>
        /// 根据Id获取模块状态值信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessstatuzconfiggetbyid")]
        //<used>1</used>
        public async Task<Result> ModuleProcessStatuzConfig_GetById(long Id)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            BModuleProcessStatuzConfig b =await moduleProcessStatuzConfigManager.GetById(Id);
            if (b != null)
            {
                r.flag = 1;
                r.msg = "";
                r.data.rows = mapper.Map<BModuleProcessStatuzConfigDto>(b);
            }
            return r;
        }

        /// <summary>
        /// 根据Id设置状态
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessstatuzconfigsetstatuz")]
        //<used>1</used>
        public async Task<Result> ModuleProcessStatuzConfig_SetStatuz(long Id)
        {
            Result r = new Result();
            if (!user.IsSystemUser)
            {
                r.flag = 0;
                r.msg = "非法操作";
                return r;
            }
            var obj =await moduleProcessStatuzConfigManager.GetById(Id);
            if (obj != null)
            {
                if (obj.SetStatuz == 1)
                {
                    obj.SetStatuz = 2;
                }
                else
                {
                    obj.SetStatuz = 1;
                }
                if (await moduleProcessStatuzConfigManager.Update(obj))
                {
                    r.flag = 1;
                    r.msg = "设置成功";
                }
            }
            return r;
        }


        ///// <summary>
        ///// 获取密钥列表信息
        ///// </summary>
        ///// <param name="param"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("apiappsecretfind")]
        ////<used>1</used>
        //public async Task<Result> ApiAppSecret_Find([FromBody] ApiAppSecretParam param)
        //{
        //    Result r = new Result();
        //    PageModel<ApiAppSecret> pg = await apiAppSecretManager.GetPaged(param);
        //    r.data.total = pg.dataCount;
        //    r.flag = 1;
        //    r.data.rows = mapper.Map<List<ApiAppSecretDto>>(pg.data);
        //    r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
        //    return r;
        //}

        ///// <summary>
        ///// 根据Id查询密钥信息
        ///// </summary>
        ///// <param name="id"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("apiappsecretgetbyid")]
        ////<used>1</used>
        //public async Task<Result> ApiAppSecret_GetById(long id)
        //{
        //    Result r = new Result();
        //    var entity =await apiAppSecretManager.QueryById(id);
        //    if (entity != null)
        //    {
        //        r.flag = 1;
        //        r.msg = "查询成功。";
        //        r.data.rows = entity;
        //    }
        //    else
        //    {
        //        r.flag = 0;
        //        r.msg = "查询失败，数据不存在。";
        //    }
        //    return r;
        //}

        ///// <summary>
        ///// 保存密钥信息
        ///// </summary>
        ///// <param name="obj"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("apiappsecretsave")]
        ////<used>1</used>
        //public async Task<Result> ApiAppSecret_Save([FromBody] ApiAppSecretDto obj)
        //{
        //    Result r = new Result();
        //    if (user.IsSystemUser)
        //    {
        //        ApiAppSecret o = mapper.Map<ApiAppSecret>(obj);
        //        var entity =await apiAppSecretManager.QueryById(o.Id);
        //        if (entity != null)
        //        {
        //            if (entity.KeyType == 1)
        //            {
        //                entity.UpdateTime = DateTime.Now;
        //                entity.UserId = user.ID;
        //                entity.Name = o.Name;
        //                entity.Url = o.Url;
        //                if (await apiAppSecretManager.Update(entity))
        //                {
        //                    r.flag = 1;
        //                    r.msg = "保存成功。<br />";
        //                }
        //                else
        //                {
        //                    r.flag = 0;
        //                    r.msg = "保存失败。<br />";
        //                }
        //            }
        //            else if (entity.KeyType == 2)
        //            {
        //                entity.UpdateTime = DateTime.Now;
        //                entity.UserId = user.ID;
        //                entity.Name = o.Name;
        //                entity.Url = o.Url;
        //                entity.AppId = o.AppId;
        //                if (string.IsNullOrEmpty(o.AppSecret))
        //                {
        //                    entity.AppSecret = entity.AppSecret;
        //                }
        //                else
        //                {
        //                    entity.AppSecret = o.AppSecret;
        //                }
        //                if (await apiAppSecretManager.Update(entity))
        //                {
        //                    r.flag = 1;
        //                    r.msg = "保存成功。<br />";
        //                }
        //                else
        //                {
        //                    r.flag = 0;
        //                    r.msg = "保存失败。<br />";
        //                }
        //            }
        //        }
        //        else
        //        {
        //            r.flag = 0;
        //            r.msg = "当前配置信息不存在。";
        //        }
        //    }
        //    else
        //    {
        //        r.flag = 0;
        //        r.msg = "你无权操作。";
        //    }
        //    return r;
        //}

        ///// <summary>
        ///// 更新密钥信息
        ///// </summary>
        ///// <param name="o"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("apiappsecretcreateappid")]
        ////<used>1</used>
        //public async Task<Result> ApiAppSecret_CreateAppId([FromBody] ApiAppSecretModel o)
        //{
        //    Result r = new Result();
        //    if (user.IsSystemUser)
        //    {
        //        var entity =await apiAppSecretManager.QueryById(o.id);
        //        if (entity != null)
        //        {
        //            string strRandom = Random(o.length, o.isHas);
        //            string strMd5 = MD5Helper.MD5Encrypt32(strRandom);
        //            strMd5 = strMd5.Substring(0, 16);

        //            entity.AppId = strMd5;
        //            if (await apiAppSecretManager.Update(entity))
        //            {
        //                r.data.rows = strMd5;
        //                r.flag = 1;
        //                r.msg = "执行成功。";
        //            }
        //            else
        //            {
        //                r.flag = 0;
        //                r.msg = "执行失败。";
        //            }
        //        }
        //        else
        //        {
        //            r.flag = 0;
        //            r.msg = "该配置已不存在。";
        //        }
        //    }
        //    else
        //    {
        //        r.flag = 0;
        //        r.msg = "你无权操作。";
        //    }
        //    return r;
        //}

        ///// <summary>
        ///// 添加密钥
        ///// </summary>
        ///// <param name="o"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("apiappsecretcreateappsecret")]
        ////<used>1</used>
        //public async Task<Result> ApiAppSecret_CreateAppSecret([FromBody] ApiAppSecretModel o)
        //{
        //    Result r = new Result();
        //    if (user.IsSystemUser)
        //    {
        //        ApiAppSecret entity =await apiAppSecretManager.QueryById(o.id);
        //        if (entity != null)
        //        {
        //            string strRandom = Random(o.length, o.isHas);
        //            string strBase64 = ComLib.Base64Encode(strRandom);
        //            entity.AppSecret = strBase64;
        //            if (await apiAppSecretManager.Update(entity))
        //            {
        //                r.data.rows = mapper.Map<ApiAppSecretDto>(entity);
        //                r.flag = 1;
        //                r.msg = "执行成功。";
        //            }
        //            else
        //            {
        //                r.flag = 0;
        //                r.msg = "执行失败。";
        //            }
        //        }
        //        else
        //        {
        //            r.flag = 0;
        //            r.msg = "该配置已不存在。";
        //        }
        //    }
        //    else
        //    {
        //        r.flag = 0;
        //        r.msg = "你无权操作。";
        //    }
        //    return r;
        //}

        /// <summary>
        /// 审核审批配置：获取流程模块列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("moduleprocessdatafind")]
        //<used>1</used>
        public async Task<Result> ModuleProcessData_Find()
        {
            Result r = new Result();
            if (user != null)
            {
                List<BModuleProcessDataDto> listData = await moduleProcessDataManager.SearchProcessDataList(user.UnitTypeId, user.UnitId, user.UserRoleIds);
                List<BModuleProcessDataDto> modulelistdata = listData.Where(item => item.RoleIdz.Split(',').ToList().Intersect(user.UserRoleIds.Split(',').ToList()).Any()).ToList();

                foreach (BModuleProcessDataDto p in modulelistdata)
                {
                    switch (user.UnitTypeId)
                    {
                        case 1:
                            p.ListUrl = p.ListUrl1;
                            break;
                        case 2:
                            p.ListUrl = p.ListUrl2;
                            break;
                        case 3:
                            p.ListUrl = p.ListUrl3;
                            break;
                        case 4:
                            p.ListUrl = p.ListUrl4;
                            break;
                    }
                }
                r.flag = 1;
                r.msg = "查询成功";
                r.data.total = modulelistdata.Count;
                r.data.rows = modulelistdata;
                r.data.headers = modulelistdata.Count;
            }
            else
            {
                r.flag = 0;
                r.msg = "用户尚未登录";
                r.data = null;
            }
            return r;
        }

        /// <summary>
        /// 生成随机码
        /// </summary>
        /// <param name="length"></param>
        /// <param name="isHas"></param>
        /// <returns></returns>
        private string Random(int length, bool isHas)
        {
            string allowed = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
            if (isHas)
            {
                allowed += "!@#$?%&*-+=<{[>]}";
            }
            return new string(allowed
                .OrderBy(o => Guid.NewGuid())
                .Take(length)
                .ToArray());
        }
    }
}
