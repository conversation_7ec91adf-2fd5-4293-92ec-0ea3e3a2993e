﻿using Org.BouncyCastle.Asn1.Mozilla;

namespace Hyun.Core.Model
{

    ///<summary>
    ///101单位p_Unit
    ///</summary>
    public class PUnitParam : BaseSearch
    {

        public PUnitParam()
        {

        }

        /// <summary>
        ///Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        ///Name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        ///Ids
        /// </summary>
        public string Ids { get; set; }

        /// <summary>
        /// 单位类型
        /// </summary>
        public int UnitType { get; set; } = -10000;

        public int statuzgt { get; set; } = -10000;

        /// <summary>
        /// 是否为市级或区县（默认false）
        /// </summary>
        public bool UnitCityOrCounty { get; set; } = false;
        /// <summary>
        /// Id集合
        /// </summary>
        public List<long> IdList { get; set; }
        /// <summary>
        /// Pid
        /// </summary>
        public long Pid { get; set; }

        /// <summary>
        /// 是否为市级或区县或单位（默认false）
        /// </summary>
        public bool UnitCityOrCountyOrSchool { get; set; } = false;

        /// <summary>
        /// 是否为市级或区县或单位或企业
        /// </summary>
        public bool UnitCityOrCountyOrSchoolOrCompany { get; set; } = false;
        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactUser { get; set; }
        /// <summary>
        /// 所在区域
        /// </summary>
        public string AreaName { get; set; }
    }

    ///// <summary>
    ///// 校服平台单位查询参数
    ///// </summary>
    //public class UnitXfParam: BaseSearch
    //{
    //    /// <summary>
    //    /// 单位类型
    //    /// </summary>
    //    public int UnitType { get; set; } = -10000;

    //    /// <summary>
    //    /// 单位Id
    //    /// </summary>
    //    public long UnitId { get; set; } = -10000;

    //    /// <summary>
    //    /// 关键字
    //    /// </summary>
    //    public string Key { get; set; }
    //}
}

