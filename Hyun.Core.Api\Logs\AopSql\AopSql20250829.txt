
Date：2025-08-29 15:26:20.239
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:25.688
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:31.552
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:31.624
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:31.750
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:admin [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:32.784
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:0 [Type]:Int64    
[Name]:@AccountName [Value]:admin [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:5 [Type]:Int32    
[Name]:@Statuz [Value]:0 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/29 15:26:32 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 15:26:31 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.298
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.312
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.340
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.381
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:26:33 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.382
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.399
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:26:33 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.409
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:26:33 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:33.414
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:26:33 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.433
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.466
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.476
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.494
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.520
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.555
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.558
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.584
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.594
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.600
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.612
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.641
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.812
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.812
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.829
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:56.830
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:57.366
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/29 15:26:56 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 15:26:56 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:57.366
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/29 15:26:56 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 15:26:56 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:57.658
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:57.749
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:57.791
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:5 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:218 [Type]:Int32    
[Name]:@LoginCount [Value]:1862 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 15:26:57 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:26:57.897
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:01.583
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:01.713
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (30,350,354,351,352,353,370,3200,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:02.438
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000010,100000000001001,100000000001002,100000000001003,100000000001004,100000000000020,100000000002001,100000000002002,100000000002017,100000000002008,100000000002009,100000000002010,100000000003001,100000000000030,100000000000040,100000000004001,100000000004003,100000000004004,100000000000050,100000000005001,100000000000060,100000000006001,100000000006002,100000000006003,100000000006004,100000000000070,100000000007004,100000000007005,100000000007006,100000000007007,100000000007017,100000000007018,100000000007019,100000000002011,100000000002012,100000000002013,100000000002014,100000000002015,100000000007001,100000000004002,100000000007002,100000000007014,100000000007020,100000000007021,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,815152703427579904,815152216284336128,814917889516638208,827558788750905344,827558788880928768,827558790864834560,840599930178375680,840600506052120576,842694325652426752,827558797206622208,815512156001800192,815512500404490240,815512620718100480,815512817057665024,844961710316982272,844957453845008384,827558795155607552,844957620178522112,844966541932892160,844966811886686208,844972844101144576,844972973948407808,844973424735424512,844973591006023680,844973895587991552,844974175394205696,844974402880671744,844974534216912896,844974696779747328,844974828485087232,844975023654440960,844975297731235840,844975449124638720,844975635729223680,844975797407059968,844975728242987008,844976309716127744,844976490087976960,844976660540297216,844976914006282240,844977126699438080,844977251022802944,844977462650605568,844977581781422080,844977790431268864,844978207466721280,844978326723366912,844977922153385984,844978006509228032,844978402241810432,844978933500743680,844979146500083712,844979303874564096,845255096391438336,844979454731096064,844979648616992768,844979828049317888,844980287262691328,844980359450857472,844980598295498752,844982125559025664,844981921363529728,844981743516651520,844981082699862016,844981392927363072,844980852977831936,844983416607739904,844983343421329408,844982957755076608,844982807137619968,844982595073609728,844982459761168384,844982262398193664,844983686142103552,844983827448205312,844983906800242688,844984091039240192,844984272614854656,844984515939012608,844984636638498816,844984773846765568,844984942273236992,844985090730627072,844985239972352000,844985309048344576,844985864302891008,844986081353928704,844986204750352384,844986319925940224,844986470987993088,844988224278368256,844988795462881280,844988925083652096,844989069602590720,844992020215762944,844992477537505280,844992359836946432,844992873186201600,844992931449278464,844993347960442880,844993496422027264,844997017473126400,844997280166580224,844997405890842624,844997569112182784,844997754236178432,844997884142161920,844997950672211968,844998050886717440,844998119732023296,852931579138281472,855107575388049408,855107866284003328,870714919371149312,871030740077252608,872554852792668160,875322682860638208,853564351586504704,852932447195631616)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:02.629
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:02.891
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst9) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst10) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst11) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst12) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst13) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst14) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst16  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted8 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted8 )   WHERE ((NOT (1=2)  AND( [PAU].[IsDeleted] = @IsDeleted6 )) AND ( [PAU].[AuditUserId] = @AuditUserId7 ))  AND ( [PAU].[IsDeleted] = @IsDeleted8 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId7 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@MethodConst9 [Value]: [Type]:String    
[Name]:@MethodConst10 [Value]:0 [Type]:Int32    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst14 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.106
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[ProcessNodeId],[Statuz],[StatuzDesc],[WaitOrBack],[IsEnable],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessStatuz]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.225
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.296
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.336
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.360
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.397
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679134371973 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.423
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:702534294601861 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.448
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:705091520331909 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.474
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:703912462606469 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.498
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:674228238864517 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.523
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:675249294651525 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.546
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.576
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.612
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [GPS].[ProcessId],[GPS].[GroupName],[GPS].[TypeCode],[GPS].[TypeBox],[GPS].[IsDeleted],[GPS].[CreateId],[GPS].[CreateBy],[GPS].[CreateTime],[GPS].[ModifyId],[GPS].[ModifyBy],[GPS].[ModifyTime],[GPS].[Version],[GPS].[Id] FROM [wf_GroupProcessSet] [GPS] Inner JOIN [wf_Process] [P] ON ( [GPS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted3 )   WHERE ((( [GPS].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [P].[Statuz] = @Statuz2 ))  AND ( [GPS].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:03.626
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[PSort],[IsOpenControl],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [UseUnitId] = @UseUnitId1 )) AND ( [Usage] = @Usage2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsOpenControl] = @IsOpenControl4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UseUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@Usage2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsOpenControl4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.487
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.524
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.579
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.656
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.685
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.696
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.721
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:04.740
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:05.765
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:27:05.774
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:25.906
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:25.961
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:25.989
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:30.117
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:30.136
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:33.885
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:33.895
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:36.506
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:36.508
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:36.545
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:36.551
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:36.560
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:36.563
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:39.265
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:39.273
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:43.274
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:43.282
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:43.288
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:43.295
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:45.758
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:45.767
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:48.037
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:48.058
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:48.066
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:50.138
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:50.150
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:54.588
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:31:54.599
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:54:56.600
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:54:59.352
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:09.178
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE ((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:09.943
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:09.994
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:55:09 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:10.261
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:55:09 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:11.911
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:12.003
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:12.052
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:12.612
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:12.680
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:12.692
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:12.748
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:12.779
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:13.404
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:13.412
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:20.270
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:20.284
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:22.039
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:22.054
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:55:22.063
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:15.922
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:15.989
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:16.028
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:16.031
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:58:16 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:16.051
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:58:16 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:19.095
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:19.115
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:19.181
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:19.200
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:58:19 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:19.207
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 15:58:19 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.303
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.339
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.352
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.365
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.385
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.412
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:713882132443269 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.429
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.441
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.455
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/29 15:58:32 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 15:58:32 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:749176146951675909 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.462
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.499
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.537
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:5 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:218 [Type]:Int32    
[Name]:@LoginCount [Value]:1866 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 15:58:32 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:32.565
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:36.468
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:36.676
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (30,350,354,351,352,353,370,3200,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:38.196
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000010,100000000001001,100000000001002,100000000001003,100000000001004,100000000000020,100000000002001,100000000002002,100000000002017,100000000002008,100000000002009,100000000002010,100000000003001,100000000000030,100000000000040,100000000004001,100000000004003,100000000004004,100000000000050,100000000005001,100000000000060,100000000006001,100000000006002,100000000006003,100000000006004,100000000000070,100000000007004,100000000007005,100000000007006,100000000007007,100000000007017,100000000007018,100000000007019,100000000002011,100000000002012,100000000002013,100000000002014,100000000002015,100000000007001,100000000004002,100000000007002,100000000007014,100000000007020,100000000007021,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,815152703427579904,815152216284336128,814917889516638208,827558788750905344,827558788880928768,827558790864834560,840599930178375680,840600506052120576,842694325652426752,827558797206622208,815512156001800192,815512500404490240,815512620718100480,815512817057665024,844961710316982272,844957453845008384,827558795155607552,844957620178522112,844966541932892160,844966811886686208,844972844101144576,844972973948407808,844973424735424512,844973591006023680,844973895587991552,844974175394205696,844974402880671744,844974534216912896,844974696779747328,844974828485087232,844975023654440960,844975297731235840,844975449124638720,844975635729223680,844975797407059968,844975728242987008,844976309716127744,844976490087976960,844976660540297216,844976914006282240,844977126699438080,844977251022802944,844977462650605568,844977581781422080,844977790431268864,844978207466721280,844978326723366912,844977922153385984,844978006509228032,844978402241810432,844978933500743680,844979146500083712,844979303874564096,845255096391438336,844979454731096064,844979648616992768,844979828049317888,844980287262691328,844980359450857472,844980598295498752,844982125559025664,844981921363529728,844981743516651520,844981082699862016,844981392927363072,844980852977831936,844983416607739904,844983343421329408,844982957755076608,844982807137619968,844982595073609728,844982459761168384,844982262398193664,844983686142103552,844983827448205312,844983906800242688,844984091039240192,844984272614854656,844984515939012608,844984636638498816,844984773846765568,844984942273236992,844985090730627072,844985239972352000,844985309048344576,844985864302891008,844986081353928704,844986204750352384,844986319925940224,844986470987993088,844988224278368256,844988795462881280,844988925083652096,844989069602590720,844992020215762944,844992477537505280,844992359836946432,844992873186201600,844992931449278464,844993347960442880,844993496422027264,844997017473126400,844997280166580224,844997405890842624,844997569112182784,844997754236178432,844997884142161920,844997950672211968,844998050886717440,844998119732023296,852931579138281472,855107575388049408,855107866284003328,870714919371149312,871030740077252608,872554852792668160,875322682860638208,853564351586504704,852932447195631616)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:38.790
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.038
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst9) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst10) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst11) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst12) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst13) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst14) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst16  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted8 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted8 )   WHERE ((NOT (1=2)  AND( [PAU].[IsDeleted] = @IsDeleted6 )) AND ( [PAU].[AuditUserId] = @AuditUserId7 ))  AND ( [PAU].[IsDeleted] = @IsDeleted8 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId7 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@MethodConst9 [Value]: [Type]:String    
[Name]:@MethodConst10 [Value]:0 [Type]:Int32    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst14 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.204
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[ProcessNodeId],[Statuz],[StatuzDesc],[WaitOrBack],[IsEnable],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessStatuz]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.489
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.628
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.729
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.778
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.896
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679134371973 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:39.956
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:702534294601861 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.005
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:705091520331909 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.073
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:703912462606469 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.132
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:674228238864517 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.177
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:675249294651525 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.223
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.267
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.325
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [GPS].[ProcessId],[GPS].[GroupName],[GPS].[TypeCode],[GPS].[TypeBox],[GPS].[IsDeleted],[GPS].[CreateId],[GPS].[CreateBy],[GPS].[CreateTime],[GPS].[ModifyId],[GPS].[ModifyBy],[GPS].[ModifyTime],[GPS].[Version],[GPS].[Id] FROM [wf_GroupProcessSet] [GPS] Inner JOIN [wf_Process] [P] ON ( [GPS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted3 )   WHERE ((( [GPS].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [P].[Statuz] = @Statuz2 ))  AND ( [GPS].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.344
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[PSort],[IsOpenControl],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [UseUnitId] = @UseUnitId1 )) AND ( [Usage] = @Usage2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsOpenControl] = @IsOpenControl4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UseUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@Usage2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsOpenControl4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.673
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.717
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:40.746
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:52.997
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:53.032
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 15:58:53.041
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.268
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.269
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.272
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.316
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.322
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.325
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.331
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.339
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:00:03.344
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:39.667
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:39.825
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:39.886
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:39.907
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:39.931
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:39.969
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:749176146951675909 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:40.188
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:40.200
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:40.220
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/29 16:05:40 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 16:05:40 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:749177941128126469 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:40.313
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:40.372
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:40.415
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:5 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:218 [Type]:Int32    
[Name]:@LoginCount [Value]:1867 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 16:05:40 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:40.539
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:45.739
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:45.889
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (30,350,354,351,352,353,370,3200,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:47.326
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000010,100000000001001,100000000001002,100000000001003,100000000001004,100000000000020,100000000002001,100000000002002,100000000002017,100000000002008,100000000002009,100000000002010,100000000003001,100000000000030,100000000000040,100000000004001,100000000004003,100000000004004,100000000000050,100000000005001,100000000000060,100000000006001,100000000006002,100000000006003,100000000006004,100000000000070,100000000007004,100000000007005,100000000007006,100000000007007,100000000007017,100000000007018,100000000007019,100000000002011,100000000002012,100000000002013,100000000002014,100000000002015,100000000007001,100000000004002,100000000007002,100000000007014,100000000007020,100000000007021,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,815152703427579904,815152216284336128,814917889516638208,827558788750905344,827558788880928768,827558790864834560,840599930178375680,840600506052120576,842694325652426752,827558797206622208,815512156001800192,815512500404490240,815512620718100480,815512817057665024,844961710316982272,844957453845008384,827558795155607552,844957620178522112,844966541932892160,844966811886686208,844972844101144576,844972973948407808,844973424735424512,844973591006023680,844973895587991552,844974175394205696,844974402880671744,844974534216912896,844974696779747328,844974828485087232,844975023654440960,844975297731235840,844975449124638720,844975635729223680,844975797407059968,844975728242987008,844976309716127744,844976490087976960,844976660540297216,844976914006282240,844977126699438080,844977251022802944,844977462650605568,844977581781422080,844977790431268864,844978207466721280,844978326723366912,844977922153385984,844978006509228032,844978402241810432,844978933500743680,844979146500083712,844979303874564096,845255096391438336,844979454731096064,844979648616992768,844979828049317888,844980287262691328,844980359450857472,844980598295498752,844982125559025664,844981921363529728,844981743516651520,844981082699862016,844981392927363072,844980852977831936,844983416607739904,844983343421329408,844982957755076608,844982807137619968,844982595073609728,844982459761168384,844982262398193664,844983686142103552,844983827448205312,844983906800242688,844984091039240192,844984272614854656,844984515939012608,844984636638498816,844984773846765568,844984942273236992,844985090730627072,844985239972352000,844985309048344576,844985864302891008,844986081353928704,844986204750352384,844986319925940224,844986470987993088,844988224278368256,844988795462881280,844988925083652096,844989069602590720,844992020215762944,844992477537505280,844992359836946432,844992873186201600,844992931449278464,844993347960442880,844993496422027264,844997017473126400,844997280166580224,844997405890842624,844997569112182784,844997754236178432,844997884142161920,844997950672211968,844998050886717440,844998119732023296,852931579138281472,855107575388049408,855107866284003328,870714919371149312,871030740077252608,872554852792668160,875322682860638208,853564351586504704,852932447195631616)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:47.602
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:47.761
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst9) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst10) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst11) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst12) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst13) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst14) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst16  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted8 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted8 )   WHERE ((NOT (1=2)  AND( [PAU].[IsDeleted] = @IsDeleted6 )) AND ( [PAU].[AuditUserId] = @AuditUserId7 ))  AND ( [PAU].[IsDeleted] = @IsDeleted8 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId7 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@MethodConst9 [Value]: [Type]:String    
[Name]:@MethodConst10 [Value]:0 [Type]:Int32    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst14 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.081
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[ProcessNodeId],[Statuz],[StatuzDesc],[WaitOrBack],[IsEnable],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessStatuz]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.202
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.279
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.311
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.401
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.475
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679134371973 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.562
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:702534294601861 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.602
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:705091520331909 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.640
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:703912462606469 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.724
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:674228238864517 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.752
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:675249294651525 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.806
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.836
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.875
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [GPS].[ProcessId],[GPS].[GroupName],[GPS].[TypeCode],[GPS].[TypeBox],[GPS].[IsDeleted],[GPS].[CreateId],[GPS].[CreateBy],[GPS].[CreateTime],[GPS].[ModifyId],[GPS].[ModifyBy],[GPS].[ModifyTime],[GPS].[Version],[GPS].[Id] FROM [wf_GroupProcessSet] [GPS] Inner JOIN [wf_Process] [P] ON ( [GPS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted3 )   WHERE ((( [GPS].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [P].[Statuz] = @Statuz2 ))  AND ( [GPS].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:48.891
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[PSort],[IsOpenControl],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [UseUnitId] = @UseUnitId1 )) AND ( [Usage] = @Usage2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsOpenControl] = @IsOpenControl4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UseUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@Usage2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsOpenControl4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:49.450
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:49.483
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:05:49.525
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:03.617
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:03.635
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:03.648
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:05.106
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:05.135
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.087
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.098
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.106
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.163
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.176
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.189
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.198
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.221
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.805
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:06:28.818
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:09:35.843
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:09:36.505
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:09:36.566
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 16:09:36 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:09:36.617
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 16:09:36 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:09:38.910
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:14.360
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:14.431
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:14.485
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:14.541
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 16:10:14 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:14.561
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/29 16:10:14 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.179
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.205
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.219
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.230
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.242
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/29 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.250
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:749177941128126469 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.261
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.272
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.280
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/29 16:10:16 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 16:10:16 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:749179099011883013 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.296
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.309
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.316
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:5 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:218 [Type]:Int32    
[Name]:@LoginCount [Value]:1868 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/29 16:10:16 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:16.335
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:17.276
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:17.355
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (30,350,354,351,352,353,370,3200,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:19.385
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000010,100000000001001,100000000001002,100000000001003,100000000001004,100000000000020,100000000002001,100000000002002,100000000002017,100000000002008,100000000002009,100000000002010,100000000003001,100000000000030,100000000000040,100000000004001,100000000004003,100000000004004,100000000000050,100000000005001,100000000000060,100000000006001,100000000006002,100000000006003,100000000006004,100000000000070,100000000007004,100000000007005,100000000007006,100000000007007,100000000007017,100000000007018,100000000007019,100000000002011,100000000002012,100000000002013,100000000002014,100000000002015,100000000007001,100000000004002,100000000007002,100000000007014,100000000007020,100000000007021,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,815152703427579904,815152216284336128,814917889516638208,827558788750905344,827558788880928768,827558790864834560,840599930178375680,840600506052120576,842694325652426752,827558797206622208,815512156001800192,815512500404490240,815512620718100480,815512817057665024,844961710316982272,844957453845008384,827558795155607552,844957620178522112,844966541932892160,844966811886686208,844972844101144576,844972973948407808,844973424735424512,844973591006023680,844973895587991552,844974175394205696,844974402880671744,844974534216912896,844974696779747328,844974828485087232,844975023654440960,844975297731235840,844975449124638720,844975635729223680,844975797407059968,844975728242987008,844976309716127744,844976490087976960,844976660540297216,844976914006282240,844977126699438080,844977251022802944,844977462650605568,844977581781422080,844977790431268864,844978207466721280,844978326723366912,844977922153385984,844978006509228032,844978402241810432,844978933500743680,844979146500083712,844979303874564096,845255096391438336,844979454731096064,844979648616992768,844979828049317888,844980287262691328,844980359450857472,844980598295498752,844982125559025664,844981921363529728,844981743516651520,844981082699862016,844981392927363072,844980852977831936,844983416607739904,844983343421329408,844982957755076608,844982807137619968,844982595073609728,844982459761168384,844982262398193664,844983686142103552,844983827448205312,844983906800242688,844984091039240192,844984272614854656,844984515939012608,844984636638498816,844984773846765568,844984942273236992,844985090730627072,844985239972352000,844985309048344576,844985864302891008,844986081353928704,844986204750352384,844986319925940224,844986470987993088,844988224278368256,844988795462881280,844988925083652096,844989069602590720,844992020215762944,844992477537505280,844992359836946432,844992873186201600,844992931449278464,844993347960442880,844993496422027264,844997017473126400,844997280166580224,844997405890842624,844997569112182784,844997754236178432,844997884142161920,844997950672211968,844998050886717440,844998119732023296,852931579138281472,855107575388049408,855107866284003328,870714919371149312,871030740077252608,872554852792668160,875322682860638208,853564351586504704,852932447195631616)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:19.950
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:19.963
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst9) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst10) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst11) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst12) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst13) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst14) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst16  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted8 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted8 )   WHERE ((NOT (1=2)  AND( [PAU].[IsDeleted] = @IsDeleted6 )) AND ( [PAU].[AuditUserId] = @AuditUserId7 ))  AND ( [PAU].[IsDeleted] = @IsDeleted8 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId7 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@MethodConst9 [Value]: [Type]:String    
[Name]:@MethodConst10 [Value]:0 [Type]:Int32    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst14 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.152
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[ProcessNodeId],[Statuz],[StatuzDesc],[WaitOrBack],[IsEnable],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessStatuz]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.578
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.627
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.678
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.730
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.808
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679134371973 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.863
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:702534294601861 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.915
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:705091520331909 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.952
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:703912462606469 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:20.988
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:674228238864517 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.027
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:675249294651525 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.065
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.100
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.134
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [GPS].[ProcessId],[GPS].[GroupName],[GPS].[TypeCode],[GPS].[TypeBox],[GPS].[IsDeleted],[GPS].[CreateId],[GPS].[CreateBy],[GPS].[CreateTime],[GPS].[ModifyId],[GPS].[ModifyBy],[GPS].[ModifyTime],[GPS].[Version],[GPS].[Id] FROM [wf_GroupProcessSet] [GPS] Inner JOIN [wf_Process] [P] ON ( [GPS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted3 )   WHERE ((( [GPS].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [P].[Statuz] = @Statuz2 ))  AND ( [GPS].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.155
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[PSort],[IsOpenControl],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [UseUnitId] = @UseUnitId1 )) AND ( [Usage] = @Usage2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsOpenControl] = @IsOpenControl4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UseUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@Usage2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsOpenControl4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.435
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.472
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:10:21.505
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:11:17.805
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:11:17.814
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:11:17.838
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:11:17.885
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:11:17.885
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-29 16:11:17.907
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------