using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Repository.UnitOfWorks;
using Pipelines.Sockets.Unofficial.Arenas;
using AutoMapper;
using Hyun.Old.Util;
using MongoDB.Driver;
using Hyun.Core.Common.DB;
using FluentValidation;
using Hyun.Core.Model.Validator.Common;
using Hyun.Core.Model.Models;
using System.Text.RegularExpressions;
using System.Reflection;
using Org.BouncyCastle.Utilities.Encoders;
using StackExchange.Redis;
using Hyun.Core.Model.Model;
using Hyun.Core.Model;
using NPOI.Util;
using Hyun.Core.Model.Models.Workflow;
using Grpc.Core;
using NPOI.SS.Formula.Functions;


namespace Hyun.Core.Services
{

    ///<summary>
    ///PUnit方法
    ///</summary>
    public class PUnitServices : BaseServices<PUnit>, IPUnitServices
    {

        private readonly IUser user;
        private readonly IPSchoolExtensionServices schoolExtensionManager;
        private readonly IUnitOfWorkManage unitOfWorkManage;
        private readonly IBRepairHomeFeeServices repairHomeFeeManager;
        private readonly IMapper mapper;
        private readonly IBUnitSettingServices unitSettingManager;
        private readonly IBAddressServices addressManager;
        private readonly IAAddressPlaceUserServices addressPlaceUserManager;
        private readonly IRUnitGroupServices unitGroupManager;
        private readonly IRUnitDivisionServices unitDivisionManager;
        private readonly IPCompanyExtensionServices companyExtensionManager;
        private readonly IPCompanyImageServices companyImageManager;
        private readonly ISysUserInfoServices accountManager;
        private readonly ISysUserExtensionServices userManager;
        private readonly ISysUserRoleServices sysUserRoleManager;
        private readonly IPUserInDepartServices userInDepartManager;
        private readonly IBUserActionLogServices userActionLogManager;
        private readonly IPSupplierSchoolAuditServices supplierSchoolAuditManager;

        public PUnitServices(IUser _user, IPSchoolExtensionServices _schoolExtensionManager, IUnitOfWorkManage _unitOfWorkManage, IBRepairHomeFeeServices _repairHomeFeeManager, IMapper _mapper, IBUnitSettingServices _unitSettingManager, IBAddressServices _addressManager, IAAddressPlaceUserServices _addressPlaceUserManager, IRUnitGroupServices _unitGroupManager, IRUnitDivisionServices _unitDivisionManager, IPCompanyExtensionServices _companyExtensionManager, IPCompanyImageServices _companyImageManager, ISysUserInfoServices _accountManager, ISysUserExtensionServices _userManager, ISysUserRoleServices _sysUserRoleManager, IPUserInDepartServices _userInDepartManager, IBUserActionLogServices _userActionLogManager, IPSupplierSchoolAuditServices _supplierSchoolAuditManager)
        {
            user = _user;
            schoolExtensionManager = _schoolExtensionManager;
            unitOfWorkManage = _unitOfWorkManage;
            repairHomeFeeManager = _repairHomeFeeManager;
            mapper = _mapper;
            unitSettingManager = _unitSettingManager;
            addressManager = _addressManager;
            addressPlaceUserManager = _addressPlaceUserManager;
            unitGroupManager = _unitGroupManager;
            unitDivisionManager = _unitDivisionManager;
            companyExtensionManager = _companyExtensionManager;
            companyImageManager = _companyImageManager;
            accountManager = _accountManager;
            userManager = _userManager;
            sysUserRoleManager = _sysUserRoleManager;
            userInDepartManager = _userInDepartManager;
            userActionLogManager = _userActionLogManager;
            supplierSchoolAuditManager = _supplierSchoolAuditManager;
        }

        #region 查询数据
        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        //public async Task<PUnit> QueryById(long id)
        //{
        //    return await base.QueryById(id);
        //}

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        public async Task<List<PUnit>> Find(Expression<Func<PUnit, bool>> expression)
        {
            return await base.Query(expression);
        }
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">PUnitParam实体参数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<List<PUnit>> Find(PUnitParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            return await base.Query(expression, orderByFields);
        }
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">PUnitParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<PUnit>> GetPaged(PUnitParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            return await base.QueryPage(expression, param.pageIndex, param.pageSize, orderByFields);
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">PUnitParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<PUnit, bool>> ListFilter(PUnitParam param)
        {
            var expression = LinqExtensions.True<PUnit>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
                if (param.IdList != null && param.IdList.Count > 0)
                {
                    expression = expression.AndNew(t => param.IdList.Contains(t.Id));
                }
                if (param.Pid > 0)
                {
                    expression = expression.AndNew(t => t.PId == param.Pid);
                }
                if (param.statuzgt != -10000)
                {
                    expression = expression.AndNew(t => t.Statuz > param.statuzgt);
                }
                if (param.UnitType != -10000)
                {
                    expression = expression.AndNew(t => t.UnitType == param.UnitType);
                }
                if (param.UnitCityOrCounty)
                {
                    expression = expression.AndNew(t => t.UnitType == 1 || t.UnitType == 2);
                }
                if (param.UnitCityOrCountyOrSchool)
                {
                    expression = expression.AndNew(t => t.UnitType == 1 || t.UnitType == 2 || t.UnitType == 3);
                }
                if (param.UnitCityOrCountyOrSchoolOrCompany)
                {
                    expression = expression.AndNew(t => t.UnitType == 1 || t.UnitType == 2 || t.UnitType == 3 || t.UnitType == 4);
                }
            }
            return expression;
        }
        #endregion

        //<used>0</used>
        public async Task<List<PUnit>> GetByUnitType(int unitType)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }

        //<used>0</used>
        public async Task<DataTable> GetSchool(int countyId)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }

        //<used>0</used>
        public async Task<Result> Active(int id)
        {
            var result = await Task.Run(() => { Result r = new Result(); r.flag = 1; r.msg = ""; return r; }); return result;

        }

        //<used>0</used>
        public async Task<Result> InsertUpdateJj(int areaId, string townName, int pid, int unitType, int industryId, string code, string name, string brief, string pinYin, string pinYinBrief, string legal, string address, string zipCode, string url, string introduction, string logo, int vipGrade, string organizationCode, string loginPic, string mobile, string tel, string email, string position, string trafficMap, int employeeNum, int userId, string memo, int statuz, int classNum, int studentNum, int mileage, int id)
        {
            var result = await Task.Run(() => { Result r = new Result(); r.flag = 1; r.msg = ""; return r; }); return result;

        }

        //<used>0</used>
        public async Task<Result> ChangeMobile(int id, string mobile)
        {
            var result = await Task.Run(() => { Result r = new Result(); r.flag = 1; r.msg = ""; return r; }); return result;

        }

        //<used>0</used>
        public async Task<List<PUnit>> Delete(List<PUnit> entityCollection)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }

        //<used>1</used>
        public async Task<Result> UpdateSchoolInfo(long id, string brief, string organizationCode,
            string address, string zipCode, string url, string mobile, string introduction,
            int classNum, int studentNum, int teacherNum, decimal floorArea, decimal buildArea,
            int schoolStage, string memo, string schoolAdmin, string adminMobile,
            string headMaster, string msaterMobile, int schoolNature)
        {
            Result r = new Result();
            if(!string.IsNullOrEmpty(brief) && brief.Length > 250)
            {
                r.flag = 0;
                r.msg = "简称不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(organizationCode) && organizationCode.Length > 250)
            {
                r.flag = 0;
                r.msg = "组织机构代码不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(address) && address.Length > 250)
            {
                r.flag = 0;
                r.msg = "地址不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(zipCode) && zipCode.Length > 6)
            {
                r.flag = 0;
                r.msg = "邮编不能大于6个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(url) && url.Length > 250)
            {
                r.flag = 0;
                r.msg = "网址不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(mobile) && mobile.Length > 30)
            {
                r.flag = 0;
                r.msg = "短信服务手机号不能大于30个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(introduction) && introduction.Length > 1000)
            {
                r.flag = 0;
                r.msg = "简介不能大于1000个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(memo) && memo.Length > 500)
            {
                r.flag = 0;
                r.msg = "备注不能大于500个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(schoolAdmin) && schoolAdmin.Length > 60)
            {
                r.flag = 0;
                r.msg = "单位管理员不能大于60个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(adminMobile) && adminMobile.Length > 30)
            {
                r.flag = 0;
                r.msg = "管理员联系号码不能大于30个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(headMaster) && headMaster.Length > 60)
            {
                r.flag = 0;
                r.msg = "校长不能大于60个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(msaterMobile) && msaterMobile.Length > 30)
            {
                r.flag = 0;
                r.msg = "校长联系号码不能大于30个字符";
                return r;
            }

            if (!string.IsNullOrEmpty(organizationCode))
            {
                PUnit unit = await this.Db.Queryable<PUnit>().Where(f => f.OrganizationCode == organizationCode && f.Id != id).FirstAsync();
                if (unit != null)
                {
                    r.flag = 0;
                    r.msg = $"组织机构代码证已经被【{user.UnitName}】使用，保存失败！";
                    return r;
                }
            }

            PUnit u = await base.QueryById(id);
            if (u != null)
            {
                u.Brief = brief;
                u.OrganizationCode = organizationCode;
                u.Address = address;
                u.ZipCode = zipCode;
                u.Url = url;
                u.Mobile = mobile;
                u.Introduction = introduction;
                u.Legal = headMaster;

                await base.Update(u);
            }

            PSchoolExtension schoolExtension = await this.Db.Queryable<PSchoolExtension>().Where(f => f.UnitId == id).FirstAsync();
            if (schoolExtension != null)
            {
                schoolExtension.Memo = memo;
                schoolExtension.SchoolAdmin = schoolAdmin;
                schoolExtension.AdminMobile = adminMobile;
                schoolExtension.HeadMaster = headMaster;
                schoolExtension.MsaterMobile = msaterMobile;
                await schoolExtensionManager.Update(schoolExtension);

                if (schoolExtension.IsLock == 0)
                {
                    schoolExtension.ClassNum = classNum;
                    schoolExtension.StudentNum = studentNum;
                    schoolExtension.TeacherNum = teacherNum;
                    schoolExtension.FloorArea = floorArea;
                    schoolExtension.BuildArea = buildArea;
                    schoolExtension.SchoolStage = schoolStage;
                    schoolExtension.SchoolNature = schoolNature;
                    await schoolExtensionManager.Update(schoolExtension);
                }
            }
            else
            {
                await schoolExtensionManager.Add(new PSchoolExtension()
                {
                    UnitId = id,
                    ClassNum = classNum,
                    StudentNum = studentNum,
                    TeacherNum = teacherNum,
                    FloorArea = floorArea,
                    BuildArea = buildArea,
                    SchoolStage = schoolStage,
                    Memo = memo,
                    SchoolAdmin = schoolAdmin,
                    AdminMobile = adminMobile,
                    HeadMaster = headMaster,
                    MsaterMobile = headMaster
                });


            }

            r.flag = 1;
            r.msg = "执行成功";
            return r;
        }

        //<used>1</used>
        public async Task<Result> DeleteByIds(string ids)
        {
            Result r = new Result();
            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CityAdmin) && !user.Roles.Contains(RoleTypes.CoutyAdmin))
            {
                r.flag = 0;
                r.msg = "无删除权限";
                return r;
            }
            object[] objUserIds = ids.Split(',').Cast<object>().ToArray();
            List<PUnit> listUnit = await base.QueryByIDs(objUserIds);
            List<long> listUnitId = listUnit.Select(f => f.Id).ToList();
            if (user.Roles.Contains(RoleTypes.CityAdmin) || user.Roles.Contains(RoleTypes.CoutyAdmin))
            {
                if (listUnit.Exists(f => f.PId != user.UnitId))
                {
                    r.flag = 0;
                    r.msg = "只能管理本辖区，删除失败";
                    return r;
                }
            }
            if (listUnit.Count != objUserIds.Length)
            {
                r.flag = 0;
                r.msg = "有单位不存在的数据,删除失败";
                return r;
            }

            var listUser = this.Db.Queryable<PUnit>()
                .InnerJoin<SysUserExtension>((unit, user) => unit.Id == user.UnitId)
                .Where((unit, user) => listUnitId.Contains(unit.Id)).ToList();
            if (listUser.Count > 0)
            {
                r.flag = 0;
                r.msg = "单位已经有数据，删除失败";
                return r;
            }

            listUnit.ForEach(f => f.Statuz = -1);
            await base.Update(listUnit);
            r.flag = 1;
            r.msg = "删除成功";
            return r;
        }

        //<used>1</used>
        public async Task<Result> DeleteUnitInfo(long id)
        {
            Result r = new Result();
            PUnit unit = await base.QueryById(id);
            if (unit != null)
            {
                if (unit.Statuz == 1)
                {
                    r.flag = 0;
                    r.msg = "删除失败,该单位已审核，故不能删除";
                    return r;
                }
                if (unit.Statuz == -1)
                {
                    r.flag = 0;
                    r.msg = "删除失败,帐号已经禁用";
                    return r;
                }
                try
                {
                    unitOfWorkManage.BeginTran();

                    if (unit.SourceType == 3)
                    {
                        unit.SourceType = 2;
                        unit.Mobile = "";
                        unit.OrganizationCode = "";
                        unit.Tel = "";
                        await base.Update(unit);

                    }
                    else if (unit.SourceType == 1)
                    {
                        await base.Delete(unit);
                    }

                    //删除图片
                    List<PCompanyImage> listCompanyImg = await companyImageManager.Query(f => f.UnitId == id);
                    if (listCompanyImg.Count > 0)
                    {
                        var comImg = listCompanyImg.Select(t => t.Id.ToString()).ToList();
                        //删除原数据
                        await companyImageManager.DeleteByIds(comImg.ToArray());
                    }

                    //删除账号
                    List<SysUserInfo> listAccount = this.Db.Queryable<SysUserInfo>()
                        .InnerJoin<SysUserExtension>((a, u) => a.UserExtensionId == u.Id)
                        .Where((a, u) => u.UnitId == id)
                        .Select((a, u) => new SysUserInfo()
                        {
                            Id = a.Id,
                            LoginName = a.LoginName
                        }).ToList();

                    if (listAccount.Count > 0)
                    {
                        var account = listAccount.Select(f => f.Id.ToString()).ToList();
                        await accountManager.DeleteByIds(account.ToArray());
                    }

                    //删除用户
                    List<SysUserExtension> listUser = await userManager.Query(f => f.UnitId == id);
                    if (listUser.Count > 0)
                    {
                        var u = listUser.Select(f => f.Id.ToString()).ToList();
                        await userManager.DeleteByIds(u.ToArray());
                    }

                    //删除单位扩展信息
                    List<PCompanyExtension> listExtesion = await companyExtensionManager.Query(f => f.UnitId == id);
                    if (listExtesion.Count > 0)
                    {
                        var extension = listExtesion.Select(f => f.Id.ToString()).ToList();
                        await companyExtensionManager.DeleteByIds(extension.ToArray());
                    }

                    r.flag = 1;
                    r.msg = "执行成功";

                    unitOfWorkManage.CommitTran();
                }
                catch (Exception)
                {
                    unitOfWorkManage.RollbackTran();
                    throw;
                }

            }
            return r;
        }

        //<used>0</used>
        public async Task<Result> Inactive(int id)
        {
            var result = await Task.Run(() => { Result r = new Result(); r.flag = 1; r.msg = ""; return r; }); return result;

        }

        //<used>0</used>
        public async Task<DataTable> ExistsOrganizationCode(string orgCode)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }

        //<used>0</used>
        public async Task<DataTable> ExistsCompanyName(string name)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }

        //<used>0</used>
        public async Task<DataTable> ExistsUserName(string name)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }

        //<used>1</used>
        public async Task<Result> ExamineStatuz(long id, string companyName)
        {
            Result r = new Result();
            PUnit u = await base.QueryById(id);
            if (u != null)
            {
                unitOfWorkManage.BeginTran();
                try
                {
                    u.Statuz = 1;
                    await base.Update(u);

                    //批量更新用户
                    List<SysUserExtension> listUser = await userManager.Query(f => f.UnitId == id);
                    listUser.ForEach(f => f.Statuz = 1);
                    await userManager.Update(listUser);

                    //批量更新账号
                    List<SysUserInfo> listAccount =
                        this.Db.Queryable<SysUserInfo>()
                      .InnerJoin<SysUserExtension>((a, u) => a.UserExtensionId == u.Id)
                      .Where((a, u) => u.UnitId == id)
                      .Select((a, u) => new SysUserInfo()
                      {
                          Id = a.Id,
                          UserExtensionId = a.UserExtensionId,
                          LoginName = a.LoginName,
                          Mobile = a.Mobile,
                          //Email = a.Email,
                          LoginPWD = a.LoginPWD,
                          NickName = a.NickName,
                          CreateId = a.CreateId,
                          CreateTime = a.CreateTime,
                          Statuz = a.Statuz,
                          //IsUseRealName = a.IsUseRealName,
                          UserValidate = a.UserValidate,
                      }).ToList();
                    listAccount.ForEach(f => f.Statuz = 1);
                    await accountManager.Update(listAccount);

                    if (listUser.Count > 0)
                    {
                        List<SysUserRole> listUserInRole = new List<SysUserRole>();
                        foreach (SysUserExtension user in listUser)
                        {
                            listUserInRole.Add(new SysUserRole()
                            {
                                UserId = user.Id,
                                RoleId = 40
                            });
                        }
                        await sysUserRoleManager.Add(listUserInRole);
                    }

                    r.flag = 1;
                    r.msg = "审核成功";

                    unitOfWorkManage.CommitTran();
                }
                catch (Exception)
                {
                    unitOfWorkManage.RollbackTran();
                    throw;
                }
            }
            return r;
        }

        //<used>1</used>
        public async Task<Result> InsertCompany(long pid, int unitType, int industryId, string code, string name, string mobile, int statuz, string userName, string acctName, string pwd, long serviceAreaId)
        {
            Result r = new Result();
            if (!string.IsNullOrEmpty(code) && code.Length > 15)
            {
                r.flag = 0;
                r.msg = "单位编号不能大于15个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(name) && name.Length > 250)
            {
                r.flag = 0;
                r.msg = "单位名称不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(mobile) && mobile.Length > 30)
            {
                r.flag = 0;
                r.msg = "手机号不能大于30个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(userName) && userName.Length > 60)
            {
                r.flag = 0;
                r.msg = "手机号不能大于60个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(acctName) && acctName.Length > 60)
            {
                r.flag = 0;
                r.msg = "手机号不能大于60个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(pwd) && pwd.Length > 60)
            {
                r.flag = 0;
                r.msg = "密码不能大于60个字符";
                return r;
            }
            try
            {
                unitOfWorkManage.BeginTran();

                PUnit unit = await this.Db.Queryable<PUnit>().Where(f => f.Name == name).FirstAsync();
                if (unit == null)
                {
                    long unitId = await base.Add(new PUnit()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        AreaId = 0,
                        PId = pid,
                        UnitType = unitType,
                        IndustryId = industryId,
                        Code = code,
                        Name = name,
                        Legal = "",
                        VipGrade = 0,
                        UserId = 1,
                        EmployeeNum = 0,
                        Address = "",
                        OrganizationCode = "",
                        Mobile = mobile,
                        Tel = "",
                        Statuz = statuz
                    });

                    long userId = await userManager.Add(new SysUserExtension()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UnitId = unitId,
                        Name = userName,
                        Mobile = mobile,
                        Tel = "",
                        Sex = "0",
                        Birthday = DateTime.Now,
                        UserId = 1,
                        Statuz = 0
                    });

                    await companyExtensionManager.Add(new PCompanyExtension()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UnitId = unitId,
                        ServerType = 1,
                        AreaId = serviceAreaId
                    });

                    await companyExtensionManager.Add(new PCompanyExtension()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UnitId = unitId,
                        ServerType = 2,
                        AreaId = serviceAreaId
                    });

                    SysUserInfo account = await this.Db.Queryable<SysUserInfo>().Where(f => f.LoginName == acctName).FirstAsync();
                    if (account == null)
                    {
                        await accountManager.Add(new SysUserInfo()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            UserExtensionId = userId,
                            LoginName = acctName,
                            Mobile = mobile,
                            LoginPWD = pwd,
                            CreateId = 0,
                            Statuz = 3,
                            //IsUseRealName = false
                        });
                    }
                }
                else
                {
                    if (unit.Statuz == 0 && unit.SourceType == 2)
                    {
                        unit.PId = 0;
                        unit.Statuz = 2;
                        unit.Mobile = mobile;
                        unit.SourceType = 3;
                        unit.RegTime = DateTime.Now;

                        await this.Db.Updateable<PUnit>(unit).UpdateColumns(f => new { f.PId, f.Statuz, f.Mobile, f.SourceType, f.RegTime }).ExecuteCommandAsync();

                        long userId = await userManager.Add(new SysUserExtension()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            UnitId = unit.Id,
                            Name = userName,
                            Mobile = mobile,
                            Tel = "",
                            Sex = "0",
                            Birthday = DateTime.Now,
                            UserId = 1,
                            Statuz = 0
                        });

                        await companyExtensionManager.Add(new PCompanyExtension()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            UnitId = unit.Id,
                            ServerType = 1,
                            AreaId = serviceAreaId
                        });

                        await companyExtensionManager.Add(new PCompanyExtension()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            UnitId = unit.Id,
                            ServerType = 2,
                            AreaId = serviceAreaId
                        });

                        SysUserInfo account = await this.Db.Queryable<SysUserInfo>().Where(f => f.LoginName == acctName).FirstAsync();
                        if (account == null)
                        {
                            await accountManager.Add(new SysUserInfo()
                            {
                                Id = BaseDBConfig.GetYitterId(),
                                UserExtensionId = userId,
                                LoginName = acctName,
                                Mobile = mobile,
                                LoginPWD = pwd,
                                CreateId = 0,
                                Statuz = 3,
                                //IsUseRealName = false
                            });
                        }

                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "单位已注册。您如需认证，请登录平台后前往认证。";
                        return r;
                    }
                }

                unitOfWorkManage.CommitTran();
            }
            catch (Exception)
            {
                unitOfWorkManage.RollbackTran();
                throw;
            }

            r.flag = 1;
            r.msg = "注册成功，请您继续完善企业信息！";
            return r;

        }

        //<used>1</used>
        public async Task<Result> SubmitCertificationCompany(string unitName, long areaId, string legal, string address, string organizationCode, string userName, string tel, string busLinUrl)
        {
            Result r = new Result();
            if (!string.IsNullOrEmpty(legal) && legal.Length > 60)
            {
                r.flag = 0;
                r.msg = "法人不能大于60个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(address) && address.Length > 250)
            {
                r.flag = 0;
                r.msg = "地址不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(organizationCode) && organizationCode.Length > 250)
            {
                r.flag = 0;
                r.msg = "组织机构代码不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(tel) && tel.Length > 30)
            {
                r.flag = 0;
                r.msg = "联系电话不能大于30个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(userName) && userName.Length > 250)
            {
                r.flag = 0;
                r.msg = "名称不能大于250个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(busLinUrl) && busLinUrl.Length > 500)
            {
                r.flag = 0;
                r.msg = "营业执照路径不能大于500个字符";
                return r;
            }
            try
            {
                unitOfWorkManage.BeginTran();

                PUnit unit = await this.Db.Queryable<PUnit>().Where(f => f.Name == unitName && f.UnitType == 4 && f.Statuz == 2).FirstAsync();
                if (unit == null)
                {
                    r.flag = 0;
                    r.msg = "单位不存在或不可提交认证。";
                    return r;
                }

                SysUserExtension pUser = await this.Db.Queryable<SysUserExtension>().Where(f => f.UnitId == unit.Id).FirstAsync();
                if (pUser == null)
                {
                    r.flag = 0;
                    r.msg = "用户信息不存在。";
                    return r;
                }

                SysUserInfo account = await this.Db.Queryable<SysUserInfo>().Where(f => f.UserExtensionId == pUser.Id).FirstAsync();
                if (account == null)
                {
                    r.flag = 0;
                    r.msg = "账号信息不存在。";
                    return r;

                }

                //string CityCode = "";
                //BArea area =await this.Db.Queryable<BArea>().Where(f => f.Id == areaId).FirstAsync();
                //if (area != null)
                //{
                //    ECityCode eCity = await this.Db.Queryable<ECityCode>().Where(f => f.AreaId == area.Pid).FirstAsync();
                //    if (eCity != null)
                //    {
                //        CityCode = $"G{eCity.Code.ToUpper()}";
                //    }
                //}
                //if (string.IsNullOrEmpty(CityCode))
                //{
                //    r.flag = 0;
                //    r.msg = "城市编码不存在，请联系管理员";
                //    return r;
                //}

                unit.AreaId = areaId;
                unit.Legal = legal;
                unit.Address = address;
                unit.OrganizationCode = organizationCode;
                unit.Tel = tel;
                unit.Statuz = 0;
                await this.Db.Updateable<PUnit>(unit).UpdateColumns(f => new { f.AreaId, f.Legal, f.Address, f.OrganizationCode, f.Tel, f.Statuz }).ExecuteCommandAsync();

                pUser.Name = userName;
                pUser.Tel = tel;
                await this.Db.Updateable<PUnit>(pUser).UpdateColumns(f => new { f.Name, f.Tel }).ExecuteCommandAsync();


                account.Statuz = 2;
                await this.Db.Updateable<SysUserInfo>(account).UpdateColumns(f => new { f.Statuz }).ExecuteCommandAsync();

                if (!string.IsNullOrEmpty(busLinUrl))
                {
                    await companyImageManager.Add(new PCompanyImage()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        Name = "营业执照证",
                        ImagePath = busLinUrl,
                        UnitId = unit.Id,
                        Statuz = 0,
                        ClassType = 0,
                        IsShow = 0,
                        UserId = pUser.Id,
                        RegTime = DateTime.Now
                    });
                }


                unitOfWorkManage.CommitTran();
            }
            catch (Exception)
            {
                unitOfWorkManage.RollbackTran();
                throw;
            }

            r.flag = 1;
            r.msg = "提交成功，我们会在两个工作日内进行审核，请耐心等待！！";
            return r;

        }

        //<used>0</used>
        public async Task<Result> SchoolRegister(int pid, string name, string mobile, string userName, string userPswd, int id)
        {
            var result = await Task.Run(() => { Result r = new Result(); r.flag = 1; r.msg = ""; return r; }); return result;

        }

        //<used>1</used>
        public async Task<DataTable> GetSchoolDetail(PUnitParam param)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }

        //<used>0</used>
        public async Task<List<PUnit>> GetByPid(int pid)
        {
            await Task.Delay(1000); throw new NotImplementedException();
        }


        /// <summary>
        /// 添加单位信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        //<used>1</used>
        public async Task<Result> InsertUpdate(PUnitDto o)
        {
            Result r = new Result();
            PUnit unit = mapper.Map<PUnit>(o);

            #region 增加FluentValidation验证
            var validator = new PUnitValidator();
            var result = validator.Validate(unit);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                r.flag = 0;
                r.msg = tipMsg;
                return r;
            }
            #endregion

            

            unit.Statuz = 1;
            PUnit u = await base.QueryById(o.Id);
            PSchoolExtension extension = null;
            BRepairHomeFee homeFee = null;
            if (unit.UnitType == 3)
            {
                var listHomeFee = await repairHomeFeeManager.Query(f => f.SchoolId == o.Id);
                if (listHomeFee.Count > 0)
                {
                    homeFee = listHomeFee.FirstOrDefault();
                }

                var listExtension = await schoolExtensionManager.Query(f => f.UnitId == o.Id);
                if (listExtension.Count > 0)
                {
                    extension = listExtension.FirstOrDefault();
                }

                if (unit.ClassNum == 0)
                {
                    unit.ClassNum = 1;
                }
                if (unit.TeacherNum == 0)
                {
                    unit.TeacherNum = 1;
                }
                if (unit.StudentNum == 0)
                {
                    unit.StudentNum = 1;
                }

                //如果创建人是区县则直接默认区域Id为该区县Id
                if (user.UnitTypeId == UnitTypeEnum.County.ToEnumInt())
                {
                    unit.AreaId = user.AreaId;
                }
            }

            var listUnit = await base.Query(f => f.Id != o.Id && (f.Name == o.Name) && f.Statuz > -1);
            if (listUnit.Count > 0)
            {
                r.flag = 0;
                r.msg = "单位编码或名称与其他单位重复，执行失败。";
                return r;
            }

            try
            {
                unitOfWorkManage.BeginTran();
                if (u != null)
                {
                    //修改
                    await base.Update(unit);

                    if (unit.UnitType == 3)
                    {
                        if (extension != null)
                        {
                            string strPeriod = await GetPeriodBySchoolStage(unit.SchoolStage);
                            extension.SchoolStage = unit.SchoolStage;
                            extension.ClassNum = unit.ClassNum;
                            extension.StudentNum = unit.StudentNum;
                            extension.TeacherNum = unit.TeacherNum;
                            extension.FloorArea = unit.FloorArea;
                            extension.BuildArea = unit.BuildArea;
                            extension.SchoolAdmin = unit.SchoolAdmin;
                            extension.AdminMobile = unit.AdminMobile;
                            extension.SchoolNature = unit.SchoolNature;
                            extension.UnderTeacherNum = 0;
                            extension.Period = strPeriod;
                            await schoolExtensionManager.Update(extension);
                        }
                        if (homeFee != null)
                        {
                            homeFee.Mileage = o.Mileage;
                            await repairHomeFeeManager.Update(homeFee);
                        }
                    }
                }
                else
                {
                    //新增
                    var listUcUnit = await base.Query(f => f.Code == o.Code && f.Statuz == -1);
                    if (listUcUnit.Count > 0)
                    {
                        PUnit uc = listUcUnit.FirstOrDefault();
                        if (uc != null)
                        {
                            uc.Statuz = 1;
                            await base.Update(uc);
                        }
                    }
                    else
                    {
                        //var listUnitCode = await base.Query(f => f.Code == o.Code);
                        //if (listUnitCode.Count > 0)
                        //{
                        //    r.flag = 0;
                        //    r.msg = "单位编码重复，执行失败。";
                        //    return r;
                        //}

                        await base.Add(unit);

                        if (o.UnitType == 2)
                        {
                            await unitSettingManager.Add(new BUnitSetting()
                            {
                                UnitId = unit.Id,
                                NeedHeader = 0,
                                NeedCounty = 0,
                                EvaluationPublic = 1,
                                SchoolResponseHour = 2,
                                SchoolRepairDay = 2,
                                CompanyResponseHour = 6,
                                CompanyRepairDay = 2,
                                DefaultAuditPassDay = 2,
                                DefaultEvaluationDay = 2,
                                DefaultEvaluationStar = 5,
                                InheritServiceFee = 1,
                                InheritPartsFee = 1,
                                InheritRepairHomeFee = 1,
                                InheritCompany = 1,
                                HomePrice = 0,
                                EvaluationDay = 2,
                                SchoolAcceptanceDay = 2,
                            });
                        }
                        if (o.UnitType == 3)
                        {
                            //初始化单位地址信息
                            await addressManager.InitalizationUnit(unit.Id);
                            //初始化设备地点场所属性
                            await addressPlaceUserManager.IniPlaceProperty(unit.Id);

                            string strPeriod = await GetPeriodBySchoolStage(unit.SchoolStage);

                            await schoolExtensionManager.Add(new PSchoolExtension()
                            {
                                UnitId = unit.Id,
                                ClassNum = unit.ClassNum,
                                StudentNum = unit.StudentNum,
                                TeacherNum = unit.TeacherNum,
                                FloorArea = unit.FloorArea,
                                BuildArea = unit.BuildArea,
                                SchoolStage = o.SchoolStage,
                                SchoolAdmin = unit.SchoolAdmin,
                                AdminMobile = unit.AdminMobile,
                                SchoolNature = unit.SchoolNature,
                                Period = strPeriod

                            });

                            await repairHomeFeeManager.Add(new BRepairHomeFee()
                            {
                                UserId = unit.UserId,
                                UnitId = user.UnitId,
                                SchoolId = unit.Id,
                                CompanyId = 0,
                                Mileage = unit.Mileage,
                                RegTime = DateTime.Now
                            });

                            await unitSettingManager.Add(new BUnitSetting()
                            {
                                UnitId = unit.Id,
                                NeedHeader = 0,
                                NeedCounty = 0,
                                EvaluationPublic = 0,
                                SchoolResponseHour = 6,
                                SchoolRepairDay = 3,
                                CompanyResponseHour = 6,
                                CompanyRepairDay = 3,
                                DefaultAuditPassDay = 0,
                                DefaultEvaluationDay = 30,
                                DefaultEvaluationStar = 0,
                                InheritServiceFee = 0,
                                InheritPartsFee = 0,
                                InheritRepairHomeFee = 1,
                                InheritCompany = 0,
                                HomePrice = 0,
                                EvaluationDay = 30,
                                SchoolAcceptanceDay = 30
                            });

                            //初始化维修分类(执行时间很长)
                            //await InitializeDefault();
                        }
                    }

                    //当添加单位为单位或企业时增加认证信息数据
                    if(o.UnitType == 3 || o.UnitType == 4)
                    {
                        await supplierSchoolAuditManager.Add(new PSupplierSchoolAudit()
                        {
                            UnitId = unit.Id,
                            Name = o.Name,
                            SocialCreditCode = o.OrganizationCode,
                            ProvinceId = 0,
                            CityId = 0,
                            CountyId = o.AreaId,
                            Address = o.Address,
                            Url = o.Url,
                            Introduction = o.Introduction,
                            Tel = o.Tel,
                            Nature = o.SchoolNature,
                            Period = "",
                            SchoolStage = o.SchoolStage,
                            BeLongUnit = false,
                            AuthStatuz = -1,
                            Reason = "",
                            IsCurrent = true
                        });
                    }
                }

                //调用选择是否服务商、供应商 更新企业扩展信息表信息
                await UpdateCompanyExtent(unit.Id, unit.IsServiceProvider, unit.IsSupplier);

                r.flag = 1;
                r.msg = "保存成功";
                r.data.rows = unit.Id;
                unitOfWorkManage.CommitTran();
            }
            catch (Exception)
            {
                unitOfWorkManage.RollbackTran();
                throw;
            }
            return r;
        }


        public async Task<Result> InitializeDefault()
        {
            Result r = new Result();
            var listUnitGroup = await unitGroupManager.Query();
            var listPUnit = await base.Query(f => f.UnitType == 3);

            //查询UnitId为0的数据
            List<RUnitGroup> listUnitGroupAdd = listUnitGroup.Where(f => f.UnitId == 0).ToList();

            if (listUnitGroup.Count > 0 && listPUnit.Count > 0)
            {
                List<long> listUnit = listPUnit.Select(f => f.Id).ToList();
                List<long> listGroup = listUnitGroup.Distinct().Select(f => f.UnitId).ToList();
                List<long> listUnitExcludingGroup = listUnit.Except(listGroup).ToList();
                foreach (long unitId in listUnitExcludingGroup)
                {
                    //先批量删除
                    List<RUnitDivision> listDivision = this.Db.Queryable<RUnitDivision>()
                         .InnerJoin<RUnitGroup>((ud, ug) => ud.GroupId == ug.Id)
                         .Where((ud, ug) => ug.UnitId == unitId).ToList();
                    if (listDivision.Count > 0)
                    {
                        string divisionIds = string.Join(",", listDivision.Select(f => f.Id));
                        await unitGroupManager.DeleteByIds(divisionIds);
                    }
                    var listDeleteGroup = await unitGroupManager.Query(f => f.UnitId == unitId);
                    if (listDeleteGroup.Count > 0)
                    {
                        string groupIds = string.Join(",", listDeleteGroup.Select(f => f.Id));
                        await this.DeleteByIds(groupIds);
                    }


                    //添加
                    if (listUnitGroupAdd.Count > 0)
                    {
                        await unitGroupManager.Add(listUnitGroupAdd);
                    }

                    List<RUnitDivision> listDivisionAdd = this.Db.Queryable<RUnitGroup>()
                   .InnerJoin<RUnitGroup>((u0, u1) => u0.Name == u1.Name)
                   .InnerJoin<RUnitDivision>((u0, u1, d) => u0.Id == d.GroupId)
                   .Where((u0, u1, d) => u0.UnitId == 0 && u1.UnitId == unitId)
                   .Select((u0, u1, d) => new RUnitDivision
                   {
                       GroupId = u1.Id,
                       DeviceCodeId = d.DeviceCodeId
                   }).ToList();
                    if (listDivisionAdd.Count > 0)
                    {
                        await unitDivisionManager.Add(listDivisionAdd);
                    }

                }

            }
            r.flag = 1;
            r.msg = "";
            return r;
        }

        /// <summary>
        /// 选择是否服务商、供应商 更新企业扩展信息表信息
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="isServiceProvider"></param>
        /// <param name="isSupplier"></param>
        public async Task<Result> UpdateCompanyExtent(long unitId, bool isServiceProvider, bool isSupplier)
        {
            Result r = new Result();
            var list = companyExtensionManager.Query(f => f.UnitId == unitId).Result;
            var obj = list.Where(f => f.ServerType == 1).FirstOrDefault();
            if (isServiceProvider)
            {
                //是服务商
                if (obj == null)
                {
                    PCompanyExtension model = new PCompanyExtension();
                    model.UnitId = unitId;
                    model.ServerType = 1;
                    model.AreaId = ApplicationConfig.defaultSetdefaultCity.ObjToLong();
                    await companyExtensionManager.Add(model);
                }
            }
            else
            {
                if (obj != null)
                {
                    await companyExtensionManager.DeleteById(obj.Id);
                }
            }

            //供应商信息
            var supplier = list.Where(f => f.ServerType == 2).FirstOrDefault();
            if (isSupplier)
            {
                //是服务商
                if (supplier == null)
                {
                    PCompanyExtension model = new PCompanyExtension();
                    model.UnitId = unitId;
                    model.ServerType = 2;
                    model.AreaId = ApplicationConfig.defaultSetdefaultCity.ObjToLong();
                    await companyExtensionManager.Add(model);
                }
            }
            else
            {
                if (supplier != null)
                {
                    await companyExtensionManager.DeleteById(supplier.Id);
                }
            }
            return r;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result> InsertUpdateUser(VUserDetail o)
        {
            Result r = new Result();

            #region 增加FluentValidation验证
            var validator = new VUserDetailValidator();
            var result = validator.Validate(o);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                r.flag = 0;
                r.msg = tipMsg;
                return r;
            }
            #endregion

            SysUserExtension pUser = mapper.Map<SysUserExtension>(o);
            int administratorType = 2;
            int roleIndex = 0;
            int superIndex = 0;
            string roleIds = o.StrRoleIds;
            string strRoleIds = $",{roleIds},";
            if (pUser.UserType == 0)
            {
                pUser.UserType = 1;
            }

            roleIndex = strRoleIds.IndexOf("-10");
            superIndex = strRoleIds.IndexOf(",0,");
            if (roleIndex > 0 && superIndex == -1)
            {
                administratorType = 1;
            }
            else if (superIndex > 0)
            {
                administratorType = 0;
            }
            if (roleIds == "-10")
            {
                strRoleIds = ",-10,0,";
            }

            roleIds = strRoleIds.Replace(",-10,", ",");
            roleIds = roleIds.TrimStart(',').TrimEnd(',');

            pUser.AdministratorType = administratorType;

            PUnit u = await base.QueryById(o.UnitId);
            if (u != null && u.Statuz == 1)
            {
                //判断用户权限是否跨越单位。一个用户只能同时拥有一个单位的权限，否则非法，比如用户权限 21,31肯定不正确的
                var list = roleIds.Split(",");
                bool allStartWithSameLetter = list.All(s => s.Length > 0 && s[0] == list[0][0]);
                if (!allStartWithSameLetter)
                {
                    r.flag = 0;
                    r.msg = "没有权限，非法操作，系统阻止";
                    return r;
                }

                //如果不是超级管理员
                if (!user.IsSystemUser)
                {
                    //判断是否为管理员
                    if (!user.Roles.Contains(RoleTypes.CityAdmin) && !user.Roles.Contains(RoleTypes.CoutyAdmin)
                        && !user.Roles.Contains(RoleTypes.SchoolAdmin) && !user.Roles.Contains(RoleTypes.CompanyAdmin))
                    {
                        r.flag = 0;
                        r.msg = "只有管理员才能修改用户权限，请使用管理员账号登录。";
                        return r;
                    }

                    //特色专家判断
                    string rIndex = ((int)user.Roles[0]).ToString().Substring(0, 1);
                    if (o.StrRoleIds.Equals("100"))
                    {
                        if (!rIndex.Equals("1") && !rIndex.Equals("2") && rIndex.Equals("3"))
                        {
                            r.flag = 0;
                            r.msg = "权限越级操作，系统阻止。";
                            return r;
                        }
                    }
                    else
                    {
                        //需剔除@RoleIds中100，再验证
                        string strRole = $",{o.StrRoleIds},";
                        string tempRolesIds = strRole.Replace(",100,", "").TrimStart(',').TrimEnd(',');
                        string tempIndex = tempRolesIds.Substring(0, 1);
                        int rAdmin = int.Parse(rIndex);
                        int tAdmin = int.Parse(tempIndex);
                        if (tAdmin - rAdmin > 1 || tAdmin - rAdmin < 0)
                        {
                            r.flag = 0;
                            r.msg = "权限越级操作，系统阻止。";
                            return r;
                        }
                    }
                }

                try
                {
                    unitOfWorkManage.BeginTran();

                    //增加判断
                    if (o.Sex == "男")
                    {
                        pUser.Sex = "1";
                    }
                    else if (o.Sex == "女")
                    {
                        pUser.Sex = "0";
                    }
                    else if (o.Sex == null || o.Sex == "")
                    {
                        pUser.Sex = "-1";
                    }

                    //
                    if (pUser.Id > 0)
                    {
                        var listAccount = await this.Db.Queryable<SysUserInfo>().Where(f => f.UserExtensionId != pUser.Id && f.LoginName == o.AcctName && f.Statuz > 0).ToListAsync();
                        if (listAccount.Count > 0)
                        {
                            r.flag = 0;
                            r.msg = "更新数据失败。可能原因：帐号和他人重复";
                            return r;
                        }

                        //判断手机号码是否重复
                        SysUserExtension objUser = await this.Db.Queryable<SysUserExtension>().Where(f => f.Mobile == o.Mobile && f.Id != pUser.Id && f.Statuz > 0).FirstAsync();
                        if (objUser != null)
                        {
                            r.flag = 0;
                            r.msg = "更新数据失败。手机号码已经存在";
                            return r;
                        }

                        //更新用户
                        pUser.Statuz = 1;
                        await userManager.Update(pUser);

                        //更新账号
                        SysUserInfo upAccount = await this.Db.Queryable<SysUserInfo>().Where(f => f.UserExtensionId == pUser.Id).FirstAsync();
                        if (upAccount != null)
                        {
                            upAccount.LoginName = o.AcctName;
                            upAccount.Mobile = o.Mobile;
                            //upAccount.Email = o.Email;
                            upAccount.NickName = o.NickName;
                            //upAccount.IsUseRealName = o.IsUseRealName.Value;
                            await accountManager.Update(upAccount);
                        }


                        //更新角色
                        var listUserInRole = await this.Db.Queryable<SysUserRole>()
                                             .InnerJoin<SysRole>((UIR, R) => UIR.RoleId == R.RoleId)
                                             .Where((UIR, R) => UIR.UserId == pUser.Id && R.RoleType == u.UnitType).ToListAsync();

                        List<long> listOldRoleId = listUserInRole.Select(f => f.RoleId).ToList();
                        List<long> listNewRoleId = o.StrRoleIds.Split(',').Select(long.Parse).ToList();
                        List<long> listDelete = listOldRoleId.Except(listNewRoleId).ToList();
                        List<long> listAdd = listNewRoleId.Except(listOldRoleId).ToList();
                        if (listDelete.Count > 0)
                        {
                            var queriedObjects = listUserInRole.Where(f => listDelete.Contains(f.RoleId)).Select(f => f.Id).ToList();
                            object[] ids = queriedObjects.Cast<object>().ToArray();
                            await sysUserRoleManager.DeleteByIds(ids);
                        }
                        if (listAdd.Count > 0)
                        {
                            List<SysUserRole> listUserInRoleNew = new List<SysUserRole>();
                            foreach (int l in listAdd)
                            {
                                listUserInRoleNew.Add(new SysUserRole()
                                {
                                    UserId = pUser.Id,
                                    RoleId = l,
                                    Id = BaseDBConfig.GetYitterId()
                                });
                            }
                            if (listUserInRoleNew.Count > 0)
                            {
                                await sysUserRoleManager.Add(listUserInRoleNew);
                            }
                        }

                        //更新密码
                        if (!string.IsNullOrEmpty(o.Pwd) && upAccount != null)
                        {
                            upAccount.LoginPWD = o.Pwd;
                            await accountManager.Update(upAccount);

                            //添加操作日志
                            await userActionLogManager.Add(new BUserActionLog()
                            {
                                UserId = pUser.Id,
                                AccountName = o.AcctName,
                                UserIp = "",
                                Type = 3,
                                CreateTime = DateTime.Now,
                                Statuz = 1,
                                Id = BaseDBConfig.GetYitterId()
                            });
                        }

                        //更新部门
                        if (o.DepartmentIds != "-1")
                        {
                            if (!string.IsNullOrEmpty(o.DepartmentIds))
                            {
                                List<long> listNewDepartmentId = o.DepartmentIds.Split(',').Select(long.Parse).ToList();

                                List<long> listOldDepartmentId = await this.Db.Queryable<PUserInDepart>()
                                   .InnerJoin<PDepartment>((ud, d) => ud.DepartmentId == d.Id)
                                   .Where((ud, d) => d.UnitId == o.UnitId && ud.UserId == pUser.Id)
                                   .Select((ud, d) => ud.Id).ToListAsync();
                                List<long> listDepartmentDelete = listOldDepartmentId.Except(listNewDepartmentId).ToList();
                                List<long> listDepartmentAdd = listNewDepartmentId.Except(listOldDepartmentId).ToList();

                                if (listDepartmentDelete.Count > 0)
                                {
                                    object[] ids = listDepartmentDelete.Cast<object>().ToArray();
                                    await userInDepartManager.DeleteByIds(ids);
                                }
                                if (listDepartmentAdd.Count > 0)
                                {
                                    List<PUserInDepart> listPUserInDepartmentNew = new List<PUserInDepart>();
                                    foreach (int l in listDepartmentAdd)
                                    {
                                        listPUserInDepartmentNew.Add(new PUserInDepart()
                                        {
                                            UserId = pUser.Id,
                                            DepartmentId = l,
                                            Id = BaseDBConfig.GetYitterId()
                                        });
                                    }
                                    if (listPUserInDepartmentNew.Count > 0)
                                    {
                                        await userInDepartManager.Add(listPUserInDepartmentNew);
                                    }
                                }
                            }
                            else
                            {
                                List<long> listId = await this.Db.Queryable<PUserInDepart>()
                                    .InnerJoin<PDepartment>((ud, d) => ud.DepartmentId == d.Id)
                                    .Where((ud, d) => d.UnitId == o.UnitId && ud.UserId == pUser.Id)
                                    .Select((ud, d) => ud.Id).ToListAsync();
                                object[] objDel = listId.Cast<object>().ToArray();
                                await userInDepartManager.DeleteByIds(objDel);
                            }
                        }

                        r.flag = 1;
                        r.msg = "更新数据成功";
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(o.Pwd))
                        {
                            r.flag = 0;
                            r.msg = "密码不能为空";
                            return r;
                        }

                        //判断手机号码是否重复
                        SysUserExtension objUser = await this.Db.Queryable<SysUserExtension>().Where(f => f.Mobile == o.Mobile && f.Statuz > 0).FirstAsync();
                        if (objUser != null)
                        {
                            r.flag = 0;
                            r.msg = "新增数据失败。手机号码已经存在";
                            return r;
                        }

                        //新增
                        var listAccount = await this.Db.Queryable<SysUserInfo>().Where(f => f.LoginName == o.AcctName).ToListAsync();
                        if (listAccount.Count > 0)
                        {
                            SysUserInfo account = listAccount.FirstOrDefault();
                            if (account != null && account.Statuz == 0)
                            {
                                //更新
                                account.Statuz = 1;
                                await accountManager.Update(account);

                                SysUserExtension uOld = await this.Db.Queryable<SysUserExtension>().Where(f => f.Id == account.UserExtensionId).FirstAsync();
                                if (uOld != null)
                                {
                                    uOld.Statuz = 1;
                                    await base.Update(uOld);
                                }

                                r.flag = 1;
                                r.msg = "新增数据成功";
                            }
                            else
                            {
                                r.flag = 0;
                                r.msg = "新增数据失败，帐号已经存在。";
                            }

                        }
                        else
                        {

                            //新增用户
                            pUser.RegTime = DateTime.Now;
                            pUser.UserId = user.ID;
                            pUser.Statuz = 1;
                            pUser.UserType = 1;
                            pUser.Id = BaseDBConfig.GetYitterId();
                            long userId = await userManager.Add(pUser);

                            await accountManager.Add(new SysUserInfo()
                            {
                                UserExtensionId = userId,
                                LoginName = o.AcctName,
                                Mobile = o.Mobile,
                                //Email = o.Email,
                                NickName = o.NickName,
                                CreateId = user.ID,
                                CreateTime = DateTime.Now,
                                Statuz = 1,
                                LoginPWD = o.Pwd,
                                //IsUseRealName = o.IsUseRealName.Value,
                                Id = BaseDBConfig.GetYitterId()
                            });

                            long[] longArray = o.StrRoleIds.Split(',').Select(long.Parse).ToArray();

                            List<SysUserRole> listUserInRole = new List<SysUserRole>();
                            foreach (long l in longArray)
                            {
                                long roleIdValue = l == -10 ? 0 : l;
                                if (!listUserInRole.Exists(f => f.RoleId == roleIdValue))
                                {
                                    listUserInRole.Add(new SysUserRole()
                                    {
                                        Id = BaseDBConfig.GetYitterId(),
                                        UserId = userId,
                                        RoleId = roleIdValue,
                                    });
                                }
                            }
                            if (listUserInRole.Count > 0)
                            {
                                await sysUserRoleManager.Add(listUserInRole);
                            }

                            //判断是否添加部门
                            if (o.DepartmentIds != null && o.DepartmentIds != "" && o.DepartmentIds != "-1")
                            {
                                long[] longDepartmentId = o.DepartmentIds.Split(',').Select(long.Parse).ToArray();
                                List<PUserInDepart> listUserInDepart = new List<PUserInDepart>();
                                foreach (int l in longDepartmentId)
                                {
                                    listUserInDepart.Add(new PUserInDepart()
                                    {
                                        UserId = userId,
                                        DepartmentId = l,
                                        Id = BaseDBConfig.GetYitterId()
                                    });
                                }
                                if (listUserInDepart.Count > 0)
                                {
                                    await userInDepartManager.Add(listUserInDepart);
                                }
                            }

                            r.flag = 1;
                            r.msg = "新增数据成功";
                        }
                    }

                    unitOfWorkManage.CommitTran();
                }
                catch (Exception)
                {
                    unitOfWorkManage.RollbackTran();
                    throw;
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "当前单位状态为“未认证”或“未审核”或“已删除”，不能添加用户。";
            }
            return r;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="unitType"></param>
        /// <param name="unitId"></param>
        /// <param name="domainName"></param>
        /// <param name="unitPid"></param>
        /// <returns></returns>
        public async Task<WebSiteDataTable> GetWebSiteV1(int unitType, long unitId, string domainName, long unitPid)
        {
            WebSiteDataTable site = new WebSiteDataTable();
            BCustomerDomain domain = await this.Db.Queryable<BCustomerDomain>().Where(f => f.DomainName == domainName).FirstAsync();
            if (domain != null)
            {
                site.titleId = domain.Id;
                site.webTitle = domain.WebName;
                site.logo = domain.InsideLog;
                site.OutsideLog = domain.OutsideLog;
                site.logoId = 0;
                site.logoMemo = "";
                return site;
            }

            List<BWebSiteConfig> list = await this.Db.Queryable<BWebSiteConfig>().Where(f => f.ConfigType == 14).ToListAsync();

            //如果是超级管理员或企业
            if (unitType == 4 || unitType == 0)
            {
                BWebSiteConfig configLogo = list.Where(f => f.UnitId == 0 && f.ConfigCode == "Logo").FirstOrDefault();
                if (configLogo != null)
                {
                    site.logo = configLogo.ConfigValue;
                    site.OutsideLog = configLogo.ConfigValue;
                    site.logoId = configLogo.Id;
                    site.logoMemo = configLogo.Memo;
                }

                BWebSiteConfig configWebTitle = list.Where(f => f.UnitId == 0 && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                if (configWebTitle != null)
                {
                    site.webTitle = configWebTitle.ConfigValue;
                    site.titleId = configWebTitle.Id;
                    site.titleMemo = configWebTitle.Memo;
                }
                return site;
            }
            else
            {
                BWebSiteConfig configLogo = list.Where(f => f.UnitId == unitId && f.ConfigCode == "Logo").FirstOrDefault();
                if (configLogo != null)
                {
                    site.logo = configLogo.ConfigValue;
                    site.OutsideLog = configLogo.ConfigValue;
                    site.logoId = configLogo.Id;
                    site.logoMemo = configLogo.Memo;
                }

                BWebSiteConfig configWebTitle = list.Where(f => f.UnitId == unitId && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                if (configWebTitle != null)
                {
                    site.webTitle = configWebTitle.ConfigValue;
                    site.titleId = configWebTitle.Id;
                    site.titleMemo = configWebTitle.Memo;
                }

                if (configLogo == null)
                {
                    if (unitType == 1)
                    {
                        configLogo = list.Where(f => f.UnitId == 0 && f.ConfigCode == "Logo").FirstOrDefault();
                        if (configLogo != null)
                        {
                            site.logo = configLogo.ConfigValue;
                            site.OutsideLog = configLogo.ConfigValue;
                            site.logoId = configLogo.Id;
                            site.logoMemo = configLogo.Memo;
                        }
                    }
                    long cityUnitId = 0;
                    long countyUnitId = 0;
                    if (unitType == 2)
                    {
                        PUnit u = await this.Db.Queryable<PUnit>().Where(f => f.Id == unitId).FirstAsync();
                        if (u != null)
                        {
                            cityUnitId = u.PId;
                        }
                        configLogo = list.Where(f => f.UnitId == cityUnitId && f.ConfigCode == "Logo").FirstOrDefault();
                        if (configLogo != null)
                        {
                            site.logo = configLogo.ConfigValue;
                            site.OutsideLog = configLogo.ConfigValue;
                            site.logoId = configLogo.Id;
                            site.logoMemo = configLogo.Memo;
                        }
                        else
                        {
                            configLogo = list.Where(f => f.UnitId == 0 && f.ConfigCode == "Logo").FirstOrDefault();
                            if (configLogo != null)
                            {
                                site.logo = configLogo.ConfigValue;
                                site.OutsideLog = configLogo.ConfigValue;
                                site.logoId = configLogo.Id;
                                site.logoMemo = configLogo.Memo;
                            }
                        }
                    }

                    if (unitType == 3)
                    {
                        PUnit u = await this.Db.Queryable<PUnit>().Where(f => f.Id == unitId).FirstAsync();
                        if (u != null)
                        {
                            countyUnitId = u.PId;

                            configLogo = list.Where(f => f.UnitId == countyUnitId && f.ConfigCode == "Logo").FirstOrDefault();
                            if (configLogo != null)
                            {
                                site.logo = configLogo.ConfigValue;
                                site.OutsideLog = configLogo.ConfigValue;
                                site.logoId = configLogo.Id;
                                site.logoMemo = configLogo.Memo;
                            }
                            else
                            {
                                u = await this.Db.Queryable<PUnit>().Where(f => f.Id == countyUnitId).FirstAsync();
                                if (u != null)
                                {
                                    cityUnitId = u.PId;
                                }
                                configLogo = list.Where(f => f.UnitId == cityUnitId && f.ConfigCode == "Logo").FirstOrDefault();
                                if (configLogo != null)
                                {
                                    site.logo = configLogo.ConfigValue;
                                    site.OutsideLog = configLogo.ConfigValue;
                                    site.logoId = configLogo.Id;
                                    site.logoMemo = configLogo.Memo;
                                }
                                else
                                {
                                    configLogo = list.Where(f => f.UnitId == 0 && f.ConfigCode == "Logo").FirstOrDefault();
                                    if (configLogo != null)
                                    {
                                        site.logo = configLogo.ConfigValue;
                                        site.OutsideLog = configLogo.ConfigValue;
                                        site.logoId = configLogo.Id;
                                        site.logoMemo = configLogo.Memo;
                                    }
                                }
                            }
                        }
                    }

                }

                if (configWebTitle == null)
                {
                    if (unitType == 1)
                    {
                        configWebTitle = list.Where(f => f.UnitId == 0 && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                        if (configWebTitle != null)
                        {
                            site.webTitle = configWebTitle.ConfigValue;
                            site.titleId = configWebTitle.Id;
                            site.titleMemo = configWebTitle.Memo;
                        }
                    }
                    long cityUnitId = 0;
                    long countyUnitId = 0;
                    if (unitType == 2)
                    {
                        PUnit u = await this.Db.Queryable<PUnit>().Where(f => f.Id == unitId).FirstAsync();
                        if (u != null)
                        {
                            cityUnitId = u.PId;
                        }
                        configWebTitle = list.Where(f => f.UnitId == cityUnitId && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                        if (configWebTitle != null)
                        {
                            site.webTitle = configWebTitle.ConfigValue;
                            site.titleId = configWebTitle.Id;
                            site.titleMemo = configWebTitle.Memo;
                        }
                        else
                        {
                            configWebTitle = list.Where(f => f.UnitId == 0 && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                            if (configWebTitle != null)
                            {
                                site.webTitle = configWebTitle.ConfigValue;
                                site.titleId = configWebTitle.Id;
                                site.titleMemo = configWebTitle.Memo;
                            }
                        }
                    }

                    if (unitType == 3)
                    {
                        PUnit u = await this.Db.Queryable<PUnit>().Where(f => f.Id == unitId).FirstAsync();
                        if (u != null)
                        {
                            countyUnitId = u.PId;

                            configWebTitle = list.Where(f => f.UnitId == countyUnitId && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                            if (configWebTitle != null)
                            {
                                site.webTitle = configWebTitle.ConfigValue;
                                site.titleId = configWebTitle.Id;
                                site.titleMemo = configWebTitle.Memo;
                            }
                            else
                            {
                                u = await this.Db.Queryable<PUnit>().Where(f => f.Id == countyUnitId).FirstAsync();
                                if (u != null)
                                {
                                    cityUnitId = u.PId;
                                }
                                configWebTitle = list.Where(f => f.UnitId == cityUnitId && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                                if (configWebTitle != null)
                                {
                                    site.webTitle = configWebTitle.ConfigValue;
                                    site.titleId = configWebTitle.Id;
                                    site.titleMemo = configWebTitle.Memo;
                                }
                                else
                                {
                                    configWebTitle = list.Where(f => f.UnitId == 0 && (f.ConfigCode == "WebTitle" || f.ConfigCode == "webTitle")).FirstOrDefault();
                                    if (configWebTitle != null)
                                    {
                                        site.webTitle = configWebTitle.ConfigValue;
                                        site.titleId = configWebTitle.Id;
                                        site.titleMemo = configWebTitle.Memo;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return site;
        }

        /// <summary>
        /// 获取区县、市级单位信息
        /// </summary>
        /// <returns></returns>
        public async Task<Result<PUnitSetDto>> GetXfUnitPaged()
        {
            PUnitSetDto unit = await this.Db.Queryable<PUnit>().Where(f => f.Id == user.UnitId)
                .Select(f => new PUnitSetDto()
                {
                    Pid = f.PId,
                    UnitType = f.UnitType,
                    Name = f.Name,
                    OrganizationCode = f.OrganizationCode,

                }).FirstAsync();
            unit.ParentUnitName = "无";

            if (unit.UnitType != 1)
            {
                PUnit u = await this.Db.Queryable<PUnit>().Where(f => f.Id == unit.Pid).FirstAsync();
                if (u != null)
                {
                    unit.ParentUnitName = u.Name;
                }
            }
            return Result<PUnitSetDto>.Success("查询成功", unit, 1);
        }


        /// <summary>
        /// 设置区县、市级单位信息
        /// </summary>
        /// <returns></returns>
        public async Task<Result<string>> SetXfUnitInfo(PUnitSetDto o)
        {
            PUnit u = await this.Db.Queryable<PUnit>().Where(f => f.Id == user.UnitId).FirstAsync();
            if (u != null)
            {
                u.Name = o.Name;
                u.OrganizationCode = o.OrganizationCode;

                await base.Update(u);
            }
            return Result<string>.Success("保存成功");
        }

        //public async Task<PageModel<UnitXfDto>> GetXfUnitPaged(UnitXfParam param)
        //{
        //    string orderByFields = string.Empty;
        //    if (param.sortModel != null && param.sortModel.Count > 0)
        //    {
        //        orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
        //    }
        //    else
        //    {
        //        orderByFields = "Sort ASC";
        //    }
        //    RefAsync<int> totalCount = 0;
        //    PageModel<UnitXfDto> pageList = new PageModel<UnitXfDto>();
        //    this.Db.Queryable<PUnit>().Where(f => f.Statuz == 1)
        //        .WhereIF(param.UnitType != -10000, f => f.UnitType == param.UnitType)
        //        .WhereIF(param.UnitId != -10000, f => f.Id == param.UnitId)
        //        .WhereIF(!string.IsNullOrEmpty(param.Key), f => f.Name.Contains(param.Key))

        //        .Select(f => new UnitXfDto()
        //        {
        //            Id = f.Id,
        //            UnitType = f.UnitType,
        //            UnitTypeName = SqlFunc.IF(u.UnitType == 1).Return("市级").ElseIF(u.UnitType == 2).Return("区县").ElseIF(u.UnitType == 3).Return("单位").ElseIF(u.UnitType == 4).Return("企业").End(""),
        //            Name = f.Name,
        //            Brief = f.Brief,
        //            Statuz = f.Statuz,
        //            RegTime = f.RegTime
        //        });
        //}


        /// <summary>
        /// 校服管理平台单位企业注册
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result> XfUnitRegister(UnitRegisterModel o)
        {
            Result r = new Result();
           
            PUnit unit = await this.Db.Queryable<PUnit>().Where(f => f.Name == o.Name && f.Statuz > -1).FirstAsync();
            if(unit!= null)
            {
                r.flag = 0;
                r.msg = "单位名称已注册";
                return r;
            }
            if(o.UnitType != 3 && o.UnitType != 4)
            {
                r.flag = 0;
                r.msg = "单位类型错误";
                return r;
            }
            SysUserExtension objUser = await this.Db.Queryable<SysUserExtension>().Where(f => f.Mobile == o.Mobile && f.Statuz > 0).FirstAsync();
            if(objUser != null)
            {
                r.flag = 0;
                r.msg = "手机号码已经存在";
                return r;
            }
            try
            {
                unitOfWorkManage.BeginTran();
                int roleId = 40;
                string code = $"G{DateTime.Now.ToString("yyyyMMddmmss")}";
                if(o.UnitType == 3)
                {
                    code = $"X{DateTime.Now.ToString("yyyyMMddmmss")}";
                    roleId = 30;
                }

                //创建单位
                long unitId = await base.Add(new PUnit()
                {
                    Id = BaseDBConfig.GetYitterId(),
                    AreaId = 0,
                    PId = 0,
                    UnitType = o.UnitType,
                    IndustryId = 1,
                    Code = code,
                    Name = o.Name,
                    Legal = "",
                    VipGrade = 0,
                    UserId = 1,
                    EmployeeNum = 0,
                    Address = "",
                    OrganizationCode = "",
                    Mobile = o.Mobile,
                    Tel = "",
                    Statuz = 2
                });

                //创建待认证信息
                await supplierSchoolAuditManager.Add(new PSupplierSchoolAudit()
                {
                    UnitId = unitId,
                    Name = o.Name,
                    SocialCreditCode = "",
                    ProvinceId = 0,
                    CityId = 0,
                    CountyId = 0,
                    Address = "",
                    Url = "",
                    Introduction = "",
                    Tel = o.Mobile,
                    Nature = 0,
                    Period = "",
                    SchoolStage = 0,
                    BeLongUnit = false,
                    AuthStatuz = -1,
                    Reason = "",
                    IsCurrent = true
                });

                if(o.UnitType == 3)
                {
                    //创建单位扩展信息
                    await schoolExtensionManager.Add(new PSchoolExtension()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UnitId = unitId,
                        ClassNum = 0,
                        StudentNum = 0,
                        TeacherNum = 0,
                        FloorArea = 0,
                        BuildArea = 0,
                        SchoolStage = 0,
                        Memo = "",
                        SchoolAdmin = "",
                        AdminMobile = "",
                        HeadMaster = "",
                        MsaterMobile = ""
                    });
                }

                SysUserInfo account = await this.Db.Queryable<SysUserInfo>().Where(f => f.LoginName == o.Mobile).FirstAsync();
                if (account != null)
                {
                    account.LoginPWD = o.PassWord;
                    await accountManager.Update(account);

                    SysUserExtension currentUser = await this.Db.Queryable<SysUserExtension>().Where(f => f.Id == account.UserExtensionId).FirstAsync();
                    if(currentUser != null)
                    {
                        currentUser.UnitId = unitId;
                        currentUser.UserType = 1;

                        await userManager.Update(currentUser);
                    }

                    //判断角色是否存在
                   var listRole = await this.Db.Queryable<SysUserRole>().Where(f => f.UserId == account.UserExtensionId && f.RoleId == roleId).ToListAsync();
                    if(listRole.Count == 0)
                    {
                        await sysUserRoleManager.Add(new SysUserRole()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            UserId = account.UserExtensionId,
                            RoleId = roleId
                        });
                    }
                }
                else
                {
                    // 添加用户
                    long userId = await userManager.Add(new SysUserExtension()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UnitId = unitId,
                        Name = o.Mobile,
                        Mobile = o.Mobile,
                        Tel = "",
                        Sex = "0",
                        Birthday = DateTime.Now,
                        UserId = 1,
                        Statuz = 1
                    });

                    //添加账号
                    await accountManager.Add(new SysUserInfo()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UserExtensionId = userId,
                        LoginName = o.Mobile,
                        Mobile = o.Mobile,
                        LoginPWD = o.PassWord,
                        CreateId = 0,
                        Statuz = 1,
                        //IsUseRealName = false
                    });

                    //创建用户角色
                    await sysUserRoleManager.Add(new SysUserRole()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        UserId = userId,
                        RoleId = roleId
                    });
                }

                unitOfWorkManage.CommitTran();
            }
            catch (Exception)
            {
                unitOfWorkManage.RollbackTran();
                throw;
            }

            r.flag = 1;
            r.msg = "注册成功";
            return r;
        }


        /// <summary>
        /// 导入单位信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<Result> SaveSchoolData(List<SchoolImportDto> list)
        {
            Result r = new Result();
            if (list.Any())
            {
                string errorMsg = "";
                List<PUnit> listUnitAdd = new List<PUnit>();
                List<PSchoolExtension> listSchoolExtensionAdd = new List<PSchoolExtension>();
                //判断导入单位编码是否重复
                var listCode = list.GroupBy(f => new
                {
                    f.Code
                })
                .Select(group => new SchoolImportDto
                {
                    Code = group.Key.Code,
                    Total = group.Count()
                }).ToList();

                if (listCode.Exists(f => f.Total > 1))
                {
                    var listTip = listCode.Where(f => f.Total > 1).Select(f => "【单位编码：" + f.Code + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }

                //判断单位名称是否有重复
                var listName = list.GroupBy(f => new
                {
                    f.Name,
                })
                .Select(group => new SchoolImportDto
                {
                    Name = group.Key.Name,
                    Total = group.Count()
                }).ToList();

                if (listName.Exists(f => f.Total > 1))
                {
                    var listTip = listName.Where(f => f.Total > 1).Select(f => "【单位名称：" + f.Name + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }

                //获取所有单位信息
                var listUnit = await this.Db.Queryable<PUnit>().Where(f => f.IsDeleted == false).ToListAsync();
                var listDic = await this.Db.Queryable<BDictionary>().Where(f => f.TypeCode.Equals("1101") && f.IsDeleted == false).ToListAsync();

                int index = 2;
                foreach (SchoolImportDto stu in list)
                {
                    if (string.IsNullOrEmpty(stu.Code))
                    {
                        errorMsg += $"第{index}行单位编码不能为空!\n";
                    }
                    else
                    {
                        if (listUnit.Exists(f => f.Code.Equals(stu.Code)))
                        {
                            errorMsg += $"第{index}行单位编码已经存在!\n";
                        }
                    }
                    if (string.IsNullOrEmpty(stu.Name))
                    {
                        errorMsg += $"第{index}行单位名称不能为空!\n";
                    }
                    else
                    {
                        if (listUnit.Exists(f => f.Name.Equals(stu.Name)))
                        {
                            errorMsg += $"第{index}行单位名称已经存在!\n";
                        }
                    }
                    if (string.IsNullOrEmpty(stu.Brief))
                    {
                        errorMsg += $"第{index}行单位简称不能为空!\n";
                    }

                    PUnit unit = new PUnit();
                    unit.Id = BaseDBConfig.GetYitterId();
                    unit.Code = stu.Code;
                    unit.PId = user.UnitId;
                    unit.IndustryId = 1;
                    unit.UnitType = UnitTypeEnum.School.ToEnumInt();
                    unit.Name = stu.Name;
                    unit.Brief = stu.Brief;
                    unit.Legal = "";
                    unit.Address = "";
                    unit.ZipCode = "";
                    unit.Url = "";
                    unit.OrganizationCode = "";
                    unit.Tel = "";
                    unit.Email = "";
                    unit.EmployeeNum = 0;
                    unit.Introduction = "";
                    unit.Statuz = 1;
                    unit.AuthStatuz = 0;
                    unit.StreetTown = 0;

                    int SchoolStage = 0;
                    if (!string.IsNullOrEmpty(stu.StrSchoolNature))
                    {
                        var obj = listDic.Where(f => f.DicName.Equals(stu.StrSchoolNature)).FirstOrDefault();
                        if (obj != null)
                        {
                            int.TryParse(obj.DicValue, out SchoolStage);
                        }
                    }
                    PSchoolExtension schoolExtension = new PSchoolExtension();
                    schoolExtension.Id = BaseDBConfig.GetYitterId();
                    schoolExtension.UnitId = unit.Id;
                    schoolExtension.ClassNum = stu.ClassNum;
                    schoolExtension.StudentNum = stu.StudentNum;
                    schoolExtension.TeacherNum = stu.TeacherNum;
                    schoolExtension.FloorArea = stu.FloorArea;
                    schoolExtension.BuildArea = stu.BuildArea;
                    schoolExtension.SchoolStage = SchoolStage;
                    schoolExtension.Period = "";
                    schoolExtension.Memo = "";
                    schoolExtension.SchoolAdmin = "";
                    schoolExtension.AdminMobile = "";
                    schoolExtension.HeadMaster = "";
                    schoolExtension.MsaterMobile = "";
                    schoolExtension.IsLock = 0;
                    schoolExtension.SchoolGuid = "";
                    schoolExtension.SchoolNature = 1;

                    listUnitAdd.Add(unit);
                    listSchoolExtensionAdd.Add(schoolExtension);

                    index++;
                }


                if (string.IsNullOrEmpty(errorMsg))
                {
                    await this.Db.Insertable<PUnit>(listUnitAdd).ExecuteCommandAsync();

                    await this.Db.Insertable<PSchoolExtension>(listSchoolExtensionAdd).ExecuteCommandAsync();

                    r.flag = 1;
                    r.msg = "导入成功!";

                }
                else
                {
                    r.flag = 0;
                    r.msg = errorMsg;
                }
            }
            else
            {
                r.flag = 0;
                r.msg = " 未找到导入的数据";
            }

            return r;
        }

        /// <summary>
        /// 导入区县、市级、企业信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<Result> SaveCountyCityCompanyData(List<CountyCityCompanyImportDto> list)
        {
            Result r = new Result();
            if (list.Any())
            {
                string errorMsg = "";
                List<PUnit> listUnitAdd = new List<PUnit>();
                //判断导入单位编码是否重复
                var listCode = list.GroupBy(f => new
                {
                    f.Code
                })
                .Select(group => new CountyCityCompanyImportDto
                {
                    Code = group.Key.Code,
                    Total = group.Count()
                }).ToList();

                if (listCode.Exists(f => f.Total > 1))
                {
                    var listTip = listCode.Where(f => f.Total > 1).Select(f => "【单位编码：" + f.Code + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }

                //判断单位名称是否有重复
                var listName = list.GroupBy(f => new
                {
                    f.Name,
                })
                .Select(group => new CountyCityCompanyImportDto
                {
                    Name = group.Key.Name,
                    Total = group.Count()
                }).ToList();

                if (listName.Exists(f => f.Total > 1))
                {
                    var listTip = listName.Where(f => f.Total > 1).Select(f => "【单位名称：" + f.Name + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }

                //获取所有单位信息
                var listUnit = await this.Db.Queryable<PUnit>().Where(f => f.IsDeleted == false).ToListAsync();

                long pid = user.UnitId;
                int unitType = UnitTypeEnum.County.ToEnumInt();
                if (user.IsSystemUser)
                {
                    pid = 0;
                    unitType = UnitTypeEnum.Company.ToEnumInt();
                }
                

                int index = 2;
                foreach (CountyCityCompanyImportDto stu in list)
                {
                    if (string.IsNullOrEmpty(stu.Code))
                    {
                        errorMsg += $"第{index}行单位编号不能为空!\n";
                    }
                    else
                    {
                        if (listUnit.Exists(f => f.Code.Equals(stu.Code)))
                        {
                            errorMsg += $"第{index}行单位编号已经存在!\n";
                        }
                    }
                    if (string.IsNullOrEmpty(stu.Name))
                    {
                        errorMsg += $"第{index}行单位名称不能为空!\n";
                    }
                    else
                    {
                        if (listUnit.Exists(f => f.Name.Equals(stu.Name)))
                        {
                            errorMsg += $"第{index}行单位名称已经存在!\n";
                        }
                    }
                    if (string.IsNullOrEmpty(stu.Brief))
                    {
                        errorMsg += $"第{index}行单位简称不能为空!\n";
                    }

                    PUnit unit = new PUnit();
                    unit.Id = BaseDBConfig.GetYitterId();
                    unit.PId = pid;
                    unit.IndustryId = 1;
                    unit.Code = stu.Code;
                    unit.UnitType = unitType;
                    unit.Name = stu.Name;
                    unit.Brief = stu.Brief;
                    unit.Legal = stu.Legal;
                    unit.Address = stu.Address;
                    unit.ZipCode = stu.ZipCode;
                    unit.Url = stu.Url;
                    unit.OrganizationCode = stu.OrganizationCode;
                    unit.Tel = stu.Tel;
                    unit.Email = stu.Email;
                    unit.EmployeeNum = stu.EmployeeNum;
                    unit.Introduction = stu.Introduction;
                    unit.Statuz = 1;
                    unit.AuthStatuz = 0;
                    unit.StreetTown = 0;
                    
                    listUnitAdd.Add(unit);
                    index++;
                }


                if (string.IsNullOrEmpty(errorMsg))
                {
                    await this.Db.Insertable<PUnit>(listUnitAdd).ExecuteCommandAsync();
                    r.flag = 1;
                    r.msg = "导入成功!";

                }
                else
                {
                    r.flag = 0;
                    r.msg = errorMsg;
                }
            }
            else
            {
                r.flag = 0;
                r.msg = " 未找到导入的数据";
            }

            return r;
        }

        /// <summary>
        /// 导入用户账号信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<Result> SaveImportUserData(List<UserImportDto> list)
        {
            Result r = new Result();
            if (list.Any())
            {
                string errorMsg = "";
                List<VUserDetail> listUserAdd = new List<VUserDetail>();

                //判断账号是否重复
                var listAccount = list.GroupBy(f => new
                {
                    f.AccoutName
                })
                .Select(group => new UserImportDto
                {
                    AccoutName = group.Key.AccoutName,
                    Total = group.Count()
                }).ToList();

                if (listAccount.Exists(f => f.Total > 1))
                {
                    var listTip = listAccount.Where(f => f.Total > 1).Select(f => "【登录账号：" + f.AccoutName + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }

                //判断手机号码是否有重复
                var listPhone = list.GroupBy(f => new
                {
                    f.Mobile,
                })
                .Select(group => new UserImportDto
                {
                    Mobile = group.Key.Mobile,
                    Total = group.Count()
                }).ToList();

                if (listPhone.Exists(f => f.Total > 1))
                {
                    var listTip = listPhone.Where(f => f.Total > 1).Select(f => "【手机电话：" + f.Mobile + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }

                //获取所有单位信息
                var listUnit = await this.Db.Queryable<PUnit>().Where(f => f.IsDeleted == false && f.Statuz > 0).ToListAsync();
                //获取所有用户登录账号信息
                var listUser = await this.Db.Queryable<SysUserInfo>().Where(f => f.IsDeleted == false && f.Statuz > 0).ToListAsync();
                //获取所有用户信息
                var listUserExtension = await this.Db.Queryable<SysUserExtension>().Where(f => f.IsDeleted == false && f.Statuz > 0 && !string.IsNullOrEmpty(f.Mobile)).ToListAsync();
                //获取所有用户对应角色信息
                var listUserInRole = await this.Db.Queryable<SysUserRole>().Where(f => f.IsDeleted == false).ToListAsync();

                #region 判断密码是否需要强化验证
                //判断密码是否需要强化验证
                int PwdVerificationWay = 0;
                string PwdVerificationMsg = "密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种";
                List<BWebSiteConfig> listJs = await this.Db.Queryable<BWebSiteConfig>().Where(f => f.IsDeleted == false && f.ConfigType == 10 && (f.ConfigCode.Equals("PwdVerificationWay") || f.ConfigCode.Equals("PwdVerificationMsg"))).ToListAsync();
                if (listJs.Count > 0)
                {
                    var objConfig = listJs.Where(f => f.ConfigCode.Equals("PwdVerificationWay")).FirstOrDefault();
                    if (objConfig != null)
                    {
                        PwdVerificationWay = int.Parse(objConfig.ConfigValue);
                    }
                    objConfig = listJs.Where(f => f.ConfigCode.Equals("PwdVerificationMsg")).FirstOrDefault();
                    if (objConfig != null)
                    {
                        PwdVerificationMsg = objConfig.ConfigValue;
                    }

                }
                #endregion

                int index = 2;
                string roleId = string.Empty;

                if (user.IsSystemUser)
                {
                    roleId = RoleTypes.CompanyAdmin.ToEnumInt().ToString();
                }
                else if (user.Roles.Contains(RoleTypes.CoutyAdmin))
                {
                    roleId = RoleTypes.SchoolAdmin.ToEnumInt().ToString();
                }
                else if (user.Roles.Contains(RoleTypes.CityAdmin))
                {
                    roleId = RoleTypes.CoutyAdmin.ToEnumInt().ToString();
                }

                foreach (UserImportDto stu in list)
                {
                    if (string.IsNullOrEmpty(stu.Name))
                    {
                        errorMsg += $"第{index}行姓名不能为空!\n";
                    }

                    if (string.IsNullOrEmpty(stu.Mobile))
                    {
                        errorMsg += $"第{index}行手机电话不能为空!\n";
                    }
                    else
                    {
                        //验证手机号码输入是否正确
                        if (!StringHelper.IsPhoneNo(stu.Mobile))
                        {
                            errorMsg += $"第{index}行手机号码输入有误!\n";
                        }
                        if (listUserExtension.Exists(f => f.Mobile.Equals(stu.Mobile)))
                        {
                            errorMsg += $"第{index}行手机电话已经存在!\n";
                        }
                    }
                    if (string.IsNullOrEmpty(stu.AccoutName))
                    {
                        errorMsg += $"第{index}行登录账号不能为空!\n";
                    }
                    else
                    {
                        if (listUser.Exists(f => f.LoginName.Equals(stu.AccoutName)))
                        {
                            errorMsg += $"第{index}行登录账号已经存在!\n";
                        }
                    }
                    if (string.IsNullOrEmpty(stu.Pwd))
                    {
                        errorMsg += $"第{index}行密码不能为空!\n";
                    }
                    else
                    {
                        if (PwdVerificationWay != 0)
                        {
                            Regex objReg = new Regex(@"^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$");
                            if (!objReg.IsMatch(stu.Pwd))
                            {
                                errorMsg += $"第{index}行密码“{PwdVerificationMsg}”!\n";
                            }
                        }
                        //密码加密
                        stu.Pwd = MD5Helper.MD5Encrypt32(stu.Pwd).ToLower();
                    }
                    if (string.IsNullOrEmpty(stu.Code))
                    {
                        errorMsg += $"第{index}行单位编号不能为空!\n";
                    }
                    if (string.IsNullOrEmpty(stu.UnitName))
                    {
                        errorMsg += $"第{index}行单位名称不能为空!\n";
                    }
                    if (!string.IsNullOrEmpty(stu.Sex))
                    {
                        if (!stu.Sex.Equals("男") && !stu.Sex.Equals("女"))
                        {
                            errorMsg += $"第{index}行性别填写有误,只能填写男或女!\n";
                        }
                    }
                    if (!string.IsNullOrEmpty(stu.Qq))
                    {
                        string regex = "[1-9][0-9]{4,14}";
                        if (!Regex.IsMatch(stu.Qq, regex))
                        {
                            errorMsg += $"第{index}行QQ号码输入有误!\n";
                        }
                    }

                    if(!string.IsNullOrEmpty(stu.Code) && !string.IsNullOrEmpty(stu.UnitName))
                    {
                        var objUnit = listUnit.Where(f => f.Code.Equals(stu.Code) && f.Name.Equals(stu.UnitName)).FirstOrDefault();
                        if(objUnit == null)
                        {
                            errorMsg += $"第{index}行单位编号或单位名称不存在或已删除!\n";
                        }
                        else
                        {
                            listUserAdd.Add(new VUserDetail()
                            {
                                Name = stu.Name,
                                Mobile = stu.Mobile,
                                Sex = stu.Sex,
                                Qq = stu.Qq,
                                AcctName = stu.AccoutName,
                                Pwd = stu.Pwd,
                                IdNumber = "",
                                Address = "",
                                ZipCode = "",
                                Tel = "",
                                Memo = "",
                                NickName = stu.Name,
                                RegTime = DateTime.Now,
                                CreateId = user.ID,
                                Statuz = 1,
                                UnitId = objUnit.Id,
                                UnitName = objUnit.Name,
                                StrRoleIds = roleId
                            });
                        }
                    }

                    index++;
                }


                if (string.IsNullOrEmpty(errorMsg))
                { 
                    r.flag = 1;
                    r.msg = "导入成功!";
                    foreach(VUserDetail u in listUserAdd)
                    {
                        await InsertUpdateUser(u);
                    }
                }
                else
                {
                    r.flag = 0;
                    r.msg = errorMsg;
                }
            }
            else
            {
                r.flag = 0;
                r.msg = " 未找到导入的数据";
            }

            return r;
        }


        /// <summary>
        /// 根据城市Id区县信息
        /// </summary>
        /// <param name="cityId">城市Id，传0取默认单位Id</param>
        /// <returns></returns>
        public async Task<List<VCountyUnit>> GetAreaListByCityId(long cityId)
        {
            List<VCountyUnit> list = new List<VCountyUnit>();

            list = await this.Db.Queryable<PUnit>()
                .InnerJoin<BArea>((U, A) => U.AreaId == A.Id)
                .InnerJoin<BArea>((U, A, B) => A.Pid == B.Id)
                .InnerJoin<BArea>((U, A, B, C) => B.Pid == C.Id && U.Statuz > 0)
                .Where((U, A, B, C) => U.UnitType == 2)
                .WhereIF(cityId > 0, (U, A, B, C) => B.Id == cityId)
                .WhereIF(cityId == 0, (U, A, B, C) => U.PId == user.UnitId)
                .Select((U, A, B, C) => new VCountyUnit()
                {
                    UnitId = U.Id,
                    AreaId = U.AreaId,
                    UnitName = U.Name,
                    UnitType = U.UnitType,
                    CountyName = A.Name,
                    AreaPid = A.Pid,
                    CountyId = U.PId,
                    CityId = B.Id,
                    CityName = B.Name,
                    ProvinceId = B.Pid,
                    ProvinceName = C.Name,
                    Statuz = U.Statuz,
                    Sort = U.Sort,
                    AreaSort = A.Sort
                }).ToListAsync();
            return list;
        }

        /// <summary>
        /// 获取本单位单位信息
        /// </summary>
        /// <returns></returns>
        public async Task<Result> GetSchoolUnitInfo()
        {
            Result r = new Result();
            SchoolUnitInfoModel unit = await this.Db.Queryable<PUnit>()
                .LeftJoin<PSchoolExtension>((U, SE) => U.Id == SE.UnitId)
                .LeftJoin<BDictionary>((U, SE, D) => SE.SchoolStage.ToString() == D.DicValue && D.TypeCode == "1101")
                .Where((U, SE, D) => U.Id == user.UnitId)
                .Select((U, SE, D) => new SchoolUnitInfoModel
                {
                    Id = SE.Id,
                    UnitId = U.Id,
                    Name = U.Name,
                    Code = U.Code,
                    AreaId = U.AreaId,
                    SchoolNature = SqlFunc.IsNull(SE.SchoolNature,0),
                    SchoolStageName = SqlFunc.IsNull(D.DicName,""),
                    SchoolStage = SE.SchoolStage,
                    OrganizationCode = U.OrganizationCode,
                    StreetTown = U.StreetTown,
                    TownName = U.TownName,
                    Address = U.Address,
                    Url = U.Url,
                    Introduction = U.Introduction,
                    ClassNum = SE.ClassNum,
                    StudentNum = SE.StudentNum,
                    TeacherNum = SE.TeacherNum,
                    UnderTeacherNum = SE.UnderTeacherNum,
                    FloorArea = SE.FloorArea.Value,
                    BuildArea = SE.BuildArea.Value,
                }).FirstAsync();
            List<BArea> listTown = new List<BArea>();
            unit.ProvinceCityCountyName = await GetAreaName(unit.AreaId);
            if(unit.AreaId != 0)
            {
                listTown = await this.Db.Queryable<BArea>().Where(f => f.Pid == unit.AreaId && f.IsDeleted == false).ToListAsync();
            }
            
            r.flag = 1;
            r.msg = "查询成功";
            r.data.rows = unit;
            r.data.other = new { listTown = listTown };
            return r;
        }

        /// <summary>
        /// 保存学校信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> SaveSchoolUnitInfo(SchoolUnitInfoModel o)
        {
            if (!user.Roles.Contains(RoleTypes.SchoolAdmin))
            {
                return Result<string>.Fail("只有管理员才能修改单位信息");
            }
            var objUnit = await this.Db.Queryable<PUnit>().Where(f => f.Id == user.UnitId).FirstAsync();
            if (objUnit != null)
            {
                objUnit.Code = o.Code;
                objUnit.OrganizationCode = o.OrganizationCode;
                objUnit.StreetTown = o.StreetTown;
                objUnit.TownName = o.TownName;
                objUnit.Address = o.Address;
                objUnit.Url = o.Url;
                objUnit.Introduction = o.Introduction;
                await this.Db.Updateable<PUnit>(objUnit).ExecuteCommandAsync();
            }

            var objSchoolExtension = await this.Db.Queryable<PSchoolExtension>().Where(f => f.UnitId == user.UnitId).FirstAsync();
            if (objSchoolExtension != null)
            {
                objSchoolExtension.ClassNum = o.ClassNum;
                objSchoolExtension.StudentNum = o.StudentNum;
                objSchoolExtension.TeacherNum = o.TeacherNum;
                objSchoolExtension.UnderTeacherNum = o.UnderTeacherNum;
                objSchoolExtension.FloorArea = o.FloorArea;
                objSchoolExtension.BuildArea = o.BuildArea;
                await this.Db.Updateable<PSchoolExtension>(objSchoolExtension).ExecuteCommandAsync();
            }
            else
            {
                PSchoolExtension schoolExtension = new PSchoolExtension();
                schoolExtension.Id = BaseDBConfig.GetYitterId();
                schoolExtension.UnitId = user.UnitId;
                schoolExtension.ClassNum = o.ClassNum;
                schoolExtension.StudentNum = o.StudentNum;
                schoolExtension.TeacherNum = o.TeacherNum;
                schoolExtension.UnderTeacherNum = o.UnderTeacherNum;
                schoolExtension.FloorArea = o.FloorArea;
                schoolExtension.BuildArea = o.BuildArea;
                schoolExtension.SchoolStage = 0;
                schoolExtension.Period = "";
                schoolExtension.Memo = "";
                schoolExtension.SchoolAdmin = "";
                schoolExtension.AdminMobile = "";
                schoolExtension.HeadMaster = "";
                schoolExtension.MsaterMobile = "";
                schoolExtension.IsLock = 0;
                schoolExtension.SchoolGuid = "";
                schoolExtension.SchoolNature = 1;
                await this.Db.Insertable<PSchoolExtension>(schoolExtension).ExecuteCommandAsync();
            }

            return Result<string>.Success("保存成功");
        }

        /// <summary>
        /// 保存下属单位学校信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<Result> SaveSchoolViewData(List<SchoolImportDto> list)
        {
            Result r = new Result();
            if (list.Any())
            {
                string errorMsg = "";
                List<PUnit> listUnitAdd = new List<PUnit>();
                List<PSchoolExtension> listSchoolExtensionAdd = new List<PSchoolExtension>();
               

                //判断单位名称是否有重复
                var listName = list.GroupBy(f => new
                {
                    f.Name,
                })
                .Select(group => new SchoolImportDto
                {
                    Name = group.Key.Name,
                    Total = group.Count()
                }).ToList();

                if (listName.Exists(f => f.Total > 1))
                {
                    var listTip = listName.Where(f => f.Total > 1).Select(f => "【单位名称：" + f.Name + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }

                //获取所有单位信息
                var listUnit = await this.Db.Queryable<PUnit>().Where(f => f.IsDeleted == false).ToListAsync();
                var listDic = await this.Db.Queryable<BDictionary>().Where(f => f.TypeCode.Equals("1101") && f.IsDeleted == false).ToListAsync();

                int index = 2;
                foreach (SchoolImportDto stu in list)
                {
                    if (!string.IsNullOrEmpty(stu.Code))
                    {
                        if (listUnit.Exists(f => f.Code.Equals(stu.Code)))
                        {
                            errorMsg += $"第{index}行单位代码已经存在!\n";
                        }
                    }
                   
                    if (string.IsNullOrEmpty(stu.Name))
                    {
                        errorMsg += $"第{index}行单位名称不能为空!\n";
                    }
                    else
                    {
                        if (listUnit.Exists(f => f.Name.Equals(stu.Name)))
                        {
                            errorMsg += $"第{index}行单位名称已经存在!\n";
                        }
                    }
                    if (string.IsNullOrEmpty(stu.StrSchoolNature))
                    {
                        errorMsg += $"第{index}行单位属性不能为空!\n";
                    }
                    if (string.IsNullOrEmpty(stu.StrSchoolStage))
                    {
                        errorMsg += $"第{index}行单位性质不能为空!\n";
                    }
                    PUnit unit = new PUnit();
                    unit.Id = BaseDBConfig.GetYitterId();
                    unit.Code = stu.Code;
                    unit.PId = user.UnitId;
                    unit.IndustryId = 1;
                    unit.UnitType = UnitTypeEnum.School.ToEnumInt();
                    unit.Name = stu.Name;
                    unit.Brief = "";
                    unit.Legal = "";
                    unit.Address = stu.Address;
                    unit.ZipCode = "";
                    unit.Url = "";
                    unit.OrganizationCode = stu.OrganizationCode;
                    unit.Tel = "";
                    unit.Email = "";
                    unit.EmployeeNum = 0;
                    unit.Introduction = stu.Introduction;
                    unit.Statuz = 1;
                    unit.AuthStatuz = 0;
                    unit.StreetTown = 0;
                    unit.AreaId = user.AreaId;

                    int SchoolStage = 0;
                    int SchoolNature = 1;
                    if (!string.IsNullOrEmpty(stu.StrSchoolStage))
                    {
                        var obj = listDic.Where(f => f.DicName.Equals(stu.StrSchoolStage)).FirstOrDefault();
                        if (obj != null)
                        {
                            int.TryParse(obj.DicValue, out SchoolStage);
                        }
                    }

                    //单位性质（1：公办校，2：民办校，3：其他）
                    if (!string.IsNullOrEmpty(stu.StrSchoolNature))
                    {
                        if (stu.StrSchoolNature.Equals("民办校"))
                        {
                            SchoolNature = 2;
                        }
                        else if (stu.StrSchoolNature.Equals("其他"))
                        {
                            SchoolNature = 3;
                        }
                    }

                    string strPeriod = await GetPeriodBySchoolStage(SchoolStage);
                    PSchoolExtension schoolExtension = new PSchoolExtension();
                    schoolExtension.Id = BaseDBConfig.GetYitterId();
                    schoolExtension.UnitId = unit.Id;
                    schoolExtension.ClassNum = 0;
                    schoolExtension.StudentNum = 0;
                    schoolExtension.TeacherNum = 0;
                    schoolExtension.UnderTeacherNum = 0;
                    schoolExtension.FloorArea = 0;
                    schoolExtension.BuildArea = 0;
                    schoolExtension.SchoolStage = SchoolStage;
                    schoolExtension.Period = strPeriod;
                    schoolExtension.Memo = "";
                    schoolExtension.SchoolAdmin = "";
                    schoolExtension.AdminMobile = "";
                    schoolExtension.HeadMaster = "";
                    schoolExtension.MsaterMobile = "";
                    schoolExtension.IsLock = 0;
                    schoolExtension.SchoolGuid = "";
                    schoolExtension.SchoolNature = SchoolNature;

                    listUnitAdd.Add(unit);
                    listSchoolExtensionAdd.Add(schoolExtension);

                    index++;
                }


                if (string.IsNullOrEmpty(errorMsg))
                {
                    await this.Db.Insertable<PUnit>(listUnitAdd).ExecuteCommandAsync();

                    await this.Db.Insertable<PSchoolExtension>(listSchoolExtensionAdd).ExecuteCommandAsync();

                    r.flag = 1;
                    r.msg = "导入成功!";

                }
                else
                {
                    r.flag = 0;
                    r.msg = errorMsg;
                }
            }
            else
            {
                r.flag = 0;
                r.msg = " 未找到导入的数据";
            }

            return r;
        }

        #region 私有方法
        /// <summary>
        /// 根据区域Id获取省市区信息
        /// </summary>
        /// <param name="areaId">区Id</param>
        /// <returns></returns>
        public async Task<string> GetAreaName(long areaId)
        {
            List<string> list = new List<string>();
            string areaName = string.Empty;
            var objCounty = await this.Db.Queryable<BArea>().Where(f => f.Id == areaId).FirstAsync();
            if (objCounty != null)
            {
                list.Add(objCounty.Name);

                var objCity = await this.Db.Queryable<BArea>().Where(f => f.Id == objCounty.Pid).FirstAsync();
                if (objCity != null)
                {
                    list.Add(objCity.Name);

                    var objProvince = await this.Db.Queryable<BArea>().Where(f => f.Id == objCity.Pid).FirstAsync();
                    if (objProvince != null)
                    {
                        list.Add(objProvince.Name);
                    }
                }
            }
            if (list.Count > 0)
            {
                list.Reverse();
                areaName = string.Join(",", list);
            }
            return areaName;
        }

        /// <summary>
        /// 根据单位属性获取单位学段信息
        /// </summary>
        /// <param name="schoolStage"></param>
        /// <returns></returns>
        public async Task<string> GetPeriodBySchoolStage(int schoolStage)
        {
            string strPeriod = string.Empty;
            if (schoolStage == 3)
            {
                strPeriod = "100010,100020";
            }
            else if (schoolStage == 5)
            {
                strPeriod = "100050";
            }
            else if (schoolStage == 7)
            {
                strPeriod = "100010,100020,100030";
            }
            else
            {
                var listPeriod = await this.Db.Queryable<BDictionary>().Where(f => f.IsDeleted == false && f.Statuz == 1 && f.TypeCode == "100000").ToListAsync();
                BDictionary obj = await this.Db.Queryable<BDictionary>().Where(f => f.IsDeleted == false && f.Statuz == 1 && f.TypeCode == "1101" && f.DicValue == schoolStage.ToString()).FirstAsync();
                if (obj != null)
                {
                    string strSchoolStage = obj.DicName;
                    var objPeriod = listPeriod.Where(f => f.DicName == strSchoolStage).FirstOrDefault();
                    if (objPeriod != null)
                    {
                        strPeriod = objPeriod.DicValue;
                    }
                }
            }
            
            return strPeriod;
        }
        #endregion

    }
}

