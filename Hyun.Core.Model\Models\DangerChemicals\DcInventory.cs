namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///盘点记录
    ///</summary>
    [SugarTable("dc_Inventory","盘点记录")]
    public class DcInventory : BaseEntity
    {

          public DcInventory()
          {

          }

           /// <summary>
           ///单位物品库Id
          /// </summary>
          public long SchoolMaterialId { get; set; }

           /// <summary>
           ///盘点时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? InventoryDate { get; set; }

           /// <summary>
           ///盘点数量
          /// </summary>
          [SugarColumn(IsNullable = true,ColumnDataType = "money")]
          public decimal? InventoryNum { get; set; }

           /// <summary>
           ///情况说明
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Remark { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

