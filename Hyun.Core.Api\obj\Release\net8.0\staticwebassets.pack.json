{"Files": [{"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\CorsPost.html", "PackagePath": "staticwebassets\\CorsPost.html"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\SchoolAddress.xlsx", "PackagePath": "staticwebassets\\Download\\SchoolAddress.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\SchoolUserThirdAllow.xlsx", "PackagePath": "staticwebassets\\Download\\SchoolUserThirdAllow.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\ThEquipmentCategory.xlsx", "PackagePath": "staticwebassets\\Download\\ThEquipmentCategory.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\Unit.xlsx", "PackagePath": "staticwebassets\\Download\\Unit.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UnitCompany.xlsx", "PackagePath": "staticwebassets\\Download\\UnitCompany.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UnitSchool.xlsx", "PackagePath": "staticwebassets\\Download\\UnitSchool.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\User.xlsx", "PackagePath": "staticwebassets\\Download\\User.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UserMine.xlsx", "PackagePath": "staticwebassets\\Download\\UserMine.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\UserThirdAllow.xlsx", "PackagePath": "staticwebassets\\Download\\UserThirdAllow.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\关于征订校服的征求家长意见书.docx", "PackagePath": "staticwebassets\\Download\\关于征订校服的征求家长意见书.docx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\学生导入信息.xlsx", "PackagePath": "staticwebassets\\Download\\学生导入信息.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\校服清单模板.xlsx", "PackagePath": "staticwebassets\\Download\\校服清单模板.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Download\\班级导入信息.xlsx", "PackagePath": "staticwebassets\\Download\\班级导入信息.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Department.xlsx", "PackagePath": "staticwebassets\\HyunCore.Data.excel\\Department.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Modules.xlsx", "PackagePath": "staticwebassets\\HyunCore.Data.excel\\Modules.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Permission.xlsx", "PackagePath": "staticwebassets\\HyunCore.Data.excel\\Permission.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\Role.xlsx", "PackagePath": "staticwebassets\\HyunCore.Data.excel\\Role.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\RoleModulePermission.xlsx", "PackagePath": "staticwebassets\\HyunCore.Data.excel\\RoleModulePermission.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\SysUserInfo.xlsx", "PackagePath": "staticwebassets\\HyunCore.Data.excel\\SysUserInfo.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.excel\\UserRole.xlsx", "PackagePath": "staticwebassets\\HyunCore.Data.excel\\UserRole.xlsx"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\BlogArticle.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\BlogArticle.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Department.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\Department.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Modules.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\Modules.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Permission.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\Permission.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Role.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\Role.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\RoleModulePermission.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\RoleModulePermission.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\TasksQz.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\TasksQz.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\Topic.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\Topic.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\TopicDetail.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\TopicDetail.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\UserRole.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\UserRole.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\HyunCore.Data.json\\sysUserInfo.tsv", "PackagePath": "staticwebassets\\HyunCore.Data.json\\sysUserInfo.tsv"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\JMeterTest.png", "PackagePath": "staticwebassets\\JMeterTest.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\Resource\\Export\\Excel\\预算清单.xls", "PackagePath": "staticwebassets\\Resource\\Export\\Excel\\预算清单.xls"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\css\\style.css", "PackagePath": "staticwebassets\\css\\style.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\index.html", "PackagePath": "staticwebassets\\index.html"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\js\\anime.min.js", "PackagePath": "staticwebassets\\js\\anime.min.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\js\\jquery-3.3.1.min.js", "PackagePath": "staticwebassets\\js\\jquery-3.3.1.min.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\logo.png", "PackagePath": "staticwebassets\\logo.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\logo\\favicon-32x32.png", "PackagePath": "staticwebassets\\logo\\favicon-32x32.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\swg-login.html", "PackagePath": "staticwebassets\\swg-login.html"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\101001\\20241218\\2412183a79082c364e45fe8d3a94764114afe3.png", "PackagePath": "staticwebassets\\uploadfile\\101001\\20241218\\2412183a79082c364e45fe8d3a94764114afe3.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\101001\\20241218\\24121873205bc2bf804e0da229dfc805527d8d.jpeg", "PackagePath": "staticwebassets\\uploadfile\\101001\\20241218\\24121873205bc2bf804e0da229dfc805527d8d.jpeg"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\101001\\20241218\\2412189e78b12084924d4681c65e81c7b1e001.png", "PackagePath": "staticwebassets\\uploadfile\\101001\\20241218\\2412189e78b12084924d4681c65e81c7b1e001.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\104001\\20250506\\250506254352c38b6f4d9a9c84e2d965a2ee66.png", "PackagePath": "staticwebassets\\uploadfile\\104001\\20250506\\250506254352c38b6f4d9a9c84e2d965a2ee66.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600001\\20250805\\2508051c8c56052f414adbbb7d100e162b5367.png", "PackagePath": "staticwebassets\\uploadfile\\600001\\20250805\\2508051c8c56052f414adbbb7d100e162b5367.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600001\\20250805\\25080527e597a48e54481aa6d3ae2b4d554d9c.png", "PackagePath": "staticwebassets\\uploadfile\\600001\\20250805\\25080527e597a48e54481aa6d3ae2b4d554d9c.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600002\\20250805\\250805207f0c42701c49a281162398af8ce25c.png", "PackagePath": "staticwebassets\\uploadfile\\600002\\20250805\\250805207f0c42701c49a281162398af8ce25c.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\uploadfile\\600002\\20250805\\2508052db62f35d6c749e6a07ecbb5b4865268.png", "PackagePath": "staticwebassets\\uploadfile\\600002\\20250805\\2508052db62f35d6c749e6a07ecbb5b4865268.png"}, {"Id": "D:\\工作管理\\项目研发\\维修平台\\系统开发\\程序\\hyun.core\\Hyun.Core.Api\\wwwroot\\web.config", "PackagePath": "staticwebassets\\web.config"}, {"Id": "obj\\Release\\net8.0\\staticwebassets\\msbuild.Hyun.Core.Api.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Release\\net8.0\\staticwebassets\\msbuild.build.Hyun.Core.Api.props", "PackagePath": "build\\Hyun.Core.Api.props"}, {"Id": "obj\\Release\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.Hyun.Core.Api.props", "PackagePath": "buildMultiTargeting\\Hyun.Core.Api.props"}, {"Id": "obj\\Release\\net8.0\\staticwebassets\\msbuild.buildTransitive.Hyun.Core.Api.props", "PackagePath": "buildTransitive\\Hyun.Core.Api.props"}], "ElementsToRemove": []}