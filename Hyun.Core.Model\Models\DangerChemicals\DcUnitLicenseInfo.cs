namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位许可证信息
    ///</summary>
    [SugarTable("dc_UnitLicenseInfo","单位许可证信息")]
    public class DcUnitLicenseInfo : BaseEntity
    {

          public DcUnitLicenseInfo()
          {

          }

           /// <summary>
           ///对象表Id（UnitType： 3、单位p_Unit表Id， 4、 供应商dc_Company表Id）
          /// </summary>
          public long ObjId { get; set; }

           /// <summary>
           ///名称（UnitType： 3、单位名称， 4、 对方单位名称）
          /// </summary>
          [SugarColumn(Length = 255)]
          public string UnitName { get; set; }

           /// <summary>
           ///地址（UnitType ：3、单位地址， 4、 对方单位地址 ）
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Addresz { get; set; }

           /// <summary>
           ///经办人姓名（UnitType：3、经办人姓名 ，4、 对方经办人姓名）
          /// </summary>
          [SugarColumn(Length = 63)]
          public string UserName { get; set; }

           /// <summary>
           ///单位许可证名称（UnitType ：3、单位许可证名称， 4、 对方单位许可证名称 ）
          /// </summary>
          [SugarColumn(Length = 63)]
          public string LicenseName { get; set; }

           /// <summary>
           ///单位许可证编号（UnitType ：3、单位许可证编号， 4、 对方单位许可证编号 ）
          /// </summary>
          [SugarColumn(Length = 63)]
          public string LicenseNo { get; set; }

           /// <summary>
           ///经办人证件类型（01身份证、99其他证件[字典]）（UnitType ：3、经办人证件类型， 4、 对方经办人证件类型 ）
          /// </summary>
          [SugarColumn(Length = 31)]
          public string IdType { get; set; }

           /// <summary>
           ///经办人证件号码（UnitType ：3、经办人证件号码， 4、 对方经办人证件号码 ）
          /// </summary>
          [SugarColumn(Length = 63)]
          public string IdNo { get; set; }

           /// <summary>
           ///经办人手机（UnitType ：3、经办人手机， 4、 对方经办人手机 ）
          /// </summary>
          [SugarColumn(Length = 31)]
          public string Mobile { get; set; }

           /// <summary>
           ///单位类型（3：单位    4：供应商）
          /// </summary>
          public int UnitType { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建单位
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

