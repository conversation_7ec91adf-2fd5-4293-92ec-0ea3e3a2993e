﻿using Hyun.Core.Model.Model;

namespace Hyun.Core.IServices
{

    ///<summary>
    ///DcApplyConfirmDetail接口方法
    ///</summary>
    public interface IDcApplyConfirmDetailServices : IBaseServices<DcApplyConfirmDetail>
    {

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<DcApplyConfirmDetail> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<DcApplyConfirmDetail>> Find(Expression<Func<DcApplyConfirmDetail, bool>> expression);
        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="param">DcApplyConfirmDetailParam实体参数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<List<DcApplyConfirmDetail>> Find(DcApplyConfirmDetailParam param);
        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">DcApplyConfirmDetailParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<DcApplyConfirmDetail>> GetPaged(DcApplyConfirmDetailParam param);

        //<used>0</used>
        Task<Result<string>> AfterConfirm(long id, int surplusStatuz, int wasetStatuz, decimal backNum, long unitId, long userId, List<DcWasteRecordModel> wasteList);

        //<used>1</used>
        Task<Result<string>> UpdateBackNum(long id, decimal backNum, long unitId, long userId);

        //<used>1</used>
        Task<Result<VDcApplyAudit>> ApplyView(long id, long unitId, long userId);

        //<used>1</used>
        Task<Result> Adjust(long applyId, long schoolMaterialId, decimal num, long userId, long unitId);

        //<used>0</used>
        Task<List<DcDistributionConfirm>> GetDistributionConfirm(string ids, int unitId);

        //<used>0</used>
        Task<Result> Revoke(int id, int userId, int unitId);

        //<used>1</used>
        Task<Result> Collar(long confirmDetailId, string checkCode, string withCode, long withUserId, decimal num, long userId, long unitId);

        //<used>0</used>
        Task<Result> Confirm(int id, int unitId, int userId);

        //<used>0</used>
        Task<Result> BatchConfirm(string ids, int unitId, int userId);

        //<used>0</used>
        Task<Result> BackConfirm(int backId, DateTime regDate, int isMayUse, decimal backNum, string cabinetAddress, int userId, int unitId, int id);

        //<used>0</used>
        Task<List<DcApplyConfirmDetail>> Insert(List<DcApplyConfirmDetail> entityCollection);

        //<used>1</used>
        Task<List<DcApplyConfirmDetail>> GetApplyConfirmDetail(long applyId, long unitId);

        Task<List<DcMessage>> GetMessage(string batchNo, long unitId);

        Task<Result<string>> DeleteApplyConfirm(long applyConfirmDetailId, int waitConfrimStatuz, long userId, long unitId);

        Task<Result<VDcApplyAudit>> ApplyDetailView(long id, int process);
        /// <summary>
        /// 单位领用详情(原：V_dc_MaterialApplyDetailList GetPaged)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcMaterialApplyDetailList>> GetStatisticsPaged(VDcMaterialApplyDetailListParam param);
        /// <summary>
        /// 单位危化品退回列表 (原V_dc_MaterialBackList GetPaged)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcMaterialBackList>> GetMaterialBackPaged(VDcMaterialBackListParam param);
        /// <summary>
        /// 单位危化品归还列表列表(原：V_dc_MaterialRevertList GetPaged )
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<VDcMaterialRevertList>> GetMaterialRevertPaged(VDcMaterialRevertListParam param);
    }
}

