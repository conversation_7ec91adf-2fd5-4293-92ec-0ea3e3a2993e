
Date：2025-09-09 09:21:54.950
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:05.343
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:14.393
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/9/9 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:14.579
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/9/9 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:14.680
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:超级管理员 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:14.719
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/9/9 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:14.759
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/9/9 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:14.830
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:717683386531973 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:15.267
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:15.288
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:15.833
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:超级管理员 [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/9/9 9:22:15 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/9/9 9:22:15 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:16.118
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  DISTINCT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:16.206
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:16.298
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:2 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:199 [Type]:Int32    
[Name]:@LoginCount [Value]:1150 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/9/9 9:22:16 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:592116605120645 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:16.648
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.272
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.292
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/9/9 9:22:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.325
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.371
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND ********** 
[Pars]:
[Name]:@Date0 [Value]:2025/9/9 9:22:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.521
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.619
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.722
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/9/9 9:22:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:17.741
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND ********** 
[Pars]:
[Name]:@Date0 [Value]:2025/9/9 9:22:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:644092336705669 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:22.456
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:22.565
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (0,9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:22.770
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000007008,100000000000070,100000000007046,100000000007013,100000000007015,100000000007016,100000000007028,100000000007029,100000000007031,100000000007035,100000000007033,100000000002016,100000000007022,100000000007034,100000000007023,100000000007024,100000000007025,100000000007038,100000000007042,100000000007043,100000000007044,852930755125317632,853563899415367680,878944878002704384,878945428651905024,880770164449087488,880772015173144576,880774745178509312)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:22.877
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.115
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst9) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst10) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst11) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst12) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst13) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst14) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst16  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted8 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted8 )   WHERE ((NOT (1=2)  AND( [PAU].[IsDeleted] = @IsDeleted6 )) AND ( [PAU].[AuditUserId] = @AuditUserId7 ))  AND ( [PAU].[IsDeleted] = @IsDeleted8 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId7 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@MethodConst9 [Value]: [Type]:String    
[Name]:@MethodConst10 [Value]:0 [Type]:Int32    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst14 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.129
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , @constant4 AS [ProcessId] , @constant5 AS [ProcessName] , @constant6 AS [ProcessNodeId] , @constant7 AS [ProcessNodeName] , @constant8 AS [NodeType] , @constant9 AS [TreatHandle] , @constant10 AS [TreatHandleUrl] , @constant11 AS [StopHandle] , @constant12 AS [StopHandleUrl] , CAST([M].[Sort] AS BIGINT) AS [Sort] , @constant13 AS [IsBegin]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Module] [M] ON (( [PAU].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted0 )) AND ( [M].[IsDeleted] = @IsDeleted3 )   WHERE (( [PAU].[IsDeleted] = @IsDeleted1 ) AND ( [PAU].[AuditUserId] = @AuditUserId2 ))  AND ( [PAU].[IsDeleted] = @IsDeleted3 )) MergeTable  ORDER BY [Sort] ASC 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@constant4 [Value]:0 [Type]:Int64    
[Name]:@constant5 [Value]: [Type]:String    
[Name]:@constant6 [Value]:0 [Type]:Int64    
[Name]:@constant7 [Value]: [Type]:String    
[Name]:@constant8 [Value]:3 [Type]:Int32    
[Name]:@constant9 [Value]: [Type]:String    
[Name]:@constant10 [Value]: [Type]:String    
[Name]:@constant11 [Value]: [Type]:String    
[Name]:@constant12 [Value]: [Type]:String    
[Name]:@constant13 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.174
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [GPS].[ProcessId],[GPS].[GroupName],[GPS].[TypeCode],[GPS].[TypeBox],[GPS].[IsDeleted],[GPS].[CreateId],[GPS].[CreateBy],[GPS].[CreateTime],[GPS].[ModifyId],[GPS].[ModifyBy],[GPS].[ModifyTime],[GPS].[Version],[GPS].[Id] FROM [wf_GroupProcessSet] [GPS] Inner JOIN [wf_Process] [P] ON ( [GPS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted3 )   WHERE ((( [GPS].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [P].[Statuz] = @Statuz2 ))  AND ( [GPS].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.194
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[PSort],[IsOpenControl],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [UseUnitId] = @UseUnitId1 )) AND ( [Usage] = @Usage2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsOpenControl] = @IsOpenControl4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UseUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@Usage2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsOpenControl4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.650
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.691
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.745
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.869
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id] FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:23.880
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [IsDeleted],[Name],[LinkUrl],[Area],[Controller],[Action],[Icon],[Code],[OrderSort],[Description],[IsMenu],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsCommon],[PlatformType],[ParentId],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [SysModules]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 50 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:29.352
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:29.359
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:30.124
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:22:30.134
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:23:13.181
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:23:15.047
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:23:15.122
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:23:16.540
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:23:16.662
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:53.648
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:53.673
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:53.687
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:53.730
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup]  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) CountTable  
[Pars]:
[Name]:@JobId0 [Value]:713464753176709 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:53.760
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup] ,ROW_NUMBER() OVER(ORDER BY [log].[RunTime] DESC) AS RowIndex  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@JobId0 [Value]:713464753176709 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:57.880
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:57.888
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:57.951
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:57.956
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:59.655
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:59.662
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:59.701
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup]  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) CountTable  
[Pars]:
[Name]:@JobId0 [Value]:713461527326853 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:25:59.710
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup] ,ROW_NUMBER() OVER(ORDER BY [log].[RunTime] DESC) AS RowIndex  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@JobId0 [Value]:713461527326853 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:01.045
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:01.052
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:01.124
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:01.132
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:02.283
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:02.292
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:02.305
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup]  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) CountTable  
[Pars]:
[Name]:@JobId0 [Value]:713464753176709 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:02.310
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup] ,ROW_NUMBER() OVER(ORDER BY [log].[RunTime] DESC) AS RowIndex  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@JobId0 [Value]:713464753176709 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:04.593
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:04.605
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:04.611
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:04.673
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:04.679
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:05.964
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:05.974
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:05.997
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup]  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) CountTable  
[Pars]:
[Name]:@JobId0 [Value]:713182850519173 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:06.004
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup] ,ROW_NUMBER() OVER(ORDER BY [log].[RunTime] DESC) AS RowIndex  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@JobId0 [Value]:713182850519173 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:07.302
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:07.309
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:07.372
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:07.378
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:08.497
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:08.510
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:08.528
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup]  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) CountTable  
[Pars]:
[Name]:@JobId0 [Value]:713171138310277 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:08.534
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup] ,ROW_NUMBER() OVER(ORDER BY [log].[RunTime] DESC) AS RowIndex  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@JobId0 [Value]:713171138310277 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:21.674
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:21.694
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:21.706
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:21.770
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:21.777
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:26.335
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:26.346
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:26.359
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup]  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) CountTable  
[Pars]:
[Name]:@JobId0 [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:26.367
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [log].[RunPars] AS [RunPars] , [log].[RunResult] AS [RunResult] , [log].[RunTime] AS [RunTime] , [log].[EndTime] AS [EndTime] , [log].[ErrMessage] AS [ErrMessage] , [log].[ErrStackTrace] AS [ErrStackTrace] , [log].[TotalTime] AS [TotalTime] , [qz].[Name] AS [Name] , [qz].[JobGroup] AS [JobGroup] ,ROW_NUMBER() OVER(ORDER BY [log].[RunTime] DESC) AS RowIndex  FROM [TasksLog] [log] Left JOIN [TasksQz] [qz] ON ( [log].[JobId] = [qz].[Id] )   WHERE ( [log].[JobId] = @JobId0 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@JobId0 [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:29.212
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:29.219
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:29.257
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 09:26:29.262
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:14.930
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:14.945
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:14.951
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:14.992
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:14.997
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:18.090
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:18.096
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:18.129
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id] FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:18.134
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [Name],[JobGroup],[Cron],[AssemblyName],[ClassName],[Remark],[RunTimes],[BeginTime],[EndTime],[TriggerType],[IntervalSecond],[CycleRunTimes],[CycleHasRunTimes],[IsStart],[JobParams],[IsDeleted],[CreateTime],[Id],ROW_NUMBER() OVER(ORDER BY  Id desc ) AS RowIndex  FROM [TasksQz]  WHERE (( [IsDeleted] <> @IsDeleted0 ) AND (( [Name] IS NOT NULL ) AND  ([Name] like '%'+@MethodConst2+'%') ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:True [Type]:Boolean    
[Name]:@MethodConst2 [Value]: [Type]:String    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.072
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.095
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.112
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.323
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.391
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.876
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.908
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_AttachmentConfig]  WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted0 )) CountTable  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:25.917
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [UnitId],[ModuleType],[ModuleName],[FileCategory],[Name],[IsFilled],[LeastFileNumber],[MaxFileNumber],[UploadFileType],[Statuz],[Sequence],[Memo],[FileSize],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Sequence asc) AS RowIndex  FROM [b_AttachmentConfig]  WHERE ( 1 = 1 )   AND ( [IsDeleted] = @IsDeleted0 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:33.166
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:33.173
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:33.885
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [shelf].[Id] AS [Id] , [shelf].[IsDeleted] AS [IsDeleted] , [shelf].[CreateTime] AS [CreateTime] , [shelf].[Brand] AS [Brand] , [shelf].[Name] AS [Name] , [shelf].[OriginAddress] AS [OriginAddress] , [shelf].[Parameter] AS [Parameter] , [shelf].[Price] AS [Price] , [shelf].[Producer] AS [Producer] , [school].[Name] AS [SchoolName] , [shelf].[SecurityLevel] AS [SecurityLevel] , [shelf].[Sex] AS [Sex] , [shelf].[MainImagePath] AS [MainImagePath] , [shelf].[SizePath] AS [SizePath] , ISNULL([shelf].[Sort],@MethodConst5) AS [Sort] , [shelf].[StandardNum] AS [StandardNum] , [shelf].[Uniformtype] AS [Uniformtype] , [shelf].[UnitId] AS [UnitId] , [shelf].[UnitName] AS [UnitName] , [shelf].[UseStatuz] AS [UseStatuz] , [shelf].[AuditStatuz] AS [AuditStatuz] , [shelf].[AuditTime] AS [AuditTime] , [supplier].[Name] AS [SupplierName] , [purchase].[PurchaseNo] AS [PurchaseNo] , [purchase].[ContractEndDate] AS [ContractEndDate] , [shelf].[IsShow] AS [IsShow] , [school].[PId] AS [CountyId]  FROM [x_UniformShelf] [shelf] Inner JOIN [p_Unit] [school] ON ( [shelf].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted4 )  Inner JOIN [p_Unit] [supplier] ON ( [shelf].[UnitId] = [supplier].[Id] ) AND ( [supplier].[IsDeleted] = @IsDeleted4 )  Left JOIN [x_UniformPurchase] [purchase] ON ( [shelf].[UniformPurchaseId] = [purchase].[Id] ) AND ( [purchase].[IsDeleted] = @IsDeleted4 )   WHERE ((( [shelf].[UseStatuz] = @UseStatuz0 ) AND ( [shelf].[AuditStatuz] =@constant2)) AND ( [shelf].[IsDeleted] = @IsDeleted3 ))  AND ( [shelf].[IsDeleted] = @IsDeleted4 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted6 )) CountTable  
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@constant2 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@MethodConst5 [Value]:0 [Type]:Int32    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:33.901
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY  Sort ASC ) AS RowIndex  FROM  (SELECT  [shelf].[Id] AS [Id] , [shelf].[IsDeleted] AS [IsDeleted] , [shelf].[CreateTime] AS [CreateTime] , [shelf].[Brand] AS [Brand] , [shelf].[Name] AS [Name] , [shelf].[OriginAddress] AS [OriginAddress] , [shelf].[Parameter] AS [Parameter] , [shelf].[Price] AS [Price] , [shelf].[Producer] AS [Producer] , [school].[Name] AS [SchoolName] , [shelf].[SecurityLevel] AS [SecurityLevel] , [shelf].[Sex] AS [Sex] , [shelf].[MainImagePath] AS [MainImagePath] , [shelf].[SizePath] AS [SizePath] , ISNULL([shelf].[Sort],@MethodConst5) AS [Sort] , [shelf].[StandardNum] AS [StandardNum] , [shelf].[Uniformtype] AS [Uniformtype] , [shelf].[UnitId] AS [UnitId] , [shelf].[UnitName] AS [UnitName] , [shelf].[UseStatuz] AS [UseStatuz] , [shelf].[AuditStatuz] AS [AuditStatuz] , [shelf].[AuditTime] AS [AuditTime] , [supplier].[Name] AS [SupplierName] , [purchase].[PurchaseNo] AS [PurchaseNo] , [purchase].[ContractEndDate] AS [ContractEndDate] , [shelf].[IsShow] AS [IsShow] , [school].[PId] AS [CountyId]  FROM [x_UniformShelf] [shelf] Inner JOIN [p_Unit] [school] ON ( [shelf].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted4 )  Inner JOIN [p_Unit] [supplier] ON ( [shelf].[UnitId] = [supplier].[Id] ) AND ( [supplier].[IsDeleted] = @IsDeleted4 )  Left JOIN [x_UniformPurchase] [purchase] ON ( [shelf].[UniformPurchaseId] = [purchase].[Id] ) AND ( [purchase].[IsDeleted] = @IsDeleted4 )   WHERE ((( [shelf].[UseStatuz] = @UseStatuz0 ) AND ( [shelf].[AuditStatuz] =@constant2)) AND ( [shelf].[IsDeleted] = @IsDeleted3 ))  AND ( [shelf].[IsDeleted] = @IsDeleted4 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted6 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@constant2 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@MethodConst5 [Value]:0 [Type]:Int32    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:33.992
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[UnitId],[Title],[Explanation],[Sort],[Statuz],[ValueNum],[ValueDecimal],[Memo],[DicValue],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [x_UniformConfig]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:4000 [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:34.104
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( [UnitType] =@constant1) AND ( [IsDeleted] = @IsDeleted2 )) AND ( [Statuz] = @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@constant1 [Value]:4 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:34.250
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( [UnitType] =@constant1) AND ( [IsDeleted] = @IsDeleted2 )) AND ( [Statuz] = @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@constant1 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:39.374
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:39.374
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:39.386
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:39.393
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:39.394
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:39.401
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:49.598
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds]  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) CountTable  
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:49.613
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds] ,ROW_NUMBER() OVER(ORDER BY Id DESC) AS RowIndex  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:49.881
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE (( 1 = 1 ) AND( [Statuz] > @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:49.887
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Id ASC) AS RowIndex  FROM [p_Unit]  WHERE (( 1 = 1 ) AND( [Statuz] > @Statuz1 ))  AND ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND ********** 
[Pars]:
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:55.449
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:55.460
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:55.466
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:55.485
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds]  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) CountTable  
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:55.495
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds] ,ROW_NUMBER() OVER(ORDER BY Id DESC) AS RowIndex  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:56.360
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:56.367
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:56.388
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds]  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) CountTable  
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:56.398
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds] ,ROW_NUMBER() OVER(ORDER BY Id DESC) AS RowIndex  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:57.113
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:57.119
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:57.140
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds]  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) CountTable  
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:57.148
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [Id] AS [Id] , [Code] AS [Code] , [Name] AS [Name] , [AreaId] AS [AreaId] , [UnitId] AS [UnitId] , [UnitName] AS [UnitName] , [UnitType] AS [UnitType] , [UnitPId] AS [UnitPId] , [UnitPName] AS [UnitPName] , [AcctId] AS [AcctId] , [AcctName] AS [AcctName] , [StaffNumber] AS [StaffNumber] , [IdNumber] AS [IdNumber] , [Mobile] AS [Mobile] , [Qq] AS [Qq] , [Email] AS [Email] , [Statuz] AS [Statuz] , [NickName] AS [NickName] , [AcctStatuz] AS [AcctStatuz] , [UserType] AS [UserType] , [AdministratorType] AS [AdministratorType] , [Sort] AS [Sort] , [UserValidate] AS [UserValidate] , [ContiLoginCount] AS [ContiLoginCount] , [TotalLoginCount] AS [TotalLoginCount] , [LoginCount] AS [LoginCount] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [it].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleIds] ,ROW_NUMBER() OVER(ORDER BY Id DESC) AS RowIndex  FROM [V_UserList] [it]  WHERE (( 1 = 1 ) AND( [UnitType] <> @UnitType1 ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@UnitType1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:58.878
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:58.888
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:58.927
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [U].[Id] AS [Id] , [A].[Id] AS [AcctId] , [U].[Name] AS [Name] , [U].[Mobile] AS [Mobile] , [A].[LoginName] AS [AcctName] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [U].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , [U].[Statuz] AS [Statuz]  FROM [SysUserExtension] [U] Inner JOIN [SysUserInfo] [A] ON ( [U].[Id] = [A].[UserExtensionId] )  Inner JOIN [SysUserRole] [UIR] ON ( [U].[Id] = [UIR].[UserId] ) AND ( [UIR].[IsDeleted] = @IsDeleted2 )  Inner JOIN [SysRole] [R] ON ( [UIR].[RoleId] = [R].[Id] )   WHERE (( [R].[RoleType] = @RoleType0 ) OR ( [R].[RoleType] = @RoleType1 ))  AND ( [U].[IsDeleted] = @IsDeleted2 )GROUP BY [U].[Id],[A].[Id],[U].[Name],[U].[Mobile],[A].[LoginName],[U].[Statuz] ) MergeTable  ) CountTable  
[Pars]:
[Name]:@RoleType0 [Value]:5 [Type]:Int32    
[Name]:@RoleType1 [Value]:6 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:56:58.935
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Id DESC) AS RowIndex  FROM  (SELECT  [U].[Id] AS [Id] , [A].[Id] AS [AcctId] , [U].[Name] AS [Name] , [U].[Mobile] AS [Mobile] , [A].[LoginName] AS [AcctName] , (SELECT stuff((SELECT cast(N',' as nvarchar(max)) + cast([r].[Name] as nvarchar(max)) FROM [SysUserRole] [ur]    INNER JOIN [SysRole] [r] ON (( [U].[Id] = [ur].[UserId] ) AND ( [ur].[RoleId] = [r].[RoleId] ))  FOR XML PATH('')),1,len(N','),'')  ) AS [RoleNames] , [U].[Statuz] AS [Statuz]  FROM [SysUserExtension] [U] Inner JOIN [SysUserInfo] [A] ON ( [U].[Id] = [A].[UserExtensionId] )  Inner JOIN [SysUserRole] [UIR] ON ( [U].[Id] = [UIR].[UserId] ) AND ( [UIR].[IsDeleted] = @IsDeleted2 )  Inner JOIN [SysRole] [R] ON ( [UIR].[RoleId] = [R].[Id] )   WHERE (( [R].[RoleType] = @RoleType0 ) OR ( [R].[RoleType] = @RoleType1 ))  AND ( [U].[IsDeleted] = @IsDeleted2 )GROUP BY [U].[Id],[A].[Id],[U].[Name],[U].[Mobile],[A].[LoginName],[U].[Statuz] ) MergeTable  ) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@RoleType0 [Value]:5 [Type]:Int32    
[Name]:@RoleType1 [Value]:6 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.892
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.895
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.896
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.904
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.909
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.910
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.919
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.919
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.923
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:00.924
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:01.330
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ((( 1 = 1 ) AND( [Statuz] > @Statuz1 )) AND (( [UnitType] = @UnitType2 ) OR ( [UnitType] = @UnitType3 )))  AND ( [IsDeleted] = @IsDeleted4 )) CountTable  
[Pars]:
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
[Name]:@UnitType2 [Value]:1 [Type]:Int32    
[Name]:@UnitType3 [Value]:2 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:01.337
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id],ROW_NUMBER() OVER(ORDER BY Id ASC) AS RowIndex  FROM [p_Unit]  WHERE ((( 1 = 1 ) AND( [Statuz] > @Statuz1 )) AND (( [UnitType] = @UnitType2 ) OR ( [UnitType] = @UnitType3 )))  AND ( [IsDeleted] = @IsDeleted4 )) T WHERE RowIndex BETWEEN 1 AND ********** 
[Pars]:
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
[Name]:@UnitType2 [Value]:1 [Type]:Int32    
[Name]:@UnitType3 [Value]:2 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:01.477
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [U].[Id] AS [Id] , [U].[AreaId] AS [AreaId] , [A].[Name] AS [AreaName] , [U].[PId] AS [PId] , [U1].[Name] AS [PName] , ISNULL([SE].[SchoolStage],@MethodConst3) AS [SchoolStage] , ISNULL([D].[DicName],@MethodConst4) AS [SchoolStageName] , ISNULL([SE].[SchoolNature],@MethodConst5) AS [SchoolNature] , [U].[UnitType] AS [UnitType] , [U].[IndustryId] AS [IndustryId] , [B].[Name] AS [IndustryName] , [U].[Code] AS [Code] , [U].[Name] AS [Name] , [U].[Brief] AS [Brief] , [U].[PinYin] AS [PinYin] , [U].[PinYinBrief] AS [PinYinBrief] , [U].[Legal] AS [Legal] , [U].[Address] AS [Address] , [U].[ZipCode] AS [ZipCode] , [U].[Url] AS [Url] , [U].[Introduction] AS [Introduction] , [U].[Logo] AS [Logo] , [U].[VipGrade] AS [VipGrade] , [U].[OrganizationCode] AS [OrganizationCode] , [U].[LoginPic] AS [LoginPic] , [U].[Mobile] AS [Mobile] , [U].[Tel] AS [Tel] , [U].[Email] AS [Email] , [U].[Position] AS [Position] , [U].[TrafficMap] AS [TrafficMap] , [U].[EmployeeNum] AS [EmployeeNum] , [U].[UserId] AS [UserId] , [U].[RegTime] AS [RegTime] , [U].[Memo] AS [Memo] , [U].[Statuz] AS [Statuz] , [A].[Pid] AS [AreaPid] , [U].[Sort] AS [Sort] , [U].[SourceType] AS [SourceType] , [U].[ThirdUnitId] AS [ThirdUnitId] , [U].[ThirdUnitName] AS [ThirdUnitName]  FROM [p_Unit] [U] Left JOIN [b_Area] [A] ON ( [U].[AreaId] = [A].[Id] ) AND ( [A].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_SchoolExtension] [SE] ON ( [U].[Id] = [SE].[UnitId] ) AND ( [SE].[IsDeleted] = @IsDeleted2 )  Left JOIN [b_Dictionary] [D] ON ((CAST([SE].[SchoolStage] AS NVARCHAR(MAX)) = [D].[DicValue] ) AND ( [D].[TypeCode] = @TypeCode0 )) AND ( [D].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [U1] ON ( [U].[PId] = [U1].[Id] ) AND ( [U1].[IsDeleted] = @IsDeleted2 )  Left JOIN [b_Industry] [B] ON ( [U].[IndustryId] = [B].[Id] ) AND ( [B].[IsDeleted] = @IsDeleted2 )   WHERE ( [U].[Statuz] >= @Statuz1 )  AND ( [U].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE (( 1 = 1 ) AND( [Statuz] = @Statuz7 ))) CountTable  
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@MethodConst3 [Value]:0 [Type]:Int32    
[Name]:@MethodConst4 [Value]: [Type]:String    
[Name]:@MethodConst5 [Value]:0 [Type]:Int32    
[Name]:@Statuz7 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:01.491
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY Id DESC) AS RowIndex  FROM  (SELECT  [U].[Id] AS [Id] , [U].[AreaId] AS [AreaId] , [A].[Name] AS [AreaName] , [U].[PId] AS [PId] , [U1].[Name] AS [PName] , ISNULL([SE].[SchoolStage],@MethodConst3) AS [SchoolStage] , ISNULL([D].[DicName],@MethodConst4) AS [SchoolStageName] , ISNULL([SE].[SchoolNature],@MethodConst5) AS [SchoolNature] , [U].[UnitType] AS [UnitType] , [U].[IndustryId] AS [IndustryId] , [B].[Name] AS [IndustryName] , [U].[Code] AS [Code] , [U].[Name] AS [Name] , [U].[Brief] AS [Brief] , [U].[PinYin] AS [PinYin] , [U].[PinYinBrief] AS [PinYinBrief] , [U].[Legal] AS [Legal] , [U].[Address] AS [Address] , [U].[ZipCode] AS [ZipCode] , [U].[Url] AS [Url] , [U].[Introduction] AS [Introduction] , [U].[Logo] AS [Logo] , [U].[VipGrade] AS [VipGrade] , [U].[OrganizationCode] AS [OrganizationCode] , [U].[LoginPic] AS [LoginPic] , [U].[Mobile] AS [Mobile] , [U].[Tel] AS [Tel] , [U].[Email] AS [Email] , [U].[Position] AS [Position] , [U].[TrafficMap] AS [TrafficMap] , [U].[EmployeeNum] AS [EmployeeNum] , [U].[UserId] AS [UserId] , [U].[RegTime] AS [RegTime] , [U].[Memo] AS [Memo] , [U].[Statuz] AS [Statuz] , [A].[Pid] AS [AreaPid] , [U].[Sort] AS [Sort] , [U].[SourceType] AS [SourceType] , [U].[ThirdUnitId] AS [ThirdUnitId] , [U].[ThirdUnitName] AS [ThirdUnitName]  FROM [p_Unit] [U] Left JOIN [b_Area] [A] ON ( [U].[AreaId] = [A].[Id] ) AND ( [A].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_SchoolExtension] [SE] ON ( [U].[Id] = [SE].[UnitId] ) AND ( [SE].[IsDeleted] = @IsDeleted2 )  Left JOIN [b_Dictionary] [D] ON ((CAST([SE].[SchoolStage] AS NVARCHAR(MAX)) = [D].[DicValue] ) AND ( [D].[TypeCode] = @TypeCode0 )) AND ( [D].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [U1] ON ( [U].[PId] = [U1].[Id] ) AND ( [U1].[IsDeleted] = @IsDeleted2 )  Left JOIN [b_Industry] [B] ON ( [U].[IndustryId] = [B].[Id] ) AND ( [B].[IsDeleted] = @IsDeleted2 )   WHERE ( [U].[Statuz] >= @Statuz1 )  AND ( [U].[IsDeleted] = @IsDeleted2 )) MergeTable   WHERE (( 1 = 1 ) AND( [Statuz] = @Statuz7 ))) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@MethodConst3 [Value]:0 [Type]:Int32    
[Name]:@MethodConst4 [Value]: [Type]:String    
[Name]:@MethodConst5 [Value]:0 [Type]:Int32    
[Name]:@Statuz7 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:03.500
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Brief],[EnName],[EnBrief],[Code],[Depth],[Sort],[Path],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Area]  WHERE ( [Pid] = @Pid0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Pid0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:03.831
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE ( [TypeCode] = @TypeCode0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:15000 [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:03.843
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [TypeCode],[TypeName],[DicName],[DicValue],[Memo],[Sequence],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_Dictionary]  WHERE ( [TypeCode] = @TypeCode0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@TypeCode0 [Value]:1101 [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.100
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.111
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.119
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.164
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.179
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.187
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.193
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.204
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.306
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 10:57:06.313
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.823
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.845
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.851
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.877
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.887
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.892
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.899
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.909
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.981
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:09.988
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.877
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.886
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.892
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.912
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.922
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.928
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.932
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:53.942
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:54.016
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:00:54.021
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:01:39.308
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:01:39.320
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:01:39.327
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.131
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [UserExtensionId] = @UserExtensionId0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@UserExtensionId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.139
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.145
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.153
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.163
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (9000)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.226
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.241
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-09-09 11:04:18.246
LogLevel：Information
Message：------------------ 
 User:["超级管理员"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------