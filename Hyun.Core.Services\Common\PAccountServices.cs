﻿using Amazon.Runtime.Internal.Util;
using Grpc.Core;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SkiaSharp;
using StackExchange.Redis;
using Hyun.Core.Model.Models;

namespace Hyun.Core.Services
{

    ///<summary>
    ///PAccount方法
    ///</summary>
    public class PAccountServices : BaseServices<SysUserInfo>, IPAccountServices
    {
        private readonly IBaseRepository<SysUserRole> _userInRoleRepository;
        private readonly IBaseRepository<SysUserExtension> _userRepository;
        //b_UserActionLog
        private readonly IBaseRepository<BUserActionLog> _userActionLogRepository;
        //b_UserActionStatic
        private readonly IBaseRepository<BUserActionStatic> _userActionStaticRepository;


        public PAccountServices(IBaseRepository<BUserActionLog> userActionLogRepository, 
            IBaseRepository<BUserActionStatic> userActionStaticRepository,
            IBaseRepository<SysUserExtension> userRepository,
            IBaseRepository<SysUserRole> userInRoleRepository)
            : base()
        {
            _userActionLogRepository = userActionLogRepository;
            _userActionStaticRepository = userActionStaticRepository;
            _userRepository = userRepository;
            _userInRoleRepository = userInRoleRepository;
        }

        #region 查询数据
        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        public async Task<SysUserInfo> GetById(long id)
        {
            return await base.QueryById(id);
        }

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        public async Task<List<SysUserInfo>> Find(Expression<Func<SysUserInfo, bool>> expression)
        {
            return await base.Query(expression);
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">PAccountParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<SysUserInfo>> GetPaged(SysUserInfoParam param)
        {
            var expression = ListFilter(param);
            return await base.QueryPage(expression, param.pageIndex, param.pageSize, param.orderByFields);
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">PAccountParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<SysUserInfo, bool>> ListFilter(SysUserInfoParam param)
        {
            var expression = LinqExtensions.True<SysUserInfo>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
            }
            return expression;
        }
        #endregion
        /// <summary>
        /// 用户登录,如果登录成功，返回VUserDetail，实现USP_P_Account_Login、b_UserActionLog
        /// </summary>
        /// <param name="name"></param>
        /// <param name="pass">md5加密</param>
        /// <param name="userIp"></param>
        /// <used>1</used>
        /// <returns></returns>
        public async Task<Result> Login(string name, string pass, string userIp)
        {
            Result r = new Result();

            if (!string.IsNullOrEmpty(name) && name.Length > 60)
            {
                r.flag = 0;
                r.msg = "登录账号不能大于60个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(userIp) && userIp.Length > 60)
            {
                r.flag = 0;
                r.msg = "用户IP不能大于60个字符";
                return r;
            }

            //if exists(SELECT  COUNT(id) FROM b_UserActionLog
            // WHERE  UserIp = @UserIp and[Type] = 5  AND RegTime >= @CurrentDate having COUNT(id) > 50)
            var query = this.Db.Queryable<BUserActionLog>()
                .Where(a => a.UserIp == userIp && a.Type == 5 && a.CreateTime > DateTime.Today.Date && a.IsDeleted == false)
                .Having(it => SqlFunc.AggregateCount(it.Id) > 50)
                .Select(it => new
                {
                    count = SqlFunc.AggregateCount(it.Id)
                });
            var qr = await query.ToListAsync();
            if (qr != null && qr.Count > 0)
            {
                r.flag = 2;
                r.msg = "您的操作，有侵入风险，系统已经拦截。如有疑问，请联系客服。";
                return r;
            }
            //断用户是否存在，帐号 
            var userList = await base.Query(a =>
               a.LoginName == name && a.Statuz > 0);
            if (userList != null && userList.Count > 0)
            {
                var userAccount = userList.FirstOrDefault();
                if (userAccount.Statuz == 2)
                {
                    r.flag = 2;
                    r.msg = "等待审核中，请在审核通过后再登录。";
                    return r;
                }
                //判断是否在当日内连续登陆密码错误超过五次
                //--获取最后一次登录正确登录的Id值
                //SELECT @MaxId =ISNULL(MAX(Id) ,0)  FROM b_UserActionLog
                //WHERE UserId = @UserId AND RegTime > DATEADD(dd, -1, GETDATE()) AND Statuz = 1


                var maxId = await this.Db.Queryable<BUserActionLog>()
                    .Where(a => a.UserId == userAccount.UserExtensionId && a.Statuz == 1 && a.CreateTime > DateTime.Today.Date)
                    .MaxAsync(it => it.Id);
                var lastDate = await this.Db.Queryable<BUserActionLog>()
                 .Where(a => a.UserId == userAccount.UserExtensionId && a.Statuz == 1 && a.CreateTime > DateTime.Today.Date)
                 .MaxAsync(it => it.CreateTime); // SQL Server不存在数据时，返回{0001/1/1 0:00:00}，其他未验证

                long ErrorNum = 0;
                if (lastDate.Year > 2023)
                {
                    //  --获取大于最后一次成功登录后，错误的次数
                    //SELECT @ErrorNum = COUNT(1) FROM b_UserActionLog WHERE UserId = @UserId AND Id > @MaxId

                    ErrorNum = await this.Db.Queryable<BUserActionLog>()
                        .Where(a => a.UserId == userAccount.UserExtensionId && a.Id > maxId)
                        .CountAsync();
                }
                else
                {
                    //--当日内没有登录成功过，获取当日内错误次数
                    //SELECT @ErrorNum = COUNT(Id) FROM b_UserActionLog WHERE UserId = @UserId AND RegTime > DATEADD(dd, -1, GETDATE())
                    ErrorNum = await this.Db.Queryable<BUserActionLog>()
                             .Where(a => a.UserId == userAccount.UserExtensionId && a.CreateTime > DateTime.Today.Date)
                             .CountAsync();
                }

                if (ErrorNum >= 5)
                {
                    r.flag = 2;
                    r.msg = "登录失败。账号当日被锁定,次日0时后自动解除锁定；或联系客服解锁。";
                    return r;
                }
                if(userAccount.UserValidate.HasValue && userAccount.UserValidate.Value < DateTime.Today.Date.AddDays(1))
                {
                    r.flag = 0;
                    r.msg = "账号已经过期，无法使用平台。";
                    return r;
                }
                string headPortrait = string.Empty;
                //var userInfo = await this.Db.Queryable<VUserDetail>().FirstAsync(it => it.Id == userAccount.UserExtensionId);
                var userInfo = await this.Db.Queryable<SysUserExtension>()
                .LeftJoin<PUnit>((u, un) => u.UnitId == un.Id)
                .LeftJoin<PUnit>((u, un, pun) => un.PId == pun.Id)
                .LeftJoin<SysUserInfo>((u, un, pun, a) => u.Id == a.UserExtensionId)
                .Where(u => u.Statuz >= 0)
                .Select((u, un, pun, a) => new VUserDetail
                {
                    Id = u.Id,
                    AreaId = un.AreaId,
                    UnitId = u.UnitId,
                    UnitName = un.Name,
                    UnitType = un.UnitType,
                    UnitPId = pun.Id,
                    UnitPName = pun.Name,
                    AcctId = a.Id,
                    AcctName = a.LoginName,
                    NickName = a.NickName,
                    //RealName = u.Name,
                    StaffNumber = u.StaffNumber,
                    IdNumber = u.IdNumber,
                    Sex = u.Sex,
                    Birthday = u.Birthday,
                    Address = u.Address,
                    ZipCode = u.ZipCode,
                    Tel = u.Tel,
                    Mobile = u.Mobile,
                    Qq = u.Qq,
                    Email = u.Email,
                    RegTime = u.CreateTime,
                    Memo = u.Memo,
                    Statuz = u.Statuz,
                    Name = u.Name,
                    //CreateId = a.CreateId,
                    AcctStatuz = a.Statuz,
                    UserType = u.UserType,
                    AdministratorType = u.AdministratorType,
                    UserValidate = a.UserValidate,
                    HeadPortrait = u.HeadPortrait,
                    UnitStatus = un.Statuz
                }).FirstAsync(u => u.Id == userAccount.UserExtensionId);

                var objUser =await this.Db.Queryable<SysUserExtension>().Where(f => f.Id == userAccount.UserExtensionId).FirstAsync();
                if(objUser != null)
                {
                    userInfo.HeadPortrait = objUser.HeadPortrait;
                }
                //如果用户所在单位禁用，则用户仍然能登录，但禁止使用单位功能，这样可以兼容家长端、班主任（ClassTeacher = 370）不依赖单位登录使用平台。
                var listRemoveRole = new List<long>();
                if (userInfo.UnitId > 0)
                {
                    var unit = await this.Db.Queryable<PUnit>().Where(a => a.Id == userInfo.UnitId).FirstAsync();
                    //加不等于2兼容注册成功后可以登录
                    if (unit != null && unit.Statuz != 1 && unit.Statuz != 2)
                    {
                        userInfo.UnitId = 0;
                        listRemoveRole = await this.Db.Queryable<SysRole>().Where(a => a.RoleType == userInfo.UnitType).Select(it => it.Id).ToListAsync();
                    }
                }
               
                if (userAccount.LoginPWD.ToLower() == pass.ToLower())
                {                    
                    await _userActionLogRepository.Add(new BUserActionLog
                    {
                        UserId = userAccount.UserExtensionId,
                        AccountName = name,
                        UserIp = userIp,
                        Type = 1,
                        CreateTime = DateTime.Now,
                        Statuz = 1,
                        CreateId = userAccount.UserExtensionId,
                        CreateBy = userInfo.Name,
                        ModifyId = userAccount.UserExtensionId,
                        ModifyBy = userInfo.Name,
                        Id = BaseDBConfig.GetYitterId()
                    });

                    var listRole = await this.Db.Queryable<SysRole>()
                                 .InnerJoin<SysUserRole>((a, b) => a.RoleId == b.RoleId)
                                 .Where((a, b) => b.UserId == userInfo.Id).ToListAsync();

                    if (listRemoveRole.Count > 0)
                    {
                        //从 listRole 中删除所有在 listRemoveRole 中 Id 匹配的 RoleId 的元素
                        listRole.RemoveAll(role => listRemoveRole.Any(remove => remove == role.RoleId));                      
                    }
                    if (listRole.Count > 0)
                    {
                        userInfo.RoleIds = listRole.Select(a => a.RoleId).ToList();
                        userInfo.RoleNames = listRole.Select(a => a.Name).ToList();
                    }
                    await UserActionStaticLogin(userAccount, userInfo);

                    r.flag = 1;
                    r.msg = "登录成功。";
                    r.data.rows = userInfo;
                }
                else
                {
                    await _userActionLogRepository.Add(new BUserActionLog
                    {
                        UserId = userAccount.UserExtensionId,
                        AccountName = name,
                        UserIp = userIp,
                        Type = 1,
                        CreateTime = DateTime.Now,
                        Statuz = 0,
                        CreateId = userAccount.UserExtensionId,
                        CreateBy = userInfo.Name,
                        ModifyId = userAccount.UserExtensionId,
                        ModifyBy = userInfo.Name
                    });
                    ErrorNum++;
                    if (ErrorNum >= 5)
                    {
                        r.flag = 2;
                        r.msg = "登录失败。账号当日被锁定,次日0时后自动解除锁定；或联系客服解锁。";
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = $"用户名或密码错误。当日如果连续累计输错5次（含）将锁定账号，次日才能登录。你已经输错{ErrorNum}次。";
                    }
                    r.data.total = ErrorNum;
                    return r;
                }

            }
            else
            {
                await _userActionLogRepository.Add(new BUserActionLog
                {
                    UserId = 0,
                    AccountName = name,
                    UserIp = userIp,
                    Type = 5,
                    CreateTime = DateTime.Now,
                    Statuz = 0,
                    CreateId = 0,
                    CreateBy = "",
                    ModifyId = 0,
                    ModifyBy = ""
                });

                r.flag = 2;
                r.msg = "登录失败。可能原因：用户名或密码错误。";
                return r;

            }          
            r.flag = 1;
            return r;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userIp"></param>        
        /// <returns></returns>
        public async Task<Result> LoginById(long id, string userIp) {
            Result r = new Result();
            if (!string.IsNullOrEmpty(userIp) && userIp.Length > 60)
            {
                r.flag = 0;
                r.msg = "用户IP不能大于60个字符";
                return r;
            }
            //断用户是否存在，帐号 
            var userList = await base.Query(a =>
               a.UserExtensionId == id && a.Statuz > 0);
            if (userList != null && userList.Count > 0)
            {
                var userAccount = userList.FirstOrDefault();
                if (userAccount.Statuz == 2)
                {
                    r.flag = 2;
                    r.msg = "等待审核中，请在审核通过后再登录。";
                    return r;
                }
              
                if (userAccount.UserValidate.HasValue && userAccount.UserValidate.Value < DateTime.Today.Date.AddDays(1))
                {
                    r.flag = 0;
                    r.msg = "账号已经过期，无法使用平台。";
                    return r;
                }
                var userInfo = await this.Db.Queryable<VUserDetail>().FirstAsync(it => it.Id == userAccount.UserExtensionId);
               
                    await _userActionLogRepository.Add(new BUserActionLog
                    {
                        UserId = userAccount.UserExtensionId,
                        AccountName = userInfo.AcctName,
                        UserIp = userIp,
                        Type = 1,
                        CreateTime = DateTime.Now,
                        Statuz = 1,
                        CreateId = userAccount.UserExtensionId,
                        CreateBy = userInfo.Name,
                        ModifyId = userAccount.UserExtensionId,
                        ModifyBy = userInfo.Name
                    });

                    var listRole = await this.Db.Queryable<VUserListTemp>()
                        .Where(a => a.Id == userInfo.Id && a.RoleId != null).ToListAsync();
                    if (listRole.Count > 0)
                    {
                        userInfo.RoleIds = listRole.Select(a => (long)a.RoleId.Value).ToList();
                        userInfo.RoleNames = listRole.Select(a => a.RoleName).ToList();
                    }
                    await UserActionStaticLogin(userAccount, userInfo);

                    r.flag = 1;
                    r.msg = "登录成功。";
                    r.data.rows = userInfo;
            }
            else
            {
                r.flag = 2;
                r.msg = "当前账号不存在。";
                return r;

            }
            r.flag = 1;
            return r;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result> GetLoginInfo(long id)
        {
            Result r = new Result();
          
            //断用户是否存在，帐号 
            var userAccountList = await base.Query(a =>
               a.UserExtensionId == id && a.Statuz > 0);
            if (userAccountList != null && userAccountList.Count > 0)
            {
                var userAccount = userAccountList.FirstOrDefault();
                if (userAccount.Statuz == 2)
                {
                    r.flag = 2;
                    r.msg = "等待审核中，请在审核通过后再登录。";
                    return r;
                }

                if (userAccount.UserValidate.HasValue && userAccount.UserValidate.Value < DateTime.Today.Date.AddDays(1))
                {
                    r.flag = 0;
                    r.msg = "账号已经过期，无法使用平台。";
                    return r;
                }
                var userInfo = await this.Db.Queryable<VUserDetail>().FirstAsync(it => it.Id == userAccount.UserExtensionId);
                var listRemoveRole = new List<long>();
                if (userInfo.UnitId > 0)
                {
                    var unit = await this.Db.Queryable<PUnit>().Where(a => a.Id == userInfo.UnitId).FirstAsync();
                    if (unit != null && unit.Statuz != 1 && unit.Statuz != 2)
                    {
                        userInfo.UnitId = 0;
                        listRemoveRole = await this.Db.Queryable<SysRole>().Where(a => a.RoleType == userInfo.UnitType).Select(it => it.Id).ToListAsync();
                    }
                }               

                var listRole = await this.Db.Queryable<VUserListTemp>()
                    .Where(a => a.Id == userInfo.Id && a.RoleId != null).ToListAsync();

                if (listRemoveRole.Count > 0)
                {
                    //从 listRole 中删除所有在 listRemoveRole 中 Id 匹配的 RoleId 的元素
                    listRole.RemoveAll(role => listRemoveRole.Any(remove => remove == role.RoleId));
                }

                if (listRole.Count > 0)
                {
                    userInfo.RoleIds = listRole.Select(a => (long)a.RoleId.Value).ToList();
                    userInfo.RoleNames = listRole.Select(a => a.RoleName).ToList();
                }          

                r.flag = 1;
                r.msg = "获取成功。";
                r.data.rows = userInfo;
            }
            else
            {
                r.flag = 2;
                r.msg = "当前账号不存在。";
                return r;

            }
            r.flag = 1;
            return r;
        }


        private async Task UserActionStaticLogin(SysUserInfo userAccount, VUserDetail userInfo)
        {
            var userActionStatic = await this.Db.Queryable<BUserActionStatic>().FirstAsync(it => it.UserId == userAccount.UserExtensionId);
            if (userActionStatic == null)
            {
                await _userActionStaticRepository.Add(new BUserActionStatic
                {
                    UserId = userAccount.UserExtensionId,
                    ContiLoginCount = 1,
                    TotalLoginCount = 1,
                    LoginCount = 1,
                    CreateTime = DateTime.Now,
                    ModifyTime = DateTime.Now,
                    CreateId = userAccount.UserExtensionId,
                    CreateBy = userInfo.Name,
                    ModifyId = userAccount.UserExtensionId,
                    ModifyBy = userInfo.Name
                });
            }
            else
            {
                if (userActionStatic.ModifyTime.Value.AddDays(1).Date == DateTime.Now.Date)
                {
                    userActionStatic.ContiLoginCount++;
                    userActionStatic.TotalLoginCount++;
                }
                else if (userActionStatic.ModifyTime.Value.AddDays(1).Date < DateTime.Now.Date)
                {
                    userActionStatic.ContiLoginCount = 1;
                    userActionStatic.TotalLoginCount++;
                }
                userActionStatic.ModifyTime = DateTime.Now;
                userActionStatic.LoginCount++;
                userActionStatic.CreateId = userInfo.Id;
                userActionStatic.CreateBy = userInfo.Name;
                userActionStatic.ModifyId = userInfo.Id;
                userActionStatic.ModifyBy = userInfo.Name;
                await _userActionStaticRepository.Update(userActionStatic);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id">用户Id</param>
        /// <param name="memo"></param>
        /// <param name="userIp"></param>
        /// <param name="userId">操作人Id</param>
        /// <returns></returns>
        //<used>1</used>
        public async Task<Result> Unlock(long id, string memo, string userIp, long userId)
        {
            Result r = new Result();
            if (!string.IsNullOrEmpty(userIp) && userIp.Length > 60)
            {
                r.flag = 0;
                r.msg = "用户IP不能大于60个字符";
                return r;
            }
            var acct = await this.Db.Queryable<SysUserInfo>().Where(a => a.UserExtensionId == id).FirstAsync();
            if (acct == null)
            {
                r.flag = 2;
                r.msg = "解冻失败。可能原因：用户不存在、或者帐号状态异常";
                return r;
            }
            var userList = await this.Db.Queryable<VUserDetail>().Where(a => a.Id == id || a.Id == userId).ToListAsync();
            if(userList == null) 
            {
                r.flag = 2;
                r.msg = "解冻失败。非法操作，系统阻止。";
                return r;
            }
            if(!userList.Exists(a => a.Id == id))
            {
                r.flag = 2;
                r.msg = "解冻失败。要解冻的用户不存在，请刷新后重试。";
                return r;
            }
            if (!userList.Exists(a => a.Id == userId))
            {
                r.flag = 2;
                r.msg = "解冻失败。您的账号存在异常，请退出后重新登录。";
                return r;
            }
            var uRoleList = await this.Db.Queryable<SysUserRole>().Where(a => a.UserId == userId).ToListAsync();
            bool isSystemAdmin = uRoleList.Any(role => 
            role.RoleId == RoleTypes.SystemAdmin.ObjToInt() || role.RoleId == RoleTypes.SystemOps.ObjToInt()
            );

            if (!isSystemAdmin)
            {
                if (userList.Select(a => a.UnitId).Distinct().Count() > 1) //--不是同一单位
                {
                    if (userList.Select(a => a.UnitType).Distinct().Count() == 1) //--相同单位类型,比如都是学校
                    {
                        r.flag = 2;
                        r.msg = "给其他单位账号非法解锁，系统阻止。";
                        return r;
                    }
                    else
                    {
                        var userUnitPId = userList.Where(a => a.Id == id).FirstOrDefault().UnitPId;
                        if (!userList.Exists(a => a.Id == userId && a.UnitId == userUnitPId)) //--不是父子关系
                        {
                            r.flag = 2;
                            r.msg = "给其他单位账号非法解锁，系统阻止。";
                            return r;
                        }
                    }
                }
            }
            //此处可加不是超级管理员，不能解锁的判断
                       

            var optUserName = userList.Where(a => a.Id == userId).FirstOrDefault().Name;
            await _userActionLogRepository.Add(new BUserActionLog
            {
                UserId = id,
                AccountName = "[解冻]" + memo,
                UserIp = userIp,
                Type = 2,
                CreateTime = DateTime.Now,
                Statuz = 1,
                CreateId = userId,
                CreateBy = optUserName,
                ModifyId = userId,
                ModifyBy = optUserName
            });

            r.flag = 1;
            r.msg = "执行成功";

            return r;
        }

        /// <summary>
        /// 判断账号是否存在家长、班主任账号
        /// </summary>
        /// <param name="accountName"></param>
        /// <param name="userType"></param>
        /// <returns></returns>
        public async Task<Result<bool>> IsExistAccount(string accountName, int userType)
        {
            var isExist = await this.Db.Queryable<SysUserInfo>().AnyAsync(a => a.LoginName == accountName);
            bool isExistRole = false; 
            int roleId = -1;
            if(userType == 5)
            {
                roleId = RoleTypes.Parent.ObjToInt();
            }
            else if (userType == 6)
            {
                roleId = RoleTypes.ClassTeacher.ObjToInt();
            }
            if (isExist)
            {
                isExistRole = await this.Db.Queryable<SysUserRole>()
                    .InnerJoin<SysUserExtension>((a, b) => a.UserId == b.Id)
                    .InnerJoin<SysUserInfo>((a, b, c)=> b.Id == c.UserExtensionId)
                    .Where((a, b, c) => c.LoginName == accountName)
                    .AnyAsync(a => a.RoleId == roleId);
            }
            return Result<bool>.Success("", isExist && isExistRole, 1);
        }

        /// <summary>
        /// 通过手机号码登录，可自动创建家长、班主任账号
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <param name="smsId"></param>
        /// <param name="userIp"></param>
        /// <param name="userType">5:家长；6：班主任，1：普通用户</param>
        /// <returns></returns>
        public async Task<Result> LoginByPhoneNumber(string phoneNumber, long smsId, string userIp, int userType)
        {
            Result r = new Result();

            if (!string.IsNullOrEmpty(phoneNumber) && phoneNumber.Length > 60)
            {
                r.flag = 0;
                r.msg = "登录账号不能大于60个字符";
                return r;
            }
            if (!string.IsNullOrEmpty(userIp) && userIp.Length > 60)
            {
                r.flag = 0;
                r.msg = "用户IP不能大于60个字符";
                return r;
            }
            if (smsId <= 0)
            {
                r.flag = 0;
                r.msg = "无效的登录请求，uuid不正确";
                return r;
            }
            var smsList = this.Db.Queryable<BSmsHistoryValidate>().Where(a => a.Id == smsId);
            if (smsList == null || !smsList.Any())
            {
                r.flag = 0;
                r.msg = "未查询到获取验证码信息，登录失败";
                return r; ;
            }

            if (smsList.Any(a => a.ErrorCount >= 3 || a.Status == 2))
            {
                r.flag = 0;
                r.msg = "验证码已经失效，请重新获取";
                return r; ;
            }
             

            //断用户是否存在，帐号 
            long userId = 0;
            SysUserInfo userAccount = null;
            var userAccountList = await base.Query(a => a.LoginName == phoneNumber);

            if (userAccountList == null || !userAccountList.Any()) //--用户不存在,创建账号
            {
                if (userType == 5 || userType == 6)
                {

                    userId = await _userRepository.Add(new SysUserExtension
                    {
                        Name = phoneNumber,
                        RegTime = DateTime.Now,
                        Statuz = 1,
                        CreateId = 0,
                        ModifyId = 0,
                        UserType = userType,
                        Mobile = phoneNumber,
                        Id = BaseDBConfig.GetYitterId()
                    });
                    var accountId = await base.Add(new SysUserInfo
                    {
                        LoginName = phoneNumber,
                        LoginPWD = "通过手机验证码登录",
                        CreateTime = DateTime.Now,
                        Statuz = 1,
                        CreateId = 0,
                        ModifyId = 0,
                        UserExtensionId = userId,
                        Id = BaseDBConfig.GetYitterId()
                    });
                    userAccount = await base.QueryById(accountId);
                }
                else
                {
                    r.flag = 0;
                    r.msg = "账号不存在，请先注册。";
                    return r; ;
                }
            }
            else
            {
                userAccount = userAccountList.FirstOrDefault();
                if (userAccount.Statuz == 2)
                {
                    r.flag = 2;
                    r.msg = "等待审核中，请在审核通过后再登录。";
                    return r;
                }
                userId = userAccount.UserExtensionId;
            }


            var userInfo = await this.Db.Queryable<VUserDetail>().FirstAsync(it => it.Id == userId);

            //如果用户所在单位禁用用户账号，则用户仍然能登录，但禁止使用单位功能，这样可以兼容家长端、班主任（ClassTeacher = 370）不依赖单位登录使用平台。
            var listRemoveRole = new List<long>();
            if (userInfo.Statuz == 0 && userInfo.UnitId > 0)
            {
                listRemoveRole = await this.Db.Queryable<SysRole>().Where(a => a.RoleType == userInfo.UnitType).Select(it => it.Id).ToListAsync();
            }

            //如果没有角色权限，需要自动授权
            if (userType == 5 || userType == 6)
            {
                var isExistRole = await this.Db.Queryable<SysUserRole>().
                    InnerJoin<SysRole>((u, r) => u.RoleId == r.RoleId)
                    .Where((u, r) => u.UserId == userId && r.RoleType == userType)
                    .AnyAsync();
                int roleId = -1;
                if (userType == 5)
                {
                    roleId = RoleTypes.Parent.ObjToInt();
                }
                else if (userType == 6)
                {
                    roleId = RoleTypes.ClassTeacher.ObjToInt();
                }
                if (!isExistRole)
                {
                    await _userInRoleRepository.Add(new SysUserRole
                    {
                        UserId = userId,
                        RoleId = roleId,
                        // 移除手动设置ID，让系统自动生成
                    });
                }
            }

            await _userActionLogRepository.Add(new BUserActionLog
            {
                UserId = userId,
                AccountName = userInfo.Name,
                UserIp = userIp,
                Type = 1,
                CreateTime = DateTime.Now,
                Statuz = 1
                // 移除手动设置ID，让系统自动生成
            });

            var listRole = await this.Db.Queryable<SysRole>()
                .InnerJoin<SysUserRole>((a, b) => a.RoleId == b.RoleId)
                .Where((a, b) => b.UserId == userInfo.Id).ToListAsync();
            if (listRemoveRole.Count > 0)
            {
                //从 listRole 中删除所有在 listRemoveRole 中 Id 匹配的 RoleId 的元素
                listRole.RemoveAll(role => listRemoveRole.Any(remove => remove == role.RoleId));
            }
            if (listRole.Count > 0)
            {
                userInfo.RoleIds = listRole.Select(a => (long)a.RoleId).ToList();
                userInfo.RoleNames = listRole.Select(a => a.Name).ToList();
            }
            else
            {
                userInfo.RoleIds = new List<long>();
                userInfo.RoleNames = new List<string>();
            }

            await UserActionStaticLogin(userAccount, userInfo);

             await this.Db.Updateable<BSmsHistoryValidate>()
               .SetColumns(it => it.Status == 2)
               .Where(it => it.Id == smsId)
               .ExecuteCommandAsync();

            r.flag = 1;
            r.msg = "登录成功。";
            r.data.rows = userInfo;

            return r;
        }

    }
}

