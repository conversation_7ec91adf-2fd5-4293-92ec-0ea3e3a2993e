namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///单位物品库
    ///</summary>
    [SugarTable("dc_SchoolMaterial", "单位物品库")]
    public class DcSchoolMaterial : BaseEntity
    {

        public DcSchoolMaterial()
        {

        }

        /// <summary>
        ///单位Id
        /// </summary>
        public long SchoolId { get; set; }

        /// <summary>
        ///分类Id
        /// </summary>
        public long SchoolCatalogId { get; set; }

        /// <summary>
        ///基础库Id
        /// </summary>
        public long BaseCatalogId { get; set; }

        /// <summary>
        ///规格型号Id
        /// </summary>
        public long SchoolMaterialModelId { get; set; }

        /// <summary>
        ///品牌Id
        /// </summary>
        public long SchoolMaterialBrandId { get; set; }

        /// <summary>
        ///供应商列表Id
        /// </summary>
        public long LvCompanyId { get; set; }

        /// <summary>
        ///设备名称
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Name { get; set; }

        /// <summary>
        ///设备品牌
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string Brand { get; set; }

        /// <summary>
        ///规格型号
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Model { get; set; }

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Num { get; set; }

        /// <summary>
        ///单位
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string UnitName { get; set; }

        /// <summary>
        ///单价
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Price { get; set; }

        /// <summary>
        ///金额
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Sum { get; set; }

        /// <summary>
        ///质保月份
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? WarrantyMonth { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 1023, IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        ///有效期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ValidDate { get; set; }

        /// <summary>
        ///状态（0：待审核；2：待绑定编码；3：待上报公安；1：已入库；-1：已删除 [上报公安后，如果多条标识码依据标识码拆分数据，源数据标记为删除] ）
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        ///库存量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal StockNum { get; set; }

        /// <summary>
        ///报废数量
        /// </summary>
        [SugarColumn(IsNullable = true,ColumnDataType = "money")]
        public decimal? ScrapNum { get; set; }

        /// <summary>
        ///盘盈盘亏量
        /// </summary>
        [SugarColumn(IsNullable = true,ColumnDataType = "money")]
        public decimal? InventoryNum { get; set; }

        /// <summary>
        ///创建时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///创建人Id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///物品采购清单Id
        /// </summary>
        public long PurchaseListId { get; set; }

        /// <summary>
        ///是否变更（0：未变更  1：已变更）
        /// </summary>
        public int IsChange { get; set; }

        /// <summary>
        ///采购批次
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string PurchaseBatchNo { get; set; }

        /// <summary>
        ///是否已打印
        /// </summary>
        public int IsPrint { get; set; }

        /// <summary>
        ///打印时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? PrintDate { get; set; }

        /// <summary>
        ///存放地点（dc_DepositAddress表Id）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? DepositAddressId { get; set; }

        /// <summary>
        ///MSDS文件
        /// </summary>
        [SugarColumn(Length = 2000, IsNullable = true)]
        public string MsdsFile { get; set; }

        /// <summary>
        ///入库方式（1：采购，2：退回）
        /// </summary>
        public int InputType { get; set; } = 1;

        /// <summary>
        ///是否可使用
        /// </summary>
        public int IsMayUse { get; set; } = 1;

        /// <summary>
        ///储藏柜地址
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string CabinetAddress { get; set; }

        /// <summary>
        ///变动时间（更改库存时更新）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        ///第三方物品编码
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ThirdMaterialId { get; set; }

        /// <summary>
        ///源Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? SourceId { get; set; }

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
        /// <summary>
        /// 金额
        /// </summary>
        [SugarColumn(IsIgnore = true,ColumnDataType = "money")]
        public decimal Amount { get;set; }

    }


}

