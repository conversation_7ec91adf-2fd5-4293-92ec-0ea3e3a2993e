namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///附件上传配置管理
    ///</summary>
    [SugarTable("b_AttachmentConfig", "附件上传配置管理")]
    public class BAttachmentConfig : BaseEntity
    {

        public BAttachmentConfig()
        {

        }

        /// <summary>
        ///使用单位(0:系统默认   > 0 对应的单位)
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        ///模块Id（对应的是每个表）
        /// </summary>
        public int ModuleType { get; set; } = 1;

        /// <summary>
        ///模块名称
        /// </summary>
        [SugarColumn(Length = 50)]
        public string ModuleName { get; set; }

        /// <summary>
        ///文件分类
        /// </summary>
        public int FileCategory { get; set; }

        /// <summary>
        ///名称
        /// </summary>
        [SugarColumn(Length = 31)]
        public string Name { get; set; }

        /// <summary>
        ///是否必填(0：非必填；1：必填）
        /// </summary>
        public int IsFilled { get; set; } = 0;

        /// <summary>
        ///至少上传文件个数(0：无限制，其它数字是几即需传几个)
        /// </summary>
        public int LeastFileNumber { get; set; } = 0;

        /// <summary>
        ///最大文件个数
        /// </summary>
        public int MaxFileNumber { get; set; } = 20;

        /// <summary>
        ///上传文件类型集合，必须.开头，且小写（图片：.jpg.jpeg.png.gif.bmp；Pdf文件：.pdf；Office文件：.doc.docx.xls.xlsx）
        /// </summary>
        [SugarColumn(Length = 255)]
        public string UploadFileType { get; set; } = "";

        /// <summary>
        ///状态（1：启用；0：禁用）
        /// </summary>
        public int Statuz { get; set; } = 1;

        /// <summary>
        ///排序
        /// </summary>
        public int Sequence { get; set; } = 0;

        /// <summary>
        ///备注
        /// </summary>
        [SugarColumn(Length = 127, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        [SugarColumn(DefaultValue = "0")]
        public int FileSize { get; set; } = 0;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
        /// <summary>
        /// 附件集合
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<object> AttachmentList { get; set; }
    }


}

