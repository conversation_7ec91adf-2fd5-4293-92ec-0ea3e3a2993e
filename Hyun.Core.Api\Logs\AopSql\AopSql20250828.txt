
Date：2025-08-28 18:08:49.680
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:08:49.680
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:08:49.680
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:23.480
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:23.480
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:23.481
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:23.481
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:24.126
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:24.462
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:56.944
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:56.944
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:09:56.945
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:12:08.579
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:12:08.608
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:12:08.608
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:12:09.055
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:13:28.186
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:15:22.156
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:15:22.156
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:15:22.156
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:15:22.156
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:32.554
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:35.669
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:35.715
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:admin [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:36.737
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:0 [Type]:Int64    
[Name]:@AccountName [Value]:admin [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:5 [Type]:Int32    
[Name]:@Statuz [Value]:0 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/28 18:18:36 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:18:35 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:51.980
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.018
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.026
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.076
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.087
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.106
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.107
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.116
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.148
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.158
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.176
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:713563873140869 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.186
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:713563873140869 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.367
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.371
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.380
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.380
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.906
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/28 18:18:52 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:18:52 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:52.906
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/28 18:18:52 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:18:52 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:53.206
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:53.250
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:53.287
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:4 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:217 [Type]:Int32    
[Name]:@LoginCount [Value]:1845 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:18:53 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:53.392
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:57.319
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:57.404
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (30,350,354,351,352,353,370,3200,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:57.962
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000010,100000000001001,100000000001002,100000000001003,100000000001004,100000000000020,100000000002001,100000000002002,100000000002017,100000000002008,100000000002009,100000000002010,100000000003001,100000000000030,100000000000040,100000000004001,100000000004003,100000000004004,100000000000050,100000000005001,100000000000060,100000000006001,100000000006002,100000000006003,100000000006004,100000000000070,100000000007004,100000000007005,100000000007006,100000000007007,100000000007017,100000000007018,100000000007019,100000000002011,100000000002012,100000000002013,100000000002014,100000000002015,100000000007001,100000000004002,100000000007002,100000000007014,100000000007020,100000000007021,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,815152703427579904,815152216284336128,814917889516638208,827558788750905344,827558788880928768,827558790864834560,840599930178375680,840600506052120576,842694325652426752,827558797206622208,815512156001800192,815512500404490240,815512620718100480,815512817057665024,844961710316982272,844957453845008384,827558795155607552,844957620178522112,844966541932892160,844966811886686208,844972844101144576,844972973948407808,844973424735424512,844973591006023680,844973895587991552,844974175394205696,844974402880671744,844974534216912896,844974696779747328,844974828485087232,844975023654440960,844975297731235840,844975449124638720,844975635729223680,844975797407059968,844975728242987008,844976309716127744,844976490087976960,844976660540297216,844976914006282240,844977126699438080,844977251022802944,844977462650605568,844977581781422080,844977790431268864,844978207466721280,844978326723366912,844977922153385984,844978006509228032,844978402241810432,844978933500743680,844979146500083712,844979303874564096,845255096391438336,844979454731096064,844979648616992768,844979828049317888,844980287262691328,844980359450857472,844980598295498752,844982125559025664,844981921363529728,844981743516651520,844981082699862016,844981392927363072,844980852977831936,844983416607739904,844983343421329408,844982957755076608,844982807137619968,844982595073609728,844982459761168384,844982262398193664,844983686142103552,844983827448205312,844983906800242688,844984091039240192,844984272614854656,844984515939012608,844984636638498816,844984773846765568,844984942273236992,844985090730627072,844985239972352000,844985309048344576,844985864302891008,844986081353928704,844986204750352384,844986319925940224,844986470987993088,844988224278368256,844988795462881280,844988925083652096,844989069602590720,844992020215762944,844992477537505280,844992359836946432,844992873186201600,844992931449278464,844993347960442880,844993496422027264,844997017473126400,844997280166580224,844997405890842624,844997569112182784,844997754236178432,844997884142161920,844997950672211968,844998050886717440,844998119732023296,852931579138281472,855107575388049408,855107866284003328,870714919371149312,871030740077252608,872554852792668160,875322682860638208,853564351586504704)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:58.194
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:58.395
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst9) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst10) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst11) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst12) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst13) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst14) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst16  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted8 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted8 )   WHERE ((NOT (1=2)  AND( [PAU].[IsDeleted] = @IsDeleted6 )) AND ( [PAU].[AuditUserId] = @AuditUserId7 ))  AND ( [PAU].[IsDeleted] = @IsDeleted8 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId7 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@MethodConst9 [Value]: [Type]:String    
[Name]:@MethodConst10 [Value]:0 [Type]:Int32    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst14 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:58.496
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[ProcessNodeId],[Statuz],[StatuzDesc],[WaitOrBack],[IsEnable],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessStatuz]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:58.804
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:58.891
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:58.936
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:58.973
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.025
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679134371973 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.070
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:702534294601861 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.115
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:705091520331909 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.158
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:703912462606469 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.202
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:674228238864517 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.246
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:675249294651525 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.292
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.337
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.387
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [GPS].[ProcessId],[GPS].[GroupName],[GPS].[TypeCode],[GPS].[TypeBox],[GPS].[IsDeleted],[GPS].[CreateId],[GPS].[CreateBy],[GPS].[CreateTime],[GPS].[ModifyId],[GPS].[ModifyBy],[GPS].[ModifyTime],[GPS].[Version],[GPS].[Id] FROM [wf_GroupProcessSet] [GPS] Inner JOIN [wf_Process] [P] ON ( [GPS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted3 )   WHERE ((( [GPS].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [P].[Statuz] = @Statuz2 ))  AND ( [GPS].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:18:59.405
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[PSort],[IsOpenControl],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [UseUnitId] = @UseUnitId1 )) AND ( [Usage] = @Usage2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsOpenControl] = @IsOpenControl4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UseUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@Usage2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsOpenControl4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:00.219
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:00.250
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:00.295
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.042
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.048
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[ProcessNodeId],[Statuz],[StatuzDesc],[WaitOrBack],[IsEnable],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessStatuz]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.062
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [PAU].[ProcessId] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , [PAU].[ProcessNodeId] AS [ProcessNodeId] , [PN].[NodeShowName] AS [NodeShowName] , [PAU].[AuditUserId] AS [AuditUserId] , [PN].[NodeType] AS [NodeType]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Module] [M] ON ( [PAU].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_Process] [P] ON ( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted7 )  Inner JOIN [wf_ProcessNode] [PN] ON ( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted7 )   WHERE ((((((( [PAU].[IsDeleted] = @IsDeleted0 ) AND ( [M].[IsDeleted] = @IsDeleted1 )) AND ( [P].[IsDeleted] = @IsDeleted2 )) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PAU].[AuditUserId] = @AuditUserId4 )) AND  ([PN].[Id] IN (658679471378565,658679471378565,658681824243845,658681824243845,658682131247237,658682131247237,658682545234053,658682545234053,661475211567237,661475211567237,661478208483461,661478208483461,667815747584133,667815747584133,667839088402565,667839088402565,667839181226117,667839181226117,667839300063365,667839300063365,661475211567237,661475211567237,661478208483461,661478208483461,674228817981573,674228817981573,674228986634373,674228986634373,674229194723461,674229194723461,675249401610373,675249401610373,675249516048517,675249516048517,675249600512133,675249600512133,675249401610373,675249401610373,675249516048517,675249516048517,675249600512133,675249600512133,675249401610373,675249401610373,675249516048517,675249516048517,675249600512133,675249600512133,675249401610373,675249401610373,675249516048517,675249516048517,675315433488517,675315433488517,675249600512133,675249600512133,675315513630853,675315513630853,676375207424133,676375207424133,676375283605637,676375283605637,676375359160453,676375359160453,676375808934021,676375808934021,676375283605637,676375283605637,676375359160453,676375359160453,676389425160325,676389425160325,676389492240517,676389492240517,676389582729349,676389582729349,676389640851589,676389640851589,701431612588165,701431612588165,701431842508933,701431842508933,701432050229381,701432050229381,702534414794885,702534414794885,702534572294277,702534572294277,703912581980293,703912581980293,703913893118085,703913893118085,703912581980293,703912581980293,704428996944005,704428996944005,703913893118085,703913893118085,705091701473413,705091701473413,705091859951749,705091859951749,705092139769989,705092139769989,705092317196421,705092317196421,705092469321861,705092469321861,705092789448837,705092789448837,705093020311685,705093020311685,705091701473413,705091701473413,705091859951749,705091859951749,705092317196421,705092317196421,705092789448837,705092789448837)) ) AND NOT (1=2) )  AND ( [PAU].[IsDeleted] = @IsDeleted7 )ORDER BY [M].[Sort],[P].[PSort],[PN].[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId4 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted7 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.149
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:658689418428549 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:658679024259205 [Type]:Int64    
[Name]:@Const6 [Value]:658689418428549 [Type]:Int64    
[Name]:@constant7 [Value]:658679471378565 [Type]:Int64    
[Name]:@ToId8 [Value]:658679471378565 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.165
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:667848427954309 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:667811814981765 [Type]:Int64    
[Name]:@Const6 [Value]:667848427954309 [Type]:Int64    
[Name]:@constant7 [Value]:667815747584133 [Type]:Int64    
[Name]:@ToId8 [Value]:667815747584133 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.173
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:670258683539589 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:658679134371973 [Type]:Int64    
[Name]:@Const6 [Value]:670258683539589 [Type]:Int64    
[Name]:@constant7 [Value]:661475211567237 [Type]:Int64    
[Name]:@ToId8 [Value]:661475211567237 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.182
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:676389824655493 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:658679024259205 [Type]:Int64    
[Name]:@Const6 [Value]:676389824655493 [Type]:Int64    
[Name]:@constant7 [Value]:676389425160325 [Type]:Int64    
[Name]:@ToId8 [Value]:676389425160325 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.189
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:701436251205765 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:701431378894981 [Type]:Int64    
[Name]:@Const6 [Value]:701436251205765 [Type]:Int64    
[Name]:@constant7 [Value]:701431612588165 [Type]:Int64    
[Name]:@ToId8 [Value]:701431612588165 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.196
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:702534674399365 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:702534294601861 [Type]:Int64    
[Name]:@Const6 [Value]:702534674399365 [Type]:Int64    
[Name]:@constant7 [Value]:702534414794885 [Type]:Int64    
[Name]:@ToId8 [Value]:702534414794885 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.202
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:703914263199877 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:703912462606469 [Type]:Int64    
[Name]:@Const6 [Value]:703914263199877 [Type]:Int64    
[Name]:@constant7 [Value]:703912581980293 [Type]:Int64    
[Name]:@ToId8 [Value]:703912581980293 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.212
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:704429713551493 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:703912462606469 [Type]:Int64    
[Name]:@Const6 [Value]:704429713551493 [Type]:Int64    
[Name]:@constant7 [Value]:703912581980293 [Type]:Int64    
[Name]:@ToId8 [Value]:703912581980293 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.219
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  @Const5 AS [ModuleId] , @Const6 AS [ProcessId] , @constant7 AS [ProcessNodeId] , [Id] AS [ProjectDeclarationId] , [ProjectName] AS [ProjectName] , [Statuz] AS [Statuz] , [StatuzDesc] AS [StatuzDesc] , [ModifyTime] AS [CreateDate] , ( CASE  WHEN ( [ToId] = @ToId8 )  THEN  @Const9  ELSE  @Const10  END ) AS [OperateType]  FROM [wf_ProjectDeclaration] [f]  WHERE (((( [ProcessId] = @ProcessId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [CreateId] = @CreateId2 )) AND ( [Statuz] <> @Statuz3 ))  AND ( [IsDeleted] = @IsDeleted4 )ORDER BY [ModifyTime] DESC 
[Pars]:
[Name]:@ProcessId0 [Value]:705106779930757 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@CreateId2 [Value]:*************** [Type]:Int64    
[Name]:@Statuz3 [Value]:1000 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@Const5 [Value]:705091520331909 [Type]:Int64    
[Name]:@Const6 [Value]:705106779930757 [Type]:Int64    
[Name]:@constant7 [Value]:705091701473413 [Type]:Int64    
[Name]:@ToId8 [Value]:705091701473413 [Type]:Int64    
[Name]:@Const9 [Value]:1 [Type]:Int32    
[Name]:@Const10 [Value]:2 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:19:09.234
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [PAU].[ModuleId] AS [ModuleId] , [PAU].[ProcessId] AS [ProcessId] , [PAU].[ProcessNodeId] AS [ProcessNodeId] , [PD].[Id] AS [ProjectDeclarationId] , [P].[ProcessName] AS [ProcessName] , [PD].[ProjectName] AS [ProjectName] , [PD].[StatuzDesc] AS [StatuzDesc] , [PD].[ModifyTime] AS [CreateDate] , [PD].[Statuz] AS [Statuz] , @constant7 AS [OperateType]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted6 )  Inner JOIN [wf_ProjectDeclaration] [PD] ON ((( [PAU].[ProcessId] = [PD].[ProcessId] ) AND ( [PAU].[ProcessNodeId] = [PD].[ToId] )) AND (( [PD].[AppointProcessNodeId] <> [PD].[ToId] ) OR ((( [PD].[AppointProcessNodeId] = [PD].[ToId] ) AND  ([PD].[AuditUserIds] like '%'+ CAST(@MethodConst0 AS NVARCHAR(MAX))+'%') ) AND NOT ([PD].[AuditedUserIds] like '%'+ CAST(@MethodConst1 AS NVARCHAR(MAX))+'%') ))) AND ( [PD].[IsDeleted] = @IsDeleted6 )   WHERE (((( [PAU].[IsDeleted] = @IsDeleted2 ) AND ( [P].[IsDeleted] = @IsDeleted3 )) AND ( [PD].[IsDeleted] = @IsDeleted4 )) AND ( [PAU].[AuditUserId] = @AuditUserId5 ))  AND ( [PAU].[IsDeleted] = @IsDeleted6 ) 
[Pars]:
[Name]:@MethodConst0 [Value]:*************** [Type]:Int64    
[Name]:@MethodConst1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId5 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@constant7 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:13.780
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:13.871
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:22:13 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:13.930
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:22:13 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:14.827
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:15.960
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:19.555
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:19.571
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:19.611
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:19.686
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:22:19 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:22:19.709
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:22:19 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:09.311
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:09.340
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:09.378
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:09.393
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:09 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:09.400
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:09 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:11.012
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:11.028
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:11.059
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:11.077
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:11 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:11.083
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:11 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:22.564
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:22.577
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:22.604
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:22.621
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:22 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:22.627
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:22 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:25.788
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:25.800
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:25.846
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:25.862
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:25 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:23:25.869
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:23:25 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:24:57.276
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:24:57.341
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:24:57.353
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:admin [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:24:58.390
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:0 [Type]:Int64    
[Name]:@AccountName [Value]:admin [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:5 [Type]:Int32    
[Name]:@Statuz [Value]:0 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/28 18:24:57 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:24:57 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:24:59.052
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:24:59.071
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:24:59.078
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:admin [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:25:00.104
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:0 [Type]:Int64    
[Name]:@AccountName [Value]:admin [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:5 [Type]:Int32    
[Name]:@Statuz [Value]:0 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/28 18:24:59 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:24:59 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:25:00.778
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:25:00.818
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:25:00.827
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:admin [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:25:01.852
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:0 [Type]:Int64    
[Name]:@AccountName [Value]:admin [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:5 [Type]:Int32    
[Name]:@Statuz [Value]:0 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/28 18:25:01 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:25:00 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:25:02.552
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionLog]  WHERE (([UserIp] = @MethodConst0) AND(  CAST([CreateTime] AS DATE) = @Date1 ))  AND ( [IsDeleted] = @IsDeleted2 )ORDER BY  Id DESC 
[Pars]:
[Name]:@MethodConst0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Date1 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:25:25.874
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[Remark] AS [Remark] , [A].[ImageUrl] AS [ImageUrl] , [A].[Attachment] AS [Attachment] , [A].[BeginTime] AS [BeginTime] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Id] = @Id2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )ORDER BY [A].[Sort] ASC,[A].[ModifyTime] DESC 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:25:25 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Id2 [Value]:644092655071365 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:31:21.007
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:31:21.021
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:31:21.069
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:31:21.085
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:31:21 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:31:21.091
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:31:21 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:35:15.774
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:35:15.801
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:35:15.834
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:35:15.863
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:35:15 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:35:15.868
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:35:15 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:36:26.744
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:36:26.756
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:36:26.800
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:36:26.840
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:36:26 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:36:26.852
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:36:26 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:43:56.661
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:43:56.673
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:43:56.713
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:43:56.726
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:43:56 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:43:56.732
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:43:56 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:45:41.536
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:45:41.546
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:45:41.590
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:45:41.601
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:45:41 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:45:41.606
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:45:41 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:45:59.762
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:00.248
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:00.307
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:00.319
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:00 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:00.324
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:00 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:03.385
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[Remark] AS [Remark] , [A].[ImageUrl] AS [ImageUrl] , [A].[Attachment] AS [Attachment] , [A].[BeginTime] AS [BeginTime] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Id] = @Id2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )ORDER BY [A].[Sort] ASC,[A].[ModifyTime] DESC 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:03 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Id2 [Value]:644092655071365 [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:04.857
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:04.870
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:04.915
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:04.927
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:04 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:04.932
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:04 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:07.038
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:07.098
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:07.139
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:07.151
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:07 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:07.155
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:07 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:08.948
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:09.003
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:08 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:09.051
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:09.056
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:09.083
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:08 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:13.399
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:13.486
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:13 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:13.488
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:13.508
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:13 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:13.509
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:18.344
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:18.347
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:18.352
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:18.350
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:18 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:18.396
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:18 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:21.263
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:21.274
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:21.300
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:21.315
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:21 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:46:21.320
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:46:21 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:16.912
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:16.935
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:16.992
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:17.008
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:51:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:17.016
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:51:17 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.383
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  COUNT([Id]) AS [count]  FROM [b_UserActionLog]  WHERE (((( [UserIp] = @UserIp0 ) AND ( [Type] = @Type1 )) AND ( [CreateTime] > @CreateTime2 )) AND ( [IsDeleted] = @IsDeleted3 ))  AND ( [IsDeleted] = @IsDeleted5 ) HAVING (COUNT([Id]) > @Const4 ) 
[Pars]:
[Name]:@UserIp0 [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type1 [Value]:5 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@Const4 [Value]:50 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.392
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [LoginName],[LoginPWD],[RealName],[Statuz],[DepartmentId],[Remark],[CriticalModifyTime],[LastErrorTime],[ErrorCount],[Mobile],[Enable],[IsDeleted],[UserValidate],[UserExtensionId],[NickName],[TenantId],[CreateId],[CreateBy],[CreateTime],[UpdateTime],[ModifyId],[ModifyBy],[Version],[Id] FROM [SysUserInfo]  WHERE (( [LoginName] = @LoginName0 ) AND ( [Statuz] > @Statuz1 )) 
[Pars]:
[Name]:@LoginName0 [Value]:yucai [Type]:String    
[Name]:@Statuz1 [Value]:0 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.396
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([Id]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.403
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT MAX([CreateTime]) FROM [b_UserActionLog]  WHERE ((( [UserId] = @UserId0 ) AND ( [Statuz] = @Statuz1 )) AND ( [CreateTime] > @CreateTime2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@CreateTime2 [Value]:2025/8/28 0:00:00 [Type]:DateTime    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.411
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT COUNT(1) FROM [b_UserActionLog]  WHERE (( [UserId] = @UserId0 ) AND ( [Id] > @Id1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.416
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1  [u].[Id] AS [Id] , [un].[AreaId] AS [AreaId] , [u].[UnitId] AS [UnitId] , [un].[Name] AS [UnitName] , [un].[UnitType] AS [UnitType] , [pun].[Id] AS [UnitPId] , [pun].[Name] AS [UnitPName] , [a].[Id] AS [AcctId] , [a].[LoginName] AS [AcctName] , [a].[NickName] AS [NickName] , [u].[StaffNumber] AS [StaffNumber] , [u].[IdNumber] AS [IdNumber] , [u].[Sex] AS [Sex] , [u].[Birthday] AS [Birthday] , [u].[Address] AS [Address] , [u].[ZipCode] AS [ZipCode] , [u].[Tel] AS [Tel] , [u].[Mobile] AS [Mobile] , [u].[Qq] AS [Qq] , [u].[Email] AS [Email] , [u].[CreateTime] AS [RegTime] , [u].[Memo] AS [Memo] , [u].[Statuz] AS [Statuz] , [u].[Name] AS [Name] , [a].[Statuz] AS [AcctStatuz] , [u].[UserType] AS [UserType] , [u].[AdministratorType] AS [AdministratorType] , [a].[UserValidate] AS [UserValidate] , [u].[HeadPortrait] AS [HeadPortrait] , [un].[Statuz] AS [UnitStatus]  FROM [SysUserExtension] [u] Left JOIN [p_Unit] [un] ON ( [u].[UnitId] = [un].[Id] ) AND ( [un].[IsDeleted] = @IsDeleted2 )  Left JOIN [p_Unit] [pun] ON ( [un].[PId] = [pun].[Id] ) AND ( [pun].[IsDeleted] = @IsDeleted2 )  Left JOIN [SysUserInfo] [a] ON ( [u].[Id] = [a].[UserExtensionId] )   WHERE ( [u].[Statuz] >= @Statuz0 )  AND ( [u].[Id] = @Id1 )  AND ( [u].[IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:0 [Type]:Int32    
[Name]:@Id1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.422
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [AreaId],[PId],[UnitType],[IndustryId],[Code],[Name],[Brief],[PinYin],[PinYinBrief],[Legal],[Address],[ZipCode],[Url],[Introduction],[Logo],[VipGrade],[OrganizationCode],[LoginPic],[Mobile],[ContactUser],[Tel],[Email],[Position],[TrafficMap],[EmployeeNum],[UserId],[RegTime],[Memo],[Statuz],[Sort],[SourceType],[TownName],[ThirdUnitId],[ThirdUnitName],[SubjectNature],[StreetTown],[IsCountyManager],[AuthStatuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [p_Unit]  WHERE ( [Id] = @Id0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.934
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Insert"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:INSERT INTO [b_UserActionLog]  
           ([UserId],[AccountName],[UserIp],[Type],[Statuz],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id])
     VALUES
           (@UserId,@AccountName,@UserIp,@Type,@Statuz,@IsDeleted,@CreateId,@CreateBy,@CreateTime,@ModifyId,@ModifyBy,@ModifyTime,@Version,@Id) ; 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@AccountName [Value]:yucai [Type]:String    
[Name]:@UserIp [Value]:::ffff:127.0.0.1 [Type]:String    
[Name]:@Type [Value]:1 [Type]:Int32    
[Name]:@Statuz [Value]:1 [Type]:Int32    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@CreateId [Value]:0 [Type]:Int64    
[Name]:@CreateBy [Value]: [Type]:String    
[Name]:@CreateTime [Value]:2025/8/28 18:51:39 [Type]:DateTime    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:51:39 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.940
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [a].[IsDeleted],[a].[Name],[a].[Description],[a].[OrderSort],[a].[Dids],[a].[AuthorityScope],[a].[Enabled],[a].[CreateId],[a].[CreateBy],[a].[CreateTime],[a].[ModifyId],[a].[ModifyBy],[a].[ModifyTime],[a].[RoleType],[a].[RoleId],[a].[ModuleName],[a].[ModuleSort],[a].[Id] FROM [SysRole] [a] Inner JOIN [SysUserRole] [b] ON ( [a].[RoleId] = [b].[RoleId] ) AND ( [b].[IsDeleted] = @IsDeleted1 )   WHERE ( [b].[UserId] = @UserId0 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.947
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  TOP 1 [UserId],[ContiLoginCount],[TotalLoginCount],[LoginCount],[ExtendField1],[ExtendField2],[ExtendField3],[ExtendField4],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_UserActionStatic]  WHERE ( [UserId] = @UserId0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.954
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Update"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:UPDATE [b_UserActionStatic]  SET
           [UserId]=@UserId,[ContiLoginCount]=@ContiLoginCount,[TotalLoginCount]=@TotalLoginCount,[LoginCount]=@LoginCount,[ExtendField1]=@ExtendField1,[ExtendField2]=@ExtendField2,[ExtendField3]=@ExtendField3,[ExtendField4]=@ExtendField4,[IsDeleted]=@IsDeleted,[ModifyId]=@ModifyId,[ModifyBy]=@ModifyBy,[ModifyTime]=@ModifyTime,[Version]=@Version  WHERE [Id]=@Id 
[Pars]:
[Name]:@UserId [Value]:*************** [Type]:Int64    
[Name]:@ContiLoginCount [Value]:4 [Type]:Int32    
[Name]:@TotalLoginCount [Value]:217 [Type]:Int32    
[Name]:@LoginCount [Value]:1846 [Type]:Int32    
[Name]:@ExtendField1 [Value]:0 [Type]:Int32    
[Name]:@ExtendField2 [Value]:0 [Type]:Int32    
[Name]:@ExtendField3 [Value]: [Type]:String    
[Name]:@ExtendField4 [Value]: [Type]:String    
[Name]:@IsDeleted [Value]:False [Type]:Boolean    
[Name]:@ModifyId [Value]:0 [Type]:Int64    
[Name]:@ModifyBy [Value]: [Type]:String    
[Name]:@ModifyTime [Value]:2025/8/28 18:51:39 [Type]:DateTime    
[Name]:@Version [Value]:0 [Type]:Int32    
[Name]:@Id [Value]:1 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:39.964
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT  [r].[Name] AS [Role] , [r].[RoleId] AS [RoleId] , [m].[LinkUrl] AS [Url] , [rmp].[Id] AS [Id]  FROM [SysRoleModulePermission] [rmp] Left JOIN [SysModules] [m] ON ( [rmp].[ModuleId] = [m].[Id] )  Left JOIN [SysRole] [r] ON ( [rmp].[RoleId] = [r].[RoleId] )   WHERE (((( [rmp].[IsDeleted] = @IsDeleted0 ) AND ( [m].[IsDeleted] = @IsDeleted1 )) AND ( [r].[IsDeleted] = @IsDeleted2 )) AND  ([rmp].[RoleId] IN (370,370,370,370,30,30,30,30,30,30,30,3200,352,352,352,352,352,352,352,354,354,354,354,354,354,354,350,350,350,350,350,350,350,353,353,353,353,353,353,351,351,351,351,351,351,351,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:40.275
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [UserId],[RoleId],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Id] FROM [SysUserRole]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [UserId] = @UserId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UserId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:40.332
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[RoleId],[ModuleId],[PermissionId],[Id] FROM [SysRoleModulePermission]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND  ([RoleId] IN (30,350,354,351,352,353,370,3200,355)) ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:40.942
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Code],[Name],[IsButton],[IsHide],[IskeepAlive],[Func],[OrderSort],[Icon],[Description],[Enabled],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[IsDeleted],[SqlText],[PageCode],[PlatformType],[AppType],[Pid],[Mid],[Id] FROM [SysPermission]  WHERE (( [AppType] = @AppType0 ) AND  ([Id] IN (100000000000010,100000000001001,100000000001002,100000000001003,100000000001004,100000000000020,100000000002001,100000000002002,100000000002017,100000000002008,100000000002009,100000000002010,100000000003001,100000000000030,100000000000040,100000000004001,100000000004003,100000000004004,100000000000050,100000000005001,100000000000060,100000000006001,100000000006002,100000000006003,100000000006004,100000000000070,100000000007004,100000000007005,100000000007006,100000000007007,100000000007017,100000000007018,100000000007019,100000000002011,100000000002012,100000000002013,100000000002014,100000000002015,100000000007001,100000000004002,100000000007002,100000000007014,100000000007020,100000000007021,814852328300482560,814852564905365504,814852943126728704,814853925042655232,814853692145537024,814855159220801536,814855268880879616,814855791944142848,814855881240875008,814916365319147520,815152703427579904,815152216284336128,814917889516638208,827558788750905344,827558788880928768,827558790864834560,840599930178375680,840600506052120576,842694325652426752,827558797206622208,815512156001800192,815512500404490240,815512620718100480,815512817057665024,844961710316982272,844957453845008384,827558795155607552,844957620178522112,844966541932892160,844966811886686208,844972844101144576,844972973948407808,844973424735424512,844973591006023680,844973895587991552,844974175394205696,844974402880671744,844974534216912896,844974696779747328,844974828485087232,844975023654440960,844975297731235840,844975449124638720,844975635729223680,844975797407059968,844975728242987008,844976309716127744,844976490087976960,844976660540297216,844976914006282240,844977126699438080,844977251022802944,844977462650605568,844977581781422080,844977790431268864,844978207466721280,844978326723366912,844977922153385984,844978006509228032,844978402241810432,844978933500743680,844979146500083712,844979303874564096,845255096391438336,844979454731096064,844979648616992768,844979828049317888,844980287262691328,844980359450857472,844980598295498752,844982125559025664,844981921363529728,844981743516651520,844981082699862016,844981392927363072,844980852977831936,844983416607739904,844983343421329408,844982957755076608,844982807137619968,844982595073609728,844982459761168384,844982262398193664,844983686142103552,844983827448205312,844983906800242688,844984091039240192,844984272614854656,844984515939012608,844984636638498816,844984773846765568,844984942273236992,844985090730627072,844985239972352000,844985309048344576,844985864302891008,844986081353928704,844986204750352384,844986319925940224,844986470987993088,844988224278368256,844988795462881280,844988925083652096,844989069602590720,844992020215762944,844992477537505280,844992359836946432,844992873186201600,844992931449278464,844993347960442880,844993496422027264,844997017473126400,844997280166580224,844997405890842624,844997569112182784,844997754236178432,844997884142161920,844997950672211968,844998050886717440,844998119732023296,852931579138281472,855107575388049408,855107866284003328,870714919371149312,871030740077252608,872554852792668160,875322682860638208,853564351586504704)) ) 
[Pars]:
[Name]:@AppType0 [Value]:1 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.128
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId] FROM [wf_ChildUnitDisable]  WHERE (( [IsDeleted] = @IsDeleted0 ) AND ( [DisableUnitId] = @DisableUnitId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@DisableUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.138
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [M].[Id] AS [ModuleId] , [M].[Name] AS [ModuleName] , [M].[Logo] AS [Logo] , [P].[Id] AS [ProcessId] , [P].[ProcessName] AS [ProcessName] , ISNULL([PN].[Id],[PAU].[ProcessNodeId]) AS [ProcessNodeId] , ISNULL([PN].[NodeShowName],@MethodConst9) AS [ProcessNodeName] , ISNULL([PN].[NodeType],@MethodConst10) AS [NodeType] , ISNULL([PN].[TreatHandle],@MethodConst11) AS [TreatHandle] , ISNULL([PN].[TreatHandleUrl],@MethodConst12) AS [TreatHandleUrl] , ISNULL([PN].[StopHandle],@MethodConst13) AS [StopHandle] , ISNULL([PN].[StopHandleUrl],@MethodConst14) AS [StopHandleUrl] , ( CASE  WHEN ( [PRS].[Sort] IS NULL ) THEN @MethodConst16  ELSE [PAU].[ProcessNodeId] END ) AS [Sort] , [PN].[IsBegin] AS [IsBegin] , [M].[Sort] AS [ModuleSort]  FROM [wf_ProjectAuditUser] [PAU] Inner JOIN [wf_Process] [P] ON ((( [PAU].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted0 )) AND ( [P].[Statuz] = @Statuz1 )) AND ( [P].[IsDeleted] = @IsDeleted8 )  Inner JOIN [wf_Module] [M] ON (( [P].[ModuleId] = [M].[Id] ) AND ( [M].[IsDeleted] = @IsDeleted2 )) AND ( [M].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessNode] [PN] ON (( [PAU].[ProcessNodeId] = [PN].[Id] ) AND ( [PN].[IsDeleted] = @IsDeleted3 )) AND ( [PN].[IsDeleted] = @IsDeleted8 )  Left JOIN [wf_ProcessReturnSet] [PRS] ON (( [PN].[Id] = [PRS].[ProcessNodeId] ) AND ( [PRS].[IsDeleted] = @IsDeleted4 )) AND ( [PRS].[IsDeleted] = @IsDeleted8 )   WHERE ((NOT (1=2)  AND( [PAU].[IsDeleted] = @IsDeleted6 )) AND ( [PAU].[AuditUserId] = @AuditUserId7 ))  AND ( [PAU].[IsDeleted] = @IsDeleted8 )) MergeTable  ORDER BY [ModuleSort],[Sort] 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@Statuz1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted6 [Value]:False [Type]:Boolean    
[Name]:@AuditUserId7 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted8 [Value]:False [Type]:Boolean    
[Name]:@MethodConst9 [Value]: [Type]:String    
[Name]:@MethodConst10 [Value]:0 [Type]:Int32    
[Name]:@MethodConst11 [Value]: [Type]:String    
[Name]:@MethodConst12 [Value]: [Type]:String    
[Name]:@MethodConst13 [Value]: [Type]:String    
[Name]:@MethodConst14 [Value]: [Type]:String    
[Name]:@MethodConst16 [Value]:0 [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.232
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessId],[ProcessNodeId],[Statuz],[StatuzDesc],[WaitOrBack],[IsEnable],[Sort],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProcessStatuz]  WHERE ( [IsDeleted] = @IsDeleted0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.408
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.458
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:667811814981765 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.510
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.560
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleId],[Name],[ShowName],[ProcessIds],[Unittype],[SourceData],[UseType],[PageType],[PageSize],[TotalName],[TotalCounmn],[UseUnitId],[Logo],[Statuz],[DefaultSort],[DefaultWhere],[PkId],[SortType],[UseUnitField],[UseUserField],[IsSubpage],[SearchType],[Url],[FieldCode],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_PageDefinition]  WHERE (( [Statuz] = @Statuz0 ) AND ( [ModuleId] = @ModuleId1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Statuz0 [Value]:1 [Type]:Int32    
[Name]:@ModuleId1 [Value]:658679024259205 [Type]:Int64    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.615
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:658679134371973 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.665
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:702534294601861 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.717
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:705091520331909 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.769
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:703912462606469 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.820
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:674228238864517 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.879
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:675249294651525 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.930
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:701431378894981 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.981
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [BusinessType],[ProcessId],[ProcessNodeId],[AuditUnitId],[AuditUserId],[Remark],[ModuleId],[GroupValue],[TypeBox],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_ProjectAuditUser]  WHERE (((( [AuditUserId] = @AuditUserId0 ) AND ( [IsDeleted] = @IsDeleted1 )) AND ( [BusinessType] = @BusinessType2 )) AND ( [ModuleId] = @ModuleId3 ))  AND ( [IsDeleted] = @IsDeleted4 ) 
[Pars]:
[Name]:@AuditUserId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@BusinessType2 [Value]:3 [Type]:Int32    
[Name]:@ModuleId3 [Value]:698593460789381 [Type]:Int64    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:41.996
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [GPS].[ProcessId],[GPS].[GroupName],[GPS].[TypeCode],[GPS].[TypeBox],[GPS].[IsDeleted],[GPS].[CreateId],[GPS].[CreateBy],[GPS].[CreateTime],[GPS].[ModifyId],[GPS].[ModifyBy],[GPS].[ModifyTime],[GPS].[Version],[GPS].[Id] FROM [wf_GroupProcessSet] [GPS] Inner JOIN [wf_Process] [P] ON ( [GPS].[ProcessId] = [P].[Id] ) AND ( [P].[IsDeleted] = @IsDeleted3 )   WHERE ((( [GPS].[IsDeleted] = @IsDeleted0 ) AND ( [P].[IsDeleted] = @IsDeleted1 )) AND ( [P].[Statuz] = @Statuz2 ))  AND ( [GPS].[IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:42.015
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ProcessName],[CreateUnitId],[ProcessLogo],[Sign],[UseUnitId],[Usage],[Statuz],[ModuleId],[InputDataType],[IsShowHistoryFile],[FieldCode],[TypeCode],[IsSignature],[IsControlAmount],[IsOpen],[NodeConfig],[LineConfig],[WriteSourceFundStatuz],[ProjectListCode],[PSort],[IsOpenControl],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [wf_Process]  WHERE ((((( [IsDeleted] = @IsDeleted0 ) AND ( [UseUnitId] = @UseUnitId1 )) AND ( [Usage] = @Usage2 )) AND ( [Statuz] = @Statuz3 )) AND ( [IsOpenControl] = @IsOpenControl4 ))  AND ( [IsDeleted] = @IsDeleted5 ) 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[Name]:@UseUnitId1 [Value]:*************** [Type]:Int64    
[Name]:@Usage2 [Value]:2 [Type]:Int32    
[Name]:@Statuz3 [Value]:1 [Type]:Int32    
[Name]:@IsOpenControl4 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:42.734
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [StaffNumber],[UnitId],[IdNumber],[Name],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[UserId],[RegTime],[Memo],[Statuz],[VerifiedMobile],[UserType],[AdministratorType],[Wechat],[HeadPortrait],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [SysUserExtension]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:42.747
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[StaffNumber],[IdNumber],[Sex],[Birthday],[Address],[ZipCode],[Tel],[Mobile],[Qq],[Email],[RegTime],[Memo],[Statuz],[NickName],[CreateId],[AcctStatuz],[UserType],[AdministratorType],[UserValidate],[UnitStatus] FROM [V_UserDetail]  WHERE ( [Id] = @Id0 ) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:42.754
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Id],[Name],[AreaId],[UnitId],[UnitName],[UnitType],[UnitPId],[UnitPName],[AcctId],[AcctName],[RoleId],[RoleName],[StaffNumber],[IdNumber],[Mobile],[Qq],[Email],[Statuz],[NickName],[AcctStatuz],[Sort],[UserType],[AdministratorType],[UserValidate] FROM [V_UserListTemp]  WHERE (( [Id] = @Id0 ) AND ( [RoleId] IS NOT NULL )) 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:45.687
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT * FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted1 )   WHERE ( [scheme].[SchoolId] = @SchoolId0 )  AND ( [scheme].[IsDeleted] = @IsDeleted1 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted2 )) CountTable  
[Pars]:
[Name]:@SchoolId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:45.701
LogLevel：Information
Message：------------------ 
 User:["yucai"]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT *,ROW_NUMBER() OVER(ORDER BY  CreateTime DESC ) AS RowIndex  FROM  (SELECT  [scheme].[Id] AS [Id] , [scheme].[IsDeleted] AS [IsDeleted] , [scheme].[CreateTime] AS [CreateTime] , [scheme].[ModifyTime] AS [ModifyTime] , [scheme].[CreateBy] AS [CreateBy] , [scheme].[ModifyBy] AS [ModifyBy] , [scheme].[SchemeYear] AS [SchemeYear] , [scheme].[SchemeNo] AS [SchemeNo] , [scheme].[SolicitedNum] AS [SolicitedNum] , [scheme].[SolicitedDeadline] AS [SolicitedDeadline] , [scheme].[ReleaseTime] AS [ReleaseTime] , [scheme].[SolicitedStatuz] AS [SolicitedStatuz] , [scheme].[AgreeNum] AS [AgreeNum] , [scheme].[AgreeRate] AS [AgreeRate] , [scheme].[PurchaseMethod] AS [PurchaseMethod] , [scheme].[PurchaseMethodName] AS [PurchaseMethodName] , [scheme].[FilingStatuz] AS [FilingStatuz] , [scheme].[FilingExplanation] AS [FilingExplanation] , [scheme].[ParentOpinion] AS [ParentOpinion] , [scheme].[Memo] AS [Memo]  FROM [x_UniformScheme] [scheme] Inner JOIN [p_Unit] [school] ON ( [scheme].[SchoolId] = [school].[Id] ) AND ( [school].[IsDeleted] = @IsDeleted1 )   WHERE ( [scheme].[SchoolId] = @SchoolId0 )  AND ( [scheme].[IsDeleted] = @IsDeleted1 )) MergeTable   WHERE ( [IsDeleted] = @IsDeleted2 )) T WHERE RowIndex BETWEEN 1 AND 10 
[Pars]:
[Name]:@SchoolId0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:57.217
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM  (SELECT  [SchoolId] AS [SchoolId] , COUNT([Id]) AS [Num]  FROM [x_UniformShelf]  WHERE (((( [UseStatuz] = @UseStatuz0 ) AND ( [IsShow] = @IsShow1 )) AND ( [AuditStatuz] =@constant3)) AND ( [IsDeleted] = @IsDeleted4 ))  AND ( [IsDeleted] = @IsDeleted5 )GROUP BY [SchoolId] ) MergeTable   WHERE ( [Num] >= @Num6 ) 
[Pars]:
[Name]:@UseStatuz0 [Value]:1 [Type]:Int32    
[Name]:@IsShow1 [Value]:1 [Type]:Int32    
[Name]:@constant3 [Value]:20 [Type]:Int32    
[Name]:@IsDeleted4 [Value]:False [Type]:Boolean    
[Name]:@IsDeleted5 [Value]:False [Type]:Boolean    
[Name]:@Num6 [Value]:5 [Type]:Int32    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:57.319
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [ModuleName],[ModuleCode],[TypeName],[TypeCode],[ConfigType],[UnitType],[ConfigValue],[Statuz],[UnitId],[UserId],[RegDate],[ValueType],[ComboValues],[Remark],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [b_ConfigSet]  WHERE ((( [UnitType] = @UnitType0 ) AND ( [ModuleCode] = @ModuleCode1 )) AND ( [Statuz] = @Statuz2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@UnitType0 [Value]:0 [Type]:Int32    
[Name]:@ModuleCode1 [Value]:8002 [Type]:String    
[Name]:@Statuz2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:57.331
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ((( [Pid] = @Const0 ) AND ( [CateType] <> @CateType1 )) AND ( [IsShowBottom] = @IsShowBottom2 ))  AND ( [IsDeleted] = @IsDeleted3 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:3 [Type]:Int32    
[Name]:@IsShowBottom2 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:57.335
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:51:57 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:57.349
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [C].[CateType] = @CateType2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:51:57 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@CateType2 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:59.212
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE ( [Code] = @Code0 )  AND ( [IsDeleted] = @IsDeleted1 ) 
[Pars]:
[Name]:@Code0 [Value]:1010 [Type]:Int32    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:59.222
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE [Id] = @InPara0    AND ( [IsDeleted] = @IsDeleted0 ) 
[Pars]:
[Name]:@InPara0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:59.264
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT [Pid],[Name],[Depth],[Path],[Sort],[Icon1],[Icon2],[ConfigCode],[CateType],[UserId],[UnitTypeId],[Code],[IsShowBottom],[IsMany],[IsDeleted],[CreateId],[CreateBy],[CreateTime],[ModifyId],[ModifyBy],[ModifyTime],[Version],[Id] FROM [d_ArticleCategory]  WHERE (( [Pid] = @Const0 ) AND ( [CateType] = @CateType1 ))  AND ( [IsDeleted] = @IsDeleted2 ) 
[Pars]:
[Name]:@Const0 [Value]:0 [Type]:Int32    
[Name]:@CateType1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:59.276
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]: SELECT COUNT(1) FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment]  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) CountTable  
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:51:59 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------
Date：2025-08-28 18:51:59.280
LogLevel：Information
Message：------------------ 
 User:[""]  Table:[""]  Operate:["Query"] ConnId:["wmhyun_mssql_1"]【SQL语句】: 
 "
[Sql]:SELECT * FROM (SELECT  [A].[Id] AS [Id] , [A].[Title] AS [Title] , [A].[ShortTitle] AS [ShortTitle] , [A].[Cid] AS [Cid] , [C].[Code] AS [Code] , [C].[CateType] AS [CateType] , [C].[IsMany] AS [IsMany] , [A].[RegDate] AS [RegDate] , [A].[IsDeleted] AS [IsDeleted] , [A].[Source] AS [Source] , [A].[Author] AS [Author] , [A].[Statuz] AS [Statuz] , [A].[Hits] AS [Hits] , [A].[Sort] AS [Sort] , [A].[UserId] AS [UserId] , [A].[BeginTime] AS [BeginTime] , [A].[ImageUrl] AS [ImageUrl] , [C].[Name] AS [CategoryName] , [A].[ModifyTime] AS [ModifyTime] , [A].[Remark] AS [Remark] , [A].[Attachment] AS [Attachment] ,ROW_NUMBER() OVER(ORDER BY [A].[ModifyTime] DESC) AS RowIndex  FROM [d_Article] [A] Inner JOIN [d_ArticleCategory] [C] ON ( [A].[Cid] = [C].[Id] ) AND ( [C].[IsDeleted] = @IsDeleted3 )   WHERE (( @Date0 >=  CAST([A].[BeginTime] AS DATE) ) AND ( [A].[Statuz] = @Statuz1 ))  AND ( [A].[Cid] = @Cid2 )  AND ( [A].[IsDeleted] = @IsDeleted3 )) T WHERE RowIndex BETWEEN 1 AND 2147483647 
[Pars]:
[Name]:@Date0 [Value]:2025/8/28 18:51:59 [Type]:DateTime    
[Name]:@Statuz1 [Value]:2 [Type]:Int32    
[Name]:@Cid2 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
"
----------------------------------------------------------------------------------------------------