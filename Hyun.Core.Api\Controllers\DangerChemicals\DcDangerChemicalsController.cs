﻿using Dm.util;
using FluentValidation;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Model;
using Hyun.Old.Util;
using Microsoft.EntityFrameworkCore.ChangeTracking.Internal;
using MongoDB.Driver;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.Util;
using Ocelot.Infrastructure.Extensions;
using SkyWalking.NetworkProtocol.V3;
using SqlSugar;
using System.Data;
using System.Linq.Expressions;
using System.Text.RegularExpressions;

namespace Hyun.Core.Api
{

    [Route("api/hyun/dcdangerchemicals")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class DcDangerChemicalsController : BaseApiController
    {
        #region 引用对象

        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IDcAuditGroupServices dcAuditGroupManager;
        private readonly IDcAuditMemberServices dcAuditMemberManager;
        private readonly ISysUserExtensionServices userManager;
        private readonly IDcAuditConditionServices dcAuditConditionManager;
        private readonly IDcApplyGrantUserServices dcApplyGrantUserManager;
        private readonly ISysUserRoleServices userInRoleManager;
        private readonly IBConfigSetServices configSetManager;
        private readonly IDcDepositAddressServices dcDepositAddressManager;
        private readonly IDcCompanyServices dcCompanyManager;
        private readonly IVUnitListServices vUnitListManager;
        private readonly IDcSchoolCatalogServices dcSchoolCatalogManager;
        private readonly IDcSchoolModelBrandServices dcSchoolModelBrandManager;
        private readonly IVSysStatServices vSysStatManager;
        private readonly IDcSchoolMaterialServices dcSchoolMaterialManager;
        private readonly IDcSchoolMaterialModelServices dcSchoolMaterialModelManager;
        private readonly IDcApplyServices dcApplyManager;
        private readonly IDcSchoolMaterialBrandServices dcSchoolMaterialBrandManager;
        private readonly IDcMaterialsNumAuditServices dcMaterialsNumAuditManager;
        private readonly IDcScrapServices dcScrapManager;
        private readonly IDcInventoryServices dcInventoryManager;
        private readonly IDcSchoolMaterialTempServices dcSchoolMaterialTempManager;
        private readonly IDcPurchaseOrderServices dcPurchaseOrderManager;
        private readonly IPUnitServices unitManager;
        private readonly IDcBaseCatalogServices dcBaseCatalogManager;
        private readonly IDcPurchaseListServices dcPurchaseListManager;
        private readonly IDcBaseModelExtensionServices dcBaseModelExtensionManager;
        private readonly IPSchoolExtensionServices schoolExtensionManager;
        private readonly IDcPurchaseApprovalServices dcPurchaseApprovalManager;
        private readonly IDcSchoolMaterialBackLogServices dcSchoolMaterialBackLogManager;
        private readonly IDcBaseFieldConfigServices dcBaseFieldConfigManager;
        private readonly IDcTrainSafeEducationServices dcTrainSafeEducationManager;
        private readonly IDcEmergencyPlanServices dcEmergencyPlanManager;
        private readonly IDcWasteDisposalDetailServices dcWasteDisposalDetailManager;
        private readonly IDcWasteDisposalServices dcWasteDisposalManager;
        private readonly IDcApplyConfirmDetailServices dcApplyConfirmDetailManager;
        private readonly IDcBaseWasteServices dcBaseWasteManager;
        private readonly IDcWasteRecordServices dcWasteRecordManager;
        private readonly IDcModelStockHistoryServices dcModelStockHistoryManager;
        private readonly IDcUnitLicenseInfoServices dcUnitLicenseInfoManager;
        private readonly IBAttachmentServices bAttachmentManager;
        private readonly IDcBaseBrandServices dcBaseBrandManager;
        private readonly IBAttachmentConfigServices ibattachmentconfigservicesManager;
        private readonly IBAttachmentDataServices bAttachmentDataManager;
        public DcDangerChemicalsController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IBAttachmentConfigServices _ibattachmentconfigservicesManager, IDcAuditGroupServices _dcAuditGroupManager, IDcAuditMemberServices _dcAuditMemberManager, ISysUserExtensionServices _userManager, IDcAuditConditionServices _dcAuditConditionManager, IDcApplyGrantUserServices _dcApplyGrantUserManager, ISysUserRoleServices _userInRoleManager, IBConfigSetServices _configSetManager, IDcDepositAddressServices _dcDepositAddressManager, IDcCompanyServices _dcCompanyManager, IVUnitListServices _vUnitListManager, IDcSchoolCatalogServices _dcSchoolCatalogManager, IDcSchoolModelBrandServices _dcSchoolModelBrandManager, IVSysStatServices _vSysStatManager, IDcSchoolMaterialServices _dcSchoolMaterialManager, IDcSchoolMaterialModelServices _dcSchoolMaterialModelManager, IDcApplyServices _dcApplyManager, IDcSchoolMaterialBrandServices _dcSchoolMaterialBrandManager, IDcMaterialsNumAuditServices _dcMaterialsNumAuditManager, IDcScrapServices _dcScrapManager, IDcInventoryServices _dcInventoryManager, IDcSchoolMaterialTempServices _dcSchoolMaterialTempManager, IDcPurchaseOrderServices _dcPurchaseOrderManager, IPUnitServices _unitManager, IDcBaseCatalogServices _dcBaseCatalogManager, IDcPurchaseListServices _dcPurchaseListManager, IDcBaseModelExtensionServices _dcBaseModelExtensionManager, IPSchoolExtensionServices _schoolExtensionManager, IDcPurchaseApprovalServices _dcPurchaseApprovalManager, IDcSchoolMaterialBackLogServices _dcSchoolMaterialBackLogManager, IDcBaseFieldConfigServices _dcBaseFieldConfigManager, IDcTrainSafeEducationServices _dcTrainSafeEducationManager, IDcEmergencyPlanServices _dcEmergencyPlanManager, IDcWasteDisposalDetailServices _dcWasteDisposalDetailManager, IDcWasteDisposalServices _dcWasteDisposalManager, IDcApplyConfirmDetailServices _dcApplyConfirmDetailManager, IDcBaseWasteServices _dcBaseWasteManager, IDcWasteRecordServices _dcWasteRecordManager, IDcModelStockHistoryServices _dcModelStockHistoryManager, IDcUnitLicenseInfoServices _dcUnitLicenseInfoManager, IBAttachmentServices _bAttachmentManager, IDcBaseBrandServices _dcBaseBrandManager, IBAttachmentDataServices _bAttachmentDataManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            dcAuditGroupManager = _dcAuditGroupManager;
            dcAuditMemberManager = _dcAuditMemberManager;
            userManager = _userManager;
            dcAuditConditionManager = _dcAuditConditionManager;
            dcApplyGrantUserManager = _dcApplyGrantUserManager;
            userInRoleManager = _userInRoleManager;
            configSetManager = _configSetManager;
            dcDepositAddressManager = _dcDepositAddressManager;
            dcCompanyManager = _dcCompanyManager;
            vUnitListManager = _vUnitListManager;
            dcSchoolCatalogManager = _dcSchoolCatalogManager;
            dcSchoolModelBrandManager = _dcSchoolModelBrandManager;
            vSysStatManager = _vSysStatManager;
            dcSchoolMaterialManager = _dcSchoolMaterialManager;
            dcSchoolMaterialModelManager = _dcSchoolMaterialModelManager;
            dcApplyManager = _dcApplyManager;
            dcSchoolMaterialBrandManager = _dcSchoolMaterialBrandManager;
            dcMaterialsNumAuditManager = _dcMaterialsNumAuditManager;
            dcScrapManager = _dcScrapManager;
            dcInventoryManager = _dcInventoryManager;
            dcSchoolMaterialTempManager = _dcSchoolMaterialTempManager;
            dcPurchaseOrderManager = _dcPurchaseOrderManager;
            unitManager = _unitManager;
            dcBaseCatalogManager = _dcBaseCatalogManager;
            dcPurchaseListManager = _dcPurchaseListManager;
            dcBaseModelExtensionManager = _dcBaseModelExtensionManager;
            schoolExtensionManager = _schoolExtensionManager;
            dcPurchaseApprovalManager = _dcPurchaseApprovalManager;
            dcSchoolMaterialBackLogManager = _dcSchoolMaterialBackLogManager;
            dcBaseFieldConfigManager = _dcBaseFieldConfigManager;
            dcTrainSafeEducationManager = _dcTrainSafeEducationManager;
            dcEmergencyPlanManager = _dcEmergencyPlanManager;
            dcWasteDisposalDetailManager = _dcWasteDisposalDetailManager;
            dcWasteDisposalManager = _dcWasteDisposalManager;
            dcApplyConfirmDetailManager = _dcApplyConfirmDetailManager;
            dcBaseWasteManager = _dcBaseWasteManager;
            dcWasteRecordManager = _dcWasteRecordManager;
            dcModelStockHistoryManager = _dcModelStockHistoryManager;
            dcUnitLicenseInfoManager = _dcUnitLicenseInfoManager;
            bAttachmentManager = _bAttachmentManager;
            dcBaseBrandManager = _dcBaseBrandManager;
            ibattachmentconfigservicesManager = _ibattachmentconfigservicesManager;
            bAttachmentDataManager = _bAttachmentDataManager;
        }


        #endregion

        #region 后台设置


        //[HttpPost]
        //[Route("dcauditgrouplistfind")]
        ////<used>0</used>
        //public async Task<Result> DcAuditGroupList_Find([FromBody] VDcAuditGroupParam param)
        //{
        //    Result r = new Result();
        //    PageModel<VDcAuditGroup> pg = await vDcAuditGroupManager.GetPaged(param);
        //    r.data.total = pg.dataCount;
        //    r.flag = 1;
        //    r.data.rows = pg.data;
        //    r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
        //    return r;
        //}


        //[HttpPost]
        //[Route("dcauditgrouplistgetbyid")]
        ////<used>0</used>
        //public async Task<Result> DcAuditGroupList_GetById(int Id)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        ////[HttpPost]
        ////[Route("dcauditgroupinsertupdate")]
        //////<used>0</used>
        ////public async Task<Result> DcAuditGroup_InsertUpdate(LvAuditGroupModel o)
        ////{
        ////      Result r = new Result();
        ////      r.flag = 1;
        ////      r.msg = "";
        ////      return r;
        ////}


        //[HttpPost]
        //[Route("dcauditgroupdelete")]
        ////<used>0</used>
        //public async Task<Result> DcAuditGroup_Delete(int Id)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        //[HttpPost]
        //[Route("dcauditmemberlistfind")]
        ////<used>0</used>
        //public async Task<Result> DcAuditMemberList_Find([FromBody] VDcAuditMemberParam param)
        //{
        //    Result r = new Result();
        //    PageModel<VDcAuditMember> pg = await vDcAuditMemberManager.GetPaged(param);
        //    r.data.total = pg.dataCount;
        //    r.flag = 1;
        //    r.data.rows = pg.data;
        //    r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
        //    return r;
        //}


        //[HttpPost]
        //[Route("dcauditmemberlistgetbyid")]
        ////<used>0</used>
        //public async Task<Result> DcAuditMemberList_GetById(int Id)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        //[HttpPost]
        //[Route("dcauditmemberlistinsertupdate")]
        ////<used>0</used>
        //public async Task<Result> DcAuditMemberList_InsertUpdate(DcAuditMember o)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        ////[HttpPost]
        ////[Route("dcauditmemberlistbacthinsert")]
        //////<used>0</used>
        ////public async Task<Result> DcAuditMemberList_BacthInsert(AuditMemberBatch o)
        ////{
        ////      Result r = new Result();
        ////      r.flag = 1;
        ////      r.msg = "";
        ////      return r;
        ////}


        //[HttpPost]
        //[Route("dcauditmemberlistdelete")]
        ////<used>0</used>
        //public async Task<Result> DcAuditMemberList_Delete(int Id)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        //[HttpPost]
        //[Route("dcauditmemberlistbatchdelete")]
        ////<used>0</used>
        //public async Task<Result> DcAuditMemberList_BatchDelete([FromQuery] List<int> listUserId)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        //[HttpPost]
        //[Route("dcauditmemberselectfind")]
        ////<used>0</used>
        //public async Task<Result> DcAuditMemberSelect_Find(int AuditGroupId, VDcSelectUserParam param)
        //{
        //    Result r = new Result();
        //    PageModel<VDcSelectUser> pg = await vDcSelectUserManager.GetPaged(param);
        //    r.data.total = pg.dataCount;
        //    r.flag = 1;
        //    r.data.rows = pg.data;
        //    r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
        //    return r;
        //}

        /// <summary>
        /// 审核审批配置-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcauditconditionfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcAuditConditionDto>>> DcAuditCondition_Find()
        {
            var param = new DcAuditConditionParam();
            param.unitId = user.UnitId;
            var list = await dcAuditConditionManager.Find(param);
            var pageModel = new PageModel<DcAuditCondition>()
            {
                data = list,
                dataCount = list.Count
            };
            return baseSucc(pageModel.ConvertTo<DcAuditConditionDto>(mapper), (long)list.Count);
        }

        /// <summary>
        /// 审核审批设置-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcauditconditioninsertupdate")]
        //<used>1</used>
        public async Task<Result<string>> DcAuditCondition_InsertUpdate([FromBody] List<DcAuditConditionDto> o)
        {
            Result r = new Result();
            var successnum = 0;
            if (o != null && o.Count > 0)
            {
                foreach (var m in o)
                {
                    r = await dcAuditConditionManager.Set(m.AuditCode, m.MinValue, m.MaxValue, user.UserId, user.UnitId);
                    if (r.flag == 1)
                    {
                        successnum += 1;
                    }
                }
                if (successnum == o.Count)
                {
                    return baseSucc<string>("配置成功", 1);
                }
                else
                {
                    return baseSucc<string>($"配置成功{successnum}条配置;失败{o.Count - successnum}条配置", 1);
                }
            }
            else
            {
                return baseFailed<string>("没有需要保存的数据");
            }
        }

        #region 领用人配置、发送人配置

        /// <summary>
        /// 获取领用人、发送人列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplygrantuserfind")]
        //<used>1</used>
        public async Task<Result> DcApplyGrantUser_Find([FromBody] DcApplyGrantUserParam param)
        {
            Result r = new Result();
            //权限验证
            if (param == null)
            {
                param = new DcApplyGrantUserParam();
            }
            param.SchoolId = user.UnitId;
            var list = await dcApplyGrantUserManager.GetApplyGrantUserList(param);
            r.data.rows = list;
            r.data.total = param.totalCount;
            r.flag = 1;
            r.msg = "查询成功。";
            return r;
        }

        /// <summary>
        /// 添加领用人、发放人-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplygrantuseradd")]
        //<used>1</used>
        public async Task<Result> DcApplyGrantUser_Add([FromBody] DcApplyGrantUserDto model)
        {
            Result r = new Result();
            var entityUser = userManager.GetById(model.MemberUserId);
            if (entityUser == null)
            {
                r.flag = 0;
                r.msg = "所选用户不存在。";
                return r;
            }
            if (model.Remark != null && model.Remark.Length > 500)
            {
                r.flag = 0;
                r.msg = "备注字符长度必须不大于500。";
                return r;
            }
            var RoleId = model.UserType == 1 ? 352 : 354;

            var modelUserRoleList = await userInRoleManager.QueryPage(a => a.UserId == model.MemberUserId && a.RoleId == RoleId);
            if (modelUserRoleList == null || modelUserRoleList.data == null || modelUserRoleList.dataCount == 0)
            {
                r.flag = 0;
                r.msg = model.UserType == 1 ? "所选用户不是“危化品领用人”。" : "所选用户不是“危化品保管人”。";
                return r;
            }
            //string.Format(" SchoolId = {0} AND UserType = {1} AND MemberUserId = {2}", UnitId, o.UserType, o.MemberUserId)
            var paramGrantUser = new DcApplyGrantUserParam();
            paramGrantUser.SchoolId = user.UnitId;
            paramGrantUser.UserType = model.UserType;
            paramGrantUser.MemberUserId = model.MemberUserId;
            var applyGrantUser = await dcApplyGrantUserManager.GetPaged(paramGrantUser);
            if (applyGrantUser != null && applyGrantUser.data != null && applyGrantUser.data.Count > 0)
            {
                r.flag = 0;
                r.msg = "所选用户已存在。";
                return r;
            }
            if (model.UserType == 2)
            {
                int grantMode = 0;
                var paramConfigSet = new BConfigSetParam();
                paramConfigSet.Statuz = 1;
                paramConfigSet.ModuleCode = "9";
                paramConfigSet.TypeCode = "WHPFFMS";
                var configList = await configSetManager.Find(paramConfigSet);
                if (configList != null && configList.Count > 0)
                {
                    long countyId = 0;
                    //查找上级单位自定义配置
                    var entityUnit = await unitManager.QueryById(user.UnitId);
                    if (entityUnit != null)
                    {
                        countyId = entityUnit.PId;
                    }
                    var unitConfig = configList.Find(f => f.ConfigType == 1 && f.UnitId == countyId);
                    if (unitConfig != null)
                    {
                        int.TryParse(unitConfig.ConfigValue, out grantMode);
                    }
                    else
                    {
                        //单位未配置时，读取系统默认配置
                        var systemConfig = configList.Find(f => f.ConfigType == 0);
                        if (systemConfig != null)
                        {
                            int.TryParse(systemConfig.ConfigValue, out grantMode);
                        }
                    }
                }
                //发放人最多只能配置两人
                var paramApplyGrantUser2 = new DcApplyGrantUserParam();
                paramApplyGrantUser2.SchoolId = user.UnitId;
                paramApplyGrantUser2.UserType = 2;
                paramApplyGrantUser2.pageSize = int.MaxValue;
                var list = await dcApplyGrantUserManager.GetPaged(paramApplyGrantUser2);
                if (list != null && list.data != null && list.data.Count >= grantMode)
                {
                    r.flag = 0;
                    r.msg = "发放人最多只能配置" + grantMode + "人。";
                    return r;
                }
            }
            var entityGrantuser = new DcApplyGrantUser
            {
                SchoolId = user.UnitId,
                MemberUserId = model.MemberUserId,
                Remark = model.Remark,
                UserType = model.UserType,
                UserId = user.UserId,
                RegDate = DateTime.Now
            };
            var resultAdd = await dcApplyGrantUserManager.Add(entityGrantuser);
            if (resultAdd > 0)
            {
                r.flag = 1;
                r.msg = "操作成功。";
            }
            else
            {
                r.flag = 0;
                r.msg = "添加失败。";
            }
            return r;
        }

        /// <summary>
        /// 删除领用人、发放人-删除
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplygrantuserdelete")]
        //<used>1</used>
        public async Task<Result> DcApplyGrantUser_Delete(long id)
        {
            Result r = new Result();
            var entityGrantuser = await dcApplyGrantUserManager.GetById(id);
            if (entityGrantuser != null && entityGrantuser.SchoolId == user.UnitId)
            {
                if (await dcApplyGrantUserManager.DeleteById(entityGrantuser.Id))
                {
                    r.flag = 1;
                    r.msg = "删除成功。";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "删除失败。";
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "数据不存在。";
            }
            return r;
        }

        #endregion
        /// <summary>
        /// 获取同领用人、发放人下拉框数据--查询
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplygrantusercomboget")]
        //<used>1</used>
        public async Task<Result<object>> DcApplyGrantUserCombo_Get(int type)
        {
            var data = await dcApplyGrantUserManager.GetApplyGrantUserCombo(type, user.UnitId, user.ID);
            if (data != null && data.Rows != null && data.Rows.Count > 0)
            {
                return baseSucc<object>(data, data.Rows.Count);
            }
            return baseFailed<object>("未查询到数据");
        }

        /// <summary>
        /// 获取工作指导文件列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcworkguidefind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcWorkGuide_Find([FromBody] BAttachmentParam param)
        {
            Result r = new Result();

            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.UnitId = user.UnitPId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.UnitId = user.UnitId;
            }
            param.FileCategory = FileCategory.DcWorkGuide.ObjToInt();
            param.IsDelete = 0;
            var list = await bAttachmentManager.GetPaged(param);
            PageModel<object> page = new PageModel<object>();
            if (list != null && list.data != null && list.data.Count > 0)
            {
                page.data = list.data.Select(t => new { t.Id, t.Title, t.Path }).ToList<object>();
                page.dataCount = list.dataCount;
            }
            return baseSucc(page, page.dataCount);
        }


        #endregion

        #region 存放地址管理
        /// <summary>
        /// 存放地点-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcdepositaddressfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcDepositAddressDto>>> DcDepositAddress_Find([FromBody] DcDepositAddressParam param)
        {
            param.unitId = user.UnitId;
            PageModel<DcDepositAddress> pg = await dcDepositAddressManager.GetPaged(param);
            return baseSucc(pg.ConvertTo<DcDepositAddressDto>(mapper), pg.dataCount);
        }

        /// <summary>
        /// 获取地址配置列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcdepositaddresslistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcDepositAddressDto>>> DcDepositAddressList_Find([FromBody] DcDepositAddressParam param)
        {
            Result r = new Result();
            //权限验证

            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.unitId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                return baseSucc(new PageModel<DcDepositAddressDto>(), 0);
            }
            var list = await dcDepositAddressManager.SearchDcDepositAddressList(param);
            //查询附件信息
            if (list != null && list.Count > 0)
            {
                var paramAttach = new BAttachmentParam();
                paramAttach.FileCategory = FileCategory.ScenePhoto.ObjToInt();
                paramAttach.IsDelete = 0;
                paramAttach.ModuleType = 10;
                if (user.UnitTypeId == UnitTypes.School.ObjToInt())
                {
                    paramAttach.unitId = user.UnitId;
                }
                paramAttach.pageSize = int.MaxValue;
                var listAttr = await bAttachmentManager.GetPaged(paramAttach);
                if (listAttr != null && listAttr.data != null && listAttr.data.Count > 0)
                {
                    foreach (var l in list)
                    {
                        l.ListAttachment = listAttr.data.Where(m => m.ObjectId == l.Id).ToList();
                    }
                }
            }
            var page = new PageModel<DcDepositAddressDto>();
            page.data = list;
            page.dataCount = param.totalCount;
            return baseSucc(page, param.totalCount);
        }

        /// <summary>
        /// 存放地点设置-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcdepositaddressinsertupdate")]
        //<used>1</used>
        public async Task<Result<string>> DcDepositAddress_InsertUpdate([FromBody] DcDepositAddressDto o)
        {
            var validatorResult = new DcDepositAddressValidator().Validate(o);
            if (!validatorResult.IsValid)
            {
                return baseFailed<string>(string.Join("<br/>", validatorResult.Errors.Select(m => m.ErrorMessage)));
            }
            var param = new DcDepositAddressParam();
            param.unitId = user.UnitId;
            param.Statuz = 1;
            param.Id = o.Id;
            param.Address = o.Address;
            param.OptType = 1;
            DcDepositAddress entity = new DcDepositAddress();
            var list = await dcDepositAddressManager.Find(param);
            if (list != null && list.Count > 0)
            {
                var listTemp = list.Where(m => m.Address == o.Address && m.Id != o.Id);
                if (listTemp != null && listTemp.Count() > 0)
                {
                    return baseFailed<string>("已存在相同的存放地点，不可重复录入。");
                }
            }
            if (o.Id > 0)
            {
                bool isTrue = false;
                if (list != null && list.Count > 0)
                {
                    var listTemp = list.Where(m => m.Id == o.Id);
                    if (listTemp != null && listTemp.Count() > 0)
                    {
                        entity = listTemp.FirstOrDefault();
                        list.Remove(entity);
                        if (entity.Statuz != 1)
                        {
                            isTrue = true;
                        }
                    }
                    if (list.Count > 0)
                    {
                        isTrue = true;
                    }
                }
                if (isTrue)
                {
                    return baseFailed<string>("找不到您要修改的数据");
                }
            }
            //处理附件
            List<BAttachment> delAttachment = new List<BAttachment>();
            List<BAttachment> addAttachment = new List<BAttachment>();
            if (entity.Id > 0)
            {
                var oldAttachmentList = await bAttachmentManager.Find(m => m.IsDelete == 0 && m.IsDeleted == false && m.ObjectId == entity.Id && m.FileCategory == FileCategory.ScenePhoto.ObjToInt());
                if (oldAttachmentList != null && oldAttachmentList.Count > 0)
                {
                    if (o.AttachmentIdList == null)
                    {
                        o.AttachmentIdList = new List<long>();
                    }
                    var listTemp = oldAttachmentList.Where(m => !o.AttachmentIdList.Contains(m.Id));
                    if (listTemp != null && listTemp.Count() > 0)
                    {
                        delAttachment.AddRange(listTemp);
                    }
                }
            }
            if (o.AttachmentIdList != null && o.AttachmentIdList.Count > 0)
            {
                //一天内有效附件
                var newAttachmentDataList = await bAttachmentDataManager.Find(n => n.UnitId == user.UnitId && n.CreateId == user.UserId && n.FileCategory == FileCategory.ScenePhoto.ObjToInt() && n.ModuleType == ModuleTypeEnum.Address.ObjToInt() && n.ModifyTime > DateTime.Now.AddDays(-1));
                if (newAttachmentDataList != null && newAttachmentDataList.Count > 0)
                {
                    foreach (var item in newAttachmentDataList)
                    {
                        if (o.AttachmentIdList.Contains(item.Id))
                        {
                            var itemTemp = bAttachmentManager.GetModel(item);
                            entity.UnitId = user.UnitId;
                            entity.UserId = user.UserId;
                            addAttachment.Add(itemTemp);
                        }
                    }
                }
            }

            entity.Address = o.Address;
            entity.Space = o.Space;
            entity.Remark = o.Remark;
            entity.LiablePerson = o.LiablePerson;
            entity.UserId = user.UserId;
            entity.RegDate = DateTime.Now;
            if (entity.Id > 0)
            {
                if (await dcDepositAddressManager.Update(entity))
                {
                    if (addAttachment.Count > 0)
                    {
                        foreach (var item in addAttachment)
                        {
                            item.Id = BaseDBConfig.GetYitterId();
                            item.ObjectId = entity.Id;
                        }
                        await bAttachmentManager.Add(addAttachment);
                    }
                    if (delAttachment.Count > 0)
                    {
                        foreach (var item in delAttachment)
                        {
                            item.IsDelete = 1;
                            item.IsDeleted = true;
                            item.ModifyBy = user.UserName;
                            item.ModifyId = user.UserId;
                            item.ModifyTime = DateTime.Now;
                            await bAttachmentManager.Update(item, new List<string>() { "IsDelete", "IsDeleted", "ModifyBy", "ModifyId", "ModifyTime" });
                        }
                    }
                    return baseSucc(entity.Id.ToString(), 0, "添加成功");
                }
                else
                {
                    return baseFailed<string>("添加失败");
                }
            }
            else
            {
                entity.Statuz = 1;
                entity.UnitId = user.UnitId;
                if (await dcDepositAddressManager.Add(entity) > 0)
                {
                    if (addAttachment.Count > 0)
                    {
                        foreach (var item in addAttachment)
                        {
                            item.Id = BaseDBConfig.GetYitterId();
                            item.ObjectId = entity.Id;
                        }
                        await bAttachmentManager.Add(addAttachment);
                    }
                    if (delAttachment.Count > 0)
                    {
                        foreach (var item in delAttachment)
                        {
                            item.IsDelete = 1;
                            item.IsDeleted = true;
                            item.ModifyBy = user.UserName;
                            item.ModifyId = user.UserId;
                            item.ModifyTime = DateTime.Now;
                            await bAttachmentManager.Update(item, new List<string>() { "IsDelete", "IsDeleted", "ModifyBy", "ModifyId", "ModifyTime" });
                        }
                    }
                    return baseSucc(entity.Id.ToString(), 0, "添加成功");
                }
                else
                {
                    return baseFailed<string>("添加失败");
                }
            }
        }

        /// <summary>
        /// 存放地点-删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcdepositaddressdelete")]
        //<used>1</used>
        public async Task<Result<string>> DcDepositAddress_Delete(long id)
        {
            var entity = await dcDepositAddressManager.GetById(id);
            if (entity == null || entity.UnitId != user.UnitId || entity.Statuz != 1)
            {
                return baseFailed<string>("找不到您要删除的数据");
            }

            var param = new DcSchoolMaterialParam();
            param.DepositAddressId = id;
            var list = await dcSchoolMaterialManager.Find(param);
            var isUpdate = false;
            if (list != null && list.Count > 0)
            {
                isUpdate = true;
            }
            else
            {
                // dc_SchoolMaterialTemp
                var paramTemp = new DcSchoolMaterialTempParam();
                paramTemp.DepositAddressId = id;
                var listTemp = await dcSchoolMaterialTempManager.Find(paramTemp);
                if (listTemp != null && listTemp.Count > 0)
                {
                    isUpdate = true;
                }
            }
            if (isUpdate)
            {
                entity.Statuz = -1;
                if (!await dcDepositAddressManager.Update(entity))
                {
                    return baseFailed<string>("删除失败");
                }
            }
            else
            {
                if (!await dcDepositAddressManager.DeleteById(entity.Id))
                {
                    return baseFailed<string>("删除失败");
                }
            }

            var paramAttach = new BAttachmentParam();
            paramAttach.FileCategory = FileCategory.ScenePhoto.ObjToInt();
            paramAttach.IsDelete = 0;
            paramAttach.ModuleType = 10;
            paramAttach.ObjectId = entity.Id;
            paramAttach.unitId = user.UnitId;
            var listAttr = await bAttachmentManager.GetPaged(paramAttach);
            if (listAttr != null && listAttr.data != null && listAttr.data.Count > 0)
            {
                listAttr.data.ForEach(n => n.IsDelete = 1);
                await bAttachmentManager.Update(listAttr.data);
            }

            return baseSucc<string>("删除成功", 0);
        }

        /// <summary>
        /// 获取地址信息-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcdepositaddressgetbyid")]
        //<used>1</used>
        public async Task<Result<DcDepositAddressDto>> DcDepositAddress_GetById(long id)
        {
            var entity = await dcDepositAddressManager.GetById(id);
            if (entity != null)
            {
                var paramAtt = new BAttachmentParam();
                paramAtt.IsDelete = 0;
                paramAtt.ObjectId = id;
                paramAtt.unitId = user.UnitId;
                paramAtt.FileCategory = FileCategory.ScenePhoto.ObjToInt();
                var listAttchment = await bAttachmentManager.Find(paramAtt);
                var footer = new List<BAttachmentDto>();
                if (listAttchment != null && listAttchment.Count > 0)
                {
                    footer = mapper.Map<List<BAttachmentDto>>(listAttchment);
                }
                return baseSucc<DcDepositAddressDto>(mapper.Map<DcDepositAddressDto>(entity), 1, "查询成功", footer);
            }
            else
            {
                return baseFailed<DcDepositAddressDto>("查询失败");
            }
        }

        #endregion

        #region 供应商管理

        /// <summary>
        /// 供应商管理列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccompanyfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcCompanyDto>>> DcCompany_Find([FromBody] DcCompanyParam param)
        {
            param.SchoolId = user.UnitId;
            var entityPid = await unitManager.QueryById(user.UnitId);
            if (entityPid != null)
            {
                param.CountyId = entityPid.PId;
            }
            param.OptType = 1;
            PageModel<DcCompany> pg = await dcCompanyManager.GetPaged(param);
            return baseSucc(pg.ConvertTo<DcCompanyDto>(mapper), pg.dataCount);
        }

        /// <summary>
        /// 根据Id获取供应商信息-查询
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccompanygetbyid")]
        //<used>1</used>
        public async Task<Result<DcCompanyDto>> DcCompany_GetById(long Id)
        {
            var company = await dcCompanyManager.GetById(Id);
            if (company != null)
            {
                return baseSucc<DcCompanyDto>(mapper.Map<DcCompanyDto>(company), 1, "查询成功");
            }
            else
            {
                return baseFailed<DcCompanyDto>("查询失败");
            }
        }

        /// <summary>
        /// 供应商新增修改-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccompanyinsertupdate")]
        //<used>1</used>
        public async Task<Result<string>> DcCompany_InsertUpdate([FromBody] DcCompanyDto o)
        {
            o.UserId = user.UserId;
            o.SchoolId = user.UnitId;
            var r = await dcCompanyManager.InsertUpdate(o);
            if (r.flag == 1)
            {
                return baseSucc(r.data.rows.ToString(), 1, r.msg);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }

        }

        /// <summary>
        /// 供应商删除-删除
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccompanydeletebyid")]
        //<used>1</used>
        public async Task<Result<string>> DcCompany_DeleteById(long Id)
        {
            var entity = await dcCompanyManager.GetById(Id);
            if (entity == null)
            {
                return baseFailed<string>("数据不存在");
            }
            if (entity.SchoolId != user.UnitId)
            {
                return baseFailed<string>("您无权操作其它单位供应商信息");
            }

            var param = new DcSchoolMaterialParam();
            param.LvCompanyId = entity.Id;
            //param.SchoolId = user.UnitId;//存在使用的就禁止删除
            var list = await dcSchoolMaterialManager.GetPaged(param);
            if (list != null && list.data != null && list.data.Count > 0)
            {
                return baseFailed<string>("单位危化品库已经存在此供应商信息不能删除！");
            }
            if (await dcCompanyManager.DeleteById(entity.Id))
            {
                return baseSucc(entity.Id.ToString(), 1, "删除成功");
            }
            else
            {
                return baseFailed<string>("删除失败！");
            }
        }

        /// <summary>
        /// 供应商启用禁用-设置
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccompanyenabled")]
        //<used>1</used>
        public async Task<Result<string>> DcCompany_Enabled(long Id)
        {
            var entity = await dcCompanyManager.GetById(Id);
            if (entity == null)
            {
                return baseFailed<string>("数据不存在");
            }
            if (entity.SchoolId != user.UnitId)
            {
                return baseFailed<string>("您无权操作其它单位供应商信息");
            }
            if (entity.Statuz == 0)
            {
                entity.Statuz = 1;
                if (await dcCompanyManager.Update(entity))
                {
                    return baseSucc(entity.Id.ToString(), 1, "启用成功");
                }
                else
                {
                    return baseFailed<string>("启用失败");
                }
            }
            else
            {
                var param = new DcSchoolMaterialParam();
                param.LvCompanyId = entity.Id;
                param.SchoolId = user.UnitId;
                param.pageSize = 1;
                var list = await dcSchoolMaterialManager.GetPaged(param);
                if (list != null && list.data != null && list.data.Count > 0)
                {
                    return baseFailed<string>("单位危化品库已经存在此供应商信息不能禁用！");
                }
                entity.Statuz = 0;
                if (await dcCompanyManager.Update(entity))
                {
                    return baseSucc(entity.Id.ToString(), 1, "禁用成功");
                }
                else
                {
                    return baseFailed<string>("禁用失败");
                }

            }
        }

        /// <summary>
        /// 单位许可证信息-查询
        /// </summary>
        /// <param name="id">供应商表Id</param>
        /// <param name="unittype">类型3：单位信息  4：供应商信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcunitlicenseinfogetcommany")]
        //<used>1</used>
        public async Task<Result<DcUnitLicenseInfoDto>> DcUnitLicenseInfo_GetCommany(long id, int unittype)
        {
            if (unittype == 4)
            {
                var entity = await dcCompanyManager.GetById(id);
                if (entity != null)
                {
                    var paramLicense = new DcUnitLicenseInfoParam();
                    paramLicense.UnitType = UnitTypes.Company.ObjToInt();
                    paramLicense.unitId = user.UnitId;
                    paramLicense.ObjId = id;
                    var list = await dcUnitLicenseInfoManager.Find(paramLicense);
                    if (list != null && list.Count > 0)
                    {
                        return baseSucc(mapper.Map<DcUnitLicenseInfoDto>(list.FirstOrDefault()), 1, "查询成功");
                    }
                    else
                    {
                        return baseSucc(2, new DcUnitLicenseInfoDto() { UnitType = 4, UnitName = entity.Name, ObjId = entity.Id }, 1, "查询成功");
                    }
                }
                else
                {
                    return baseFailed<DcUnitLicenseInfoDto>("供应商信息已不存在。");
                }
            }
            else if (unittype == 3)
            {
                var paramLicense = new DcUnitLicenseInfoParam();
                paramLicense.UnitType = UnitTypes.School.ObjToInt();
                paramLicense.unitId = user.UnitId;
                paramLicense.ObjId = id;
                var list = await dcUnitLicenseInfoManager.Find(paramLicense);
                if (list != null && list.Count > 0)
                {
                    return baseSucc(mapper.Map<DcUnitLicenseInfoDto>(list.FirstOrDefault()), 1, "查询成功");

                }
                else
                {
                    var entityUnit = await unitManager.QueryById(user.UnitId);
                    if (entityUnit != null)
                    {
                        return baseSucc(1, new DcUnitLicenseInfoDto() { UnitType = 3, UnitName = entityUnit.Name, Addresz = entityUnit.Address, ObjId = user.UnitId }, 1, "查询成功");

                    }
                    else
                    {
                        return baseFailed<DcUnitLicenseInfoDto>("单位信息不存在。");
                    }
                }
            }
            else
            {
                return baseFailed<DcUnitLicenseInfoDto>("非法操作，系统阻止。");
            }
            return baseFailed<DcUnitLicenseInfoDto>("非法操作，系统阻止。");
        }

        /// <summary>
        /// 保存供应商、单位单位证书信息-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcunitlicenseinfosave")]
        //<used>1</used>
        public async Task<Result<string>> DcUnitLicenseInfo_Save([FromBody] DcUnitLicenseInfoDto o)
        {
            //验证信息。
            if (string.IsNullOrEmpty(o.UnitName) || o.UnitName.Length < 4 || o.UnitName.Length > 200)
            {
                return baseFailed<string>("请填写单位名称，内容不能小于4个字符，并且不能大于50个字符。");
            }
            if (!string.IsNullOrEmpty(o.Addresz) && o.Addresz.Length > 200)
            {
                return baseFailed<string>("单位地址信息不能大于200个字符。");
            }
            if (string.IsNullOrEmpty(o.LicenseName) || o.LicenseName.Length < 4 || o.LicenseName.Length > 50)
            {
                return baseFailed<string>("请填写单位许可证，内容不能小于4个字符，并且不能大于50个字符。");
            }
            if (string.IsNullOrEmpty(o.LicenseNo) || o.LicenseNo.Length < 4 || o.LicenseNo.Length > 50)
            {
                return baseFailed<string>("请填写单位许可证编号，内容不能小于4个字符，并且不能大于50个字符。");
            }
            if (string.IsNullOrEmpty(o.IdType) || o.IdType.Length > 4)
            {
                return baseFailed<string>("请填写证件类型，证件类型不能大于4个字符。");
            }
            if (string.IsNullOrEmpty(o.Mobile) || o.Mobile.Length != 11)
            {
                return baseFailed<string>("经办人手机号码不正确，请正确填写经办人手机号码。");
            }
            if (string.IsNullOrEmpty(o.IdNo) || o.IdNo.Length > 18)
            {
                return baseFailed<string>("经办人身份证号码不正确，请正确填写经办人身份证号码。");
            }



            if (o.UnitType == 4)
            {
                var entity = await dcCompanyManager.GetById(o.ObjId);
                if (entity != null)
                {
                    var param = new DcUnitLicenseInfoParam();
                    param.UnitType = UnitTypes.Company.ObjToInt();
                    param.unitId = user.UnitId;
                    param.ObjId = o.ObjId;
                    var list = await dcUnitLicenseInfoManager.Find(param);
                    if (list != null && list.Count > 0)
                    {
                        list[0].UnitName = o.UnitName;
                        list[0].LicenseName = o.LicenseName;
                        list[0].LicenseNo = o.LicenseNo;
                        list[0].Mobile = o.Mobile;
                        list[0].Addresz = o.Addresz;
                        list[0].UserName = o.UserName;
                        list[0].IdType = o.IdType;
                        list[0].IdNo = o.IdNo;
                        if (await dcUnitLicenseInfoManager.Update(list[0]))
                        {
                            return baseSucc(list[0].Id.ToString(), 1, "保存成功");
                        }
                        else
                        {
                            return baseFailed<string>("保存失败，请刷新重试，如无法解决请联系客服协助。");
                        }
                    }
                    else
                    {
                        var entityLicense = mapper.Map<DcUnitLicenseInfo>(o);
                        entityLicense.UnitType = UnitTypes.Company.ObjToInt();
                        entityLicense.UnitId = user.UnitId;
                        entityLicense.UserId = user.UserId;
                        entityLicense.RegDate = DateTime.Now;
                        if (await dcUnitLicenseInfoManager.Add(entityLicense) > 0)
                        {
                            return baseSucc(entityLicense.Id.ToString(), 1, "保存成功");
                        }
                        else
                        {
                            return baseFailed<string>("保存失败，请刷新重试，如无法解决请联系客服协助。");
                        }
                    }
                }
                else
                {
                    return baseFailed<string>("修改失败，供应商信息已不存在。");
                }
            }
            else if (o.UnitType == 3)
            {

                var entity = unitManager.QueryById(o.ObjId);
                if (entity != null)
                {
                    //更新
                    var param = new DcUnitLicenseInfoParam();
                    param.UnitType = UnitTypes.School.ObjToInt();
                    param.unitId = user.UnitId;
                    param.ObjId = o.ObjId;
                    var list = await dcUnitLicenseInfoManager.Find(param);
                    if (list != null && list.Count > 0)
                    {
                        list[0].UnitName = o.UnitName;
                        list[0].LicenseName = o.LicenseName;
                        list[0].LicenseNo = o.LicenseNo;
                        list[0].Mobile = o.Mobile;
                        list[0].Addresz = o.Addresz;
                        list[0].UserName = o.UserName;
                        list[0].IdType = o.IdType;
                        list[0].IdNo = o.IdNo;
                        if (await dcUnitLicenseInfoManager.Update(list[0]))
                        {
                            return baseSucc(list[0].Id.ToString(), 1, "保存成功");
                        }
                        else
                        {
                            return baseFailed<string>("保存失败，请刷新重试，如无法解决请联系客服协助。");
                        }
                    }
                    else
                    {
                        var entityLicense = mapper.Map<DcUnitLicenseInfo>(o);
                        entityLicense.UnitType = UnitTypes.School.ObjToInt();
                        entityLicense.UnitId = user.UnitId;
                        entityLicense.UserId = user.UserId;
                        entityLicense.RegDate = DateTime.Now;
                        if (await dcUnitLicenseInfoManager.Add(entityLicense) > 0)
                        {
                            return baseSucc(entityLicense.Id.ToString(), 1, "保存成功");
                        }
                        else
                        {
                            return baseFailed<string>("保存失败，请刷新重试，如无法解决请联系客服协助。");
                        }
                    }
                }
                else
                {
                    return baseFailed<string>("保存失败，单位信息已不存在。");
                }
            }
            else
            {
                return baseFailed<string>("非法操作，系统阻止");
            }
        }
        #endregion

        #region 仓库管理
        /// <summary>
        /// 单位基础库管理-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcataloginitfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VUnitList>>> DcSchoolCatalogInit_Find([FromBody] VUnitListParam param)
        {
            var paramCatalog = new DcSchoolCatalogParam();
            var list = await dcSchoolCatalogManager.Find(paramCatalog);
            if (list != null && list.Count > 0)
            {
                param.Ids = string.Join(",", list.Select(m => m.SchoolId).Distinct());
            }
            else
            {
                param.Ids = "0";
            }
            PageModel<VUnitList> pg = await vUnitListManager.GetPaged(param);
            return baseSucc(pg.ConvertTo<VUnitList>(mapper), pg.dataCount); ;
        }

        /// <summary>
        /// 单位基础库-保存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcataloginitsave")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolCatalogInit_Save(long id)
        {
            var param = new DcSchoolCatalogParam();
            param.unitId = id;
            var list = await dcSchoolCatalogManager.Find(param);
            if (list != null && list.Count > 0)
            {
                return baseFailed<string>("当前单位基础库已添加。不可重复添加。");
            }
            else
            {
                return await DcSchoolCatalogInit_Update(id);
            }
        }

        /// <summary>
        /// 修改单位基础库-保存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcataloginitupdate")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolCatalogInit_Update(long id)
        {
            var r = await dcSchoolCatalogManager.InitData(id, user.UnitId, user.UserId);
            if (r.flag == 1)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }


        //[HttpPost]
        //[Route("dcschoolmodelbranddisablelistfind")]
        ////<used>0</used>
        //public async Task<Result> DcSchoolModelBrandDisableList_Find([FromBody] VDcSchoolModelBrandParam param)
        //{
        //    Result r = new Result();
        //    PageModel<VDcSchoolModelBrand> pg = await vDcSchoolModelBrandManager.GetPaged(param);
        //    r.data.total = pg.dataCount;
        //    r.flag = 1;
        //    r.data.rows = pg.data;
        //    r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
        //    return r;
        //}


        //[HttpPost]
        //[Route("dcmodelbrandsetstatus")]
        ////<used>0</used>
        //public async Task<Result> DcModelBrandSetStatus(int id, int statuz)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        //[HttpPost]
        //[Route("dcmodelbrandbatchsetstatus")]
        ////<used>0</used>
        //public async Task<Result> DcModelBrandBatchSetStatus(int statuz, string ids)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}

        /// <summary>
        /// 物品一二级分类下拉框数据-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogparentget")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcSchoolCatalogParent_Get()
        {
            Result r = new Result();

            var param = new VDcSchoolCatalogParentListParam();
            param.unitId = user.UnitId;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                var entityUnit = await unitManager.QueryById(user.UnitId);
                if (entityUnit != null)
                {
                    param.CountyId = entityUnit.PId;
                }
            }
            param.OptType = 1;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel { SortCode = "Path", SortType = "ASC" });
            var list = await dcSchoolCatalogManager.FindParent(param);
            if (list != null && list.Count > 0)
            {
                var data = from p in list
                           select new
                           {
                               Id = p.Id,
                               Name = p.Pname + " > " + p.Name
                           };
                PageModel<object> page = new PageModel<object>();
                page.data = data.ToList<object>();
                return baseSucc(page, page.data.Count); ;
            }
            else
            {
                return baseFailed<PageModel<object>>("未查询到数据。");
            }
        }

        /// <summary>
        /// 单位物品分类添加-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcataloginsertupdate")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolCatalog_InsertUpdate([FromBody] DcSchoolCatalogDto o)
        {
            DcSchoolCatalog entity = new DcSchoolCatalog();
            string pcode = "";
            var param = new DcSchoolCatalogParam();
            param.Id = o.Id;
            param.Pid = o.Pid;
            param.Name = o.Name;
            param.unitId = user.UnitId;
            param.OptType = 1;
            param.pageSize = int.MaxValue;
            var list = await dcSchoolCatalogManager.GetPaged(param);
            if (list != null && list.data != null && list.data.Count > 0)
            {
                //验证重名
                var listTemp = list.data.Where(m => m.Name == o.Name && m.Pid == o.Pid && m.Id != param.Id).ToList();
                if (listTemp != null && listTemp.Count > 0)
                {
                    return baseFailed<string>("已存在相同的危化品名称，不可重复录入。");
                }
                //验证父级是否存在
                listTemp = list.data.Where(m => m.Id == o.Pid).ToList();
                if (!(listTemp != null && listTemp.Count > 0))
                {
                    return baseFailed<string>("您所选择的上级分类不存在，请重新选择。");
                }
                pcode = listTemp[0].Code;
                if (o.Id > 0)
                {
                    listTemp = list.data.Where(m => m.Id == o.Id).ToList();
                    if (listTemp != null && listTemp.Count > 0)
                    {
                        entity = listTemp.FirstOrDefault();
                    }
                }
            }
            entity.Pid = o.Pid;
            entity.Name = o.Name;

            entity.UnitsMeasurement = o.UnitsMeasurement;
            entity.Nature = o.Nature;
            entity.IsNeedBack = o.IsNeedBack;
            entity.IsCommonUse = o.IsCommonUse;
            entity.Statuz = o.Statuz;
            if (o.Id > 0)
            {
                if (!(entity.Id > 0 && entity.SchoolId == user.UnitId))
                {
                    return baseFailed<string>("您要修改的危化品不存在。");
                }
                if (await dcSchoolCatalogManager.Update(entity))
                {
                    return baseSucc<string>(entity.Id.ToString(), 1, "保存成功");
                }
                else
                {
                    return baseFailed<string>("保存失败。");
                }
            }
            else
            {
                var paramCode = new DcSchoolCatalogParam();
                paramCode.Pid = o.Pid;
                paramCode.Depth = 2;
                paramCode.pageSize = 1;
                paramCode.CodeNT = pcode + "99";
                var listtemp = await dcSchoolCatalogManager.GetPaged(paramCode);
                if (listtemp != null && listtemp.data != null && listtemp.data.Count > 0)
                {
                    var maxCode = listtemp.data[0].Code;
                    int maxCodeVal = 0;
                    if (maxCode != null && maxCode.Length >= 2)
                    {
                        maxCode = maxCode.Substring(maxCode.Length - 2, 2).TrimStart("0");
                        int.TryParse(maxCode, out maxCodeVal);
                    }
                    entity.Code = "0" + (maxCodeVal + 1).ToString();
                    entity.Code = pcode + entity.Code.Substring(entity.Code.Length - 2);
                }
                if (string.IsNullOrEmpty(entity.Code))
                {
                    entity.Code = pcode + "01";
                }
                entity.SchoolId = user.UnitId;
                entity.Brief = o.Brief;
                entity.Depth = 2;
                entity.Sort = 0;
                entity.Path = "";
                entity.BaseCatalogId = 0;
                entity.Statuz = o.Statuz;
                entity.Code = "";
                entity.ModelCount = 0;
                entity.BrandCount = 0;
                if (await dcSchoolCatalogManager.Add(entity) > 0)
                {
                    return baseSucc<string>(entity.Id.ToString(), 1, "添加成功");
                }
                else
                {
                    return baseFailed<string>("添加失败。");
                }
            }
        }

        /// <summary>
        /// 根据id获取物品详细信息-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcataloggetbyid")]
        //<used>1</used>
        public async Task<Result<DcSchoolCatalogDto>> DcSchoolCatalog_GetById(long id)
        {
            var model = await dcSchoolCatalogManager.GetById(id);
            if (model != null)
            {
                return baseSucc(mapper.Map<DcSchoolCatalogDto>(model), 1, "查询成功");
            }
            else
            {
                return baseFailed<DcSchoolCatalogDto>("获取失败");
            }
        }

        /// <summary>
        ///  单位库基础库,物品汇总列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcMaterialStatisticsList>>> DcSchoolMaterialStatistics_Find([FromBody] VDcMaterialStatisticsListParam param)
        {
            param.OptType = 1;
            param.unitId = user.UnitId;
            param.UnitPId = user.UnitPId;
            param.Statuzge = 0;
            param.SchoolId = user.UnitId;
            var pagData = await dcSchoolMaterialManager.GetStatisticsListPaged(param);

            return baseSucc(pagData, pagData.dataCount, "", pagData.Other);
        }

        /// <summary>
        /// 单位库基础库,物品汇总列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialcatalogfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VDcSchoolMaterialList>>> DcSchoolMaterialCatalog_Find([FromBody] VDcSchoolMaterialListParam param)
        {
            param.unitId = user.UnitId;
            param.Statuzge = 0;
            PageModel<VDcSchoolMaterialList> pg = await dcSchoolMaterialManager.GetListPaged(param);
            return baseSucc(pg, pg.dataCount, "", pg.Other);
        }

        /// <summary>
        /// 获取单位供应商信息-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccompanycomboxfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcCompanyCombox_Find([FromBody] DcCompanyParam param)
        {
            Result r = new Result();
            param.OptType = 1;
            param.SchoolId = user.UnitId;
            param.CountyId = 0;
            var entityUnit = await unitManager.QueryById(user.UnitId);
            if (entityUnit != null)
            {
                param.CountyId = entityUnit.PId;
            }
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "DESC" });
            var listCompany = await dcCompanyManager.Find(param);
            PageModel<object> page = new PageModel<object>();
            //获取是否开启允许单位添加供应商权限，用于控制combobox下拉框是否可编辑
            if (ApplicationConfig.DangerChemicalsLevel == UnitTypes.School.ObjToInt().ToString())
            {
                page.Other = 1;
            }
            else
            {

                var paramConfigSet = new BConfigSetParam();
                paramConfigSet.ModuleCode = "9";
                paramConfigSet.TypeCode = "WHPXXTJGYS";
                var listConfigSet = await configSetManager.Find(paramConfigSet);
                if (listConfigSet.Count > 0)
                {
                    //查找单位自定义配置
                    var unitConfig = listConfigSet.Find(f => f.ConfigType == 1 && f.UnitId == (param.CountyId == 0 ? user.UnitId : param.CountyId));
                    if (unitConfig != null)
                    {
                        page.Other = unitConfig.ConfigValue;
                    }
                    else
                    {
                        //单位未配置时，读取系统默认配置
                        var systemConfig = listConfigSet.Find(f => f.ConfigType == 0);
                        if (systemConfig != null)
                        {
                            page.Other = systemConfig.ConfigValue;
                        }
                    }
                }
            }

            r.flag = 1;
            r.msg = "";
            if (listCompany != null && listCompany.Count > 0)
            {
                page.dataCount = listCompany.Count;
                page.data = listCompany.Select(m => new { m.Id, m.Name }).ToList<object>();
            }
            return baseSucc(page, page.dataCount, "查询成功", page.Other);
        }

        /// <summary>
        /// 根据Id获取“单位物品库”信息-查询
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialcataloggetbyid")]
        //<used>0</used>
        public async Task<Result<VDcSchoolMaterialList>> DcSchoolMaterialCatalog_GetById(long Id)
        {
            Result r = new Result();
            var param = new VDcSchoolMaterialListParam();
            param.Id = Id;
            param.unitId = user.UnitId;
            var list = await dcSchoolMaterialManager.FindList(param);
            if (list != null && list.Count > 0)
            {
                return baseSucc(list[0], 1, "查询成功");
            }
            else
            {
                return baseFailed<VDcSchoolMaterialList>("查询失败");
            }
        }

        /// <summary>
        /// 批量审核-审核
        /// </summary>
        /// <param name="listUserId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialbatchaudit")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolMaterial_BatchAudit([FromBody] List<long> listUserId)
        {
            var r = await dcSchoolMaterialManager.BatchAudit(listUserId, user.UserId, user.UnitId);
            if (r.flag == 1)
            {
                string resultData = "";
                if (r.data != null && r.data.rows != null)
                {
                    resultData = r.data.rows.ToString();
                }
                return baseSucc<string>(resultData, 1, r.msg);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 入库审核物品删除-删除
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialdeletebyid")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolMaterial_DeleteById(long Id)
        {
            var r = await dcSchoolMaterialManager.DeleteById(Id, user.UserId, user.UnitId);
            if (r.flag == 1)
            {
                return baseSucc<string>("", 1, r.msg);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 用户库基础库,物品管理启用、禁用、是否归还-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolCatalog>>> DcSchoolCatalog_Find([FromBody] VDcSchoolCatalogParam param)
        {
            param.unitId = user.UnitId;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                var entityUnit = await unitManager.QueryById(user.UnitId);
                if (entityUnit != null)
                {
                    param.CountyId = entityUnit.PId;
                }
            }
            param.Depth = 2;
            param.statuzgt = 0;
            PageModel<VDcSchoolCatalog> pg = await dcSchoolCatalogManager.GetTwoPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 用户库基础库,物品管理启用、禁用-设置
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogsetstatuz")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolCatalog_SetStatuz(string ids, int statuz)
        {
            Result r = new Result();
            string msg = "";
            if (statuz == 1)
            {
                msg = "启用";
            }
            else if (statuz == 2)
            {
                msg = "禁用";
            }
            else
            {
                return baseFailed<string>("非法操作。");
            }
            DcSchoolCatalogParam param = new DcSchoolCatalogParam();
            param.unitId = user.UnitId;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                var entityUnit = await unitManager.QueryById(user.UnitId);
                if (entityUnit != null)
                {
                    param.CountyId = entityUnit.PId;
                }
            }
            param.Depth = 2;
            param.statuzgt = 0;
            param.Ids = ids;
            param.pageSize = int.MaxValue;
            var list = await dcSchoolCatalogManager.GetPaged(param);
            if (list != null && list.data != null && list.data.Count > 0)
            {
                list.data.ForEach(m => m.Statuz = statuz);
                if (await dcSchoolCatalogManager.Update(list.data))
                {
                    return baseSucc<string>(msg + "成功。", 1);
                }
            }
            return baseFailed<string>(msg + "失败，请重试，如还无法解决请联系客服协助处理。");
        }

        /// <summary>
        /// 危化品选品-设置
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="isCommonUse"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogsetiscommonuse")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolCatalog_SetIsCommonUse(string ids, int isCommonUse)
        {
            DcSchoolCatalogParam param = new DcSchoolCatalogParam();
            param.unitId = user.UnitId;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                var entityUnit = await unitManager.QueryById(user.UnitId);
                if (entityUnit != null)
                {
                    param.CountyId = entityUnit.PId;
                }
            }
            param.Ids = ids;
            param.pageSize = int.MaxValue;
            var list = await dcSchoolCatalogManager.Find(param);
            if (list != null && list.Count > 0)
            {
                list.ForEach(m => m.IsCommonUse = isCommonUse);
                if (await dcSchoolCatalogManager.Update(list))
                {
                    return baseSucc<string>("设置成功", 1);
                }
            }
            return baseFailed<string>("设置失败。");
        }

        /// <summary>
        /// 用户库基础型号库管理启用、禁用-设置
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelsetstatuz")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolMaterialModel_SetStatuz(string ids, int statuz)
        {
            string msg = "";
            if (statuz == 1)
            {
                msg = "启用";
            }
            else if (statuz == 2)
            {
                msg = "禁用";
            }
            else
            {
                return baseFailed<string>("非法操作。");
            }
            if (string.IsNullOrEmpty(ids))
            {
                return baseFailed<string>("请选择需要操作的数据。");
            }
            var param = new DcSchoolMaterialModelParam();
            param.Ids = ids;
            param.unitId = user.UnitId;
            param.statuzgt = 0;
            var list = await dcSchoolMaterialModelManager.Find(param);
            if (list != null)
            {
                list.ForEach(m => m.Statuz = statuz);
                if (await dcSchoolMaterialModelManager.Update(list))
                {
                    return baseSucc<string>(msg + "成功。", 1);
                }
                else
                {
                    return baseFailed<string>(msg + "失败，请重试，如还无法解决请联系客服协助处理。");
                }
            }
            else
            {
                return baseFailed<string>("非法操作，请根据页面内容点击操作。");
            }

        }

        /// <summary>
        /// 物品规格型号，获取实体-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelgetbyid")]
        //<used>1</used>
        public async Task<Result<DcSchoolMaterialModelDto>> DcSchoolMaterialModel_GetById(long id)
        {
            DcSchoolMaterialModel entity = await dcSchoolMaterialModelManager.QueryById(id);
            if (entity != null)
            {
                return baseSucc(mapper.Map<DcSchoolMaterialModelDto>(entity), 1, "查询成功");
            }
            return baseFailed<DcSchoolMaterialModelDto>("查询失败");
        }

        /// <summary>
        /// 添加物品规格型号-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelsave")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolMaterialModel_Save([FromBody] DcSchoolMaterialModel o)
        {
            if (ApplicationConfig.DangerChemicalsLevel != user.UnitTypeId.ToString())
            {
                return baseFailed<string>("没有权限保存数据。");
            }
            var entityCatalog = await dcSchoolCatalogManager.GetById(o.SchoolCatalogId);
            if (entityCatalog == null)
            {
                return baseFailed<string>("请选择物品名称。");
            }

            var param = new DcSchoolMaterialModelParam();
            param.unitId = user.UnitId;
            param.Model = o.Model;
            param.SchoolCatalogId = o.SchoolCatalogId;
            param.statuzgt = 0;
            if (o.Id > 0)
            {
                param.Id = o.Id;
                param.OptType = 1;
            }
            var list = await dcSchoolMaterialModelManager.Find(param);
            if (list != null && list.Count > 0)
            {
                return baseFailed<string>("你添加的物品规格属性已存在,物品不可添加多个相同的规格属性。");
            }
            //查询型号基础库表是否存在同名的型号
            long baseModelBrandId = 0;
            var paramBrand = new DcBaseBrandParam();
            paramBrand.Statuz = 1;
            paramBrand.Type = 1;
            paramBrand.BaseCatalogId = o.BaseCatalogId;
            paramBrand.Name = o.Model;
            var baseModelList = await dcBaseBrandManager.Find(paramBrand);
            if (baseModelList.Count > 0)
            {
                baseModelBrandId = baseModelList.FirstOrDefault().Id;
            }
            if (o.Id > 0)
            {
                var entityModel = await dcSchoolMaterialModelManager.GetById(o.Id);
                if (entityModel != null)
                {
                    entityModel.Model = o.Model;
                    entityModel.BaseModelBrandId = baseModelBrandId;
                    entityModel.EduCode = o.EduCode;
                    entityModel.Cas = o.Cas;
                    entityModel.Limited = o.Limited;
                    entityModel.SchoolStagez = o.SchoolStagez;
                    entityModel.IsNeedApproval = o.IsNeedApproval;
                    entityModel.IsNeedReport = o.IsNeedReport;
                    entityModel.IsDetonate = o.IsDetonate;
                    entityModel.IsPoison = o.IsPoison;
                    entityModel.ValidityValue = o.ValidityValue;
                    entityModel.Statuz = o.Statuz;
                    entityModel.UseLife = o.UseLife;
                    entityModel.Remark = o.Remark;
                    entityModel.IsBurn = o.IsBurn;
                    entityModel.IsBlast = o.IsBlast;
                    entityModel.IsToxic = o.IsToxic;
                    entityModel.IsHyperToxic = o.IsHyperToxic;
                    entityModel.IsCorrode = o.IsCorrode;
                    entityModel.IsOther = o.IsOther;
                    if (await dcSchoolMaterialModelManager.Update(entityModel))
                    {
                        return baseSucc<string>(entityModel.Id.ToString(), 1, "规格属性修改成功。");
                    }
                }
            }
            else
            {
                var entityModel = new DcSchoolMaterialModel()
                {
                    SchoolId = user.UnitId,
                    SchoolCatalogId = entityCatalog.Id,
                    BaseCatalogId = entityCatalog.BaseCatalogId,
                    Model = o.Model,
                    Statuz = 1,
                    Remark = o.Remark,
                    RegDate = DateTime.Now,
                    UserId = user.UserId,
                    BaseModelBrandId = baseModelBrandId,
                    EduCode = o.EduCode,
                    Cas = o.Cas,
                    Limited = o.Limited,
                    SchoolStagez = o.SchoolStagez,
                    IsNeedApproval = o.IsNeedApproval,
                    IsNeedReport = o.IsNeedReport,
                    IsDetonate = o.IsDetonate,
                    IsPoison = o.IsPoison,
                    UseLife = o.UseLife,
                    IsBurn = o.IsBurn,
                    IsBlast = o.IsBlast,
                    IsToxic = o.IsToxic,
                    IsHyperToxic = o.IsHyperToxic,
                    IsCorrode = o.IsCorrode,
                    IsOther = o.IsOther
                };
                if (await dcSchoolMaterialModelManager.Add(entityModel) > 0)
                {
                    return baseSucc<string>(entityModel.Id.ToString(), 1, "规格属性添加成功。");
                }
            }
            return baseFailed<string>("执行失败，数据异常，请从页面点击操作，并检查填写内容。");
        }

        /// <summary>
        /// 获取物品规格型号-查询
        /// </summary>
        /// <param name="catelogId"></param>
        /// <param name="param"></param>
        /// <param name="isNoSet"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelget")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolMaterialModel_Get(int catelogId, DcApplyParam param, int isNoSet)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }

        /// <summary>
        /// 获取物品规格型号-查询2
        /// </summary>
        /// <param name="catelogId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelgetv2")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolMaterialModel_GetV2(int catelogId)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }


        //[HttpPost]
        //[Route("dcschoolmaterialmodelbatchedit")]
        ////<used>0</used>
        //public async Task<Result> DcSchoolMaterialModel_BatchEdit(LvSchoolCatalogModelBrand model)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}


        //[HttpPost]
        //[Route("dcschoolmaterialbrandbatchedit")]
        ////<used>0</used>
        //public async Task<Result> DcSchoolMaterialBrand_BatchEdit(LvSchoolCatalogModelBrand model)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}

        /// <summary>
        /// 物品规格品牌配置-设置
        /// </summary>
        /// <param name="schoolCatalogId"></param>
        /// <param name="modelId"></param>
        /// <param name="baseModelId"></param>
        /// <param name="brandIds"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmodelbrandset")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolModelBrand_Set(int schoolCatalogId, int modelId, int baseModelId, string brandIds)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }


        //[HttpPost]
        //[Route("dcschoolmaterialbrandgetbyid")]
        ////<used>0</used>
        //public async Task<Result> DcSchoolMaterialBrand_GetById(int id)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}

        /// <summary>
        /// 用户库基础库,物品管理启用、禁用-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialbrandsave")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolMaterialBrand_Save(DcSchoolMaterialBrand o)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }

        /// <summary>
        /// 获取物品库品牌信息-查询
        /// </summary>
        /// <param name="catelogId"></param>
        /// <param name="param"></param>
        /// <param name="isNoSet"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialbrandget")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolMaterialBrand_Get(int catelogId, DcApplyParam param, int isNoSet)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }

        /// <summary>
        /// 获取物品库品牌信息-查询2
        /// </summary>
        /// <param name="catelogId"></param>
        /// <param name="modelId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialbrandgetv2")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolMaterialBrand_GetV2(int catelogId, int modelId)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }

        /// <summary>
        /// 查询物品型号预警设置列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelconfigfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolMaterialModelConfig>>> DcSchoolMaterialModelConfig_Find([FromBody] VDcSchoolMaterialModelConfigParam param)
        {
            if (ApplicationConfig.DangerChemicalsLevel == UnitTypes.School.ObjToInt().ToString())
            {
                param.unitId = user.UnitId;
            }
            else
            {
                if (UnitTypes.Couty.ObjToInt() == user.UnitTypeId)
                {
                    param.unitId = user.UnitId;
                }
                else
                {
                    var entityUnit = await unitManager.QueryById(user.UnitId);
                    if (entityUnit != null)
                    {
                        param.unitId = entityUnit.PId;
                    }
                }
            }
            PageModel<VDcSchoolMaterialModelConfig> page = await dcSchoolMaterialModelManager.GetStatisticsPaged(param);
            return baseSucc(page, page.dataCount, "查询成功", page.Other);
        }

        /// <summary>
        /// 物品预警获取实体信息-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelconfiggetbyid")]
        //<used>1</used>
        public async Task<Result<VDcSchoolMaterialModelConfig>> DcSchoolMaterialModelConfig_GetById(long id)
        {
            var param = new VDcSchoolMaterialModelConfigParam();
            param.Id = id;
            param.unitId = user.UnitId;
            var list = await dcSchoolMaterialModelManager.FindStatistics(param);
            VDcSchoolMaterialModelConfig model = null;
            if (list != null && list.Count > 0)
            {
                model = list[0];
            }
            return baseSucc(model, 1, "查询成功");
        }

        /// <summary>
        /// 物品型号配置参数保存-保存
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelconfigsave")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolMaterialModelConfig_Save([FromBody] DcSchoolMaterialModelBatchParam param)
        {
            if (ApplicationConfig.DangerChemicalsLevel != user.UnitTypeId.ToString())
            {
                return baseFailed<string>("没有权限保存数据。");
            }
            long[] arrId = param.arrId;
            int type = param.type;
            string setValue = param.setValue;

            var paramDc = new DcSchoolMaterialModelParam();
            if (arrId != null)
            {
                paramDc.IdList = arrId.ToList();
            }
            paramDc.unitId = user.UnitId;
            var list = await dcSchoolMaterialModelManager.Find(paramDc);
            if (list != null && list.Count > 0)
            {
                int nValue;
                decimal dValue;
                switch (type)
                {
                    case 1://状态
                        int.TryParse(setValue, out nValue);
                        list.ForEach(m => m.Statuz = nValue);
                        break;
                    case 2://超量预警值
                        decimal.TryParse(setValue, out dValue);
                        list.ForEach(m => m.Limited = dValue);
                        break;
                    case 3://超期预警值
                        decimal.TryParse(setValue, out dValue);
                        list.ForEach(m => m.ValidityValue = dValue);
                        break;
                    case 4://学段				
                        Regex m_regex = new Regex(@"^\d*$");
                        if (m_regex.IsMatch(setValue.Replace(",", "")))
                        {
                            list.ForEach(m => m.SchoolStagez = setValue);
                        }
                        else
                        {
                            return baseFailed<string>("请选择学段。");
                        }
                        break;
                    case 5://易制毒
                        int.TryParse(setValue, out nValue);
                        list.ForEach(m => m.IsPoison = nValue);
                        break;
                    case 6://易制爆
                        int.TryParse(setValue, out nValue);
                        list.ForEach(m => m.IsDetonate = nValue);
                        break;
                    case 7://公安审批
                        int.TryParse(setValue, out nValue);
                        list.ForEach(m => m.IsNeedApproval = nValue);
                        break;
                    case 8://公安报备
                        int.TryParse(setValue, out nValue);
                        list.ForEach(m => m.IsNeedReport = nValue);
                        break;
                    case 9://存量预警值
                        decimal.TryParse(setValue, out dValue);
                        list.ForEach(m => m.WarningValue = dValue);
                        break;
                    case 10: //使用年限
                        decimal.TryParse(setValue, out dValue);
                        list.ForEach(m => m.UseLife = dValue);
                        break;
                    case 11: //备注
                        list.ForEach(m => m.Remark = setValue);
                        break;
                }
                if (await dcSchoolMaterialModelManager.Update(list))
                {
                    return baseSucc<string>("", 1, "设置成功");
                }
            }
            return baseFailed<string>("设置失败。");
        }

        /// <summary>
        /// 物品超量预警列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwarningstocklistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcWarningStockList>>> DcWarningStockList_Find([FromBody] VDcWarningStockListParam param)
        {
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                return baseFailed<PageModel<VDcWarningStockList>>("你无权访问", 1);
            }
            PageModel<VDcWarningStockList> pg = await dcSchoolMaterialModelManager.GetWarningStockPaged(param);
            return baseSucc(pg, pg.dataCount);
        }


        //[HttpPost]
        //[Route("dcwarningstockbrandlistfind")]
        ////<used>0</used>
        //public async Task<Result> DcWarningStockBrandList_Find([FromBody] DcSchoolMaterialParam param)
        //{
        //    Result r = new Result();
        //    PageModel<DcSchoolMaterial> pg = await dcSchoolMaterialManager.GetPaged(param);
        //    r.data.total = pg.dataCount;
        //    r.flag = 1;
        //    r.data.rows = mapper.Map<List<DcSchoolMaterialDto>>(pg.data);
        //    r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
        //    return r;
        //}

        /// <summary>
        /// 物品超期预警列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwarningvaliditylistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcWarningValidityList>>> DcWarningValidityList_Find([FromBody] VDcWarningValidityListParam param)
        {
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                return baseFailed<PageModel<VDcWarningValidityList>>("你无权访问");
            }
            PageModel<VDcWarningValidityList> pg = await dcSchoolMaterialModelManager.GetWarningValidityPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 使用年限预警列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwarninguselifelistfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VDcWarningUseLifeList>>> DcWarningUseLifeList_Find([FromBody] VDcWarningUseLifeListParam param)
        {
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                return baseFailed<PageModel<VDcWarningUseLifeList>>("你无权访问");
            }
            PageModel<VDcWarningUseLifeList> pg = await dcSchoolMaterialModelManager.GetWarningUsePaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 存量预警查看-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcscraplistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolMaterialList>>> DcScrapList_Find([FromBody] VDcSchoolMaterialListParam param)
        {
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.unitId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                param.CityId = user.UnitId;
            }
            else
            {
                return baseSucc(new PageModel<VDcSchoolMaterialList>(), 1, "查询成功");
            }
            PageModel<VDcSchoolMaterialList> pg = await dcSchoolMaterialManager.GetListPaged(param);
            if (pg.data != null && pg.data.Count > 0)
            {
                pg.data.ForEach(m => m.Ext = GetFileExt(m.MsdsFile));
            }
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        private string GetFileExt(string filepath)
        {
            // 处理空值或空白路径
            if (string.IsNullOrWhiteSpace(filepath))
            {
                return string.Empty;
            }

            try
            {
                // 使用 Path 类安全获取扩展名
                string ext = Path.GetExtension(filepath);

                // 统一转换为小写（可选）
                return ext?.ToLowerInvariant() ?? string.Empty;
            }
            catch (ArgumentException ex) when (ex.Message.Contains("Illegal characters in path"))
            {
                // 处理路径中的非法字符
                return string.Empty;
            }
        }

        /// <summary>
        ///  入库记录打印，更新打印状态-设置
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcstoragelogprintupdatestatuz")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolMaterialList>>> DcStorageLogPrint_UpdateStatuz([FromBody] VDcSchoolMaterialListParam param)
        {
            param.unitId = user.UnitId;
            PageModel<VDcSchoolMaterialList> pg = await dcSchoolMaterialManager.GetListPaged(param);
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                var paramMaterial = new DcSchoolMaterialParam();
                paramMaterial.Ids = string.Join(",", pg.data.Select(m => m.Id));
                var listMaterial = await dcSchoolMaterialManager.Find(paramMaterial);
                if (listMaterial != null && listMaterial.Count > 0)
                {
                    listMaterial.ForEach(m =>
                    {
                        m.IsPrint = 1;
                        m.PrintDate = DateTime.Now;
                    });
                    await dcSchoolMaterialManager.Update(listMaterial);
                }
            }
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        ///  物品报废-获取物品实体方法-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialgetbyid")]
        //<used>1</used>
        public async Task<Result<DcSchoolMaterialDto>> DcSchoolMaterial_GetById(long id)
        {
            var entity = await dcSchoolMaterialManager.GetById(id);
            if (entity != null)
            {
                if (entity.SchoolId == user.UnitId)
                {
                    return baseSucc(mapper.Map<DcSchoolMaterialDto>(entity), 1, "获取成功");
                }
                else
                {
                    return baseFailed<DcSchoolMaterialDto>("不可以报废其他单位的物品。");
                }
            }
            else
            {
                return baseFailed<DcSchoolMaterialDto>("你报废的物品不存在，请刷新重试。");
            }
        }
        /// <summary>
        /// 存量预警查看-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcscrapedlistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcScrapList>>> DcScrapedList_Find([FromBody] VDcScrapListParam param)
        {
            param.unitId = user.UnitId;
            PageModel<VDcScrapList> pg = await dcScrapManager.GetListPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }


        //[HttpPost]
        //[Route("dcscrapcancel")]
        ////<used>0</used>
        //public async Task<Result> DcScrap_Cancel(int id)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}

        /// <summary>
        /// 设置是否可二次使用-设置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isMayUse"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialsetismayuse")]
        //<used>1</used>
        public async Task<Result<string>> DcMaterial_SetIsMayUse(long id, int isMayUse)
        {
            var r = await dcSchoolMaterialManager.SetIsMayUse(id, isMayUse, user.UnitId);
            if (r.flag > 0)
            {
                string rowsData = "";
                if (r.data != null && r.data.rows != null)
                {
                    rowsData = r.data.rows.ToString();
                }
                return baseSucc<string>(rowsData, 1, "添加成功");
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 物品盘点-盘盈，盘亏-保存
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcinventorysave")]
        //<used>1</used>
        public async Task<Result<string>> DcInventory_Save(long id, int statuz)
        {
            var r = await dcSchoolMaterialManager.DcInventory_Save(id, statuz, user.UnitId, user.UserId);
            if (r.flag == 1 && r.Id > 0)
            {
                dcApplyManager.AutoStockNumUpdate(r.Id);
                string rowsData = "";
                if (r.data != null && r.data.rows != null)
                {
                    rowsData = r.data.rows.ToString();
                }
                return baseSucc<string>(rowsData, 1, "添加成功");
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 存量预警查看-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcinventoryrecordfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcInventoryList>>> DcInventoryRecord_Find([FromBody] VDcInventoryListParam param)
        {
            param.unitId = user.UnitId;
            PageModel<VDcInventoryList> pg = await dcSchoolMaterialManager.GetInventoryPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 退货、盘点、报废审核表新增-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialsnumauditadd")]
        //<used>1</used>
        public async Task<Result<string>> DcMaterialsNumAudit_Add([FromBody] DcMaterialsNumAudit o)
        {
            Result r = new Result();
            var validatorResult = new DcMaterialsNumAuditValidator(o.OptType).Validate(o);
            if (!validatorResult.IsValid)
            {
                return baseFailed<string>(string.Join("<br/>", validatorResult.Errors.Select(m => m.ErrorMessage)));
            }
            var schoolMaterial = await dcSchoolMaterialManager.GetById(o.SchoolMaterialId);
            if (schoolMaterial == null || schoolMaterial.SchoolId != user.UnitId)
            {
                return baseFailed<string>("找不到您要操作的数据。");

            }
            if (o.OptType == 1)
            {
                //盘点
                if (schoolMaterial.StockNum + o.OptNum < 0)
                {
                    return baseFailed<string>("盘亏数量不能大于库存数量。");
                }
            }
            else
            {
                if (schoolMaterial.StockNum < o.OptNum)
                {
                    return baseFailed<string>(string.Format("{0}数量不得大于库存数量。", (o.OptType == 2 ? "报废" : "退货")));
                }
            }

            var paramAudit = new DcMaterialsNumAuditParam();
            paramAudit.OptType = o.OptType;
            paramAudit.Statuz = 0;
            paramAudit.SchoolMaterialId = o.SchoolMaterialId;
            var materialNumAuditList = await dcMaterialsNumAuditManager.Find(paramAudit);
            DcMaterialsNumAudit entityNumAudit = null;
            if (materialNumAuditList.Count > 0)
            {
                entityNumAudit = materialNumAuditList.FirstOrDefault();
            }
            else
            {
                entityNumAudit = new DcMaterialsNumAudit();
                entityNumAudit.OptType = o.OptType;
                entityNumAudit.SchoolMaterialId = o.SchoolMaterialId;
            }
            entityNumAudit.OptNum = o.OptNum;
            entityNumAudit.OptReason = o.OptReason;
            entityNumAudit.OptTime = o.OptTime;
            entityNumAudit.UnitId = user.UnitId;
            entityNumAudit.UserId = user.UserId;
            entityNumAudit.RegTime = DateTime.Now;
            bool isSuccess = false;
            if (entityNumAudit.Id > 0)
            {
                isSuccess = await dcMaterialsNumAuditManager.Update(entityNumAudit);
            }
            else
            {
                if (await dcMaterialsNumAuditManager.Add(entityNumAudit) > 0)
                {
                    isSuccess = true;
                }
            }
            if (isSuccess)
            {
                return baseSucc<string>("执行成功", 1);
            }
            else
            {
                return baseFailed<string>("执行失败");
            }
        }

        /// <summary>
        /// 盘点、退货、报废审核表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialsnumauditfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcMaterialsNumAudit>>> DcMaterialsNumAudit_Find([FromBody] VDcMaterialsNumAuditParam param)
        {
            param.unitId = user.UnitId;
            PageModel<VDcMaterialsNumAudit> pg = await dcSchoolMaterialManager.GetMaterialsNumPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 危化品存量库-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialoptfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolMaterialOptList>>> DcSchoolMaterialOpt_Find([FromBody] VDcSchoolMaterialOptListParam param)
        {
            param.unitId = user.UnitId;
            PageModel<VDcSchoolMaterialOptList> pg = await dcSchoolMaterialManager.GetOptPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 导出盘点表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportdcschoolmaterialoptfind")]
        public async Task<IActionResult> ExportDcSchoolMaterialOpt_Find([FromBody] VDcSchoolMaterialOptListParam param)
        {
            param.unitId = user.UnitId;
            param.pageSize = int.MaxValue;
            PageModel<VDcSchoolMaterialOptList> pg = await dcSchoolMaterialManager.GetOptPaged(param);
            var excelBytes = await new ExcelHelper<VDcSchoolMaterialOptList>().ExportExecl(pg.data.ToList(), "盘点表信息", new string[] { "Name", "Model", "StockNum", "UnitName", "Price", "Brand", "RegDate", "ValidDate", "Address", "InventoryAuditNum" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "盘点表信息.xlsx");
        }

        /// <summary>
        /// 按计划录入入库列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcinputmaterialbyplanfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VDcInputMaterialByPlan>>> DcInputMaterialByPlan_Find([FromBody] VDcInputMaterialByPlanParam param)
        {
            param.unitId = user.UnitId;
            PageModel<VDcInputMaterialByPlan> pg = await dcPurchaseListManager.GetInputMaterialByPlanPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 按计划录入-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialbyplansave")]
        //<used>1</used>
        public async Task<Result<string>> DcMaterialByPlan_Save([FromBody] LvPlanInputDto model)
        {
            if (model != null && model.o != null && model.o.Count > 0)
            {
                List<string> errorList = new List<string>();
                foreach (var item in model.o)
                {
                    var validatorResult = new LvPlanInputValidator().Validate(item);
                    if (!validatorResult.IsValid)
                    {
                        foreach (var failure in validatorResult.Errors)
                        {
                            if (!errorList.Contains(failure.ErrorMessage))
                            {
                                errorList.Add(failure.ErrorMessage);
                            }
                        }
                    }
                }
                if (string.IsNullOrEmpty(model.CompanyName))
                {
                    errorList.Add("请选择供应商。");
                }
                if (errorList.Count > 0)
                {
                    return baseFailed<string>(string.Join("<br/>", errorList));
                }
            }
            else
            {
                return baseFailed<string>("没有需要执行数据。");
            }

            if (model.CompanyId == 0)
            {
                var entityCompany = new DcCompanyDto();
                entityCompany.SchoolId = user.UnitId;
                if (user.UnitPId > 0)
                {
                    entityCompany.SchoolId = user.UnitPId;
                }
                entityCompany.UserId = user.UserId;
                entityCompany.Name = model.CompanyName;
                entityCompany.Id = model.CompanyId;
                //VUser.UnitPid ?? UnitId, 0, model.CompanyName, "", "", "", "", UserId, ref companyId, ref companyFlag, ref companyMsg);
                await dcCompanyManager.InsertUpdate(entityCompany);
                model.CompanyId = entityCompany.Id;
            }
            //删除临时表数据
            await dcSchoolMaterialTempManager.DeleteTemp(model.PurchaseOrderId, user.UnitId);
            //插入临时表数据
            int successCount = 0;
            string msg = "";
            foreach (var m in model.o)
            {
                //var entityMaterialTemp = mapper.Map<DcSchoolMaterialTemp>(m);
                var entityMaterialTemp = new DcSchoolMaterialTemp();
                entityMaterialTemp.PurchaseOrderId = model.PurchaseOrderId;
                entityMaterialTemp.SchoolId = user.UnitId;
                entityMaterialTemp.UserId = user.UserId;
                entityMaterialTemp.DangerChemicalsLevel = ApplicationConfig.DangerChemicalsLevel;

                entityMaterialTemp.Brand = m.Brand;
                entityMaterialTemp.Model = m.Model;
                entityMaterialTemp.SchoolMaterialBrandId = m.BrandId;
                entityMaterialTemp.SchoolMaterialModelId = m.ModelId;
                entityMaterialTemp.CabinetAddress = m.CabinetAddress;
                entityMaterialTemp.DepositAddressId = m.DepositAddressId;
                entityMaterialTemp.MsdsFile = m.MsdsFile;
                entityMaterialTemp.LvCompanyId = model.CompanyId;
                entityMaterialTemp.Num = m.Num;
                entityMaterialTemp.Price = m.Price;
                entityMaterialTemp.PurchaseListId = m.PurchaseListId;
                entityMaterialTemp.RegDate = DateTime.Now;
                entityMaterialTemp.Remark = m.Remark;
                entityMaterialTemp.SchoolCatalogId = m.SchoolCatalogId;
                entityMaterialTemp.ValidDate = m.ValidDate;
                entityMaterialTemp.WarrantyMonth = m.WarrantyMonth ?? 0;

                var r = await dcSchoolMaterialTempManager.Save(entityMaterialTemp);
                if (r.flag == 1) successCount += 1;
                else msg += r.msg + "<br />";
            }
            if (successCount < model.o.Count)
            {
                return baseFailed<string>(string.Format("共执行{0}条数据，其中成功{1}条，失败{2}条。失败原因：<br />{3}", model.o.Count, successCount, (model.o.Count - successCount), msg));
            }
            else
            {
                return baseSucc("", 1, "保存成功", model);
            }
        }

        /// <summary>
        /// 按计划录入入库-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialbyplaninput")]
        //<used>1</used>
        public async Task<Result<PageModel<DcSchoolMaterialTempDto>>> DcMaterialByPlan_Input([FromBody] LvPlanInputDto model)
        {
            Result r = new Result();
            //判断采购状态
            var order = await dcPurchaseOrderManager.GetById(model.PurchaseOrderId);
            if (order == null || order.SchoolId != user.UnitId)
            {
                return baseFailed<PageModel<DcSchoolMaterialTempDto>>("采购单不存在。");
            }
            if (order.InputStatuz == 2)
            {
                return baseFailed<PageModel<DcSchoolMaterialTempDto>>("该采购单已结束采购， 无法入库。");
            }
            if (model.o != null && model.o.Count > 0)
            {
                var templist = model.o.Where(m => m.Num <= 0);
                if (templist.Count() > 0)
                {
                    return baseFailed<PageModel<DcSchoolMaterialTempDto>>("该采购单中数量必须都大于0， 否则无法入库。");
                }
            }
            if (model.CompanyId == 0)
            {
                var entityCompany = new DcCompanyDto();
                entityCompany.SchoolId = user.UnitId;
                var entityUnit = await unitManager.QueryById(user.UnitId);
                if (entityUnit != null)
                {
                    entityCompany.SchoolId = entityUnit.PId;
                }
                entityCompany.UserId = user.UserId;
                entityCompany.Name = model.CompanyName;
                entityCompany.Id = model.CompanyId;
                await dcCompanyManager.InsertUpdate(entityCompany);
                model.CompanyId = entityCompany.Id;
            }
            //删除临时表数据
            await dcSchoolMaterialTempManager.DeleteTemp(model.PurchaseOrderId, user.UnitId);
            //插入临时表数据
            int successCount = 0;
            string msg = "";
            foreach (var m in model.o)
            {
                var entityMaterialTemp = new DcSchoolMaterialTemp();
                entityMaterialTemp.PurchaseOrderId = model.PurchaseOrderId;
                entityMaterialTemp.SchoolId = user.UnitId;
                entityMaterialTemp.UserId = user.UserId;
                entityMaterialTemp.DangerChemicalsLevel = ApplicationConfig.DangerChemicalsLevel;

                entityMaterialTemp.Brand = m.Brand;
                entityMaterialTemp.Model = m.Model;
                entityMaterialTemp.SchoolMaterialBrandId = m.BrandId;
                entityMaterialTemp.SchoolMaterialModelId = m.ModelId;
                entityMaterialTemp.CabinetAddress = m.CabinetAddress;
                entityMaterialTemp.DepositAddressId = m.DepositAddressId;
                entityMaterialTemp.LvCompanyId = model.CompanyId;

                entityMaterialTemp.MsdsFile = m.MsdsFile;
                entityMaterialTemp.Num = m.Num;
                entityMaterialTemp.Price = m.Price;
                entityMaterialTemp.PurchaseListId = m.PurchaseListId;
                entityMaterialTemp.RegDate = DateTime.Now;
                entityMaterialTemp.Remark = m.Remark;
                entityMaterialTemp.SchoolCatalogId = m.SchoolCatalogId;
                entityMaterialTemp.ValidDate = m.ValidDate;
                entityMaterialTemp.WarrantyMonth = m.WarrantyMonth ?? 0;
                r = await dcSchoolMaterialTempManager.Save(entityMaterialTemp);
                if (r.flag == 1) successCount += 1;
                else msg += r.msg + "<br />";
            }

            if (successCount < model.o.Count)
            {
                return baseFailed<PageModel<DcSchoolMaterialTempDto>>(msg);
            }
            else
            {
                //获取需要上报合法用途说明至公安溯源平台的危化品
                var reportMaterialList = await dcSchoolMaterialTempManager.GetNeedReportSchoolMaterialTempList(model.PurchaseOrderId, user.UnitId);

                r = await dcSchoolMaterialTempManager.Input(model.PurchaseOrderId, user.UnitId, user.UserId);
                if (r.flag == 1)
                {
                    PageModel<DcSchoolMaterialTempDto> pg = new PageModel<DcSchoolMaterialTempDto>();
                    pg.Other = model;
                    pg.data = mapper.Map<List<DcSchoolMaterialTempDto>>(reportMaterialList);
                    //入库成功，判断是否有需要上报公安溯源平台的危化品， 有的话需要上报合法用途说明
                    return baseSucc(pg, 1, "入库成功", model);
                }
                else
                {
                    return baseFailed<PageModel<DcSchoolMaterialTempDto>>(msg);
                }
            }
        }

        /// <summary>
        /// 结束入库-设置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseorderfinishinputstatuz")]
        //<used>0</used>
        public async Task<Result<string>> DcPurchaseOrder_FinishInputStatuz(long id)
        {
            var r = await dcPurchaseOrderManager.FinishInput(id, user.UnitId, user.UserId);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 清除物品入库保存的临时数据-删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialtempdelete")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolMaterialTemp_Delete(long id)
        {
            var r = await dcSchoolMaterialTempManager.DeleteTempByListId(id, user.UnitId);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 基础分类数据列表-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasecatalogfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcBaseCatalog_Find()
        {
            var param = new DcSchoolCatalogParam();
            var schoolCatalogList = await dcSchoolCatalogManager.Find(param);
            if (schoolCatalogList.Count == 0)
            {
                //初始化数据
                var paramUnit = new PUnitParam();
                paramUnit.UnitType = UnitTypes.Couty.ObjToInt();
                paramUnit.statuzgt = 0;
                paramUnit.pageSize = int.MaxValue;
                paramUnit.sortModel = new List<SortBaseModel>();
                paramUnit.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "asc" });
                var unitList = await unitManager.Find(paramUnit);
                unitList.ForEach(async f =>
                {
                    await DcSchoolCatalogInit_Update(f.Id);
                });
            }
            var paramBase = new DcBaseCatalogParam();
            paramBase.pageSize = int.MaxValue;
            paramBase.sortModel = new List<SortBaseModel>();
            paramBase.sortModel.Add(new SortBaseModel() { SortCode = "Path", SortType = "asc" });
            var list = await dcBaseCatalogManager.Find(paramBase);
            var oneList = list.Where(f => f.Depth == 0).ToList();
            var data = from d in oneList
                       where d.Depth == 0
                       select new
                       {
                           Id = d.Id,
                           Pid = d.Pid,
                           Name = d.Name,
                           Code = d.Code,
                           Sort = d.Sort,
                           UnitsMeasurement = d.UnitsMeasurement,
                           Nature = d.Nature,
                           Depth = d.Depth,
                           Statuz = d.Statuz,
                           children = from d2 in list.Where(f => f.Depth == 1 && f.Pid == d.Id)
                                      select new
                                      {
                                          Id = d2.Id,
                                          Pid = d2.Pid,
                                          Name = d2.Name,
                                          Code = d2.Code,
                                          Sort = d2.Sort,
                                          UnitsMeasurement = d2.UnitsMeasurement,
                                          Nature = d2.Nature,
                                          Depth = d2.Depth,
                                          Statuz = d2.Statuz,
                                          children = from d3 in list.Where(f => f.Depth == 2 && f.Pid == d2.Id)
                                                     select new
                                                     {
                                                         Id = d3.Id,
                                                         Pid = d3.Pid,
                                                         Name = d3.Name,
                                                         Code = d3.Code,
                                                         Sort = d3.Sort,
                                                         UnitsMeasurement = d3.UnitsMeasurement,
                                                         Nature = d3.Nature,
                                                         Depth = d3.Depth,
                                                         Statuz = d3.Statuz
                                                     }
                                      }
                       };
            PageModel<object> page = new PageModel<object>();
            page.data = data.ToList<object>();
            return baseSucc(page, data.Count());
        }

        /// <summary>
        /// 获取危化品分类一二级下拉框-查询
        /// </summary>
        /// <param name="depth"></param>
        /// <param name="pid"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasecatalogcmboget")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcBaseCatalogCmbo_Get(int depth, int pid)
        {
            var param = new DcBaseCatalogParam();
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Path", SortType = "asc" });
            if (depth > 0)
            {
                param.Depth = depth;
                if (pid > 0)
                {
                    param.Pid = pid;
                }
                var list = await dcBaseCatalogManager.Find(param);
                if (list != null && list.Count > 0)
                {
                    PageModel<object> page = new PageModel<object>();
                    page.data = list.Select(m => new { Id = m.Id, Name = m.Name }).ToList<object>();
                    return baseSucc(page, page.dataCount, "查询成功", page.Other);
                }
            }
            else
            {
                var list = await dcBaseCatalogManager.Find(param);
                if (list != null && list.Count > 0)
                {
                    var oneList = list.Where(f => f.Depth == 0);
                    var data = from d in oneList
                               select new
                               {
                                   id = d.Id,
                                   text = d.Name,
                                   Depth = d.Depth,
                                   children = from d2 in list.Where(f => f.Depth == 1 && f.Pid == d.Id)
                                              select new
                                              {
                                                  id = d2.Id,
                                                  text = d2.Name,
                                                  Depth = d2.Depth,
                                              }
                               };

                    PageModel<object> page = new PageModel<object>();
                    page.data = data.ToList<object>();
                    return baseSucc(page, page.dataCount, "查询成功", page.Other);
                }
            }
            return baseSucc<PageModel<object>>(null, 0, "查询成功");
        }

        /// <summary>
        /// 保存危化品分类-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasecataloginsertupdate")]
        //<used>0</used>
        public async Task<Result<string>> DcBaseCatalog_InsertUpdate([FromBody] DcBaseCatalogDto o)
        {
            o.UnitId = user.UnitId;
            o.UserId = user.UserId;
            var r = await dcBaseCatalogManager.InsertUpdate(o);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 基础分类-删除
        /// </summary>
        /// <param name="id">分类Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasecatalogdelete")]
        //<used>1</used>
        public async Task<Result<string>> DcBaseCatalog_Delete(long id)
        {
            var entity = await dcBaseCatalogManager.GetById(id);
            if (entity == null)
            {
                return baseFailed<string>("分类不存在。");
            }
            if (entity.Depth != 2)
            {
                var param = new DcBaseCatalogParam();
                param.Pid = id;
                var childrenList = await dcBaseCatalogManager.Find(param);
                if (childrenList.Count > 0)
                {
                    return baseFailed<string>("存在下级分类无法删除。如需删除，请先删除下级分类。");
                }
            }
            else
            {
                var param = new DcSchoolMaterialParam();
                param.BaseCatalogId = id;
                param.StatuzGT = -1;
                var schoolMaterialList = await dcSchoolMaterialManager.Find(param);
                if (schoolMaterialList.Count > 0)
                {
                    return baseFailed<string>("分类已使用，无法删除。");
                }
                var paramPurchase = new DcPurchaseListParam();
                paramPurchase.BaseCatalogId = id;
                paramPurchase.StatuzGT = -1;
                var purchaseList = await dcPurchaseListManager.Find(paramPurchase);
                if (purchaseList.Count > 0)
                {
                    return baseFailed<string>("分类已使用，无法删除。");
                }
            }
            string strJson = string.Format("【id:{0}】", id);
            if (await dcBaseCatalogManager.DeleteById(id))
            {
                return baseSucc<string>("删除成功", 1);
            }
            else
            {
                return baseFailed<string>("删除失败。");
            }
        }

        /// <summary>
        /// 分类启用、禁用-设置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasecatalogsetstatuz")]
        //<used>1</used>
        public async Task<Result<string>> DcBaseCatalog_SetStatuz(long id, int statuz)
        {
            var entity = await dcBaseCatalogManager.GetById(id);
            if (entity == null)
            {
                return baseFailed<string>("分类不存在");
            }
            var paramSchool = new DcSchoolCatalogParam();
            paramSchool.BaseCatalogId = id;
            paramSchool.statuzgt = -1;
            var listSchool = await dcSchoolCatalogManager.Find(paramSchool);
            var updateBaseList = new List<DcBaseCatalog>();
            var updateSchoolList = new List<DcSchoolCatalog>();
            updateBaseList.Add(entity);
            if (listSchool != null && listSchool.Count > 0)
            {
                updateSchoolList.AddRange(listSchool);
            }
            if (entity.Depth == 1)
            {
                var param = new DcBaseCatalogParam();
                param.Pid = id;
                param.statuzgt = -1;
                var list = await dcBaseCatalogManager.Find(param);
                if (list != null && list.Count > 0)
                {
                    updateBaseList.AddRange(list);
                }
                if (listSchool != null && listSchool.Count > 0)
                {
                    var paramSchoolChilder = new DcSchoolCatalogParam();
                    paramSchoolChilder.Pids = listSchool.Select(m => m.Id).ToList();
                    paramSchoolChilder.statuzgt = -1;
                    var listSchoolChilder = await dcSchoolCatalogManager.Find(paramSchoolChilder);
                    if (listSchoolChilder != null && listSchoolChilder.Count > 0)
                    {
                        updateSchoolList.AddRange(updateSchoolList);
                    }
                }
            }
            if (entity.Depth != 1 && entity.Depth != 2)
            {
                return baseFailed<string>("执行失败");
            }
            bool istrue = false;
            if (updateBaseList.Count > 0)
            {
                updateBaseList.ForEach(m => m.Statuz = statuz);
                istrue = await dcBaseCatalogManager.Update(updateBaseList);

            }
            if (updateSchoolList.Count > 0)
            {
                updateSchoolList.ForEach(m => m.Statuz = statuz);
                await dcSchoolCatalogManager.Update(updateSchoolList);
            }
            if (istrue)
            {
                return baseSucc<string>("执行成功", 1);
            }
            else
            {
                return baseFailed<string>("执行失败");
            }
        }

        /// <summary>
        /// 基础规格参数列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasemodelextensionfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcBaseModelExtensionDto>>> DcBaseModelExtension_Find([FromBody] DcBaseModelExtensionParam param)
        {
            var list = await dcBaseModelExtensionManager.GetList(param);
            PageModel<DcBaseModelExtensionDto> pg = new PageModel<DcBaseModelExtensionDto>();
            pg.data = list;
            pg.dataCount = param.totalCount;
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 添加、修改基础规格参数-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasemodelextensionsave")]
        //<used>1</used>
        public async Task<Result<string>> DcBaseModelExtension_Save([FromBody] DcBaseModelExtension o)
        {
            var entityCatalog = await dcBaseCatalogManager.GetById(o.BaseCatalogId);
            if (entityCatalog == null)
            {
                return baseFailed<string>("请选择危化品名称。");
            }
            if (o.Id == 0)
            {
                //验证名称长度
                if (string.IsNullOrEmpty(o.Name) || o.Name.Length > 500)
                {
                    return baseFailed<string>("请填写规格、属性，并字符长度必须不超过500。");
                }
            }
            var param = new DcBaseModelExtensionParam();
            param.statuzgt = 0;
            param.Namez = o.Name;
            param.BaseCatalogId = o.BaseCatalogId;
            param.IdNT = o.Id;
            var list = await dcBaseModelExtensionManager.Find(param);
            if (list != null && list.Count > 0)
            {
                var listTemp = list.Where(m => m.Name == o.Name);
                if (listTemp != null && listTemp.Count() > 0)
                {
                    return baseFailed<string>("你添加的危化品规格已存在。");
                }
            }
            if (o.Id > 0)
            {
                //验证model
                var validatorResult = new DcBaseModelExtensionValidator().Validate(o);
                if (!validatorResult.IsValid)
                {
                    return baseFailed<string>(string.Join("<br/>", validatorResult.Errors.Select(m => m.ErrorMessage)));
                }
                var entityModel = await dcBaseModelExtensionManager.GetById(o.Id);
                if (entityModel != null)
                {
                    entityModel.Name = o.Name;
                    entityModel.EduCode = o.EduCode;
                    entityModel.Cas = o.Cas;
                    entityModel.Limited = o.Limited;
                    entityModel.SchoolStagez = o.SchoolStagez;
                    entityModel.IsNeedApproval = o.IsNeedApproval;
                    entityModel.IsNeedReport = o.IsNeedReport;
                    entityModel.IsDetonate = o.IsDetonate;
                    entityModel.IsPoison = o.IsPoison;
                    entityModel.ValidityValue = o.ValidityValue;
                    entityModel.Statuz = o.Statuz;
                    entityModel.UseLife = o.UseLife;
                    entityModel.Remark = o.Remark;
                    entityModel.IsBurn = o.IsBurn;
                    entityModel.IsBlast = o.IsBlast;
                    entityModel.IsToxic = o.IsToxic;
                    entityModel.IsHyperToxic = o.IsHyperToxic;
                    entityModel.IsCorrode = o.IsCorrode;
                    entityModel.IsOther = o.IsOther;
                    entityModel.IsSecuritySupervise = o.IsSecuritySupervise;
                    entityModel.MeasuredValue = o.MeasuredValue;
                    if (await dcBaseModelExtensionManager.Update(entityModel))
                    {
                        var paramBrand = new DcSchoolMaterialModelParam();
                        paramBrand.BaseModelBrandId = o.Id;
                        var schoolMaterialModelList = await dcSchoolMaterialModelManager.Find(paramBrand);
                        schoolMaterialModelList.ForEach(f =>
                        {
                            f.Model = o.Name;
                            f.EduCode = o.EduCode;
                            f.Cas = o.Cas;
                            f.Limited = o.Limited;
                            f.SchoolStagez = o.SchoolStagez;
                            f.IsNeedApproval = o.IsNeedApproval;
                            f.IsNeedReport = o.IsNeedReport;
                            f.IsDetonate = o.IsDetonate;
                            f.IsPoison = o.IsPoison;
                            f.ValidityValue = o.ValidityValue ?? 0;
                            f.Statuz = o.Statuz;
                            f.UseLife = o.UseLife;
                            f.Remark = o.Remark;
                            f.IsBurn = o.IsBurn;
                            f.IsBlast = o.IsBlast;
                            f.IsToxic = o.IsToxic;
                            f.IsHyperToxic = o.IsHyperToxic;
                            f.IsCorrode = o.IsCorrode;
                            f.IsOther = o.IsOther;
                            f.IsSecuritySupervise = o.IsSecuritySupervise;
                            f.MeasuredValue = o.MeasuredValue;

                        });
                        await dcSchoolMaterialModelManager.Update(schoolMaterialModelList);

                        return baseSucc<string>("规格属性修改成功", 1);
                    }
                }
            }
            else
            {
                var entity = new DcBaseModelExtension
                {
                    BaseCatalogId = entityCatalog.Id,
                    Name = o.Name,
                    Statuz = 1,
                    Remark = o.Remark,
                    EduCode = o.EduCode,
                    Cas = o.Cas,
                    Limited = o.Limited,
                    SchoolStagez = o.SchoolStagez,
                    IsNeedApproval = o.IsNeedApproval,
                    IsNeedReport = o.IsNeedReport,
                    IsDetonate = o.IsDetonate,
                    IsPoison = o.IsPoison,
                    UseLife = o.UseLife,
                    IsBurn = o.IsBurn,
                    IsBlast = o.IsBlast,
                    IsToxic = o.IsToxic,
                    IsHyperToxic = o.IsHyperToxic,
                    IsCorrode = o.IsCorrode,
                    IsOther = o.IsOther,
                    IsSecuritySupervise = o.IsSecuritySupervise,
                    MeasuredValue = o.MeasuredValue
                };
                if (await dcBaseModelExtensionManager.Add(entity) > 0)
                {
                    var listSchool = await dcSchoolCatalogManager.GetCountyCatalogList(o.BaseCatalogId);
                    if (listSchool != null && listSchool.Count > 0)
                    {
                        var listAdd = new List<DcSchoolMaterialModel>();
                        var paramCatalog = new DcSchoolCatalogParam();
                        paramCatalog.Ids = string.Join(",", listSchool.Select(m => m.Id));
                        var listCatalog = await dcSchoolCatalogManager.Find(paramCatalog);
                        foreach (var item in listCatalog)
                        {
                            var entityTemp = new DcSchoolMaterialModel()
                            {
                                SchoolId = item.SchoolId,
                                SchoolCatalogId = item.Id,
                                BaseCatalogId = o.BaseCatalogId,
                                Model = o.Name,
                                Statuz = 1,
                                Remark = o.Remark,
                                RegDate = DateTime.Now,
                                UserId = user.UserId,
                                BaseModelBrandId = entity.Id,
                                EduCode = o.EduCode,
                                Cas = o.Cas,
                                Limited = o.Limited,
                                SchoolStagez = o.SchoolStagez,
                                IsNeedApproval = o.IsNeedApproval,
                                IsNeedReport = o.IsNeedReport,
                                IsDetonate = o.IsDetonate,
                                IsPoison = o.IsPoison,
                                UseLife = o.UseLife,
                                IsBurn = o.IsBurn,
                                IsBlast = o.IsBlast,
                                IsToxic = o.IsToxic,
                                IsHyperToxic = o.IsHyperToxic,
                                IsCorrode = o.IsCorrode,
                                IsOther = o.IsOther,
                                IsSecuritySupervise = o.IsSecuritySupervise,
                                MeasuredValue = o.MeasuredValue
                            };
                        }
                        if (listAdd.Count > 0)
                        {
                            await dcSchoolMaterialModelManager.Add(listAdd);
                        }
                    }
                    return baseSucc<string>("规格属性添加成功", 1);
                }
            }
            return baseFailed<string>("数据执行异常。");
        }

        /// <summary>
        /// 获取基础规格参数信息-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcbasemodelextensiongetbyid")]
        //<used>1</used>
        public async Task<Result<PageModel<DcBaseModelExtensionDto>>> DcBaseModelExtension_GetById(long id)
        {
            DcBaseModelExtensionParam param = new DcBaseModelExtensionParam();
            param.Id = id;
            var list = await dcBaseModelExtensionManager.GetList(param);
            PageModel<DcBaseModelExtensionDto> pg = new PageModel<DcBaseModelExtensionDto>();
            if (list != null && list.Count > 0)
            {
                pg.data = list;
                pg.dataCount = param.totalCount;
            }
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 基础规格配置参数-保存
        /// </summary>
        /// <param name="arrId"></param>
        /// <param name="type"></param>
        /// <param name="setValue"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcdcbasemodelextensionconfigsave")]
        //<used>1</used>
        public async Task<Result<string>> DcDcBaseModelExtensionConfig_Save(string arrId, int type, string setValue)
        {
            Result r = new Result();
            if (arrId != null && arrId.Length > 0)
            {
                var param = new DcBaseModelExtensionParam();
                param.Ids = arrId;
                var list = await dcBaseModelExtensionManager.Find(param);
                if (list != null && list.Count > 0)
                {
                    var paramModel = new DcSchoolMaterialModelParam();
                    paramModel.BaseModelBrandIdList = list.Select(m => m.Id).ToList();
                    var listModel = await dcSchoolMaterialModelManager.Find(paramModel);
                    int nValue;
                    decimal dValue;
                    switch (type)
                    {
                        case 1://状态

                            int.TryParse(setValue, out nValue);
                            list.ForEach(m => m.Statuz = nValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.Statuz = nValue);
                            }
                            break;
                        case 2://超量预警值
                            decimal.TryParse(setValue, out dValue);
                            list.ForEach(m => m.Limited = dValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.Limited = dValue);
                            }
                            break;
                        case 3://超期预警值
                            decimal.TryParse(setValue, out dValue);
                            list.ForEach(m => m.ValidityValue = dValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.ValidityValue = dValue);
                            }
                            break;
                        case 4://学段				
                            Regex m_regex = new Regex(@"^\d*$");
                            if (m_regex.IsMatch(setValue.Replace(",", "")))
                            {
                                list.ForEach(m => m.SchoolStagez = setValue);
                                if (listModel != null)
                                {
                                    listModel.ForEach(m => m.SchoolStagez = setValue);
                                }
                            }
                            else
                            {
                                return baseFailed<string>("数据错误");
                            }
                            break;
                        case 5://易制毒
                            int.TryParse(setValue, out nValue);
                            list.ForEach(m => m.IsPoison = nValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.IsPoison = nValue);
                            }
                            break;
                        case 6://易制爆
                            int.TryParse(setValue, out nValue);
                            list.ForEach(m => m.IsDetonate = nValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.IsDetonate = nValue);
                            }
                            break;
                        case 7://公安审批
                            int.TryParse(setValue, out nValue);
                            list.ForEach(m => m.IsNeedApproval = nValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.IsNeedApproval = nValue);
                            }
                            break;
                        case 8://公安报备
                            int.TryParse(setValue, out nValue);
                            list.ForEach(m => m.IsNeedReport = nValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.IsNeedReport = nValue);
                            }
                            break;
                        case 10: //使用年限
                            decimal.TryParse(setValue, out dValue);
                            list.ForEach(m => m.UseLife = dValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.UseLife = dValue);
                            }
                            break;
                        case 11: //备注
                            list.ForEach(m => m.Remark = setValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.Remark = setValue);
                            }
                            break;
                        case 12: //公安监管
                            int.TryParse(setValue, out nValue);
                            list.ForEach(m => m.IsSecuritySupervise = nValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.IsSecuritySupervise = nValue);
                            }
                            break;
                        case 13: //计量值
                            decimal.TryParse(setValue, out dValue);
                            list.ForEach(m => m.MeasuredValue = dValue);
                            if (listModel != null)
                            {
                                listModel.ForEach(m => m.MeasuredValue = dValue);
                            }
                            break;
                    }
                    await dcBaseModelExtensionManager.Update(list);
                    if (listModel != null)
                    {
                        await dcSchoolMaterialModelManager.Update(listModel);
                    }
                    return baseSucc<string>("执行成功", 1);
                }
            }
            return baseFailed<string>("未查询到需要执行的数据。");
        }

        /// <summary>
        /// 获取存储柜地址下拉列表-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dccabinetaddressget")]
        //<used>0</used>
        public async Task<Result<PageModel<object>>> DcCabinetAddress_Get()
        {
            var param = new DcSchoolMaterialParam();
            param.SchoolId = user.UnitId;
            param.CabinetAddress = 0;
            var list = await dcSchoolMaterialManager.Find(param);
            PageModel<object> page = new PageModel<object>();
            if (list != null && list.Count > 0)
            {
                var data = from p in list.Select(f => f.CabinetAddress).Distinct()
                           select new
                           {
                               CabinetAddress = p
                           };
                page.data = data.Select(m => new { CabinetAddress = m.CabinetAddress }).ToList<object>();
                page.dataCount = param.totalCount;
            }
            return baseSucc(page, page.dataCount, "查询成功", page.Other);
        }

        #endregion

        #region 物品采购
        /// <summary>
        /// 采购清单列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaselistfind")]
        //<used>0</used>
        public async Task<Result<PageModel<DcPurchaseListDto>>> DcPurchaseList_Find([FromBody] DcPurchaseListParam param)
        {
            param.unitId = user.UnitId;
            param.userId = user.UserId;
            PageModel<DcPurchaseList> pg = await dcPurchaseListManager.GetPaged(param);
            if (pg.data.Count > 0)
            {
                var listSum = await dcPurchaseListManager.GetStatistics(param);
                pg.Other = listSum;
            }
            return baseSucc(pg.ConvertTo<DcPurchaseListDto>(mapper), pg.dataCount, "查询成功", pg.Other);
        }
        /// <summary>
        /// 采购清单列表-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaselistexportexcel")]
        public async Task<IActionResult> DcPurchaseList_ExportExcel([FromBody] DcPurchaseListParam param)
        {
            string exlName = "危化品采购清单";

            #region excel头部

            IWorkbook iwork = new HSSFWorkbook();
            IFont font = iwork.CreateFont();
            font.FontHeightInPoints = 10;

            IFont font1 = iwork.CreateFont();
            font1.IsBold = true;
            font1.FontHeightInPoints = 16;

            IFont font2 = iwork.CreateFont();
            font2.IsBold = true;
            font2.FontHeightInPoints = 10;

            IFont fontRed = iwork.CreateFont();
            fontRed.FontHeightInPoints = 9;
            fontRed.Color = NPOI.HSSF.Util.HSSFColor.Red.Index;

            IFont fontRed2 = iwork.CreateFont();
            fontRed2.FontHeightInPoints = 11;
            fontRed2.IsBold = true;
            fontRed2.Color = NPOI.HSSF.Util.HSSFColor.Red.Index;

            IFont fontGreen = iwork.CreateFont();
            fontGreen.FontHeightInPoints = 11;
            fontGreen.IsBold = true;
            fontGreen.Color = NPOI.HSSF.Util.HSSFColor.Green.Index;

            ICellStyle cellstyle = iwork.CreateCellStyle();
            cellstyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            //cellstyle.WrapText = true;
            cellstyle.SetFont(font1);
            cellstyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellRed = iwork.CreateCellStyle();
            cellRed.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellRed.WrapText = true;
            cellRed.SetFont(fontRed);
            cellRed.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellRed2 = iwork.CreateCellStyle();
            cellRed2.BorderTop = NPOI.SS.UserModel.BorderStyle.Medium;
            cellRed2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Medium;
            cellRed2.BorderRight = NPOI.SS.UserModel.BorderStyle.Medium;
            cellRed2.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellRed2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellRed2.WrapText = true;
            cellRed2.SetFont(fontRed2);
            cellRed2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellGreen = iwork.CreateCellStyle();
            cellGreen.BorderTop = NPOI.SS.UserModel.BorderStyle.Medium;
            cellGreen.BorderLeft = NPOI.SS.UserModel.BorderStyle.Medium;
            cellGreen.BorderRight = NPOI.SS.UserModel.BorderStyle.Medium;
            cellGreen.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellGreen.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellGreen.WrapText = true;
            cellGreen.SetFont(fontGreen);
            cellGreen.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellLeft = iwork.CreateCellStyle();
            cellLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellLeft.WrapText = true;
            cellLeft.SetFont(font);
            cellLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellLeft2 = iwork.CreateCellStyle();
            cellLeft2.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft2.BorderRight = NPOI.SS.UserModel.BorderStyle.Medium;
            cellLeft2.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellLeft2.WrapText = true;
            cellLeft2.SetFont(font);
            cellLeft2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellLeft3 = iwork.CreateCellStyle();
            cellLeft3.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft3.BorderLeft = NPOI.SS.UserModel.BorderStyle.Medium;
            cellLeft3.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft3.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellLeft3.WrapText = true;
            cellLeft3.SetFont(font);
            cellLeft3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellRight = iwork.CreateCellStyle();
            cellRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellRight.SetFont(font);
            cellRight.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellRight2 = iwork.CreateCellStyle();
            cellRight2.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight2.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight2.BorderBottom = NPOI.SS.UserModel.BorderStyle.Medium;
            cellRight2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellRight2.SetFont(font2);
            cellRight2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellCenter = iwork.CreateCellStyle();
            cellCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellCenter.SetFont(font);
            cellCenter.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNull = iwork.CreateCellStyle();
            cellNull.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellNull.SetFont(font1);
            cellNull.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNullLeft = iwork.CreateCellStyle();
            cellNullLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellNullLeft.SetFont(font2);
            cellNullLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;



            ICellStyle cellTitle = iwork.CreateCellStyle();
            cellTitle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle.SetFont(font2);
            cellTitle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellTitle2 = iwork.CreateCellStyle();
            cellTitle2.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle2.BorderRight = NPOI.SS.UserModel.BorderStyle.Medium;
            cellTitle2.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle2.SetFont(font2);
            cellTitle2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellTitle3 = iwork.CreateCellStyle();
            cellTitle3.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle3.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle3.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle3.BorderBottom = NPOI.SS.UserModel.BorderStyle.Medium;
            cellTitle3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle3.SetFont(font2);
            cellTitle3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellTitle4 = iwork.CreateCellStyle();
            cellTitle4.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle4.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle4.BorderRight = NPOI.SS.UserModel.BorderStyle.Medium;
            cellTitle4.BorderBottom = NPOI.SS.UserModel.BorderStyle.Medium;
            cellTitle4.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle4.SetFont(font2);
            cellTitle4.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellTitle5 = iwork.CreateCellStyle();
            cellTitle5.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle5.BorderLeft = NPOI.SS.UserModel.BorderStyle.Medium;
            cellTitle5.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle5.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle5.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle5.SetFont(font2);
            cellTitle5.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellTitle6 = iwork.CreateCellStyle();
            cellTitle6.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle6.BorderLeft = NPOI.SS.UserModel.BorderStyle.Medium;
            cellTitle6.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle6.BorderBottom = NPOI.SS.UserModel.BorderStyle.Medium;
            cellTitle6.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle6.SetFont(font2);
            cellTitle6.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            //建立一个名为Sheet1的工作表
            NPOI.SS.UserModel.ISheet isheet = iwork.CreateSheet("危化品采购清单");
            isheet.PrintSetup.PaperSize = 9;
            ICell cell = null;
            //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行
            NPOI.SS.Util.CellRangeAddress region = new NPOI.SS.Util.CellRangeAddress(1, 1, 0, 13);

            isheet.AddMergedRegion(region);

            NPOI.SS.UserModel.IRow row = isheet.CreateRow(1);
            row.HeightInPoints = 23;

            cell = row.CreateCell(0);
            cell.CellStyle = cellNull;
            cell.SetCellValue("危化品采购清单");

            for (int i = 0; i <= 11; i++)
            {
                if (i == 2 || i == 10)
                {
                    isheet.SetColumnWidth(i, 16 * 256);
                }
                else if (i == 3 || i == 11)
                {
                    isheet.SetColumnWidth(i, 10 * 256);
                }
                else if (i == 4 || i == 5 || i == 6 || i == 7 || i == 12)
                {
                    isheet.SetColumnWidth(i, 8 * 256);
                }
                else if (i == 13)
                {
                    isheet.SetColumnWidth(i, 20 * 256);
                }
                else
                {
                    isheet.SetColumnWidth(i, 12 * 256);
                }
            }

            NPOI.SS.Util.CellRangeAddress region2 = new NPOI.SS.Util.CellRangeAddress(2, 5, 0, 13);
            isheet.AddMergedRegion(region2);
            IRow msgRow1 = isheet.CreateRow(2);
            cell = msgRow1.CreateCell(0);
            cell.CellStyle = cellRed;
            cell.SetCellValue("备注：1、供应商必须填写：供货的规格属性、品牌、单价、金额；\r\n            2、如没有“有效期至”和“质保期”就不填；\r\n            3、“申请批次”、“危化品名称”和“单位”不得修改。\r\n          ");

            NPOI.SS.Util.CellRangeAddress region3 = new NPOI.SS.Util.CellRangeAddress(7, 7, 0, 9);
            isheet.AddMergedRegion(region3);
            IRow msgRow4 = isheet.CreateRow(7);
            msgRow4.HeightInPoints = 23;
            cell = msgRow4.CreateCell(0);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("供货信息");
            cell = msgRow4.CreateCell(1);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(2);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(3);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(4);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(5);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(6);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(7);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(8);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(9);
            cell.CellStyle = cellRed2;
            cell.SetCellValue("");
            NPOI.SS.Util.CellRangeAddress region4 = new NPOI.SS.Util.CellRangeAddress(7, 7, 10, 13);
            isheet.AddMergedRegion(region4);
            msgRow4.HeightInPoints = 23;
            cell = msgRow4.CreateCell(10);
            cell.CellStyle = cellGreen;
            cell.SetCellValue("采购信息");
            cell = msgRow4.CreateCell(11);
            cell.CellStyle = cellGreen;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(12);
            cell.CellStyle = cellGreen;
            cell.SetCellValue("");
            cell = msgRow4.CreateCell(13);
            cell.CellStyle = cellGreen;
            cell.SetCellValue("");
            NPOI.SS.UserModel.IRow row1 = isheet.CreateRow(8);
            row1.HeightInPoints = 23;

            cell = row1.CreateCell(0);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("申请批次");

            cell = row1.CreateCell(1);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("危化品名称");

            cell = row1.CreateCell(2);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("供货规格属性");

            cell = row1.CreateCell(3);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("供货品牌");

            cell = row1.CreateCell(4);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("供货数量");

            cell = row1.CreateCell(5);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("单位");

            cell = row1.CreateCell(6);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("单价");

            cell = row1.CreateCell(7);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("金额");

            cell = row1.CreateCell(8);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("有效期至");

            cell = row1.CreateCell(9);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("质保期(月)");

            cell = row1.CreateCell(10);
            cell.CellStyle = cellTitle5;
            cell.SetCellValue("采购规格属性");

            cell = row1.CreateCell(11);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("采购品牌");

            cell = row1.CreateCell(12);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("采购数量");

            cell = row1.CreateCell(13);
            cell.CellStyle = cellTitle2;
            cell.SetCellValue("备注");

            #endregion

            #region 打印重复表头
            //横向打印
            isheet.PrintSetup.Landscape = true;
            //缩放比：100% 不缩放
            isheet.PrintSetup.Scale = 100;
            //不缩放到一页
            isheet.FitToPage = false;
            //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
            isheet.RepeatingRows = new CellRangeAddress(8, 8, 0, int.MaxValue);
            #endregion

            //string where = string.Format(" PurchaseOrderId = {0} AND SchoolId = {1} AND Statuz = 100", hiddenOrderId.Value, UnitId);
            //DcPurchaseListService dcPurchaseListManager = new DcPurchaseListService();
            //int total = 0;

            //param.PurchaseOrderId
            param.unitId = user.UnitId;
            param.Statuz = 100;
            param.pageSize = int.MaxValue;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "DESC" });

            var list = await dcPurchaseListManager.Find(param);
            if (list.Count > 0)
            {
                int y = 9;
                for (int i = 0; i < list.Count; i++)
                {
                    IRow rows = isheet.CreateRow(y);
                    rows.HeightInPoints = 20;

                    cell = rows.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].BatchNo);

                    cell = rows.CreateCell(1);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Name);

                    cell = rows.CreateCell(2);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Model);

                    cell = rows.CreateCell(3);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Brand);

                    cell = rows.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Num.ToString("G0"));

                    cell = rows.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].UnitName.ToString());

                    cell = rows.CreateCell(6);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(7);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(8);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(9);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(10);
                    cell.CellStyle = cellLeft3;
                    cell.SetCellValue(list[i].Model);

                    cell = rows.CreateCell(11);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Brand);

                    cell = rows.CreateCell(12);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].Num.ToString("G0"));

                    cell = rows.CreateCell(13);
                    cell.CellStyle = cellLeft2;
                    cell.SetCellValue(list[i].Remark);

                    y++;
                }
                IRow rowSum = isheet.CreateRow(y);
                rowSum.HeightInPoints = 20;
                cell = rowSum.CreateCell(0);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("总计（元）：");
                cell = rowSum.CreateCell(1);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(2);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(3);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(4);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(5);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(6);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(7);
                cell.CellStyle = cellRight2;
                cell.SetCellValue("0.00");
                cell = rowSum.CreateCell(8);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(9);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(10);
                cell.CellStyle = cellTitle6;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(11);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(12);
                cell.CellStyle = cellTitle3;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(13);
                cell.CellStyle = cellTitle4;
                cell.SetCellValue("");

                //var fileInfo = FileHelper.GetSaveFileInfo(env.ContentRootPath, exlName);
                //using (MemoryStream ms = new MemoryStream())
                //{
                //    //将工作簿的内容放到内存流中
                //    iwork.Write(ms);
                //    iwork.Close();
                //    ms.Flush();
                //    ms.Position = 0;
                //    using (FileStream fs = new FileStream(fileInfo.filePath, FileMode.Create, FileAccess.Write))
                //    {
                //        byte[] data = ms.ToArray();
                //        fs.Write(data, 0, data.Length);
                //        fs.Flush();
                //    }
                //}
                //return baseSucc<object>(new { fileInfo.url, fileInfo.fileName }, 1, "导出成功");

            }

            using var stream = new MemoryStream();
            iwork.Write(stream, true);
            var excelBytes = stream.ToArray();
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", exlName);
        }
        /// <summary>
        /// 采购物品明细列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasedetaillistfind")]
        public async Task<Result<PageModel<VDcPurchaseDetailList>>> DcPurchaseDetailList_Find([FromBody] VDcPurchaseDetailListParam param)
        {
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.unitId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                return baseFailed<PageModel<VDcPurchaseDetailList>>("你无权访问！");
            }
            PageModel<VDcPurchaseDetailList> pg = await dcPurchaseListManager.GetDetailPaged(param);
            if (param.isFirst)
            {
                var listCatalog = await dcBaseCatalogManager.Query(f => f.Depth == 1 && f.Nature == 1);
                List<dropdownModel> list = listCatalog.OrderBy(f => f.Sort).Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name.ToString() }).ToList();
                pg.Other = new { listCatalog = list };
            }
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }

        /// <summary>
        /// 采购物品明细列表-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasedetaillistexport")]
        //<used>0</used>
        public async Task<IActionResult> DcPurchaseDetailList_Export([FromBody] VDcPurchaseDetailListParam param)
        {
            Result r = new Result();
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.unitId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                param.unitId = 0;
            }
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<VDcPurchaseDetailList> pg = await dcPurchaseListManager.GetDetailPaged(param); ;
            var excelBytes = await new ExcelHelper<VDcPurchaseDetailList>().ExportExecl(pg.data.ToList(), "采购明细", new string[] { "AuditYear", "SchoolName", "SecondName", "Name", "Model", "UnitName", "Num" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "采购明细.xlsx");
        }

        /// <summary>
        /// 根据Id查询采购清单详细数据-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaselistfindbyid")]
        //<used>0</used>
        public async Task<Result<DcPurchaseListDto>> DcPurchaseList_FindById(long id)
        {
            var m = await dcPurchaseListManager.GetById(id);
            if (m != null)
            {
                return baseSucc(mapper.Map<DcPurchaseListDto>(m), 1, "查询成功");
            }
            else
            {
                return baseFailed<DcPurchaseListDto>("数据不存在");
            }
        }

        /// <summary>
        /// 已填报列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseorderfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcPurchaseOrderList>>> DcPurchaseOrder_Find([FromBody] VDcPurchaseOrderListParam param)
        {
            param.unitId = user.UnitId;
            param.userId = user.UserId;
            PageModel<VDcPurchaseOrderList> pg = await dcPurchaseOrderManager.GetUserPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }

        /// <summary>
        /// 从基础库添加列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasebycatalogfind")]
        //<used>0</used>
        public async Task<Result<PageModel<DcSchoolCatalogDto>>> DcPurchaseByCatalog_Find([FromBody] DcSchoolCatalogParam param)
        {
            Result r = new Result();

            var schoolExtension = await schoolExtensionManager.GetByUnitId(user.UnitId);
            PageModel<DcSchoolCatalogDto> page = new PageModel<DcSchoolCatalogDto>();
            if (schoolExtension.Count > 0)
            {
                param.unitId = user.UnitId;
                param.CountyId = user.UnitPId;
                param.SchoolStage = schoolExtension.FirstOrDefault().SchoolStage;
                var list = await dcSchoolCatalogManager.PurchaseListByCatalog_Find(param);
                page.data = list;
                page.dataCount = param.totalCount;
                return baseSucc(page, page.dataCount, "查询成功", page.Other);
            }
            else
            {
                return baseFailed<PageModel<DcSchoolCatalogDto>>("尚未设置学段，请联系校管理员设置学段后再填报！");

            }
        }

        /// <summary>
        /// 物品批量添加采购车-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasebatchadd")]
        //<used>0</used>
        public async Task<Result<string>> DcPurchaseBatchAdd([FromBody] List<DcPurchaseListDto> o)
        {
            int successNum = 0; //执行成功数量
            foreach (var m in o)
            {
                m.UnitId = user.UnitId;
                m.UserId = user.UserId;
                var r = await dcPurchaseListManager.Insert(m);
                if (r.flag == 1)
                {
                    successNum += 1;
                }
            }
            if (o.Count == successNum)
            {
                return baseSucc<string>("添加成功", 1);
            }
            else
            {
                return baseFailed<string>(string.Format("共添加{0}条物品数据，成功{1}条，失败{2}条。失败可能原因：物品已被禁用或采购数量不合法。", o.Count, successNum, (o.Count - successNum)));
            }
        }

        /// <summary>
        /// 物品单条加入采购车-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseadd")]
        //<used>1</used>
        public async Task<Result<string>> DcPurchaseAdd([FromBody] DcPurchaseListDto o)
        {
            o.UserId = user.UserId;
            o.UnitId = user.UnitId;
            var r = await dcPurchaseListManager.Insert(o);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 物品采购申请-保存
        /// </summary>
        /// <param name="purchaseOrderId"></param>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseapply")]
        //<used>1</used>
        public async Task<Result<string>> DcPurchaseApply(long purchaseOrderId, string ids)
        {
            var r = await dcPurchaseListManager.Apply(purchaseOrderId, ids, user.UserId, user.UnitId);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 物品采购清单-删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaselistdelete")]
        //<used>0</used>
        public async Task<Result<string>> DcPurchaseListDelete(long id)
        {
            var r = await dcPurchaseListManager.Delete(id, user.UserId, user.UnitId);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 物品采购单-删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseorderdelete")]
        //<used>0</used>
        public async Task<Result<string>> DcPurchaseOrderDelete(long id)
        {
            var r = await dcPurchaseOrderManager.Delete(id, user.UserId, user.UnitId);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 采购物品清单修改-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaselistupdate")]
        //<used>1</used>
        public async Task<Result<string>> DcPurchaseListUpdate([FromBody] DcPurchaseListDto o)
        {
            o.UserId = user.UserId;
            o.SchoolId = user.UnitId;
            var r = await dcPurchaseListManager.Update(o);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 通用物品待审批列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseauditfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcPurchaseOrderList>>> DcPurchaseAudit_Find([FromBody] VDcPurchaseOrderListParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            PageModel<VDcPurchaseOrderList> pg = await dcPurchaseOrderManager.GetUserPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }

        /// <summary>
        /// 通用物品已审批列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseauditedfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VDcPurchaseOrderAudited>>> DcPurchaseAudited_Find([FromBody] VDcPurchaseOrderAuditedParam param)
        {
            param.IsShow = 1;
            param.unitId = user.UnitId;
            param.userId = user.UserId;
            PageModel<VDcPurchaseOrderAudited> pg = await dcPurchaseOrderManager.GetrAuditedPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }
        /// <summary>
        /// 通用物品采购审批-审核
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseaudit")]
        //<used>1</used>
        public async Task<Result<string>> DcPurchaseAudit([FromBody] PurchaseResultDto model)
        {
            model.unitId = user.UnitId;
            model.userId = user.UserId;
            model.IsWithdraw = 1;
            var r = await dcPurchaseApprovalManager.InsertUpdate(model);
            if (r.flag > 0)
            {
                return baseSucc<string>(r.msg, 1);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 通用物品采购审批-撤销
        /// </summary>
        /// <param name="id"></param>
        /// <param name="purchaseOrderId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasewithdraw")]
        //<used>1</used>
        public async Task<Result<string>> DcPurchaseWithdraw(long id, long purchaseOrderId)
        {
            var r = await dcPurchaseApprovalManager.Withdraw(id, purchaseOrderId, user.UserId, user.UnitId);
            if (r.flag > 0)
            {
                string rows = "";
                if (r.data.rows != null)
                {
                    rows = r.data.rows.ToString();
                }
                return baseSucc(rows, 1, r.msg);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 获取退回原因-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasenopassreasonget")]
        //<used>1</used>
        public async Task<Result<object>> DcPurchaseNoPassReasonGet(long id)
        {
            var param = new DcPurchaseApprovalParam();
            param.PurchaseOrderId = id;
            var list = await dcPurchaseApprovalManager.Find(param);
            if (list != null && list.Count > 0)
            {
                var m = list.OrderByDescending(f => f.Id).FirstOrDefault();
                return baseSucc<object>(new { ApprovalRemark = list.OrderByDescending(m => m.Id).FirstOrDefault().ApprovalRemark }, 1, "查询成功");
            }
            else
            {
                return baseFailed<object>("执行失败。");
            }
        }

        /// <summary>
        /// 采购已生成计划列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseendfind")]
        public async Task<Result<PageModel<VDcPurchaseOrderList>>> DcPurchaseEnd_Find([FromBody] VDcPurchaseOrderListParam param)
        {
            param.unitId = user.UnitId;
            param.Statuz = DcPurchaseApprovalStatuz.End.ObjToInt();
            PageModel<VDcPurchaseOrderList> pg = await dcPurchaseOrderManager.GetUserPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }
        /// <summary>
        /// 采购已生成计划实体-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseendgetbyid")]
        public async Task<Result<VDcPurchaseOrderList>> DcPurchaseEnd_GetById(long id)
        {
            var param = new VDcPurchaseOrderListParam();
            param.Id = id;
            PageModel<VDcPurchaseOrderList> pg = await dcPurchaseOrderManager.GetUserPaged(param);
            VDcPurchaseOrderList entity = null;

            PageModel<VDcPurchaseOrderAudited> pgLog = null;
            object headers = null;
            if (pg.data != null && pg.data.Count > 0)
            {
                entity = pg.data.FirstOrDefault();
                int isNeedApproval = 0;
                if (entity.Statuz == DcPurchaseApprovalStatuz.WaitAudit.ObjToInt())
                {
                    var paramPurchase = new DcPurchaseListParam();
                    paramPurchase.PurchaseOrderId = entity.Id;
                    var listPurchase = await dcPurchaseListManager.Find(paramPurchase);
                    if (listPurchase != null && listPurchase.Count > 0)
                    {
                        var paramModel = new DcSchoolMaterialModelParam();
                        paramModel.Ids = string.Join(",", listPurchase.Select(m => m.SchoolMaterialModelId));
                        paramModel.IsNeedApproval = 1;
                        var listModel = await dcSchoolMaterialModelManager.Find(paramModel);
                        if (listModel != null && listModel.Count > 0)
                        {
                            isNeedApproval = 1;
                        }
                    }
                }
                headers = new { IsNeedApproval = isNeedApproval };
                VDcPurchaseOrderAuditedParam paramLog = new VDcPurchaseOrderAuditedParam();
                paramLog.IsShow = 1;
                paramLog.PurchaseOrderId = id;
                paramLog.pageSize = int.MaxValue;
                paramLog.sortModel = new List<SortBaseModel>();
                paramLog.sortModel.Add(new SortBaseModel() { SortCode = "ApprovalStatus", SortType = "asc" });
                pgLog = await dcPurchaseOrderManager.GetrAuditedPaged(paramLog);
            }
            return baseSucc(entity, pg.dataCount, "查询成功", pgLog.data, headers);
        }
        /// <summary>
        /// 采购已生成计划列表-导出
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseendexport")]
        public async Task<IActionResult> DcPurchaseEnd_Export([FromBody] DcPurchaseListParam param)
        {
            Result r = new Result();
            string exlName = "危化品采购清单";
            #region workbook

            IWorkbook iwork = new HSSFWorkbook();
            IFont font = iwork.CreateFont();
            //font.IsBold = true;
            font.FontHeightInPoints = 10;

            IFont font1 = iwork.CreateFont();
            font1.IsBold = true;
            font1.FontHeightInPoints = 16;

            IFont font2 = iwork.CreateFont();
            font2.IsBold = true;
            font2.FontHeightInPoints = 10;

            IFont fontRed = iwork.CreateFont();
            fontRed.FontHeightInPoints = 10;
            fontRed.Color = NPOI.HSSF.Util.HSSFColor.Red.Index;

            ICellStyle cellstyle = iwork.CreateCellStyle();
            cellstyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            //cellstyle.WrapText = true;
            cellstyle.SetFont(font1);
            cellstyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellRed = iwork.CreateCellStyle();
            cellRed.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            //cellRed.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Red.Index;
            //cellRed.FillPattern = FillPattern.SolidForeground;
            cellRed.WrapText = true;
            cellRed.SetFont(fontRed);
            cellRed.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellLeft = iwork.CreateCellStyle();
            cellLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellLeft.WrapText = true;
            cellLeft.SetFont(font);
            cellLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellRight = iwork.CreateCellStyle();
            cellRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellRight.SetFont(font);
            cellRight.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellCenter = iwork.CreateCellStyle();
            cellCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellCenter.SetFont(font);
            cellCenter.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNull = iwork.CreateCellStyle();
            cellNull.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            //cellstyle.WrapText = true;


            cellNull.SetFont(font1);
            cellNull.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNullLeft = iwork.CreateCellStyle();
            cellNullLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellNullLeft.SetFont(font2);
            cellNullLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;



            ICellStyle cellTitle = iwork.CreateCellStyle();
            cellTitle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle.SetFont(font2);
            cellTitle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            #endregion

            #region sheettitle

            //建立一个名为Sheet1的工作表
            NPOI.SS.UserModel.ISheet isheet = iwork.CreateSheet("危化品采购清单");
            isheet.PrintSetup.PaperSize = 9;
            ICell cell = null;
            //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行
            NPOI.SS.Util.CellRangeAddress region = new NPOI.SS.Util.CellRangeAddress(0, 0, 0, 11);
            isheet.AddMergedRegion(region);

            NPOI.SS.UserModel.IRow rowTitle = isheet.CreateRow(0);
            rowTitle.HeightInPoints = 23;

            cell = rowTitle.CreateCell(0);
            cell.CellStyle = cellNull;
            cell.SetCellValue("危化品采购清单");

            for (int i = 0; i <= 11; i++)
            {
                if (i == 0 || i == 2 || i == 3)
                {
                    isheet.SetColumnWidth(i, 20 * 256);
                }
                else if (i == 4 || i == 5 || i == 6 || i == 7)
                {
                    isheet.SetColumnWidth(i, 8 * 256);
                }
                else
                {
                    isheet.SetColumnWidth(i, 15 * 256);
                }
            }

            NPOI.SS.Util.CellRangeAddress region2 = new NPOI.SS.Util.CellRangeAddress(1, 4, 0, 11);
            isheet.AddMergedRegion(region2);
            IRow msgRow1 = isheet.CreateRow(1);
            cell = msgRow1.CreateCell(0);
            cell.CellStyle = cellRed;
            cell.SetCellValue("备注：1、供应商必填项：单价、金额；选填项：有效期至、质保期，危化品如没有效期和质保期，则不填；其它项不得修改、删除！\r\n          2、当“供货品牌”与“采购品牌”不一致，或“采购品牌”为空时，必须填写“供货品牌”；在批量入库时需检查“供货品牌”在品牌库是否存在。\r\n          3、“有效期至”填写格式如：2019/03/15；“质保期”填写格式如：9。\r\n          4、送货时需提供电子版本。");

            NPOI.SS.Util.CellRangeAddress region3 = new NPOI.SS.Util.CellRangeAddress(5, 5, 0, 11);
            isheet.AddMergedRegion(region3);
            IRow msgRow4 = isheet.CreateRow(5);
            msgRow4.HeightInPoints = 23;
            cell = msgRow4.CreateCell(0);
            cell.CellStyle = cellNullLeft;
            cell.SetCellValue("供应商全称：");

            NPOI.SS.UserModel.IRow row1 = isheet.CreateRow(6);
            row1.HeightInPoints = 23;

            cell = row1.CreateCell(0);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("ID");

            cell = row1.CreateCell(1);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("申请批次");

            cell = row1.CreateCell(2);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("危化品名称");

            cell = row1.CreateCell(3);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("规格属性");

            cell = row1.CreateCell(4);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("数量");

            cell = row1.CreateCell(5);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("单位");

            cell = row1.CreateCell(6);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("单价");

            cell = row1.CreateCell(7);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("金额");

            cell = row1.CreateCell(8);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("采购品牌");

            cell = row1.CreateCell(9);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("供货品牌");

            cell = row1.CreateCell(10);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("有效期至");

            cell = row1.CreateCell(11);
            cell.CellStyle = cellTitle;
            cell.SetCellValue("质保期(月)");

            #endregion

            #region 打印重复表头
            //横向打印
            isheet.PrintSetup.Landscape = true;
            //缩放比：100% 不缩放
            isheet.PrintSetup.Scale = 100;
            //不缩放到一页
            isheet.FitToPage = false;
            //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
            isheet.RepeatingRows = new CellRangeAddress(6, 6, 0, int.MaxValue);
            #endregion

   
            //string where = string.Format(" PurchaseOrderId = {0} AND SchoolId = {1} AND UserId = {2} AND Statuz = 100", hiddenOrderId.Value, UnitId, UserId);
            //DcPurchaseListService dcPurchaseListManager = new DcPurchaseListService();
            //var list = dcPurchaseListManager.Find(where);

            param.unitId = user.UnitId;
            param.userId = user.UserId;
            param.Statuz = 100;
            var list = await dcPurchaseListManager.Find(param);
            if (list.Count > 0)
            {
                int y = 7;
                for (int i = 0; i < list.Count; i++)
                {
                    IRow rows = isheet.CreateRow(y);
                    rows.HeightInPoints = 20;
                    cell = rows.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Id.ToString());

                    cell = rows.CreateCell(1);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].BatchNo);

                    cell = rows.CreateCell(2);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Name.ToString());

                    cell = rows.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Model.ToString());

                    cell = rows.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Num.ToString("G0"));

                    cell = rows.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].UnitName.ToString());

                    cell = rows.CreateCell(6);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(7);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(8);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Brand);

                    cell = rows.CreateCell(9);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(10);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");

                    cell = rows.CreateCell(11);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    y++;
                }
                IRow rowSum = isheet.CreateRow(y);
                rowSum.HeightInPoints = 20;
                cell = rowSum.CreateCell(0);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(1);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(2);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("总计（元）：");
                cell = rowSum.CreateCell(3);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(4);
                cell.CellStyle = cellRight;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(5);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(6);
                cell.CellStyle = cellRight;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(7);
                cell.CellStyle = cellRight;
                cell.SetCellValue("0.00");
                cell = rowSum.CreateCell(8);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(9);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(10);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
                cell = rowSum.CreateCell(11);
                cell.CellStyle = cellCenter;
                cell.SetCellValue("");
            }
            //
            using var stream = new MemoryStream();
            iwork.Write(stream, true);
            var excelBytes = stream.ToArray();
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", exlName);

        }
        /// <summary>
        /// 物品采购-撤销
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaserevoke")]
        public async Task<Result<string>> DcPurchase_Revoke(long id)
        {
            var r = await dcPurchaseOrderManager.Revoke(id, user.UnitId, user.UserId);
            if (r.flag > 0)
            {
                string rows = "";
                if (r.data.rows != null)
                {
                    rows = r.data.rows.ToString();
                }
                return baseSucc(rows, 1, r.msg);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 单位采购汇总表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolpurchasesummarylistfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VDcPurchaseDetailList>>> DcSchoolPurchaseSummaryList_Find([FromBody] DcPurchaseOrderParam param)
        {
            PageModel<VDcPurchaseDetailList> page = new PageModel<VDcPurchaseDetailList>();
            if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                return baseSucc(page, page.dataCount, "查询成功");
            }
            var list = await dcPurchaseOrderManager.GetSchoolPurchaseSummaryList(param);
            if (list != null && list.Count > 0)
            {
                page.data = list;
                page.dataCount = param.totalCount;
            }
            return baseSucc(page, page.dataCount, "查询成功");
        }
        /// <summary>
        /// 单位采购汇总表-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolpurchasesummarylistexport")]
        public async Task<IActionResult> DcSchoolPurchaseSummaryList_Export([FromBody] DcPurchaseOrderParam param)
        {
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            var list = await dcPurchaseOrderManager.GetSchoolPurchaseSummaryList(param);
            var excelBytes = await new ExcelHelper<VDcPurchaseDetailList>().ExportExecl(list, "采购汇总", new string[] { "AuditYear", "SchoolName", "SecondName", "Name", "Model", "UnitName", "Num" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "采购汇总.xlsx");
        }

        #endregion

        #region 公共方法
        /// <summary>
        /// 根据上级Id获取分类-查询
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccataloggetbypid")]
        //<used>1</used>
        public async Task<Result<PageModel<DcSchoolCatalogDto>>> DcCatalog_GetByPid(long pid)
        {
            var param = new DcSchoolCatalogParam();
            param.OptType = 2;
            param.unitId = user.UnitId;
            var entityUnit = await unitManager.QueryById(user.UnitId);
            if (entityUnit != null)
            {
                param.CountyId = entityUnit.PId;
            }
            param.Pid = pid;
            param.statuz = 1;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Sort", SortType = "ASC" });
            var list = await dcSchoolCatalogManager.Find(param);
            PageModel<DcSchoolCatalogDto> page = new PageModel<DcSchoolCatalogDto>();
            page.data = mapper.Map<List<DcSchoolCatalogDto>>(list);
            page.dataCount = param.totalCount;
            return baseSucc(page, param.totalCount, "查询成功");
        }

        /// <summary>
        /// 获取维护品二级分类-查询
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccataloggetclasstwo")]
        //<used>1</used>
        public async Task<Result<PageModel<DcSchoolCatalogDto>>> DcCatalog_GetClassTwo(long unitId)
        {
            PageModel<DcSchoolCatalogDto> page = new PageModel<DcSchoolCatalogDto>();
            var param = new DcSchoolCatalogParam();
            if (user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                if (unitId > 0)
                {
                    param.unitId = unitId;
                    param.Depth = 1;
                    param.statuz = 1;
                    param.sortModel = new List<SortBaseModel>();
                    param.sortModel.Add(new SortBaseModel() { SortCode = "Sort", SortType = "ASC" });
                    var list = await dcSchoolCatalogManager.Find(param);
                    page.data = mapper.Map<List<DcSchoolCatalogDto>>(list);
                    page.dataCount = param.totalCount;
                }
                else
                {
                    var paramBase = new DcBaseCatalogParam();
                    paramBase.Depth = 1;
                    paramBase.Nature = 1;
                    param.sortModel = new List<SortBaseModel>();
                    param.sortModel.Add(new SortBaseModel() { SortCode = "Sort", SortType = "ASC" });
                    var list = await dcBaseCatalogManager.Find(paramBase);
                    if (list != null && list.Count > 0)
                    {
                        page.data = (from item in list
                                     select new DcSchoolCatalogDto()
                                     {
                                         Id = item.Id,
                                         Pid = item.Pid,
                                         Name = item.Name,
                                         Code = item.Code,
                                         Brief = item.Brief,
                                         Depth = item.Depth,
                                         Sort = item.Sort,
                                         Path = item.Path,
                                         UnitsMeasurement = item.UnitsMeasurement,
                                         SchoolId = 0,
                                         BaseCatalogId = item.Id,
                                         IsNeedBack = false,
                                         IsCommonUse = 0,
                                         ModelCount = 0,
                                         Statuz = item.Statuz,
                                         BrandCount = 0,
                                         Nature = item.Nature,
                                         IsDeleted = item.IsDeleted,
                                         CreateId = item.CreateId,
                                         CreateBy = item.CreateBy,
                                         CreateTime = item.CreateTime,
                                         ModifyId = item.ModifyId,
                                         ModifyBy = item.ModifyBy,
                                         ModifyTime = item.ModifyTime,
                                         Version = item.Version
                                     }).ToList();
                    }
                    page.dataCount = param.totalCount;
                }
            }
            else
            {
                param.OptType = 2;
                param.unitId = user.UnitId;
                if (user.UnitTypeId == UnitTypes.School.ObjToInt())
                {
                    param.CountyId = user.UnitPId;
                }
                param.Depth = 1;
                param.statuz = 1;
                param.sortModel = new List<SortBaseModel>();
                param.sortModel.Add(new SortBaseModel() { SortCode = "Sort", SortType = "ASC" });
                var list = await dcSchoolCatalogManager.Find(param);
                page.data = mapper.Map<List<DcSchoolCatalogDto>>(list);
                page.dataCount = param.totalCount;
            }
            return baseSucc(page, param.totalCount, "查询成功");
        }

        /// <summary>
        /// 获取可使用的物品型号-查询
        /// </summary>
        /// <param name="id"></param>
        /// <remarks>
        /// 注意：只有物品领用时使用(包含多余库存的数据)
        /// </remarks>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogmodelgetv2")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcSchoolCatalogModel_GetV2(long id)
        {
            VDcApplyFillListParam param = new VDcApplyFillListParam();
            param.SchoolId = user.UnitId;
            param.SchoolCatalogId = id;
            param.SchoolMaterialModelIdgt = 0;
            PageModel<VDcApplyFillList> pageList = await dcApplyManager.GetFillPaged(param);
            PageModel<object> page = new PageModel<object>();
            page.data = pageList.data.Select(f => new { f.SchoolMaterialModelId, f.Model }).Distinct().ToList<object>();
            page.dataCount = page.data.Count;
            return baseSucc(page, param.totalCount, "查询成功");
        }

        /// <summary>
        /// 根据型号获取可用品牌-查询 
        /// </summary>
        /// <param name="schoolCatalogId"></param>
        /// <param name="modelId"></param>
        /// <remarks>
        /// 注意：只有物品领用时使用(包含多余库存的数据)
        /// </remarks>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogbrandgetbymodelidv2")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcSchoolCatalogBrand_GetByModelIdV2(long schoolCatalogId, long modelId)
        {
            VDcApplyFillListParam param = new VDcApplyFillListParam();
            param.SchoolId = user.UnitId;
            param.SchoolCatalogId = schoolCatalogId;
            if (modelId > 0)
            {
                param.SchoolMaterialModelId = modelId;
            }
            else
            {
                param.SchoolMaterialModelIdgt = 0;
            }
            PageModel<VDcApplyFillList> pageList = await dcApplyManager.GetFillPaged(param);
            PageModel<object> page = new PageModel<object>();
            page.data = pageList.data.Select(f => new { f.SchoolMaterialBrandId, f.Brand }).Distinct().ToList<object>();
            page.dataCount = page.data.Count;
            return baseSucc(page, param.totalCount, "查询成功");
        }

        /// <summary>
        /// 获取物品型号-查询
        /// </summary>
        /// <param name="schoolCatalodId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialmodelgetbycatalogid")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcSchoolMaterialModel_GetByCatalogId(long schoolCatalodId)
        {
            var schoolExtension = await schoolExtensionManager.GetByUnitId(user.UnitId);
            if (schoolExtension.Count > 0)
            {
                // string where = string.Format("SchoolCatalogId = {0} AND Statuz = 1 AND (SchoolId = {1} OR SchoolId = {2}) AND SchoolStagez LIKE '%,{3},%'", schoolCatalodId, UnitId, VUser.UnitPid, schoolExtension.FirstOrDefault().SchoolStage);
                var param = new DcSchoolMaterialModelParam();
                param.SchoolCatalogId = schoolCatalodId;
                param.Statuz = 1;
                param.unitId = user.UnitId;
                param.UnitPid = user.UnitPId;
                param.OptType = 2;

                param.SchoolStage = schoolExtension.FirstOrDefault().SchoolStage;
                var pagemodel = await dcSchoolMaterialModelManager.GetPaged(param);
                PageModel<object> page = new PageModel<object>();
                if (pagemodel.data != null)
                {
                    page.dataCount = pagemodel.dataCount;
                    page.data = (from item in pagemodel.data select new { Id = item.Id, Model = item.Model }).ToList<object>();
                }
                return baseSucc(page, param.totalCount, "查询成功");
            }
            else
            {
                return baseFailed<PageModel<object>>("尚未设置学段，请联系校管理员设置学段后再填报！");
            }
        }

        /// <summary>
        /// 获取物品可使用的品牌-查询
        /// </summary>
        /// <param name="schoolCatalogId"></param>
        /// <param name="modelId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmodelbrandgetbrand")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcSchoolModelBrand_GetBrand(long schoolCatalogId, long modelId)
        {
            Result r = new Result();
            var param = new VDcSchoolModelBrandParam();
            param.SchoolCatalogId = schoolCatalogId;
            param.SetStatuz = 1;
            param.unitId = user.UnitId;
            param.UnitPid = user.UnitPId;
            if (modelId > 0)
            {
                param.SchoolMaterialModelId = modelId;
            }
            else
            {
                param.SchoolMaterialBrandIdgt = 0;
            }
            List<VDcSchoolModelBrand> list = await dcSchoolModelBrandManager.GetStatistics(param);
            PageModel<object> page = new PageModel<object>();
            page.data = list.Select(m => new { m.SchoolMaterialBrandId, m.Brand }).Distinct().ToList<object>();
            return baseSucc(page, page.data.Count, "查询成功");
        }


        //[HttpPost]
        //[Route("dcschoolcatalogfindall")]
        ////<used>0</used>
        //public async Task<Result> DcSchoolCatalog_FindAll()
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}

        /// <summary>
        /// 物品-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialinsertupdate")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolMaterial_InsertUpdate([FromBody] DcSchoolMaterialDto model)
        {
            if (model.LvCompanyId == 0 && string.IsNullOrEmpty(model.CompanyName))
            {
                return baseFailed<string>("请选择供应商");
            }
            model.Sum = Math.Round(model.Num * model.Price, 2);
            model.SchoolId = user.UnitId;
            model.UserId = user.UserId;
            model.DangerChemicalsLevel = ApplicationConfig.DangerChemicalsLevel.ObjToInt();
            var r = await dcSchoolMaterialManager.InsertUpdate(model);
            if (r.flag == 1 && r.Id > 0)
            {
                dcApplyManager.AutoStockNumUpdate(r.Id);
            }
            if (r.flag > 0)
            {
                string rows = "";
                if (r.data.rows != null)
                {
                    rows = r.data.rows.ToString();
                }
                return baseSucc(rows, 1, r.msg);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 物品分类搜索-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogcommonusefind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolCatalogList>>> DcSchoolCatalogCommonUse_Find([FromBody] VDcSchoolCatalogListParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            var entityUnit = await unitManager.QueryById(user.UnitId);
            if (entityUnit != null)
            {
                param.UnitPid = entityUnit.PId;
            }
            param.OptType = 1;
            PageModel<VDcSchoolCatalogList> pg = await dcSchoolCatalogManager.GetStatisticsPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 物品分类下拉框数据-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogcomboboxget")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcSchoolCatalogCombobox_Get()
        {
            var param = new VDcSchoolCatalogListParam();
            param.Statuz = 1;
            param.unitId = user.UnitId;
            var entityUnit = await unitManager.QueryById(user.UnitId);
            if (entityUnit != null)
            {
                param.UnitPid = entityUnit.PId;
            }
            param.OptType = 1;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "code", SortType = "asc" });
            param.pageSize = int.MaxValue;
            var pagemodel = await dcSchoolCatalogManager.GetStatisticsPaged(param);
            PageModel<object> page = new PageModel<object>();
            if (pagemodel != null && pagemodel.dataCount > 0)
            {
                //组装下拉框格式数据
                var data = from p in pagemodel.data
                           select new
                           {
                               Id = p.Id,
                               Name = p.SecondName + " > " + p.Name,
                               UnitName = p.UnitsMeasurement
                           };
                page.data = data.ToList<object>();
                page.dataCount = page.data.Count;
            }
            return baseSucc(page, page.dataCount, "查询成功");
        }

        /// <summary>
        /// 获取单位物品类别选择-查询
        /// </summary>
        /// <param name="commonUse"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogfindcommonuseall")]
        //<used>1</used>
        public async Task<Result<PageModel<DcSchoolCatalogDto>>> DcSchoolCatalog_FindCommonUseAll(int commonUse = 0)
        {
            PageModel<DcSchoolCatalogDto> page = new PageModel<DcSchoolCatalogDto>();

            var param = new DcSchoolCatalogParam();
            param.unitId = user.UnitId;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                var entityUnit = await unitManager.QueryById(user.UnitId);
                if (entityUnit != null)
                {
                    param.CountyId = entityUnit.PId;
                }
            }
            param.statuz = 1;
            param.OptType = 2;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Path", SortType = "asc" });
            var list = await dcSchoolCatalogManager.Find(param);
            if (list != null && list.Count > 0)
            {
                var list2 = list.Where(m => m.Depth == 1).ToList();
                foreach (var l in list2)
                {
                    if (list.Find(m => m.Pid == l.Id) != null)
                    {
                        continue;
                    }
                    list.Remove(l);
                }
                var list1 = list.Where(m => m.Depth == 0).ToList();
                foreach (var l in list1)
                {
                    if (list.Find(m => m.Pid == l.Id) != null)
                    {
                        continue;
                    }
                    list.Remove(l);
                }
                page.dataCount = list.Count;
                page.data = mapper.Map<List<DcSchoolCatalogDto>>(list);
                if (commonUse == 1)
                {
                    var commonUseList = list.Where(m => m.IsCommonUse == 1).ToList();
                    if (commonUseList != null && commonUseList.Count > 0)
                    {
                        var arrPidList = commonUseList.Select(m => m.Pid).Distinct();
                        if (arrPidList != null && arrPidList.Count() > 0)
                        {
                            var commonUsePList = list.Where(m => arrPidList.Contains(m.Id)).ToList();
                            if (commonUsePList != null && commonUsePList.Count > 0)
                            {
                                commonUseList.AddRange(commonUsePList);
                                commonUseList = commonUseList.OrderBy(m => m.Path).ToList();
                                page.Other = mapper.Map<List<DcSchoolCatalogDto>>(commonUseList);
                            }

                        }
                    }
                }
            }
            return baseSucc(page, page.dataCount, "查询成功", page.Other);
        }

        #endregion

        #region 物品库管理
        /// <summary>
        /// 入库记录变更记录信息-查询
        /// </summary>
        /// <param name="SchoolMaterialId"></param>
        /// <param name="PurchaseListId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolcatalogrecord")]
        //<used>1</used>
        public async Task<Result<PageModel<DcSchoolMaterialDto>>> DcSchoolCatalog_Record(long SchoolMaterialId, long PurchaseListId)
        {
            PageModel<DcSchoolMaterialDto> page = new PageModel<DcSchoolMaterialDto>();
            List<DcSchoolMaterial> listMaterial = new List<DcSchoolMaterial>();
            DcSchoolMaterial schoolMaterial = await dcSchoolMaterialManager.GetById(SchoolMaterialId);
            if (schoolMaterial != null)
            {
                schoolMaterial.Brand = schoolMaterial.Brand == null ? "" : schoolMaterial.Brand;
                schoolMaterial.Model = schoolMaterial.Model == null ? "" : schoolMaterial.Model;
                schoolMaterial.Remark = schoolMaterial.Remark == null ? "" : schoolMaterial.Remark;

                listMaterial.Add(schoolMaterial);

                DcPurchaseList purchaseList = await dcPurchaseListManager.GetById(PurchaseListId);
                if (purchaseList != null)
                {
                    DcSchoolMaterial schoolMaterial1 = new DcSchoolMaterial();
                    schoolMaterial1.Name = purchaseList.Name;
                    schoolMaterial1.Brand = purchaseList.Brand == null ? "" : purchaseList.Brand;
                    schoolMaterial1.Model = purchaseList.Model == null ? "" : purchaseList.Model;
                    schoolMaterial1.Num = purchaseList.Num;
                    schoolMaterial1.UnitName = purchaseList.UnitName;
                    schoolMaterial1.Price = purchaseList.Price;
                    listMaterial.Add(schoolMaterial1);
                }
                page.data = mapper.Map<List<DcSchoolMaterialDto>>(listMaterial);
                page.dataCount = page.data.Count;
            }
            return baseSucc(page, page.dataCount, "查询成功");
        }

        /// <summary>
        /// 获取采购批次下拉框数据-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasebatchnofind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcPurchaseBatchNo_Find()
        {
            PageModel<object> page = new PageModel<object>();
            var param = new DcSchoolMaterialParam();
            param.SchoolId = user.UnitId;
            param.PurchaseBatchNo = "";
            var listMaterial = await dcSchoolMaterialManager.Find(param);
            if (listMaterial != null && listMaterial.Count > 0)
            {
                var material = listMaterial.Select(m => m.PurchaseBatchNo).Distinct().ToList();
                page.data = (from item in material
                             select new
                             {
                                 PurchaseBatchNo = item
                             }).ToList<object>();
                page.dataCount = page.data.Count;
            }
            return baseSucc(page, page.dataCount, "查询成功");
        }

        /// <summary>
        /// 入库审核 下一条显示-查询
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialnext")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolMaterialList>>> DcSchoolMaterial_Next(long Id)
        {
            Result r = new Result();
            var param = new VDcSchoolMaterialListParam();
            param.Idgt = Id;
            param.unitId = user.UnitId;
            param.Statuz = 0;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "ASC" });
            param.pageSize = 1;
            var pageData = await dcSchoolMaterialManager.GetListPaged(param);
            if (pageData != null && pageData.data != null && pageData.data.Count > 0)
            {
                return baseSucc(pageData, pageData.data.Count, "查询成功");
            }
            else
            {
                return baseSucc(new PageModel<VDcSchoolMaterialList>(), 0, "查询成功");
            }
        }

        /// <summary>
        /// 物品退货-保存
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialreturnlibrary")]
        //<used>1</used>
        public async Task<Result<string>> DcSchoolMaterial_ReturnLibrary(long id, int statuz)
        {
            var r = await dcSchoolMaterialManager.ReturnLibrary(id, statuz, user.UnitId, user.UserId);
            if (r.flag == 1 && r.Id > 0)
            {
                dcApplyManager.AutoStockNumUpdate(r.Id);
            }
            if (r.flag > 0)
            {
                string rows = "";
                if (r.data.rows != null)
                {
                    rows = r.data.rows.ToString();
                }
                return baseSucc(rows, 1, r.msg);
            }
            else
            {
                return baseFailed<string>(r.msg);
            }
        }

        /// <summary>
        /// 已退货清单列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmaterialbacklogfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcSchoolMaterialBackLog>>> DcSchoolMaterialBackLog_Find([FromBody] VDcSchoolMaterialBackLogParam param)
        {
            param.unitId = user.UnitId;
            PageModel<VDcSchoolMaterialBackLog> pg = await dcSchoolMaterialBackLogManager.GetStatisticsPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }


        //[HttpPost]
        //[Route("dcschoolmodelbrandgetbyid")]
        ////<used>0</used>
        //public async Task<Result> DcSchoolModelBrand_GetById(int id)
        //{
        //      Result r = new Result();
        //      r.flag = 1;
        //      r.msg = "";
        //      return r;
        //}

        /// <summary>
        /// 添加物品详细描述-设置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="imageUrl"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcschoolmodelbrandsetdetail")]
        //<used>0</used>
        public async Task<Result<string>> DcSchoolModelBrand_SetDetail(int id, string imageUrl, string remark)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }

        /// <summary>
        /// 采购申请单上传公安报备文件-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseorderuploadfile")]
        //<used>1</used>
        public async Task<Result<string>> DcPurchaseOrder_UploadFile([FromBody] AttachmentModel model)
        {
            var entity = await dcPurchaseOrderManager.GetById(model.Id);
            if (entity != null && entity.SchoolId == user.UnitId)
            {
                entity.ReportFile = model.Path;
                if (await dcPurchaseOrderManager.Update(entity))
                {
                    return baseSucc<string>("上传成功", 1);
                }
            }
            return baseFailed<string>("上传失败");
        }

        /// <summary>
        /// 根据Id采购申请单-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchaseordergetbyid")]
        //<used>1</used>
        public async Task<Result<DcPurchaseOrderDto>> DcPurchaseOrder_GetById(long id)
        {
            var model = await dcPurchaseOrderManager.GetById(id);
            if (model != null && model.SchoolId == user.UnitId)
            {
                return baseSucc<DcPurchaseOrderDto>(mapper.Map<DcPurchaseOrderDto>(model), 1, "查询成功");
            }
            return baseFailed<DcPurchaseOrderDto>("查询失败");
        }

        /// <summary>
        /// 批量修改存放地点-设置
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="addressId"></param>
        /// <param name="cabinetAddress"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcmaterialaddressbatchedit")]
        //<used>1</used>
        public async Task<Result<string>> DcMaterialAddress_BatchEdit(string ids, long addressId, string cabinetAddress)
        {
            if (!string.IsNullOrEmpty(ids))
            {
                int successCount = await dcSchoolMaterialManager.BatchUpdateAddress(ids, addressId, cabinetAddress, user.UnitId);
                int totalCount = ids.Split(',').Length;
                if (successCount < totalCount)
                {
                    return baseFailed<string>("共执行{0}条数据，其中成功{1}条，失败{2}条。失败可能原因：无权限操作。");
                }
                else
                {
                    return baseSucc<string>("批量修改成功", 1);
                }
            }
            else
            {
                return baseFailed<string>("非法操作。");
            }
        }

        /// <summary>
        /// 根据Id修改MSDS文件-保存
        /// </summary>
        /// <param name="id"></param>
        /// <param name="msdsFile"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("schoolmaterialmsdsfileedit")]
        //<used>1</used>
        public async Task<Result<string>> SchoolMaterial_MsdsFileEdit(long id, string msdsFile)
        {
            Result r = new Result();
            DcSchoolMaterial material = await dcSchoolMaterialManager.GetById(id);
            if (material != null)
            {
                if (material.SchoolId != user.UnitId)
                {
                    return baseFailed<string>("非法操作，您不能上传其它单位MSDS文件");
                }
                if (!string.IsNullOrEmpty(material.MsdsFile))
                {
                    return baseFailed<string>("非法操作，MSDS文件已上传不能修改");
                }
                material.MsdsFile = msdsFile;

                if (await dcSchoolMaterialManager.Update(material))
                {
                    return baseSucc<string>("上传成功", 1);
                }
            }
            return baseFailed<string>("数据异常，文件上传失败。");
        }
        #endregion

        #region 保障要求

        /// <summary>
        /// 加载制度与队伍建设数据信息-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("basefieldconfigfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcBaseFieldConfigDto>>> BaseFieldConfig_Find(long id)
        {
            PageModel<DcBaseFieldConfigDto> page = new PageModel<DcBaseFieldConfigDto>();
            long schoolId = user.UnitId;
            if (id > 0)
            {
                var entity = await unitManager.QueryById(id);
                if (entity != null && entity.PId == user.UnitId)
                {
                    schoolId = id;
                }
            }
            //先获取文本框内容
            var listInput = await dcBaseFieldConfigManager.GetInputList(schoolId, 1);
            page.data = mapper.Map<List<DcBaseFieldConfigDto>>(listInput);

            //获取附件显示内容 已使用附件配置，这个标题就不显示了。
            List<DcBaseFieldConfig> fieldValues = new List<DcBaseFieldConfig>();
            var msgdata = new Result<List<BAttachmentConfigDto>>();
            BAttachmentConfigParam param = new BAttachmentConfigParam();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            param.Statuz = 1;
            param.ModuleType = ModuleTypeEnum.DcTeamBuild.ObjToInt();
            PageModel<BAttachmentConfig> pg = await ibattachmentconfigservicesManager.GetPaged(param);
            if (pg.data != null && pg.data.Count > 0)
            {
                var attaachmentList = mapper.Map<List<BAttachmentConfigDto>>(pg.data);
                //存在获取值
                //获取该单位附件内容
                var fileValuesData = await dcBaseFieldConfigManager.GetPaged(new DcBaseFieldConfigParam { UnitId = schoolId, TypeBox = 2, pageIndex = 1, pageSize = int.MaxValue });
                if (fileValuesData != null && fileValuesData.data != null)
                {
                    var baseFieldConfigList = mapper.Map<List<DcBaseFieldConfigDto>>(fileValuesData.data);
                    baseFieldConfigList.ForEach(n => n.Ext = GetFileExt(n.FieldValue));
                    for (int i = 0; i < attaachmentList.Count; i++)
                    {
                        attaachmentList[i].AttachmentList = baseFieldConfigList.Where(m => m.RelationId == pg.data[i].FileCategory).ToList<object>();
                    }
                }
                page.Other = attaachmentList;
            }
            return baseSucc(page, page.data.Count, "查询成功", page.Other);
        }

        /// <summary>
        /// 附件信息-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("basefieldconfigsavefile")]
        //<used>1</used>
        public async Task<Result<long>> BaseFieldConfig_SaveFile([FromBody] DcBaseFieldConfigDto model)
        {
            DcBaseFieldConfig o = mapper.Map<DcBaseFieldConfig>(model);
            DcBaseFieldConfig config = await dcBaseFieldConfigManager.GetById(o.Id);
            if (config != null)
            {
                DcBaseFieldConfig dcFile = new DcBaseFieldConfig();
                dcFile.RelationId = o.Id;
                dcFile.FieldValue = o.FilePath;
                dcFile.TypeBox = 2;
                dcFile.UnitId = user.UnitId;
                dcFile.FieldName = o.Title;
                dcFile.Remark = config.Remark;
                var result = await dcBaseFieldConfigManager.Insert(dcFile);
                if (result > 0)
                {
                    return baseSucc<long>(result, 1, "添加成功");
                }
            }
            return baseFailed<long>("添加失败");

        }

        /// <summary>
        /// 删除附件-删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("basefieldconfigdeletefile")]
        //<used>1</used>
        public async Task<Result<string>> BaseFieldConfig_DeleteFile(long id)
        {
            Result r = new Result();
            DcBaseFieldConfig config = await dcBaseFieldConfigManager.GetById(id);
            if (config != null)
            {
                if (config.UnitId != user.UnitId)
                {
                    return baseFailed<string>("不能删除其它单位附件");
                }

                if (await dcBaseFieldConfigManager.DeleteById(id))
                {
                    return baseSucc<string>("删除成功", 1);
                }
            }
            return baseFailed<string>("删除失败");
        }

        /// <summary>
        /// 批量保存制度与队伍建设文本信息-保存
        /// </summary>
        /// <param name="model">{list:数据集合,attrs:附件集合[{Id:历史Id(新增的附件为0),RelationId:附件标识编码,FilePath:附件路径,Title:附件标题}]}</param>
        /// <returns></returns>
        [HttpPost]
        [Route("basefieldconfigsavelist")]
        //<used>1</used>
        public async Task<Result<string>> BaseFieldConfig_SaveList([FromBody] DcBaseFieldConfigModel model)
        {
            foreach (var team in model.list)
            {
                var config = (await dcBaseFieldConfigManager.GetPaged(new DcBaseFieldConfigParam { RelationId = team.Id, UnitId = user.UnitId, pageIndex = 1, pageSize = 1 })).data.FirstOrDefault();
                if (config != null)
                {
                    //更新
                    config.FieldValue = team.FieldValue;
                    await dcBaseFieldConfigManager.Update(config);
                }
                else
                {
                    DcBaseFieldConfig c = await dcBaseFieldConfigManager.GetById(team.Id);
                    if (c != null)
                    {
                        //插入
                        DcBaseFieldConfig dcFile = new DcBaseFieldConfig();
                        dcFile.RelationId = team.Id;
                        dcFile.FieldValue = team.FieldValue;
                        dcFile.TypeBox = c.TypeBox;
                        dcFile.UnitId = user.UnitId;
                        dcFile.FieldName = c.FieldName;
                        dcFile.Remark = c.Remark;
                        await dcBaseFieldConfigManager.Insert(dcFile);
                    }

                }
            }
            /***
             *1：处理附件，首先查出历史附件
             *2：判断历史是否删除，删除则更新删除。
             *3：判断是否存在本次上传的。本次上传的，存在历史不懂，不存在添加。
             */
            var msgdata = new Result<List<BAttachmentConfigDto>>();
            BAttachmentConfigParam param = new BAttachmentConfigParam();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            param.Statuz = 1;
            param.ModuleType = ModuleTypeEnum.DcTeamBuild.ObjToInt();
            PageModel<BAttachmentConfig> pgAttrConfig = await ibattachmentconfigservicesManager.GetPaged(param);

            //获取附件显示内容
            //var listFileFeild = await dcBaseFieldConfigManager.GetPaged(new DcBaseFieldConfigParam { UnitId = 0, TypeBox = 2, pageIndex = 1, pageSize = int.MaxValue });
            //获取该单位附件内容
            var listOldAttr = await dcBaseFieldConfigManager.GetPaged(new DcBaseFieldConfigParam { UnitId = user.UnitId, TypeBox = 2, pageIndex = 1, pageSize = int.MaxValue });
            //添加
            List<DcBaseFieldConfig> listAddAttr = new List<DcBaseFieldConfig>();
            if (model.attrs != null && model.attrs.Count > 0)
            {
                for (int i = 0; i < model.attrs.Count; i++)
                {
                    var item = model.attrs[i];
                    if (item.Id > 0)
                    {
                        if (listOldAttr != null && listOldAttr.data != null && listOldAttr.data.Count > 0)
                        {
                            var entity = listOldAttr.data.Where(m => m.Id == item.Id).FirstOrDefault();
                            if (entity != null)
                            {
                                listOldAttr.data.Remove(entity);
                            }
                        }
                    }
                    else
                    {
                        if (pgAttrConfig != null && pgAttrConfig.data != null)
                        {
                            var entityFeild = pgAttrConfig.data.Where(m => m.FileCategory == item.RelationId).FirstOrDefault();
                            if (entityFeild != null)
                            {
                                //添加
                                DcBaseFieldConfig dcFile = new DcBaseFieldConfig();
                                dcFile.RelationId = entityFeild.FileCategory;//分类Id
                                dcFile.FieldValue = item.FilePath;
                                dcFile.TypeBox = 2;
                                dcFile.UnitId = user.UnitId;
                                dcFile.FieldName = item.Title;
                                dcFile.Remark = entityFeild.Memo;
                                listAddAttr.Add(dcFile);
                            }
                        }
                    }
                }
            }
            //删除附件
            if (listOldAttr != null && listOldAttr.data != null && listOldAttr.data.Count > 0)
            {
                for (int i = 0; i < listOldAttr.data.Count; i++)
                {
                    var entity = listOldAttr.data[i];
                    entity.IsDeleted = true;
                    entity.ModifyBy = user.UserName;
                    entity.ModifyId = user.UserId;
                    entity.ModifyTime = DateTime.Now;
                    await dcBaseFieldConfigManager.Update(entity, new List<string>() { "IsDeleted", "ModifyBy", "ModifyId", "ModifyTime" });
                }
            }
            if (listAddAttr.Count > 0)
            {
                for (int i = 0; i < listAddAttr.Count; i++)
                {
                    await dcBaseFieldConfigManager.Insert(listAddAttr[i]);
                }
            }
            return baseSucc<string>("保存成功", 1);
        }

        /// <summary>
        /// 培训与安全教育列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("trainsafeeducationfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcTrainSafeEducationDto>>> TrainSafeEducation_Find([FromBody] DcTrainSafeEducationParam param)
        {
            Result r = new Result();
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel() { SortCode = "RegTime", SortType = "DESC" } };
            }

            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.UnitId = user.UnitId;
            }
            PageModel<DcTrainSafeEducation> pg = await dcTrainSafeEducationManager.GetPaged(param);
            return baseSucc(pg.ConvertTo<DcTrainSafeEducationDto>(mapper), pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 保存培训与安全教育信息-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("trainsafeeducationinsertupdate")]
        //<used>1</used>
        public async Task<Result<long>> TrainSafeEducation_InsertUpdate([FromBody] DcTrainSafeEducationDto model)
        {
            var o = mapper.Map<DcTrainSafeEducation>(model);

            var listAttchmentData = await bAttachmentDataManager.Find(m => m.UnitId == user.UnitId && m.CreateId == user.UserId && m.ModuleType == ModuleTypeEnum.DcTrainSafe.ObjToInt());
            //添加和删除附件。
            List<BAttachment> listAdd = new List<BAttachment>();
            List<BAttachment> listDel = new List<BAttachment>();
            if (o.Id > 0)
            {
                DcTrainSafeEducation safeEducation = await dcTrainSafeEducationManager.GetById(o.Id);
                if (safeEducation != null)
                {
                    List<BAttachment> listAttchment = new List<BAttachment>();
                    var listAttchmentResult = await bAttachmentManager.GetPaged(new BAttachmentParam { ObjectId = safeEducation.Id, UnitId = user.UnitId, IsDelete = 0, pageIndex = 1, pageSize = int.MaxValue });
                    if (listAttchmentResult != null && listAttchmentResult.data != null)
                    {
                        listAttchment = listAttchmentResult.data;
                    }
                    safeEducation.Name = o.Name;
                    safeEducation.PersonCharge = o.PersonCharge;
                    safeEducation.EffectiveDate = o.EffectiveDate;
                    safeEducation.Trainees = o.Trainees;
                    safeEducation.Address = o.Address;
                    safeEducation.RegTime = DateTime.Now;
                    safeEducation.UserId = user.UserId;
                    safeEducation.UnitId = user.UnitId;
                    if (await dcTrainSafeEducationManager.Update(safeEducation))
                    {
                        if (model.AttachmentList != null && model.AttachmentList.Count > 0)
                        {
                            foreach (var item in model.AttachmentList)
                            {
                                var itemTemp = listAttchment.Where(m => m.Id == item).FirstOrDefault();
                                //存在移除
                                if (itemTemp != null)
                                {
                                    listAttchment.Remove(itemTemp);
                                }
                                else
                                {
                                    if (listAttchmentData != null)
                                    {
                                        var entityTemp = listAttchmentData.Where(m => m.Id == item).FirstOrDefault();
                                        if (entityTemp != null)
                                        {
                                            var entity = bAttachmentManager.GetModel(entityTemp);
                                            entity.UnitId = user.UnitId;
                                            entity.UserId = user.UserId;
                                            entity.ObjectId = safeEducation.Id;
                                            listAdd.Add(entity);
                                        }
                                    }
                                }
                            }
                        }
                        //处理附件
                        if (listAttchment.Count > 0)
                        {
                            listAttchment.ForEach(m => m.IsDelete = 1);
                            await bAttachmentManager.Update(listAttchment);
                        }
                        if (listAdd.Count > 0)
                        {
                            await bAttachmentManager.Add(listAdd);
                        }
                        return baseSucc<long>(safeEducation.Id, 1, "添加成功");
                    }
                    else
                    {
                        return baseFailed<long>("修改失败");
                    }
                }
            }
            else
            {
                o.UserId = user.UserId;
                o.RegTime = DateTime.Now;
                o.UnitId = user.UnitId;
                var saveId = await dcTrainSafeEducationManager.Add(o);
                if (saveId > 0)
                {
                    //处理附件
                    if (model.AttachmentList != null && model.AttachmentList.Count > 0)
                    {
                        foreach (var item in model.AttachmentList)
                        {
                            if (listAttchmentData != null)
                            {
                                var entityTemp = listAttchmentData.Where(m => m.Id == item).FirstOrDefault();
                                if (entityTemp != null)
                                {
                                    var entity = bAttachmentManager.GetModel(entityTemp);
                                    entity.UnitId = user.UnitId;
                                    entity.UserId = user.UserId;
                                    entity.ObjectId = saveId;
                                    listAdd.Add(entity);
                                }
                            }
                        }
                        if (listAdd.Count > 0)
                        {
                            await bAttachmentManager.Add(listAdd);
                        }
                    }
                    return baseSucc<long>(saveId, 1, "保存成功");
                }
                else
                {
                    return baseFailed<long>("保存失败");
                }
            }
            return baseFailed<long>("修改失败");
        }

        /// <summary>
        /// 培训与安全教育修改内容获取-查询
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("trainsafeeducationedit")]
        //<used>1</used>
        public async Task<Result<object>> TrainSafeEducation_Edit(long Id)
        {
            DcTrainSafeEducation safeEducation = await dcTrainSafeEducationManager.GetById(Id);
            if (safeEducation != null)
            {
                //获取所有附件
                var listAttchment = await bAttachmentManager.GetPaged(new BAttachmentParam { ObjectId = Id, UnitId = safeEducation.UnitId, ModuleType = ModuleTypeEnum.DcTrainSafe.ObjToInt(), IsDelete = 0, pageIndex = 1, pageSize = int.MaxValue });

                var data = new { safeEducation.Id, safeEducation.Name, safeEducation.PersonCharge, safeEducation.Address, safeEducation.EffectiveDate, safeEducation.Trainees };
                var footer = listAttchment.data.Select(t => new { t.Id, t.ObjectId, t.Title, t.Path, t.FileCategory, t.Ext });
                return baseSucc<object>(data, 1, "查询成功", footer);
            }
            else
            {
                return baseFailed<object>("查询失败");
            }
        }

        /// <summary>
        /// 培训与安全教育-删除
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("trainsafeeducationdelete")]
        //<used>1</used>
        public async Task<Result<string>> TrainSafeEducation_Delete(long Id)
        {
            DcTrainSafeEducation safeEducation = await dcTrainSafeEducationManager.GetById(Id);
            if (safeEducation != null)
            {
                if (safeEducation.UnitId != user.UnitId)
                {
                    return baseFailed<string>("不能删除其它单位数据");
                }
                //更新所有附件
                var listAttchmentResult = await bAttachmentManager.GetPaged(new BAttachmentParam { ObjectId = Id, UnitId = user.UnitId, ModuleType = ModuleTypeEnum.DcTrainSafe.ObjToInt(), IsDelete = 0, pageIndex = 1, pageSize = int.MaxValue });
                if (listAttchmentResult.dataCount > 0)
                {
                    var listAttchment = listAttchmentResult.data;
                    listAttchment.ForEach(a => a.IsDelete = 1);
                    await bAttachmentManager.Update(listAttchment);
                }

                if (await dcTrainSafeEducationManager.DeleteById(Id))
                {
                    return baseSucc<string>("删除成功", 1);
                }
                else
                {
                    return baseFailed<string>("删除失败");
                }
            }
            return baseFailed<string>("删除失败");
        }

        /// <summary>
        /// 应急预案与演练列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("emergencyplanfind")]
        //<used>1</used>
        public async Task<Result<PageModel<DcEmergencyPlanDto>>> EmergencyPlan_Find([FromBody] DcEmergencyPlanParam param)
        {
            Result r = new Result();
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel() { SortCode = "RegTime", SortType = "DESC" } };
            }
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.UnitId = user.UnitId;
            }
            PageModel<DcEmergencyPlan> pg = await dcEmergencyPlanManager.GetPaged(param);
            return baseSucc(pg.ConvertTo<DcEmergencyPlanDto>(mapper), pg.dataCount);
        }

        /// <summary>
        /// 保存应急预案与演练信息-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("emergencyplaninsertupdate")]
        //<used>1</used>
        public async Task<Result<long>> EmergencyPlan_InsertUpdate([FromBody] DcEmergencyPlanDto model)
        {
            var o = mapper.Map<DcEmergencyPlan>(model);
            var listAttchmentData = await bAttachmentDataManager.Find(m => m.UnitId == user.UnitId && m.CreateId == user.UserId && m.ModuleType == ModuleTypeEnum.DcEmergencyDrill.ObjToInt());
            //添加和删除附件。
            List<BAttachment> listAdd = new List<BAttachment>();
            List<BAttachment> listDel = new List<BAttachment>();

            if (o.Id > 0)
            {
                DcEmergencyPlan safeEducation = await dcEmergencyPlanManager.GetById(o.Id);

                if (safeEducation != null)
                {
                    List<BAttachment> listAttchment = new List<BAttachment>();
                    var listAttchmentResult = await bAttachmentManager.GetPaged(new BAttachmentParam { ObjectId = safeEducation.Id, UnitId = user.UnitId, IsDelete = 0, pageIndex = 1, pageSize = int.MaxValue });
                    if (listAttchmentResult != null && listAttchmentResult.data != null)
                    {
                        listAttchment = listAttchmentResult.data;
                    }

                    safeEducation.Name = o.Name;
                    safeEducation.PersonCharge = o.PersonCharge;
                    safeEducation.EffectiveDate = o.EffectiveDate;
                    safeEducation.Trainees = o.Trainees;
                    safeEducation.Address = o.Address;
                    safeEducation.RegTime = DateTime.Now;
                    safeEducation.UserId = user.UserId;
                    safeEducation.UnitId = user.UnitId;
                    if (await dcEmergencyPlanManager.Update(safeEducation))
                    {
                        if (model.AttachmentList != null && model.AttachmentList.Count > 0)
                        {
                            foreach (var item in model.AttachmentList)
                            {
                                var itemTemp = listAttchment.Where(m => m.Id == item).FirstOrDefault();
                                //存在移除
                                if (itemTemp != null)
                                {
                                    listAttchment.Remove(itemTemp);
                                }
                                else
                                {
                                    if (listAttchmentData != null)
                                    {
                                        var entityTemp = listAttchmentData.Where(m => m.Id == item).FirstOrDefault();
                                        if (entityTemp != null)
                                        {
                                            var entity = bAttachmentManager.GetModel(entityTemp);
                                            entity.UnitId = user.UnitId;
                                            entity.UserId = user.UserId;
                                            entity.ObjectId = safeEducation.Id;
                                            listAdd.Add(entity);
                                        }
                                    }
                                }
                            }
                        }
                        //处理附件
                        if (listAttchment.Count > 0)
                        {
                            listAttchment.ForEach(m => m.IsDelete = 1);
                            await bAttachmentManager.Update(listAttchment);
                        }
                        if (listAdd.Count > 0)
                        {
                            await bAttachmentManager.Add(listAdd);
                        }

                        return baseSucc<long>(safeEducation.Id, 1, "修改成功");
                    }
                }
                return baseFailed<long>("修改失败");
            }
            else
            {
                o.UserId = user.UserId;
                o.RegTime = DateTime.Now;
                o.UnitId = user.UnitId;
                var id = await dcEmergencyPlanManager.Add(o);
                if (id > 0)
                {
                    //处理附件
                    if (model.AttachmentList != null && model.AttachmentList.Count > 0)
                    {
                        foreach (var item in model.AttachmentList)
                        {
                            if (listAttchmentData != null)
                            {
                                var entityTemp = listAttchmentData.Where(m => m.Id == item).FirstOrDefault();
                                if (entityTemp != null)
                                {
                                    var entity = bAttachmentManager.GetModel(entityTemp);
                                    entity.UnitId = user.UnitId;
                                    entity.UserId = user.UserId;
                                    entity.ObjectId = id;
                                    listAdd.Add(entity);
                                }
                            }
                        }
                        if (listAdd.Count > 0)
                        {
                            await bAttachmentManager.Add(listAdd);
                        }
                    }

                    return baseSucc<long>(id, 1, "保存成功");
                }
            }
            return baseFailed<long>("保存失败");
        }

        /// <summary>
        /// 应急预案与演练修改内容获取-查询
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("emergencyplanedit")]
        //<used>1</used>
        public async Task<Result<object>> EmergencyPlan_Edit(long Id)
        {
            DcEmergencyPlan safeEducation = await dcEmergencyPlanManager.GetById(Id);
            if (safeEducation != null)
            {
                //获取所有附件
                var listAttchment = await bAttachmentManager.GetPaged(new BAttachmentParam { ObjectId = Id, UnitId = safeEducation.UnitId, ModuleType = ModuleTypeEnum.DcEmergencyDrill.ObjToInt(), IsDelete = 0, pageIndex = 1, pageSize = int.MaxValue });
                var data = new { safeEducation.Id, safeEducation.Name, safeEducation.PersonCharge, safeEducation.Address, safeEducation.EffectiveDate, safeEducation.Trainees };
                var footer = listAttchment.data.Select(t => new { t.Id, t.ObjectId, t.Title, t.Path, t.FileCategory, t.Ext });
                return baseSucc<object>(data, 1, "查询成功", footer);
            }
            else
            {
                return baseFailed<object>("查询失败");
            }
        }

        /// <summary>
        /// 培训与安全教育-删除
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("emergencyplandelete")]
        //<used>1</used>
        public async Task<Result<string>> EmergencyPlan_Delete(long Id)
        {
            DcEmergencyPlan safeEducation = await dcEmergencyPlanManager.GetById(Id);

            if (safeEducation != null)
            {
                if (safeEducation.UnitId != user.UnitId)
                {
                    return baseFailed<string>("不能删除其它单位数据");
                }
                //更新所有附件
                var listAttchmentObj = await bAttachmentManager.GetPaged(new BAttachmentParam { ObjectId = Id, UnitId = user.UnitId, ModuleType = ModuleTypeEnum.DcEmergencyDrill.ObjToInt(), IsDelete = 0, pageIndex = 1, pageSize = int.MaxValue });
                if (listAttchmentObj.dataCount > 0)
                {
                    var listAttchment = listAttchmentObj.data;
                    listAttchment.ForEach(a => a.IsDelete = 1);
                    await bAttachmentManager.Update(listAttchment);
                }
                if (await dcEmergencyPlanManager.DeleteById(Id))
                {
                    return baseSucc<string>("", 1, "删除成功");
                }
            }
            return baseFailed<string>("删除失败");
        }

        /// <summary>
        /// 保障要求查看列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcguaranteeclaimlistfind")]
        //<used>1</used>
        public async Task<Result<object>> DcGuaranteeClaimList_Find([FromBody] DcTrainSafeEducationParam param)
        {
            Result r = new Result();
            param.UnitTypeId = user.UnitTypeId;
            param.unitId = user.UnitId;
            var list = await dcTrainSafeEducationManager.DcGuaranteeClaimList_Find(param);
            if (param.totalCount > 0)
            {
                //增加总计 
                var totallist = await dcTrainSafeEducationManager.DcGuaranteeClaimList_FindTotal(param);
                if (totallist != null)
                {
                    r.data.headers = totallist;
                }
            }
            if (param.isFirst)
            {
                //加载单位下拉列表
                List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                var listSchool = await unitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                if (listSchool != null)
                {
                    foreach (var item in listSchool)
                    {
                        dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                    }
                }
                r.data.other = new { SchoolList = dropdownSchool };
            }
            return baseSucc(list, param.totalCount, "查询成功", r.data.other, r.data.headers);
        }

        #endregion

        #region 废弃物处置流程
        /// <summary>
        /// 废弃物基础分类列表-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastebasefind")]
        //<used>1</used>
        public async Task<Result<DataTable>> DcWasteBase_Find()
        {
            Result r = new Result();
            DataTable dt = await dcWasteDisposalDetailManager.GetBaseWasteList();
            return baseSucc<DataTable>(dt, dt.Rows.Count, "查询成功");
        }

        /// <summary>
        /// 废弃物处置填报列表-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcWasteDisposal_Find()
        {
            var pg = await dcWasteDisposalDetailManager.GetSchoolWasteList(user.UnitId);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 废弃物处置申请-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwasetdisposalsave")]
        //<used>1</used>
        public async Task<Result<string>> DcWasetDisposal_Save([FromBody] DcWasteDisposalSaveModel model)
        {
            if (model == null || model.o == null || model.o.Count == 0)
            {
                return baseFailed<string>("执行失败");
            }
            foreach (var item in model.o)
            {
                var validatorResult = new DcWasteDisposalValidator().Validate(item);
                if (!validatorResult.IsValid)
                {
                    var msg = string.Join("<br/>", validatorResult.Errors.Select(m => m.ErrorMessage));
                    return baseFailed<string>(msg);
                }
            }

            if (model.type == 1)
            {
                await dcWasteDisposalDetailManager.DeleteTempByWasteId(user.UnitId, model.ids);
            }
            else
            {
                await dcWasteDisposalDetailManager.DeleteTempByUnitId(user.UnitId);
            }
            List<DcWasteDisposalDetail> listDisposalDetail = new List<DcWasteDisposalDetail>();
            foreach (var item in model.o)
            {
                listDisposalDetail.Add(new DcWasteDisposalDetail
                {
                    WasteDisposalId = 0,
                    SchoolId = user.UnitId,
                    BaseWasteId = item.BaseWasteId,
                    Num = item.Num,
                    Remark = item.Remark,
                    UserId = user.UserId,
                    RegDate = DateTime.Now
                });
            }
            var listId = await dcWasteDisposalDetailManager.Add(listDisposalDetail);

            var r = await dcWasteDisposalManager.Add(user.UnitId, user.UserId, model.type, model.ids);

            if (listId.Count < model.o.Count)
            {
                var msg = string.Format("共执行{0}条数据，其中成功{1}条，失败{2}条。", model.o.Count, listId.Count, (model.o.Count - listId.Count));
                return baseFailed<string>(msg);
            }
            else
            {
                string rows = "";
                if (r.data.rows != null)
                {
                    rows = r.data.rows.ToString();
                }
                return baseSucc<string>(rows, 1, "添加成功");
            }
        }

        /// <summary>
        /// 查询是否存在废弃物-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwasetdisposalgetisexists")]
        //<used>1</used>
        public async Task<Result<object>> DcWasetDisposal_GetIsExists()
        {
            Result r = new Result();
            int IsExistApplyWaset = 0;
            var param = new DcApplyConfirmDetailParam();
            param.unitId = user.UserId;
            param.WasetStatuz = 1;
            var wasetApply = await dcApplyConfirmDetailManager.Find(param);
            if (wasetApply != null && wasetApply.Count > 0)
            {
                IsExistApplyWaset = wasetApply.Count > 0 ? 1 : 0;
            }
            return baseSucc<object>(new { IsExistApplyWaset = IsExistApplyWaset }, 1, "查询成功");
        }

        /// <summary>
        /// 废弃物处置审核列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalauditfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcWasteDisposalAudit>>> DcWasteDisposalAudit_Find([FromBody] VDcWasteDisposalAuditParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            PageModel<VDcWasteDisposalAudit> pg = await dcWasteDisposalManager.GetAuditPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 废弃物明细列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposaldetailfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcWasteDisposalDetail>>> DcWasteDisposalDetail_Find([FromBody] VDcWasteDisposalDetailParam param)
        {
            Result r = new Result();
            param.unitId = user.UnitId;
            PageModel<VDcWasteDisposalDetail> pg = await dcWasteDisposalManager.GetDetailPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }

        /// <summary>
        /// 处置-审核
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statuz"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalaudit")]
        //<used>1</used>
        public async Task<Result<object>> DcWasteDisposal_Audit(long id, int statuz, string remark)
        {
            var r = await dcWasteDisposalManager.Audit(id, statuz, remark, user.UnitId, user.UserId);
            if (r.flag > 0)
            {
                return baseSucc(r.data.rows, 1, r.msg);
            }
            else
            {
                return baseFailed<object>(r.msg);
            }
        }

        /// <summary>
        /// 处置信息-填报
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalreport")]
        //<used>1</used>
        public async Task<Result<string>> DcWasteDisposal_Report([FromBody] DcWasteDisposalDto model)
        {
            var entity = await dcWasteDisposalManager.GetById(model.Id);
            if (entity != null && entity.SchoolId == user.UnitId && (entity.Statuz == 1 || entity.Statuz == 3))
            {
                //long id, DateTime disposalDate, string remark, string processFile, string processImg, string companyName
                entity.DisposalDate = model.DisposalDate;
                entity.DisposalRemark = model.DisposalRemark;
                entity.ProcessFile = model.ProcessFile;
                entity.ProcessImg = model.ProcessImg;
                entity.Statuz = 3;
                entity.CompanyName = model.CompanyName;
                if (await dcWasteDisposalManager.Update(entity))
                {
                    if (entity.SourceType == 2)
                    {
                        //来源为危化品时，修改报废表处置完成日期
                        var param = new DcScrapParam();
                        param.WasteDisposalId = model.Id;
                        var scrapList = await dcScrapManager.Find(param);
                        if (scrapList != null && scrapList.Count > 0)
                        {
                            scrapList.ForEach(t => { t.DisposalDate = model.DisposalDate; t.WasetStatuz = 3; });
                            await dcScrapManager.Update(scrapList);
                        }
                    }
                    return baseSucc<string>("保存成功", 1);
                }
                else
                {
                    return baseFailed<string>("保存失败");
                }
            }
            else
            {
                return baseFailed<string>("找不到您要填报的数据，或当前状态不可操作。");
            }
        }

        /// <summary>
        /// 根据Id查询废弃物处置信息-查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalgetbyid")]
        //<used>1</used>
        public async Task<Result<DcWasteDisposalDto>> DcWasteDisposal_GetById(long id)
        {
            Result r = new Result();
            var model = await dcWasteDisposalManager.GetById(id);
            if (model != null && model.SchoolId == user.UnitId)
            {
                return baseSucc(mapper.Map<DcWasteDisposalDto>(model), 1, "查询成功");
            }
            else
            {
                return baseFailed<DcWasteDisposalDto>("执行失败");
            }
        }

        /// <summary>
        /// 获取废弃物分类-查询
        /// </summary>
        /// <param name="pid"></param>
        /// <param name="id"></param>
        /// <param name="depth"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwasteclassget")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcWasteClass_Get(long pid, long id, int depth)
        {
            var param = new DcBaseWasteParam();
            param.Depth = depth;
            if (pid > 0)
            {
                param.Pid = pid;
            }
            if (id > 0)
            {
                param.Id = id;
            }
            var list = await dcBaseWasteManager.Find(param);
            PageModel<object> page = new PageModel<object>();
            if (list != null && list.Count > 0)
            {
                page.data = list.Select(m => new
                {
                    Id = m.Id,
                    Name = m.Name,
                    UnitsMeasurement = m.UnitsMeasurement
                }).ToList<object>();
                page.dataCount = page.data.Count;
            }
            return baseSucc(page, page.dataCount, "查询成功");
        }

        /// <summary>
        /// 实验后废弃物明细列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwasterecordfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcWasteRecord>>> DcWasteRecord_Find([FromBody] VDcWasteRecordParam param)
        {
            PageModel<VDcWasteRecord> pg = await dcWasteRecordManager.GetListPaged(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 报废危化品处置申请-保存
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcscrapdisposal")]
        //<used>1</used>
        public async Task<Result<object>> DcScrap_Disposal(string ids)
        {
            var r = await dcScrapManager.Disposal(user.UnitId, user.UserId, ids); ;
            if (r.flag > 0)
            {
                return baseSucc(r.data.rows, 1, r.msg);
            }
            else
            {
                return baseFailed<object>(r.msg);
            }
        }

        #endregion

        #region 查询统计
        /// <summary>
        /// 获取领用查询年度列表-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getdcapplystatisticsyear")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> GetDcApplyStatisticsYear()
        {
            VDcApplyStatisticsParam param = new VDcApplyStatisticsParam();
            if (user.UnitTypeId == 3)
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == 2)
            {
                param.CountyId = user.UnitId;
            }
            else if (user.UnitTypeId == 1)
            {
                param.CityId = user.UnitId;
            }
            else
            {
                param.SchoolId = -1;
            }
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<VDcApplyStatistics> pg = await dcApplyManager.GetStatisticsPaged(param);
            PageModel<object> page = new PageModel<object>();
            page.dataCount = pg.dataCount;
            if (pg.dataCount > 0)
            {
                var list = pg.data.GroupBy(f => f.Yearz).Select(group => new
                {
                    Yearz = group.Key.ToString(),
                    YearName = group.Key.ToString()
                });
                page.data = list.ToList<object>();
                return baseSucc(page, page.dataCount, "查询成功", page.Other);
            }
            return baseSucc(new PageModel<object>(), 0, "查询成功");
        }

        /// <summary>
        /// 获取领用查询年度列表-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getdcapplystatisticsuser")]
        //<used>1</used>
        public async Task<Result<PageModel<PUserDto>>> GetDcApplyStatisticsUser()
        {
            Result r = new Result();
            List<PUserDto> listUser = await userManager.StatisticsUser(user.UnitId);
            PageModel<PUserDto> page = new PageModel<PUserDto>();
            if (listUser.Count > 0)
            {
                page.dataCount = listUser.Count;
                page.data = listUser;
                return baseSucc(page, page.dataCount, "查询成功");
            }
            else
            {
                return baseSucc(new PageModel<PUserDto>(), 0, "未查询到领用记录");
            }
        }

        /// <summary>
        /// 物品领用按领用人统计-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplyusernumstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcApplyUserNumStatistics_Find([FromBody] VDcApplyStatisticsParam param)
        {
            List<PUserDto> listUser = await userManager.StatisticsUser(user.UnitId);
            param.SchoolId = user.UnitId;
            var list = await dcApplyManager.ApplyUserNumStatistics(param, listUser);
            PageModel<object> page = new PageModel<object>();
            page.dataCount = list.Count;
            page.data = list;
            return baseSucc(page, page.dataCount, "查询成功");
        }

        /// <summary>
        /// 库存数量统计-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccountystocknumstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcCountyStockNumStatistics>>> DcCountyStockNumStatistics_Find([FromBody] VDcCountyStockNumStatisticsParam param)
        {
            param.unitId = user.UnitId;
            param.UnitTypeId = user.UnitTypeId;
            var pg = await dcSchoolMaterialManager.GetStockNumStatistics(param);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }
        /// <summary>
        /// 库存数量统计-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccountystocknumstatisticsexport")]
        //<used>1</used>
        public async Task<IActionResult> DcCountyStockNumStatistics_Exort([FromBody] VDcCountyStockNumStatisticsParam param)
        {
            param.unitId = user.UnitId;
            param.UnitTypeId = user.UnitTypeId;
            param.pageSize = int.MaxValue;
            var pg = await dcSchoolMaterialManager.GetStockNumStatistics(param);
            var excelBytes = await new ExcelHelper<VDcCountyStockNumStatistics>().ExportExecl(pg.data.ToList(), "库存数量统计", new string[] { "SchoolName", "TwoCatalogName", "Name", "Model", "UnitsMeasurement", "StockNum", "Limited" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "库存数量统计.xlsx");
            //if (list != null && list.Count > 0)
            //{
            //    IWorkbook iwork = new HSSFWorkbook();
            //    #region workbook_title

            //    IFont font = iwork.CreateFont();
            //    font.FontHeightInPoints = 10;
            //    font.FontName = "宋体";

            //    IFont font2 = iwork.CreateFont();
            //    font2.FontHeightInPoints = 12;
            //    font2.FontName = "宋体";

            //    IFont font3 = iwork.CreateFont();
            //    font3.FontHeightInPoints = 16;
            //    font3.IsBold = true;
            //    font3.FontName = "宋体";

            //    IFont font4 = iwork.CreateFont();
            //    font4.FontHeightInPoints = 10;
            //    font4.IsBold = true;
            //    font4.FontName = "宋体";

            //    IFont font5 = iwork.CreateFont();
            //    font5.FontHeightInPoints = 12;
            //    font5.IsBold = true;
            //    font5.FontName = "宋体";

            //    ICellStyle cellTitle = iwork.CreateCellStyle();
            //    cellTitle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitle.Alignment = HorizontalAlignment.Center;
            //    cellTitle.SetFont(font4);
            //    cellTitle.VerticalAlignment = VerticalAlignment.Center;
            //    cellTitle.WrapText = true;//自动换行

            //    ICellStyle cellTitleRight = iwork.CreateCellStyle();
            //    cellTitleRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitleRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitleRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitleRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellTitleRight.Alignment = HorizontalAlignment.Right;
            //    cellTitleRight.SetFont(font4);
            //    cellTitleRight.VerticalAlignment = VerticalAlignment.Center;

            //    ICellStyle cellLeft = iwork.CreateCellStyle();
            //    cellLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            //    cellLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            //    cellLeft.SetFont(font);
            //    cellLeft.WrapText = true;//自动换行

            //    ICellStyle cellRight = iwork.CreateCellStyle();
            //    cellRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            //    cellRight.SetFont(font);
            //    cellRight.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            //    ICellStyle cellCenter = iwork.CreateCellStyle();
            //    cellCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            //    cellCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            //    cellCenter.SetFont(font);
            //    cellCenter.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            //    cellCenter.WrapText = true;//自动换行

            //    ICellStyle cellNull = iwork.CreateCellStyle();
            //    cellNull.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            //    cellNull.SetFont(font3);
            //    cellNull.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            //    ICellStyle cellNull2 = iwork.CreateCellStyle();
            //    cellNull2.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull2.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull2.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull2.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            //    cellNull2.SetFont(font2);
            //    cellNull2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            //    ICellStyle cellNull3 = iwork.CreateCellStyle();
            //    cellNull3.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull3.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull3.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull3.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            //    cellNull3.SetFont(font5);
            //    cellNull3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            //    ICellStyle cellNull4 = iwork.CreateCellStyle();
            //    cellNull4.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull4.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull4.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull4.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            //    cellNull4.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            //    cellNull4.SetFont(font);
            //    cellNull4.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            //    #endregion

            //    #region sheet_title


            //    //建立一个名为Sheet1的工作表
            //    NPOI.SS.UserModel.ISheet isheet = iwork.CreateSheet("sheet1");
            //    isheet.PrintSetup.PaperSize = 9;
            //    for (int i = 0; i <= 7; i++)
            //    {
            //        if (i == 0)
            //        {
            //            isheet.SetColumnWidth(i, 5 * 256);
            //        }
            //        else if (i == 1 || i == 4)
            //        {
            //            isheet.SetColumnWidth(i, 20 * 256);
            //        }
            //        else if (i == 2 || i == 3)
            //        {
            //            isheet.SetColumnWidth(i, 15 * 256);
            //        }
            //        else if (i == 5)
            //        {
            //            isheet.SetColumnWidth(i, 10 * 256);
            //        }
            //        else
            //        {
            //            isheet.SetColumnWidth(i, 13 * 256);
            //        }
            //    }
            //    //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行
            //    CellRangeAddress region = new CellRangeAddress(0, 0, 0, 7);
            //    isheet.AddMergedRegion(region);

            //    ICell cell = null;
            //    IRow row = isheet.CreateRow(0);
            //    row.HeightInPoints = 35;
            //    cell = row.CreateCell(0);
            //    cell.CellStyle = cellNull;
            //    cell.SetCellValue("库存数量统计");

            //    row = isheet.CreateRow(1);
            //    row.HeightInPoints = 20;
            //    cell = row.CreateCell(0);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("序号");
            //    cell = row.CreateCell(1);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("单位名称");
            //    cell = row.CreateCell(2);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("危化品分类");
            //    cell = row.CreateCell(3);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("危化品名称");
            //    cell = row.CreateCell(4);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("规格、属性");
            //    cell = row.CreateCell(5);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("单位");
            //    cell = row.CreateCell(6);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("库存数量");
            //    cell = row.CreateCell(7);
            //    cell.CellStyle = cellTitle;
            //    cell.SetCellValue("超量预警值");

            //    #endregion

            //    #region 打印重复表头
            //    //横向打印
            //    isheet.PrintSetup.Landscape = true;
            //    //缩放比：100% 不缩放
            //    isheet.PrintSetup.Scale = 100;
            //    //不缩放到一页
            //    isheet.FitToPage = false;
            //    //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
            //    isheet.RepeatingRows = new CellRangeAddress(1, 1, 0, int.MaxValue);
            //    #endregion

            //    int y = 2;
            //    for (int i = 0; i < list.Count; i++)
            //    {
            //        row = isheet.CreateRow(y);
            //        row.HeightInPoints = 20;
            //        cell = row.CreateCell(0);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue(i + 1);

            //        cell = row.CreateCell(1);
            //        cell.CellStyle = cellLeft;
            //        cell.SetCellValue(list[i].SchoolName);

            //        cell = row.CreateCell(2);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue(list[i].TwoCatalogName);

            //        cell = row.CreateCell(3);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue(list[i].Name);

            //        cell = row.CreateCell(4);
            //        cell.CellStyle = cellLeft;
            //        cell.SetCellValue(list[i].Model);

            //        cell = row.CreateCell(5);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue(list[i].UnitsMeasurement);

            //        cell = row.CreateCell(6);
            //        cell.CellStyle = cellRight;
            //        cell.SetCellValue(list[i].StockNum.ToString("F2"));

            //        cell = row.CreateCell(7);
            //        cell.CellStyle = cellRight;
            //        cell.SetCellValue(list[i].Limited.ToString("F2"));
            //        y++;
            //    }
            //    if (list.Count > 0)
            //    {
            //        row = isheet.CreateRow(y);
            //        row.HeightInPoints = 20;
            //        cell = row.CreateCell(0);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue("");
            //        cell = row.CreateCell(1);
            //        cell.CellStyle = cellTitle;
            //        cell.SetCellValue("总计：");
            //        cell = row.CreateCell(2);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue("");
            //        cell = row.CreateCell(3);
            //        cell.CellStyle = cellLeft;
            //        cell.SetCellValue("");
            //        cell = row.CreateCell(4);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue("");
            //        cell = row.CreateCell(5);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue("");
            //        cell = row.CreateCell(6);
            //        cell.CellStyle = cellTitleRight;
            //        cell.SetCellValue(list.Sum(f => f.StockNum).ToString("F2"));
            //        cell = row.CreateCell(7);
            //        cell.CellStyle = cellCenter;
            //        cell.SetCellValue("");
            //    }
            //    string exlName = "库存数量统计";
            //    var fileInfo = FileHelper.GetSaveFileInfo(env.ContentRootPath, exlName);
            //    using (MemoryStream ms = new MemoryStream())
            //    {
            //        //将工作簿的内容放到内存流中
            //        iwork.Write(ms);
            //        iwork.Close();
            //        ms.Flush();
            //        ms.Position = 0;
            //        using (FileStream fs = new FileStream(fileInfo.filePath, FileMode.Create, FileAccess.Write))
            //        {
            //            byte[] data = ms.ToArray();
            //            fs.Write(data, 0, data.Length);
            //            fs.Flush();
            //        }
            //    }
            //    return baseSucc<object>(new { fileInfo.url, fileInfo.fileName }, 1, "导出成功");
            //}
            //else
            //{
            //    return baseFailed<object>("没有需要导出的数据。");
            //}
        }
        /// <summary>
        /// 获取采购查询年度列表-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getdcpurchasestatisticsyear")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> GetDcPurchaseStatisticsYear()
        {
            if (!(user.UnitTypeId == UnitTypes.School.ObjToInt() || user.UnitTypeId == UnitTypes.Couty.ObjToInt() || user.UnitTypeId == UnitTypes.City.ObjToInt()))
            {
                return baseFailed<PageModel<object>>("无权访问");
            }
            var list = await dcSchoolMaterialManager.GetYearz(user.UnitId, user.UnitTypeId);
            PageModel<object> page = new PageModel<object>();
            if (list != null && list.Count > 0)
            {
                page.data = list.Select(m => new { Yearz = m, YearName = string.Format("year{0}", m) }).ToList<object>();
                page.dataCount = page.data.Count;
            }
            return baseSucc(page, page.dataCount, "查询成功", "", user.UnitTypeId);
        }

        /// <summary>
        /// 废弃物存量统计-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcWasteDisposalStatistics_Find([FromBody] DcWasteDisposalDetailParam param)
        {
            param.unitId = user.UnitId;
            param.UnitTypeId = user.UnitTypeId;
            var pg = await dcWasteDisposalDetailManager.DcWasteStatisticsList_GetByCounty(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }

        /// <summary>
        /// 采购数量统计-查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcpurchasenumstatisticsfind")]
        //<used>1</used>
        public async Task<Result<object>> DcPurchaseNumStatistics_Find([FromBody] DcSchoolMaterialStatisticsModel model)
        {
            model.UnitTypeId = user.UnitTypeId;
            model.UnitId = user.UnitId;
            if (string.IsNullOrEmpty(model.Years))
            {
                if (!(user.UnitTypeId == UnitTypes.School.ObjToInt() || user.UnitTypeId == UnitTypes.Couty.ObjToInt() || user.UnitTypeId == UnitTypes.City.ObjToInt()))
                {
                    return baseFailed<object>("无权访问");
                }
            }
            var r = await dcPurchaseListManager.DcPurchaseNumStatistics(model);
            if (r.flag > 0)
            {
                return baseSucc(r.data.rows, r.data.total, r.msg);
            }
            else
            {
                return baseFailed<object>(r.msg);
            }
        }

        /// <summary>
        /// 领用数量统计-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcapplynumstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcApplyNumStatistics_Find([FromBody] VDcApplyStatisticsParam param)
        {
            PageModel<object> page = new PageModel<object>();
            VDcApplyStatisticsParam pmYear = new VDcApplyStatisticsParam();
            if (param.UserUnitType == 3)
            {
                param.SchoolId = user.UnitId;
                pmYear.SchoolId = user.UnitId;
            }
            else if (param.UserUnitType == 2)
            {
                param.CountyId = user.UnitId;
                pmYear.CountyId = user.UnitId;
            }
            else if (param.UserUnitType == 1)
            {
                param.CityId = user.UnitId;
                pmYear.CityId = user.UnitId;
            }
            else
            {
                param.SchoolId = -1;
                pmYear.SchoolId = -1;
            }
            if (string.IsNullOrEmpty(param.years))
            {
                pmYear.pageIndex = 1;
                pmYear.pageSize = int.MaxValue;
                PageModel<VDcApplyStatistics> pg = await dcApplyManager.GetStatisticsPaged(pmYear);
                page.dataCount = pg.dataCount;
                if (pg.dataCount > 0)
                {
                    var listYear = pg.data.GroupBy(f => f.Yearz).Select(group => new
                    {
                        Yearz = group.Key.ToString()
                    });
                    param.years = string.Join(",", listYear.Select(f => f.Yearz));
                }
                else
                {
                    param.years = DateTime.Now.Year.ToString();
                }
            }

            if (param.UserUnitType == 3 || param.UserUnitType == 2 || param.CountyId > 0)
            {
                var list = await dcApplyManager.ApplyNumStatistics(param);
                page.data = list;
                page.dataCount = list.Count;
            }
            else
            {
                var list = await dcApplyManager.CityApplyNumStatistics(param);
                page.data = list;
                page.dataCount = list.Count;
            }
            return baseSucc(page, page.dataCount, "查询成功");
        }

        /// <summary>
        /// 处置数量统计-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalnumstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcWasteDisposalNumStatistics_Find([FromBody] VDcWasteDisposalDetailParam param)
        {
            if (string.IsNullOrEmpty(param.Years))
            {
                int statuz = 3;
                var listYear = await dcWasteDisposalManager.GetVDcWasteDisposalDetail_Years(user.UnitId, user.UnitTypeId, statuz);
                if (listYear != null && listYear.Count > 0)
                {
                    param.Years = string.Join(",", listYear);
                }
            }
            if (string.IsNullOrEmpty(param.Years))
            {
                return baseFailed<PageModel<object>>("未查询到数据");
            }
            param.unitId = user.UnitId;
            param.UnitTypeId = user.UnitTypeId;
            param.Statuz = 3;
            var list = await dcWasteDisposalManager.DcWasteDisposalNumStatistics(param);
            PageModel<object> page = new PageModel<object>();
            if (list != null && list.Count > 0)
            {
                page.data = list;
                page.dataCount = list.Count;
            }
            return baseSucc(page, page.dataCount, "查询成功");
        }
        /// <summary>
        /// 处置数量统计-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcwastedisposalnumstatisticsexport")]
        public async Task<IActionResult> DcWasteDisposalNumStatistics_Export([FromBody] VDcWasteDisposalDetailParam param)
        {
            #region workbook

            string exlName = "处置数量统计表";

            IWorkbook iwork = new HSSFWorkbook();

            IFont font = iwork.CreateFont();
            //font.IsBold = true;
            font.FontHeightInPoints = 10;

            IFont font1 = iwork.CreateFont();
            font1.IsBold = true;
            font1.FontHeightInPoints = 16;

            IFont font2 = iwork.CreateFont();
            font2.IsBold = true;
            font2.FontHeightInPoints = 10;

            IFont fontRed = iwork.CreateFont();
            fontRed.FontHeightInPoints = 10;
            fontRed.Color = NPOI.HSSF.Util.HSSFColor.Red.Index;

            ICellStyle cellstyle = iwork.CreateCellStyle();
            cellstyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            //cellstyle.WrapText = true;
            cellstyle.SetFont(font1);
            cellstyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellRed = iwork.CreateCellStyle();
            cellRed.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellRed.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            //cellRed.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Red.Index;
            //cellRed.FillPattern = FillPattern.SolidForeground;
            cellRed.WrapText = true;
            cellRed.SetFont(fontRed);
            cellRed.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellLeft = iwork.CreateCellStyle();
            cellLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellLeft.WrapText = true;
            cellLeft.SetFont(font);
            cellLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellRight = iwork.CreateCellStyle();
            cellRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellRight.SetFont(font);
            cellRight.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellCenter = iwork.CreateCellStyle();
            cellCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellCenter.SetFont(font);
            cellCenter.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNull = iwork.CreateCellStyle();
            cellNull.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            //cellstyle.WrapText = true;


            cellNull.SetFont(font1);
            cellNull.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNullLeft = iwork.CreateCellStyle();
            cellNullLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNullLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellNullLeft.SetFont(font2);
            cellNullLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;



            ICellStyle cellTitle = iwork.CreateCellStyle();
            cellTitle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellTitle.SetFont(font2);
            cellTitle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            #endregion

            //建立一个名为Sheet1的工作表
            NPOI.SS.UserModel.ISheet isheet = iwork.CreateSheet("处置数量统计");
            isheet.PrintSetup.PaperSize = 9;
            ICell cell = null;
            //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行

            //获取当前查询的年度如果为空则查询所有的。
            var years = param.Years;
            List<int> listYear = new List<int>();
            if (years != null && years != "年度" && years.Length > 0)
            {
                listYear = TextHelper.SplitToArray<int>(years, ',').ToList();
            }
            else
            {
                if (string.IsNullOrEmpty(param.Years))
                {
                    listYear = await dcWasteDisposalManager.GetVDcWasteDisposalDetail_Years(user.UnitId, user.UnitTypeId, 3);
                }
            }
            if (listYear.Count > 0)
            {
                NPOI.SS.Util.CellRangeAddress region = new NPOI.SS.Util.CellRangeAddress(0, 0, 0, 5 + listYear.Count);
                isheet.AddMergedRegion(region);

                NPOI.SS.UserModel.IRow rowTitle = isheet.CreateRow(0);
                rowTitle.HeightInPoints = 23;

                cell = rowTitle.CreateCell(0);
                cell.CellStyle = cellNull;
                cell.SetCellValue("处置数量统计");

                for (int i = 0; i <= listYear.Count + 5; i++)
                {
                    if (i == 0)
                    {
                        isheet.SetColumnWidth(i, 5 * 256);
                    }
                    else if (i == 3 || i == 1)
                    {
                        isheet.SetColumnWidth(i, 20 * 256);
                    }
                    else if (i == 4)
                    {
                        isheet.SetColumnWidth(i, 6 * 256);
                    }
                    else
                    {
                        isheet.SetColumnWidth(i, 12 * 256);
                    }
                }
                NPOI.SS.UserModel.IRow row1 = isheet.CreateRow(1);
                row1.HeightInPoints = 23;
                int cellNo = 0;
                cell = row1.CreateCell(cellNo);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("序号");
                if (user.UnitTypeId == UnitTypes.City.ObjToInt() && param.CountyId == 0)
                {
                    cellNo++;
                    cell = row1.CreateCell(cellNo);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue("区县名称");
                }
                else
                {
                    cellNo++;
                    cell = row1.CreateCell(cellNo);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue("单位名称");
                }
                cellNo++;
                cell = row1.CreateCell(cellNo);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("一级分类");
                cellNo++;
                cell = row1.CreateCell(cellNo);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("二级分类");
                cellNo++;
                cell = row1.CreateCell(cellNo);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("单位");

                #region 打印重复表头
                //横向打印
                isheet.PrintSetup.Landscape = true;
                //缩放比：100% 不缩放
                isheet.PrintSetup.Scale = 100;
                //不缩放到一页
                isheet.FitToPage = false;
                //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
                isheet.RepeatingRows = new CellRangeAddress(1, 1, 0, int.MaxValue);
                #endregion


                List<string> fileds = new List<string>();
                List<string> filedTotal = new List<string>();
                List<string> privotForValue = new List<string>();//[2018],[2019]

                for (int i = 0; i < listYear.Count; i++)
                {
                    cellNo++;
                    cell = row1.CreateCell(cellNo);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue(listYear[i]);
                }
                cellNo++;
                cell = row1.CreateCell(cellNo);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("小计");

                param.unitId = user.UnitId;
                param.UnitTypeId = user.UnitTypeId;
                param.Statuz = 3;
                param.Years = string.Join(",", listYear);
                var list = await dcWasteDisposalManager.DcWasteDisposalNumStatistics(param);
                if (list != null)
                {
                    int y = 2;
                    int index = 1;
                    foreach (Dictionary<string, string> item in list)
                    {
                        IRow rows = isheet.CreateRow(y);
                        rows.HeightInPoints = 20;
                        cellNo = 0;

                        cell = rows.CreateCell(cellNo);
                        cell.CellStyle = cellCenter;
                        cell.SetCellValue(index);
                        if (user.UnitTypeId == UnitTypes.City.ObjToInt() && param.CountyId == 0)
                        {
                            cellNo++;
                            cell = rows.CreateCell(cellNo);
                            cell.CellStyle = cellCenter;
                            cell.SetCellValue(item["AreaName"].ToString());
                        }
                        else
                        {
                            cellNo++;
                            cell = rows.CreateCell(cellNo);
                            cell.CellStyle = cellCenter;
                            cell.SetCellValue(item["SchoolName"].ToString());
                        }
                        cellNo++;
                        cell = rows.CreateCell(cellNo);
                        cell.CellStyle = cellLeft;
                        cell.SetCellValue(item["OneClassName"].ToString());
                        cellNo++;
                        cell = rows.CreateCell(cellNo);
                        cell.CellStyle = cellLeft;
                        cell.SetCellValue(item["TwoClassName"].ToString());
                        cellNo++;
                        cell = rows.CreateCell(cellNo);
                        cell.CellStyle = cellCenter;
                        cell.SetCellValue(item["UnitsMeasurement"].ToString());

                        for (int i = 0; i < listYear.Count; i++)
                        {
                            cellNo++;
                            cell = rows.CreateCell(cellNo);
                            cell.CellStyle = cellRight;
                            var yearstr = listYear[i].ToString();
                            cell.SetCellValue(GetNumByObject(item[yearstr]));
                        }
                        cellNo++;
                        cell = rows.CreateCell(cellNo);
                        cell.CellStyle = cellRight;
                        cell.SetCellValue(GetNumByObject(item["SumYear"]));
                        y++;
                        index++;
                    }
                    var fileInfo = FileHelper.GetSaveFileInfo(env.ContentRootPath, exlName);
                    using (MemoryStream ms = new MemoryStream())
                    {
                        //将工作簿的内容放到内存流中
                        iwork.Write(ms);
                        iwork.Close();
                        ms.Flush();
                        ms.Position = 0;
                        using (FileStream fs = new FileStream(fileInfo.filePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                }
            }

            using var stream = new MemoryStream();
            iwork.Write(stream, true);
            var excelBytes = stream.ToArray();
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "处置数量统计.xlsx");
        }

        private string GetNumByObject(object item)
        {
            string val = "0.00";
            if (item != null)
            {
                string temp = item.ToString();
                decimal num = 0;
                if (decimal.TryParse(temp, out num))
                {
                    val = num.ToString("G0");
                }
            }
            return val;
        }

        /// <summary>
        /// 获取处置数量统计年度-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("getdcwastedisposalstatisticsyear")]
        //<used>0</used>
        public async Task<Result<PageModel<object>>> GetDcWasteDisposalStatisticsYear()
        {
            if (!(user.UnitTypeId == UnitTypes.School.ObjToInt() || user.UnitTypeId == UnitTypes.Couty.ObjToInt() || user.UnitTypeId == UnitTypes.City.ObjToInt() || user.UnitTypeId == UnitTypes.System.ObjToInt()))
            {
                return baseFailed<PageModel<object>>("无权访问");
            }
            int statuz = 3;
            var list = await dcWasteDisposalManager.GetVDcWasteDisposalDetail_Years(user.UnitId, user.UnitTypeId, statuz);
            PageModel<object> pg = new PageModel<object>();
            if (list != null && list.Count > 0)
            {
                pg.data = list.Select(m => new { Yearz = m, YearName = m }).ToList<object>();
                pg.dataCount = pg.data.Count;
            }
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 按属性统计数量-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcclassifystocknumstatisticsfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VDcClassifyStockNumStatistics>>> DcClassifyStockNumStatistics_Find([FromBody] VDcClassifyStockNumStatisticsParam param)
        {
            var seachPageSize = param.pageSize;
            var searchPageIndex = param.pageIndex;

            param.pageSize = int.MaxValue;
            param.pageIndex = 1;
            if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                param.CityId = user.UnitId;
            }
            else
            {
                return baseSucc(new PageModel<VDcClassifyStockNumStatistics>(), 0, "查询成功");
            }

            PageModel<VDcClassifyStockNumStatistics> pg = await dcSchoolMaterialManager.GetClassifyStockNumPaged(param);
            PageModel<VDcClassifyStockNumStatistics> page = new PageModel<VDcClassifyStockNumStatistics>();
            if (pg.data != null && pg.data.Count > 0)
            {
                if (searchPageIndex <= 0)
                {
                    searchPageIndex = 1;
                }
                page.data = pg.data.Take(seachPageSize).Skip((searchPageIndex - 1) * seachPageSize).ToList();

                decimal stockNum1 = 0, stockNum2 = 0,
                        burnNum1 = 0, burnNum2 = 0,
                        blastNum1 = 0, blastNum2 = 0,
                        detonateNum1 = 0, detonateNum2 = 0,
                        toxicNum1 = 0, toxicNum2 = 0,
                        hyperToxicNum1 = 0, hyperToxicNum2 = 0,
                        poisonNum1 = 0, poisonNum2 = 0,
                        corrodeNum1 = 0, corrodeNum2 = 0,
                        otherNum1 = 0, otherNum2 = 0,
                        generalNum1 = 0, generalNum2 = 0; //一般危化品
                foreach (var model in pg.data)
                {
                    if (model.StockNum > 0)
                    {
                        if (model.UnitsMeasurement != "毫升") stockNum1 += model.StockNum;
                        if (model.UnitsMeasurement == "毫升") stockNum2 += model.StockNum;

                        if (model.IsBurn == 1 && model.UnitsMeasurement != "毫升") burnNum1 += model.StockNum;
                        if (model.IsBurn == 1 && model.UnitsMeasurement == "毫升") burnNum2 += model.StockNum;

                        if (model.IsBlast == 1 && model.UnitsMeasurement != "毫升") blastNum1 += model.StockNum;
                        if (model.IsBlast == 1 && model.UnitsMeasurement == "毫升") blastNum2 += model.StockNum;

                        if (model.IsDetonate == 1 && model.UnitsMeasurement != "毫升") detonateNum1 += model.StockNum;
                        if (model.IsDetonate == 1 && model.UnitsMeasurement == "毫升") detonateNum2 += model.StockNum;

                        if (model.IsToxic == 1 && model.UnitsMeasurement != "毫升") toxicNum1 += model.StockNum;
                        if (model.IsToxic == 1 && model.UnitsMeasurement == "毫升") toxicNum2 += model.StockNum;

                        if (model.IsHyperToxic == 1 && model.UnitsMeasurement != "毫升") hyperToxicNum1 += model.StockNum;
                        if (model.IsHyperToxic == 1 && model.UnitsMeasurement == "毫升") hyperToxicNum2 += model.StockNum;

                        if (model.IsPoison == 1 && model.UnitsMeasurement != "毫升") poisonNum1 += model.StockNum;
                        if (model.IsPoison == 1 && model.UnitsMeasurement == "毫升") poisonNum2 += model.StockNum;

                        if (model.IsCorrode == 1 && model.UnitsMeasurement != "毫升") corrodeNum1 += model.StockNum;
                        if (model.IsCorrode == 1 && model.UnitsMeasurement == "毫升") corrodeNum2 += model.StockNum;

                        if (model.IsOther == 1 && model.UnitsMeasurement != "毫升") otherNum1 += model.StockNum;
                        if (model.IsOther == 1 && model.UnitsMeasurement == "毫升") otherNum2 += model.StockNum;

                        if ((model.IsOther == 1 || model.IsToxic == 1 || model.IsCorrode == 1 || model.IsBurn == 1 || model.IsBlast == 1) && model.UnitsMeasurement != "毫升") generalNum1 += model.StockNum; //一般危化品
                        if ((model.IsOther == 1 || model.IsToxic == 1 || model.IsCorrode == 1 || model.IsBurn == 1 || model.IsBlast == 1) && model.UnitsMeasurement == "毫升") generalNum2 += model.StockNum;
                    }
                }
                List<object> footer = new List<object>();
                footer.Add(new
                {
                    SchoolName = "<b>总计：</b>",
                    StockNum1 = stockNum1,
                    StockNum2 = stockNum2,
                    BurnNum1 = burnNum1,
                    BurnNum2 = burnNum2,
                    BlastNum1 = blastNum1,
                    BlastNum2 = blastNum2,
                    DetonateNum1 = detonateNum1,
                    DetonateNum2 = detonateNum2,
                    ToxicNum1 = toxicNum1,
                    ToxicNum2 = toxicNum2,
                    HyperToxicNum1 = hyperToxicNum1,
                    HyperToxicNum2 = hyperToxicNum2,
                    PoisonNum1 = poisonNum1,
                    PoisonNum2 = poisonNum2,
                    CorrodeNum1 = corrodeNum1,
                    CorrodeNum2 = corrodeNum2,
                    OtherNum1 = otherNum1,
                    OtherNum2 = otherNum2,
                    GeneralNum1 = generalNum1,
                    GeneralNum2 = generalNum2
                });
                page.Other = footer;
            }
            page.dataCount = pg.dataCount;
            return baseSucc(page, page.dataCount, "查询成功", page.Other);
        }
        /// <summary>
        /// 按属性统计数量-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcclassifystocknumstatisticsexport")]
        public async Task<IActionResult> DcClassifyStockNumStatistics_Export([FromBody] VDcClassifyStockNumStatisticsParam param)
        {
            param.pageSize = int.MaxValue;
            param.pageIndex = 1;
            if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                param.CityId = user.UnitId;
            }
            else
            {
                param.CountyId = 0;
            }

            PageModel<VDcClassifyStockNumStatistics> pg = await dcSchoolMaterialManager.GetClassifyStockNumPaged(param);
            IWorkbook iwork = new HSSFWorkbook();
            #region workbook_title

            IFont font = iwork.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "宋体";

            IFont font2 = iwork.CreateFont();
            font2.FontHeightInPoints = 12;
            font2.FontName = "宋体";

            IFont font3 = iwork.CreateFont();
            font3.FontHeightInPoints = 16;
            font3.IsBold = true;
            font3.FontName = "宋体";

            IFont font4 = iwork.CreateFont();
            font4.FontHeightInPoints = 10;
            font4.IsBold = true;
            font4.FontName = "宋体";

            IFont font5 = iwork.CreateFont();
            font5.FontHeightInPoints = 12;
            font5.IsBold = true;
            font5.FontName = "宋体";

            ICellStyle cellTitle = iwork.CreateCellStyle();
            cellTitle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitle.Alignment = HorizontalAlignment.Center;
            cellTitle.SetFont(font4);
            cellTitle.VerticalAlignment = VerticalAlignment.Center;
            cellTitle.WrapText = true;//自动换行

            ICellStyle cellTitleRight = iwork.CreateCellStyle();
            cellTitleRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitleRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitleRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitleRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellTitleRight.Alignment = HorizontalAlignment.Right;
            cellTitleRight.SetFont(font4);
            cellTitleRight.VerticalAlignment = VerticalAlignment.Center;

            ICellStyle cellLeft = iwork.CreateCellStyle();
            cellLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            cellLeft.SetFont(font);
            cellLeft.WrapText = true;//自动换行

            ICellStyle cellRight = iwork.CreateCellStyle();
            cellRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellRight.SetFont(font);
            cellRight.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellCenter = iwork.CreateCellStyle();
            cellCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellCenter.SetFont(font);
            cellCenter.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            cellCenter.WrapText = true;//自动换行

            ICellStyle cellNull = iwork.CreateCellStyle();
            cellNull.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellNull.SetFont(font3);
            cellNull.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNull2 = iwork.CreateCellStyle();
            cellNull2.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull2.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull2.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull2.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellNull2.SetFont(font2);
            cellNull2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNull3 = iwork.CreateCellStyle();
            cellNull3.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull3.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull3.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull3.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellNull3.SetFont(font5);
            cellNull3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            ICellStyle cellNull4 = iwork.CreateCellStyle();
            cellNull4.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellNull4.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellNull4.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellNull4.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellNull4.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellNull4.SetFont(font);
            cellNull4.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

            #endregion



            //建立一个名为Sheet1的工作表
            NPOI.SS.UserModel.ISheet isheet = iwork.CreateSheet("sheet1");
            isheet.PrintSetup.PaperSize = 9;

            if (param.OperateType == 1)
            {
                #region sheet_title
                for (int i = 0; i <= 26; i++)
                {
                    if (i == 0)
                    {
                        isheet.SetColumnWidth(i, 5 * 256);
                    }
                    else if (i == 1 || i == 4)
                    {
                        isheet.SetColumnWidth(i, 20 * 256);
                    }
                    else if (i == 2 || i == 3)
                    {
                        isheet.SetColumnWidth(i, 15 * 256);
                    }
                    else if (i == 5)
                    {
                        isheet.SetColumnWidth(i, 6 * 256);
                    }
                    else
                    {
                        isheet.SetColumnWidth(i, 10 * 256);
                    }
                }


                //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行
                CellRangeAddress region = new CellRangeAddress(0, 0, 0, 26);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 0, 0);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 1, 1);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 2, 2);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 3, 3);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 4, 4);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 5, 5);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 6, 7);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 8, 9);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 10, 11);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 12, 13);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 14, 15);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 16, 17);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 18, 19);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 20, 21);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 22, 23);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 24, 24);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 25, 25);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 26, 26);
                isheet.AddMergedRegion(region);
                ICell cell = null;
                IRow row = isheet.CreateRow(0);
                row.HeightInPoints = 35;
                cell = row.CreateCell(0);
                cell.CellStyle = cellNull;
                cell.SetCellValue(param.PageTitle);

                row = isheet.CreateRow(1);
                row.HeightInPoints = 20;
                cell = row.CreateCell(0);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("序号");
                cell = row.CreateCell(1);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("单位名称");
                cell = row.CreateCell(2);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("类  别");
                cell = row.CreateCell(3);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("危化品名称");
                cell = row.CreateCell(4);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("规格、属性");
                cell = row.CreateCell(5);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("单位");
                cell = row.CreateCell(6);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("数量");
                cell = row.CreateCell(7);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(8);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易燃");
                cell = row.CreateCell(9);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(10);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易爆");
                cell = row.CreateCell(11);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(12);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易制爆");
                cell = row.CreateCell(13);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(14);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("有毒");
                cell = row.CreateCell(15);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(16);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("剧毒");
                cell = row.CreateCell(17);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(18);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易制毒");
                cell = row.CreateCell(19);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(20);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("腐蚀");
                cell = row.CreateCell(21);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(22);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("其它");
                cell = row.CreateCell(23);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(24);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("存放地点");
                cell = row.CreateCell(25);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("责任人");
                cell = row.CreateCell(26);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("是否可用");

                row = isheet.CreateRow(2);
                cell = row.CreateCell(0);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(1);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(2);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(3);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(4);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(5);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(6);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(7);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(8);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(9);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(10);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(11);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(12);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(13);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(14);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(15);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(16);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(17);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(18);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(19);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(20);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(21);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(22);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(23);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(24);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(25);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(26);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");


                #endregion

                #region 打印重复表头
                //横向打印
                isheet.PrintSetup.Landscape = true;
                //缩放比：100% 不缩放
                isheet.PrintSetup.Scale = 100;
                //不缩放到一页
                isheet.FitToPage = false;
                //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
                isheet.RepeatingRows = new CellRangeAddress(1, 1, 0, int.MaxValue);
                #endregion

                decimal stockNum1 = 0, stockNum2 = 0,
                    burnNum1 = 0, burnNum2 = 0,
                    blastNum1 = 0, blastNum2 = 0,
                    detonateNum1 = 0, detonateNum2 = 0,
                    toxicNum1 = 0, toxicNum2 = 0,
                    hyperToxicNum1 = 0, hyperToxicNum2 = 0,
                    poisonNum1 = 0, poisonNum2 = 0,
                    corrodeNum1 = 0, corrodeNum2 = 0,
                    otherNum1 = 0, otherNum2 = 0;
                int y = 3;
                var list = pg.data;
                for (int i = 0; i < list.Count; i++)
                {
                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(i + 1);

                    cell = row.CreateCell(1);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].SchoolName);

                    cell = row.CreateCell(2);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].TwoCatalogName);

                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Name);

                    cell = row.CreateCell(4);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Model);

                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].UnitsMeasurement);

                    decimal stockNum = list[i].StockNum;

                    cell = row.CreateCell(6);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(7);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(8);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBurn == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(9);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBurn == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(10);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBlast == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(11);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBlast == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(12);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsDetonate == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(13);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsDetonate == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(14);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsToxic == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(15);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsToxic == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(16);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(17);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(18);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsPoison == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(19);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsPoison == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(20);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsCorrode == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(21);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsCorrode == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(22);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsOther == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(23);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsOther == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(24);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Address);

                    cell = row.CreateCell(25);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].LiablePerson);

                    cell = row.CreateCell(26);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].IsMayUse == 1 ? "是" : "否");
                    y++;

                    if (list[i].UnitsMeasurement != "毫升") stockNum1 += stockNum;
                    if (list[i].UnitsMeasurement == "毫升") stockNum2 += stockNum;

                    if (list[i].IsBurn == 1 && list[i].UnitsMeasurement != "毫升") burnNum1 += stockNum;
                    if (list[i].IsBurn == 1 && list[i].UnitsMeasurement == "毫升") burnNum2 += stockNum;

                    if (list[i].IsBlast == 1 && list[i].UnitsMeasurement != "毫升") blastNum1 += stockNum;
                    if (list[i].IsBlast == 1 && list[i].UnitsMeasurement == "毫升") blastNum2 += stockNum;

                    if (list[i].IsDetonate == 1 && list[i].UnitsMeasurement != "毫升") detonateNum1 += stockNum;
                    if (list[i].IsDetonate == 1 && list[i].UnitsMeasurement == "毫升") detonateNum2 += stockNum;

                    if (list[i].IsToxic == 1 && list[i].UnitsMeasurement != "毫升") toxicNum1 += stockNum;
                    if (list[i].IsToxic == 1 && list[i].UnitsMeasurement == "毫升") toxicNum2 += stockNum;

                    if (list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement != "毫升") hyperToxicNum1 += stockNum;
                    if (list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement == "毫升") hyperToxicNum2 += stockNum;

                    if (list[i].IsPoison == 1 && list[i].UnitsMeasurement != "毫升") poisonNum1 += stockNum;
                    if (list[i].IsPoison == 1 && list[i].UnitsMeasurement == "毫升") poisonNum2 += stockNum;

                    if (list[i].IsCorrode == 1 && list[i].UnitsMeasurement != "毫升") corrodeNum1 += stockNum;
                    if (list[i].IsCorrode == 1 && list[i].UnitsMeasurement == "毫升") corrodeNum2 += stockNum;

                    if (list[i].IsOther == 1 && list[i].UnitsMeasurement != "毫升") otherNum1 += stockNum;
                    if (list[i].IsOther == 1 && list[i].UnitsMeasurement == "毫升") otherNum2 += stockNum;
                }
                if (list.Count > 0)
                {
                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(1);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue("总计：");
                    cell = row.CreateCell(2);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue("");
                    cell = row.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(6);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum1.ToString("G0"));
                    cell = row.CreateCell(7);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum2.ToString("G0"));
                    cell = row.CreateCell(8);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(burnNum1.ToString("G0"));
                    cell = row.CreateCell(9);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(burnNum2.ToString("G0"));
                    cell = row.CreateCell(10);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum1.ToString("G0"));
                    cell = row.CreateCell(11);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum2.ToString("G0"));
                    cell = row.CreateCell(12);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum1.ToString("G0"));
                    cell = row.CreateCell(13);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum2.ToString("G0"));
                    cell = row.CreateCell(14);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(toxicNum1.ToString("G0"));
                    cell = row.CreateCell(15);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(toxicNum2.ToString("G0"));
                    cell = row.CreateCell(16);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum1.ToString("G0"));
                    cell = row.CreateCell(17);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum2.ToString("G0"));
                    cell = row.CreateCell(18);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(poisonNum1.ToString("G0"));
                    cell = row.CreateCell(19);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(poisonNum2.ToString("G0"));
                    cell = row.CreateCell(20);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(corrodeNum1.ToString("G0"));
                    cell = row.CreateCell(21);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(corrodeNum2.ToString("G0"));
                    cell = row.CreateCell(22);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(otherNum1.ToString("G0"));
                    cell = row.CreateCell(23);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(otherNum2.ToString("G0"));
                    cell = row.CreateCell(24);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(25);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(26);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");

                    //统计合并信息
                    //统计
                    var stockNum = list.GroupBy(m => m.Name).Count();
                    var blastNum = list.Where(n => n.IsBlast == 1).GroupBy(m => m.Name).Count();
                    var burnNum = list.Where(n => n.IsBurn == 1).GroupBy(m => m.Name).Count(); // await vDcClassifyStockNumStatisticsManager.GetNumByName(where + " AND IsBurn = 1 ");
                    var detonateNum = list.Where(n => n.IsDetonate == 1).GroupBy(m => m.Name).Count();  //await vDcClassifyStockNumStatisticsManager.GetNumByName(where + " AND IsDetonate = 1 ");
                    var toxicNum = list.Where(n => n.IsToxic == 1).GroupBy(m => m.Name).Count();  //await vDcClassifyStockNumStatisticsManager.GetNumByName(where + " AND IsToxic = 1 ");//是否有毒
                    var hyperToxicNum = list.Where(n => n.IsHyperToxic == 1).GroupBy(m => m.Name).Count();  //await vDcClassifyStockNumStatisticsManager.GetNumByName(where + " AND IsHyperToxic = 1 ");
                    var poisonNum = list.Where(n => n.IsPoison == 1).GroupBy(m => m.Name).Count();  //await vDcClassifyStockNumStatisticsManager.GetNumByName(where + " AND IsPoison = 1 "); //是否易制毒
                    var corrodeNum = list.Where(n => n.IsCorrode == 1).GroupBy(m => m.Name).Count(); // await vDcClassifyStockNumStatisticsManager.GetNumByName(where + " AND IsCorrode = 1 ");//是否腐蚀
                    var otherNum = list.Where(n => n.IsOther == 1).GroupBy(m => m.Name).Count();  //await vDcClassifyStockNumStatisticsManager.GetNumByName(where + " AND IsOther = 1 ");
                    y++;
                    region = new CellRangeAddress(y, y, 6, 7);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 8, 9);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 10, 11);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 12, 13);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 14, 15);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 16, 17);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 18, 19);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 20, 21);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 22, 23);
                    isheet.AddMergedRegion(region);

                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(1);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue("危化品种类：");
                    cell = row.CreateCell(2);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue("");
                    cell = row.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(6);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum.ToString());
                    cell = row.CreateCell(7);
                    cell.CellStyle = cellTitleRight;

                    cell = row.CreateCell(8);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(burnNum.ToString());
                    cell = row.CreateCell(9);
                    cell.CellStyle = cellTitleRight;

                    cell = row.CreateCell(10);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum.ToString());
                    cell = row.CreateCell(11);
                    cell.CellStyle = cellTitleRight;

                    cell = row.CreateCell(12);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum.ToString());
                    cell = row.CreateCell(13);
                    cell.CellStyle = cellTitleRight;
                    //有毒
                    cell = row.CreateCell(14);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(toxicNum.ToString());
                    cell = row.CreateCell(15);
                    cell.CellStyle = cellTitleRight;
                    //剧毒
                    cell = row.CreateCell(16);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum.ToString());
                    cell = row.CreateCell(17);
                    cell.CellStyle = cellTitleRight;
                    //易制毒
                    cell = row.CreateCell(18);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(poisonNum.ToString());
                    cell = row.CreateCell(19);
                    cell.CellStyle = cellTitleRight;
                    //腐蚀
                    cell = row.CreateCell(20);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(corrodeNum.ToString());
                    cell = row.CreateCell(21);
                    cell.CellStyle = cellTitleRight;
                    //其他
                    cell = row.CreateCell(22);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(otherNum.ToString());
                    cell = row.CreateCell(23);
                    cell.CellStyle = cellTitleRight;

                    cell = row.CreateCell(24);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(25);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(26);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                }
            }
            else
            {
                #region sheet_title

                for (int i = 0; i <= 18; i++)
                {
                    if (i == 0)
                    {
                        isheet.SetColumnWidth(i, 5 * 256);
                    }
                    else if (i == 1 || i == 4)
                    {
                        isheet.SetColumnWidth(i, 20 * 256);
                    }
                    else if (i == 2 || i == 3)
                    {
                        isheet.SetColumnWidth(i, 15 * 256);
                    }
                    else if (i == 5)
                    {
                        isheet.SetColumnWidth(i, 6 * 256);
                    }
                    else
                    {
                        isheet.SetColumnWidth(i, 10 * 256);
                    }
                }


                //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行
                CellRangeAddress region = new CellRangeAddress(0, 0, 0, 18);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 0, 0);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 1, 1);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 2, 2);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 3, 3);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 4, 4);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 5, 5);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 6, 7);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 8, 9);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 10, 11);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 12, 13);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 14, 15);
                isheet.AddMergedRegion(region);

                region = new CellRangeAddress(1, 2, 16, 16);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 17, 17);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 18, 18);
                isheet.AddMergedRegion(region);
                ICell cell = null;
                IRow row = isheet.CreateRow(0);
                row.HeightInPoints = 35;
                cell = row.CreateCell(0);
                cell.CellStyle = cellNull;
                cell.SetCellValue(param.PageTitle);

                row = isheet.CreateRow(1);
                row.HeightInPoints = 20;
                cell = row.CreateCell(0);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("序号");
                cell = row.CreateCell(1);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("单位名称");
                cell = row.CreateCell(2);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("类  别");
                cell = row.CreateCell(3);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("危化品名称");
                cell = row.CreateCell(4);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("规格、属性");
                cell = row.CreateCell(5);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("单位");
                cell = row.CreateCell(6);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("数量");
                cell = row.CreateCell(7);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(8);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易制毒");
                cell = row.CreateCell(9);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(10);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易制爆");
                cell = row.CreateCell(11);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(12);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("剧毒");
                cell = row.CreateCell(13);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(14);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("一般危化品");
                cell = row.CreateCell(15);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");

                cell = row.CreateCell(16);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("存放地点");
                cell = row.CreateCell(17);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("责任人");
                cell = row.CreateCell(18);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("是否可用");

                row = isheet.CreateRow(2);
                cell = row.CreateCell(0);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(1);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(2);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(3);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(4);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(5);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(6);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(7);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(8);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(9);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(10);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(11);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(12);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(13);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(14);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(15);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");

                cell = row.CreateCell(16);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(17);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(18);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");


                #endregion
                #region 打印重复表头
                //横向打印
                isheet.PrintSetup.Landscape = true;
                //缩放比：100% 不缩放
                isheet.PrintSetup.Scale = 100;
                //不缩放到一页
                isheet.FitToPage = false;
                //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
                isheet.RepeatingRows = new CellRangeAddress(1, 1, 0, int.MaxValue);
                #endregion

                decimal stockNum1 = 0, stockNum2 = 0,

                    blastNum1 = 0, blastNum2 = 0,
                    detonateNum1 = 0, detonateNum2 = 0,
                    hyperToxicNum1 = 0, hyperToxicNum2 = 0,
                    generalNum1 = 0, generalNum2 = 0;
                int y = 3;
                var list = pg.data;
                for (int i = 0; i < list.Count; i++)
                {
                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(i + 1);

                    cell = row.CreateCell(1);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].SchoolName);

                    cell = row.CreateCell(2);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].TwoCatalogName);

                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Name);

                    cell = row.CreateCell(4);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Model);

                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].UnitsMeasurement);

                    decimal stockNum = list[i].StockNum;

                    cell = row.CreateCell(6);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(7);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(8);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBlast == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(9);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBlast == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(10);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsDetonate == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(11);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsDetonate == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(12);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(13);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(14);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue((list[i].IsOther == 1 || list[i].IsToxic == 1 || list[i].IsCorrode == 1 || list[i].IsBurn == 1 || list[i].IsBlast == 1) && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(15);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue((list[i].IsOther == 1 || list[i].IsToxic == 1 || list[i].IsCorrode == 1 || list[i].IsBurn == 1 || list[i].IsBlast == 1) && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");


                    cell = row.CreateCell(16);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].Address);

                    cell = row.CreateCell(17);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].LiablePerson);

                    cell = row.CreateCell(18);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].IsMayUse == 1 ? "是" : "否");
                    y++;

                    if (list[i].UnitsMeasurement != "毫升") stockNum1 += stockNum;
                    if (list[i].UnitsMeasurement == "毫升") stockNum2 += stockNum;

                    if (list[i].IsBlast == 1 && list[i].UnitsMeasurement != "毫升") blastNum1 += stockNum;
                    if (list[i].IsBlast == 1 && list[i].UnitsMeasurement == "毫升") blastNum2 += stockNum;

                    if (list[i].IsDetonate == 1 && list[i].UnitsMeasurement != "毫升") detonateNum1 += stockNum;
                    if (list[i].IsDetonate == 1 && list[i].UnitsMeasurement == "毫升") detonateNum2 += stockNum;

                    if (list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement != "毫升") hyperToxicNum1 += stockNum;
                    if (list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement == "毫升") hyperToxicNum2 += stockNum;

                    if ((list[i].IsOther == 1 || list[i].IsToxic == 1 || list[i].IsCorrode == 1 || list[i].IsBurn == 1 || list[i].IsBlast == 1) && list[i].UnitsMeasurement != "毫升") generalNum1 += stockNum;
                    if ((list[i].IsOther == 1 || list[i].IsToxic == 1 || list[i].IsCorrode == 1 || list[i].IsBurn == 1 || list[i].IsBlast == 1) && list[i].UnitsMeasurement == "毫升") generalNum2 += stockNum;
                }
                if (list.Count > 0)
                {
                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(1);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue("总计：");
                    cell = row.CreateCell(2);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue("");
                    cell = row.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(6);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum1.ToString("G0"));
                    cell = row.CreateCell(7);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum2.ToString("G0"));

                    cell = row.CreateCell(8);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum1.ToString("G0"));
                    cell = row.CreateCell(9);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum2.ToString("G0"));

                    cell = row.CreateCell(10);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum1.ToString("G0"));
                    cell = row.CreateCell(11);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum2.ToString("G0"));

                    cell = row.CreateCell(12);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum1.ToString("G0"));
                    cell = row.CreateCell(13);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum2.ToString("G0"));

                    cell = row.CreateCell(14);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(generalNum1.ToString("G0"));
                    cell = row.CreateCell(15);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(generalNum2.ToString("G0"));

                    cell = row.CreateCell(16);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(17);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(18);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");

                    //统计合并信息
                    //统计
                    var stockNum = list.GroupBy(m => m.Name).Count();
                    var blastNum = list.Where(n => n.IsBlast == 1).GroupBy(m => m.Name).Count();
                    var detonateNum = list.Where(n => n.IsDetonate == 1).GroupBy(m => m.Name).Count();
                    var hyperToxicNum = list.Where(n => n.IsHyperToxic == 1).GroupBy(m => m.Name).Count();
                    var GeneralNum = list.Where(n => n.IsOther == 1 || n.IsToxic == 1 || n.IsCorrode == 1 || n.IsBurn == 1 || n.IsBlast == 1).GroupBy(m => m.Name).Count();

                    y++;
                    region = new CellRangeAddress(y, y, 6, 7);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 8, 9);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 10, 11);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 12, 13);
                    isheet.AddMergedRegion(region);
                    region = new CellRangeAddress(y, y, 14, 15);
                    isheet.AddMergedRegion(region);


                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");

                    cell = row.CreateCell(1);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue("危化品种类：");
                    cell = row.CreateCell(2);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue("");
                    cell = row.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(6);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum.ToString());
                    cell = row.CreateCell(7);
                    cell.CellStyle = cellTitleRight;

                    cell = row.CreateCell(8);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum.ToString());
                    cell = row.CreateCell(9);
                    cell.CellStyle = cellTitleRight;

                    cell = row.CreateCell(10);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum.ToString());
                    cell = row.CreateCell(11);
                    cell.CellStyle = cellTitleRight;

                    //剧毒
                    cell = row.CreateCell(12);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum.ToString());
                    cell = row.CreateCell(13);
                    cell.CellStyle = cellTitleRight;


                    //一般危化品
                    cell = row.CreateCell(14);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(GeneralNum.ToString());
                    cell = row.CreateCell(15);
                    cell.CellStyle = cellTitleRight;

                    cell = row.CreateCell(16);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(17);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(18);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                }
            }

            using var stream = new MemoryStream();
            iwork.Write(stream, true);
            var excelBytes = stream.ToArray();
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "按属性分类统计.xlsx");

        }

        /// <summary>
        /// 市级库存数量统计-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccitystocknumstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcCountyStockNumStatistics>>> DcCityStockNumStatistics_Find([FromBody] DcSchoolMaterialParam param)
        {
            param.unitId = user.UnitId;
            param.userId = user.UserId;
            var pg = await dcSchoolMaterialManager.GetCityStockNumStatistics(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Statistics);
        }
        /// <summary>
        /// 市级库存数量统计-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccitystocknumstatisticsexport")]
        //<used>1</used>
        public async Task<IActionResult> DcCityStockNumStatistics_Export([FromBody] DcSchoolMaterialParam param)
        {
            param.unitId = user.UnitId;
            param.userId = user.UserId;
            param.pageSize = int.MaxValue;
            param.pageIndex = 1;
            var pg = await dcSchoolMaterialManager.GetCityStockNumStatistics(param);
            var excelBytes = await new ExcelHelper<VDcCountyStockNumStatistics>().ExportExecl(pg.data.ToList(), "库存数量统计", new string[] { "AreaName", "TwoCatalogName", "Name", "Model", "UnitsMeasurement", "StockNum"});
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "库存数量统计.xlsx");
        }
        /// <summary>
        /// 市级按属性统计数量-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccityclassifystocknumstatisticsfind")]
        //<used>0</used>
        public async Task<Result<PageModel<VDcCityClassifyStockStatistics>>> DcCityClassifyStockNumStatistics_Find([FromBody] VDcCityClassifyStockStatisticsParam param)
        {
            var seachPageSize = param.pageSize;
            var searchPageIndex = param.pageIndex;

            param.pageSize = int.MaxValue;
            param.pageIndex = 1;
            param.CityId = user.UnitId;
            PageModel<VDcCityClassifyStockStatistics> pg = await dcSchoolMaterialManager.GetCityClassifyStockPaged(param);
            PageModel<VDcCityClassifyStockStatistics> page = new PageModel<VDcCityClassifyStockStatistics>();
            if (pg.data != null && pg.data.Count > 0)
            {
                page.data = pg.data.Take(seachPageSize).Skip((searchPageIndex - 1) * seachPageSize).ToList();
                decimal stockNum1 = 0, stockNum2 = 0,
                       burnNum1 = 0, burnNum2 = 0,
                       blastNum1 = 0, blastNum2 = 0,
                       detonateNum1 = 0, detonateNum2 = 0,
                       toxicNum1 = 0, toxicNum2 = 0,
                       hyperToxicNum1 = 0, hyperToxicNum2 = 0,
                       poisonNum1 = 0, poisonNum2 = 0,
                       corrodeNum1 = 0, corrodeNum2 = 0,
                       otherNum1 = 0, otherNum2 = 0;
                foreach (var model in pg.data)
                {
                    if (model.StockNum > 0)
                    {
                        if (model.UnitsMeasurement != "毫升") stockNum1 += model.StockNum;
                        if (model.UnitsMeasurement == "毫升") stockNum2 += model.StockNum;

                        if (model.IsBurn == 1 && model.UnitsMeasurement != "毫升") burnNum1 += model.StockNum;
                        if (model.IsBurn == 1 && model.UnitsMeasurement == "毫升") burnNum2 += model.StockNum;

                        if (model.IsBlast == 1 && model.UnitsMeasurement != "毫升") blastNum1 += model.StockNum;
                        if (model.IsBlast == 1 && model.UnitsMeasurement == "毫升") blastNum2 += model.StockNum;

                        if (model.IsDetonate == 1 && model.UnitsMeasurement != "毫升") detonateNum1 += model.StockNum;
                        if (model.IsDetonate == 1 && model.UnitsMeasurement == "毫升") detonateNum2 += model.StockNum;

                        if (model.IsToxic == 1 && model.UnitsMeasurement != "毫升") toxicNum1 += model.StockNum;
                        if (model.IsToxic == 1 && model.UnitsMeasurement == "毫升") toxicNum2 += model.StockNum;

                        if (model.IsHyperToxic == 1 && model.UnitsMeasurement != "毫升") hyperToxicNum1 += model.StockNum;
                        if (model.IsHyperToxic == 1 && model.UnitsMeasurement == "毫升") hyperToxicNum2 += model.StockNum;

                        if (model.IsPoison == 1 && model.UnitsMeasurement != "毫升") poisonNum1 += model.StockNum;
                        if (model.IsPoison == 1 && model.UnitsMeasurement == "毫升") poisonNum2 += model.StockNum;

                        if (model.IsCorrode == 1 && model.UnitsMeasurement != "毫升") corrodeNum1 += model.StockNum;
                        if (model.IsCorrode == 1 && model.UnitsMeasurement == "毫升") corrodeNum2 += model.StockNum;

                        if (model.IsOther == 1 && model.UnitsMeasurement != "毫升") otherNum1 += model.StockNum;
                        if (model.IsOther == 1 && model.UnitsMeasurement == "毫升") otherNum2 += model.StockNum;
                    }
                }
                List<object> footer = new List<object>();
                footer.Add(new
                {
                    AreaName = "<b>总计：</b>",
                    StockNum1 = stockNum1,
                    StockNum2 = stockNum2,
                    BurnNum1 = burnNum1,
                    BurnNum2 = burnNum2,
                    BlastNum1 = blastNum1,
                    BlastNum2 = blastNum2,
                    DetonateNum1 = detonateNum1,
                    DetonateNum2 = detonateNum2,
                    ToxicNum1 = toxicNum1,
                    ToxicNum2 = toxicNum2,
                    HyperToxicNum1 = hyperToxicNum1,
                    HyperToxicNum2 = hyperToxicNum2,
                    PoisonNum1 = poisonNum1,
                    PoisonNum2 = poisonNum2,
                    CorrodeNum1 = corrodeNum1,
                    CorrodeNum2 = corrodeNum2,
                    OtherNum1 = otherNum1,
                    OtherNum2 = otherNum2
                });
                page.Other = footer;
            }
            page.dataCount = pg.dataCount;
            return baseSucc(page, page.dataCount, "查询成功", page.Other);
        }
        /// <summary>
        /// 市级按属性统计数量-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccityclassifystocknumstatisticsexport")]
        //<used>0</used>
        public async Task<IActionResult> DcCityClassifyStockNumStatisticsExport([FromBody] VDcCityClassifyStockStatisticsParam param)
        {
            Result r = new Result();
            param.pageSize = int.MaxValue;
            param.pageIndex = 1;
            param.CityId = user.UnitId;
            IWorkbook iwork = new HSSFWorkbook();
            PageModel<VDcCityClassifyStockStatistics> pg = await dcSchoolMaterialManager.GetCityClassifyStockPaged(param);
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                #region workbook_title

                IFont font = iwork.CreateFont();
                font.FontHeightInPoints = 10;
                font.FontName = "宋体";

                IFont font2 = iwork.CreateFont();
                font2.FontHeightInPoints = 12;
                font2.FontName = "宋体";

                IFont font3 = iwork.CreateFont();
                font3.FontHeightInPoints = 16;
                font3.IsBold = true;
                font3.FontName = "宋体";

                IFont font4 = iwork.CreateFont();
                font4.FontHeightInPoints = 10;
                font4.IsBold = true;
                font4.FontName = "宋体";

                IFont font5 = iwork.CreateFont();
                font5.FontHeightInPoints = 12;
                font5.IsBold = true;
                font5.FontName = "宋体";

                ICellStyle cellTitle = iwork.CreateCellStyle();
                cellTitle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitle.Alignment = HorizontalAlignment.Center;
                cellTitle.SetFont(font4);
                cellTitle.VerticalAlignment = VerticalAlignment.Center;
                cellTitle.WrapText = true;//自动换行

                ICellStyle cellTitleRight = iwork.CreateCellStyle();
                cellTitleRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitleRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitleRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitleRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellTitleRight.Alignment = HorizontalAlignment.Right;
                cellTitleRight.SetFont(font4);
                cellTitleRight.VerticalAlignment = VerticalAlignment.Center;

                ICellStyle cellLeft = iwork.CreateCellStyle();
                cellLeft.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellLeft.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellLeft.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellLeft.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellLeft.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellLeft.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellLeft.SetFont(font);
                cellLeft.WrapText = true;//自动换行

                ICellStyle cellRight = iwork.CreateCellStyle();
                cellRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
                cellRight.SetFont(font);
                cellRight.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

                ICellStyle cellCenter = iwork.CreateCellStyle();
                cellCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellCenter.SetFont(font);
                cellCenter.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellCenter.WrapText = true;//自动换行

                ICellStyle cellNull = iwork.CreateCellStyle();
                cellNull.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
                cellNull.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
                cellNull.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
                cellNull.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
                cellNull.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellNull.SetFont(font3);
                cellNull.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

                ICellStyle cellNull2 = iwork.CreateCellStyle();
                cellNull2.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
                cellNull2.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
                cellNull2.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
                cellNull2.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
                cellNull2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellNull2.SetFont(font2);
                cellNull2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

                ICellStyle cellNull3 = iwork.CreateCellStyle();
                cellNull3.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
                cellNull3.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
                cellNull3.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
                cellNull3.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
                cellNull3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellNull3.SetFont(font5);
                cellNull3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

                ICellStyle cellNull4 = iwork.CreateCellStyle();
                cellNull4.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
                cellNull4.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
                cellNull4.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
                cellNull4.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
                cellNull4.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellNull4.SetFont(font);
                cellNull4.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

                //建立一个名为Sheet1的工作表
                NPOI.SS.UserModel.ISheet isheet = iwork.CreateSheet("sheet1");
                isheet.PrintSetup.PaperSize = 9;
                for (int i = 0; i <= 24; i++)
                {
                    if (i == 0)
                    {
                        isheet.SetColumnWidth(i, 5 * 256);
                    }
                    else if (i == 4)
                    {
                        isheet.SetColumnWidth(i, 20 * 256);
                    }
                    else if (i == 2 || i == 3)
                    {
                        isheet.SetColumnWidth(i, 15 * 256);
                    }
                    else if (i == 5)
                    {
                        isheet.SetColumnWidth(i, 6 * 256);
                    }
                    else
                    {
                        isheet.SetColumnWidth(i, 10 * 256);
                    }
                }
                //将数据添加到表中对应的单元格中，因为行已经创建，不需要重新创建行
                CellRangeAddress region = new CellRangeAddress(0, 0, 0, 24);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 0, 0);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 1, 1);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 2, 2);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 3, 3);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 4, 4);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 5, 5);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 6, 7);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 8, 9);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 10, 11);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 12, 13);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 14, 15);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 16, 17);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 18, 19);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 20, 21);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 1, 22, 23);
                isheet.AddMergedRegion(region);
                region = new CellRangeAddress(1, 2, 24, 24);
                isheet.AddMergedRegion(region);
                ICell cell = null;
                IRow row = isheet.CreateRow(0);
                row.HeightInPoints = 35;
                cell = row.CreateCell(0);
                cell.CellStyle = cellNull;
                cell.SetCellValue("按属性分类统计");

                row = isheet.CreateRow(1);
                row.HeightInPoints = 20;
                cell = row.CreateCell(0);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("序号");
                cell = row.CreateCell(1);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("区县名称");
                cell = row.CreateCell(2);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("类  别");
                cell = row.CreateCell(3);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("危化品名称");
                cell = row.CreateCell(4);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("规格、属性");
                cell = row.CreateCell(5);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("单位");
                cell = row.CreateCell(6);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("数量");
                cell = row.CreateCell(7);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(8);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易燃");
                cell = row.CreateCell(9);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(10);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易爆");
                cell = row.CreateCell(11);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(12);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易制爆");
                cell = row.CreateCell(13);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(14);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("有毒");
                cell = row.CreateCell(15);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(16);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("剧毒");
                cell = row.CreateCell(17);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(18);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("易制毒");
                cell = row.CreateCell(19);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(20);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("腐蚀");
                cell = row.CreateCell(21);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(22);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("其它");
                cell = row.CreateCell(23);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(24);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("是否可用");

                row = isheet.CreateRow(2);
                cell = row.CreateCell(0);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(1);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(2);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(3);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(4);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(5);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");
                cell = row.CreateCell(6);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(7);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(8);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(9);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(10);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(11);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(12);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(13);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(14);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(15);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(16);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(17);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(18);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(19);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(20);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(21);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(22);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("克");
                cell = row.CreateCell(23);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("毫升");
                cell = row.CreateCell(24);
                cell.CellStyle = cellTitle;
                cell.SetCellValue("");


                #endregion
                #region 打印重复表头
                //横向打印
                isheet.PrintSetup.Landscape = true;
                //缩放比：100% 不缩放
                isheet.PrintSetup.Scale = 100;
                //不缩放到一页
                isheet.FitToPage = false;
                //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
                isheet.RepeatingRows = new CellRangeAddress(1, 1, 0, int.MaxValue);
                #endregion

                decimal stockNum1 = 0, stockNum2 = 0,
                    burnNum1 = 0, burnNum2 = 0,
                    blastNum1 = 0, blastNum2 = 0,
                    detonateNum1 = 0, detonateNum2 = 0,
                    toxicNum1 = 0, toxicNum2 = 0,
                    hyperToxicNum1 = 0, hyperToxicNum2 = 0,
                    poisonNum1 = 0, poisonNum2 = 0,
                    corrodeNum1 = 0, corrodeNum2 = 0,
                    otherNum1 = 0, otherNum2 = 0;
                int y = 3;
                var list = pg.data;
                for (int i = 0; i < list.Count; i++)
                {
                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(i + 1);

                    cell = row.CreateCell(1);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].AreaName);

                    cell = row.CreateCell(2);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].TwoCatalogName);

                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Name);

                    cell = row.CreateCell(4);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue(list[i].Model);

                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].UnitsMeasurement);

                    decimal stockNum = list[i].StockNum;

                    cell = row.CreateCell(6);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(7);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(8);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBurn == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(9);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBurn == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(10);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBlast == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(11);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsBlast == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(12);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsDetonate == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(13);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsDetonate == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(14);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsToxic == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(15);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsToxic == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(16);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(17);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(18);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsPoison == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(19);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsPoison == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(20);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsCorrode == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(21);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsCorrode == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(22);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsOther == 1 && list[i].UnitsMeasurement != "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(23);
                    cell.CellStyle = cellRight;
                    cell.SetCellValue(list[i].IsOther == 1 && list[i].UnitsMeasurement == "毫升" ? stockNum.ToString("G0") : "");

                    cell = row.CreateCell(24);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue(list[i].IsMayUse == 1 ? "是" : "否");
                    y++;

                    if (list[i].UnitsMeasurement != "毫升") stockNum1 += stockNum;
                    if (list[i].UnitsMeasurement == "毫升") stockNum2 += stockNum;

                    if (list[i].IsBurn == 1 && list[i].UnitsMeasurement != "毫升") burnNum1 += stockNum;
                    if (list[i].IsBurn == 1 && list[i].UnitsMeasurement == "毫升") burnNum2 += stockNum;

                    if (list[i].IsBlast == 1 && list[i].UnitsMeasurement != "毫升") blastNum1 += stockNum;
                    if (list[i].IsBlast == 1 && list[i].UnitsMeasurement == "毫升") blastNum2 += stockNum;

                    if (list[i].IsDetonate == 1 && list[i].UnitsMeasurement != "毫升") detonateNum1 += stockNum;
                    if (list[i].IsDetonate == 1 && list[i].UnitsMeasurement == "毫升") detonateNum2 += stockNum;

                    if (list[i].IsToxic == 1 && list[i].UnitsMeasurement != "毫升") toxicNum1 += stockNum;
                    if (list[i].IsToxic == 1 && list[i].UnitsMeasurement == "毫升") toxicNum2 += stockNum;

                    if (list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement != "毫升") hyperToxicNum1 += stockNum;
                    if (list[i].IsHyperToxic == 1 && list[i].UnitsMeasurement == "毫升") hyperToxicNum2 += stockNum;

                    if (list[i].IsPoison == 1 && list[i].UnitsMeasurement != "毫升") poisonNum1 += stockNum;
                    if (list[i].IsPoison == 1 && list[i].UnitsMeasurement == "毫升") poisonNum2 += stockNum;

                    if (list[i].IsCorrode == 1 && list[i].UnitsMeasurement != "毫升") corrodeNum1 += stockNum;
                    if (list[i].IsCorrode == 1 && list[i].UnitsMeasurement == "毫升") corrodeNum2 += stockNum;

                    if (list[i].IsOther == 1 && list[i].UnitsMeasurement != "毫升") otherNum1 += stockNum;
                    if (list[i].IsOther == 1 && list[i].UnitsMeasurement == "毫升") otherNum2 += stockNum;
                }
                if (list.Count > 0)
                {
                    row = isheet.CreateRow(y);
                    row.HeightInPoints = 20;
                    cell = row.CreateCell(0);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(1);
                    cell.CellStyle = cellTitle;
                    cell.SetCellValue("总计：");
                    cell = row.CreateCell(2);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(3);
                    cell.CellStyle = cellLeft;
                    cell.SetCellValue("");
                    cell = row.CreateCell(4);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(5);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                    cell = row.CreateCell(6);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum1.ToString("G0"));
                    cell = row.CreateCell(7);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(stockNum2.ToString("G0"));
                    cell = row.CreateCell(8);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(burnNum1.ToString("G0"));
                    cell = row.CreateCell(9);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(burnNum2.ToString("G0"));
                    cell = row.CreateCell(10);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum1.ToString("G0"));
                    cell = row.CreateCell(11);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(blastNum2.ToString("G0"));
                    cell = row.CreateCell(12);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum1.ToString("G0"));
                    cell = row.CreateCell(13);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(detonateNum2.ToString("G0"));
                    cell = row.CreateCell(14);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(toxicNum1.ToString("G0"));
                    cell = row.CreateCell(15);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(toxicNum2.ToString("G0"));
                    cell = row.CreateCell(16);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum1.ToString("G0"));
                    cell = row.CreateCell(17);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(hyperToxicNum2.ToString("G0"));
                    cell = row.CreateCell(18);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(poisonNum1.ToString("G0"));
                    cell = row.CreateCell(19);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(poisonNum2.ToString("G0"));
                    cell = row.CreateCell(20);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(corrodeNum1.ToString("G0"));
                    cell = row.CreateCell(21);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(corrodeNum2.ToString("G0"));
                    cell = row.CreateCell(22);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(otherNum1.ToString("G0"));
                    cell = row.CreateCell(23);
                    cell.CellStyle = cellTitleRight;
                    cell.SetCellValue(otherNum2.ToString("G0"));
                    cell = row.CreateCell(24);
                    cell.CellStyle = cellCenter;
                    cell.SetCellValue("");
                }

                
            }
            using var stream = new MemoryStream();
            iwork.Write(stream, true);
            var excelBytes = stream.ToArray();
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "按属性分类统计.xlsx");
        }

        /// <summary>
        /// 市级采购数量统计-查询
        /// </summary>
        /// <param name="years"></param>
        /// <param name="classTwoId"></param>
        /// <param name="name"></param>
        /// <param name="model"></param>
        /// <param name="countyId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccitypurchasenumstatisticsfind")]
        //<used>0</used>
        public async Task<Result<string>> DcCityPurchaseNumStatistics_Find(string years, int classTwoId, string name, string model, int countyId)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });

        }

        /// <summary>
        /// 市级领用数量统计-查询
        /// </summary>
        /// <param name="years"></param>
        /// <param name="classTwoId"></param>
        /// <param name="name"></param>
        /// <param name="model"></param>
        /// <param name="countyId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccityapplynumstatisticsfind")]
        //<used>1</used>
        public async Task<Result<string>> DcCityApplyNumStatistics_Find(string years, int classTwoId, string name, string model, int countyId)
        {
            return await Task.Run<Result<string>>(() =>
            {
                return baseFailed<string>("非法操作，系统阻止");
            });
        }

        /// <summary>
        /// 市级废弃物存量统计-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dccitywastedisposalstatisticsfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcCityWasteDisposalStatistics_Find([FromBody] DcWasteDisposalDetailParam param)
        {
            param.unitId = user.UnitId;
            param.UnitTypeId = user.UnitTypeId;
            var pg = await dcWasteDisposalDetailManager.DcWasteStatisticsList_GetByCity(param);
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }
        /// <summary>
        /// 采购台账-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcstandbookpurchasefind")]
        //<used>0</used>
        public async Task<Result<PageModel<DcSchoolMaterialDto>>> DcStandbookPurchase_Find([FromBody] DcSchoolMaterialModelParam param)
        {
            var pg = await dcSchoolMaterialManager.GetStockbookPurchase(param);
            return baseSucc(pg, pg.dataCount, "查询成功。");
        }

        /// <summary>
        /// 采购台账导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportdcstandbookpurchasefind")]
        public async Task<IActionResult> ExportDcStandbookPurchase_Find([FromBody] DcSchoolMaterialModelParam param)
        {
            param.pageSize = int.MaxValue;
            var pg = await dcSchoolMaterialManager.GetStockbookPurchase(param);
            var excelBytes = await new ExcelHelper<DcSchoolMaterialDto>().ExportExecl(pg.data.ToList(), "采购台账", new string[] { "Name", "Model", "UnitName", "Num", "RegDate", "CompanyName"});
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "采购台账.xlsx");
        }


        /// <summary>
        /// 存量台账-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcstandbookstockfind")]
        //<used>0</used>
        public async Task<Result<PageModel<DcSchoolMaterialModelDto>>> DcStandbookStock_Find([FromBody] DcSchoolMaterialModelParam param)
        {
            if (param.Attribute > 0)
            {
                switch (param.Attribute)
                {
                    case 1:
                        param.IsDetonate = 1;
                        break;
                    case 2:
                        param.IsPoison = 1;
                        break;
                    case 3:
                        param.IsHyperToxic = 1;
                        break;
                }
            }
            if (param.Month > 0 && param.Year > 0)
            {
                param.unitId = user.UnitId;
                var pg = await dcBaseWasteManager.GetStockbookStand(param);
                return baseSucc(pg, pg.dataCount, "查询成功。");
            }
            else
            {
                return baseFailed<PageModel<DcSchoolMaterialModelDto>>("请选择统计时间。");
            }
    

        }

        /// <summary>
        /// 处置台账-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcstandbookwastefind")]
        //<used>0</used>
        public async Task<Result<PageModel<DcBaseWasteDto>>> DcStandbookWaste_Find([FromBody] DcWasteDisposalDetailParam param)
        {
            param.SchoolId = user.UnitId;
            var pg = await dcBaseWasteManager.GetWasteStandbook(param);
            return baseSucc(pg, pg.dataCount, "查询成功。");
        }


        /// <summary>
        /// 存量台账导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportdcstandbookstockfind")]
        public async Task<IActionResult> ExportDcStandbookStock_Find(DcSchoolMaterialModelParam param)
        {
            PageModel<DcSchoolMaterialModelDto> pg = new PageModel<DcSchoolMaterialModelDto>();
            if (param.Attribute > 0)
            {
                switch (param.Attribute)
                {
                    case 1:
                        param.IsDetonate = 1;
                        break;
                    case 2:
                        param.IsPoison = 1;
                        break;
                    case 3:
                        param.IsHyperToxic = 1;
                        break;
                }
            }
            param.pageSize = int.MaxValue;
            if (param.Month > 0 && param.Year > 0)
            {
                param.unitId = user.UnitId;
                pg = await dcBaseWasteManager.GetStockbookStand(param);
            }
            var excelBytes = await new ExcelHelper<DcSchoolMaterialModelDto>().ExportExecl(pg.data.ToList(), "存量台账", new string[] { "Name", "Model", "UnitsMeasurement", "LastStockNum", "PurchaseNum", "ApplyNum", "BackNum", "ScrapNum", "StockNum" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "存量台账.xlsx");
        }


        /// <summary>
        /// 导出处置台账
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportdcstandbookwastefind")]
        public async Task<IActionResult> ExportDcStandbookWaste_Find(DcWasteDisposalDetailParam param)
        {
            param.SchoolId = user.UnitId;
            param.pageSize= int.MaxValue;
            var pg = await dcBaseWasteManager.GetWasteStandbook(param);
            var excelBytes = await new ExcelHelper<DcBaseWasteDto>().ExportExecl(pg.data.ToList(), "处置台账", new string[] { "OneClassName", "Name", "UnitsMeasurement", "WaitNum", "DisposalNum" });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "处置台账.xlsx");
        }


        /// <summary>
        /// 导出危化品存量库(个性化处理)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportdcscraplist")]
        public async Task<IActionResult> ExportDcScrapList([FromBody] VDcSchoolMaterialListParam param)
        {
            IWorkbook iwork = new HSSFWorkbook();
            ISheet isheet = iwork.CreateSheet("Sheet1");
            isheet.PrintSetup.PaperSize = 9; //A3 = 8 ，A4 = 9

            IRow row = null;
            ICell cell = null;
            CellRangeAddress region = null;
            IFont font = iwork.CreateFont();
            font.FontName = "宋体";
            font.FontHeightInPoints = 11;

            IFont font1 = iwork.CreateFont();
            font1.FontName = "宋体";
            font1.IsBold = true;
            font1.FontHeightInPoints = 13;

            IFont font2 = iwork.CreateFont();
            font2.FontName = "宋体";
            font2.IsBold = true;
            font2.Color = NPOI.HSSF.Util.HSSFColor.Grey50Percent.Index;
            font2.FontHeightInPoints = 10;

            IFont font3 = iwork.CreateFont();
            font3.FontName = "宋体";
            font3.FontHeightInPoints = 10;

            IFont font4 = iwork.CreateFont();
            font4.FontName = "宋体";
            font4.FontHeightInPoints = 9;


            ICellStyle cellstyleCenter = iwork.CreateCellStyle();
            cellstyleCenter.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleCenter.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleCenter.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleCenter.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleCenter.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellstyleCenter.SetFont(font3);
            cellstyleCenter.VerticalAlignment = VerticalAlignment.Center;

            ICellStyle cellstyleRight = iwork.CreateCellStyle();
            cellstyleRight.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleRight.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleRight.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleRight.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyleRight.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
            cellstyleRight.SetFont(font3);
            cellstyleRight.VerticalAlignment = VerticalAlignment.Center;

            ICellStyle cellstyle3 = iwork.CreateCellStyle();
            cellstyle3.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle3.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle3.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle3.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
            cellstyle3.SetFont(font3);
            cellstyle3.VerticalAlignment = VerticalAlignment.Center;

            ICellStyle cellstyle4 = iwork.CreateCellStyle();
            cellstyle4.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle4.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle4.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle4.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            cellstyle4.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
            cellstyle4.WrapText = true;
            cellstyle4.SetFont(font2);
            cellstyle4.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;


            ICellStyle cellstyle5 = iwork.CreateCellStyle();
            cellstyle5.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle5.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle5.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle5.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle5.Alignment = HorizontalAlignment.Center;
            cellstyle5.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            cellstyle5.SetFont(font1);

            ICellStyle cellstyle6 = iwork.CreateCellStyle();
            cellstyle6.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle6.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle6.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle6.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle6.Alignment = HorizontalAlignment.Left;
            cellstyle6.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            cellstyle6.SetFont(font);

            ICellStyle cellstyle7 = iwork.CreateCellStyle();
            cellstyle7.BorderTop = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle7.BorderLeft = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle7.BorderRight = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle7.BorderBottom = NPOI.SS.UserModel.BorderStyle.None;
            cellstyle7.Alignment = HorizontalAlignment.Right;
            cellstyle7.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
            cellstyle7.SetFont(font4);

            for (int i = 0; i <= 4; i++)
            {
                if (i == 1)
                {
                    isheet.SetColumnWidth(i, 20 * 256);
                }
                else if (i == 2)
                {
                    isheet.SetColumnWidth(i, 25 * 256);
                }
                else
                {
                    isheet.SetColumnWidth(i, 13 * 256);
                }
            }

            int rowIndex = 0;
            int cellIndex = 0;

            #region 打印重复表头
            //横向打印
            isheet.PrintSetup.Landscape = true;
            //缩放比：100% 不缩放
            isheet.PrintSetup.Scale = 100;
            //不缩放到一页
            isheet.FitToPage = false;
            //设置重复出现的行（表头）[备注：第一行~第二行重复，第一列~最后一列]
            isheet.RepeatingRows = new CellRangeAddress(rowIndex, rowIndex, 0, int.MaxValue);
            #endregion


            param.Statuz = 1;
            param.IsStockNum = true;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.unitId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                param.CityId = user.UnitId;
            }


            ////获取数据
            //string where = "Statuz = 1 AND StockNum > 0 ";
            //if (!string.IsNullOrEmpty(hidBeginTime.Value))
            //{
            //    where += string.Format(" AND RegDate >= '{0}'", hidBeginTime.Value);
            //}
            //if (!string.IsNullOrEmpty(hidEndTime.Value))
            //{
            //    where += string.Format(" AND RegDate <= '{0}'", hidEndTime.Value + " 23:59:59");
            //}
            //if (!string.IsNullOrEmpty(hidCatalogSecond.Value) && hidCatalogSecond.Value != "0")
            //{
            //    where += string.Format(" AND TwoCatalogId = {0}", hidCatalogSecond.Value);
            //}
            //if (!string.IsNullOrEmpty(hidIsMayUse.Value) && hidIsMayUse.Value != "-1")
            //{
            //    where += string.Format(" AND IsMayUse = {0}", hidIsMayUse.Value);
            //}
            //if (!string.IsNullOrEmpty(hidDatKeyword.Value))
            //{
            //    where += string.Format(" AND {0} LIKE '%{1}%'", hidDatField.Value, ComLib.filterExportSql(hidDatKeyword.Value));
            //}
            //string orderBy = "DepositAddressId asc, CabinetAddress asc, SchoolCatalogId asc, SchoolMaterialModelId asc";
            //if (UnitTypeId == UnitTypes.School.ToInt())
            //{
            //    where += " AND SchoolId = " + UnitId;
            //}
            //else
            //{
            //    where = " 1<>1 ";
            //}

            List<SortBaseModel> listSortModel = new List<SortBaseModel>();
            listSortModel.add(new SortBaseModel() { SortCode = "DepositAddressId", SortType = "ASC" });
            listSortModel.add(new SortBaseModel() { SortCode = "CabinetAddress", SortType = "ASC" });
            listSortModel.add(new SortBaseModel() { SortCode = "SchoolCatalogId", SortType = "ASC" });
            listSortModel.add(new SortBaseModel() { SortCode = "SchoolMaterialModelId", SortType = "ASC" });
            param.sortModel = listSortModel;
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<VDcSchoolMaterialList> pg = await dcSchoolMaterialManager.GetListPaged(param);
            var list = pg.data;
            var data = list.GroupBy(f => new { f.DepositAddressId, f.Address, f.CabinetAddress, f.SchoolCatalogId, f.Name, f.SchoolMaterialModelId, f.Model, f.UnitName })
                .Select(p => new
                {
                    DepositAddressId = p.Key.DepositAddressId,
                    Address = p.Key.Address,
                    CabinetAddress = p.Key.CabinetAddress,
                    SchoolCatalogId = p.Key.SchoolCatalogId,
                    Name = p.Key.Name,
                    SchoolMaterialModelId = p.Key.SchoolMaterialModelId,
                    Model = p.Key.Model,
                    UnitName = p.Key.UnitName,
                    StockNum = p.Sum(x => x.StockNum)
                }).ToList();

            if (data != null && data.Count > 0)
            {
                long depositAddressId = 0; //存放地点id
                foreach (var item in data)
                {
                    if (item.DepositAddressId != depositAddressId)
                    {
                        rowIndex++;
                        row = isheet.CreateRow(rowIndex);
                        region = new CellRangeAddress(rowIndex, rowIndex, 0, 4);
                        isheet.AddMergedRegion(region);
                        rowIndex++;
                        row.HeightInPoints = 35;
                        cell = row.CreateCell(0);
                        cell.CellStyle = cellstyle5;
                        cell.SetCellValue("危险化学品橱柜卡");

                        row = isheet.CreateRow(rowIndex);
                        region = new CellRangeAddress(rowIndex, rowIndex, 0, 4);
                        isheet.AddMergedRegion(region);
                        row.HeightInPoints = 20;
                        rowIndex++;
                        cell = row.CreateCell(0);
                        cell.CellStyle = cellstyle6;
                        cell.SetCellValue("地点：" + item.Address);

                        cellIndex = 0;
                        row = isheet.CreateRow(rowIndex);
                        row.HeightInPoints = 25;
                        rowIndex++;
                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyle4;
                        cell.SetCellValue("层次");

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyle4;
                        cell.SetCellValue("危化品名称");

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyle4;
                        cell.SetCellValue("规格属性");

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyle4;
                        cell.SetCellValue("数量");

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyle4;
                        cell.SetCellValue("单位");

                        row = isheet.CreateRow(rowIndex);
                        rowIndex++;
                        row.HeightInPoints = 20;
                        cellIndex = 0;
                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.CabinetAddress);

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.Name);

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyle3;
                        cell.SetCellValue(item.Model);

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.StockNum.ToString("G0"));

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.UnitName);
                    }
                    else
                    {
                        row = isheet.CreateRow(rowIndex);
                        rowIndex++;
                        row.HeightInPoints = 20;
                        cellIndex = 0;
                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.CabinetAddress);

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.Name);

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyle3;
                        cell.SetCellValue(item.Model);

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.StockNum.ToString("G0"));

                        cell = row.CreateCell(cellIndex);
                        cellIndex++;
                        cell.CellStyle = cellstyleCenter;
                        cell.SetCellValue(item.UnitName);
                    }
                    depositAddressId = item.DepositAddressId;
                }
            }
            using var stream = new MemoryStream();
            iwork.Write(stream, true);
            var excelBytes = stream.ToArray();
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "危化品存量库.xlsx");
        }
        #endregion
    }
}
