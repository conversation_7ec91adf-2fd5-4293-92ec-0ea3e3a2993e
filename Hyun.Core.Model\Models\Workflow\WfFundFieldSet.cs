﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///项目库字段管理表
    ///</summary>
    [SugarTable("wf_FundFieldSet","项目库字段管理表")]
    public class WfFundFieldSet : BaseEntity
    {

          public WfFundFieldSet()
          {

          }

           /// <summary>
           ///项目库字段显示名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ShowName { get; set; }

           /// <summary>
           ///项目库字段名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Code { get; set; }

           /// <summary>
           ///字段类型
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string CodeType { get; set; }

           /// <summary>
           ///显示类型（1：数字  2：金额  3：文本   4：日期  ）
          /// </summary>
          public int ShowType { get; set; } = 1;

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

