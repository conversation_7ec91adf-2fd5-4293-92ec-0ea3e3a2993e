﻿namespace Hyun.Core.Model
{

    ///<summary>
    ///评价清单表
    ///</summary>
    public class XUniformEvaluateParam : BaseSearch
    {

        public XUniformEvaluateParam()
        {

        }

        /// <summary>
        ///Ids
        /// </summary>
        public string Ids { get; set; }


        /// <summary>
        /// 年份
        /// </summary>
        public int PurchaseYear { get; set; } = -10000;

        /// <summary>
        /// 供应商Id
        /// </summary>
        public long CompanyId { get; set; } = -10000;


        /// <summary>
        /// 关键字
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 单位Id
        /// </summary>
        public long SchoolId { get; set; } = 0;

        /// <summary>
        /// 状态（1：正在评价，2：评价结束）
        /// </summary>
        public int Statuz { get; set; } = -10000;

        /// <summary>
        /// 区域Id
        /// </summary>
        public long AreaId { get; set; } = 0;
    }

 
}

