﻿using AutoMapper;
using Grpc.Core;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.Validator;
using NetTaste;
using NPOI.Util;
using SqlSugar;
using System.Diagnostics;
using NCalc;
using Hyun.Core.Model;
using Hyun.Core.Model.Models.Workflow;
using Hyun.Core.Repository.UnitOfWorks;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using System.Collections.Generic;
using NPOI.XSSF.Model;
using ZstdSharp.Unsafe;
using System;
using System.Net.Mail;
using MongoDB.Bson;
using OfficeOpenXml.VBA;
using Microsoft.AspNetCore.Server.IISIntegration;
using Nacos.V2.Remote;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Linq;
using System.Text.RegularExpressions;
using Org.BouncyCastle.Bcpg.OpenPgp;
using MongoDB.Bson.Serialization.IdGenerators;

namespace Hyun.Core.Services
{

    ///<summary>
    ///WfProjectDeclaration方法
    ///</summary>
    public class WfProjectDeclarationServices : BaseServices<WfProjectDeclaration>, IWfProjectDeclarationServices
    {
        private readonly IUser user;
        private readonly IMapper mapper;
        private readonly IUnitOfWorkManage unitOfWorkManage;
        private readonly IWfSourceFundServices sourceFundManager;

        public WfProjectDeclarationServices(IUser _user, IMapper _mapper, IUnitOfWorkManage _unitOfWorkManage, IWfSourceFundServices _sourceFundManager)
        {
            user = _user;
            mapper = _mapper;
            unitOfWorkManage = _unitOfWorkManage;
            sourceFundManager = _sourceFundManager;
        }

        /// <summary>
        /// 删除填报信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> DelFillingInfo(long id)
        {
            var obj = await base.QueryById(id);
            if(obj == null)
            {
                return Result<string>.Fail("删除失败，数据不存在!");
            }
            if (obj.CreateId != user.ID)
            {
                return Result<string>.Fail("只有创建人才能删除!");
            }
            if (obj.Statuz != 0)
            {
                var objAudit = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProjectDeclarationId == id && f.ApprovalStatuz == 0 && f.IsDeleted == false).FirstAsync();
                if(objAudit != null)
                {
                    var objNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == objAudit.ProcessNodeId).FirstAsync();
                    if (objNode != null)
                    {
                        if (obj.Statuz % 2 == 1 && objNode.AuditBackCanDel == 1)
                        {
                            await this.Db.Updateable<WfProjectDeclaration>().SetColumns(f => new WfProjectDeclaration() { IsDeleted = true }).Where(f => f.Id == id).ExecuteCommandAsync();
                            await this.Db.Updateable<WfProjectDeclarationDetail>().SetColumns(f => new WfProjectDeclarationDetail() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                            await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                            await this.Db.Updateable<WfProjectDeclarationQuery>().SetColumns(f => new WfProjectDeclarationQuery() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                            //删除清单数据
                            await this.Db.Updateable<WfProjectList>().SetColumns(f => new WfProjectList() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                            //删除附件数据
                            await this.Db.Updateable<BAttachment>().SetColumns(f => new BAttachment() { IsDeleted = true, IsDelete = 1 }).Where(f => f.MainId == id).ExecuteCommandAsync();

                            //删除资金来源数据
                            await this.Db.Updateable<WfSourceFundUse>().SetColumns(f => new WfSourceFundUse() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                        }
                        else
                        {
                            return Result<string>.Fail("当前状态不能删除!");
                        }
                    }
                    else
                    {
                        return Result<string>.Fail("删除失败，节点不存在!");
                    }
                }
            }
            else
            {
                await this.Db.Updateable<WfProjectDeclaration>().SetColumns(f => new WfProjectDeclaration() { IsDeleted = true }).Where(f => f.Id == id).ExecuteCommandAsync();
                await this.Db.Updateable<WfProjectDeclarationDetail>().SetColumns(f => new WfProjectDeclarationDetail() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                await this.Db.Updateable<WfProjectDeclarationQuery>().SetColumns(f => new WfProjectDeclarationQuery() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                //删除清单数据
                await this.Db.Updateable<WfProjectList>().SetColumns(f => new WfProjectList() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
                //删除附件数据
                await this.Db.Updateable<BAttachment>().SetColumns(f => new BAttachment() { IsDeleted = true, IsDelete = 1 }).Where(f => f.MainId == id).ExecuteCommandAsync();

                //删除资金来源数据
                await this.Db.Updateable<WfSourceFundUse>().SetColumns(f => new WfSourceFundUse() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == id).ExecuteCommandAsync();
            }
            //计算资金来源
            await CalculateSourceFund(id);
            return Result<string>.Success("删除成功");
        }

        /// <summary>
        /// 保存提交填报页面
        /// </summary>
        /// <param name="o"></param>
        /// <param name="typeButton">1：保存，2：提交</param>
        /// <returns></returns>
        public async Task<Result> FillingSave(FillingModel o, int typeButton)
        {
            Result r = new Result();
            //提示文字
            string tipMsg = "保存成功";
            //采购项目名称
            string projectName = "";
            string projectCode = string.Empty;
            long oneClassId = 0;
            long twoClassId = 0;
            //项目金额
            bool isHasProjectAmount = false;
            decimal projectAmount = 0;
            //年度
            int planYear = DateTime.Now.Year;
            //部门Id
            long departmentId = 0;
            //数量
            decimal numSum = 0;
            //状态值
            int statuz = 0;
            //状态描述内容
            string statuzDesc = "填报中";
            //采购项目立项申报Id
            long projectDeclarationId = 0;

            //是否可撤销
            bool isCanIsWithdraw = false;
            //是否为第一次转交下一步,默认是
            bool isFirstNext = true;

            long fromId = 0;
            //下一个节点Id
            long toId = o.ProcessNodeId;
            //审批流程节点关系表Id
            long processNodeLinkId = 0;
            //审批人Id逗号分隔（只有节点配置设置成指定人的时候）
            string auditUserIds = "";
            //审批人员类型（1：所有人审批；2指定单人审批；3：指定多人都审批）
            int approvalType = 1;
            //清单金额
            decimal sumMoney = 0;

            long auditFromId = 0;
            long auditToId = o.ProcessNodeId;
            //清单编码集合
            List<string> listFieldCode = new List<string>();
            int isWithdraw = 2;
            int currentStatuz = 0;
            string strUserIds = "";
            long appointProcessNodeId = 0;

            //填报实体
            WfProjectDeclaration objDeclaration = new WfProjectDeclaration();

            if (typeButton == 2)
            {
                tipMsg = "提交成功";
            }

            //获取流程
            WfProcess objProcess = await this.Db.Queryable<WfProcess>().Where(f => f.Id == o.ProcessId).FirstAsync();
            if(objProcess == null)
            {
                r.flag = 0;
                r.msg = "当前流程不存在";
                return r;
            }

            //获取流程节点
            var objCurrentNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == o.ProcessNodeId).FirstAsync();
            if(objCurrentNode == null)
            {
                r.flag = 0;
                r.msg = "当前节点不存在";
                return r;
            }
            List<FieldFillingModel> list = new List<FieldFillingModel>();

            //处理填报字段
            if (!string.IsNullOrEmpty(o.FieldConfig))
            {
                list = JsonHelper.JsonToObj<List<FieldFillingModel>>(o.FieldConfig);
                if (list.Count > 0)
                {
                    FieldFillingModel fillObj = list.Where(f => f.FieldCode == "ProjectName").FirstOrDefault();
                    if (fillObj != null)
                    {
                        projectName = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                        var obj = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.ProjectCode == projectName && f.IsDeleted == false && f.UnitId == user.UnitId).FirstAsync();
                        if (obj != null)
                        {
                            projectName = obj.ProjectName;
                        }
                    }

                    fillObj = list.Where(f => f.FieldCode == "ProjectCode").FirstOrDefault();
                    if (fillObj != null)
                    {
                        projectCode = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                    }

                    fillObj = list.Where(f => f.FieldCode == "OneClassId").FirstOrDefault();
                    if (fillObj != null)
                    {
                        string strOneClassId = fillObj.InfoId == null ? "" : fillObj.InfoId.ToString();
                        long.TryParse(strOneClassId, out oneClassId);
                    }

                    fillObj = list.Where(f => f.FieldCode == "TwoClassId").FirstOrDefault();
                    if (fillObj != null)
                    {
                        string strTwoClassId = fillObj.InfoId == null ? "" : fillObj.InfoId.ToString();
                        long.TryParse(strTwoClassId, out twoClassId);
                    }

                    fillObj = list.Where(f => f.FieldCode == "ProjectAmount").FirstOrDefault();
                    if (fillObj != null)
                    {
                        isHasProjectAmount = true;
                        string strProjectAmount = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                        decimal.TryParse(strProjectAmount, out projectAmount);
                    }

                    fillObj = list.Where(f => f.FieldCode == "NumSum").FirstOrDefault();
                    if (fillObj != null)
                    {
                        string strNumSum = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                        decimal.TryParse(strNumSum, out numSum);
                    }

                    fillObj = list.Where(f => f.FieldCode == "PlanYear").FirstOrDefault();
                    if (fillObj != null)
                    {
                        string strPlanYear = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                        int.TryParse(strPlanYear, out planYear);
                    }

                    //指定审核人
                    long appointAuditUserId = 0;
                    fillObj = list.Where(f => f.FieldCode == "AppointAuditUser").FirstOrDefault();
                    if (fillObj != null)
                    {
                        string strAppointAuditUser = fillObj.InfoId == null ? "" : fillObj.InfoId.ToString();
                        long.TryParse(strAppointAuditUser, out appointAuditUserId);
                        if (appointAuditUserId > 0)
                        {
                            strUserIds = appointAuditUserId.ToString();
                        }
                        else
                        {
                            List<long> listUserId = JsonHelper.JsonToObj<List<long>>(strAppointAuditUser);
                            if (listUserId != null)
                            {
                                strUserIds = string.Join(",", listUserId);
                            }
                        }
                    }
                }
            }

            if (!string.IsNullOrEmpty(o.FieldConfig))
            {
                //加上填报人、填报时间
                o.FieldConfig = o.FieldConfig.Replace("ReportUserName_@#$", user.Name).Replace("ReportDate_@#$", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }

            if (list.Count == 0)
            {
                r.flag = 0;
                r.msg = "未提交任何数据";
                return r;
            }

            if (typeButton == 2)
            {
                FieldFillingModel fillObj = list.Where(f => f.FieldCode == "ProjectList").FirstOrDefault();
               
                if (o.ProjectDeclarationId == 0 && fillObj != null)
                {
                    r.flag = 0;
                    r.msg = "请先添加项目清单";
                    return r;
                }
            }

            //验证必填是否未填写
            List<WfProcessField> listField = await this.Db.Queryable<WfProcessField>().Where(f => f.ProcessNodeId == o.ProcessNodeId && f.Statuz == 1 && f.IsDeleted == false).ToListAsync();

            if (o.ProjectDeclarationId > 0)
            {
                objDeclaration = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.Id == o.ProjectDeclarationId).FirstAsync();
                if (objDeclaration == null)
                {
                    r.flag = 0;
                    r.msg = "数据信息不存在";
                    return r;
                }
                projectDeclarationId = objDeclaration.Id;
                //此处要判断状态，退回的和0的可以修改，其它都不能修改
                if (objDeclaration.Statuz != 0 && objDeclaration.Statuz % 2 != 1)
                {
                    r.flag = 0;
                    r.msg = "当前状态不能修改填报信息";
                    return r;
                }
            }
            else
            {
                long schoolId = 0;
                long countyId = 0;
                long cityId = 0;
                if(user.UnitTypeId == UnitTypeEnum.School.ToEnumInt())
                {
                    schoolId = user.UnitId;
                    countyId = user.UnitPId;
                    var objUnit = await this.Db.Queryable<PUnit>().Where(f => f.Id == countyId && f.Statuz == 1).FirstAsync();
                    if(objUnit != null)
                    {
                        cityId = objUnit.PId;
                    }
                }
                else if(user.UnitTypeId == UnitTypeEnum.County.ToEnumInt())
                {
                    schoolId = user.UnitId;
                    countyId = user.UnitId;
                    cityId = user.UnitPId;
                }
                else if(user.UnitTypeId == UnitTypeEnum.City.ToEnumInt())
                {
                    schoolId = user.UnitId;
                    countyId = 0;
                    cityId = user.UnitPId;
                }

                objDeclaration.Id = BaseDBConfig.GetYitterId();
                objDeclaration.ProcessId = o.ProcessId;
                objDeclaration.UnitId = schoolId;
                objDeclaration.ProjectName = projectName;
                objDeclaration.ProjectAmount = projectAmount;
                objDeclaration.Statuz = statuz;
                objDeclaration.StatuzDesc = statuzDesc;
                objDeclaration.NumSum = numSum;
                objDeclaration.DataContent = o.FieldConfig;
                objDeclaration.FromId = fromId;
                objDeclaration.ToId = toId;
                objDeclaration.ProcessNodeLinkId = processNodeLinkId;
                objDeclaration.AuditUserIds = auditUserIds;
                objDeclaration.PlanYear = planYear;
                objDeclaration.AuditedUserIds = "";
                objDeclaration.IsWithdraw = isWithdraw;
                objDeclaration.CurrentStatuz = currentStatuz;
                objDeclaration.FillingDate = DateTime.Now;
                objDeclaration.CountyId = countyId;
                objDeclaration.CityId = cityId;
                objDeclaration.ProjectCode = projectCode;
                objDeclaration.OneClassId = oneClassId;
                objDeclaration.TwoClassId = twoClassId;
                await this.Db.Insertable<WfProjectDeclaration>(objDeclaration).ExecuteCommandAsync();
                projectDeclarationId = objDeclaration.Id;
            }

            //先判断审核表数据是否存在
            WfProjectAudit objProjectAudit = await this.Db.Queryable<WfProjectAudit>().Where(f => f.IsDeleted == false && f.ApprovalStatuz == 0 && f.ProjectDeclarationId == projectDeclarationId && f.ProcessNodeId == o.ProcessNodeId).FirstAsync();
            List<WfProjectDeclarationQuery> listQuery = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.IsDeleted == false && f.ProjectDeclarationId == projectDeclarationId).ToListAsync();

            //添加“采购项目立项申报明细表”数据
            //获取所有节点字段信息

            //是否存在统计清单数据
            bool isHasSumProjectList = false;
            //是否含有清单数据
            bool isHaisProjectList = false;

            //附件存储                              
            List<AttachmentModel> listAddAttach = new List<AttachmentModel>();
            //资金来源存储
            List<SourceFundModel> listSourceFund = new List<SourceFundModel>();
            List<WfProjectDeclarationDetail> listDetail = new List<WfProjectDeclarationDetail>();
            string infoId = string.Empty;
            string infoText = string.Empty;
            
            //必填提示文字
            string fieldTipMsg = string.Empty;
            foreach (FieldFillingModel fill in list)
            {
                var objField = listField.Where(f => f.FieldId == fill.FieldId).FirstOrDefault();
                if (objField != null)
                {
                    if (objField.TypeBox == 11 || objField.TypeBox == 13 || objField.TypeBox == 14 || objField.TypeBox == 15 || objField.TypeBox == 20 || objField.TypeBox == 31)
                    {
                        continue;
                    }
                    if (objField.TypeBox == 25 && objField.FieldCode.Equals("ProjectList"))
                    {
                        listFieldCode.Add(objField.FieldCode);
                        isHaisProjectList = true;
                        isHasSumProjectList = true;
                        continue;
                    }
                    if (objField.TypeBox == 25)
                    {
                        listFieldCode.Add(objField.FieldCode);
                        isHaisProjectList = true;
                        continue;
                    }

                    infoId = fill.InfoId == null ? "" : fill.InfoId.ToString().Trim();
                    infoText = fill.InfoText == null ? "" : fill.InfoText.ToString().Trim();

                    //如果为附件存储到list中
                    List<AttachmentModel> listAttach = new List<AttachmentModel>();
                    if (objField.TypeBox == 17 && !string.IsNullOrEmpty(infoId))
                    {
                        listAttach = JsonHelper.JsonToObj<List<AttachmentModel>>(infoId);
                        listAddAttach.AddRange(listAttach);
                    }

                    //如果为资金来源存储到list中
                    if (objField.TypeBox == 6 && !string.IsNullOrEmpty(infoId))
                    {
                        List<SourceFundModel> listFund = JsonHelper.JsonToObj<List<SourceFundModel>>(infoId);
                        listSourceFund.AddRange(listFund);
                        //continue;
                    }

                    if (fill.FieldCode.Equals("AuditUserName"))
                    {
                        infoId = user.UserName;
                        infoText = user.UserName;
                    }

                    if (fill.FieldCode.Equals("AuditDate"))
                    {
                        infoId = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        infoText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    }


                    listDetail.Add(new WfProjectDeclarationDetail()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        ProcessId = o.ProcessId,
                        ProjectDeclarationId = projectDeclarationId,
                        ProjectAuditId = 0,
                        FieldId = fill.FieldId,
                        FieldName = objField.FieldName,
                        FieldCode = fill.FieldCode,
                        FromId = o.ProcessNodeId,
                        ToId = toId,
                        InfoId = infoId,
                        InfoText = infoText,
                        TypeBox = objField.TypeBox,
                        TypeCode = objField.TypeCode,
                        IsCurrent = 1
                    });

                    //如果是提交要验证必填项是否已经填写
                    if(typeButton == 2 && objField.IsRequired == 1 && (objField.TypeBox != 6 && objField.TypeBox != 25 && objField.TypeBox != 26))
                    {
                        //如果是附件要单独处理
                        if(objField.TypeBox == 17)
                        {
                            if(listAttach.Count == 0)
                            {
                                fieldTipMsg += $"附件“{objField.FieldName}”未上传!\n";
                            }
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(infoId))
                            {
                                fieldTipMsg += $"“{objField.FieldName}”不能为空!\n";
                            }
                        }
                    }
                }
            }

            //后台去除接口必填效验，因为会存在显影问题
            //if(typeButton == 2 && !string.IsNullOrEmpty(fieldTipMsg))
            //{
            //    //删除已填报信息
            //    await this.Db.Deleteable<WfProjectDeclaration>().Where(f => f.Id == projectDeclarationId).ExecuteCommandAsync();
            //    r.flag = 0;
            //    r.msg = fieldTipMsg;
            //    return r;
            //}

            #region 资金来源判断
            List<WfSourceFundUse> listFundAdd = new List<WfSourceFundUse>();
            if (listSourceFund.Count > 0)
            {
                string sourceTipMsg = string.Empty;
                //循环
                foreach (SourceFundModel fund in listSourceFund)
                {
                    WfSourceFund obj = await this.Db.Queryable<WfSourceFund>().Where(f => f.Id == fund.SourceFundId).FirstAsync();
                    if (obj != null)
                    {
                        decimal sumUseAmount = await this.Db.Queryable<WfSourceFundUse>().Where(f => f.IsDeleted == false && f.SourceFundIdId == fund.SourceFundId && f.ProjectDeclarationId != projectDeclarationId).SumAsync(f => f.UseAmount.Value);
                        decimal canUseAmount = obj.ProjectAmount.Value - sumUseAmount;
                        if (canUseAmount - fund.CurrentAmount < 0)
                        {
                            sourceTipMsg += $"资金来源“{obj.ProjectName}”,填写金额超出可用金额“{canUseAmount.ToString("0")}”；";
                            continue;
                        }

                        listFundAdd.Add(new WfSourceFundUse()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProjectDeclarationId = projectDeclarationId,
                            SourceFundIdId = fund.SourceFundId,
                            UseAmount = fund.CurrentAmount,
                            Statuz = 2
                        });
                    }

                }
                if (!string.IsNullOrEmpty(sourceTipMsg))
                {
                    r.flag = 0;
                    r.msg = sourceTipMsg;
                    return r;
                }
            }
            #endregion

            //提交时处理
            if (typeButton == 2)
            {
                ProjectAuditProcessModel auditModel = await GetNextAuditInfo(list, 0, o.ProcessId, o.ProcessNodeId, objCurrentNode, strUserIds);
                toId = auditModel.ToId;
                statuz = auditModel.Statuz;
                statuzDesc = auditModel.StatuzDesc;
                processNodeLinkId = auditModel.ProcessNodeLinkId;
                auditUserIds = auditModel.StrAuditUserIds;
                approvalType = auditModel.ApprovalType;
                fromId = o.ProcessNodeId;
                appointProcessNodeId = auditModel.AppointProcessNodeId;

                o.ProjectDeclarationId = projectDeclarationId;

                #region 项目清单金额判断
                //判断是否要验证金额一致
                if (objCurrentNode.IsLockProjectAmount == 1 && isHasSumProjectList)
                {
                    sumMoney = await this.Db.Queryable<WfProjectList>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.FieldCode == "ProjectList" && f.IsDeleted == false).SumAsync(f => f.Sum);
                    if (projectAmount != sumMoney)
                    {
                        r.flag = 0;
                        r.msg = $"填报金额({projectAmount})与清单金额({sumMoney.ToString("0")})不一致,";
                        return r;
                    }
                }
                #endregion

                #region 资金来源金额与项目金额判断
                if (listSourceFund.Count > 0)
                {
                    var sourceFundAmount = listSourceFund.Sum(f => f.CurrentAmount);
                    if (sourceFundAmount != projectAmount)
                    {
                        r.flag = 0;
                        r.msg = $"资金来源金额“{sourceFundAmount.ToString("0")}”与项目金额“{projectAmount}”不一致";
                        return r;
                    }
                }
                #endregion
            }
          
            try
            {
                unitOfWorkManage.BeginTran();

                if (o.ProjectDeclarationId > 0)
                {
                    if(objDeclaration.Statuz % 2 == 1)
                    {
                        isFirstNext = false;
                        isCanIsWithdraw = true;
                    }

                    if(objDeclaration.Statuz != 0)
                    {
                        auditFromId = objDeclaration.FromId;
                        auditToId = objDeclaration.ToId;
                    }

                    objDeclaration.DataContent = o.FieldConfig;
                    objDeclaration.ProjectName = projectName;
                    objDeclaration.ProjectAmount = projectAmount;
                    objDeclaration.NumSum = numSum;
                    objDeclaration.FromId = fromId;
                    objDeclaration.ToId = toId;
                    objDeclaration.ProjectCode = projectCode;
                    objDeclaration.OneClassId = oneClassId;
                    objDeclaration.TwoClassId = twoClassId;

                    if (typeButton == 2)
                    {
                        objDeclaration.Statuz = statuz;
                        objDeclaration.StatuzDesc = statuzDesc;
                        objDeclaration.ProcessNodeLinkId = processNodeLinkId;
                        objDeclaration.AuditUserIds = auditUserIds;
                        objDeclaration.CurrentStatuz = statuz;
                        objDeclaration.IsWithdraw = 1;

                        if (isFirstNext)
                        {
                            objDeclaration.FillingDate = DateTime.Now;
                        }

                        //指定审核人节点
                        if(appointProcessNodeId != 0)
                        {
                            objDeclaration.AppointProcessNodeId = appointProcessNodeId;
                            objDeclaration.AuditedUserIds = "";
                            objDeclaration.AppointedProcessNodeId = 0;
                        }
                    }


                    //判断是否项目金额使用清单金额总和
                    if(isHaisProjectList && objCurrentNode.IsUsetListSum == 1)
                    {
                        sumMoney = await this.Db.Queryable<WfProjectList>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.FieldCode == "ProjectList" && f.IsDeleted == false).SumAsync(f => f.Sum);
                        objDeclaration.ProjectAmount = sumMoney;
                    }

                    await this.Db.Updateable<WfProjectDeclaration>(objDeclaration).ExecuteCommandAsync();
                }

                //删除“采购项目立项申报明细表”数据
                await this.Db.Updateable<WfProjectDeclarationDetail>().SetColumns(f => new WfProjectDeclarationDetail() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == projectDeclarationId).ExecuteCommandAsync();

                if (listDetail.Count > 0)
                {
                    await this.Db.Insertable<WfProjectDeclarationDetail>(listDetail).ExecuteCommandAsync();
                }

                long approvalId = 0;
                if(objProjectAudit != null)
                {
                    approvalId = objProjectAudit.Id;
                    objProjectAudit.ApprovalType = approvalType;
                    objProjectAudit.ProcessStatuz = statuz;
                    objProjectAudit.FormContent = objCurrentNode.NodeConfig;
                    objProjectAudit.DataContent = o.FieldConfig;
                    //更新
                    await this.Db.Updateable<WfProjectAudit>(objProjectAudit).ExecuteCommandAsync();
                }
                else
                {
                    WfProjectAudit projectAudit = new WfProjectAudit();
                    projectAudit.Id = BaseDBConfig.GetYitterId();
                    projectAudit.ProjectDeclarationId = projectDeclarationId;
                    projectAudit.UnitId = user.UnitId;
                    projectAudit.ProcessId = o.ProcessId;
                    projectAudit.ProcessNodeLinkId = processNodeLinkId;
                    projectAudit.ProcessNodeId = o.ProcessNodeId;
                    projectAudit.ApprovalStatuz = 0;
                    projectAudit.IsShow = 1;
                    projectAudit.IsUsable = 1;
                    projectAudit.IsWithdraw = 1;
                    projectAudit.FromId = auditFromId;
                    projectAudit.ToId = auditToId;
                    projectAudit.PreProjectAuditId = 0;
                    projectAudit.ApprovalType = approvalType;
                    projectAudit.ApprovalNo = 1;
                    projectAudit.ProcessStatuz = statuz;
                    projectAudit.FormContent = objCurrentNode.NodeConfig;
                    projectAudit.DataContent = o.FieldConfig;

                    //添加“采购立项审批表wf_ProjectAudit”数据
                    await this.Db.Insertable<WfProjectAudit>(projectAudit).ExecuteCommandAsync();
                    approvalId = projectAudit.Id;
                }

                //更新“采购项目立项申报明细表”立项审批Id
                await this.Db.Updateable<WfProjectDeclarationDetail>().SetColumns(f => new WfProjectDeclarationDetail() { ProjectAuditId = approvalId }).Where(f => f.Id == approvalId).ExecuteCommandAsync();

                //处理附件
                if (listAddAttach.Count > 0)
                {
                    await ProjectAttachmentSupose(listAddAttach, projectDeclarationId, approvalId);
                }
                

                //资金来源数据处理
                if (listFundAdd.Count > 0)
                {
                    //先删除以前数据
                    await this.Db.Updateable<WfSourceFundUse>().SetColumns(f => new WfSourceFundUse() { IsDeleted = true }).Where(f=>f.ProjectDeclarationId == projectDeclarationId).ExecuteCommandAsync();
                    //批量添加
                    await this.Db.Insertable<WfSourceFundUse>(listFundAdd).ExecuteCommandAsync();
                    //计算资金来源
                    await CalculateSourceFund(projectDeclarationId);
                }

                //当为提交时
                if (typeButton == 2)
                {
                    //验证转交下一步生成编码
                    List<FieldFillingModel> listModel = await ProcessNextGenerateProjectCode(objCurrentNode.Id, projectDeclarationId, o.ProcessId);
                    if (listModel.Count > 0)
                    {
                        list.AddRange(listModel);
                        string fieldConfig = list.ToJson();
                        
                        ////判断该填报是否已经生成项目编号,没生成才继续生成
                        //var listProjectDeclarationQuery = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsDeleted == false).ToListAsync();

                       
                        await this.Db.Updateable<WfProjectDeclaration>().SetColumns(f => new WfProjectDeclaration() { DataContent = fieldConfig }).Where(f => f.Id == projectDeclarationId).ExecuteCommandAsync();
                        await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { DataContent = fieldConfig }).Where(f => f.Id == approvalId).ExecuteCommandAsync();

                    }


                    //更新“采购立项审批表wf_ProjectAudit”数据
                    await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsUsable = 2 }).Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsUsable == 1 && f.Id != approvalId).ExecuteCommandAsync();

                    //项目清单数据写入
                    if (isHaisProjectList)
                    {
                        await ProjectListAdd(listFieldCode, objProcess, objCurrentNode, objDeclaration, approvalId);
                    }

                    //设置可撤销
                    if (isCanIsWithdraw)
                    {
                        await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsWithdraw = 1 }).Where(f => f.ProjectDeclarationId == projectDeclarationId && f.ApprovalStatuz == 0 && f.IsDeleted == false && f.FromId == 0).ExecuteCommandAsync();
                    }

                    //资金来源数据写入
                    if (objProcess.IsOpen == 1)
                    {
                        await FundSourWrite(objProcess, objCurrentNode, objDeclaration, 1, statuz);
                    }
           
                }

                //采购项目立项查询表数据写入
                await ProjectDeclarationQueryAdd(listQuery, listDetail, objProcess.Id, objDeclaration.Id);

                unitOfWorkManage.CommitTran();

                r.flag = 1;
                r.msg = tipMsg;
                r.data.footer = projectDeclarationId;
                return r;
            }
            catch (Exception ex)
            {
                unitOfWorkManage.RollbackTran();
                r.msg = $"执行失败，失败信息：{ex.Message}";
                return r;
                throw;
            }
        }


        /// <summary>
        /// 填报详情信息
        /// </summary>
        /// <param name="id">填报Id</param>
        /// <returns></returns>
        public async Task<Result> FillingDetail(long id)
        {
            Result r = new Result();

            if(user.UnitTypeId == 3)
            {
                var obj = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.Id == id && f.UnitId == user.UnitId && f.IsDeleted == false).FirstAsync();
                if(obj == null)
                {
                    r.flag = 0;
                    r.msg = "非法操作";
                    return r;
                }
            }

            r.flag = 1;
            r.msg = "查询成功";
            List<FillingDetailModel> listDetail = await GetDetailInfoById(id);
            r.data.rows = listDetail;
            return r;
        }

        /// <summary>
        /// 审核页面信息
        /// </summary>
        /// <param name="id">填报Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <returns></returns>
        public async Task<Result> FillingAudit(long id,long processNodeId)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";
            WfProjectDeclaration objDeclaration = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.Id == id).FirstAsync();
            if (objDeclaration != null)
            {
                if(user.UnitTypeId == 3)
                {
                    if(objDeclaration.UnitId != user.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "非法操作";
                        return r;
                    }
                }

                List<FillingDetailModel> listDetail = await GetDetailInfoById(id);
                r.data.rows = listDetail;

                string dateContent = string.Empty;
                var objNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == processNodeId).FirstAsync();
                if (objNode != null)
                {
                    var obj = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProjectDeclarationId == id && f.IsDeleted == false && f.ApprovalStatuz == 3).FirstAsync();
                    string formContent = objNode.NodeConfig;
                    if (obj != null)
                    {
                        dateContent = obj.DataContent;
                    }
                    r.data.headers = new { FormContent = formContent, DataContent = dateContent };
                }

                List<WfProjectDeclarationQuery> listShow = new List<WfProjectDeclarationQuery>();
                var listField = await this.Db.Queryable<WfProcessField>().Where(f => f.IsUsePreData == 1 && f.Statuz == 1 && f.IsDeleted == false).ToListAsync();
                if (listField.Count > 0)
                {
                    //获取采购项目立项查询表数据信息
                    listShow = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => listField.Select(m => m.FieldCode).Contains(f.FieldCode) && f.ProjectDeclarationId == id && f.IsDeleted == false).ToListAsync();

                }
                //查询是否有下拉框配置值
                SelectDropDownModel objModel = await GetDropList(objDeclaration.ProcessId, processNodeId, id);
                r.data.other = new { listSelect = objModel.ListSelect, submitButtonName = objNode.SubmitButtonName, stagingButton = objNode.StagingButton, listQuery = listShow,currentTabName = objNode.NodeShowName };
            }
           
            return r;
        }


        /// <summary>
        /// 流程审核暂存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> ProcessSave(FillingModel o)
        {
            //采购项目名称
            string projectName = "";
            //项目金额
            decimal projectAmount = 0;
            //数量
            decimal numSum = 0;

            //是
            int yes = WfYesNo.Yes.ToEnumInt();
            //否
            int no = WfYesNo.No.ToEnumInt();

            long preProjectAuditId = 0;
            int approvalNo = 0;


            //1、验证是否有保存权限
            var objAuditUser = await this.Db.Queryable<WfProjectAuditUser>().Where(f => f.IsDeleted == false && f.ProcessId == o.ProcessId && f.ProcessNodeId == o.ProcessNodeId && f.AuditUserId == user.ID).FirstAsync();
            if (objAuditUser == null)
            {
                return Result<string>.Fail("您无权保存");
            }

            WfProcess objProcess = await this.Db.Queryable<WfProcess>().Where(f => f.Id == o.ProcessId).FirstAsync();
            if (objProcess == null)
            {
                return Result<string>.Fail("非法操作，流程信息不存在!");
            }

            //获取流程节点
            var objCurrentNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == o.ProcessNodeId).FirstAsync();
            if (objCurrentNode == null)
            {
                return Result<string>.Fail("非法操作，节点信息不存在!");
            }

            WfProjectDeclaration objDeclaration = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.Id == o.ProjectDeclarationId).FirstAsync();
            if (objDeclaration == null)
            {
                return Result<string>.Fail("非法操作，填报项目不存在!");
            }

            if (objDeclaration.ToId != o.ProcessNodeId)
            {
                return Result<string>.Fail("您无权保存当前节点");
            }


            List<FieldFillingModel> list = JsonHelper.JsonToObj<List<FieldFillingModel>>(o.FieldConfig);

            projectName = objDeclaration.ProjectName;
            projectAmount = objDeclaration.ProjectAmount;
            numSum = objDeclaration.NumSum.Value;

            if (list.Count > 0)
            {
                FieldFillingModel fillObj = list.Where(f => f.FieldCode == "ProjectName").FirstOrDefault();
                if (fillObj != null)
                {
                    projectName = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                }

                fillObj = list.Where(f => f.FieldCode == "ProjectAmount").FirstOrDefault();
                if (fillObj != null)
                {
                    string strProjectAmount = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                    decimal.TryParse(strProjectAmount, out projectAmount);
                }

                fillObj = list.Where(f => f.FieldCode == "NumSum").FirstOrDefault();
                if (fillObj != null)
                {
                    string strNumSum = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                    decimal.TryParse(strNumSum, out numSum);
                }
            }


            //判断是否有保存数据
            WfProjectAudit objCurrentAudit = new WfProjectAudit();
            objCurrentAudit = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.ApprovalStatuz == 3 && f.IsDeleted == false).FirstAsync();
            if (objCurrentAudit != null)
            {
                objCurrentAudit.FormContent = objCurrentNode.NodeConfig;
                objCurrentAudit.DataContent = o.FieldConfig;
                await this.Db.Updateable<WfProjectAudit>(objCurrentAudit).ExecuteCommandAsync();
            }
            else
            {
                objCurrentAudit = new WfProjectAudit();
                objCurrentAudit.Id = BaseDBConfig.GetYitterId();
                objCurrentAudit.ProjectDeclarationId = o.ProjectDeclarationId;
                objCurrentAudit.UnitId = user.UnitId;
                objCurrentAudit.DepartmentId = 0;
                objCurrentAudit.ProcessId = o.ProcessId;
                objCurrentAudit.FormContent = objCurrentNode.NodeConfig;
                objCurrentAudit.DataContent = o.FieldConfig;
                objCurrentAudit.IsShow = yes;
                objCurrentAudit.IsUsable = yes;
                objCurrentAudit.IsWithdraw = yes;
                objCurrentAudit.ProcessNodeId = o.ProcessNodeId;
                objCurrentAudit.FromId = objDeclaration.FromId;
                objCurrentAudit.ToId = objDeclaration.ToId;
                objCurrentAudit.ApprovalType = objCurrentNode.AduitUserType;
                objCurrentAudit.ApprovalStatuz = 3;
                objCurrentAudit.ProcessNodeLinkId = 0;
                objCurrentAudit.PreProjectAuditId = preProjectAuditId;
                objCurrentAudit.ApprovalNo = approvalNo;
                objCurrentAudit.ProcessStatuz = objDeclaration.Statuz;
                await this.Db.Insertable<WfProjectAudit>(objCurrentAudit).ExecuteCommandAsync();
            }
            
            List<WfProjectDeclarationQuery> listQuery = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.IsDeleted == false && f.ProjectDeclarationId == o.ProjectDeclarationId).ToListAsync();
            List<WfProcessField> listField = await this.Db.Queryable<WfProcessField>().Where(f => f.ProcessNodeId == o.ProcessNodeId && f.Statuz == 1 && f.IsDeleted == false).ToListAsync();
            try
            {

                unitOfWorkManage.BeginTran();

                //添加“采购项目立项申报明细表”数据
                List<WfProjectDeclarationDetail> listDetail = new List<WfProjectDeclarationDetail>();
                string infoId = string.Empty;
                string infoText = string.Empty;
                //附件处理
                List<AttachmentModel> listAddAttach = new List<AttachmentModel>();

                foreach (FieldFillingModel fill in list)
                {
                    var objField = listField.Where(f => f.FieldId == fill.FieldId).FirstOrDefault();
                    if (objField != null)
                    {
                        if (objField.TypeBox == 11 || objField.TypeBox == 13 || objField.TypeBox == 14)
                        {
                            continue;
                        }

                        infoId = fill.InfoId == null ? "" : fill.InfoId.ToString().Trim();
                        infoText = fill.InfoText == null ? "" : fill.InfoText.ToString().Trim();

                        //如果为附件存储到list中
                        if (objField.TypeBox == 17)
                        {
                            List<AttachmentModel> listAttach = JsonHelper.JsonToObj<List<AttachmentModel>>(infoId);
                            listAddAttach.AddRange(listAttach);
                            continue;
                        }

                        listDetail.Add(new WfProjectDeclarationDetail()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = o.ProcessId,
                            ProjectDeclarationId = o.ProjectDeclarationId,
                            ProjectAuditId = objCurrentAudit.Id,
                            FieldId = fill.FieldId,
                            FieldName = objField.FieldName,
                            FieldCode = fill.FieldCode,
                            FromId = o.ProcessNodeId,
                            ToId = 0,
                            InfoId = infoId,
                            InfoText = infoText,
                            TypeBox = objField.TypeBox,
                            TypeCode = objField.TypeCode,
                            IsCurrent = 1
                        });
                    }
                }

                if (listDetail.Count > 0)
                {
                    await this.Db.Insertable<WfProjectDeclarationDetail>(listDetail).ExecuteCommandAsync();
                }

                objDeclaration.ProjectName = projectName;
                objDeclaration.ProjectAmount = projectAmount;
                objDeclaration.NumSum = numSum;
                await base.Update(objDeclaration);

                //处理附件
                if (listAddAttach.Count > 0)
                {
                    await ProjectAttachmentSupose(listAddAttach, o.ProjectDeclarationId, objCurrentAudit.Id);
                }

                //处理采购项目立项查询表数据
                await ProjectDeclarationQueryAdd(listQuery, listDetail, objProcess.Id, o.ProjectDeclarationId);

                unitOfWorkManage.CommitTran();
                return Result<string>.Success("保存成功");
            }
            catch (Exception ex)
            {
                unitOfWorkManage.RollbackTran();
                return Result<string>.Fail("执行失败，失败信息：" + ex.Message);
                throw;
            }
        }


        /// <summary>
        /// 流程审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result> ProcessApproval(FillingModel o)
        {
            Result r = new Result();
            //流程审核状态结果（2：不通过 1：通过）
            int approvalStatuz = 1;
            //备注
            string approvalRemak = "";

            //采购项目名称
            string projectName = "";
            //项目金额
            decimal projectAmount = 0;
            //数量
            decimal numSum = 0;

            //是
            int yes = WfYesNo.Yes.ToEnumInt();
            //否
            int no = WfYesNo.No.ToEnumInt();

            //退回方式（1：退到源头，2：退到上一级 ，3：无， 4：终止）
            int backWay = 1;

            long preProjectAuditId = 0;
            int approvalNo = 0;
            //项目清单编码
            string fieldCode = "";
            int statuz = 0;
            string statuzDesc = "";
            long processNodeLinkId = 0;
            long fromId = 0;
            long toId = 0;
            string strAuditUserIds = "";
            int approvalType = 0;
            string strAuditedUserIds = "";
            WfSourceFundUse fundUse = null;
            string strUserIds = "";
            bool isAppointUser = false;
            long appointProcessNodeId = 0;

            //1、验证是否有审核权限
            var objAuditUser = await this.Db.Queryable<WfProjectAuditUser>().Where(f => f.IsDeleted == false && f.ProcessId == o.ProcessId && f.ProcessNodeId == o.ProcessNodeId && f.AuditUserId == user.ID).FirstAsync();
            if (objAuditUser == null)
            {
                r.flag = 0;
                r.msg = "您无权审核";
                return r;
            }

            WfProcess objProcess = await this.Db.Queryable<WfProcess>().Where(f => f.Id == o.ProcessId).FirstAsync();
            if(objProcess == null)
            {
                r.flag = 0;
                r.msg = "非法操作，流程信息不存在!";
                return r;
            }

            //获取流程节点
            var objCurrentNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == o.ProcessNodeId).FirstAsync();
            if(objCurrentNode == null)
            {
                r.flag = 0;
                r.msg = "非法操作，节点信息不存在!";
                return r;
            }

            WfProjectDeclaration objDeclaration = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.Id == o.ProjectDeclarationId).FirstAsync();
            if(objDeclaration == null)
            {
                r.flag = 0;
                r.msg = "非法操作，填报项目不存在!";
                return r;
            }

            if(objDeclaration.ToId != o.ProcessNodeId)
            {
                r.flag = 0;
                r.msg = "您无权审核当前节点!";
                return r;
            }

            //判断是否为指定人审核，并且当前节点等于设置的指定人审核节点
            if(objDeclaration.AppointProcessNodeId > 0 && objDeclaration.AppointProcessNodeId == o.ProcessNodeId)
            {
                if (!objDeclaration.AuditUserIds.Contains(user.ID.ToString()))
                {
                    r.flag = 0;
                    r.msg = "该节点为指定审核人审核，您不在指定人员中！";
                    return r;
                }
                isAppointUser = true;
            }

            //判断是否需要审批清单（默认一个节点只审批一个项目清单）
            var objAuditProjectList = await this.Db.Queryable<WfProcessField>().Where(f => f.ProcessNodeId == o.ProcessNodeId && f.ControlType == "projectexaminelist" && f.Statuz == 1).FirstAsync();
            if (objAuditProjectList != null)
            {
                fieldCode = objAuditProjectList.FieldCode;
                //判断项目清单是否已经审批
                var objAuditNo = await this.Db.Queryable<WfProjectApprovalNo>().Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.ProjectAuditId == 0 && (f.AuditStatuz == 1 || f.AuditStatuz == 2) && f.AuditUserId == user.ID && f.IsCurrentValid == 1).FirstAsync();
                if(objAuditNo == null)
                {
                    r.flag = 0;
                    r.msg = $"请先审核{objAuditProjectList.ShowName}";
                    return r;
                }
            }

            //先删除暂存数据
           await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsDeleted = true}).Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.ApprovalStatuz == 3).ExecuteCommandAsync();

            List<FieldFillingModel> list = JsonHelper.JsonToObj<List<FieldFillingModel>>(o.FieldConfig);

            projectName = objDeclaration.ProjectName;
            projectAmount = objDeclaration.ProjectAmount;
            numSum = objDeclaration.NumSum.Value;

            //3、数据校验（所有填报字段均需在后端校验）

            //4、业务审批算法
            /*
             * wf_ProjectDeclaration表
                采购项目名称	ProjectName	如果有，则更新
                项目金额	ProjectAmount	如果有，则更新
                项目状态	Statuz	多人审核，最后一个人审核完成时更新，其他必须更新
                项目状态描述	StatuzDesc	多人审核，最后一个人审核完成时更新，其他必须更新
                审核完成时间	ApprovalDate	每次更新为当前时间
                退回次数	NoPassNum	如果是初始节点提交的，且退回的，改字段加1
                数量	NumSum	如果有，则更新
                流程节点关系表Id	ProcessNodeLinkId	必须更新
                From	FromId	必须更新
                To	ToId	必须更新
                审批人Id逗号分隔	AuditUserIds	如果指定审批人，则更新为指定审批人，否则更新为空
              *
              * wf_ProjectAudit表
              * 更新上一个节点：首先判断是否需要更新上一个节点，一般都是需要的，除了多人审批，如果多人审批，则判断是否都审批完成，审批完成后，才更新。
              * IsWithdraw = 0；
              * 插入新的节点时候注意：
              * 上一流程审批id	PreProjectAuditId	查当前项目最后一条，且节点ProcessNodeId= 主表的fromId
                审批人员类型	ApprovalType	当前节点表
                审批序号	ApprovalNo	填报时候为0，每次最大值+1（查当前项目最后一条ApprovalNo + 1）
                项目审批过程状态	ProcessStatuz	

              * 
              * 

             */

            //获取所有已审批数据
            List<WfProjectAudit> listAudited = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.IsDeleted == false && f.IsShow == yes && f.IsUsable == yes).ToListAsync();


            //确定审核通过、不通过字段
            //审核备注信息
            //区县审核是否增加项目金额修改
            bool isEditProjectAmount = false;
            if (list.Count > 0)
            {
                FieldFillingModel fillObj = list.Where(f => f.FieldCode == "ProjectName").FirstOrDefault();
                if (fillObj != null)
                {
                    projectName = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                }

                fillObj = list.Where(f => f.FieldCode == "ProjectAmount").FirstOrDefault();
                if (fillObj != null)
                {
                    isEditProjectAmount = true;
                    string strProjectAmount = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                    decimal.TryParse(strProjectAmount, out projectAmount);
                }

                fillObj = list.Where(f => f.FieldCode == "NumSum").FirstOrDefault();
                if (fillObj != null)
                {
                    string strNumSum = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                    decimal.TryParse(strNumSum, out numSum);
                }

                fillObj = list.Where(f => f.FieldCode.StartsWith("AuditStatuz")).FirstOrDefault();
                if (fillObj != null)
                {
                    string strApprovalStatuz = fillObj.InfoId == null ? "" : fillObj.InfoId.ToString();
                    int.TryParse(strApprovalStatuz, out approvalStatuz);
                }

                fillObj = list.Where(f => f.FieldCode == "AuditRemark").FirstOrDefault();
                if (fillObj != null)
                {
                    approvalRemak = fillObj.InfoText == null ? "" : fillObj.InfoText.ToString();
                }

                //指定审核人
                long appointAuditUserId = 0;
                fillObj = list.Where(f => f.FieldCode == "AppointAuditUser").FirstOrDefault();
                if (fillObj != null)
                {
                    string strAppointAuditUser = fillObj.InfoId == null ? "" : fillObj.InfoId.ToString();
                    long.TryParse(strAppointAuditUser, out appointAuditUserId);
                    if(appointAuditUserId > 0)
                    {
                        strUserIds = appointAuditUserId.ToString();
                    }
                    else
                    {
                        List<long> listUserId = JsonHelper.JsonToObj<List<long>>(strAppointAuditUser);
                        if(listUserId != null)
                        {
                            strUserIds = string.Join(",", listUserId);
                        }
                    }
                }
            }

            //判断是否为完善模式，如果为完善模式，审批状态直接设置为1
            if(objCurrentNode.NodeType == 4)
            {
                approvalStatuz = 1;
            }
            //

            if(approvalStatuz != 1 && approvalStatuz != 2)
            {
                r.flag = 0;
                r.msg = "当前审批状态有误！";
                return r;
            }
            if (objCurrentNode.IsWriteOpinion == 1 && approvalStatuz == 2 && string.IsNullOrEmpty(approvalRemak))
            {
                r.flag = 0;
                r.msg = "不通过必须填写原因！";
                return r;
            }

            
            if (!string.IsNullOrEmpty(o.FieldConfig))
            {
                //加上审核人、审核时间
                o.FieldConfig = o.FieldConfig.Replace("AuditUserName_@#$", user.Name).Replace("AuditDate_@#$", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }

            WfProjectAudit objCurrentAudit = new WfProjectAudit();
            objCurrentAudit.Id = BaseDBConfig.GetYitterId();
            objCurrentAudit.ProjectDeclarationId = o.ProjectDeclarationId;
            objCurrentAudit.UnitId = user.UnitId;
            objCurrentAudit.DepartmentId = 0;
            objCurrentAudit.ProcessId = o.ProcessId;
            objCurrentAudit.FormContent = objCurrentNode.NodeConfig;
            objCurrentAudit.DataContent = o.FieldConfig;
            objCurrentAudit.IsShow = yes;
            objCurrentAudit.IsUsable = yes;
            objCurrentAudit.IsWithdraw = yes;
            objCurrentAudit.ProcessNodeId = o.ProcessNodeId;
            objCurrentAudit.FromId = objDeclaration.FromId;
            objCurrentAudit.ToId = objDeclaration.ToId;
            objCurrentAudit.ApprovalType = objCurrentNode.AduitUserType;

            //获取当前节点的上一个节点信息
            var lastAudit = listAudited.Where(f => f.ProcessNodeId == objDeclaration.FromId).OrderByDescending(f => f.CreateTime).ToList();
            if (lastAudit.Count == 0)
            {
                r.flag = 0;
                r.msg = "您操作的数据出现异常，请关闭当前页面，然后重新进入审核！";
                return r;
            }

            //验证资金来源是否超出可用金额,前提是此节点配置了修改项目金额
            var listSourceFund = await this.Db.Queryable<WfSourceFundUse>().Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.IsDeleted == false && f.Statuz == 2).ToListAsync();
            if (isEditProjectAmount && listSourceFund.Count == 1)
            {
                fundUse = listSourceFund[0];
                if (fundUse != null)
                {
                    WfSourceFund obj = await this.Db.Queryable<WfSourceFund>().Where(f => f.Id == fundUse.SourceFundIdId).FirstAsync();
                    if (obj != null)
                    {
                        decimal sumUseAmount = await this.Db.Queryable<WfSourceFundUse>().Where(f => f.IsDeleted == false && f.SourceFundIdId == fundUse.SourceFundIdId).SumAsync(f => f.UseAmount.Value);
                        sumUseAmount = sumUseAmount - fundUse.UseAmount.Value;
                        decimal canUseAmount = obj.ProjectAmount.Value - sumUseAmount;
                        if (canUseAmount - projectAmount < 0)
                        {
                            r.flag = 0;
                            r.msg = $"资金来源“{obj.ProjectName}”,填写金额超出可用金额“{canUseAmount}”；";
                            return r;
                        }
                        fundUse.UseAmount = projectAmount;
                    }
                }
            }
            preProjectAuditId = lastAudit.FirstOrDefault().Id;
            approvalNo = lastAudit.FirstOrDefault().ApprovalNo + 1;
            bool isNormalProcess = true;

            #region 原有代码注释
            ////判断是否为多人审批
            //if (objCurrentNode.AduitType == 2)
            //{
            //    isNormalProcess = false;
            //    if (!string.IsNullOrEmpty(objDeclaration.AuditUserIds))
            //    {
            //        //获取当前待审核人集合
            //        List<long> listNeedAudit = objDeclaration.AuditUserIds.Split(',').Select(long.Parse).ToList();

            //        List<long> listNeedAudited = new List<long>();
            //        if (!string.IsNullOrEmpty(objDeclaration.AuditedUserIds))
            //        {
            //            listNeedAudited = objDeclaration.AuditedUserIds.Split(',').Select(long.Parse).ToList();
            //        }
            //        listNeedAudited.Add(user.ID);
            //        //var listCurrentAudited = listAudited.Where(f => f.ProcessNodeId == o.ProcessNodeId).ToList();
            //        //listNeedAudit.AddRange(listCurrentAudited.Select(f => f.CreateId));
            //        strAuditedUserIds = string.Join(",", listNeedAudited);
            //        //是否相等
            //        bool isEqual = listNeedAudit.Count == listNeedAudited.Count && listNeedAudit.All(listNeedAudited.Contains);
            //        if (isEqual)
            //        {
            //            isNormalProcess = true;
            //            strAuditUserIds = "";
            //            strAuditedUserIds = "";
            //        }
            //        else
            //        {
            //            strAuditUserIds = objDeclaration.AuditUserIds;
            //        }
            //    }
            //}
            #endregion

            //判断是否为指定人审核
            long appointedProcessNodeId = 0;

            if (isAppointUser)
            {
                isNormalProcess = false;
                if (!string.IsNullOrEmpty(objDeclaration.AuditUserIds))
                {
                    //获取当前待审核人集合
                    List<long> listNeedAudit = objDeclaration.AuditUserIds.Split(',').Select(long.Parse).ToList();

                    List<long> listNeedAudited = new List<long>();
                    if (!string.IsNullOrEmpty(objDeclaration.AuditedUserIds))
                    {
                        listNeedAudited = objDeclaration.AuditedUserIds.Split(',').Select(long.Parse).ToList();
                    }
                    listNeedAudited.Add(user.ID);
                    //var listCurrentAudited = listAudited.Where(f => f.ProcessNodeId == o.ProcessNodeId).ToList();
                    //listNeedAudit.AddRange(listCurrentAudited.Select(f => f.CreateId));
                    strAuditedUserIds = string.Join(",", listNeedAudited);
                    //是否相等
                    bool isEqual = listNeedAudit.Count == listNeedAudited.Count && listNeedAudit.All(listNeedAudited.Contains);
                    if (isEqual)
                    {
                        isNormalProcess = true;
                        strAuditUserIds = "";
                        objDeclaration.AppointProcessNodeId = 0;
                    }
                    else
                    {
                        strAuditUserIds = objDeclaration.AuditUserIds;
                    }

                    appointedProcessNodeId = objCurrentNode.Id;
                }

            }

            if (isNormalProcess)
            {
                //审核审批通过
                if (approvalStatuz == 1)
                {
                    ProjectAuditProcessModel auditModel = await GetNextAuditInfo(null, o.ProjectDeclarationId, o.ProcessId, o.ProcessNodeId, objCurrentNode, strUserIds);
                    statuz = auditModel.Statuz;
                    statuzDesc = auditModel.StatuzDesc;
                    processNodeLinkId = auditModel.ProcessNodeLinkId;
                    fromId = auditModel.FromId;
                    toId = auditModel.ToId;
                    strAuditUserIds = auditModel.StrAuditUserIds;
                    approvalType = auditModel.ApprovalType;
                    appointProcessNodeId = auditModel.AppointProcessNodeId;
                }
                else
                {
                    //审核审批不通过

                    //查询当前状态值
                    WfProcessStatuz processStatuz = await this.Db.Queryable<WfProcessStatuz>().Where(f => f.IsDeleted == false && f.WaitOrBack == 2 && f.ProcessId == o.ProcessId && f.ProcessNodeId == o.ProcessNodeId).FirstAsync();

                    //判断是退回源头还是退回上一步
                    WfProcessReturnSet retutnSet = await this.Db.Queryable<WfProcessReturnSet>().Where(f => f.IsDeleted == false && f.ProcessId == o.ProcessId && f.ProcessNodeId == o.ProcessNodeId).FirstAsync();
                    if (retutnSet != null)
                    {
                        backWay = retutnSet.BackWay;
                    }

                    statuz = processStatuz.Statuz;
                    statuzDesc = processStatuz.StatuzDesc;
                    fromId = o.ProcessNodeId;

                    List<WfProcessNodeLink> listNodeLink = await this.Db.Queryable<WfProcessNodeLink>().Where(f => f.ProcessId == o.ProcessId && f.FromId == o.ProcessNodeId && f.IsDeleted == false).ToListAsync();
                    if (listNodeLink.Count == 1)
                    {
                        processNodeLinkId = listNodeLink[0].Id;
                    }

                    //找出起始节点Id
                    long firstNodeId = 0;
                    var firstNodeObj = listAudited.Where(f => f.ApprovalStatuz == 0).FirstOrDefault();
                    if (firstNodeObj != null)
                    {
                        firstNodeId = firstNodeObj.ProcessNodeId;
                    }
                    //退源头
                    if (retutnSet.BackWay == 1)
                    {
                        //toId为起始节点Id
                        toId = firstNodeId;
                    }
                    //退回上一步
                    else if (retutnSet.BackWay == 2)
                    {
                        var lastAudited = listAudited.Where(f => f.ProcessNodeId == objDeclaration.ToId).OrderByDescending(f => f.CreateTime).ToList();
                        if (lastAudited.Count == 0) //查不到数据，说明初次审核
                        {
                            fromId = lastAudit.FirstOrDefault().FromId;
                            toId = lastAudit.FirstOrDefault().ToId;
                        }
                        else
                        {
                            fromId = lastAudited.FirstOrDefault().ToId;
                            toId = lastAudited.FirstOrDefault().FromId;
                        }
                        //2、从lastAudit取出上一级的节点
                    }
                }
            }

            List<WfProjectDeclarationQuery> listQuery = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.IsDeleted == false && f.ProjectDeclarationId == o.ProjectDeclarationId).ToListAsync();
            List<WfProcessField> listField = await this.Db.Queryable<WfProcessField>().Where(f => f.ProcessNodeId == o.ProcessNodeId && f.Statuz == 1 && f.IsDeleted == false).ToListAsync();
            try
            {
                //添加“采购项目立项申报明细表”数据
                List<WfProjectDeclarationDetail> listDetail = new List<WfProjectDeclarationDetail>();
                string infoId = string.Empty;
                string infoText = string.Empty;
                //附件处理
                List<AttachmentModel> listAddAttach = new List<AttachmentModel>();
                string fieldTipMsg = string.Empty;

                foreach (FieldFillingModel fill in list)
                {
                    var objField = listField.Where(f => f.FieldId == fill.FieldId).FirstOrDefault();
                    if (objField != null)
                    {
                        if (objField.TypeBox == 11 || objField.TypeBox == 13 || objField.TypeBox == 14 || objField.TypeBox == 15 || objField.TypeBox == 20 || objField.TypeBox == 31)
                        {
                            continue;
                        }

                        infoId = fill.InfoId == null ? "" : fill.InfoId.ToString().Trim();
                        infoText = fill.InfoText == null ? "" : fill.InfoText.ToString().Trim();

                        //如果为附件存储到list中
                        List<AttachmentModel> listAttach = new List<AttachmentModel>();
                        if (objField.TypeBox == 17)
                        {
                            listAttach = JsonHelper.JsonToObj<List<AttachmentModel>>(infoId);
                            listAddAttach.AddRange(listAttach);
                            //continue;
                        }

                        if (fill.FieldCode.Equals("AuditUserName"))
                        {
                            infoId = user.UserName;
                            infoText = user.UserName;
                        }

                        if (fill.FieldCode.Equals("AuditDate"))
                        {
                            infoId = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                            infoText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        }

                        listDetail.Add(new WfProjectDeclarationDetail()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = o.ProcessId,
                            ProjectDeclarationId = o.ProjectDeclarationId,
                            ProjectAuditId = objCurrentAudit.Id,
                            FieldId = fill.FieldId,
                            FieldName = objField.FieldName,
                            FieldCode = fill.FieldCode,
                            FromId = o.ProcessNodeId,
                            ToId = toId,
                            InfoId = infoId,
                            InfoText = infoText,
                            TypeBox = objField.TypeBox,
                            TypeCode = objField.TypeCode,
                            IsCurrent = 1
                        });

                        //如果是提交要验证必填项是否已经填写
                        if (objField.IsRequired == 1 && (objField.TypeBox != 6 && objField.TypeBox != 25 && objField.TypeBox != 26))
                        {
                            //如果是附件要单独处理
                            if (objField.TypeBox == 17)
                            {
                                if (listAttach.Count == 0)
                                {
                                    fieldTipMsg += $"附件“{objField.FieldName}”未上传!\n";
                                }
                            }
                            else
                            {
                                if (string.IsNullOrEmpty(infoId))
                                {
                                    fieldTipMsg += $"“{objField.FieldName}”不能为空!\n";
                                }
                            }
                        }
                    }
                }

                //后台先去除效验
                //if (!string.IsNullOrEmpty(fieldTipMsg))
                //{
                //    //复原暂存数据
                //    await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsDeleted = false }).Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.ApprovalStatuz == 3).ExecuteCommandAsync();
                //    r.flag = 0;
                //    r.msg = fieldTipMsg;
                //    return r;
                //}

                unitOfWorkManage.BeginTran();

                if (isNormalProcess)
                {
                    List<long> lastAuditIds = lastAudit.Select(f => f.Id).ToList();
                    await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsWithdraw = 2 }).Where(f => lastAuditIds.Contains(f.Id)).ExecuteCommandAsync();
                }

                objCurrentAudit.ApprovalStatuz = approvalStatuz;
                objCurrentAudit.ProcessNodeLinkId = 0;
                objCurrentAudit.PreProjectAuditId = preProjectAuditId;
                objCurrentAudit.ApprovalNo = approvalNo;
                objCurrentAudit.ProcessStatuz = statuz;
                await this.Db.Insertable<WfProjectAudit>(objCurrentAudit).ExecuteCommandAsync();

                if (listDetail.Count > 0)
                {
                    await this.Db.Insertable<WfProjectDeclarationDetail>(listDetail).ExecuteCommandAsync();
                }

                if (isNormalProcess)
                {
                    objDeclaration.ProjectName = projectName;
                    objDeclaration.ProjectAmount = projectAmount;
                    objDeclaration.NumSum = numSum;
                    objDeclaration.Statuz = statuz;
                    objDeclaration.StatuzDesc = statuzDesc;
                    objDeclaration.ApprovalDate = DateTime.Now;
                    objDeclaration.ProcessNodeLinkId = processNodeLinkId;
                    objDeclaration.FromId = fromId;
                    objDeclaration.ToId = toId;
                    if(appointProcessNodeId != 0)
                    {
                        objDeclaration.AppointProcessNodeId = appointProcessNodeId;
                        objDeclaration.AuditedUserIds = "";
                        objDeclaration.AppointedProcessNodeId = 0;
                    }

                    
                }
                if (appointedProcessNodeId != 0)
                {
                    objDeclaration.AppointedProcessNodeId = appointedProcessNodeId;
                }
                objDeclaration.IsWithdraw = 2;
                objDeclaration.AuditUserIds = strAuditUserIds;
                objDeclaration.AuditedUserIds = strAuditedUserIds;
                await base.Update(objDeclaration);

                //处理附件
                if (listAddAttach.Count > 0)
                {
                    await ProjectAttachmentSupose(listAddAttach, o.ProjectDeclarationId, objCurrentAudit.Id);
                }

     
                #region 审批清单处理
                if(objAuditProjectList != null)
                {
                    ////判断是否存在退回到源头的，但是审批清单审核状态未改变的
                    //var objNo = await this.Db.Queryable<WfProjectApprovalNo>().Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.ProcessNodeId == o.ProcessNodeId && f.FieldCode == fieldCode && f.AuditUserId == user.ID && f.IsCurrentValid == 1).FirstAsync();
                    //if (objNo != null)
                    //{
                    //    await this.Db.Updateable<WfProjectApprovalNo>().SetColumns(f => new WfProjectApprovalNo() { IsCurrentValid = 2 }).Where(f => f.Id == objNo.Id).ExecuteCommandAsync();
                    //    await this.Db.Updateable<WfProjectListReview>().SetColumns(f => new WfProjectListReview() { IsCurrentValid = 2, IsLastTime = 1 }).Where(f => f.ProjectApprovalNoId == objNo.Id).ExecuteCommandAsync();
                    //}

                    var listProjectNo = await this.Db.Queryable<WfProjectApprovalNo>().Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.IsDeleted == false && f.AuditUserId == user.ID && f.ProcessNodeId == o.ProcessNodeId && f.FieldCode == fieldCode).ToListAsync();
                    int auditNo = listProjectNo.Select(p => p.AuditNo).DefaultIfEmpty(0).Max();

                    //更新项目清单审核Id
                    await this.Db.Updateable<WfProjectApprovalNo>().SetColumns(f => new WfProjectApprovalNo() {ProjectAuditId = objCurrentAudit.Id }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == o.ProcessNodeId && f.AuditUserId == user.ID && f.IsCurrentValid == 1 && f.FieldCode == fieldCode).ExecuteCommandAsync();
                    await this.Db.Updateable<WfProjectListReview>().SetColumns(f => new WfProjectListReview() { ProjectAuditId = objCurrentAudit.Id }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == o.ProcessNodeId && f.ReviewUserId == user.ID && f.IsCurrentValid == 1 && f.FieldCode == fieldCode).ExecuteCommandAsync();

                    ////审核不通过时
                    //if (statuz % 2 != 0)
                    //{
                    //    await this.Db.Updateable<WfProjectApprovalNo>().SetColumns(f => new WfProjectApprovalNo() { IsCurrentValid = 2 }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == o.ProcessNodeId && f.AuditUserId == user.ID && f.IsCurrentValid == 1 && f.FieldCode == fieldCode).ExecuteCommandAsync();
                    //    await this.Db.Updateable<WfProjectListReview>().SetColumns(f => new WfProjectListReview() { IsCurrentValid = 2, IsLastTime = 1}).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == o.ProcessNodeId && f.ReviewUserId == user.ID && f.IsCurrentValid == 1 && f.IsLastTime == 2 && f.FieldCode == fieldCode).ExecuteCommandAsync();
                    //}

                    await this.Db.Updateable<WfProjectApprovalNo>().SetColumns(f => new WfProjectApprovalNo() { IsCurrentValid = 2 }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == o.ProcessNodeId && f.AuditUserId == user.ID && f.FieldCode == fieldCode && f.AuditNo == auditNo).ExecuteCommandAsync();
                    await this.Db.Updateable<WfProjectListReview>().SetColumns(f => new WfProjectListReview() { IsCurrentValid = 2, IsLastTime = 1 }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == o.ProcessNodeId && f.ReviewUserId == user.ID && f.IsCurrentValid == 1 && f.IsLastTime == 2 && f.FieldCode == fieldCode && f.AuditNo == auditNo).ExecuteCommandAsync();
                    await this.Db.Updateable<WfProjectListReview>().SetColumns(f => new WfProjectListReview() { IsLastTime = 2 }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == o.ProcessNodeId && f.ReviewUserId == user.ID && f.FieldCode == fieldCode && f.AuditNo != auditNo).ExecuteCommandAsync();

                }

                #endregion

                //处理采购项目立项查询表数据
                await ProjectDeclarationQueryAdd(listQuery, listDetail, objProcess.Id, o.ProjectDeclarationId);

                //资金来源数据写入
                if (objProcess.IsOpen == 1)
                {
                    await FundSourWrite(objProcess, objCurrentNode, objDeclaration, approvalStatuz, statuz);
                }
               
                //如果此节点配置了项目金额，并且设置了资金来源
                if(isEditProjectAmount && listSourceFund.Count == 1 && fundUse != null)
                {
                    await this.Db.Updateable<WfSourceFundUse>(fundUse).ExecuteCommandAsync();
                    //计算资金来源资金
                    await CalculateSourceFund(o.ProjectDeclarationId);
                }
      

                //项目结束资金来源处理
                if (statuz == 1000 && objProcess.IsOpen == 1)
                {
                    //更新“资金来源金额使用表”状态
                    await this.Db.Updateable<WfSourceFundUse>().SetColumns(f => new WfSourceFundUse() { Statuz = 1}).Where(f => f.ProjectDeclarationId == o.ProjectDeclarationId && f.Statuz == 2 && f.IsDeleted == false).ExecuteCommandAsync();
                    //计算资金来源资金
                    await CalculateSourceFund(o.ProjectDeclarationId);
                }

                unitOfWorkManage.CommitTran();
                r.flag = 1;
                r.msg = "审核成功";
                r.Id = approvalStatuz;
                return r;
            }
            catch (Exception ex)
            {
                unitOfWorkManage.RollbackTran();
                r.flag = 0;
                r.msg = $"执行失败，失败信息：{ex.Message}";
                return r;
                throw;
            }
        }

        /// <summary>
        /// 撤销
        /// </summary>
        /// <param name="id">审核表Id</param>
        /// <param name="projectDeclarationId">填报表Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<Result<string>> Revoke(long id,long projectDeclarationId,long processNodeId)
        {
            //是
            int yes = WfYesNo.Yes.ToEnumInt();
            //否
            int no = WfYesNo.No.ToEnumInt();

            int statuz = 0;
            string statuzDesc = "填报中";
            long fromId = 0;
            long toId = 0;
            string auditedUserIds = "";

            var objNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.IsDeleted == false && f.Id == processNodeId).FirstAsync();
            if (objNode == null)
            {
                return Result<string>.Fail("当前节点不存在!");
            }
            if (objNode.IsWithdraw == 2)
            {
                return Result<string>.Fail("当前节点不支持撤销，如需撤销请配置节点为可撤销!");
            }

            if(objNode.IsBegin == 1)
            {
                WfProjectDeclaration objDeclaration = await base.QueryById(projectDeclarationId);
                if (objDeclaration == null)
                {
                    return Result<string>.Fail("填报信息不存在");
                }
                if(objDeclaration.CreateId != user.ID)
                {
                    return Result<string>.Fail("只有创建人才能撤销");
                }

                //获取所有已审批数据
                List<WfProjectAudit> listAudited = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsDeleted == false && f.IsShow == yes && f.IsUsable == yes).ToListAsync();
                var objAudit = listAudited.Where(f => f.ProcessNodeId == processNodeId && f.IsShow == yes && f.IsUsable == yes && f.IsWithdraw == 2).FirstOrDefault();
                if (objAudit != null)
                {
                    return Result<string>.Fail("项目已审核不能撤销");
                }

                fromId = 0;
                toId = objDeclaration.FromId;
                statuz = 0;
                statuzDesc = "填报中";
                auditedUserIds = "";

                //更新申报主表数据
                objDeclaration.FromId = fromId;
                objDeclaration.ToId = toId;
                objDeclaration.Statuz = statuz;
                objDeclaration.StatuzDesc = statuzDesc;
                objDeclaration.AuditedUserIds = auditedUserIds;

                await base.Update(objDeclaration);
            }
            else
            {
                var objProjectAudit = await this.Db.Queryable<WfProjectAudit>().Where(f => f.Id == id && f.CreateId == user.ID).FirstAsync();
                if (objProjectAudit == null)
                {
                    return Result<string>.Fail("非法操作,当前数据信息不存在");
                }
                if (objProjectAudit.IsUsable != 1)
                {
                    return Result<string>.Fail("当前不可撤销");
                }
                WfProjectDeclaration objDeclaration = await base.QueryById(objProjectAudit.ProjectDeclarationId);
                if (objDeclaration == null)
                {
                    return Result<string>.Fail("填报信息不存在");
                }
                auditedUserIds = objDeclaration.AuditedUserIds;

                //获取所有已审批数据
                List<WfProjectAudit> listAudited = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProjectDeclarationId == objProjectAudit.ProjectDeclarationId && f.IsDeleted == false && f.IsShow == yes && f.IsUsable == yes).ToListAsync();

                //查询所有状态
                List<WfProcessStatuz> listStatuz = await this.Db.Queryable<WfProcessStatuz>().Where(f => f.IsDeleted == false && f.ProcessId == objProjectAudit.ProcessId).ToListAsync();

                //获取上一条审批数据
                List<WfProjectAudit> listPreAudit = listAudited.Where(f => f.ProcessNodeId == objDeclaration.FromId).ToList();
                List<long> listUpIds = listPreAudit.Select(f => f.Id).ToList();

                fromId = objProjectAudit.FromId;
                toId = objProjectAudit.ToId;

                var objStatuz = listStatuz.Where(f => f.IsDeleted == false && f.ProcessNodeId == processNodeId && f.WaitOrBack == 1).FirstOrDefault();
                if (objStatuz != null)
                {
                    statuz = objStatuz.Statuz;
                    statuzDesc = objStatuz.StatuzDesc;
                }

                //如果为多人审批
                //var listAllUser = listAudited.Where(f => f.ProcessNodeId == processNodeId).ToList();

                try
                {
                    unitOfWorkManage.BeginTran();

                    if(objDeclaration.AppointedProcessNodeId == processNodeId)
                    {
                        if (string.IsNullOrEmpty(objDeclaration.AuditUserIds))
                        {
                            objDeclaration.AuditUserIds = user.ID.ToString();
                        }
                        else
                        {
                            if (!objDeclaration.AuditUserIds.Contains(user.ID.ToString()))
                            {
                                objDeclaration.AuditUserIds += $",{user.ID.ToString()}";
                            }
                        }

                        string aUserId = objDeclaration.AuditedUserIds.Replace(user.ID.ToString(), "").TrimStart(',').TrimEnd(',');
                        objDeclaration.AuditedUserIds = aUserId;
                        //如果已审批人为空了则，全部退回
                        if (string.IsNullOrEmpty(aUserId))
                        {
                            //更新上一步可撤销
                            await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsWithdraw = yes }).Where(f => listUpIds.Contains(f.Id)).ExecuteCommandAsync();

                            //更新申报主表数据
                            objDeclaration.FromId = fromId;
                            objDeclaration.ToId = toId;
                            objDeclaration.ApprovalDate = DateTime.Now;
                            objDeclaration.Statuz = statuz;
                            objDeclaration.StatuzDesc = statuzDesc;
                            objDeclaration.AuditedUserIds = "";
                            objDeclaration.AppointedProcessNodeId = 0;
                            objDeclaration.AppointProcessNodeId = processNodeId;
                        }
                    }
                    else
                    {
                        //更新上一步可撤销
                        await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsWithdraw = yes }).Where(f => listUpIds.Contains(f.Id)).ExecuteCommandAsync();

                        //更新申报主表数据
                        objDeclaration.FromId = fromId;
                        objDeclaration.ToId = toId;
                        objDeclaration.ApprovalDate = DateTime.Now;
                        objDeclaration.Statuz = statuz;
                        objDeclaration.StatuzDesc = statuzDesc;
                    }
                    await base.Update(objDeclaration);

                    //删除当前审核数据
                    await this.Db.Updateable<WfProjectAudit>().SetColumns(f => new WfProjectAudit() { IsDeleted = true }).Where(f => f.Id == id).ExecuteCommandAsync();

                    //如果存在审核清单数据则删除审核清单数据
                    var objAuditProjectList = await this.Db.Queryable<WfProcessField>().Where(f => f.ProcessNodeId == processNodeId && f.ControlType == "projectexaminelist" && f.Statuz == 1).FirstAsync();
                    if (objAuditProjectList != null)
                    {
                        string fieldCode = objAuditProjectList.FieldCode;
                        WfProjectApprovalNo objNo = await this.Db.Queryable<WfProjectApprovalNo>().Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == processNodeId && f.AuditUserId == user.ID && f.IsCurrentValid == 1 && f.FieldCode == fieldCode).FirstAsync();
                        if (objNo != null)
                        {
                            //await this.Db.Updateable<WfProjectApprovalNo>().SetColumns(f => new WfProjectApprovalNo() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == processNodeId && f.AuditUserId == user.ID && f.IsCurrentValid == 1 && f.FieldCode == fieldCode).ExecuteCommandAsync();
                            //await this.Db.Updateable<WfProjectListReview>().SetColumns(f => new WfProjectListReview() { IsDeleted = true }).Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.ProcessNodeId == processNodeId && f.ReviewUserId == user.ID && f.IsCurrentValid == 1 && f.IsLastTime == 2 && f.FieldCode == fieldCode).ExecuteCommandAsync();
                            await this.Db.Updateable<WfProjectApprovalNo>().SetColumns(f => new WfProjectApprovalNo() { IsDeleted = true }).Where(f => f.Id == objNo.Id).ExecuteCommandAsync();
                            await this.Db.Updateable<WfProjectListReview>().SetColumns(f => new WfProjectListReview() { IsDeleted = true }).Where(f => f.ProjectApprovalNoId == objNo.Id).ExecuteCommandAsync();
                            await this.Db.Updateable<WfProjectListHistory>().SetColumns(f => new WfProjectListHistory() { IsDeleted = true }).Where(f => f.ProjectApprovalNoId == objNo.Id).ExecuteCommandAsync();
                        }
                    }
                    unitOfWorkManage.CommitTran();
                }
                catch (Exception ex)
                {
                    unitOfWorkManage.RollbackTran();
                    return Result<string>.Fail("执行失败，失败信息：" + ex.Message);
                    throw;
                }
            }

            
            return Result<string>.Success("撤销成功");
        }

        #region 私有方法
        /// <summary>
        /// 获取树数据
        /// </summary>
        /// <param name="flatList"></param>
        /// <returns></returns>
        private List<dropdownModel> BuildDropDownModelTree(List<dropdownModel> flatList)
        {
            var lookup = flatList.ToDictionary(n => n.value);
            var rootNodes = new List<dropdownModel>();

            foreach (var node in flatList)
            {
                if (node.pid == 0)
                {
                    // 这是根节点，没有父节点
                    rootNodes.Add(node);
                }
                else if (lookup.ContainsKey(node.pid.ToString()))
                {
                    // 将当前节点添加到其父节点的子节点列表中
                    lookup[node.pid.ToString()].children.Add(node);
                }
                // 如果PID在列表中不存在，则忽略该节点或进行其他处理
            }

            return rootNodes;
        }

        /// <summary>
        /// 根据填报Id获取详情信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private async Task<List<FillingDetailModel>> GetDetailInfoById(long id)
        {
            List<FillingDetailModel> listDetail = new List<FillingDetailModel>();
            var listProcessNode = await this.Db.Queryable<WfProcessNode>().ToArrayAsync();
            var auditList = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProjectDeclarationId == id && f.IsDeleted == false && f.ApprovalStatuz != 3).OrderBy(f => f.Id).ToListAsync();
            foreach (var item in auditList)
            {
                var obj = listProcessNode.Where(f => f.Id == item.ProcessNodeId).FirstOrDefault();
                if (obj != null)
                {
                    listDetail.Add(new FillingDetailModel()
                    {
                        TabId = obj.Id,
                        TabName = obj.NodeShowName,
                        ProcessNodeId = item.ProcessNodeId,
                        FormContent = item.FormContent,
                        DataContent = item.DataContent
                    });
                }
            }
            return listDetail;
        }

        /// <summary>
        /// 根据流程及节点Id，查询下一节点Id及状态值信息
        /// </summary>
        /// <param name="list">用于填报时传值,审批时不需要传值</param>
        /// <param name="projectDeclarationId">填报Id</param>
        /// <param name="processId">流程Id</param>
        /// <param name="fromNodeId">节点Id</param>
        /// <param name="objCurrentNode">当前节点对象</param>
        /// <param name="strUserIds">当该节点有指定审核人时，要把审核人id集合数据传过来，否则传空</param>
        /// <returns></returns>
        private async Task<ProjectAuditProcessModel> GetNextAuditInfo(List<FieldFillingModel> list, long projectDeclarationId, long processId, long fromNodeId, WfProcessNode objCurrentNode, string strUserIds)
        {
            ProjectAuditProcessModel objProcess = new ProjectAuditProcessModel();

            long appointProcessNodeId = 0;
            long toId = 0;
            long processNodeLinkId = 0;
            int statuz = 0;
            string statuzDesc = "";
            string auditUserIds = "";
            int approvalType = 1;

            if (projectDeclarationId != 0)
            {
                var objDeclaration = await base.QueryById(projectDeclarationId);
                if (objDeclaration != null)
                {
                    if (list == null)
                    {
                        List<WfProjectDeclarationQuery> listDetail = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsDeleted == false).ToListAsync();
                        list = listDetail.Select(f => new FieldFillingModel { FieldCode = f.FieldCode, InfoId = f.InfoId, InfoText = f.InfoText }).ToList();
                    }
                }
            }
            if (list != null && list.Count > 0)
            {
                //根据流程Id、节点nodeId获取下一个节点Id
                List<WfProcessNodeLink> listNodeLink = await this.Db.Queryable<WfProcessNodeLink>().Where(f => f.ProcessId == processId && f.FromId == fromNodeId && f.IsDeleted == false).ToListAsync();

                if (listNodeLink.Count == 0)
                {
                    //项目直接结束
                    statuz = 1000;
                    statuzDesc = "审批结束";
                }
                else
                {
                    if (listNodeLink.Count > 1)
                    {
                        //根据条件字段排序把有条件的排列在上方，先判断有条件的项
                        listNodeLink = listNodeLink.OrderByDescending(f => f.SqlCondition).ToList();
                        foreach (WfProcessNodeLink nodeLink in listNodeLink)
                        {
                            if (!string.IsNullOrEmpty(nodeLink.SqlCondition))
                            {
                                //判断是否满足条件
                                //获取条件集合
                                var listCondition = await this.Db.Queryable<WfProcessCondition>().Where(f => f.ProcessNodeLinkId == nodeLink.Id).ToListAsync();
                                foreach (var condition in listCondition)
                                {
                                    var objFill = list.Where(f => f.FieldCode == condition.Code).FirstOrDefault();
                                    if (objFill != null)
                                    {
                                        nodeLink.SqlCondition = nodeLink.SqlCondition.Replace(condition.Code, objFill.InfoId.ToString());
                                    }
                                }
                                var expression = new NCalc.Expression(nodeLink.SqlCondition);
                                bool isSuccess = (bool)expression.Evaluate();
                                if (isSuccess)
                                {
                                    toId = nodeLink.ToId.Value;
                                    processNodeLinkId = nodeLink.Id;
                                    break;
                                }
                            }
                            else
                            {
                                toId = nodeLink.ToId.Value;
                                processNodeLinkId = nodeLink.Id;
                                break;
                            }
                        }
                        if (toId == 0)
                        {
                            //项目直接结束
                            statuz = 1000;
                            statuzDesc = "审批结束";
                        }
                    }
                    else
                    {
                        WfProcessNodeLink obj = listNodeLink[0];
                        //下一个节点只有一个
                        //1.先看该节点是否有条件
                        if (!string.IsNullOrEmpty(obj.SqlCondition))
                        {
                            //获取条件集合
                            var listCondition = await this.Db.Queryable<WfProcessCondition>().Where(f => f.ProcessNodeLinkId == obj.Id).ToListAsync();
                            foreach (var condition in listCondition)
                            {
                                var objFill = list.Where(f => f.FieldCode == condition.Code).FirstOrDefault();
                                if (objFill != null)
                                {
                                    obj.SqlCondition = obj.SqlCondition.Replace(condition.Code, objFill.InfoId.ToString());
                                }
                            }
                            var expression = new NCalc.Expression(obj.SqlCondition);
                            bool isSuccess = (bool)expression.Evaluate();
                            if (isSuccess)
                            {
                                toId = obj.ToId.Value;
                                processNodeLinkId = obj.Id;
                            }
                            else
                            {
                                //项目直接结束
                                statuz = 1000;
                                statuzDesc = "审批结束";
                            }
                        }
                        else
                        {
                            toId = obj.ToId.Value;
                            processNodeLinkId = obj.Id;
                        }
                    }
                }

                //当存在下一个节点时，计算状态值
                if (toId > 0)
                {
                    var objStatuz = await this.Db.Queryable<WfProcessStatuz>().Where(f => f.ProcessId == processId && f.ProcessNodeId == toId && f.IsDeleted == false && f.WaitOrBack == 1).FirstAsync();
                    if (objStatuz != null)
                    {
                        statuz = objStatuz.Statuz;
                        statuzDesc = objStatuz.StatuzDesc;
                    }
                    #region 原处理方法注释
                    ////判断下一个节点是否需要多人审批
                    //WfProcessNode objNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == toId).FirstAsync();
                    ////只有节点配置设置成指定人的时候
                    //if (objNode != null && objNode.AduitType == 2)
                    //{
                    //    var listAuditUser = await this.Db.Queryable<WfProjectAuditUser>().Where(f => f.ProcessId == processId && f.ProcessNodeId == toId && f.IsDeleted == false).ToListAsync();
                    //    if (listAuditUser.Count > 0)
                    //    {
                    //        auditUserIds = string.Join(",", listAuditUser.Select(f => f.AuditUserId));
                    //    }
                    //    if (objNode.DesigneeNum == 1)
                    //    {
                    //        approvalType = 2;
                    //    }
                    //    else if (objNode.DesigneeNum == 2)
                    //    {
                    //        approvalType = 3;
                    //    }
                    //}
                    #endregion
                }
            }

            //判断当前节点是否配置了指定审核人
            if (objCurrentNode.IsOpen == 1)
            {
                //选择多流程
                if (objCurrentNode.NextProcessNodeIds.Contains(","))
                {
                    //根据流程Id、节点nodeId获取下一个节点Id
                    List<WfProcessNodeLink> listNodeLink = await this.Db.Queryable<WfProcessNodeLink>().Where(f => f.ProcessId == processId && f.FromId == fromNodeId && f.IsDeleted == false).ToListAsync();
                    if (listNodeLink.Count > 1)
                    {
                        List<WfProjectDeclarationQuery> listDetail = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsDeleted == false).ToListAsync();
                        List<FieldFillingModel> listModel = listDetail.Select(f => new FieldFillingModel { FieldCode = f.FieldCode, InfoId = f.InfoId, InfoText = f.InfoText }).ToList();

                        //根据条件字段排序把有条件的排列在上方，先判断有条件的项
                        listNodeLink = listNodeLink.OrderByDescending(f => f.SqlCondition).ToList();
                        foreach (WfProcessNodeLink nodeLink in listNodeLink)
                        {
                            if (!string.IsNullOrEmpty(nodeLink.SqlCondition))
                            {
                                //判断是否满足条件
                                //获取条件集合
                                var listCondition = await this.Db.Queryable<WfProcessCondition>().Where(f => f.ProcessNodeLinkId == nodeLink.Id).ToListAsync();
                                foreach (var condition in listCondition)
                                {
                                    var objFill = listModel.Where(f => f.FieldCode == condition.Code).FirstOrDefault();
                                    if (objFill != null)
                                    {
                                        nodeLink.SqlCondition = nodeLink.SqlCondition.Replace(condition.Code, objFill.InfoId.ToString());
                                    }
                                }
                                var expression = new NCalc.Expression(nodeLink.SqlCondition);
                                bool isSuccess = (bool)expression.Evaluate();
                                if (isSuccess)
                                {
                                    appointProcessNodeId = nodeLink.ToId.Value;
                                    break;
                                }
                            }
                            else
                            {
                                appointProcessNodeId = nodeLink.ToId.Value;
                                break;
                            }
                        }
                    }
                    else
                    {
                        WfProcessNodeLink objNodeLink = listNodeLink[0];
                        appointProcessNodeId = objNodeLink.ToId.Value;
                    }
                }
                else
                {
                    long.TryParse(objCurrentNode.NextProcessNodeIds, out appointProcessNodeId);
                }

                //所有人（系统自动指定）
                if (objCurrentNode.AduitUserType == 1)
                {
                    var objNextNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == appointProcessNodeId).FirstAsync();
                    if (objNextNode != null)
                    {
                        if (objNextNode.ProcessLevel == user.UnitTypeId)
                        {
                            List<WfProjectAuditUser> listAuditUser = await this.Db.Queryable<WfProjectAuditUser>().Where(f => f.ProcessId == processId && f.AuditUnitId == user.UnitId && f.ProcessNodeId == appointProcessNodeId && f.IsDeleted == false).ToListAsync();
                            if (listAuditUser.Count > 0)
                            {
                                auditUserIds = string.Join(",", listAuditUser.Select(f => f.AuditUserId).ToList());
                            }
                        }
                        else
                        {
                            //学校---->区级或区级---->市级
                            if ((user.UnitTypeId == 3 && objNextNode.ProcessLevel == 2) || (user.UnitTypeId == 2 && objNextNode.ProcessLevel == 1))
                            {
                                List<WfProjectAuditUser> listAuditUser = await this.Db.Queryable<WfProjectAuditUser>().Where(f => f.ProcessId == processId && f.AuditUnitId == user.UnitPId && f.ProcessNodeId == appointProcessNodeId && f.IsDeleted == false).ToListAsync();
                                if (listAuditUser.Count > 0)
                                {
                                    auditUserIds = string.Join(",", listAuditUser.Select(f => f.AuditUserId).ToList());
                                }
                            }
                        }
                    }
                    approvalType = 1;
                }
                //用户手工指定
                else if (objCurrentNode.AduitUserType == 2)
                {
                    auditUserIds = strUserIds;
                    approvalType = 3;
                }
            }

            objProcess.FromId = fromNodeId;
            objProcess.ToId = toId;
            objProcess.ProcessNodeLinkId = processNodeLinkId;
            objProcess.Statuz = statuz;
            objProcess.StatuzDesc = statuzDesc;
            objProcess.StrAuditUserIds = auditUserIds;
            objProcess.ApprovalType = approvalType;
            objProcess.AppointProcessNodeId = appointProcessNodeId;
            return objProcess;
        }


        /// <summary>
        /// 计算资金来源“项目已使用金额”、“项目审批结束使用金额”
        /// </summary>
        /// <param name="projectDeclarationId">采购立项Id</param>
        /// <returns></returns>
        private async Task CalculateSourceFund(long projectDeclarationId)
        {
            List<WfSourceFund> listALLSourceFund = await this.Db.Queryable<WfSourceFund>().ToListAsync();
            List<WfSourceFundUse> listAllFundUse = await this.Db.Queryable<WfSourceFundUse>().Where(f => f.IsDeleted == false).ToListAsync();
            List<WfSourceFundUse> listFundUse = listAllFundUse.Where(f=>f.ProjectDeclarationId == projectDeclarationId).ToList();
            List<WfSourceFund> listEdit = new List<WfSourceFund>();
            foreach (WfSourceFundUse fund in listFundUse)
            {
                var obj = listALLSourceFund.Where(f => f.Id == fund.SourceFundIdId).FirstOrDefault();
                if (obj != null)
                {
                    //使用金额
                    var useMoney = listAllFundUse.Where(f => f.SourceFundIdId == fund.SourceFundIdId).Sum(f => f.UseAmount);
                    //审批结束已使用金额
                    var actualUseMoney = listAllFundUse.Where(f => f.SourceFundIdId == fund.SourceFundIdId && f.Statuz == 1).Sum(f => f.UseAmount);

                    obj.BudgetDeclareUsed = useMoney;
                    obj.BudgetAuditEndUsed = actualUseMoney;
                    listEdit.Add(obj);
                }
            }
            if (listEdit.Count > 0)
            {
                await this.Db.Updateable<WfSourceFund>(listEdit).ExecuteCommandAsync();
            }
        }


        /// <summary>
        /// 资金来源写入
        /// </summary>
        /// <param name="objProcess">流程对象</param>
        /// <param name="objProcessNode">流程节点对象</param>
        /// <param name="objDeclaration">流程填报对象</param>
        /// <param name="approvalStatuz">状态值</param>
        /// <param name="projectStatuz">项目状态</param>
        /// <returns></returns>
        private async Task FundSourWrite(WfProcess objProcess, WfProcessNode objProcessNode, WfProjectDeclaration objDeclaration,int approvalStatuz,int projectStatuz)
        {

           var list = await this.Db.Queryable<WfFundProcessNodeSet>().Where(f => f.ProcessId == objProcess.Id && f.IsDeleted == false && f.ProcessNodeId == objProcessNode.Id && f.Statuz == approvalStatuz.ToString()).ToListAsync();
            if(list.Count > 0 && projectStatuz == 1000)
            {
                //获取“项目库字段管理表”数据信息
                List<WfFundFieldSet> listFundFieldSet = await this.Db.Queryable<WfFundFieldSet>().ToListAsync();
                //获取当前流程配置的资金来源绑定字段信息
                List<WfPageColumnConfig> listColumnConfig = await this.Db.Queryable<WfPageColumnConfig>().Where(f => f.ModeType == 3 && f.ProcessId == objProcess.Id && f.FieldType != 5 && f.Statuz == 1 && f.ListFieldType == 1 && !string.IsNullOrEmpty(f.FieldCode) && f.FundFieldSetId > 0).ToListAsync();
                //获取当前填报Code
                List<WfProjectDeclarationQuery> listDeclarationQuery = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.ProjectDeclarationId == objDeclaration.Id).ToListAsync();

                //插入对象
                WfSourceFund objSourceFund = new WfSourceFund();

                objSourceFund.ModuleId = objProcess.ModuleId;
                objSourceFund.ProcessId = objProcess.Id;
                objSourceFund.SchoolId = objDeclaration.UnitId;
                objSourceFund.ProjectDeclarationId = objDeclaration.Id;
                objSourceFund.BudgetDeclareUsed = 0;
                objSourceFund.BudgetAuditEndUsed = 0;
                objSourceFund.BudgetAddAmount = 0;
                objSourceFund.BudgetCutAmount = 0;
                objSourceFund.EstimateAmount = 0;
                objSourceFund.EstimateDeclareUsed = 0;
                objSourceFund.EstimateAuditEndUsed = 0;
                objSourceFund.SettlementUsed = 0;
                objSourceFund.BudgetReviewBalance = 0;

                //主数据
                if (objProcess.InputDataType == WfInputDataType.MainTable.ToEnumInt())
                {
                    string schoolName = string.Empty;
                    PUnit u = await this.Db.Queryable<PUnit>().Where(f => f.Id == objDeclaration.UnitId).FirstAsync();
                    if (u != null)
                    {
                        schoolName = u.Name;
                    }
                    foreach (WfPageColumnConfig config in listColumnConfig)
                    {
                        var objFieldSet = listFundFieldSet.Where(f => f.Id == config.FundFieldSetId).FirstOrDefault();
                        if (objFieldSet != null)
                        {
                            string sourceFundInfoText = string.Empty;
                            var objQuery = listDeclarationQuery.Where(f => f.FieldCode == config.FieldCode).FirstOrDefault();
                            if (objQuery != null)
                            {
                                sourceFundInfoText = objQuery.InfoText;
                            }

                            if (string.IsNullOrEmpty(sourceFundInfoText))
                            {
                                switch (config.FieldCode)
                                {
                                    case "PlanYear":
                                        sourceFundInfoText = objDeclaration.PlanYear.ToString();
                                        break;
                                    case "ProjectName":
                                        sourceFundInfoText = objDeclaration.ProjectName;
                                        break;
                                    case "ProjectAmount":
                                        sourceFundInfoText = objDeclaration.ProjectAmount.ToString();
                                        break;
                                    case "SchoolName":
                                        sourceFundInfoText = schoolName;
                                        break;
                                }
                            }

                            //写入资金来源表数据
                            if (!string.IsNullOrEmpty(sourceFundInfoText))
                            {
                                switch (objFieldSet.Code)
                                {
                                    case "ProjectYear":
                                        objSourceFund.ProjectYear = int.Parse(sourceFundInfoText);
                                        break;
                                    case "ProjectAmount":
                                        objSourceFund.ProjectAmount = decimal.Parse(sourceFundInfoText);
                                        break;
                                    case "ProjectNature":
                                        objSourceFund.ProjectNature = sourceFundInfoText;
                                        break;
                                    case "ProjectNumber":
                                        objSourceFund.ProjectNumber = sourceFundInfoText;
                                        break;
                                    case "ProjectName":
                                        objSourceFund.ProjectName = sourceFundInfoText;
                                        break;
                                    case "ProjectCode":
                                        objSourceFund.ProjectCode = sourceFundInfoText;
                                        break;
                                    case "Remark":
                                        objSourceFund.Remark = sourceFundInfoText;
                                        break;
                                    case "CustomField1":
                                        objSourceFund.CustomField1 = sourceFundInfoText;
                                        break;
                                    case "CustomField2":
                                        objSourceFund.CustomField2 = sourceFundInfoText;
                                        break;
                                    case "CustomField3":
                                        objSourceFund.CustomField3 = sourceFundInfoText;
                                        break;
                                    case "CustomField4":
                                        objSourceFund.CustomField4 = sourceFundInfoText;
                                        break;
                                    case "CustomField5":
                                        objSourceFund.CustomField5 = sourceFundInfoText;
                                        break;
                                    case "CustomField6":
                                        objSourceFund.CustomField6 = sourceFundInfoText;
                                        break;
                                    case "CustomField7":
                                        objSourceFund.CustomField7 = decimal.Parse(sourceFundInfoText);
                                        break;
                                    case "CustomField8":
                                        objSourceFund.CustomField8 = decimal.Parse(sourceFundInfoText);
                                        break;
                                    case "CustomField9":
                                        objSourceFund.CustomField9 = decimal.Parse(sourceFundInfoText);
                                        break;
                                    case "CustomField10":
                                        objSourceFund.CustomField10 = decimal.Parse(sourceFundInfoText);
                                        break;
                                }
                            }

                        }
                    }

                    //插入资金表数据
                    await this.Db.Insertable<WfSourceFund>(objSourceFund).ExecuteCommandAsync();
                }
                //项目清单数据
                else if (objProcess.InputDataType == WfInputDataType.DetailTable.ToEnumInt())
                {
                    //获取当前清单数据
                    List<WfProjectList> listProjectList = await this.Db.Queryable<WfProjectList>().Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.FieldCode == objProcess.ProjectListCode && f.Statuz == 1).ToListAsync();
                    //资金来源集合
                    List<WfSourceFund> listFundAdd = new List<WfSourceFund>();

                    foreach (WfProjectList pList in listProjectList)
                    {
                        WfSourceFund objSource = new WfSourceFund();

                        foreach (WfPageColumnConfig config in listColumnConfig)
                        {
                            var objFieldSet = listFundFieldSet.Where(f => f.Id == config.FundFieldSetId).FirstOrDefault();
                            if (objFieldSet != null)
                            {
                                string sourceFundInfoText = string.Empty;
                                var objQuery = listDeclarationQuery.Where(f => f.FieldCode == config.FieldCode).FirstOrDefault();
                                if (objQuery != null)
                                {
                                    sourceFundInfoText = objQuery.InfoText;
                                }

                                if (string.IsNullOrEmpty(sourceFundInfoText))
                                {
                                    switch (config.FieldCode)
                                    {
                                        case "PlanYear":
                                            sourceFundInfoText = objDeclaration.PlanYear.ToString();
                                            break;
                                        case "ProjectName":
                                            sourceFundInfoText = objDeclaration.ProjectName;
                                            break;
                                        case "ProjectAmount":
                                            sourceFundInfoText = objDeclaration.ProjectAmount.ToString();
                                            break;
                                    }
                                }

                                if (string.IsNullOrEmpty(sourceFundInfoText))
                                {
                                    switch (config.FieldCode)
                                    {
                                        case "ClassifyName":
                                            sourceFundInfoText = pList.ClassifyName;
                                            break;
                                        case "Name":
                                            sourceFundInfoText = pList.Name;
                                            break;
                                        case "Brand":
                                            sourceFundInfoText = pList.Brand;
                                            break;
                                        case "Model":
                                            sourceFundInfoText = pList.Model;
                                            break;
                                        case "Explain":
                                            sourceFundInfoText = pList.Explain;
                                            break;
                                        case "Num":
                                            sourceFundInfoText = pList.Num.ToString();
                                            break;
                                        case "UnitName":
                                            sourceFundInfoText = pList.UnitName;
                                            break;
                                        case "Price":
                                            sourceFundInfoText = pList.Price.ToString();
                                            break;
                                        case "Sum":
                                            sourceFundInfoText = pList.Sum.ToString();
                                            break;
                                        case "UsePlace":
                                            sourceFundInfoText = pList.UsePlace;
                                            break;
                                            //扩展数据不加，需要可以加载填报主表中
                                    }
                                }

                                //写入资金来源表数据
                                if (!string.IsNullOrEmpty(sourceFundInfoText))
                                {
                                    switch (objFieldSet.Code)
                                    {
                                        case "ProjectYear":
                                            objSource.ProjectYear = int.Parse(sourceFundInfoText);
                                            break;
                                        case "ProjectAmount":
                                            objSourceFund.ProjectAmount = decimal.Parse(sourceFundInfoText);
                                            break;
                                        case "ProjectNature":
                                            objSource.ProjectNature = sourceFundInfoText;
                                            break;
                                        case "ProjectNumber":
                                            objSource.ProjectNumber = sourceFundInfoText;
                                            break;
                                        case "ProjectName":
                                            objSource.ProjectName = sourceFundInfoText;
                                            break;
                                        case "ProjectCode":
                                            objSource.ProjectCode = sourceFundInfoText;
                                            break;
                                        case "Remark":
                                            objSource.Remark = sourceFundInfoText;
                                            break;
                                        case "CustomField1":
                                            objSource.CustomField1 = sourceFundInfoText;
                                            break;
                                        case "CustomField2":
                                            objSource.CustomField2 = sourceFundInfoText;
                                            break;
                                        case "CustomField3":
                                            objSource.CustomField3 = sourceFundInfoText;
                                            break;
                                        case "CustomField4":
                                            objSource.CustomField4 = sourceFundInfoText;
                                            break;
                                        case "CustomField5":
                                            objSource.CustomField5 = sourceFundInfoText;
                                            break;
                                        case "CustomField6":
                                            objSource.CustomField6 = sourceFundInfoText;
                                            break;
                                        case "CustomField7":
                                            objSource.CustomField7 = decimal.Parse(sourceFundInfoText);
                                            break;
                                        case "CustomField8":
                                            objSource.CustomField8 = decimal.Parse(sourceFundInfoText);
                                            break;
                                        case "CustomField9":
                                            objSource.CustomField9 = decimal.Parse(sourceFundInfoText);
                                            break;
                                        case "CustomField10":
                                            objSource.CustomField10 = decimal.Parse(sourceFundInfoText);
                                            break;
                                    }
                                }
                            }
                        }
                        listFundAdd.Add(objSource);
                    }

                    //插入资金表数据
                    await this.Db.Insertable<WfSourceFund>(listFundAdd).ExecuteCommandAsync();
                }
                //资金分配指定字段
                else if (objProcess.InputDataType == WfInputDataType.FundTable.ToEnumInt())
                {

                }

            }
        }
        
        
        /// <summary>
        /// 历史清单写入
        /// </summary>
        /// <param name="listFieldCode">项目清单编码集合</param>
        /// <param name="objProcess">当前流程对象</param>
        /// <param name="objProcessNode">当前流程节点对象</param>
        /// <param name="objDeclaration">当前填报对象</param>
        /// <param name="projectAuditId">审核表Id</param>
        /// <returns></returns>
        private async Task ProjectListAdd(List<string> listFieldCode, WfProcess objProcess, WfProcessNode objProcessNode, WfProjectDeclaration objDeclaration,long projectAuditId)
        {
            if (listFieldCode.Count == 0)
            {
                string fieldCode = listFieldCode[0];

                //写入清单数据
                var listProjectNo = await this.Db.Queryable<WfProjectApprovalNo>().Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.IsDeleted == false && f.ProcessNodeId == objProcessNode.Id && f.FieldCode == fieldCode).ToListAsync();
                int auditNo = listProjectNo.Select(p => p.AuditNo).DefaultIfEmpty(0).Max();
                auditNo++;

                WfProjectApprovalNo projectNo = new WfProjectApprovalNo();
                projectNo.Id = BaseDBConfig.GetYitterId();
                projectNo.AuditNo = auditNo;
                projectNo.ProjectDeclarationId = objDeclaration.Id;
                projectNo.ProjectAuditId = projectAuditId;
                projectNo.ProcessId = objProcess.Id;
                projectNo.ProcessNodeId = objProcessNode.Id;
                projectNo.FieldCode = fieldCode;
                projectNo.AuditUserId = user.ID;
                projectNo.AuditDate = DateTime.Now;
                projectNo.AuditStatuz = 1;
                projectNo.AuditRemark = "";
                projectNo.IsCurrentValid = 1;
                await this.Db.Insertable<WfProjectApprovalNo>(projectNo).ExecuteCommandAsync();

                List<WfProjectListHistory> listHistory = new List<WfProjectListHistory>();
                var listProjectList = await this.Db.Queryable<WfProjectList>().Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.FieldCode == fieldCode && f.Statuz == 1).ToListAsync();
                foreach (var p in listProjectList)
                {
                    listHistory.Add(new WfProjectListHistory()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        ProjectListId = p.Id,
                        AuditNo = auditNo,
                        ProjectDeclarationId = objDeclaration.Id,
                        ClassifyName = p.ClassifyName,
                        Name = p.Name,
                        Brand = p.Brand,
                        Model = p.Model,
                        Explain = p.Explain,
                        Num = p.Num,
                        UnitName = p.UnitName,
                        Price = p.Price,
                        Sum = p.Sum,
                        UsePlace = p.UsePlace,
                        Remark = p.Remark ?? "",
                        Statuz = 1,
                        CustomField1 = p.CustomField1,
                        CustomField2 = p.CustomField2,
                        CustomField3 = p.CustomField3,
                        CustomField4 = p.CustomField4,
                        CustomField5 = p.CustomField5,
                        CustomField6 = p.CustomField6,
                        CustomField7 = p.CustomField7,
                        CustomField8 = p.CustomField8,
                        CustomField9 = p.CustomField9,
                        CustomField10 = p.CustomField10,
                        CustomField11 = p.CustomField11,
                        CustomField12 = p.CustomField12,
                        IsCurrent = 1,
                        ProcessStatuz = 1,
                        AuditRemark = "",
                        FieldCode = fieldCode,
                        ProjectApprovalNoId = projectNo.Id,
                        ProcessNodeId = objProcessNode.Id
                    });
                }
                if (listHistory.Count > 0)
                {
                    await this.Db.Insertable<WfProjectListHistory>(listHistory).ExecuteCommandAsync();
                }
            }
            else
            {
                List<WfProjectListHistory> listHistory = new List<WfProjectListHistory>();
                var listProjectNo = await this.Db.Queryable<WfProjectApprovalNo>().Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.IsDeleted == false && f.ProcessNodeId == objProcessNode.Id).ToListAsync();
                var listProjectList = await this.Db.Queryable<WfProjectList>().Where(f => f.ProjectDeclarationId == objDeclaration.Id && f.Statuz == 1).ToListAsync();
                foreach (string fieldCode in listFieldCode)
                {
                    listProjectNo = listProjectNo.Where(f => f.FieldCode == fieldCode).ToList();
                    int auditNo = listProjectNo.Select(p => p.AuditNo).DefaultIfEmpty(0).Max();
                    auditNo++;

                    WfProjectApprovalNo projectNo = new WfProjectApprovalNo();
                    projectNo.Id = BaseDBConfig.GetYitterId();
                    projectNo.AuditNo = auditNo;
                    projectNo.ProjectDeclarationId = objDeclaration.Id;
                    projectNo.ProjectAuditId = projectAuditId;
                    projectNo.ProcessId = objProcess.Id;
                    projectNo.ProcessNodeId = objProcessNode.Id;
                    projectNo.FieldCode = fieldCode;
                    projectNo.AuditUserId = user.ID;
                    projectNo.AuditDate = DateTime.Now;
                    projectNo.AuditStatuz = 1;
                    projectNo.AuditRemark = "";
                    projectNo.IsCurrentValid = 1;

                    await this.Db.Insertable<WfProjectApprovalNo>(projectNo).ExecuteCommandAsync();

                    var listFieldCodeList = listProjectList.Where(f => f.FieldCode == fieldCode).ToList();
                    foreach (var p in listFieldCodeList)
                    {
                        listHistory.Add(new WfProjectListHistory()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProjectListId = p.Id,
                            AuditNo = auditNo,
                            ProjectDeclarationId = objDeclaration.Id,
                            ClassifyName = p.ClassifyName,
                            Name = p.Name,
                            Brand = p.Brand,
                            Model = p.Model,
                            Explain = p.Explain,
                            Num = p.Num,
                            UnitName = p.UnitName,
                            Price = p.Price,
                            Sum = p.Sum,
                            UsePlace = p.UsePlace,
                            Remark = p.Remark ?? "",
                            Statuz = 1,
                            CustomField1 = p.CustomField1,
                            CustomField2 = p.CustomField2,
                            CustomField3 = p.CustomField3,
                            CustomField4 = p.CustomField4,
                            CustomField5 = p.CustomField5,
                            CustomField6 = p.CustomField6,
                            CustomField7 = p.CustomField7,
                            CustomField8 = p.CustomField8,
                            CustomField9 = p.CustomField9,
                            CustomField10 = p.CustomField10,
                            CustomField11 = p.CustomField11,
                            CustomField12 = p.CustomField12,
                            IsCurrent = 1,
                            ProcessStatuz = 1,
                            AuditRemark = "",
                            FieldCode = fieldCode,
                            ProjectApprovalNoId = projectNo.Id,
                            ProcessNodeId = objProcessNode.Id
                        });
                    }
                }
                if (listHistory.Count > 0)
                {
                    await this.Db.Insertable<WfProjectListHistory>(listHistory).ExecuteCommandAsync();
                }
            }
        }
        
        /// <summary>
        /// 附件处理
        /// </summary>
        /// <param name="listAddAttach">附件集合</param>
        /// <param name="projectDeclarationId">填报表Id</param>
        /// <param name="projectAuditId">审核表Id</param>
        /// <returns></returns>
        private async Task ProjectAttachmentSupose(List<AttachmentModel> listAddAttach, long projectDeclarationId, long projectAuditId)
        {
            //查询附件上传数据
            List<BAttachmentData> listAttData = await this.Db.Queryable<BAttachmentData>().Where(f => f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.Workflow.ObjToInt() && f.UnitId == user.UnitId).ToListAsync();
            //查询所有附件
            List<BAttachment> listUploadedAttach = await this.Db.Queryable<BAttachment>().Where(f => f.ObjectId == projectAuditId && f.ModuleType == ModuleTypeEnum.Workflow.ObjToInt() && f.IsDeleted == false).ToListAsync();

            //已经存储的Id集合
            List<long> listOldId = listUploadedAttach.Select(f => f.AttachmentDataId).ToList();
            //新传的Id集合
            List<long> listNewId = listAddAttach.Select(f => f.Id).ToList();

            //新增Id
            List<long> listDiffAdd = listNewId.Except(listOldId).ToList();
            //删除Id
            List<long> listDiffDel = listOldId.Except(listNewId).ToList();

            //处理添加附件
            if (listDiffAdd.Count > 0)
            {
                List<BAttachment> listAddFile = new List<BAttachment>();
                foreach (long id in listDiffAdd)
                {
                    var obj = listAttData.Where(f => f.Id == id).FirstOrDefault();
                    if (obj != null)
                    {
                        listAddFile.Add(new BAttachment()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            MainId = projectDeclarationId,
                            ObjectId = projectAuditId,
                            ModuleType = ModuleTypeEnum.Workflow.ObjToInt(),
                            Title = obj.Title,
                            Path = obj.Path,
                            Width = obj.Width,
                            Height = obj.Height,
                            DocType = 0,
                            IsDefault = 0,
                            Remark = "",
                            UserId = user.ID,
                            UnitId = user.UnitId,
                            FileCategory = obj.FileCategory,
                            IsDelete = 0,
                            Ext = obj.Ext,
                            AttachmentDataId = id
                        });
                    }
                }
                if (listAddFile.Count > 0)
                {
                    await this.Db.Insertable<BAttachment>(listAddFile).ExecuteCommandAsync();
                }
            }

            //处理删除附件
            if (listDiffDel.Count > 0)
            {
                await this.Db.Updateable<BAttachment>().SetColumns(f => new BAttachment() { IsDelete = 1 }).Where(f => listDiffDel.Contains(f.AttachmentDataId) && f.CreateId == user.ID && f.ObjectId == projectAuditId).ExecuteCommandAsync();
            }
        }


        /// <summary>
        /// 采购项目立项查询表数据写入
        /// </summary>
        /// <param name="listQuery">采购项目立项查询表数据集合</param>
        /// <param name="listDetail">采购项目立项申报明细表数据集合</param>
        /// <param name="processId">流程Id</param>
        /// <param name="projectDeclarationId">填报表Id</param>
        /// <returns></returns>
        private async Task ProjectDeclarationQueryAdd(List<WfProjectDeclarationQuery> listQuery,List<WfProjectDeclarationDetail> listDetail,long processId,long projectDeclarationId)
        {
            List<long> listRemove = new List<long>() { 11, 13, 14, 17, 20, 25 };
            List<string> listOldCode = listQuery.Select(f => f.FieldCode).ToList();
            List<string> listNewCode = listDetail.Where(f => !listRemove.Contains(f.TypeBox)).Select(f => f.FieldCode).ToList();
            //添加
            List<string> listAddQuery = listNewCode.Except(listOldCode).ToList();
            //更新
            List<string> listEditQuery = listNewCode.Intersect(listOldCode).ToList();
            if (listAddQuery.Count > 0)
            {
                List<WfProjectDeclarationQuery> listQueryAdd = new List<WfProjectDeclarationQuery>();
                foreach (string edit in listAddQuery)
                {
                    var obj = listDetail.Where(f => f.FieldCode == edit).FirstOrDefault();
                    if (obj != null)
                    {
                        listQueryAdd.Add(new WfProjectDeclarationQuery()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = processId,
                            ProjectDeclarationId = projectDeclarationId,
                            FieldName = obj.FieldName,
                            FieldCode = obj.FieldCode,
                            InfoId = obj.InfoId,
                            InfoText = obj.InfoText,
                            TypeBox = obj.TypeBox,
                            TypeCode = obj.TypeCode
                        });
                    }
                }
                if (listQueryAdd.Count > 0)
                {
                    await this.Db.Insertable<WfProjectDeclarationQuery>(listQueryAdd).ExecuteCommandAsync();
                }
            }
            if (listEditQuery.Count > 0)
            {
                List<WfProjectDeclarationQuery> listQueryEdit = new List<WfProjectDeclarationQuery>();
                foreach (string edit in listEditQuery)
                {
                    var obj = listDetail.Where(f => f.FieldCode.Equals(edit)).FirstOrDefault();
                    if (obj != null)
                    {
                        WfProjectDeclarationQuery objQuery = listQuery.Where(f => f.FieldCode.Equals(edit)).FirstOrDefault();
                        if (objQuery != null)
                        {
                            objQuery.InfoId = obj.InfoId;
                            objQuery.InfoText = obj.InfoText;
                            objQuery.TypeBox = obj.TypeBox;
                            objQuery.TypeCode = obj.TypeCode;
                            objQuery.FieldName = obj.FieldName;
                            listQueryEdit.Add(objQuery);
                        }
                    }
                }
                if (listQueryEdit.Count > 0)
                {
                    await this.Db.Updateable<WfProjectDeclarationQuery>(listQueryEdit).ExecuteCommandAsync();
                }
            }
        }

        /// <summary>
        /// 转交下一步生成项目编码
        /// </summary>
        /// <param name="processNodeId">流程节点Id</param>
        /// <param name="projectDeclarationId">填报表Id</param>
        /// <param name="processId">流程Id</param>
        /// <returns></returns>
        private async Task<List<FieldFillingModel>> ProcessNextGenerateProjectCode(long processNodeId,long projectDeclarationId,long processId)
        {
            List<FieldFillingModel> listModel = new List<FieldFillingModel>();
            var list = await this.Db.Queryable<WfProcessField>().Where(f => f.ProcessNodeId == processNodeId && f.IsDeleted == false && f.Statuz == 1 && f.ControlType == "projectnumber" && f.IsDetail == 1).ToListAsync();
            if (list.Count > 0)
            {
                //判断该填报是否已经生成项目编号,没生成才继续生成
                var listQuery = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsDeleted == false).ToListAsync();

                List<WfProjectDeclarationQuery> listQueryAdd = new List<WfProjectDeclarationQuery>();
                List<WfCodeLibrary> listLibrary = new List<WfCodeLibrary>();
                List<WfCodeGenerateSet> listSet = await this.Db.Queryable<WfCodeGenerateSet>().Where(f => f.IsDeleted == false && f.ProcessNodeId == processNodeId).ToListAsync();
                foreach (WfProcessField field in list)
                {
                    var objCode = listQuery.Where(f => f.FieldCode == field.FieldCode).FirstOrDefault();
                    if(objCode == null)
                    {
                        var obj = listSet.Where(f => f.FieldCode == field.FieldCode).FirstOrDefault();
                        if (obj != null)
                        {
                            //自动生成的字段名称或手动输入的字符串
                            string Name = obj.Name;
                            //前缀与日期格式连接符号，无连接符默认空
                            string PreSymbol = obj.PreSymbol;
                            //日期格式format值，例如：年：yyyy、年月：yyyyMM
                            string DateFormatValue = obj.DateFormatValue;
                            //日期与后缀连接符号，无连接符默认空
                            string StufixSymbol = obj.StufixSymbol;
                            //后缀数字自增类型（3：按单位自增  2：按区县自增  5：按平台自增） 默认全平台自增
                            int SuffixNumberType = obj.SuffixNumberType;

                            //根据日期format值自增编号，此值必须在format值中存在，例如：按年自增：yyyy，按月自增：yyyyMM
                            string AutoMode = obj.AutoMode;

                            //末位数字生成位数，默认4位
                            int NumberCount = obj.NumberCount;

                            long schoolId = 0;
                            long countyId = 0;
                            if (SuffixNumberType == 3)
                            {
                                schoolId = user.UnitId;
                            }
                            else if (SuffixNumberType == 2)
                            {
                                countyId = user.UnitPId;
                            }
                            //项目编号前面部分  如：NJJF-20250513-
                            string codeMontage = Name + PreSymbol + DateTime.Now.ToString(DateFormatValue) + StufixSymbol;
                            int beginNum = 0;
                            if (!string.IsNullOrEmpty(AutoMode))
                            {
                                Regex reg = new Regex(@"(" + AutoMode + ")+", RegexOptions.IgnoreCase);
                                MatchCollection regResult;
                                regResult = reg.Matches(DateFormatValue);
                                string yearFromat = "yyyy";
                                if (regResult.Count > 0)
                                {
                                    yearFromat = regResult[0].Value;
                                }
                                string likeCodeMontage = Name + PreSymbol + DateTime.Now.ToString(yearFromat);
                                beginNum = await this.Db.Queryable<WfCodeLibrary>().Where(f => f.CodeGenerateSetId == obj.Id && f.CodeMontage.Contains(likeCodeMontage)).WhereIF(schoolId > 0, f => f.SchoolId == schoolId).WhereIF(countyId > 0, f => f.CountyId == countyId).MaxAsync(f => f.LastValue) ?? 0;
                            }
                            else
                            {
                                beginNum = await this.Db.Queryable<WfCodeLibrary>().Where(f => f.CodeGenerateSetId == obj.Id && f.CodeMontage.Equals(codeMontage)).WhereIF(schoolId > 0, f => f.SchoolId == schoolId).WhereIF(countyId > 0, f => f.CountyId == countyId).MaxAsync(f => f.LastValue) ?? 0;
                            }
                            beginNum++;
                            FieldFillingModel model = new FieldFillingModel();
                            string code = beginNum.ToString().PadLeft(NumberCount, '0');
                            string projectCode = codeMontage + code;
                            model.FieldId = field.FieldId;
                            model.FieldCode = field.FieldCode;
                            model.InfoId = projectCode;
                            model.InfoText = projectCode;
                            listModel.Add(model);

                            listLibrary.Add(new WfCodeLibrary()
                            {
                                Id = BaseDBConfig.GetYitterId(),
                                CodeGenerateSetId = obj.Id,
                                SchoolId = user.UnitId,
                                CountyId = user.UnitPId,
                                CodeMontage = codeMontage,
                                LastValue = beginNum,
                                ProjectCode = projectCode
                            });


                            listQueryAdd.Add(new WfProjectDeclarationQuery()
                            {
                                Id = BaseDBConfig.GetYitterId(),
                                ProcessId = processId,
                                ProjectDeclarationId = projectDeclarationId,
                                FieldName = field.FieldName,
                                FieldCode = field.FieldCode,
                                InfoId = projectCode,
                                InfoText = projectCode,
                                TypeBox = field.TypeBox,
                                TypeCode = field.TypeCode
                            });
                        }
                    }
                    else
                    {
                        FieldFillingModel model = new FieldFillingModel();
                        model.FieldId = field.FieldId;
                        model.FieldCode = field.FieldCode;
                        model.InfoId = objCode.InfoId;
                        model.InfoText = objCode.InfoText;
                        listModel.Add(model);
                    }
                }

                if (listLibrary.Count > 0)
                {
                    await this.Db.Insertable<WfCodeLibrary>(listLibrary).ExecuteCommandAsync();
                }

                if (listQueryAdd.Count > 0)
                {
                    await this.Db.Insertable<WfProjectDeclarationQuery>(listQueryAdd).ExecuteCommandAsync();
                }
            }

            return listModel;
        }


        /// <summary>
        /// 获取下拉框初始数据
        /// </summary>
        /// <param name="processId">流程Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <param name="projectDeclarationId">填报Id</param>
        /// <returns></returns>
        public async Task<SelectDropDownModel> GetDropList(long processId,long processNodeId,long projectDeclarationId)
        {
            SelectDropDownModel obj = new SelectDropDownModel();
            List<dropdownModel> listDefault = new List<dropdownModel>();
            List<dropdownModel> listSelect = new List<dropdownModel>();
            List<WfSourceFundDto> listSourceFund = new List<WfSourceFundDto>();
            List<WfProcessField> listField = await this.Db.Queryable<WfProcessField>().Where(f => f.ProcessNodeId == processNodeId && f.Statuz == 1 && f.IsDeleted == false && f.TypeBox > 1 && f.TypeBox < 100).ToListAsync();
            List<string> listLinkFieldCode = listField.Where(f => !string.IsNullOrEmpty(f.ParentCode)).Select(f => f.ParentCode).ToList();
            //是否联动
            int isLinkAge = 0;
            foreach (WfProcessField field in listField)
            {
                isLinkAge = 0;
                if (listLinkFieldCode.Contains(field.FieldCode))
                {
                    isLinkAge = 1;
                }
                switch (field.TypeBox)
                {
                    case 2:
                        List<BDictionary> listDictionary = await this.Db.Queryable<BDictionary>().Where(f => f.IsDeleted == false && f.TypeCode == field.TypeCode.ToString() && f.Statuz == 1).ToListAsync();
                        List<dropdownModel> listDic = listDictionary.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName, pid = 0, pname = f.TypeName, desp = f.Memo }).ToList();
                        listSelect.Add(new dropdownModel() { label = field.FieldId, value = listDic.ToJson(), IsLinkAge = isLinkAge, FieldCode = processNodeId + "_2_" + field.FieldCode + "_" + field.TypeCode + "_" });
                        break;
                    case 3:
                        List<WfDictionary> listWfDictionary = await this.Db.Queryable<WfDictionary>().Where(f => f.IsDeleted == false && f.ClassifyId.ToString() == field.TypeCode.ToString() && f.Statuz == 1).ToListAsync();
                        List<dropdownModel> listTree = listWfDictionary.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name, pid = f.Depth == 1 ? 0 : f.Pid }).ToList();
                        var treeData = BuildDropDownModelTree(listTree);
                        listSelect.Add(new dropdownModel() { label = field.FieldId, value = treeData.ToJson(), IsLinkAge = isLinkAge, FieldCode = processNodeId + "_3_" + field.FieldCode + "_" + field.TypeCode + "_" });
                        break;
                    //联系人组件
                    case 4:
                        List<SysUserExtension> listUser = await this.Db.Queryable<SysUserExtension>().Where(f => f.IsDeleted == false && f.UnitId == user.UnitId).ToListAsync();
                        List<dropdownModel> listLinkUser = listUser.Select(f => new dropdownModel { value = f.Id.ToString(), label = $"{f.Name}({f.Mobile})" }).ToList();
                        listSelect.Add(new dropdownModel() { label = field.FieldId, value = listLinkUser.ToJson() });
                        //listDefault.Add(new dropdownModel() { label = field.FieldId, value = user.ID.ToString() });
                        break;
                    //近三年
                    case 5:
                        List<dropdownModel> listYear = new List<dropdownModel> {
                                new dropdownModel {label = (DateTime.Now.Year - 1).ToString(),value = (DateTime.Now.Year - 1).ToString() },
                                new dropdownModel {label = (DateTime.Now.Year).ToString(),value = (DateTime.Now.Year).ToString() },
                                new dropdownModel {label = (DateTime.Now.Year + 1).ToString(),value = (DateTime.Now.Year + 1).ToString() }
                            };
                        listSelect.Add(new dropdownModel() { label = field.FieldId, value = listYear.ToJson() });
                        break;
                    //资金来源组件
                    case 6:
                        listSourceFund = await sourceFundManager.GetPageSourceFundList(processId, processNodeId);
                        if (listSourceFund.Count > 0 && projectDeclarationId > 0)
                        {
                            var listFundUse = await this.Db.Queryable<WfSourceFundUse>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsDeleted == false && f.Statuz == 2).ToListAsync();
                            if (listFundUse.Count > 0)
                            {
                                foreach (WfSourceFundUse fundUse in listFundUse)
                                {
                                    WfSourceFundDto fundDto = listSourceFund.Find(f => f.Id == fundUse.SourceFundIdId);
                                    if (fundDto != null)
                                    {
                                        fundDto.CurrentAmount = fundUse.UseAmount.Value;
                                    }
                                }
                            }
                        }
                        break;
                    //当年
                    case 7:
                        List<dropdownModel> listCurrentYear = new List<dropdownModel> {
                                new dropdownModel {label = (DateTime.Now.Year).ToString(),value = (DateTime.Now.Year).ToString() }
                            };
                        listSelect.Add(new dropdownModel() { label = field.FieldId, value = listCurrentYear.ToJson() });
                        break;
                    //下一年
                    case 8:
                        List<dropdownModel> listNextYear = new List<dropdownModel> {
                                 new dropdownModel {label = (DateTime.Now.Year + 1).ToString(),value = (DateTime.Now.Year + 1).ToString() }
                            };
                        listSelect.Add(new dropdownModel() { label = field.FieldId, value = listNextYear.ToJson() });
                        break;
                    //单位名称
                    case 9:
                        List<dropdownModel> listSchoolName = new List<dropdownModel> {
                                 new dropdownModel {label = user.UnitName ,value = user.UnitName }
                            };
                        listSelect.Add(new dropdownModel() { label = field.FieldId, value = listSchoolName.ToJson() });
                        break;
                    //指定审核人组件
                    case 40:
                        long nextProcessNodeId = 0;
                        var objNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == processNodeId).FirstAsync();
                        if(objNode != null && !string.IsNullOrEmpty(objNode.NextProcessNodeIds))
                        {
                            //选择多流程
                            if (objNode.NextProcessNodeIds.Contains(","))
                            {
                                //根据流程Id、节点nodeId获取下一个节点Id
                                List<WfProcessNodeLink> listNodeLink = await this.Db.Queryable<WfProcessNodeLink>().Where(f => f.ProcessId == processId && f.FromId == processNodeId && f.IsDeleted == false).ToListAsync();
                                if (listNodeLink.Count > 1)
                                {
                                    List<WfProjectDeclarationQuery> listDetail = await this.Db.Queryable<WfProjectDeclarationQuery>().Where(f => f.ProjectDeclarationId == projectDeclarationId && f.IsDeleted == false).ToListAsync();
                                    List<FieldFillingModel> list = listDetail.Select(f => new FieldFillingModel { FieldCode = f.FieldCode, InfoId = f.InfoId, InfoText = f.InfoText }).ToList();

                                    //根据条件字段排序把有条件的排列在上方，先判断有条件的项
                                    listNodeLink = listNodeLink.OrderByDescending(f => f.SqlCondition).ToList();
                                    foreach (WfProcessNodeLink nodeLink in listNodeLink)
                                    {
                                        if (!string.IsNullOrEmpty(nodeLink.SqlCondition))
                                        {
                                            //判断是否满足条件
                                            //获取条件集合
                                            var listCondition = await this.Db.Queryable<WfProcessCondition>().Where(f => f.ProcessNodeLinkId == nodeLink.Id).ToListAsync();
                                            foreach (var condition in listCondition)
                                            {
                                                var objFill = list.Where(f => f.FieldCode == condition.Code).FirstOrDefault();
                                                if (objFill != null)
                                                {
                                                    nodeLink.SqlCondition = nodeLink.SqlCondition.Replace(condition.Code,objFill.InfoId.ToString());
                                                }
                                            }
                                            var expression = new NCalc.Expression(nodeLink.SqlCondition);
                                            bool isSuccess = (bool)expression.Evaluate();
                                            if (isSuccess)
                                            {
                                                nextProcessNodeId = nodeLink.ToId.Value;
                                                break;
                                            }
                                        }
                                        else
                                        {
                                            nextProcessNodeId = nodeLink.ToId.Value;
                                            break;
                                        }
                                    }
                                }
                                else
                                {
                                    WfProcessNodeLink objNodeLink = listNodeLink[0];
                                    nextProcessNodeId = objNodeLink.ToId.Value;
                                }
                            }
                            //单流程
                            else
                            {
                                long.TryParse(objNode.NextProcessNodeIds, out nextProcessNodeId);
                            }
                            List<dropdownModel> listAuditUser = new List<dropdownModel>();
                            if (nextProcessNodeId > 0)
                            {
                                var objNextNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == nextProcessNodeId).FirstAsync();
                                if (objNextNode != null)
                                {
                                    if(objNextNode.ProcessLevel == user.UnitTypeId)
                                    {
                                        listAuditUser = await this.Db.Queryable<WfProjectAuditUser>()
                                                                .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                                                .Where((PAU, SE) => SE.UnitId == user.UnitId && PAU.ProcessId == processId && PAU.IsDeleted == false && PAU.ProcessNodeId == nextProcessNodeId && SE.IsDeleted == false)
                                                                .Select((PAU, SE) => new dropdownModel()
                                                                {
                                                                    label = SE.Name,
                                                                    value = SE.Id.ToString()
                                                                }).ToListAsync();
                                    }
                                    else
                                    {
                                        long unitId = 0;
                                        int unitType = 0;
                                        long unitPid = 0;
                                        var objDeclaration = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.Id == projectDeclarationId).FirstAsync();
                                        if (objDeclaration != null)
                                        {
                                            unitId = objDeclaration.UnitId;
                                            var unit = await this.Db.Queryable<PUnit>().Where(f => f.Statuz == 1 && f.IsDeleted == false && f.Id == unitId).FirstAsync();
                                            if (unit != null)
                                            {
                                                unitType = unit.UnitType;
                                                unitPid = unit.PId;
                                            }
                                        }

                                        //学校---->区级或区级---->市级
                                        if ((user.UnitTypeId == 3 && objNextNode.ProcessLevel == 2) || (user.UnitTypeId == 2 && objNextNode.ProcessLevel == 1))
                                        {
                                            listAuditUser = await this.Db.Queryable<WfProjectAuditUser>()
                                                               .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                                               .Where((PAU, SE) => SE.UnitId == user.UnitPId && PAU.ProcessId == processId && PAU.IsDeleted == false && PAU.ProcessNodeId == nextProcessNodeId && SE.IsDeleted == false)
                                                               .Select((PAU, SE) => new dropdownModel()
                                                               {
                                                                   label = SE.Name,
                                                                   value = SE.Id.ToString()
                                                               }).ToListAsync();
                                        }
                                        //学校---->市级
                                        else if (user.UnitTypeId == 3 && objNextNode.ProcessLevel == 1)
                                        {
                                            long unitCityId = 0;
                                            var objCounty = await this.Db.Queryable<PUnit>().Where(f => f.Id == user.UnitPId && f.IsDeleted == false && f.Statuz == 1).FirstAsync();
                                            if (objCounty != null)
                                            {
                                                unitCityId = objCounty.PId;
                                            }
                                            listAuditUser = await this.Db.Queryable<WfProjectAuditUser>()
                                                               .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                                               .Where((PAU, SE) => SE.UnitId == unitCityId && PAU.ProcessId == processId && PAU.IsDeleted == false && PAU.ProcessNodeId == nextProcessNodeId && SE.IsDeleted == false)
                                                               .Select((PAU, SE) => new dropdownModel()
                                                               {
                                                                   label = SE.Name,
                                                                   value = SE.Id.ToString()
                                                               }).ToListAsync();
                                        }
                                        //区县---->学校 (默认必须是学校创建填报)
                                        else if(user.UnitTypeId == 2 && objNextNode.ProcessLevel == 3)
                                        {
                                            listAuditUser = await this.Db.Queryable<WfProjectAuditUser>()
                                                               .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                                               .Where((PAU, SE) => SE.UnitId == unitId && PAU.ProcessId == processId && PAU.IsDeleted == false && PAU.ProcessNodeId == nextProcessNodeId && SE.IsDeleted == false)
                                                               .Select((PAU, SE) => new dropdownModel()
                                                               {
                                                                   label = SE.Name,
                                                                   value = SE.Id.ToString()
                                                               }).ToListAsync();
                                        }
                                        //市级---->区县
                                        else if(user.UnitTypeId == 1 && objNextNode.ProcessLevel == 2)
                                        {
                                            //判断是否为区县创建的填报
                                            if(unitType == 2)
                                            {
                                                listAuditUser = await this.Db.Queryable<WfProjectAuditUser>()
                                                               .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                                               .Where((PAU, SE) => SE.UnitId == unitId && PAU.ProcessId == processId && PAU.IsDeleted == false && PAU.ProcessNodeId == nextProcessNodeId && SE.IsDeleted == false)
                                                               .Select((PAU, SE) => new dropdownModel()
                                                               {
                                                                   label = SE.Name,
                                                                   value = SE.Id.ToString()
                                                               }).ToListAsync();
                                            }
                                            else
                                            {
                                                listAuditUser = await this.Db.Queryable<WfProjectAuditUser>()
                                                               .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                                               .Where((PAU, SE) => SE.UnitId == unitPid && PAU.ProcessId == processId && PAU.IsDeleted == false && PAU.ProcessNodeId == nextProcessNodeId && SE.IsDeleted == false)
                                                               .Select((PAU, SE) => new dropdownModel()
                                                               {
                                                                   label = SE.Name,
                                                                   value = SE.Id.ToString()
                                                               }).ToListAsync();
                                            }
                                        }
                                        //市级---->学校（默认必须是学校创建项目）
                                        else if(user.UnitTypeId == 1 && objNextNode.ProcessLevel == 3)
                                        {
                                            listAuditUser = await this.Db.Queryable<WfProjectAuditUser>()
                                                               .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                                               .Where((PAU, SE) => SE.UnitId == unitId && PAU.ProcessId == processId && PAU.IsDeleted == false && PAU.ProcessNodeId == nextProcessNodeId && SE.IsDeleted == false)
                                                               .Select((PAU, SE) => new dropdownModel()
                                                               {
                                                                   label = SE.Name,
                                                                   value = SE.Id.ToString()
                                                               }).ToListAsync();
                                        }
                                    }
                                }
                            }
                            listSelect.Add(new dropdownModel() { label = field.FieldId, value = listAuditUser.ToJson() });
                        }
                        break;
                }
            }
            obj.ListSelect = listSelect; 
            obj.ListSourceFund = listSourceFund;
            //obj.ListDefault = listDefault;
            return obj;
        }


        /// <summary>
        /// 获取发送短信人手机号码集合
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        public async Task<string> GetUserMobileInfo(WfProjectDeclaration obj,long processNodeId)
        {
            string userPhone = string.Empty;
            //是否为指定人审核
            if (obj.AppointProcessNodeId > 0 && obj.AppointProcessNodeId == processNodeId)
            {
                if (string.IsNullOrEmpty(obj.AuditedUserIds))
                {
                    List<long> listUserId = obj.AuditUserIds.Split(',').Select(long.Parse).ToList();
                    List<string> listPhone = await this.Db.Queryable<SysUserExtension>().Where(f => listUserId.Contains(f.Id) && !string.IsNullOrEmpty(f.Mobile)).Select(f => f.Mobile).ToListAsync();
                    userPhone = string.Join(",", listPhone);
                }
            }
            else
            {
                var objNextNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.Id == obj.ToId).FirstAsync();
                if (objNextNode.ProcessLevel == user.UnitTypeId)
                {
                    List<string> listPhone = await this.Db.Queryable<WfProjectAuditUser>()
                                            .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                            .Where((PAU, SE) => SE.UnitId == user.UnitId && PAU.ProcessId == obj.ProcessId && PAU.IsDeleted == false && PAU.ProcessNodeId == obj.ToId && SE.IsDeleted == false && !string.IsNullOrEmpty(SE.Mobile))
                                            .Select((PAU, Se) => Se.Mobile).ToListAsync();
                    userPhone = string.Join(",", listPhone);
                }
                else
                {
                    long unitId = 0;
                    int unitType = 0;
                    long unitPid = 0;
                    unitId = obj.UnitId;
                    var unit = await this.Db.Queryable<PUnit>().Where(f => f.Statuz == 1 && f.IsDeleted == false && f.Id == unitId).FirstAsync();
                    if (unit != null)
                    {
                        unitType = unit.UnitType;
                        unitPid = unit.PId;
                    }
                    //学校---->区级或区级---->市级
                    if ((user.UnitTypeId == 3 && objNextNode.ProcessLevel == 2) || (user.UnitTypeId == 2 && objNextNode.ProcessLevel == 1))
                    {
                        List<string> listPhone = await this.Db.Queryable<WfProjectAuditUser>()
                                           .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                           .Where((PAU, SE) => SE.UnitId == user.UnitPId && PAU.ProcessId == obj.ProcessId && PAU.IsDeleted == false && PAU.ProcessNodeId == obj.ToId && SE.IsDeleted == false && !string.IsNullOrEmpty(SE.Mobile))
                                           .Select((PAU, Se) => Se.Mobile).ToListAsync();
                        userPhone = string.Join(",", listPhone);
                    }
                    //学校---->市级
                    else if (user.UnitTypeId == 3 && objNextNode.ProcessLevel == 1)
                    {
                        long unitCityId = 0;
                        var objCounty = await this.Db.Queryable<PUnit>().Where(f => f.Id == user.UnitPId && f.IsDeleted == false && f.Statuz == 1).FirstAsync();
                        if (objCounty != null)
                        {
                            unitCityId = objCounty.PId;
                        }
                        List<string> listPhone = await this.Db.Queryable<WfProjectAuditUser>()
                                           .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                           .Where((PAU, SE) => SE.UnitId == unitCityId && PAU.ProcessId == obj.ProcessId && PAU.IsDeleted == false && PAU.ProcessNodeId == obj.ToId && SE.IsDeleted == false && !string.IsNullOrEmpty(SE.Mobile))
                                          .Select((PAU, Se) => Se.Mobile).ToListAsync();
                        userPhone = string.Join(",", listPhone);
                    }
                    //区县---->学校 (默认必须是学校创建填报)
                    else if (user.UnitTypeId == 2 && objNextNode.ProcessLevel == 3)
                    {
                        List<string> listPhone = await this.Db.Queryable<WfProjectAuditUser>()
                                           .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                           .Where((PAU, SE) => SE.UnitId == unitId && PAU.ProcessId == obj.ProcessId && PAU.IsDeleted == false && PAU.ProcessNodeId == obj.ToId && SE.IsDeleted == false && !string.IsNullOrEmpty(SE.Mobile))
                                           .Select((PAU, Se) => Se.Mobile).ToListAsync();
                        userPhone = string.Join(",", listPhone);
                    }
                    //市级---->区县
                    else if (user.UnitTypeId == 1 && objNextNode.ProcessLevel == 2)
                    {
                        //判断是否为区县创建的填报
                        if (unitType == 2)
                        {
                            List<string> listPhone = await this.Db.Queryable<WfProjectAuditUser>()
                                           .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                           .Where((PAU, SE) => SE.UnitId == unitId && PAU.ProcessId == obj.ProcessId && PAU.IsDeleted == false && PAU.ProcessNodeId == obj.ToId && SE.IsDeleted == false && !string.IsNullOrEmpty(SE.Mobile))
                                           .Select((PAU, Se) => Se.Mobile).ToListAsync();
                            userPhone = string.Join(",", listPhone);
                        }
                        else
                        {
                            List<string> listPhone = await this.Db.Queryable<WfProjectAuditUser>()
                                           .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                           .Where((PAU, SE) => SE.UnitId == unitPid && PAU.ProcessId == obj.ProcessId && PAU.IsDeleted == false && PAU.ProcessNodeId == obj.ToId && SE.IsDeleted == false && !string.IsNullOrEmpty(SE.Mobile))
                                           .Select((PAU, Se) => Se.Mobile).ToListAsync();
                            userPhone = string.Join(",", listPhone);
                        }
                    }
                    //市级---->学校（默认必须是学校创建项目）
                    else if (user.UnitTypeId == 1 && objNextNode.ProcessLevel == 3)
                    {
                        List<string> listPhone = await this.Db.Queryable<WfProjectAuditUser>()
                                           .InnerJoin<SysUserExtension>((PAU, SE) => PAU.AuditUserId == SE.Id)
                                           .Where((PAU, SE) => SE.UnitId == unitId && PAU.ProcessId == obj.ProcessId && PAU.IsDeleted == false && PAU.ProcessNodeId == obj.ToId && SE.IsDeleted == false && !string.IsNullOrEmpty(SE.Mobile))
                                           .Select((PAU, Se) => Se.Mobile).ToListAsync();
                        userPhone = string.Join(",", listPhone);
                    }

                }
            }
            return userPhone;
        }

        #endregion
    }
}

