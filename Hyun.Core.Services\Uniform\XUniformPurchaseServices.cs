﻿using FluentValidation;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.IServices.Uniform;
using Hyun.Core.Model.Validator;
using Hyun.Core.Repository.UnitOfWorks;
using NetTaste;
namespace Hyun.Core.Services
{

    ///<summary>
    ///XUniformPurchase方法
    ///</summary>
    public class XUniformPurchaseServices : BaseServices<XUniformPurchase>, IXUniformPurchaseServices
    {
        private readonly IBAttachmentServices attachmentManager;
        private readonly IUser user;
        private readonly IUnitOfWorkManage unitOfWorkManage;
        public XUniformPurchaseServices(IUser _user, IBAttachmentServices _attachmentManager, IUnitOfWorkManage _unitOfWorkManage)
        {
            user = _user;
            attachmentManager = _attachmentManager;
            unitOfWorkManage = _unitOfWorkManage;
        }


        #region 查询数据
        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        public async Task<XUniformPurchase> GetById(long id)
        {
            return await base.QueryById(id);
        }

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        public async Task<List<XUniformPurchase>> Find(Expression<Func<XUniformPurchase, bool>> expression)
        {
            return await base.Query(expression);
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">XUniformPurchaseParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseDto>> GetPaged(XUniformPurchaseParam param)
        {
            PageModel<XUniformPurchaseDto> pg = new PageModel<XUniformPurchaseDto>();
            var expression = ListFilter(param);
            string orderByFields = " CreateTime DESC ";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> total = 0;
            var listExpression = this.Db.Queryable<XUniformBuy>()
                .LeftJoin<XUniformPurchase>((buy, purchase) => buy.Id == purchase.UniformBuyId)
                .LeftJoin<PUnit>((buy, purchase, school) => purchase.SchoolId == school.Id)
                .LeftJoin<PUnit>((buy, purchase, school,supplier) => purchase.SupplierId == supplier.Id)
                .LeftJoinIF<PUnit>(param.UnitType == UnitTypes.City.ObjToInt(),
                    (buy, purchase, school, supplier, county) => school.PId == county.Id)
                 .Where(buy => buy.PurchaseStatuz == 100)
                .WhereIF(param.SchoolId > 0, (buy, purchase, school, supplier, county) => buy.SchoolId == param.SchoolId)
                .WhereIF(param.CountyId > 0 && param.UnitType != UnitTypes.School.ObjToInt(),
                    (buy, purchase, school, supplier, county) => school.PId == param.CountyId)
                .WhereIF(param.CityId > 0 && param.UnitType == UnitTypes.City.ObjToInt(),
                    (buy, purchase, school, supplier, county) => county.PId == param.CityId)
                .WhereIF(param.PurchaseYear != -10000, (buy, purchase, school, supplier, county) => purchase.PurchaseYear == param.PurchaseYear)
                .WhereIF(param.IsRelatedUniform != -10000,
                    (buy, purchase, school, supplier, county) => purchase.IsRelatedUniform == param.IsRelatedUniform)
                .WhereIF(!string.IsNullOrEmpty(param.Ids),
                    (buy, purchase, school, supplier, county) => param.Ids.Contains(purchase.Id.ToString()))
                .WhereIF(param.CompanyId > 0 , (buy, purchase, school, supplier, county) => purchase.SupplierId == param.CompanyId)
                .WhereIF(param.Name != null && param.Name.Length > 0,
                    (buy, purchase, school, supplier, county) => purchase.PurchaseNo.Contains(param.Name) || buy.PurchaseNo.Contains(param.Name));
            ISugarQueryable<XUniformPurchaseDto> listSelectExpression = null;
            if (param.UnitType == UnitTypes.School.ObjToInt())
            {
                listSelectExpression = listExpression.Select((buy, purchase, school, supplier, county) => new XUniformPurchaseDto()
                {
                    Id = purchase.Id > 0 ? purchase.Id : 0,
                    IsDeleted = buy.IsDeleted,
                    CreateTime = purchase.CreateTime,
                    ModifyTime = purchase.ModifyTime,
                    CreateBy = purchase.CreateBy,
                    ModifyBy = purchase.ModifyBy,
                    PurchaseYear = purchase.PurchaseYear > 0 ? purchase.PurchaseYear : 0,
                    PurchaseNo = purchase.PurchaseNo,
                    SchoolId = buy.SchoolId,
                    IsContractRenewal = purchase.IsContractRenewal > 0 ? purchase.IsContractRenewal : 2,
                    ContractStartDate = purchase.ContractStartDate,
                    ContractEndDate = purchase.ContractEndDate,
                    OrganizationFormName = purchase.OrganizationFormName,
                    OrganizationForm = purchase.OrganizationForm,
                    SupplierId = purchase.SupplierId,
                    SupplierName = supplier.Name,
                    ContractMainBodyId = purchase.ContractMainBodyId,
                    ContractMainBody = purchase.ContractMainBody,
                    ContractSignDate = purchase.ContractSignDate,
                    PayMethodId = purchase.PayMethodId,
                    PayMethod = purchase.PayMethod,
                    SupplierLocation = purchase.SupplierLocation,
                    GoodsDeadline = purchase.GoodsDeadline,
                    WarrantyMonth = purchase.WarrantyMonth,
                    Memo = purchase.Memo,
                    DeliveryDate = purchase.DeliveryDate,
                    AcceptanceDate = purchase.AcceptanceDate,
                    SupplierSendTestDate = purchase.SupplierSendTestDate,
                    SchoolSendTestDate = purchase.SchoolSendTestDate,
                    UniformBuyId = buy.Id,
                    UniformBuyNo = buy.PurchaseNo,
                    ContractPersonNum = purchase.ContractPersonNum,
                    ContractAmount = purchase.ContractAmount,
                    FilingStatuz = purchase.FilingStatuz > 0 ? purchase.FilingStatuz : 0,
                    IsFiling = purchase.IsFiling,
                    IsRelatedUniform = purchase.IsRelatedUniform > 0 ? purchase.IsRelatedUniform : 0,
                    IsCountyManager = 1,
                });
            }
            else if (param.UnitType == UnitTypes.Couty.ObjToInt() || param.UnitType == UnitTypes.Parent.ObjToInt())
            {
                listSelectExpression = listExpression.Select((buy, purchase, school, supplier, county) => new XUniformPurchaseDto()
                {
                    Id = purchase.Id > 0 ? purchase.Id : 0,
                    IsDeleted = buy.IsDeleted,
                    CreateTime = purchase.CreateTime,
                    ModifyTime = purchase.ModifyTime,
                    CreateBy = purchase.CreateBy,
                    ModifyBy = purchase.ModifyBy,
                    PurchaseYear = purchase.PurchaseYear > 0 ? purchase.PurchaseYear : 0,
                    PurchaseNo = purchase.PurchaseNo,
                    SchoolId = buy.SchoolId,
                    SchoolName = school.Name,
                    IsContractRenewal = purchase.IsContractRenewal > 0 ? purchase.IsContractRenewal : 0,
                    ContractStartDate = purchase.ContractStartDate,
                    ContractEndDate = purchase.ContractEndDate,
                    SupplierId = purchase.SupplierId,
                    SupplierName = supplier.Name,
                    OrganizationFormName = purchase.OrganizationFormName,
                    OrganizationForm = purchase.OrganizationForm,
                    ContractMainBodyId = purchase.ContractMainBodyId,
                    ContractMainBody = purchase.ContractMainBody,
                    ContractSignDate = purchase.ContractSignDate,
                    SubscriptionStatuz = purchase.SubscriptionStatuz > 0 ? purchase.SubscriptionStatuz : 0,
                    PayMethodId = purchase.PayMethodId,
                    PayMethod = purchase.PayMethod,
                    SupplierLocation = purchase.SupplierLocation,
                    GoodsDeadline = purchase.GoodsDeadline,
                    WarrantyMonth = purchase.WarrantyMonth,
                    Memo = purchase.Memo,
                    DeliveryDate = purchase.DeliveryDate,
                    AcceptanceDate = purchase.AcceptanceDate,
                    SupplierSendTestDate = purchase.SupplierSendTestDate,
                    SchoolSendTestDate = purchase.SchoolSendTestDate,
                    UniformBuyId = buy.Id,
                    UniformBuyNo = buy.PurchaseNo,
                    ContractPersonNum = purchase.ContractPersonNum,
                    ContractAmount = purchase.ContractAmount,
                    FilingStatuz = purchase.FilingStatuz > 0 ? purchase.FilingStatuz : 0,
                    IsFiling = purchase.IsFiling,
                    IsRelatedUniform = purchase.IsRelatedUniform > 0 ? purchase.IsRelatedUniform : 0,
                    IsCountyManager = 1,
                });
            }
            else if (param.UnitType == UnitTypes.City.ObjToInt())
            {
                listSelectExpression = listExpression.Select((buy, purchase, school, supplier, county) => new XUniformPurchaseDto()
                {
                    Id = purchase.Id > 0 ? purchase.Id : 0,
                    IsDeleted = buy.IsDeleted,
                    CreateTime = purchase.CreateTime,
                    ModifyTime = purchase.ModifyTime,
                    CreateBy = purchase.CreateBy,
                    ModifyBy = purchase.ModifyBy,
                    PurchaseYear = purchase.PurchaseYear > 0 ? purchase.PurchaseYear : 0,
                    PurchaseNo = purchase.PurchaseNo,
                    SchoolId = buy.SchoolId,
                    SchoolName = school.Name,
                    CountyName = county.Name,
                    CountyAreaId = county.AreaId,
                    IsContractRenewal = purchase.IsContractRenewal > 0 ? purchase.IsContractRenewal : 0,
                    ContractStartDate = purchase.ContractStartDate,
                    ContractEndDate = purchase.ContractEndDate,
                    SupplierId = purchase.SupplierId,
                    SupplierName = supplier.Name,
                    OrganizationFormName = purchase.OrganizationFormName,
                    OrganizationForm = purchase.OrganizationForm,
                    ContractMainBodyId = purchase.ContractMainBodyId,
                    ContractMainBody = purchase.ContractMainBody,
                    ContractSignDate = purchase.ContractSignDate,
                    PayMethodId = purchase.PayMethodId,
                    PayMethod = purchase.PayMethod,
                    SupplierLocation = purchase.SupplierLocation,
                    GoodsDeadline = purchase.GoodsDeadline,
                    WarrantyMonth = purchase.WarrantyMonth,
                    Memo = purchase.Memo,
                    DeliveryDate = purchase.DeliveryDate,
                    AcceptanceDate = purchase.AcceptanceDate,
                    SupplierSendTestDate = purchase.SupplierSendTestDate,
                    SchoolSendTestDate = purchase.SchoolSendTestDate,
                    UniformBuyId = buy.Id,
                    UniformBuyNo = buy.PurchaseNo,
                    ContractPersonNum = purchase.ContractPersonNum,
                    ContractAmount = purchase.ContractAmount,
                    FilingStatuz = purchase.FilingStatuz > 0 ? purchase.FilingStatuz : 0,
                    IsFiling = purchase.IsFiling,
                    IsRelatedUniform = purchase.IsRelatedUniform > 0 ? purchase.IsRelatedUniform : 0,
                    IsCountyManager = 1,
                });
            }
            else
            {
                listSelectExpression = listExpression.Select((buy, purchase, school, supplier) => new XUniformPurchaseDto()
                {
                    Id = purchase.Id > 0 ? purchase.Id : 0,
                    IsDeleted = buy.IsDeleted,
                    CreateTime = purchase.CreateTime,
                    ModifyTime = purchase.ModifyTime,
                    CreateBy = purchase.CreateBy,
                    ModifyBy = purchase.ModifyBy,
                    PurchaseYear = purchase.PurchaseYear > 0 ? purchase.PurchaseYear : 0,
                    PurchaseNo = purchase.PurchaseNo,
                    SchoolId = buy.SchoolId,
                    IsContractRenewal = purchase.IsContractRenewal > 0 ? purchase.IsContractRenewal : 2,
                    ContractStartDate = purchase.ContractStartDate,
                    ContractEndDate = purchase.ContractEndDate,
                    SupplierId = purchase.SupplierId,
                    SupplierName = supplier.Name,
                    OrganizationFormName = purchase.OrganizationFormName,
                    OrganizationForm = purchase.OrganizationForm,
                    ContractMainBodyId = purchase.ContractMainBodyId,
                    ContractMainBody = purchase.ContractMainBody,
                    ContractSignDate = purchase.ContractSignDate,
                    PayMethodId = purchase.PayMethodId,
                    PayMethod = purchase.PayMethod,
                    SupplierLocation = purchase.SupplierLocation,
                    GoodsDeadline = purchase.GoodsDeadline,
                    WarrantyMonth = purchase.WarrantyMonth,
                    Memo = purchase.Memo,
                    DeliveryDate = purchase.DeliveryDate,
                    AcceptanceDate = purchase.AcceptanceDate,
                    SupplierSendTestDate = purchase.SupplierSendTestDate,
                    SchoolSendTestDate = purchase.SchoolSendTestDate,
                    UniformBuyId = buy.Id,
                    UniformBuyNo = buy.PurchaseNo,
                    ContractPersonNum = purchase.ContractPersonNum,
                    ContractAmount = purchase.ContractAmount,
                    FilingStatuz = purchase.FilingStatuz >0? purchase.FilingStatuz:0,
                    IsFiling = purchase.IsFiling,
                    FilingExplanation = purchase.FilingExplanation,
                    IsRelatedUniform = purchase.IsRelatedUniform > 0 ? purchase.IsRelatedUniform : 0,
                    SubscriptionDeadline = purchase.SubscriptionDeadline,
                    OrderNum = purchase.OrderNum > 0 ? purchase.OrderNum : 0,
                    OrderedNum = purchase.OrderedNum > 0 ? purchase.OrderedNum : 0,
                    SubscriptionStatuz = purchase.SubscriptionStatuz > 0 ? purchase.SubscriptionStatuz : 0,
                    SwapDeadline = purchase.SwapDeadline,
                    SwapStudentNum = purchase.SwapStudentNum,
                    SponsorUserNum = purchase.SponsorUserNum,
                    SponsorAmount = purchase.SponsorAmount,
                    SponsorSourceName = purchase.SponsorSourceName,
                    SponsorTime = purchase.SponsorTime,
                    EvaluateDeadline = purchase.EvaluateDeadline,
                    EvaluateNum = purchase.EvaluateNum,
                    EvaluateScore = purchase.EvaluateScore,
                    EvaluateTime = purchase.EvaluateTime,
                    IsCountyManager = 1,
                    SchoolName = school.Name
                });
            }

            var list = await listSelectExpression.MergeTable()
                  .WhereIF(param.IsContractRenewal != -10000, (tb1) => tb1.IsContractRenewal == param.IsContractRenewal)
                  .WhereIF(param.FilingStatuz != -10000, (tb1) => tb1.FilingStatuz == param.FilingStatuz)
                  .OrderBy(orderByFields)
                  .ToPageListAsync(param.pageIndex, param.pageSize, total);
            param.totalCount = total;
            pg.data = list;
            pg.dataCount = total;
            return pg;
        }


        /// <summary>
        /// 调换发起列表
        /// </summary>
        /// <param name="param">BAttachmentConfigParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseSwapDto>> GetSwapPaged(XUniformPurchaseParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> totalCount = 0;
            PageModel<XUniformPurchaseSwapDto> pageList = new PageModel<XUniformPurchaseSwapDto>();
            var list = await this.Db.Queryable<XUniformPurchase>()
                .InnerJoin<PUnit>((p, u) => p.SupplierId == u.Id)
                .Where((p, u) => p.SchoolId == param.SchoolId && p.OrderNum > 0)
                .WhereIF(param.CompanyId != -10000, (p, u) => p.SupplierId == param.CompanyId)
                .WhereIF(param.Statuz != -10000, (p, u) => p.SwapStatuz == param.Statuz)
                .WhereIF(param.PurchaseYear != -10000, (p, u) => p.PurchaseYear == param.PurchaseYear)
                .WhereIF(!string.IsNullOrEmpty(param.Key), (p, u) => p.PurchaseNo.Contains(param.Key))
                //.OrderByIF(!string.IsNullOrEmpty(orderByFields), orderByFields)
                .OrderByDescending((p, u) => new { RegDate = p.CreateTime })
                .Select((p, u) => new XUniformPurchaseSwapDto()
                {
                    Id = p.Id,
                    RegDate = p.CreateTime,
                    PurchaseYear = p.PurchaseYear,
                    PurchaseNo = p.PurchaseNo,
                    SupplierId = p.SupplierId,
                    SupplierName = u.Name,
                    Statuz = p.SwapStatuz,
                    SwapBegin = p.SwapBegin,
                    SwapDeadline = p.SwapDeadline,
                    OrderedNum = p.OrderedNum,
                    SwapStudentNum = p.SwapStudentNum.Value

                }).ToPageListAsync(param.pageIndex, param.pageSize, totalCount);
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }

        /// <summary>
        /// 调换单列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseSwapDto>> GetSwapOrderPaged(XUniformPurchaseParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> totalCount = 0;
            PageModel<XUniformPurchaseSwapDto> pageList = new PageModel<XUniformPurchaseSwapDto>();
            var list = await this.Db.Queryable<XUniformPurchase>()
                .InnerJoin<PUnit>((p, u) => p.SupplierId == u.Id)
                .InnerJoin<PUnit>((p, u, s) => p.SchoolId == s.Id)
                .Where((p, u, s) => p.SwapStatuz >= 2)
                .WhereIF(param.SchoolId != -10000, (p, u, s) => p.SchoolId == param.SchoolId)
                .WhereIF(param.CompanyId != -10000, (p, u, s) => p.SupplierId == param.CompanyId)
                .WhereIF(param.PurchaseYear != -10000, (p, u, s) => p.PurchaseYear == param.PurchaseYear)
                .WhereIF(!string.IsNullOrEmpty(param.Key), (p, u, s) => p.PurchaseNo.Contains(param.Key))
                //.OrderByIF(!string.IsNullOrEmpty(orderByFields), orderByFields)
                .Select((p, u, s) => new XUniformPurchaseSwapDto()
                {
                    Id = p.Id,
                    RegDate = p.CreateTime,
                    PurchaseYear = p.PurchaseYear,
                    PurchaseNo = p.PurchaseNo,
                    SupplierId = p.SupplierId,
                    SupplierName = u.Name,
                    Statuz = p.SwapStatuz,
                    SwapBegin = p.SwapBegin,
                    SwapDeadline = p.SwapDeadline,
                    OrderedNum = p.OrderedNum,
                    SwapStudentNum = p.SwapStudentNum.Value,
                    SchoolName = s.Name,
                    SwapStatuz = SqlFunc.IF(p.SwapStatuz == 3).Return(2).ElseIF(DateTime.Now.Date > p.SwapDeadline.Value.Date || DateTime.Now.Date < p.SwapBegin.Value.Date).Return(2).End(1),
                    StrSwapStatuz = SqlFunc.IF(p.SwapStatuz == 3).Return("调换结束").ElseIF(DateTime.Now.Date > p.SwapDeadline.Value.Date || DateTime.Now.Date < p.SwapBegin.Value.Date).Return("调换结束").End("正在填报")

                })
                .MergeTable()
                .WhereIF(param.SwapStatuz != -10000, f => f.SwapStatuz == param.SwapStatuz)
                .OrderByDescending(f => f.RegDate)
                .ToPageListAsync(param.pageIndex, param.pageSize, totalCount);
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }


        /// <summary>
        /// 班主任获取校服调换单列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseSwapDto>> GetSwapOrderByTeacherPaged(XUniformPurchaseParam param)
        {
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> totalCount = 0;
            PageModel<XUniformPurchaseSwapDto> pageList = new PageModel<XUniformPurchaseSwapDto>();
            var list = await this.Db.Queryable<XUniformChange>()
                .InnerJoin<XUniformParentPurchase>((UC, UPP) => UC.UniformParentPurchaseId == UPP.Id)
                .InnerJoin<PClassInfo>((UC, UPP, C) => UPP.UniformClassId == C.Id)
                .LeftJoin<PClassTeacher>((UC, UPP, C, T) => C.Id == T.ClassInfoId && T.IsCurrent == 1)
                .InnerJoin<XUniformPurchase>((UC, UPP, C, T, P) => UPP.UniformPurchaseId == P.Id)
                .InnerJoin<PUnit>((UC, UPP, C, T, P, U) => P.SupplierId == U.Id)
                .Where((UC, UPP, C, T, P, U) => P.SwapStatuz == 2 && T.TeacherUserId == param.TeacherId)

                .WhereIF(param.CompanyId != -10000, (UC, UPP, C, T, P, U) => P.SupplierId == param.CompanyId)
                .WhereIF(param.PurchaseYear != -10000, (UC, UPP, C, T, P, U) => P.PurchaseYear == param.PurchaseYear)
                .WhereIF(!string.IsNullOrEmpty(param.Key), (UC, UPP, C, T, P, U) => P.PurchaseNo.Contains(param.Key))
                //.OrderByIF(!string.IsNullOrEmpty(orderByFields), orderByFields)
                .GroupBy((UC, UPP, C, T, P, U) => new { P.Id, P.CreateTime, P.PurchaseYear, P.PurchaseNo, U.Name, P.SwapStatuz, P.SwapBegin, P.SwapDeadline, P.OrderedNum, P.SwapStudentNum })
                .Select((UC, UPP, C, T, P, U) => new XUniformPurchaseSwapDto()
                {
                    Id = P.Id,
                    RegDate = P.CreateTime,
                    PurchaseYear = P.PurchaseYear,
                    PurchaseNo = P.PurchaseNo,
                    SupplierName = U.Name,
                    Statuz = P.SwapStatuz,
                    SwapBegin = P.SwapBegin,
                    SwapDeadline = P.SwapDeadline,
                    OrderedNum = P.OrderedNum,
                    SwapStudentNum = P.SwapStudentNum.Value,
                    SwapStatuz = SqlFunc.IF(P.SwapStatuz == 3).Return(2).ElseIF(DateTime.Now.Date > P.SwapDeadline.Value.Date || DateTime.Now.Date < P.SwapBegin.Value.Date).Return(2).End(1),
                    StrSwapStatuz = SqlFunc.IF(P.SwapStatuz == 3).Return("调换结束").ElseIF(DateTime.Now.Date > P.SwapDeadline.Value.Date || DateTime.Now.Date < P.SwapBegin.Value.Date).Return("调换结束").End("正在填报")
                })
                .MergeTable()
                .OrderByDescending(f => f.RegDate)
                .WhereIF(param.SwapStatuz != -10000, f => f.SwapStatuz == param.SwapStatuz)
                .ToPageListAsync(param.pageIndex, param.pageSize, totalCount);
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }

        /// <summary>
        /// 发起调换
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> SwapLaunch(XUniformPurchaseSwapModel o)
        {
            var obj = await this.Db.Queryable<XUniformPurchase>().Where(f => f.Id == o.Id && f.SchoolId == o.SchoolId).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("非法操作!");
            }
            if (o.SwapBegin.Date > o.SwapDeadline.Date)
            {
                return Result<string>.Fail("调换开始时间不能大于调换结束时间!");
            }
            obj.SwapBegin = o.SwapBegin;
            obj.SwapDeadline = o.SwapDeadline;
            obj.SwapStatuz = 2;
            await base.Update(obj);
            return Result<string>.Success("发起调换成功");
        }


        /// <summary>
        /// 校服资助列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<PageModel<XuniformSponsorDto>> GetSponsorPaged(XUniformPurchaseParam param)
        {
            PageModel<XuniformSponsorDto> pg = new PageModel<XuniformSponsorDto>();
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            else
            {
                orderByFields = " PurchaseNo desc";
            }
            RefAsync<int> totalCount = 0;
            var list = await this.Db.Queryable<XUniformPurchase>()
                .InnerJoin<PUnit>((P, U1) => P.SchoolId == U1.Id)
                .InnerJoin<PUnit>((P, U1, U2) => U1.PId == U2.Id)
                .LeftJoin<BArea>((P, U1, U2, A) => U2.AreaId == A.Id)
                .Where((P, U1, U2, A) => P.OrderNum > 0)
                .WhereIF(param.SchoolId != -10000, (P, U1, U2, A) => P.SchoolId == param.SchoolId)
                .WhereIF(param.CountyId != -10000, (P, U1, U2, A) => U1.PId == param.CountyId)
                .WhereIF(param.CityId != -10000, (P, U1, U2, A) => U2.PId == param.CityId)
                .WhereIF(param.AreaId != -10000, (P, U1, U2, A) => A.Id == param.AreaId)
                .WhereIF(param.PurchaseYear != -10000, (P, U1, U2, A) => P.PurchaseYear == param.PurchaseYear)
                .WhereIF(!string.IsNullOrEmpty(param.Key), (P, U1, U2, A) => P.PurchaseNo.Contains(param.Key))
                .Select((P, U1, U2, A) => new XuniformSponsorDto()
                {
                    Id = P.Id,
                    PurchaseYear = P.PurchaseYear,
                    PurchaseNo = P.PurchaseNo,
                    SchoolName = U1.Name,
                    AreaName = A.Name,
                    OrderNum = P.OrderNum,
                    SponsorUserNum = P.SponsorUserNum.Value,
                    SponsorAmount = SqlFunc.ToString(P.SponsorAmount.Value.ToString("#.00")),
                    SponsorRatio = SqlFunc.ToString(P.SponsorRatio.Value.ToString("0.##")),

                })
                .MergeTable()
                .OrderByIF(!string.IsNullOrEmpty(orderByFields), orderByFields)
                .ToPageListAsync(param.pageIndex, param.pageSize, totalCount);
            pg.data = list;
            pg.dataCount = totalCount;
            return pg;
        }

        /// <summary>
        /// 根据项目采购Id获取校服资助详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<XuniformSponsorDto>> GetSponsorById(long id)
        {
            XuniformSponsorDto obj = await this.Db.Queryable<XUniformPurchase>().Where(f => f.Id == id)
                .Select(f => new XuniformSponsorDto()
                {
                    Id = f.Id,
                    PurchaseYear = f.PurchaseYear,
                    PurchaseNo = f.PurchaseNo,
                    PersonSupport = SqlFunc.ToString(f.PersonSupport.Value.ToString("#.00")),
                    OrderNum = f.OrderNum,
                    SponsorUserNum = f.SponsorUserNum.Value,
                    //SponsorProportion =SqlFunc.Round(f.SponsorUserNum.Value / f.OrderNum,2) * 100,
                    SponsorAmount = f.SponsorAmount.Value.ToString(),
                    SponsorSourceName = f.SponsorSourceName

                }).FirstAsync();

            //obj.SponsorProportion = (decimal.Parse(obj.SponsorUserNum.ToString()) / decimal.Parse(obj.OrderNum.ToString()) * 100).ToString("F2");

            //获取附件
            var listAttachment = await this.Db.Queryable<BAttachment>().Where(f => f.ObjectId == id && f.IsDelete == 0 && f.ModuleType == ModuleTypeEnum.Sponsor.ToEnumInt()).ToListAsync();
            obj.ListAttachment = listAttachment;
            return Result<XuniformSponsorDto>.Success("查询成功", obj, 1);
        }

        /// <summary>
        /// 校服资助-添加、修改
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> SponsorInsertUpdate(XuniformSponsorModel o)
        {
            XUniformPurchase obj = await this.Db.Queryable<XUniformPurchase>().Where(f => f.Id == o.Id && f.SchoolId == user.UnitId).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("非法操作!");
            }

            #region 增加FluentValidation验证
            var validator = new XuniformSponsorModelValidator();
            var result = validator.Validate(o);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                return Result<string>.Fail(tipMsg);
            }
            #endregion

            try
            {
                unitOfWorkManage.BeginTran();
                obj.SponsorUserNum = o.SponsorUserNum;
                obj.PersonSupport = o.PersonSupport;
                obj.SponsorAmount = o.SponsorUserNum * o.PersonSupport;
                obj.SponsorSourceName = o.SponsorSourceName;
                obj.SponsorTime = DateTime.Now;

                List<BAttachment> listAttachAll = new List<BAttachment>();
                List<BAttachment> listAttachAdd = new List<BAttachment>();
                if (o.ListAttachmentId != null && o.ListAttachmentId.Count > 0)
                {
                    List<BAttachmentData> listAttachmentData = await this.Db.Queryable<BAttachmentData>().Where(f => f.IsDeleted == false && o.ListAttachmentId.Contains(f.Id) && f.UnitId == user.UnitId).ToListAsync();
                    foreach (BAttachmentData att in listAttachmentData)
                    {
                        listAttachAdd.Add(new BAttachment()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ObjectId = obj.Id,
                            ModuleType = ModuleTypeEnum.Sponsor.ToEnumInt(),
                            Title = att.Title,
                            Path = att.Path,
                            Width = att.Width,
                            Height = att.Height,
                            DocType = 0,
                            IsDefault = 1,
                            Remark = "",
                            UserId = user.ID,
                            UnitId = user.UnitId,
                            FileCategory = att.FileCategory,
                            IsDelete = 0,
                            Ext = att.Ext,
                        });
                    }

                    if (listAttachAdd.Count > 0)
                    {
                        listAttachAll.AddRange(listAttachAdd);
                    }
                }

                //获取附件配置信息//增加附件验证
                //List<BAttachmentConfig> listConfig = await this.Db.Queryable<BAttachmentConfig>().Where(f => f.ModuleType == UniformModuleIdEnum.Sponsor.ToEnumInt()).ToListAsync();
                //if (listConfig.Count > 0)
                //{
                //    foreach (var item in listConfig)
                //    {
                //        if(item.IsFilled == 1)
                //        {

                //        }
                //    }
                //}

                if (listAttachAdd.Count > 0)
                {
                    await attachmentManager.Add(listAttachAdd);
                }

                await base.Update(obj);

                unitOfWorkManage.CommitTran();
            }
            catch (Exception)
            {
                unitOfWorkManage.RollbackTran();
                throw;
            }
            return Result<string>.Success("保存成功");
        }

        /// <summary>
        /// 删除校服资助信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> SponsorDeleteById(long id)
        {
            XUniformPurchase obj = await this.Db.Queryable<XUniformPurchase>().Where(f => f.Id == id && f.SchoolId == user.UnitId).FirstAsync();
            if (obj == null)
            {
                return Result<string>.Fail("非法操作!");
            }

            try
            {
                unitOfWorkManage.BeginTran();

                obj.SponsorUserNum = null;
                obj.PersonSupport = null;
                obj.SponsorAmount = null;
                obj.SponsorSourceName = null;
                obj.SponsorTime = null;

                //删除附件
                await this.Db.Updateable<BAttachment>().SetColumns(f => new BAttachment() { IsDelete = 1 }).Where(f => f.UnitId == user.UnitId && f.ObjectId == id && f.ModuleType == ModuleTypeEnum.Sponsor.ToEnumInt() && f.IsDelete == 0).ExecuteCommandAsync();

                await base.Update(obj);

                unitOfWorkManage.CommitTran();
            }
            catch (Exception)
            {
                unitOfWorkManage.RollbackTran();
                throw;
            }
            return Result<string>.Success("删除成功");
        }

        /// <summary>
        /// 删除校服资助附件
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> SponsorFileDelete(long id)
        {
            BAttachment att = await this.Db.Queryable<BAttachment>().Where(f => f.Id == id && f.UnitId == user.UnitId && f.IsDelete == 0).FirstAsync();
            BAttachmentData attData = await this.Db.Queryable<BAttachmentData>().Where(f => f.Id == id && f.UnitId == user.UnitId).FirstAsync();
            if (att == null && attData == null)
            {
                return Result<string>.Fail("非法操作!");
            }

            await this.Db.Updateable<BAttachment>().SetColumns(f => new BAttachment() { IsDelete = 1 }).Where(f => f.Id == id && f.UnitId == user.UnitId && f.ModuleType == ModuleTypeEnum.Sponsor.ToEnumInt() && f.IsDelete == 0).ExecuteCommandAsync();

            return Result<string>.Success("删除成功");
        }

        #endregion

        #region 公示结果-家长
        /// <summary>
        /// 家长获取采购结果信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseDto>> GetParentPaged(XUniformPurchaseParam param)
        {
            PageModel<XUniformPurchaseDto> pg = new PageModel<XUniformPurchaseDto>();

            var listStudent = await this.Db.Queryable<PStudent>().Where((m) => m.ParentUserId == param.userId && m.IsDeleted == false).ToListAsync();

            var listSchoolId = new List<long>();
            if (listStudent != null && listStudent.Count > 0)
            {
                listSchoolId = listStudent.Select(m => m.SchoolId).ToList();
            }
            else
            {
                return pg;
            }

            var listPurchase = await this.Db.Queryable<XUniformPurchase>().Where(n => listSchoolId.Contains(n.SchoolId)).ToListAsync();
            if (listPurchase.Count > 0)
            {
                param.Ids = string.Join(',', listPurchase.Select(n => n.Id));
                //班级过滤
                var listClass = await this.Db.Queryable<PClassInfo>().Where((m) => listStudent.Select(n => n.UniformClassId).Contains(m.Id) && m.IsDeleted == false).ToListAsync();
                if (listClass != null && listClass.Count > 0)
                {
                    var listPurchaseGrade = await this.Db.Queryable<XUniformPurchaseGrade>().Where((m) => listPurchase.Select(n => n.Id).Contains(m.UniformPurchaseId) && listClass.Select(h => h.GradeId).Contains(m.GradeId) && m.IsDeleted == false).ToListAsync();
                    if (listPurchaseGrade != null && listPurchaseGrade.Count > 0)
                    {
                        listPurchase = listPurchase.Where(m => listPurchaseGrade.Select(n => n.UniformPurchaseId).Contains(m.Id)).ToList();
                        param.Ids = string.Join(',', listPurchaseGrade.Select(n => n.UniformPurchaseId));
                    }
                }
            }
            else
            {
                return pg;
            }
            pg = await GetPaged(param);

            return pg;
        }

        #endregion

        #region 校服评价
        /// <summary>
        /// 校服评价列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseEvaluateDto>> GetEvaluatePaged(XUniformEvaluateParam param)
        {
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            else
            {
                orderByFields = "ModifyTime Desc ";
            }
            RefAsync<int> totalCount = 0;
            PageModel<XUniformPurchaseEvaluateDto> pageList = new PageModel<XUniformPurchaseEvaluateDto>();
            var list = await this.Db.Queryable<XUniformPurchase>()
                .InnerJoin<PUnit>((p, u) => p.SupplierId == u.Id)
                .Where((p, u) => p.SchoolId == param.SchoolId && p.SubscriptionStatuz >= UniformSubscriptionStatuzEnum.Subscription.ToEnumInt())
                .WhereIF(param.CompanyId != -10000, (p, u) => p.SupplierId == param.CompanyId)
                .WhereIF(param.PurchaseYear != -10000, (p, u) => p.PurchaseYear == param.PurchaseYear)
                .WhereIF(!string.IsNullOrEmpty(param.Key), (p, u) => p.PurchaseNo.Contains(param.Key))
                .Select((p, u) => new XUniformPurchaseEvaluateDto()
                {
                    Id = p.Id,
                    ContractRenewal = p.IsContractRenewal == 1 ? "是" : "否",
                    PurchaseYear = p.PurchaseYear,
                    PurchaseNo = p.PurchaseNo,
                    SupplierName = u.Name,
                    ContractEndDate = p.ContractEndDate,
                    SubscriptionDeadline = p.SubscriptionDeadline,
                    EvaluateDeadline = p.EvaluateDeadline,
                    EvaluateNum = p.EvaluateNum.Value,
                    ModifyTime = p.ModifyTime

                })
                .MergeTable()
                .OrderByIF(!string.IsNullOrEmpty(orderByFields), orderByFields)
                .ToPageListAsync(param.pageIndex, param.pageSize, totalCount);
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }

        /// <summary>
        /// 单位查看评价列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseEvaluateDto>> GetEvaluateList(XUniformEvaluateParam param)
        {
            string orderByFields = string.Empty;
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            else
            {
                orderByFields = "ModifyTime Desc ";
            }
            RefAsync<int> totalCount = 0;
            PageModel<XUniformPurchaseEvaluateDto> pageList = new PageModel<XUniformPurchaseEvaluateDto>();
            var list = await this.Db.Queryable<XUniformPurchase>()
                .InnerJoin<PUnit>((p, u) => p.SupplierId == u.Id)
                .InnerJoin<PUnit>((p, u, us) => p.SchoolId == us.Id)
                .InnerJoin<PUnit>((p, u, us, uc) => us.PId == uc.Id)
                .LeftJoin<BArea>((p, u, us, uc, a) => uc.AreaId == a.Id)
                .Where((p, u, us, uc, a) => p.EvaluateDeadline != null && p.SubscriptionStatuz >= UniformSubscriptionStatuzEnum.Subscription.ToEnumInt())
                .WhereIF(param.CompanyId != -10000, (p, u, us, uc, a) => p.SupplierId == param.CompanyId)
                .WhereIF(param.PurchaseYear != -10000, (p, u, us, uc, a) => p.PurchaseYear == param.PurchaseYear)
                .WhereIF(user.UnitTypeId == 3, (p, u, us, uc, a) => p.SchoolId == user.UnitId)
                .WhereIF(user.UnitTypeId == 2, (p, u, us, uc, a) => us.PId == user.UnitId)
                .WhereIF(user.UnitTypeId == 1, (p, u, us, uc, a) => uc.PId == user.UnitId)
                .WhereIF(param.SchoolId > 0, (p, u, us, uc, a) => p.SchoolId == param.SchoolId)
                .WhereIF(param.AreaId > 0, (p, u, us, uc, a) => a.Id == param.AreaId)
                .WhereIF(!string.IsNullOrEmpty(param.Key), (p, u, us, uc, a) => p.PurchaseNo.Contains(param.Key))
                .Select((p, u, us, uc, a) => new XUniformPurchaseEvaluateDto()
                {
                    Id = p.Id,
                    PurchaseYear = p.PurchaseYear,
                    PurchaseNo = p.PurchaseNo,
                    SupplierName = u.Name,
                    ContractEndDate = p.ContractEndDate,
                    SubscriptionDeadline = p.SubscriptionDeadline,
                    EvaluateDeadline = p.EvaluateDeadline,
                    EvaluateNum = p.EvaluateNum.Value,
                    EvaluateScore = p.EvaluateScore.Value,
                    EvaluateStatuz = DateTime.Now.Date > p.EvaluateDeadline.Value.Date ? 2 : 1,
                    StrEvaluateStatuz = DateTime.Now.Date > p.SwapDeadline.Value.Date ? "评价结束" : "正在评价",
                    ModifyTime = p.ModifyTime
                })
                .MergeTable()
                .OrderByIF(!string.IsNullOrEmpty(orderByFields), orderByFields)
                .WhereIF(param.Statuz != -10000, f => f.EvaluateStatuz == param.Statuz)
                .ToPageListAsync(param.pageIndex, param.pageSize, totalCount);
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }
        #endregion

        #region 查询数据 校服征订

        /// <summary>
        /// 校服采购-校服征订-生成征订单列表查询
        /// </summary>
        /// <param name="param">XUniformPurchaseParam实体参数</param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseDto>> GetEditOrderPaged(XUniformPurchaseParam param)
        {
            PageModel<XUniformPurchaseDto> pg = new PageModel<XUniformPurchaseDto>();
            var expression = ListFilter(param);
            string orderByFields = " ModifyTime Desc ";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> total = 0;
            var list = await this.Db.Queryable<XUniformPurchase>()
                  .InnerJoin<PUnit>((purchase, school) => purchase.SchoolId == school.Id)
                  .InnerJoin<PUnit>((purchase, school, supplier) => purchase.SupplierId == supplier.Id)
                  .InnerJoin<PUnit>((purchase, school, supplier, county) => school.PId == county.Id)
                  .WhereIF(param.SchoolId > 0, (purchase, school, supplier, county) => purchase.SchoolId == param.SchoolId)
                  .WhereIF(param.CountyId > 0 && param.UnitType != UnitTypes.School.ObjToInt(), (purchase, school, supplier, county) => school.PId == param.CountyId)
                  .WhereIF(param.CityId > 0 && param.UnitType == UnitTypes.City.ObjToInt(), (purchase, school, supplier, county) => county.PId == param.CityId)
                  .WhereIF(param.unitId > 0, (purchase, school, supplier, county) => purchase.SupplierId == param.unitId)
                  //.WhereIF(param.userId > 0, (purchase, school, supplier, county) => purchase.SupplierId == param.unitId)
                  .WhereIF(param.PurchaseYear != -10000, (purchase, school, supplier, county) => purchase.PurchaseYear == param.PurchaseYear)
                  .WhereIF(param.IsContractRenewal != -10000, (purchase, school, supplier, county) => purchase.IsContractRenewal == param.IsContractRenewal)
                  .WhereIF(param.FilingStatuz != -10000, (purchase, school, supplier, county) => purchase.FilingStatuz == param.FilingStatuz)
                  .WhereIF(param.IsRelatedUniform != -10000, (purchase, school, supplier, county) => purchase.IsRelatedUniform == param.IsRelatedUniform)
                  .WhereIF(param.SubscriptionStatuz != -10000, (purchase, school, supplier, county) => purchase.SubscriptionStatuz == param.IsRelatedUniform)
                  .WhereIF(param.SubscriptionStatuzLg != -10000, (purchase, school, supplier, county) => purchase.SubscriptionStatuz >= param.SubscriptionStatuzLg)
                  .WhereIF(param.CompanyId != -10000, (purchase, school, supplier, county) => purchase.SupplierId == param.CompanyId)
                  .WhereIF(param.Name != null && param.Name.Length > 0, (purchase, school, supplier, county) => purchase.PurchaseNo.Contains(param.Name))
                  .Select((purchase, school, supplier, county) => new XUniformPurchaseDto()
                  {
                      Id = purchase.Id,
                      IsDeleted = purchase.IsDeleted,
                      CreateTime = purchase.CreateTime,
                      ModifyTime = purchase.ModifyTime,
                      CreateBy = purchase.CreateBy,
                      ModifyBy = purchase.ModifyBy,
                      PurchaseYear = purchase.PurchaseYear,
                      PurchaseNo = purchase.PurchaseNo,
                      SchoolId = purchase.SchoolId,
                      IsContractRenewal = purchase.IsContractRenewal,
                      ContractStartDate = purchase.ContractStartDate,
                      ContractEndDate = purchase.ContractEndDate,
                      OrganizationFormName = purchase.OrganizationFormName,
                      OrganizationForm = purchase.OrganizationForm,
                      FilingStatuz = purchase.FilingStatuz,
                      IsFiling = purchase.IsFiling,
                      IsRelatedUniform = purchase.IsRelatedUniform,
                      OrderNum = purchase.OrderNum,
                      OrderedNum = purchase.OrderedNum,
                      SubscriptionDeadline = purchase.SubscriptionDeadline,
                      SubscriptionStatuz = purchase.SubscriptionStatuz,
                      IsCountyManager = 1,
                      SchoolName = school.Name,
                      CountyName = county.Name,
                      CountyAreaId = county.AreaId,
                      SupplierName = supplier.Name,
                  }).MergeTable()
                  .OrderBy(orderByFields)
                  .ToPageListAsync(param.pageIndex, param.pageSize, total);
            param.totalCount = total;
            pg.data = list;
            pg.dataCount = total;
            return pg;
        }

        /// <summary>
        /// 校服采购-校服征订-生成征订单列表查询
        /// </summary>
        /// <param name="param">XUniformPurchaseParam实体参数</param>
        /// <returns></returns>
        public async Task<PageModel<XUniformPurchaseDto>> GetOrderTeacherPaged(XUniformPurchaseParam param)
        {
            PageModel<XUniformPurchaseDto> pg = new PageModel<XUniformPurchaseDto>();
            var expression = ListFilter(param);
            string orderByFields = " ModifyTime Desc ";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            //根据班主任Id获取对应的单位，只能带一个单位的班主任。
            RefAsync<int> total = 0;
            var list = await this.Db.Queryable<XUniformPurchase>()
                  .InnerJoin<XUniformPurchaseGrade>((purchase, grade) => grade.IsDeleted == false && purchase.Id == grade.UniformPurchaseId)
                  .InnerJoin<PClassInfo>((purchase, grade, classinfo) => classinfo.IsDeleted == false && purchase.SchoolId == classinfo.SchoolId && grade.GradeId == classinfo.GradeId && classinfo.IsGraduate == 0)
                  .InnerJoin<PClassTeacher>((purchase, grade, classinfo, teacher) => teacher.IsDeleted == false && classinfo.Id == teacher.ClassInfoId)
                  .InnerJoin<PUnit>((purchase, grade, classinfo, teacher, school) => purchase.SchoolId == school.Id)
                  .InnerJoin<PUnit>((purchase, grade, classinfo, teacher, school, supplier) => purchase.SupplierId == supplier.Id)
                  .Where((purchase, grade, classinfo, teacher, school, supplier) => teacher.TeacherUserId == param.userId && teacher.IsCurrent == 1 && teacher.IsDeleted == false)
                  .WhereIF(param.SchoolId > 0, (purchase, grade, classinfo, teacher, school, supplier) => purchase.SchoolId == param.SchoolId)
                  .WhereIF(param.unitId > 0, (purchase, grade, classinfo, teacher, school, supplier) => purchase.SupplierId == param.unitId)
                  .WhereIF(param.PurchaseYear != -10000, (purchase, grade, classinfo, teacher, school, supplier) => purchase.PurchaseYear == param.PurchaseYear)
                  .WhereIF(param.IsContractRenewal != -10000, (purchase, grade, classinfo, teacher, school, supplier) => purchase.IsContractRenewal == param.IsContractRenewal)
                  .WhereIF(param.FilingStatuz != -10000, (purchase, grade, classinfo, teacher, school, supplier) => purchase.FilingStatuz == param.FilingStatuz)
                  .WhereIF(param.IsRelatedUniform != -10000, (purchase, grade, classinfo, teacher, school, supplier) => purchase.IsRelatedUniform == param.IsRelatedUniform)
                  .WhereIF(param.SubscriptionStatuz != -10000, (purchase, grade, classinfo, teacher, school, supplier) => purchase.SubscriptionStatuz == param.IsRelatedUniform)
                  .WhereIF(param.SubscriptionStatuzLg != -10000, (purchase, grade, classinfo, teacher, school, supplier) => purchase.SubscriptionStatuz >= param.SubscriptionStatuzLg)
                  .WhereIF(param.CompanyId != -10000, (purchase, grade, classinfo, teacher, school, supplier) => purchase.SupplierId == param.CompanyId)
                  .WhereIF(param.Name != null && param.Name.Length > 0, (purchase, grade, classinfo, teacher, school, supplier) => purchase.PurchaseNo.Contains(param.Name))
                  .Select((purchase, grade, classinfo, teacher, school, supplier) => new XUniformPurchaseDto()
                  {
                      Id = purchase.Id,
                      IsDeleted = purchase.IsDeleted,
                      CreateTime = purchase.CreateTime,
                      ModifyTime = purchase.ModifyTime,
                      CreateBy = purchase.CreateBy,
                      ModifyBy = purchase.ModifyBy,
                      PurchaseYear = purchase.PurchaseYear,
                      PurchaseNo = purchase.PurchaseNo,
                      SchoolId = purchase.SchoolId,
                      IsContractRenewal = purchase.IsContractRenewal,
                      ContractStartDate = purchase.ContractStartDate,
                      ContractEndDate = purchase.ContractEndDate,
                      OrganizationFormName = purchase.OrganizationFormName,
                      OrganizationForm = purchase.OrganizationForm,
                      FilingStatuz = purchase.FilingStatuz,
                      IsFiling = purchase.IsFiling,
                      IsRelatedUniform = purchase.IsRelatedUniform,
                      OrderNum = classinfo.StudentNum,//这里取班级人数
                      OrderedNum = 0,//这里需要单独汇总统计
                      SubscriptionDeadline = purchase.SubscriptionDeadline,
                      SubscriptionStatuz = purchase.SubscriptionStatuz,
                      IsCountyManager = 1,
                      SchoolName = school.Name,
                      SupplierName = supplier.Name,
                      GradeName = classinfo.GradeName,
                      ClassName = classinfo.ClassName,
                      ClassInfoId = classinfo.Id,
                  }).MergeTable()
                  .OrderBy(orderByFields)
                  .ToPageListAsync(param.pageIndex, param.pageSize, total);
            param.totalCount = total;
            pg.data = list;
            pg.dataCount = total;
            return pg;
        }
        #endregion

        #region 提交保存 校服征订



        /// <summary>
        /// 校服采购-校服征订-保存征订单
        /// </summary>
        /// <param name="model">征订单信息</param>
        /// <returns></returns>
        public async Task<Result> SaveOrder(XUniformPurchaseDto model)
        {
            Result r = new Result();
            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //发布后，禁止修改
            if (entity.IsRelatedUniform != 1)
            {
                r.flag = 0;
                r.msg = "执行失败，未查询到校服信息。";
                return r;
            }
            if (model.GradeList == null || model.GradeList.Count == 0)
            {
                r.flag = 0;
                r.msg = "执行失败，生成征订单请选择征订年级。";
                return r;
            }
            List<XUniformPurchaseGrade> listPurchaseGrade = null;
            if (entity.SubscriptionStatuz == UniformSubscriptionStatuzEnum.Subscription.ObjToInt())
            {
                //获取已填写班级
                listPurchaseGrade = await this.Db.Queryable<XUniformPurchaseGrade>().Where(f => f.UniformPurchaseId == entity.Id && f.IsDeleted == false).ToListAsync();
                if (entity.SubscriptionDeadline < DateTime.Now.Date)
                {
                    r.flag = 0;
                    r.msg = "执行失败，当前征订单已过截止日期。";
                    return r;
                }
            }
            else if (entity.SubscriptionStatuz == UniformSubscriptionStatuzEnum.FillIn.ObjToInt())
            {
                entity.SubscriptionStatuz = UniformSubscriptionStatuzEnum.Subscription.ObjToInt();
                if (model.SubscriptionDeadline < DateTime.Now.Date)
                {
                    r.flag = 0;
                    r.msg = "执行失败，生成征订单，征订截止时间必须不小于当前时间。";
                    return r;
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "执行失败，当前征订禁止操作。";
                return r;
            }
            entity.SubscriptionDeadline = model.SubscriptionDeadline;
            entity.OrderNum = 0;
            var numStudent = await this.Db.Queryable<PClassInfo>().Where((classinfo) => classinfo.SchoolId == model.SchoolId
            && classinfo.IsGraduate == 0
            && classinfo.IsDeleted == false
            && model.GradeList.Contains(classinfo.GradeId)).SumAsync(m => m.StudentNum);
            if (numStudent > 0)
            {
                entity.OrderNum = numStudent;
            }
            if (listPurchaseGrade != null && listPurchaseGrade.Count > 0)
            {
                var listTemp = listPurchaseGrade.Where(m => model.GradeList.Contains(m.GradeId)).ToList();
                if (listTemp != null && listTemp.Count() > 0)
                {
                    foreach (var item in listTemp)
                    {
                        listPurchaseGrade.Remove(item);
                        model.GradeList.Remove(item.GradeId);
                    }
                }
            }
            List<XUniformPurchaseGrade> listAddPurchaseGrade = new List<XUniformPurchaseGrade>();
            if (model.GradeList != null && model.GradeList.Count > 0)
            {
                listAddPurchaseGrade.AddRange(model.GradeList.Select(m => new XUniformPurchaseGrade()
                {
                    Id = BaseDBConfig.GetYitterId(),
                    UniformPurchaseId = entity.Id,
                    GradeId = m
                }));
            }
            if (listAddPurchaseGrade != null && listAddPurchaseGrade.Count > 0)
            {
                await this.Db.Insertable<XUniformPurchaseGrade>(listAddPurchaseGrade).ExecuteCommandAsync();
            }

            if (listPurchaseGrade != null && listPurchaseGrade.Count > 0)
            {
                await this.Db.Updateable<XUniformPurchaseGrade>().SetColumns(f => new XUniformPurchaseGrade() { IsDeleted = true }).Where(f => listPurchaseGrade.Select(m => m.Id).Contains(f.Id)).ExecuteCommandAsync();
            }

            var resultNo = await this.Db.Updateable<XUniformPurchase>().SetColumns(f => new XUniformPurchase()
            {
                SubscriptionDeadline = entity.SubscriptionDeadline,
                SubscriptionStatuz = entity.SubscriptionStatuz,
                OrderNum = entity.OrderNum
            }).Where(f => f.Id == entity.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                r.flag = 1;
                r.msg = "生成成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "生成失败，请刷新重新操作。如无法解决请联系客服协助处理。";
            }
            return r;
        }

        /// <summary>
        /// 校服采购-校服征订-更新需订购人数
        /// </summary>
        /// <param name="schoolid"></param>
        /// <param name="gradeid"></param>
        /// <returns></returns>
        public async Task<Result> UpdatePurchaseOrderNum(long schoolid, int gradeid)
        {
            Result r = new Result();
            if (!(schoolid > 0 && gradeid > 0))
            {
                r.flag = 0;
                r.msg = "执行失败，请选择单位和年级。";
                return r;
            }
            return r;
            List<XUniformPurchase> listPurchase = await this.Db.Queryable<XUniformPurchase>()
                 .InnerJoin<XUniformPurchaseGrade>((purchase, grade) => purchase.Id == grade.UniformPurchaseId && grade.IsDeleted == false)
                 .Where((purchase, grade) => purchase.IsDeleted == false && purchase.SchoolId == schoolid && grade.GradeId == gradeid)
                 .Where((purchase, grade) => purchase.SubscriptionStatuz == UniformSubscriptionStatuzEnum.Subscription.ObjToInt() && purchase.SubscriptionDeadline != null && purchase.SubscriptionDeadline < DateTime.Now.Date)
                 .Select((purchase, grade) => new XUniformPurchase()
                 {
                     Id = purchase.Id
                 }).Distinct().ToListAsync();
            if (listPurchase != null && listPurchase.Count > 0)
            {
                var listPurchaseGrade = await this.Db.Queryable<XUniformPurchaseGrade>().Where(f => listPurchase.Select(m => m.Id).Distinct().Contains(f.UniformPurchaseId) && f.IsDeleted == false).ToListAsync();
                if (listPurchaseGrade != null && listPurchaseGrade.Count > 0)
                {
                    var listClassInfo = await this.Db.Queryable<PClassInfo>().Where((classinfo) => classinfo.SchoolId == schoolid
                   && classinfo.IsGraduate == 0
                   && classinfo.IsDeleted == false
                   && listPurchaseGrade.Select(m => m.GradeId).Distinct().Contains(classinfo.GradeId)).ToListAsync();

                    try
                    {
                        unitOfWorkManage.BeginTran();
                        foreach (var item in listPurchase)
                        {
                            int OrderNum = 0;
                            if (listClassInfo != null && listClassInfo.Count > 0)
                            {
                                var listGradeTemp = listPurchaseGrade.Where(m => m.UniformPurchaseId == item.Id).Select(n => n.GradeId).ToList();
                                OrderNum = listClassInfo.Where(m => listGradeTemp.Contains(m.GradeId)).Select(n => n.StudentNum).Sum();
                            }
                            await this.Db.Updateable<XUniformPurchase>().SetColumns(f => new XUniformPurchase() { OrderNum = item.OrderNum }).Where(f => f.Id == item.Id).ExecuteCommandAsync();
                        }
                        r.flag = 1;
                        r.msg = "更新成功！";
                        unitOfWorkManage.CommitTran();
                    }
                    catch
                    {
                        r.flag = 0;
                        r.msg = "更新失败，请刷新重新操作。如无法解决请联系客服协助处理。";
                        unitOfWorkManage.RollbackTran();
                        throw;
                    }

                }
            }

            if (r.flag != 1)
            {
                r.flag = 0;
                r.msg = "更新失败，请刷新重新操作。如无法解决请联系客服协助处理。";
            }
            return r;
        }


        #endregion

        #region 提交保存
        /// <summary>
        /// 校服采购-采购管理-添加、修改提交保存
        /// </summary>
        /// <param name="model">XUniformPurchaseDto对象</param>
        /// <returns></returns>
        public async Task<Result<XUniformPurchaseDto>> InsertUpdate(XUniformPurchaseDto model)
        {
            Result<XUniformPurchaseDto> r = new Result<XUniformPurchaseDto>();

            #region 增加FluentValidation验证
            var validator = new XUniformPurchaseValidator();
            var result = validator.Validate(model);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                r.flag = 0;
                r.msg = tipMsg;
                return r;
            }
            if (model.ContractEndDate != null && model.ContractStartDate != null)
            {
                if (model.ContractEndDate < model.ContractStartDate)
                {
                    r.flag = 0;
                    r.msg = "合同结束时间不能小于开始时间。";
                    return r;
                }
            }
            if (model.PurchaseYear <= 0)
            {
                r.flag = 0;
                r.msg = "请选择年度。";
                return r;
            }
            #endregion

            XUniformPurchase entity = null;
            //添加校服采购表信息
            if (model.UniformBuyId <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作，请从页面点击操作。";
                return r;
            }
            var listBuy = await this.Db.Queryable<XUniformBuy>().Where(m => m.Id == model.UniformBuyId && m.SchoolId == user.UnitId).ToListAsync();
            if (listBuy == null || listBuy.Count <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作，请从页面点击操作。";
                return r;
            }
            var entityBuy = listBuy.FirstOrDefault();
            if (entityBuy.SchoolId != user.UnitId)
            {
                r.flag = 0;
                r.msg = "非法操作，禁止操作非本单位数据。";
                return r;
            }
            if (model.Id > 0)
            {
                entity = await base.QueryById(model.Id);
                if (entity == null || entity.SchoolId != model.SchoolId)
                {
                    r.flag = 0;
                    r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                    return r;
                }
                //发布后，禁止修改
                if (entity.FilingStatuz != UniformFilingEnum.SubmitNone.ObjToInt())
                {
                    r.flag = 0;
                    r.msg = "执行失败，已提交禁止修改。";
                    return r;
                }
                if (model.PurchaseYear != entity.PurchaseYear)
                {
                    entity.PurchaseNo = await GenerateCode(2, model.PurchaseYear);
                }
            }
            else
            {
                //验证是否已添加过该采购了，和采购一对一关系。
                var listVerify = await Db.Queryable<XUniformPurchase>().Where(m => m.UniformBuyId == model.UniformBuyId && m.SchoolId == user.UnitId).ToListAsync();
                if (listVerify != null && listVerify.Count > 0)
                {
                    r.flag = 0;
                    r.msg = "添加失败，当前采购已存在合同信息。";
                    return r;
                }

                entity = new XUniformPurchase();
                entity.UniformBuyId = entityBuy.Id;
                entity.UniformBuyNo = entityBuy.PurchaseNo;
                entity.SchoolId = entityBuy.SchoolId;

                entity.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
                entity.SwapStatuz = 1;
                //生成选用批次
                entity.PurchaseNo = await GenerateCode(2, model.PurchaseYear);
            }
        
            //保存附件。
            var listAddAttachment = new List<BAttachment>();
            var listAttachmentConfig = await this.Db.Queryable<BAttachmentConfig>()
            .Where((config) => config.ModuleType == ModuleTypeEnum.PurchaseCreate.ObjToInt() && (config.UnitId == model.CountyId || config.UnitId == 0)
            && config.IsDeleted == false).ToListAsync();
            if (listAttachmentConfig != null && listAttachmentConfig.Count > 0)
            {
                if (listAttachmentConfig.Where(x => x.UnitId == model.CountyId).Count() > 0)
                {
                    listAttachmentConfig = listAttachmentConfig.Where(x => x.UnitId == model.CountyId).ToList();
                }
                if (model.AttachmentIdList != null && model.AttachmentIdList.Count > 0)
                {
                    var listAttData = await this.Db.Queryable<BAttachmentData>()
                    .Where((att) => att.CreateId == model.CreateId && att.UnitId == model.SchoolId
                    && model.AttachmentIdList.Contains(att.Id)
                    && att.ModuleType == ModuleTypeEnum.PurchaseCreate.ObjToInt() && att.IsDeleted == false).ToListAsync();
                    if (listAttData != null && listAttData.Count > 0)
                    {
                        //listAttData = listAttData.Where(m => m.CreateId == model.CreateId).ToList();
                        foreach (var item in listAttData)
                        {
                            var itemConfig = listAttachmentConfig.Where(m => m.ModuleType == item.ModuleType && m.FileCategory == item.FileCategory).FirstOrDefault();
                            if (itemConfig != null)
                            {
                                var entityAtt = SetAttachment.SetModel(item);
                                if (entityAtt != null)
                                {
                                    listAddAttachment.Add(entityAtt);
                                }
                            }
                        }
                    }
                }
            }

            entity.PurchaseYear = model.PurchaseYear;
            entity.PurchaseCode = model.PurchaseCode;
            entity.ContractEndDate = model.ContractEndDate;
            entity.ContractStartDate = model.ContractStartDate;

            entity.ContractPersonNum = model.ContractPersonNum;
            entity.ContractAmount = model.ContractAmount;
            entity.ContractSignDate = model.ContractEndDate;
            //entity.ContractMainBody = model.ContractMainBody;
            //entity.ContractMainBodyId = model.ContractMainBodyId;
            //entity.PayMethod = model.PayMethod;
            //entity.PayMethodId = model.PayMethodId;
            //entity.SupplierLocationId = model.SupplierLocationId;
            //entity.SupplierLocation = model.SupplierLocation;
            entity.WarrantyMonth = model.WarrantyMonth;
            entity.GoodsDeadline = model.GoodsDeadline;
            entity.Memo = model.Memo;
            entity.DeliveryDate = model.DeliveryDate;
            entity.AcceptanceDate = model.AcceptanceDate;
            entity.SupplierSendTestDate = model.SupplierSendTestDate;
            entity.SchoolSendTestDate = model.SchoolSendTestDate;
            if (model.SupplierId > 0)
            {
                var entitySupplie = await this.Db.Queryable<PUnit>()
                .Where((unit) => unit.UnitType == UnitTypes.Company.ObjToInt() && unit.Id == model.SupplierId && unit.IsDeleted == false).FirstAsync();
                if (entitySupplie != null)
                {
                    entity.SupplierId = model.SupplierId;
                }
            }
            if (model.IsContractRenewal > 0)
            {
                if (Enum.IsDefined(typeof(StatuzIsEnum), model.IsContractRenewal))
                {
                    var IsContractRenewal = (StatuzIsEnum)model.IsContractRenewal;
                    entity.IsContractRenewal = IsContractRenewal.ObjToInt();
                }
            }

            var listdic = await this.Db.Queryable<BDictionary>()
            .Where((dic) => (dic.TypeCode == DictionaryTypeCodeEnum.SupplierLocation.ObjToInt().ToString()
            || dic.TypeCode == DictionaryTypeCodeEnum.PayMethod.ObjToInt().ToString()
            || dic.TypeCode == DictionaryTypeCodeEnum.ContractMainBody.ObjToInt().ToString()
            )
            && dic.Statuz == StatuzEnum.Enable.ObjToInt() && dic.IsDeleted == false).ToListAsync();
            if (model.PayMethodId > 0)
            {
                var entityDicTemp = listdic.Where(m => m.DicValue == model.PayMethodId.ToString()).FirstOrDefault();
                if (entityDicTemp!=null)
                {
                    entity.PayMethodId = model.PayMethodId;
                    entity.PayMethod = entityDicTemp.DicName;
                }
            }
            if (model.ContractMainBodyId > 0)
            {
                var entityDicTemp = listdic.Where(m => m.DicValue == model.ContractMainBodyId.ToString()).FirstOrDefault();
                if (entityDicTemp != null)
                {
                    entity.ContractMainBodyId = model.ContractMainBodyId;
                    entity.ContractMainBody = entityDicTemp.DicName;
                }
            }
            if (model.SupplierLocationId > 0)
            {
                var entityDicTemp = listdic.Where(m => m.DicValue == model.SupplierLocationId.ToString()).FirstOrDefault();
                if (entityDicTemp != null)
                {
                    entity.SupplierLocationId = model.SupplierLocationId;
                    entity.SupplierLocation = entityDicTemp.DicName;
                }
            }
            //if (model.OrganizationForm > 0)
            //{
            //    var entityDictionary = await this.Db.Queryable<BDictionary>()
            //    .Where((dic) => dic.TypeCode == DictionaryTypeCodeEnum.OrganizationForm.ObjToInt().ToString() && dic.DicValue == model.OrganizationForm.ToString() && dic.Statuz == StatuzEnum.Enable.ObjToInt() && dic.IsDeleted == false).FirstAsync();
            //    if (entityDictionary != null)
            //    {
            //        entity.OrganizationForm = model.OrganizationForm;
            //        entity.OrganizationFormName = entityDictionary.DicName;
            //    }
            //}


            if (entity.Id > 0)
            {
                await this.Db.Updateable<XUniformPurchase>()
                 .SetColumns((scheme) => new XUniformPurchase()
                 {
                     PurchaseYear = entity.PurchaseYear,
                     PurchaseNo = entity.PurchaseNo,
                     PurchaseCode = entity.PurchaseCode,
                     ContractEndDate = entity.ContractEndDate,
                     ContractStartDate = entity.ContractStartDate,
                     IsContractRenewal = entity.IsContractRenewal,
                     SupplierId = entity.SupplierId,
                     OrganizationForm = entity.OrganizationForm,
                     OrganizationFormName = entity.OrganizationFormName,

                     ContractPersonNum = entity.ContractPersonNum,
                     ContractAmount = entity.ContractAmount,
                     ContractSignDate = entity.ContractEndDate,
                     ContractMainBody = entity.ContractMainBody,
                     ContractMainBodyId = entity.ContractMainBodyId,
                     PayMethod = entity.PayMethod,
                     PayMethodId = entity.PayMethodId,
                     SupplierLocationId = entity.SupplierLocationId,
                     SupplierLocation = entity.SupplierLocation,
                     WarrantyMonth = entity.WarrantyMonth,
                     GoodsDeadline = entity.GoodsDeadline,
                     Memo = entity.Memo,
                     DeliveryDate = entity.DeliveryDate,
                     AcceptanceDate = entity.AcceptanceDate,
                     SupplierSendTestDate = entity.SupplierSendTestDate,
                     SchoolSendTestDate = entity.SchoolSendTestDate,
                     ModifyTime = DateTime.Now,
                     ModifyBy = model.CreateBy,
                     ModifyId = model.CreateId
                 })
                 .Where((scheme) => scheme.Id == entity.Id)
                 .ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "保存成功";

                //处理附件
                if (listAddAttachment.Count > 0)
                {
                    foreach (var item in listAddAttachment)
                    {
                        item.ObjectId = entity.Id;
                        item.Id = BaseDBConfig.GetYitterId();
                    }
                    await this.Db.Insertable<BAttachment>(listAddAttachment).ExecuteCommandAsync();
                }
            }
            else
            {
                await base.Add(entity);
                r.flag = 1;
                r.msg = "保存成功";

                //处理附件
                if (listAddAttachment.Count > 0)
                {
                    foreach (var item in listAddAttachment)
                    {
                        item.ObjectId = entity.Id;
                        item.Id = BaseDBConfig.GetYitterId();
                    }
                    await this.Db.Insertable<BAttachment>(listAddAttachment).ExecuteCommandAsync();
                }
            }
            r.data.rows = new XUniformPurchaseDto() { Id = entity.Id, PurchaseNo = entity.PurchaseNo };
            if (model.OptType == 3)
            {
                var resultSubmit =await Submit(new XUniformPurchaseDto() { Id = entity.Id, SchoolId = entity.SchoolId });
                if (resultSubmit!=null)
                {
                    r.flag = resultSubmit.flag;
                    r.msg = resultSubmit.msg;
                }
            }
            return r;
        }


        /// <summary>
        /// 校服采购-采购管理-提交
        /// </summary>
        /// <param name="model">XUniformPurchaseDto对象</param>
        /// <returns></returns>
        public async Task<Result> Submit(XUniformPurchaseDto model)
        {
            Result r = new Result();
            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.SubmitNone.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据状态已变更禁止提交。";
                return r;
            }
            string errorMsg = "";
            //验证年度
            if (entity.PurchaseYear <= 0)
            {
                errorMsg += "请选择年度。<br/>";
                r.flag = 0;
                r.msg = "请选择年度。";
                return r;
            }
            //验证是否续签合同是否选择
            if (!(entity.IsContractRenewal == 1 || entity.IsContractRenewal == 2))
            {
                errorMsg += "请选择是否续签合同。<br/>";
                r.flag = 0;
                r.msg = "请选择是否续签合同。";
                return r;
            }
          
            //合同签订主体
            if (entity.ContractMainBodyId <= 0)
            {
                errorMsg += "请选择合同签订主体。<br/>";
                r.flag = 0;
                r.msg = "请选择合同签订主体。";
                return r;
            }
            //订购总人数（人）
            if (!(entity.ContractPersonNum >= 0))
            {
                errorMsg += "请填写订购总人数。<br/>";
                r.flag = 0;
                r.msg = "请填写订购总人数。";
                return r;
            }
            //合同签订日期
            if (entity.ContractSignDate == null)
            {
                errorMsg += "请选择合同签订日期。<br/>";
                r.flag = 0;
                r.msg = "请选择合同签订日期。";
                return r;
            }
            //验证合同时间
            if (entity.ContractStartDate == null || entity.ContractEndDate == null)
            {
                errorMsg += "请选择合同开始结束时间。<br/>";
                r.flag = 0;
                r.msg = "请选择合同开始结束时间。";
                return r;
            }
            //合同金额（元）
            if (!(entity.ContractAmount >= 0))
            {
                errorMsg += "请填写合同金额。<br/>";
                r.flag = 0;
                r.msg = "请填写合同金额。";
                return r;
            }
            //费用支付方式
            if (entity.PayMethodId <= 0)
            {
                errorMsg += "请选择费用支付方式。<br/>";
                r.flag = 0;
                r.msg = "请选择费用支付方式。";
                return r;
            }
            //供应商
            if (entity.SupplierId <= 0)
            {
                errorMsg += "请选择供应商。<br/>";
                r.flag = 0;
                r.msg = "请选择供应商。";
                return r;
            }
            //供应商属地
            if (entity.SupplierLocationId <= 0)
            {
                errorMsg += "请选择供应商属地。<br/>";
                r.flag = 0;
                r.msg = "请选择供应商属地。";
                return r;
            }
            //供货期（天）
            if (!(entity.GoodsDeadline >= 0))
            {
                errorMsg += "请填写供货期。<br/>";
                r.flag = 0;
                r.msg = "请填写供货期。";
                return r;
            }
            //质保期（月）
            if (!(entity.WarrantyMonth >= 0))
            {
                errorMsg += "请填写质保期。<br/>";
                r.flag = 0;
                r.msg = "请填写质保期。";
                return r;
            }
            ////组织形式
            //if ((entity.OrganizationForm ?? 0) <= 0)
            //{
            //    errorMsg += "请选择采购组织形式。<br/>";
            //    r.flag = 0;
            //    r.msg = "请选择采购组织形式。";
            //    return r;
            //}
            if (entity.DeliveryDate == null || entity.DeliveryDate <= DateTime.MinValue)
            {
                errorMsg += "请选择送货日期。<br/>";
                r.flag = 0;
                r.msg = "请选择送货日期。";
                return r;
            }
            if (entity.AcceptanceDate == null || entity.AcceptanceDate <= DateTime.MinValue)
            {
                errorMsg += "请选择验货日期。<br/>";
                r.flag = 0;
                r.msg = "请选择验货日期。";
                return r;
            }
            if (entity.SupplierSendTestDate == null || entity.SupplierSendTestDate <= DateTime.MinValue)
            {
                errorMsg += "请选择供应商送检日期。<br/>";
                r.flag = 0;
                r.msg = "请选择供应商送检日期。";
                return r;
            }
            if (entity.SchoolSendTestDate == null || entity.SchoolSendTestDate <= DateTime.MinValue)
            {
                errorMsg += "请选择学校送检日期。<br/>";
                r.flag = 0;
                r.msg = "请选择学校送检日期。";
                return r;
            }

            //验证附件是否都传递了(必选验证)。这里验证必填，只需要查询必填的就可以了。
            var listAttachmentConfig = await this.Db.Queryable<BAttachmentConfig>()
            .Where((config) => config.ModuleType == ModuleTypeEnum.PurchaseCreate.ObjToInt() && config.IsFilled == 1 && (config.UnitId == model.CountyId || config.UnitId == 0)
            && config.IsDeleted == false).ToListAsync();
            if (listAttachmentConfig != null && listAttachmentConfig.Count > 0)
            {
                if (listAttachmentConfig.Where(x => x.UnitId == model.CountyId).Count() > 0)
                {
                    listAttachmentConfig = listAttachmentConfig.Where(x => x.UnitId == model.CountyId).ToList();
                }
                var listAllAttachment = await this.Db.Queryable<BAttachment>()
                .Where(attachment => attachment.ModuleType == ModuleTypeEnum.PurchaseCreate.ObjToInt() && attachment.IsDeleted == false && attachment.ObjectId == entity.Id).ToListAsync();
                foreach (var item in listAttachmentConfig)
                {
                    if (item.IsFilled == 1)
                    {
                        string msgTemp = $"附件{item.Name}必须上传。<br/>";
                        if (listAllAttachment != null && listAllAttachment.Count > 0)
                        {
                            var listTemp = listAllAttachment.Where(m => m.FileCategory == item.FileCategory && m.ModuleType == item.ModuleType);
                            if (listTemp != null && listTemp.Count() > 0)
                            {
                                msgTemp = "";
                            }
                        }
                        errorMsg += msgTemp;
                    }
                } 
            }
            if (errorMsg != "")
            {
                r.flag = 0;
                r.msg ="执行失败。"+ errorMsg;
                return r;
            }
            //判断是否需要备案

            entity.FilingStatuz = UniformFilingEnum.Filinged.ObjToInt();
            entity.IsFiling = 0;
            //如果不需要审核，直接结束
            var entityConfigList = await this.Db.Queryable<XUniformConfig>().Where((config) => (config.UnitId == model.CountyId || config.UnitId == 0) && config.Code == "2000" && config.DicValue == "202").ToListAsync();
            if (entityConfigList != null && entityConfigList.Count() > 0)
            {
                var entityTempconfig = entityConfigList.Where(m => m.UnitId == model.CountyId).FirstOrDefault();
                if (entityTempconfig != null)
                {
                    if (entityTempconfig.ValueNum == 1)
                    {
                        entity.FilingStatuz = UniformFilingEnum.Wait.ObjToInt();
                    }
                }
                else if (entityConfigList.FirstOrDefault().ValueNum == 1)
                {
                    entity.FilingStatuz = UniformFilingEnum.Wait.ObjToInt();
                }
            }

            entity.FilingTime = DateTime.Now;
            entity.FilingExplanation = model.FilingExplanation;

            var resultNo = await this.Db.Updateable<XUniformPurchase>()
                 .SetColumns((scheme) => new XUniformPurchase()
                 {
                     FilingStatuz = entity.FilingStatuz,
                     IsFiling = entity.IsFiling,
                     FilingTime = entity.FilingTime,
                     FilingExplanation = entity.FilingExplanation,
                 })
                 .Where((scheme) => scheme.Id == entity.Id)
                 .ExecuteCommandAsync();

            if (resultNo > 0)
            {
                //插入记录表 UniformAuditLog
                var entityLog = new XUniformAuditLog();
                entityLog.UniformId = entity.Id;
                entityLog.AuditTime = DateTime.Now;
                entityLog.AduitStatuz = entity.FilingStatuz;
                entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Purchase.ObjToInt().ToString();
                entityLog.AuditExplain = "提交备案";
                entityLog.IsCurrent = 1;
                await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "提交成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "执行失败，请刷新重新操作。如无法解决请联系客服协助处理。";
            }
            return r;
        }


        /// <summary>
        /// 校服采购-采购管理-根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        public async Task<Result> FakeDeleteById(XUniformPurchaseDto model)
        {
            Result r = new Result();
            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //发布后，禁止修改
            if (entity.FilingStatuz != UniformFilingEnum.SubmitNone.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，已提交禁止删除。";
                return r;
            }

            var resultNo = await this.Db.Updateable<XUniformPurchase>().SetColumns(f => new XUniformPurchase() { IsDeleted = true }).Where(f => f.Id == entity.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                r.flag = 1;
                r.msg = "删除成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "删除失败，请刷新重新操作。如无法解决请联系客服协助处理。";
            }
            return r;
        }

        /// <summary>
        /// 方案选用-删除附件
        /// </summary>
        /// <param name="model">撤销信息</param>
        /// <returns></returns>
        public async Task<Result> UpdateAttachmentDelete(XUniformPurchaseDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null || entity.SchoolId != model.SchoolId)
            {
                r.flag = 0;
                r.msg = "执行失败，当前校服采购数据已不存在，请刷新重新操作。";
                return r;
            }
            var listAddAttachment = new List<BAttachment>();
            var entityAttachment = await this.Db.Queryable<BAttachment>()
            .Where((att) => att.Id == model.AttachmentId && att.ObjectId == entity.Id && att.ModuleType == ModuleTypeEnum.PurchaseCreate.ObjToInt() && att.IsDeleted == false).FirstAsync();

            if (entityAttachment == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前附件已不存在，请刷新重新操作。";
                return r;
            }
            var resultNo = await this.Db.Updateable<BAttachment>().SetColumns((att) => new BAttachment()
            {
                IsDeleted = true,
                ModifyTime = DateTime.Now,
                ModifyBy = model.CreateBy,
                ModifyId = model.CreateId
            })
            .Where((att) => att.Id == entityAttachment.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                r.flag = 1;
                r.msg = "删除成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "删除失败,请刷新重试，如无法解决请联系客服协助处理！";
            }
            return r;
        }

        #endregion

        #region 审核、退回

        /// <summary>
        /// 校服采购-区县审核
        /// </summary>
        /// <param name="model">提交</param>
        /// <returns></returns>
        public async Task<Result> FilingConfirm(XUniformPurchaseDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //获取父级Id验证
            var entityUnit = await this.Db.Queryable<PUnit>().Where((unit) => unit.Id == entity.SchoolId).FirstAsync();
            if (entityUnit == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据单位信息不存在。";
                return r;
            }
            if (entityUnit.PId != model.CountyId)
            {
                r.flag = 0;
                r.msg = "执行失败，非本单位的下属单位禁止操作。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.Wait.ObjToInt().ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据状态已变更，禁止操作 。";
                return r;
            }
            entity.FilingStatuz = model.FilingStatuz;
            entity.FilingTime = DateTime.Now;
            entity.FilingExplanation = model.FilingExplanation;
            if (model.FilingStatuz == UniformFilingEnum.Filinged.ObjToInt())
            {
                //审核通过。  
                entity.IsFiling = 1;
            }
            else
            {
                //验证审核退回，请填写退回原因。
                if (string.IsNullOrEmpty(model.FilingExplanation))
                {
                    r.flag = 0;
                    r.msg = "执行失败，退回请填写退回说明 。";
                    return r;
                }
            }
            var resultNo = await this.Db.Updateable<XUniformPurchase>().SetColumns((scheme) => new XUniformPurchase()
            {
                FilingStatuz = entity.FilingStatuz,
                FilingTime = entity.FilingTime,
                FilingExplanation = entity.FilingExplanation,
                IsFiling = entity.IsFiling
            })
             .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                //插入记录表 UniformAuditLog
                var entityLog = new XUniformAuditLog();
                entityLog.UniformId = entity.Id;
                entityLog.AuditTime = DateTime.Now;
                entityLog.AduitStatuz = entity.FilingStatuz;
                entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Purchase.ObjToInt().ToString();
                entityLog.AuditExplain = "审核" + entity.FilingExplanation;
                entityLog.IsCurrent = 1;
                await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "审核成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "审核失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            }
            return r;
        }
        /// <summary>
        /// 校服采购-区县退回
        /// </summary>
        /// <param name="model">Id</param>
        /// <returns></returns>
        public async Task<Result> FilingBackout(XUniformPurchaseDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //获取父级Id验证
            var entityUnit = await this.Db.Queryable<PUnit>().Where((unit) => unit.Id == entity.SchoolId).FirstAsync();
            if (entityUnit == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据单位信息不存在。";
                return r;
            }
            if (entityUnit.PId != model.CountyId)
            {
                r.flag = 0;
                r.msg = "执行失败，非本单位的下属单位禁止操作。";
                return r;
            }
            if (entity.IsFiling == 1)
            {
                r.flag = 0;
                r.msg = "执行失败，当前选用方案已审核通过，禁止退回 。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.Filinged.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前选用方案状态已变更，请刷新重新操作。";
                return r;
            }
            //验证审核退回，请填写退回原因。
            if (string.IsNullOrEmpty(model.FilingExplanation))
            {
                r.flag = 0;
                r.msg = "执行失败，退回请填写退回说明 。";
                return r;
            }
            entity.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
            entity.FilingTime = DateTime.Now;
            entity.FilingExplanation = model.FilingExplanation;
            var resultNo = await this.Db.Updateable<XUniformPurchase>().SetColumns((scheme) => new XUniformPurchase()
            {
                FilingStatuz = entity.FilingStatuz,
                FilingTime = entity.FilingTime,
                FilingExplanation = entity.FilingExplanation
            })
            .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                //插入记录表 UniformAuditLog
                var entityLog = new XUniformAuditLog();
                entityLog.UniformId = entity.Id;
                entityLog.AuditTime = DateTime.Now;
                entityLog.AduitStatuz = entity.FilingStatuz;
                entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Purchase.ObjToInt().ToString();
                entityLog.AuditExplain = model.FilingExplanation;
                entityLog.IsCurrent = 1;
                await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "退回成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "退回失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            }
            return r;
        }


        /// <summary>
        /// 校服采购-区县撤回（撤回上一级）
        /// </summary>
        /// <param name="model">Id</param>
        /// <returns></returns>
        public async Task<Result> FilingRevoked(XUniformPurchaseDto model)
        {
            Result r = new Result();

            var entity = await base.QueryById(model.Id);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据已不存在，请刷新重新操作。";
                return r;
            }
            //获取单位Id
            var entityUnit = await this.Db.Queryable<PUnit>().Where((unit) => unit.Id == entity.SchoolId).FirstAsync();
            if (entityUnit == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前数据单位信息不存在。";
                return r;
            }
            if (entityUnit.PId != model.CountyId)
            {
                r.flag = 0;
                r.msg = "执行失败，非本单位的下属单位禁止操作。";
                return r;
            }
            if (entity.FilingStatuz != UniformFilingEnum.Filinged.ObjToInt())
            {
                r.flag = 0;
                r.msg = "执行失败，当前选用方案状态已变更，请刷新重新操作。";
                return r;
            }
            //验证审核退回，请填写退回原因。
            //if (string.IsNullOrEmpty(model.FilingExplanation))
            //{
            //    r.flag = 0;
            //    r.msg = "执行失败，退回请填写退回说明 。";
            //    return r;
            //}
            if (entity.IsFiling == 1)
            {
                entity.FilingStatuz = UniformFilingEnum.Wait.ObjToInt();
            }
            else
            {
                entity.FilingStatuz = UniformFilingEnum.SubmitNone.ObjToInt();
            }

            entity.FilingTime = DateTime.Now;
            entity.FilingExplanation = "";
            entity.IsFiling = 0;
            var resultNo = await this.Db.Updateable<XUniformPurchase>().SetColumns((scheme) => new XUniformPurchase()
            {
                FilingStatuz = entity.FilingStatuz,
                FilingTime = entity.FilingTime,
                FilingExplanation = entity.FilingExplanation,
                IsFiling = entity.IsFiling
            })
            .Where((scheme) => scheme.Id == entity.Id).ExecuteCommandAsync();
            if (resultNo > 0)
            {
                //插入记录表 UniformAuditLog
                var entityLog = new XUniformAuditLog();
                entityLog.UniformId = entity.Id;
                entityLog.AuditTime = DateTime.Now;
                entityLog.AduitStatuz = entity.FilingStatuz;
                entityLog.DataSourceCode = "CG" + UniformAuditLogCodeEnum.Purchase.ObjToInt().ToString();
                entityLog.AuditExplain = "撤销：" + model.FilingExplanation;
                entityLog.IsCurrent = 1;
                await this.Db.Insertable<XUniformAuditLog>(entityLog).ExecuteCommandAsync();
                r.flag = 1;
                r.msg = "撤销成功！";
            }
            else
            {
                r.flag = 0;
                r.msg = "退回失败，数据执行异常，请刷新重试；如无法解决请联系客服协助处理。";
            }
            return r;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">XUniformPurchaseParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<XUniformPurchase, bool>> ListFilter(XUniformPurchaseParam param)
        {
            var expression = LinqExtensions.True<XUniformPurchase>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
            }
            return expression;
        }


        #endregion

        #region 批次编号系统自动生成
        /// <summary>
        /// 根据类型生成编号
        /// </summary>
        /// <param name="type">1:选用批次,2:合同批次,3:采购批次</param>
        /// <param name="year">年度</param>
        /// <returns></returns>
        public async Task<string> GenerateCode(int type, int year)
        {
            string code = string.Empty;
            if (type == 1)
            {
                XUniformScheme obj = await this.Db.Queryable<XUniformScheme>().Where(f => f.SchemeYear == year && f.IsDeleted == false).OrderBy(f => f.CreateTime, OrderByType.Desc).FirstAsync();
                if (obj != null)
                {
                    if (!string.IsNullOrEmpty(obj.SchemeNo))
                    {
                        int number = 0;
                        string result = obj.SchemeNo[^4..];
                        int.TryParse(result, out number);
                        number++;
                        code = $"XY{year}{number.ToString().PadLeft(4, '0')}";
                    }
                    else
                    {
                        code = $"XY{year}0001";
                    }

                }
                else
                {
                    code = $"XY{year}0001";
                }
            }
            else if (type == 2)
            {
                XUniformPurchase obj = await this.Db.Queryable<XUniformPurchase>().Where(f => f.PurchaseYear == year && f.IsDeleted == false).OrderBy(f => f.CreateTime, OrderByType.Desc).FirstAsync();
                if (obj != null)
                {
                    if (!string.IsNullOrEmpty(obj.PurchaseNo))
                    {
                        int number = 0;
                        string result = obj.PurchaseNo[^4..];
                        int.TryParse(result, out number);
                        number++;
                        code = $"HT{year}{number.ToString().PadLeft(4, '0')}";
                    }
                    else
                    {
                        code = $"HT{year}0001";
                    }
                }
                else
                {
                    code = $"HT{year}0001";
                }
            }
            else if (type == 3)
            {
                XUniformBuy obj = await this.Db.Queryable<XUniformBuy>().Where(f => f.PlanYear == year && f.IsDeleted == false).OrderBy(f => f.CreateTime, OrderByType.Desc).FirstAsync();
                if (obj != null)
                {
                    if (!string.IsNullOrEmpty(obj.PurchaseNo))
                    {
                        int number = 0;
                        string result = obj.PurchaseNo[^4..];
                        int.TryParse(result, out number);
                        number++;
                        code = $"CG{year}{number.ToString().PadLeft(4, '0')}";
                    }
                    else
                    {
                        code = $"CG{year}0001";
                    }
                }
                else
                {
                    code = $"CG{year}0001";
                }
            }

            return code;
        }

        #endregion
    }
}

