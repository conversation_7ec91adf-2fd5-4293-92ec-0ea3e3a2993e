using System.ComponentModel.DataAnnotations;
using System.Web;

namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///流程名称配置表
    ///</summary>
    [SugarTable("wf_Process", "流程名称配置表")]
    public class WfProcess : BaseEntity
    {

        public WfProcess()
        {

        }

        /// <summary>
        ///流程名称
        /// </summary>
        [SugarColumn(Length = 63)]
        public string ProcessName { get; set; }

        /// <summary>
        ///创建人单位Id
        /// </summary>
        public long CreateUnitId { get; set; }

        /// <summary>
        ///流程Logo
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ProcessLogo { get; set; }

        /// <summary>
        /// 标记
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string Sign { get; set; }

        /// <summary>
        ///使用单位Id
        /// </summary>
        public long UseUnitId { get; set; }

        /// <summary>
        /// 1：仅本单位；2：本单位及所有下属单位
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int Usage { get; set; } = 2;

        /// <summary>
        ///状态(3：设置中 1：启用 2：禁用)
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        ///模块Id
        /// </summary>
        public long ModuleId { get; set; }

        /// <summary>
        ///写入第三方库类型（1：主表；2：详细表；3：资金分配指定字段）
        /// </summary>
        public int InputDataType { get; set; } = 2;

        /// <summary>
        ///是否显示历史附件（2：否 ；1：是）默认2
        /// </summary>
        public int IsShowHistoryFile { get; set; } = 2;

        /// <summary>
        ///资金分配指定的字段编码即字段code（只有当InputDataType为3时才使用）
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string FieldCode { get; set; }

        /// <summary>
        ///资金分配指定的数据分类编码（只有当InputDataType为3时才使用）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? TypeCode { get; set; }

        /// <summary>
        ///是否签名（2：否；1：是）默认2
        /// </summary>
        public int IsSignature { get; set; } = 2;

        /// <summary>
        /// 是否控制金额（1是，填报金额不能超过余额；2：否；）
        /// </summary>
        public int IsControlAmount { get; set; } = 2;

        /// <summary>
        /// 是否开启项目库（1：是 2：否）默认2
        /// </summary>
        [SugarColumn(DefaultValue = "2")]
        public int IsOpen { get; set; } = 2;

        /// <summary>
        /// 节点Json数据
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        public string NodeConfig { get; set; } = "";

        /// <summary>
        /// 连接线Json数据
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "longtext,text,clob")]
        public string LineConfig { get; set; } = "";

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


        /// <summary>
        /// 模块名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ModuleName { get; set; }


        /// <summary>
        /// 使用单位名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string UnitName { get; set; }

        /// <summary>
        /// 写入项目库状态（满足指定状态时才写入项目库，可指定多个状态值逗号分隔）下拉框显示只显示通过的状态
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string WriteSourceFundStatuz { get; set; }


        /// <summary>
        /// 项目清单Code值（当InputDataType为项目清单时要选择项目清单对应的Code）
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string ProjectListCode { get; set; }

        /// <summary>
        /// 排序值（值越小越排前面）默认0
        /// </summary>
        [SugarColumn(DefaultValue = "0", IsNullable = true)]
        public int PSort { get; set; }

        /// <summary>
        /// 是否开启单位权限控制（1：是 2：否）默认否
        /// </summary>
        [SugarColumn(DefaultValue = "2", IsNullable = true)]
        public int IsOpenControl { get; set; }
    }
}

