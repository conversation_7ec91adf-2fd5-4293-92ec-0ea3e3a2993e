﻿using Hyun.Core.IServices.Uniform;
using Hyun.Old.Util;
using Org.BouncyCastle.Bcpg.OpenPgp;
namespace Hyun.Core.Api
{

    [Route("api/hyun/xuniformparentpurchase")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformParentPurchaseController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformParentPurchaseServices uniformparentpurchaseManager;
        private readonly IXUniformPurchaseGradeServices ixuniformpurchasegradesManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IXUniformPurchaseServices ixuniformpurchaseManager;
        private readonly IPSchoolExtensionServices schoolExtensionManager;
        private readonly IPClassInfoServices classinfoManager;
        private readonly IXUniformShelfServices uniformshelfManager;
        private readonly IXUniformShelfSizeServices uniformshelfsizeManager;
        private readonly IPStudentServices studentManager;
        private readonly IPUnitServices unitManager;
        private readonly IBAttachmentServices attachmentManager;
        public XUniformParentPurchaseController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IXUniformParentPurchaseServices _xuniformparentpurchasesManager, IXUniformPurchaseGradeServices _ixuniformpurchaseclassservicesManager, IBDictionaryServices _dictionaryManager, IXUniformPurchaseServices _ixuniformpurchaseManager, IPSchoolExtensionServices _schoolExtensionManager, IPClassInfoServices _classinfoManager, IXUniformShelfServices _uniformshelfManager, IXUniformShelfSizeServices _uniformshelfsizeManager, IPStudentServices _studentManager, IPUnitServices _unitManager, IBAttachmentServices _attachmentManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            uniformparentpurchaseManager = _xuniformparentpurchasesManager;
            ixuniformpurchasegradesManager = _ixuniformpurchaseclassservicesManager;
            dictionaryManager = _dictionaryManager;
            ixuniformpurchaseManager = _ixuniformpurchaseManager;
            schoolExtensionManager = _schoolExtensionManager;
            classinfoManager = _classinfoManager;
            uniformshelfManager = _uniformshelfManager;
            uniformshelfsizeManager = _uniformshelfsizeManager;
            studentManager = _studentManager;
            unitManager = _unitManager;
            attachmentManager = _attachmentManager;
        }

        #region 查询列表 

        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="param">XUniformPurchaseDetailParam对象</param>
        /// <returns></returns>
        [HttpGet]
        [Route("xuniformpurchasedetailgetpaged")]
        public async Task<Result> XUniformPurchaseDetailGetPaged([FromBody] XUniformParentPurchaseParam param)
        {
            Result r = new Result();
            PageModel<XUniformParentPurchase> pg = await uniformparentpurchaseManager.GetPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = mapper.Map<List<XUniformParentPurchaseDto>>(pg.data);
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            return r;
        }

        /// <summary>
        /// 校服采购-校服征订-年级班级学生征订详情列表
        /// </summary>
        /// <param name="param">XUniformPurchaseClassParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getstudentpaged")]
        public async Task<Result<List<XuniformPurchaseStudentModel>>> GetStudentPaged([FromBody] XUniformPurchaseGradeParam param)
        {
            Result<List< XuniformPurchaseStudentModel>> r = new Result<List<XuniformPurchaseStudentModel>>();
            var entity = await ixuniformpurchaseManager.GetById(param.UniformPurchaseId);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前征订已不存在。";
                return r;
            }
     
            var pg = await uniformparentpurchaseManager.GetStudentPaged(param);
            if (pg!=null && pg.data != null&& pg.data.Count > 0)
            {
                var entityTotal = pg.data.Where(m=>m.GradeName== "总计").First();
                pg.data.Remove(entityTotal);
                r.data.footer = entityTotal;
            }
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                List<dropdownModel> dropdownStatuz = new List<dropdownModel>();
                dropdownStatuz.Add(new dropdownModel() { value = "1", label = "已订" });
                dropdownStatuz.Add(new dropdownModel() { value = "2", label = "未订" });

                List<dropdownModel> dropdownGrade = new List<dropdownModel>();
                List<XUniformPurchaseGrade> listPurchaseGrade = null;
                if (entity.SubscriptionStatuz == UniformSubscriptionStatuzEnum.Subscription.ObjToInt())
                {
                    listPurchaseGrade = await ixuniformpurchasegradesManager.Find(m => m.UniformPurchaseId == entity.Id && m.IsDeleted == false);
                }
                var listGrade = await dictionaryManager.GetGradeInfo();
                if (listGrade != null && listGrade.Count > 0)
                {
                    foreach (var item in listGrade)
                    {
                        if (listPurchaseGrade != null && listPurchaseGrade.Count > 0)
                        {
                            if (listPurchaseGrade.Where(m => m.GradeId.ToString() == item.DicValue).Count() > 0)
                            {
                                dropdownGrade.Add(new dropdownModel() { value = item.DicValue, label = item.DicName });
                            }
                        }
                    }
                }

                List<dropdownModel> dropdownClass = new List<dropdownModel>();
                if (listPurchaseGrade != null && listPurchaseGrade.Count > 0)
                {
                    var listClassInfo = await classinfoManager.Find(m => m.SchoolId == entity.SchoolId && listPurchaseGrade.Select(n => n.GradeId).Contains(m.GradeId));
                    if (listClassInfo != null && listClassInfo.Count > 0)
                    {
                        foreach (var item in listClassInfo)
                        {
                            dropdownClass.Add(new dropdownModel() { value = item.Id.ToString(), label = item.ClassName });
                        }
                    }
                }
                r.data.other = new { StatuzList = dropdownStatuz, GradeList = dropdownGrade, ClassList = dropdownClass };
            }
            return r;
        }

        /// <summary>
        /// 校服采购-校服征订-获取家长征订学生校服信息
        /// </summary>
        /// <param name="id">采购征订表Id</param>
        /// <param name="studentid">学生Id，多个学生切换的时候会用到</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getparentpurchaseinfo")]
        public async Task<Result<List<XUniformShelfDto>>> GetParentPurchaseInfo(long id,long studentid=0)
        {
            Result<List<XUniformShelfDto>> r = new Result<List<XUniformShelfDto>>();
            //1：获取当前采购，征订信息
            var entity = await ixuniformpurchaseManager.GetById(id);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前征订已不存在。";
                return r;
            }
            if (!(entity.SubscriptionStatuz >= UniformSubscriptionStatuzEnum.Subscription.ObjToInt()))
            {
                r.flag = 0;
                r.msg = "执行失败，当前征订信息已不存在。";
                return r;
            }
            string schoolName = "";
            var entityUnit = await unitManager.QueryById(entity.SchoolId);
            if (entityUnit != null)
            {
                schoolName = entityUnit.Name;
            }
            //2:获取家长关联的学生信息。
            var listStudent = await studentManager.Find(m => m.ParentUserId == user.UserId && m.IsDeleted == false);
            if (listStudent == null || listStudent.Count == 0)
            {
                r.flag = 1;
                r.msg = "未找到你要采购的学生信息，请先添加学生。";
                return r;
            }
            //获取学生对应的班级
            var listClassInfo = await classinfoManager.Find(m => listStudent.Select(n => n.UniformClassId).Contains(m.Id) && m.IsGraduate == 0 && m.IsDeleted == false);
            if (listClassInfo == null || listClassInfo.Count == 0)
            {
                r.flag = 1;
                r.msg = "未找到你要采购的学生信息，请先添加学生。";
                return r;
            }
            //3:获取当前采购年级。
            var listPurchaseGrade = await ixuniformpurchasegradesManager.Find(m => m.UniformPurchaseId == entity.Id && m.IsDeleted == false);
            if (listPurchaseGrade == null || listPurchaseGrade.Count == 0)
            {
                r.flag = 0;
                r.msg = "执行失败，未找到当前校服征订设置的年级信息。";
                return r;
            }
            //学生信息集合
            List<dropdownModel> dropDownStudent = new List<dropdownModel>();
            List<PStudent> listStudentCurrent = new List<PStudent>();
            foreach (var item in listPurchaseGrade)
            {
                var listTemp = listClassInfo.Where(m => m.GradeId == item.GradeId).ToList();
                if (listTemp != null && listTemp.Count > 0)
                {
                    var listTempStudent = listStudent.Where(m => listTemp.Select(n => n.Id).Contains(m.UniformClassId)).ToList();
                    if (listTempStudent != null && listTempStudent.Count > 0)
                    {
                        listStudentCurrent.AddRange(listTempStudent);
                    }
                }
            }
            if (listStudentCurrent.Count == 0)
            {
                r.flag = 0;
                r.msg = "未查询到你关联的学生信息，存在当前校服征订的年级中。";
                return r;
            } 
            var entityStudent = listStudentCurrent.FirstOrDefault();
            if (studentid != 0)
            {
                entityStudent = listStudentCurrent.Where(m => m.Id == studentid).FirstOrDefault();
            }
            if (listStudentCurrent != null&& listStudentCurrent.Count >0) 
            {
                dropDownStudent.AddRange((from student in listStudentCurrent select new dropdownModel() { value = student.Id.ToString(), label = student.StudentName, selected = (student.Id == entityStudent.Id) }));
            }

            //获取校服信息
            List<XUniformShelfDto> listShelfDto = new List<XUniformShelfDto>();
            var listShelf = await uniformshelfManager.Find(m => m.UniformPurchaseId == entity.Id && GetSex(entityStudent.Sex).Contains(m.Sex) && m.IsDeleted == false);
            if (listShelf != null && listShelf.Count > 0)
            {
                //当前学生是否采购了。
                var listPurchaseStudent = await uniformparentpurchaseManager.Find(f => f.UniformPurchaseId == entity.Id && f.StudentId == entityStudent.Id && f.Statuz == 1 && f.IsDeleted == false);
                 
                //获取尺码表数据
                var listShelfSize = await uniformshelfsizeManager.Find(m => listShelf.Select(n => n.Id).Contains(m.UniformShelfId) && m.IsDeleted == false);

                var listSizeImage = await attachmentManager.Find(m => listShelf.Select(s => s.Id).Contains(m.ObjectId) && m.ModuleType == ModuleTypeEnum.Create.ObjToInt() && m.FileCategory == 103003 && m.IsDeleted == false);
                //采购要求
                var listpurchaseDemand = await dictionaryManager.Find(m => m.TypeCode == DictionaryTypeCodeEnum.PurchaseDemand.ObjToInt().ToString() && m.IsDeleted == false);

                foreach (var item in listShelf)
                {

                    //根据当前学生过滤男款女款。

                    var shelfDto = XUniformShelfDtoModel.GetModel(item);
                    if (listShelfSize != null && listShelfSize.Count > 0)
                    {
                        var listTemp = listShelfSize.Where(m => m.UniformShelfId == item.Id).ToList();
                        if (listTemp != null && listTemp.Count > 0)
                        {
                            shelfDto.SizeList = (from size in listTemp
                                                 orderby size.Sort
                                                 select new XUniformShelfSizeDto()
                                                 {
                                                     Id = size.Id,
                                                     UniformShelfId = size.UniformShelfId,
                                                     StandardName = size.StandardName,
                                                     Sort = size.Sort,

                                                 }).ToList();
                        }
                        shelfDto.SexName = GetSexName(item.Sex);
                    }
                    if (!string.IsNullOrEmpty(shelfDto.PurchaseDemand) && listpurchaseDemand!=null && listpurchaseDemand.Count > 0)
                    {
                        var listTemp = listpurchaseDemand.Where(m => m.DicValue == shelfDto.PurchaseDemand);
                        if (listTemp != null && listTemp.Count() > 0)
                        {
                            shelfDto.PurchaseDemandName = listTemp.FirstOrDefault().DicName;
                        }
                    }
                    if (listSizeImage != null && listSizeImage.Count > 0)
                    {
                        var listTempImage = listSizeImage.Where(m => m.ObjectId == item.Id).ToList();
                        if (listTempImage != null && listTempImage.Count > 0)
                        {
                            shelfDto.SizePath = listTempImage.FirstOrDefault().Path;
                        }
                    }
                    if (listPurchaseStudent != null && listPurchaseStudent.Count > 0)
                    {
                        var listTempShelf = listPurchaseStudent.Where(m => m.UniformShelfId == item.Id).ToList();
                        if (listTempShelf != null && listTempShelf.Count > 0)
                        {
                            var entityTemp = listTempShelf.FirstOrDefault();
                            shelfDto.OrderedNum = entityTemp.Num;
                            shelfDto.UniformShelfSizeId = entityTemp.UniformShelfSizeId;
                        }

                    }
                    listShelfDto.Add(shelfDto);
                }
            }

            r.data.total = listShelfDto.Count;
            r.flag = 1;
            r.msg = "获取数据成功。";
            r.data.rows = listShelfDto;

            r.data.other = new { PurchaseNo = entity.PurchaseNo, SchoolName = schoolName, SubscriptionDeadline = entity.SubscriptionDeadline, StudentList = dropDownStudent };
            return r;
        }

        private List<int> GetSex(string sexname)
        {
            List<int> sex = new List<int>() { 3, 0 };
            if (sexname == "女")
            {
                sex.Add(2);
            }
            else
            {
                sex.Add(1);
            }
            return sex;
        }
        /// <summary>
        /// 获取性别名称0:未知  1：男  2：女 3：男/女
        /// </summary>
        /// <param name="sex"></param>
        /// <returns></returns>
        private string GetSexName(int sex)
        {
            string name = "";
            switch (sex)
            {
                case 1:
                    name = "男";
                    break;
                case 2:
                    name = "女";
                    break;
                case 3:
                    name = "男/女";
                    break;
                default:
                    name = "未知";
                    break;
            }
            return name;
        }
        #endregion


        #region 班主任查看征订单详情
        /// <summary>
        /// 校服采购-校服征订-年级班级学生征订详情列表（班主任）
        /// </summary>
        /// <param name="param">XUniformPurchaseClassParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getteacherstudentpaged")]
        public async Task<Result<List<XuniformPurchaseStudentModel>>> GetTeacherStudentPaged([FromBody] XUniformPurchaseGradeParam param)
        {
            Result<List<XuniformPurchaseStudentModel>> r = new Result<List<XuniformPurchaseStudentModel>>();
            var entity = await ixuniformpurchaseManager.GetById(param.UniformPurchaseId);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前征订已不存在。";
                return r;
            }
            if (param.ClassInfoId <= 0)
            {
                r.flag = 0;
                r.msg = "执行失败，请从页面点击操作。";
                return r;
            }
            var pg = await uniformparentpurchaseManager.GetStudentPaged(param);
            if (pg != null && pg.data != null && pg.data.Count > 0)
            {
                var entityTotal = pg.data.Where(m => m.GradeName == "总计").First();
                pg.data.Remove(entityTotal);
                r.data.footer = entityTotal;
            }
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            if (param.isFirst)
            {
                //Other对象存储查询条件下拉数据 下拉对象都使用 dropdownModel
                List<dropdownModel> dropdownStatuz = new List<dropdownModel>();
                dropdownStatuz.Add(new dropdownModel() { value = "1", label = "已订" });
                dropdownStatuz.Add(new dropdownModel() { value = "2", label = "未订" });
                r.data.other = new { StatuzList = dropdownStatuz};
            }
            return r;
        }

        #endregion

        #region 订单详情导出
        /// <summary>
        /// 校服采购-校服征订-导出学生征订信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportstudent")]
        public async Task<Result> ExportStudent([FromBody] XUniformPurchaseGradeParam param)
        {
            Result r = new Result();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;

            var entity = await ixuniformpurchaseManager.GetById(param.UniformPurchaseId);
            if (entity == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前征订已不存在。";
                return r;
            }
            var entityUnit = await unitManager.QueryById(entity.SchoolId);
            if (entityUnit == null)
            {
                r.flag = 0;
                r.msg = "执行失败，当前征订已不存在。";
                return r;
            }
            string exl_title = string.Format("{0}_{1}_{2}", entityUnit.Name, entity.PurchaseNo, "征订订单明细");
            var pg = await uniformparentpurchaseManager.GetStudentPaged(param);
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";

            if (pg.dataCount > 0)
            {
                List<string> columns = new List<string>() { "GradeName", "ClassName", "StudentNo", "StudentName", "Sex"
                    , "Uniformtype" , "Name", "SizeDes", "NumStr", "UnitName", "PriceStr", "AmountStr", "StatuzName", "IDCard6", "ParentMobile"};
                pg.data.ForEach(m => {
                    m.StatuzName = (m.Statuz == 1 ? "已订" : (m.Statuz == 3 ? "" : "未订"));
                    m.IDCard6 = (m.Statuz == 3 ? "" : ("*" + m.IDCard6));
                    m.ParentMobile = (m.Statuz == 3 ? "" : m.ParentMobile);
                    //m.Uniformtype= (m.Statuz == 1 ? m.Uniformtype : "");
                    //m.Name = (m.Statuz == 1 ? m.Name : "");
                    //m.SizeDes = (m.Statuz == 1 ? m.SizeDes : "");
                    //m.UnitName = (m.Statuz == 1 ? m.UnitName : "");
                    m.PriceStr = (m.Statuz == 1 ? m.Price.ToString("G0") : "");
                    m.NumStr = (m.Statuz == 1 ? m.Num.ToString() : "");
                    m.AmountStr = (m.Statuz == 1 ? m.Amount.ToString("G0") : (m.Statuz == 3 ? m.Amount.ToString("G0") : ""));
                });
                string file = new ExcelHelper<XuniformPurchaseStudentModel>().ExportToExcel(env.WebRootPath, "征订订单明细.xls", exl_title, pg.data.ToList(), columns.ToArray());
                r.data.rows =  file;
                r.flag = 1;
                r.msg = "导出成功";
                r.data.headers = "征订订单明细";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }
        #endregion

        #region 保存提交家长征订
        /// <summary>
        /// 校服管理-校服征订-家长征订
        /// </summary>
        /// <param name="model">XUniformPurchaseClassDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("parentsubmit")]
        public async Task<Result> ParentSubmit([FromBody] XUniformParentPurchaseDto model)
        {
            return await uniformparentpurchaseManager.ParentSubmit(model); 
        }
        #endregion 
    }
}
