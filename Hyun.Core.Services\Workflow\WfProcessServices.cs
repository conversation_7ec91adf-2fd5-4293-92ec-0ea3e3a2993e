﻿using AutoMapper;
using Grpc.Core;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.Models.Workflow;
using Hyun.Core.Model.Validator.Workflow;
using Hyun.Core.Repository.UnitOfWorks;
using MathNet.Numerics.Distributions;
using Microsoft.AspNetCore.Mvc;
using NetTaste;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Org.BouncyCastle.Bcpg.OpenPgp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;

namespace Hyun.Core.Services
{

    ///<summary>
    ///WfProcess方法
    ///</summary>
    public class WfProcessServices : BaseServices<WfProcess>, IWfProcessServices
    {

        private readonly IMapper mapper;
        private readonly IUnitOfWorkManage unitOfWorkManage;
        private readonly IUser user;
        public WfProcessServices(IMapper _mapper, IUnitOfWorkManage _unitOfWorkManage, IUser _user)
        {
            mapper = _mapper;
            unitOfWorkManage = _unitOfWorkManage;
            user = _user;
        }


        #region 查询数据
        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        public async Task<WfProcess> GetById(long id)
        {
            return await base.QueryById(id);
        }

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        public async Task<List<WfProcess>> Find(Expression<Func<WfProcess, bool>> expression)
        {
            return await base.Query(expression);
        }

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">WfProcessParam实体参数</param>
        /// <param name="page">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        public async Task<PageModel<WfProcess>> GetPaged(WfProcessParam param)
        {
            var expression = ListFilter(param);
            string orderByFields = "ModuleId DESC";
            if (param.sortModel != null && param.sortModel.Count > 0)
            {
                orderByFields = string.Join(",", param.sortModel.Select(f => f.SortCode + " " + f.SortType));
            }
            RefAsync<int> totalCount = 0;
            PageModel<WfProcess> pageList = new PageModel<WfProcess>();
            var list = await this.Db.Queryable<WfProcess>()
                .InnerJoin<WfModule>((p, m) => p.ModuleId == m.Id)
                .InnerJoin<PUnit>((p, m, u) => p.UseUnitId == u.Id)
                .Select((p, m, u) => new WfProcess()
                {
                    Id = p.Id,
                    IsOpen = p.IsOpen,
                    IsDeleted = p.IsDeleted,
                    CreateTime = p.CreateTime,
                    ProcessName = p.ProcessName,
                    ModuleName = m.Name,
                    UseUnitId = p.UseUnitId,
                    UnitName = u.Name,
                    Statuz = p.Statuz,
                    ModuleId = m.Id,
                    IsSignature = p.IsSignature,
                })
                .MergeTable()
                .WhereIF(expression != null, expression)
                .OrderBy(orderByFields)
                .ToPageListAsync(param.pageIndex, param.pageSize, totalCount); ;
            pageList.data = list;
            pageList.dataCount = totalCount;
            return pageList;
        }
        #endregion

        /// <summary>
        /// 
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<WfProcessDto>> InsertUpdate(WfProcessDto m)
        {
            Result r = new Result();
            WfProcess o = mapper.Map<WfProcess>(m);
            #region 增加FluentValidation验证
            var validator = new WfProcessValidator();
            var result = validator.Validate(o);
            if (!result.IsValid)
            {
                string tipMsg = string.Join(';', result.Errors.Select(f => f.ErrorMessage));
                return Result<WfProcessDto>.Fail(tipMsg);
            }
            #endregion

            if (o.Id > 0)
            {
                //判断模块名称是否存在
                WfProcess obj = await this.Db.Queryable<WfProcess>().Where(f => f.ProcessName == o.ProcessName && f.Id != o.Id && f.ModuleId == o.ModuleId).FirstAsync();
                if (obj != null)
                {
                    return Result<WfProcessDto>.Fail("同模块下流程名称不能重复");
                }
                WfProcess objCurrent = await base.QueryById(o.Id);
                if (objCurrent != null)
                {
                    if(m.IsOpenControl == 2 && objCurrent.IsOpenControl == 1)
                    {
                        //单位授权从开启状态变更为关闭状态，要删除下属单位流程禁用表wf_ChildUnitDisable数据
                        await this.Db.Updateable<WfChildUnitDisable>().SetColumns(f => new WfChildUnitDisable() { IsDeleted = true}).Where(f => f.ProcessId == objCurrent.Id).ExecuteCommandAsync();

                    }
                    o.Statuz = objCurrent.Statuz;
                    o.NodeConfig = objCurrent.NodeConfig;
                    o.LineConfig = objCurrent.LineConfig;
                    await base.Update(o);

                    return Result<WfProcessDto>.Success("修改成功。", m, 1);
                }
                else
                {
                    return Result<WfProcessDto>.Fail("数据不存在");
                }

            }
            else
            {
                //判断模块名称是否存在
                WfProcess obj = await this.Db.Queryable<WfProcess>().Where(f => f.ProcessName == o.ProcessName && f.ModuleId == o.ModuleId).FirstAsync();
                if (obj != null)
                {
                    return Result<WfProcessDto>.Fail("同模块下流程名称不能重复");
                }
                o.Statuz = 3;
                o.Id = BaseDBConfig.GetYitterId();
                await base.Add(o);
                m.Id = o.Id;
                return Result<WfProcessDto>.Success("保存成功。", m, 1);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="processNameId"></param>
        /// <returns></returns>
        public async Task<Result<string>> DeleteById(long processNameId)
        {
            Result r = new Result();

            //begin  此处后续需要增加判断处理
            var obj = await this.Db.Queryable<WfProjectDeclaration>().Where(f => f.ProcessId == processNameId).FirstAsync();
            if (obj != null)
            {
                return Result<string>.Fail("当前流程已经使用，不能删除!");
            }
            //end

            //await base.DeleteById(processNameId);
            await this.Db.Updateable<WfProcess>().SetColumns(f => new WfProcess() { IsDeleted = true }).Where(f => f.Id == processNameId).ExecuteCommandAsync();
            await this.Db.Updateable<WfProcessStatuz>().SetColumns(f => new WfProcessStatuz() { IsDeleted = true }).Where(f => f.ProcessId == processNameId).ExecuteCommandAsync();
            await this.Db.Updateable<WfProcessReturnSet>().SetColumns(f => new WfProcessReturnSet() { IsDeleted = true }).Where(f => f.ProcessId == processNameId).ExecuteCommandAsync();
            await this.Db.Updateable<WfProcessNodeLink>().SetColumns(f => new WfProcessNodeLink() { IsDeleted = true }).Where(f => f.ProcessId == processNameId).ExecuteCommandAsync();
            return Result<string>.Success("删除成功");
        }

        /// <summary>
        /// 删除连接线
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> DeleteProcessNodeLinkById(long id)
        {
            Result r = new Result();

            //begin  此处后续需要增加判断处理


            //end
            await this.Db.Updateable<WfProcessCondition>().SetColumns(f => new WfProcessCondition() { IsDeleted = true }).Where(f => f.ProcessNodeLinkId == id).ExecuteCommandAsync();
            await this.Db.Updateable<WfProcessNodeLink>().SetColumns(f => new WfProcessNodeLink() { IsDeleted = true }).Where(f => f.Id == id).ExecuteCommandAsync();

            return Result<string>.Success("删除成功");
        }

        /// <summary>
        /// 删除流程节点
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Result<string>> DeleteProcessNodeById(long id)
        {
            Result r = new Result();

            //begin  此处后续需要增加判断处理
            var obj = await this.Db.Queryable<WfProjectAudit>().Where(f => f.ProcessNodeId == id).FirstAsync();
            if (obj != null)
            {
                return Result<string>.Fail("当前节点已经使用，不能删除!");
            }
            //end
          
            List<WfProcessNodeLink> listNodeLink = await this.Db.Queryable<WfProcessNodeLink>().Where(f => f.FromId == id || f.ToId == id).ToListAsync();
            List<long> listId = listNodeLink.Select(f => f.Id).ToList();
            await this.Db.Updateable<WfProcessNodeLink>().SetColumns(f => new WfProcessNodeLink() { IsDeleted = true }).Where(f => f.FromId == id || f.ToId == id).ExecuteCommandAsync();
            await this.Db.Updateable<WfProcessCondition>().SetColumns(f => new WfProcessCondition() { IsDeleted = true }).Where(f => listId.Contains(f.ProcessNodeLinkId.Value)).ExecuteCommandAsync();
            return Result<string>.Success("删除成功");
        }


        /// <summary>
        /// 流程设置
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> ProcessSetSave(WfProcessSetConfigModel o)
        {
            int errorCount = 0;
            string errorMsg = "";
            try
            {
                WfProcess objProcess = await base.QueryById(o.ProcessId);
                if (objProcess == null)
                {
                    return Result<string>.Fail("流程Id不存在");
                }

                unitOfWorkManage.BeginTran();

                List<LineConfigModel> listLine = JsonHelper.JsonToObj<List<LineConfigModel>>(o.LineConfig);
                List<string> listNewStr = listLine.Select(f => f.from + "-" + f.to).ToList();

                await this.Db.Updateable<WfProcess>().SetColumns(f => new WfProcess() { Statuz = 1, NodeConfig = o.NodeConfig, LineConfig = o.LineConfig }).Where(f => f.Id == o.ProcessId).ExecuteCommandAsync();
                //获取连线数据信息
                List<WfProcessNodeLink> listNodeLink = await this.Db.Queryable<WfProcessNodeLink>().Where(f => f.ProcessId == o.ProcessId && f.IsDeleted == false).ToListAsync();
                //获取条件数据信息
                List<WfProcessCondition> listCondition = await this.Db.Queryable<WfProcessCondition>().Where(f => f.ProcessId == o.ProcessId && f.IsDeleted == false).ToListAsync();

                List<string> listOldStr = listNodeLink.Select(f => f.FromId + "-" + f.ToId).ToList();
                
                foreach (LineConfigModel line in listLine)
                {
                    string strSqlCondition = "";
                    string strShowCondition = "";
                    int index = 0;

                    long ProcessNodeLinkId = 0;
                    List<WfProcessCondition> listConditionAdd = new List<WfProcessCondition>();
                    WfProcessNodeLink objProcessNodeLink = listNodeLink.Where(f => f.IsDeleted == false && f.ProcessId == o.ProcessId && f.FromId == line.from && f.ToId == line.to).FirstOrDefault();
                    if (objProcessNodeLink != null)
                    {
                        ProcessNodeLinkId = objProcessNodeLink.Id;
                    }

                    if (!string.IsNullOrEmpty(line.sqlCondition))
                    {
                        strSqlCondition = line.sqlCondition;

                        if (ProcessNodeLinkId > 0)
                        {
                            ////先删除
                            //await this.Db.Deleteable<WfProcessCondition>().Where(f => f.ProcessNodeLinkId == ProcessNodeLinkId).ExecuteCommandAsync();
                        
                            //先删除
                            await this.Db.Updateable<WfProcessCondition>().SetColumns(f => new WfProcessCondition() { IsDeleted = true }).Where(f => f.ProcessNodeLinkId == ProcessNodeLinkId).ExecuteCommandAsync();
                        }
                        foreach (FilterModel filter in line.filters)
                        {
                            if (!string.IsNullOrEmpty(filter.field))
                            {
                                string filterValue = filter.value;
                                if (filterValue.Contains(","))
                                {
                                    string[] strs = filterValue.Split(',');
                                    foreach (string str in strs)
                                    {
                                        listConditionAdd.Add(new WfProcessCondition()
                                        {
                                            Id = BaseDBConfig.GetYitterId(),
                                            ProcessId = o.ProcessId,
                                            ProcessNodeLinkId = 0,
                                            Code = filter.field,
                                            Name = filter.fieldName,
                                            TxtType = 1,
                                            ConditionSymbol = filter.symbol,
                                            FeildValue1 = str,
                                            FeildValue2 = str,
                                        });
                                    }
                                }
                                else
                                {
                                    listConditionAdd.Add(new WfProcessCondition()
                                    {
                                        Id = BaseDBConfig.GetYitterId(),
                                        ProcessId = o.ProcessId,
                                        ProcessNodeLinkId = 0,
                                        Code = filter.field,
                                        Name = filter.fieldName,
                                        TxtType = 1,
                                        ConditionSymbol = filter.symbol,
                                        FeildValue1 = filter.value,
                                        FeildValue2 = filter.value,
                                    });
                                }
                            }
                        }
                    }
                    //判断是否有条件
                    else
                    {
                        if (line.filters != null && line.filters.Count > 0)
                        {
                            foreach (FilterModel filter in line.filters)
                            {
                                if (!string.IsNullOrEmpty(filter.field))
                                {
                                    string filterValue = filter.value;
                                    if (filterValue.Contains(","))
                                    {
                                        string[] strs = filterValue.Split(',');
                                        int strLength = strs.Length;
                                        int j = 1;
                                        foreach (string str in strs)
                                        {
                                            listConditionAdd.Add(new WfProcessCondition()
                                            {
                                                Id = BaseDBConfig.GetYitterId(),
                                                ProcessId = o.ProcessId,
                                                ProcessNodeLinkId = 0,
                                                Code = filter.field,
                                                Name = filter.fieldName,
                                                TxtType = 1,
                                                ConditionSymbol = filter.symbol,
                                                FeildValue1 = str,
                                                FeildValue2 = str,
                                            });
                                            long fValue = 0;
                                            long.TryParse(str, out fValue);

                                            if (filter.symbol.Equals("=="))
                                            {
                                                if (j == 1)
                                                {
                                                    strSqlCondition += "('" + filter.field + "' " + filter.symbol + " '" + str + "'";
                                                    strShowCondition += $"( {filter.fieldName} {filter.symbol} {str}";
                                                }
                                                else if (j == strLength)
                                                {
                                                    strSqlCondition += " || '" + filter.field + "' " + filter.symbol + " '" + str + "' )";
                                                    strShowCondition += $" 或 {filter.fieldName} {filter.symbol} {str} )";
                                                }
                                                else
                                                {
                                                    strSqlCondition += " || '" + filter.field + "' " + filter.symbol + " '" + str + "'";

                                                    strShowCondition += $" 或 {filter.fieldName} {filter.symbol} {str}";
                                                }
                                            }
                                            else
                                            {
                                                if (j == 1)
                                                {
                                                    strSqlCondition += "('" + filter.field + "' " + filter.symbol + " '" + str + "'";
                                                    strShowCondition += $"( {filter.fieldName} {filter.symbol} {str}";
                                                }
                                                else if (j == strLength)
                                                {
                                                    strSqlCondition += " && '" + filter.field + "' " + filter.symbol + " '" + str + "' )";
                                                    strShowCondition += $" 并且 {filter.fieldName} {filter.symbol} {str} )";
                                                }
                                                else
                                                {
                                                    strSqlCondition += " && '" + filter.field + "' " + filter.symbol + " '" + str + "'";

                                                    strShowCondition += $" 并且 {filter.fieldName} {filter.symbol} {str}";
                                                }
                                            }
                                            j++;
                                        }
                                    }
                                    else
                                    {
                                        listConditionAdd.Add(new WfProcessCondition()
                                        {
                                            Id = BaseDBConfig.GetYitterId(),
                                            ProcessId = o.ProcessId,
                                            ProcessNodeLinkId = 0,
                                            Code = filter.field,
                                            Name = filter.fieldName,
                                            TxtType = 1,
                                            ConditionSymbol = filter.symbol,
                                            FeildValue1 = filter.value,
                                            FeildValue2 = filter.value,
                                        });

                                        //条件采用 "{code} && (paymoney < 1000 || paymoney > 5000)" 根据子表数据内容替换
                                        //判断值类型
                                        long fValue = 0;
                                        long.TryParse(filter.value, out fValue);
                                        if (filter.value.Equals("0") || fValue > 0)
                                        {
                                            if (index == 0)
                                            {
                                                if(fValue.ToString().Length >= 15)
                                                {
                                                    strSqlCondition += "'" + filter.field + "' " + filter.symbol + " '" + filter.value + "'";
                                                    strShowCondition += $"'{filter.fieldName}' {filter.symbol} '{filter.value}'";
                                                }
                                                else
                                                {
                                                    strSqlCondition += "" + filter.field + " " + filter.symbol + " " + filter.value + "";
                                                    strShowCondition += $"{filter.fieldName} {filter.symbol} {filter.value}";
                                                }
                                                
                                            }
                                            else
                                            {
                                                if (fValue.ToString().Length >= 15)
                                                {
                                                    strSqlCondition += " && '" + filter.field + "' " + filter.symbol + " '" + filter.value + "'";
                                                    strShowCondition += $" 并且 '{filter.fieldName}' {filter.symbol} '{filter.value}'";
                                                }
                                                else
                                                {
                                                    strSqlCondition += " && " + filter.field + " " + filter.symbol + " " + filter.value + "";
                                                    strShowCondition += $" 并且 {filter.fieldName} {filter.symbol} {filter.value}";
                                                }
                                                
                                            }
                                        }
                                        else
                                        {
                                            if (index == 0)
                                            {
                                                strSqlCondition += "'" + filter.field + "' " + filter.symbol + " '" + filter.value + "'";
                                                strShowCondition += $"{filter.fieldName} {filter.symbol} {filter.value}";
                                            }
                                            else
                                            {
                                                strSqlCondition += " && '" + filter.field + "' " + filter.symbol + " '" + filter.value + "'";
                                                strShowCondition += $" 并且 {filter.fieldName} {filter.symbol} {filter.value}";
                                            }
                                        }
                                    }
                                }                               
                                index++;
                            }
                        }
                    }


                    if (ProcessNodeLinkId > 0)
                    {
                        //先删除
                        await this.Db.Updateable<WfProcessCondition>().SetColumns(f => new WfProcessCondition() { IsDeleted = true }).Where(f => f.ProcessNodeLinkId == ProcessNodeLinkId).ExecuteCommandAsync();
                        ////先删除
                        //await this.Db.Deleteable<WfProcessCondition>().Where(f => f.ProcessNodeLinkId == ProcessNodeLinkId).ExecuteCommandAsync();

                        objProcessNodeLink.SqlCondition = strSqlCondition;
                        objProcessNodeLink.ShowCondition = strShowCondition;
                        await this.Db.Updateable<WfProcessNodeLink>(objProcessNodeLink).ExecuteCommandAsync();
                    }
                    else
                    {
                        WfProcessNodeLink nodeLink = new WfProcessNodeLink()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = o.ProcessId,
                            FromId = line.from,
                            ToId = line.to,
                            SqlCondition = strSqlCondition,
                            ShowCondition = strShowCondition,
                            ConditionConfig = ""
                        };

                        await this.Db.Insertable<WfProcessNodeLink>(nodeLink).ExecuteCommandAsync();
                        ProcessNodeLinkId = nodeLink.Id;
                    }

                    if (listConditionAdd.Count > 0)
                    {
                        listConditionAdd.ForEach(f => f.ProcessNodeLinkId = ProcessNodeLinkId);
                        await this.Db.Insertable<WfProcessCondition>(listConditionAdd).ExecuteCommandAsync();
                    }
                }

                //判断是否有删除的节点信息
                var listDeleteNode = listOldStr.Except(listNewStr).ToList();
                if (listDeleteNode.Count > 0)
                {
                    List<WfProcessNodeLink> listNodeLinkUpdate = new List<WfProcessNodeLink>();
                    List<WfProcessCondition> listConditionUpdate = new List<WfProcessCondition>();

                    foreach (var str in listDeleteNode)
                    {
                        long fromId = long.Parse(str.Split('-')[0]);
                        long toId = long.Parse(str.Split('-')[1]);
                        WfProcessNodeLink objProcessNodeLink = listNodeLink.Where(f => f.IsDeleted == false && f.ProcessId == o.ProcessId && f.FromId == fromId && f.ToId == toId).FirstOrDefault();
                        if (objProcessNodeLink != null)
                        {
                            objProcessNodeLink.IsDeleted = true;
                            listNodeLinkUpdate.Add(objProcessNodeLink);

                            List<WfProcessCondition> listOldCondition = await this.Db.Queryable<WfProcessCondition>().Where(f => f.ProcessNodeLinkId == objProcessNodeLink.Id).ToListAsync();
                            if (listOldCondition.Count > 0)
                            {
                                listOldCondition.ForEach(f => f.IsDeleted = true);
                                listConditionUpdate.AddRange(listOldCondition);
                            }
                        }
                    }

                    if (listNodeLinkUpdate.Count > 0)
                    {
                        await this.Db.Updateable<WfProcessNodeLink>(listNodeLinkUpdate).ExecuteCommandAsync();
                    }
                    if (listConditionUpdate.Count > 0)
                    {
                        await this.Db.Updateable<WfProcessCondition>(listConditionUpdate).ExecuteCommandAsync();
                    }
                }

                //根据流程节点Id生成或修改状态值信息
                Result<string> r = await GenerateStatuzAndReturn(o.ProcessId,o.NodeConfig);
                if (r.flag == 1)
                {
                    unitOfWorkManage.CommitTran();
                    return Result<string>.Success("设置成功");
                }
                else
                {
                    unitOfWorkManage.RollbackTran();
                    return r;
                }
            }
            catch (Exception ex)
            {
                errorCount++;
                errorMsg += ex.Message;
                unitOfWorkManage.RollbackTran();
                return Result<string>.Fail($"设置失败,原因：{errorMsg}");
                throw;
            }

        }

        /// <summary>
        /// 保存资金来源信息
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public async Task<Result<string>> SourceFundInputDataSet(SourceFundInputModel o)
        {
            WfProcess objProcess = await this.Db.Queryable<WfProcess>().Where(f => f.Id == o.ProcessId).FirstAsync();
            if (objProcess == null)
            {
                return Result<string>.Fail("非法操作,流程Id（ProcessId）传入有误!");
            }

            if (o.InputDataType == 3)
            {
                if (string.IsNullOrEmpty(o.FieldCode) || o.TypeCode <= 0 || o.TypeCode > 9999999)
                {
                    return Result<string>.Fail("当选择“资金分配指定字段”时，资金分配指定的字段编码必须在1~9999999!");
                }
            }

            if (o.InputDataType == 2)
            {
                if (string.IsNullOrEmpty(o.ProjectListCode))
                {
                    return Result<string>.Fail("当选择“项目清单数据”时，必须选择项目列表编码");
                }
            }

            objProcess.InputDataType = o.InputDataType;
            objProcess.FieldCode = o.FieldCode;
            objProcess.TypeCode = o.TypeCode;
            objProcess.WriteSourceFundStatuz = o.WriteSourceFundStatuz;

            await this.Db.Updateable<WfProcess>(objProcess).ExecuteCommandAsync();
            if (o.ListSet.Count > 0)
            {
                //先删除
                await this.Db.Updateable<WfFundProcessNodeSet>().SetColumns(f => new WfFundProcessNodeSet() { IsDeleted = true }).Where(f => f.ProcessId == o.ProcessId).ExecuteCommandAsync();

                List<WfFundProcessNodeSet> listFundNodeSetAdd = new List<WfFundProcessNodeSet>();
                foreach(FundProcessNodeSetModel set in o.ListSet)
                {
                    listFundNodeSetAdd.Add(new WfFundProcessNodeSet()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        ProcessId = o.ProcessId,
                        ProcessNodeId = set.ProcessNodeId,
                        Statuz = "1"
                    });
                }

                if (listFundNodeSetAdd.Count > 0)
                {
                    await this.Db.Insertable<WfFundProcessNodeSet>(listFundNodeSetAdd).ExecuteCommandAsync();
                }
            }

            return Result<string>.Success("资金来源配置成功");

        }

        /// <summary>
        /// 根据流程节点Id生成或修改状态值信息(方法弃用，已经合并到下面方法)
        /// </summary>
        /// <param name="processId">流程节点Id</param>
        /// <returns></returns>
        private async Task<Result<string>> GenerateStatuz(long processId)
        {
            List<WfProcessNodeLinkDto> list = await this.Db.Queryable<WfProcessNodeLink>()
                .InnerJoin<WfProcessNode>((PLN, PN) => PLN.FromId == PN.Id)
                .Where((PLN, PN) => PLN.ProcessId == processId && PN.NodeType != 3)
                .Select((PLN, PN) => new WfProcessNodeLinkDto()
                {
                    NodeId = PN.Id,
                    NodeName = PN.NodeName,
                    NodeShowName = PN.NodeShowName,
                    NodeType = PN.NodeType.Value
                }).ToListAsync();
            if (list.Count > 0)
            {
                List<WfProcessStatuz> listAdd = new List<WfProcessStatuz>();
                int statuz = 0;
                string StatuzDescPre = null;
                string StatuzDescNext = null;
                List<WfProcessStatuz> listStatuz = await this.Db.Queryable<WfProcessStatuz>().Where(f => f.ProcessId == processId).ToListAsync();
                foreach (WfProcessNodeLinkDto f in list)
                {
                    if (listStatuz.Count == 0)
                    {
                        statuz += 2;
                        if (f.NodeType == 1)
                        {
                            listAdd.Add(new WfProcessStatuz()
                            {
                                Id = BaseDBConfig.GetYitterId(),
                                ProcessId = processId,
                                ProcessNodeId = f.NodeId,
                                Statuz = 0,
                                StatuzDesc = "项目申报中",
                                WaitOrBack = 3,
                                Sort = 0
                            });

                            listAdd.Add(new WfProcessStatuz()
                            {
                                Id = BaseDBConfig.GetYitterId(),
                                ProcessId = processId,
                                ProcessNodeId = f.NodeId,
                                Statuz = 1000,
                                StatuzDesc = "审批结束",
                                WaitOrBack = 4,
                                Sort = 1000
                            });
                        }
                        else
                        {
                            if (statuz % 2 == 0)
                            {
                                statuz += 1;
                            }
                            StatuzDescPre = "等待" + f.NodeShowName;
                            listAdd.Add(new WfProcessStatuz()
                            {
                                Id = BaseDBConfig.GetYitterId(),
                                ProcessId = processId,
                                ProcessNodeId = f.NodeId,
                                Statuz = statuz,
                                StatuzDesc = StatuzDescPre,
                                WaitOrBack = 1,
                                Sort = statuz
                            });

                            statuz += 2;
                            if (statuz % 2 != 0)
                            {
                                statuz += 1;
                            }
                            StatuzDescNext = f.NodeShowName + "退回";
                            listAdd.Add(new WfProcessStatuz()
                            {
                                Id = BaseDBConfig.GetYitterId(),
                                ProcessId = processId,
                                ProcessNodeId = f.NodeId,
                                Statuz = statuz,
                                StatuzDesc = StatuzDescNext,
                                WaitOrBack = 2,
                                Sort = statuz
                            });
                        }
                    }
                    else
                    {
                        var obj = listStatuz.Where(a => a.ProcessNodeId == f.NodeId).ToList().FirstOrDefault();
                        if(obj == null)
                        {
                            if(f.NodeType != 1)
                            {
                                statuz += 2;
                                if (statuz % 2 == 0)
                                {
                                    statuz += 1;
                                }
                                StatuzDescPre = "等待" + f.NodeShowName;
                                listAdd.Add(new WfProcessStatuz()
                                {
                                    Id = BaseDBConfig.GetYitterId(),
                                    ProcessId = processId,
                                    ProcessNodeId = f.NodeId,
                                    Statuz = statuz,
                                    StatuzDesc = StatuzDescPre,
                                    WaitOrBack = 1,
                                    Sort = statuz
                                });

                                statuz += 2;
                                if (statuz % 2 != 0)
                                {
                                    statuz += 1;
                                }
                                StatuzDescNext = f.NodeShowName + "退回";
                                listAdd.Add(new WfProcessStatuz()
                                {
                                    Id = BaseDBConfig.GetYitterId(),
                                    ProcessId = processId,
                                    ProcessNodeId = f.NodeId,
                                    Statuz = statuz,
                                    StatuzDesc = StatuzDescNext,
                                    WaitOrBack = 2,
                                    Sort = statuz
                                });
                            }
                        }
                        else
                        {
                            statuz = obj.Statuz;
                        }
                    }
                }

                //批量添加
                await this.Db.Insertable<WfProcessStatuz>(listAdd).ExecuteCommandAsync();

                List<long> listOld = listStatuz.Select(f => f.ProcessNodeId.Value).ToList();
                List<long> listNew = list.Select(f => f.NodeId).ToList();
                var expectedList = listOld.Except(listNew).ToList();
                if (expectedList.Count > 0)
                {
                   await this.Db.Deleteable<WfProcessStatuz>(f => expectedList.Contains(f.ProcessNodeId.Value) && f.ProcessId == processId).ExecuteCommandAsync();
                }

            }
            else
            {
                //清空该流程下所有数据
               await this.Db.Deleteable<WfProcessStatuz>(f => f.ProcessId == processId).ExecuteCommandAsync();
            }
            return Result<string>.Success("生成流程状态成功");
        }

        /// <summary>
        /// 根据流程Id生成“审批流程节点退回方式设置表”信息
        /// </summary>
        /// <param name="processId"></param>
        /// <param name="nodeConfig"></param>
        /// <returns></returns>
        private async Task<Result<string>> GenerateStatuzAndReturn(long processId,string nodeConfig)
        {
            WfProcess objProcess = await this.Db.Queryable<WfProcess>().Where(f => f.Id == processId).FirstAsync();
            if(objProcess != null)
            {
                //根据流程Id获取审批流程节点退回方式数据信息
                List<WfProcessReturnSet> listSet = await this.Db.Queryable<WfProcessReturnSet>().Where(f => f.ProcessId == processId && f.IsDeleted == false).ToListAsync();
                //根据流程Id获取审批流程节点状态数据信息
                List<WfProcessStatuz> listStatuz = await this.Db.Queryable<WfProcessStatuz>().Where(f => f.ProcessId == processId && f.IsDeleted == false).ToListAsync();

                //获取所有节点信息
                List<WfProcessNode> listNode = await this.Db.Queryable<WfProcessNode>().Where(f => f.IsDeleted == false).ToListAsync();
                //审批流程节点退回方式设置表
                List<WfProcessReturnSet> listReturnSetAdd = new List<WfProcessReturnSet>();
                //审批流程节点状态表
                List<WfProcessStatuz> listStatuzAdd = new List<WfProcessStatuz>();

                var listFirstQuery = await this.Db.Queryable<WfProcessNodeLink>()
                    .InnerJoin<WfProcess>((PNL, P) => PNL.ProcessId == P.Id)
                    .InnerJoin<WfProcessNode>((PNL, P, PN) => PNL.FromId == PN.Id)
                    .Where((PNL, P, PN) => PNL.IsDeleted == false && PNL.ProcessId == processId)
                    .Select((PNL, P, PN) => new
                    {
                        ModuleId = P.ModuleId,
                        ProcessId = PNL.ProcessId,
                        ProcessNodeId = PNL.FromId,
                        Sort = PN.Sort,
                        IsBegin = PN.IsBegin,
                        NodeShowName = PN.NodeShowName,
                    }).ToListAsync();

                var listSecondQuery = await this.Db.Queryable<WfProcessNodeLink>()
                   .InnerJoin<WfProcess>((PNL, P) => PNL.ProcessId == P.Id)
                   .InnerJoin<WfProcessNode>((PNL, P, PN) => PNL.ToId == PN.Id)
                   .Where((PNL, P, PN) => PNL.IsDeleted == false && PNL.ProcessId == processId)
                   .Select((PNL, P, PN) => new
                   {
                       ModuleId = P.ModuleId,
                       ProcessId = PNL.ProcessId,
                       ProcessNodeId = PNL.ToId,
                       Sort = PN.Sort,
                       IsBegin = PN.IsBegin,
                       NodeShowName = PN.NodeShowName,
                   }).ToListAsync();
                var listUnionQuery = listFirstQuery.Union(listSecondQuery).ToList();

                #region 处理审批流程节点退回方式设置表
                if (listSet.Count == 0)
                {
                    foreach (var item in listUnionQuery)
                    {
                        listReturnSetAdd.Add(new WfProcessReturnSet()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ModuleId = item.ModuleId,
                            ProcessId = item.ProcessId,
                            ProcessNodeId = item.ProcessNodeId,
                            BackWay = 1,
                            Sort = item.Sort
                        });
                    }
                }
                else
                {
                    List<long> listOld = listSet.Select(f => f.ProcessNodeId.Value).ToList();
                    List<long> listNew = listUnionQuery.Select(f => f.ProcessNodeId.Value).ToList();
                    var listDeleteId = listOld.Except(listNew).ToList();
                    if (listDeleteId.Count > 0)
                    {
                        await this.Db.Updateable<WfProcessReturnSet>().SetColumns(f => new WfProcessReturnSet(){ IsDeleted = true }).Where(f => listDeleteId.Contains(f.ProcessNodeId.Value) && f.ProcessId == processId).ExecuteCommandAsync();
                    }
                    var listAddId = listNew.Except(listOld).ToList();
                    if (listAddId.Count > 0)
                    {
                        foreach (var item in listAddId) 
                        {
                            var objNode = listNode.Where(f => f.Id == item).FirstOrDefault();
                            if (objNode != null)
                            {
                                listReturnSetAdd.Add(new WfProcessReturnSet()
                                {
                                    Id = BaseDBConfig.GetYitterId(),
                                    ModuleId = objProcess.ModuleId,
                                    ProcessId = objProcess.Id,
                                    ProcessNodeId = item,
                                    BackWay = 1,
                                    Sort = objNode.Sort
                                });
                            }
                        }
                    }
                }

                //处理一个流程节点
                List<NodeModel> listNodeModel = JsonHelper.JsonToObj<List<NodeModel>>(nodeConfig);
                if(listNodeModel.Count == 1)
                {
                    listReturnSetAdd.Add(new WfProcessReturnSet()
                    {
                        Id = BaseDBConfig.GetYitterId(),
                        ModuleId = objProcess.ModuleId,
                        ProcessId = objProcess.Id,
                        ProcessNodeId = listNodeModel[0].id,
                        BackWay = 1,
                        Sort = 0
                    });
                }


                if (listReturnSetAdd.Count > 0)
                {
                    await this.Db.Insertable<WfProcessReturnSet>(listReturnSetAdd).ExecuteCommandAsync();
                }
                #endregion

                #region 处理审批流程节点状态表
                int statuz = 0;
                string StatuzDescPre = null;
                string StatuzDescNext = null;

                if(listStatuz.Count == 0)
                {
                    if(listUnionQuery.Count == 0 && listNodeModel.Count == 1)
                    {
                        listStatuzAdd.Add(new WfProcessStatuz()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = processId,
                            ProcessNodeId = listNodeModel[0].id,
                            Statuz = 0,
                            StatuzDesc = "项目申报中",
                            WaitOrBack = 3,
                            Sort = 0
                        });

                        listStatuzAdd.Add(new WfProcessStatuz()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = processId,
                            ProcessNodeId = listNodeModel[0].id,
                            Statuz = 1000,
                            StatuzDesc = "审批结束",
                            WaitOrBack = 4,
                            Sort = 1000
                        });
                    }
                    else
                    {
                        foreach (var item in listUnionQuery)
                        {
                            if (item.IsBegin == 1)
                            {
                                statuz += 2;
                                listStatuzAdd.Add(new WfProcessStatuz()
                                {
                                    Id = BaseDBConfig.GetYitterId(),
                                    ProcessId = processId,
                                    ProcessNodeId = item.ProcessNodeId,
                                    Statuz = 0,
                                    StatuzDesc = "项目申报中",
                                    WaitOrBack = 3,
                                    Sort = 0
                                });

                                listStatuzAdd.Add(new WfProcessStatuz()
                                {
                                    Id = BaseDBConfig.GetYitterId(),
                                    ProcessId = processId,
                                    ProcessNodeId = item.ProcessNodeId,
                                    Statuz = 1000,
                                    StatuzDesc = "审批结束",
                                    WaitOrBack = 4,
                                    Sort = 1000
                                });
                            }
                            else
                            {
                                if (statuz % 2 != 0)
                                {
                                    statuz += 1;
                                }
                                StatuzDescPre = "等待" + item.NodeShowName;
                                listStatuzAdd.Add(new WfProcessStatuz()
                                {
                                    Id = BaseDBConfig.GetYitterId(),
                                    ProcessId = processId,
                                    ProcessNodeId = item.ProcessNodeId,
                                    Statuz = statuz,
                                    StatuzDesc = StatuzDescPre,
                                    WaitOrBack = 1,
                                    Sort = statuz
                                });

                                statuz += 2;
                                if (statuz % 2 == 0)
                                {
                                    statuz += 1;
                                }
                                StatuzDescNext = item.NodeShowName + "退回";
                                listStatuzAdd.Add(new WfProcessStatuz()
                                {
                                    Id = BaseDBConfig.GetYitterId(),
                                    ProcessId = processId,
                                    ProcessNodeId = item.ProcessNodeId,
                                    Statuz = statuz,
                                    StatuzDesc = StatuzDescNext,
                                    WaitOrBack = 2,
                                    Sort = statuz
                                });
                            }
                        }
                    }
                    
                }
                else
                {
                    List<long> listOld = listStatuz.Select(f => f.ProcessNodeId.Value).Distinct().ToList();
                    List<long> listNew = listUnionQuery.Select(f => f.ProcessNodeId.Value).ToList();
                    var listDeleteId = listOld.Except(listNew).ToList();
                    if (listDeleteId.Count > 0)
                    {
                        await this.Db.Updateable<WfProcessStatuz>().SetColumns(f => new WfProcessStatuz() { IsDeleted = true }).Where(f => listDeleteId.Contains(f.ProcessNodeId.Value) && f.ProcessId == processId).ExecuteCommandAsync();
                    }
                    var listAddId = listNew.Except(listOld).ToList();
                    if (listAddId.Count > 0)
                    {
                        foreach (var item in listAddId)
                        {
                            var objNode = listNode.Where(f => f.Id == item).FirstOrDefault();
                            if (objNode != null)
                            {
                                if(objNode.IsBegin == 1)
                                {
                                    statuz += 2;
                                    listStatuzAdd.Add(new WfProcessStatuz()
                                    {
                                        Id = BaseDBConfig.GetYitterId(),
                                        ProcessId = processId,
                                        ProcessNodeId = item,
                                        Statuz = 0,
                                        StatuzDesc = "项目申报中",
                                        WaitOrBack = 3,
                                        Sort = 0
                                    });

                                    listStatuzAdd.Add(new WfProcessStatuz()
                                    {
                                        Id = BaseDBConfig.GetYitterId(),
                                        ProcessId = processId,
                                        ProcessNodeId = item,
                                        Statuz = 1000,
                                        StatuzDesc = "审批结束",
                                        WaitOrBack = 4,
                                        Sort = 1000
                                    });
                                }
                                else
                                {
                                    //查询listStatuz中statuz最大值
                                    statuz = listStatuz.Where(f => f.Statuz != 1000).Max(f => f.Statuz);
                                    statuz += 2;
                                    if (statuz % 2 != 0)
                                    {
                                        statuz += 1;
                                    }
                                    if (statuz >= 1000)
                                    {
                                        return Result<string>.Fail("生成状态失败!");
                                    }
                                    StatuzDescPre = "等待" + objNode.NodeShowName;
                                    listStatuzAdd.Add(new WfProcessStatuz()
                                    {
                                        Id = BaseDBConfig.GetYitterId(),
                                        ProcessId = processId,
                                        ProcessNodeId = item,
                                        Statuz = statuz,
                                        StatuzDesc = StatuzDescPre,
                                        WaitOrBack = 1,
                                        Sort = statuz
                                    });

                                    statuz += 2;
                                    if (statuz % 2 == 0)
                                    {
                                        statuz += 1;
                                    }
                                    if (statuz >= 1000)
                                    {
                                        return Result<string>.Fail("生成状态失败!");
                                    }
                                    StatuzDescNext = objNode.NodeShowName + "退回";
                                    listStatuzAdd.Add(new WfProcessStatuz()
                                    {
                                        Id = BaseDBConfig.GetYitterId(),
                                        ProcessId = processId,
                                        ProcessNodeId = item,
                                        Statuz = statuz,
                                        StatuzDesc = StatuzDescNext,
                                        WaitOrBack = 2,
                                        Sort = statuz
                                    });
                                }
                            }
                        }
                    }

                    if (listUnionQuery.Count == 0 && listNodeModel.Count == 1)
                    {
                        listStatuzAdd.Add(new WfProcessStatuz()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = processId,
                            ProcessNodeId = listNodeModel[0].id,
                            Statuz = 0,
                            StatuzDesc = "项目申报中",
                            WaitOrBack = 3,
                            Sort = 0
                        });

                        listStatuzAdd.Add(new WfProcessStatuz()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            ProcessId = processId,
                            ProcessNodeId = listNodeModel[0].id,
                            Statuz = 1000,
                            StatuzDesc = "审批结束",
                            WaitOrBack = 4,
                            Sort = 1000
                        });
                    }
                }
                if (listStatuzAdd.Count > 0)
                {
                    await this.Db.Insertable<WfProcessStatuz>(listStatuzAdd).ExecuteCommandAsync();
                }
                #endregion
            }
            return Result<string>.Success("生成退回方式成功");
        }

        /// <summary>
        /// 组装查询条件
        /// </summary>
        /// <param name="param">WfProcessParam实体参数</param>
        /// <returns></returns>
        private Expression<Func<WfProcess, bool>> ListFilter(WfProcessParam param)
        {
            var expression = LinqExtensions.True<WfProcess>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.AndNew(t => idArr.Contains(t.Id));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    expression = expression.AndNew(f => f.ProcessName.Contains(param.Name));
                }
                if (param.ModuleId > 0)
                {
                    expression = expression.AndNew(f => f.ModuleId == param.ModuleId);
                }
                if (param.IsSubmitStatuz)
                {
                    expression = expression.AndNew(f => f.Statuz == 1);
                }
                if (param.IsOpen > 0)
                {
                    expression = expression.AndNew(f => f.IsOpen == param.IsOpen);
                }
            }
            return expression;
        }

        /// <summary>
        /// 根据当前用户获取首页审批数据
        /// </summary>
        /// <returns></returns>
        public async Task<Result> GetHomePageData()
        {
            //填报数据
            List<WfProcessIndexView> listFilling = new List<WfProcessIndexView>();

            //审批数据
            List<WfProcessIndexView> listApproval = new List<WfProcessIndexView>();

            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";

            //获取禁用流程表数据
            var listProcessId = await this.Db.Queryable<WfChildUnitDisable>().Where(f => f.IsDeleted == false && f.DisableUnitId == user.UnitId).Select(f => f.ProcessId).ToListAsync();

            //读取所有流程所用到的节点信息
            List<WfProcessStatuz> listStatuz = await this.Db.Queryable<WfProcessStatuz>().Where(f => f.IsDeleted == false).ToListAsync();
            List<long> listNodeId = listStatuz.Select(f => f.ProcessNodeId.Value).ToList();

            var list = await this.Db.Queryable<WfProjectAuditUser>()
                        .InnerJoin<WfModule>((PAU, M) => PAU.ModuleId == M.Id)
                        .InnerJoin<WfProcess>((PAU, M, P) => PAU.ProcessId == P.Id)
                        .InnerJoin<WfProcessNode>((PAU, M, P, PN) => PAU.ProcessNodeId == PN.Id)
                        .Where((PAU, M, P, PN) =>
                            PAU.IsDeleted == false &&
                            M.IsDeleted == false &&
                            P.IsDeleted == false &&
                            PN.IsDeleted == false &&
                            PAU.AuditUserId == user.ID &&
                            listNodeId.Contains(PN.Id) &&
                            !listProcessId.Contains(P.Id))
                        .OrderBy((PAU, M, P, PN) => new { M.Sort, P.PSort, NodeSort = PN.Sort })
                        .Select((PAU, M, P, PN) => new
                        {
                            ModuleId = M.Id,
                            ModuleName = M.Name,
                            ProcessId = PAU.ProcessId,
                            ProcessName = P.ProcessName,
                            ProcessNodeId = PAU.ProcessNodeId,
                            NodeShowName = PN.NodeShowName,
                            AuditUserId = PAU.AuditUserId,
                            NodeType = PN.NodeType
                        }).ToListAsync();

            //判断此用户是否存在填报的数据
            var listUserFilling = list.Where(f => f.NodeType == 1).ToList();
            if (listUserFilling.Count > 0)
            {
                //流程
                var listGroupProcess = listUserFilling.GroupBy(f => new { f.ModuleId, f.ModuleName, f.ProcessId, f.ProcessName })
                .Select(group => new
                {
                    ModuleId = group.Key.ModuleId,
                    ModuleName = group.Key.ModuleName,
                    ProcessId = group.Key.ProcessId,
                    ProcessName = group.Key.ProcessName,
                }).ToList();

                //遍历
                foreach(var p in listGroupProcess)
                {
                    //查询起始节点
                    long processNodeId = 0;
                    var processNode = listUserFilling.Where(f => f.ProcessId == p.ProcessId).FirstOrDefault();
                    if (processNode != null)
                    {
                        processNodeId = processNode.ProcessNodeId.Value;
                    }

                    List<ProcessView> listPview = await this.Db.Queryable<WfProjectDeclaration>()
                         .Where(f => f.ProcessId == p.ProcessId && f.IsDeleted == false && f.CreateId == user.ID && f.Statuz != 1000)
                         .OrderByDescending(f => f.ModifyTime)
                         .Select(f => new ProcessView()
                         {
                             ModuleId = p.ModuleId,
                             ProcessId = p.ProcessId.Value,
                             ProcessNodeId = processNodeId,
                             ProjectDeclarationId = f.Id,
                             ProjectName = f.ProjectName,
                             Statuz = f.Statuz,
                             StatuzDesc = f.StatuzDesc,
                             CreateDate = f.ModifyTime,
                             OperateType = SqlFunc.IF(f.ToId == processNodeId).Return(1).End(2)
                         }).ToListAsync();

                    if (listPview.Count > 0)
                    {
                        //填报列表
                        listFilling.Add(new WfProcessIndexView() { ProcessId = p.ProcessId.Value, ProcessName = p.ProcessName, listProcessView = listPview, Num = listPview.Count });
                    }
                }
            }

            //判断此用户是否存在审批数据
            var listUserApproval = list.Where(f => f.NodeType == 2).ToList();
            if (listUserApproval.Count > 0)
            {
                var listApprovalData = await this.Db.Queryable<WfProjectAuditUser>()
                     .InnerJoin<WfProcess>((PAU, P) => PAU.ProcessId == P.Id)
                     .InnerJoin<WfProjectDeclaration>((PAU, P, PD) => PAU.ProcessId == PD.ProcessId && PAU.ProcessNodeId == PD.ToId && (PD.AppointProcessNodeId != PD.ToId || (PD.AppointProcessNodeId == PD.ToId && PD.AuditUserIds.Contains(user.ID.ToString()) && !PD.AuditedUserIds.Contains(user.ID.ToString()))))
                     .LeftJoin<PUnit>((PAU, P, PD, U) => PD.UnitId == U.Id)
                    .Where((PAU, P, PD, U) =>PAU.IsDeleted == false && P.IsDeleted == false && PD.IsDeleted == false && PAU.AuditUserId == user.ID)
                    .Select((PAU, P, PD, U) => new
                    {
                         ModuleId = PAU.ModuleId.Value,
                         ProcessId = PAU.ProcessId.Value,
                         ProcessNodeId = PAU.ProcessNodeId.Value,
                         ProjectDeclarationId = PD.Id,
                         ProcessName = P.ProcessName,
                         ProjectName = PD.ProjectName,
                         StatuzDesc = PD.StatuzDesc,
                         CreateDate = PD.ModifyTime,
                         Statuz = PD.Statuz,
                         OperateType = 1,
                         UnitId = PD.UnitId,
                         UnitPid = U.PId
                    }).ToListAsync();

                var listGroupProcess = listApprovalData.GroupBy(f => new {f.ProcessId, f.ProcessName })
                                                       .Select(group => new
                                                       {
                                                           ProcessId = group.Key.ProcessId,
                                                           ProcessName = group.Key.ProcessName,
                                                       }).ToList();
                //遍历
                foreach (var p in listGroupProcess)
                {
                    List<ProcessView> listPview = listApprovalData.Where(f => f.ProcessId == p.ProcessId && (f.UnitId == user.UnitId || f.UnitPid == user.UnitId)).Select(f => new ProcessView() {
                        ModuleId = f.ModuleId,
                        ProcessId = f.ProcessId,
                        ProcessNodeId = f.ProcessNodeId,
                        ProjectDeclarationId = f.ProjectDeclarationId,
                        ProjectName = f.ProjectName,
                        Statuz = f.Statuz,
                        StatuzDesc = f.StatuzDesc,
                        CreateDate = f.CreateDate,
                        OperateType = f.OperateType
                    }).ToList();

                    if (listPview.Count > 0)
                    {
                        //审批列表
                        listApproval.Add(new WfProcessIndexView() { ProcessId = p.ProcessId, ProcessName = p.ProcessName, listProcessView = listPview,Num = listPview.Count });
                    }
                    
                }
            }
            r.data.other = new { listFilling = listFilling, listApproval = listApproval };
            return r;
        }
    }
}

