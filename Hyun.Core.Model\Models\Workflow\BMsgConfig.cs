﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///消息配置
    ///</summary>
    [SugarTable("b_MsgConfig","消息配置")]
    public class BMsgConfig : BaseEntity
    {

          public BMsgConfig()
          {

          }

           /// <summary>
           ///一级导航
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? OneNavigation { get; set; }

           /// <summary>
           ///二级导航
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? TwoNavigation { get; set; }

           /// <summary>
           ///页面Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? PageId { get; set; }

           /// <summary>
           ///页面名称
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string PageName { get; set; }

           /// <summary>
           ///消息编号
          /// </summary>
          [SugarColumn(Length = 63,IsNullable = true)]
          public string MsgCode { get; set; }

           /// <summary>
           ///消息说明
          /// </summary>
          [SugarColumn(Length = 1024,IsNullable = true)]
          public string MsgExplain { get; set; }

           /// <summary>
           ///消息说明具体显示
          /// </summary>
          [SugarColumn(Length = 1024,IsNullable = true)]
          public string MsgShowExplain { get; set; }

           /// <summary>
           ///总开关（1：启用，2：禁用）
          /// </summary>
          public int MainSwitch { get; set; } = 2;

           /// <summary>
           ///短信开关（1：启用，2：禁用）
          /// </summary>
          public int MsgSwitch { get; set; } = 2;

           /// <summary>
           ///短信模版
          /// </summary>
          [SugarColumn(Length = 1024,IsNullable = true)]
          public string MsgTemplate { get; set; }

           /// <summary>
           ///微信开关（1：启用，2：禁用）
          /// </summary>
          public int WeChatSwitch { get; set; } = 2;

           /// <summary>
           ///微信模版Id
          /// </summary>
          public string WeChatTemplateId { get; set; }

           /// <summary>
           ///微信模版
          /// </summary>
          [SugarColumn(Length = 1024,IsNullable = true)]
          public string WeChatTemplate { get; set; }

           /// <summary>
           ///微信跳转页面
          /// </summary>
          [SugarColumn(Length = 127,IsNullable = true)]
          public string WeChatRedirectPage { get; set; }

           /// <summary>
           ///用户端是否显示（1：是，2：否）
          /// </summary>
          public int ClientIsShow { get; set; } = 2;

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

