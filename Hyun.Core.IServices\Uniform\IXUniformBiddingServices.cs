﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///XUniformBidding接口方法
    ///</summary>
    public interface IXUniformBiddingServices : IBaseServices<XUniformBidding>
    {

        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="o">XUniformBuy对象</param>
        /// <returns></returns>
        Task<Result<string>> InsertUpdate(XUniformBiddingDto o);


        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">XUniformBuyParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<XUniformBidding>> GetPaged(XUniformBiddingParam param);

        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result<string>> Submit(long id);

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result<string>> DeleteById(long id);

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> Audit(BuyAuditModel o);

        /// <summary>
        /// 撤销、退回
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> Revoke(BuyRevokeModel o);


        Task<XUniformBidding> GetByBuyId(long id);

        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> UpdateAttachmentDelete(XUniformBiddingDto model);
    }
}

