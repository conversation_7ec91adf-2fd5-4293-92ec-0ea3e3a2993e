﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models.Uniform;
using Hyun.Core.IServices.Uniform;
using System.Collections.Generic;
using Hyun.Old.Util;
namespace Hyun.Core.Api
{

    /// <summary>
    /// 校服评价
    /// </summary>
    [Route("api/hyun/xuniformevaluate")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformEvaluateController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformEvaluateServices ixuniformevaluateservicesManager;
        private readonly IXUniformPurchaseServices ixuniformpurchaseManager;
        private readonly IPUnitServices ipunitManager;
        private readonly IXUniformShelfServices uniformshelfManager;
        private readonly IXUniformPurchaseGradeServices uniformpurchasegradeManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IPSchoolExtensionServices schoolExtensionManager;

        public XUniformEvaluateController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IXUniformEvaluateServices _ixuniformevaluateservicesManager, IXUniformPurchaseServices _ixuniformpurchaseManager, IPUnitServices _ipunitManager, IXUniformShelfServices _uniformshelfManager, IXUniformPurchaseGradeServices _uniformpurchasegradeManager, IBDictionaryServices _dictionaryManager, IPSchoolExtensionServices _schoolExtensionManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ixuniformevaluateservicesManager = _ixuniformevaluateservicesManager;
            ixuniformpurchaseManager = _ixuniformpurchaseManager;
            ipunitManager = _ipunitManager;
            uniformshelfManager = _uniformshelfManager;
            uniformpurchasegradeManager = _uniformpurchasegradeManager;
            dictionaryManager = _dictionaryManager;
            schoolExtensionManager = _schoolExtensionManager;
        }


        /// <summary>
        /// 查询校服评价列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getevaluatepaged")]
        public async Task<Result<List<XUniformPurchaseEvaluateDto>>> GetEvaluatePaged([FromBody] XUniformEvaluateParam param)
        {
            var msgdata = new Result<List<XUniformPurchaseEvaluateDto>>();
            param.SchoolId = user.UnitId;
            PageModel<XUniformPurchaseEvaluateDto> pg = await ixuniformpurchaseManager.GetEvaluatePaged(param);
            if (param.isFirst)
            {
                //供应商
                var listCompany = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listSupplier = listCompany.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                msgdata = baseSucc(mapper.Map<List<XUniformPurchaseEvaluateDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listSupplier = listSupplier });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }


        /// <summary>
        /// 创建修改评价截止时间
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("launch")]
        public async Task<Result<string>> EvaluateLaunch([FromBody] XUniformEvaluateModel obj)
        {
            var r = await ixuniformevaluateservicesManager.InsertUpdate(obj);
            return r;
        }

        /// <summary>
        /// 单位查看评价列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getevaluatelist")]
        public async Task<Result<List<XUniformPurchaseEvaluateDto>>> GetEvaluateList([FromBody] XUniformEvaluateParam param)
        {
            var msgdata = new Result<List<XUniformPurchaseEvaluateDto>>();
            param.SchoolId = user.UnitId;
            PageModel<XUniformPurchaseEvaluateDto> pg = await ixuniformpurchaseManager.GetEvaluateList(param);
            if (param.isFirst)
            {
                //状态
                var listEvaluateStatuz = EnumExtensions.EnumToList<UniformEvaluateStatuzEnum>();
                List<dropdownModel> listStatuz = listEvaluateStatuz.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //供应商
                var listCompany = await ipunitManager.Find(f => f.UnitType == UnitTypes.Company.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                List<dropdownModel> listSupplier = listCompany.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();

                msgdata = baseSucc(mapper.Map<List<XUniformPurchaseEvaluateDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listStatuz = listStatuz, listSupplier = listSupplier });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 获取评价明细列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getevaluatedetaillist")]
        public async Task<Result<List<XUniformEvaluateDto>>> GetEvaluateDetailList([FromBody] XUniformEvaluateDetailParam param)
        {
            var msgdata = new Result<List<XUniformEvaluateDto>>();
            PageModel<XUniformEvaluateDto> pg = await ixuniformevaluateservicesManager.GetEvaluateDetailPaged(param);
            string tipMsg = "采购批次：";
            XUniformEvaluateDto evaluateSum = new XUniformEvaluateDto();
            evaluateSum.UserTypename = "总评得分：";
            //计算总计
            if (pg.dataCount > 0)
            {
                tipMsg = $"采购批次：{pg.data[0].PurchaseNo}";

                param.pageIndex = 1;
                param.pageSize = int.MaxValue;
                PageModel<XUniformEvaluateDto> pgSum = await ixuniformevaluateservicesManager.GetEvaluateDetailPaged(param);
                List<XUniformEvaluateDto> listSum = pgSum.data;
                
                evaluateSum.Score =Math.Round(listSum.Average(f => f.Score),2);
                evaluateSum.DesignStyle = Math.Round(listSum.Average(f => f.DesignStyle), 2);
                evaluateSum.ComfortLevel = Math.Round(listSum.Average(f => f.ComfortLevel), 2);
                evaluateSum.Applicability = Math.Round(listSum.Average(f => f.Applicability), 2);
                evaluateSum.CostPerformance = Math.Round(listSum.Average(f => f.CostPerformance), 2);
                evaluateSum.Safe = Math.Round(listSum.Average(f => f.Safe), 2);
            }
            else
            {
                evaluateSum.Score = 0;
                evaluateSum.DesignStyle = 0;
                evaluateSum.ComfortLevel = 0;
                evaluateSum.Applicability = 0;
                evaluateSum.CostPerformance = 0;
                evaluateSum.Safe = 0;
            }

            if (param.isFirst)
            {
                //评价人分类Id
                var listEnumUserTypeId = EnumExtensions.EnumToList<EvaluateUserTypeEnum>();
                List<dropdownModel> listUserTypeId = listEnumUserTypeId.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //获取列头数据
                List<EvaluateColumn> listColumn = await ixuniformevaluateservicesManager.GetEvaluateColumnList();
                msgdata = baseSucc(mapper.Map<List<XUniformEvaluateDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new {tipMsg = tipMsg, listUserTypeId = listUserTypeId, listColumn = listColumn, footer = evaluateSum });
            }
            else
            {
                msgdata = baseSucc(pg.data, pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",new { footer = evaluateSum });
            }
            return msgdata;
        }

        /// <summary>
        /// 导出评价明细信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("exportdetail")]
        public async Task<Result> ExportEvaluateDetailList([FromBody] XUniformEvaluateDetailParam param)
        {
            Result r = new Result();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            PageModel<XUniformEvaluateDto> pg = await ixuniformevaluateservicesManager.GetEvaluateDetailPaged(param);

            if (pg.dataCount > 0)
            {
                List<XUniformEvaluateDto> list = pg.data;
                //加上总计一栏
                list.Add(new XUniformEvaluateDto()
                {
                    UserTypename = "总评得分：",
                    Score =Math.Round(list.Average(f => f.Score),2),
                    DesignStyle = Math.Round(list.Average(f => f.DesignStyle), 2),
                    ComfortLevel = Math.Round(list.Average(f => f.ComfortLevel), 2),
                    Applicability = Math.Round(list.Average(f => f.Applicability), 2),
                    CostPerformance = Math.Round(list.Average(f => f.CostPerformance), 2),
                    Safe = Math.Round(list.Average(f => f.Safe), 2),
                    EvaluateDate = null,
                });
                //
                string execlName = $"{user.UnitName}-{pg.data[0].PurchaseNo}-评价明细信息";

                string file = new ExcelHelper<XUniformEvaluateDto>().ExportToExcel(env.WebRootPath, execlName + ".xls", execlName,
                                                                                          list,
                                                                                          new string[] { "UserTypename", "UserName", "Score", "DesignStyle", "ComfortLevel", "Applicability", "CostPerformance", "Safe", "EvaluateDate", "Remark" });
                r.data.rows= file;
                r.flag = 1;
                r.msg = "导出成功";
                r.data.headers = "评价明细信息";
            }
            else
            {
                r.flag = 0;
                r.msg = "无数据导出";
            }
            return r;
        }

        /// <summary>
        /// 根据采购单Id获取需要填写的评价信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbypurchaseid")]
        [AllowAnonymous]
        public async Task<Result> GetEvaluateByPurchaseId(long id)
        {
            Result r = await ixuniformevaluateservicesManager.GetEvaluateByPurchaseId(id);
            return r;
        }

        /// <summary>
        /// 用户评价(无需登录)
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("userevaluate")]
        [AllowAnonymous]
        public async Task<Result<string>> UserEvaluate([FromBody] UniformEvaluateModel o)
        {
            var data =await ixuniformevaluateservicesManager.UserEvaluate(o);
            return data;
        }

        /// <summary>
        /// 校服明细查看
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getcreateorder")]
        [AllowAnonymous]
        public async Task<Result<List<XUniformShelfDto>>> GetCreateOrder(long id)
        {
            Result<List<XUniformShelfDto>> r = new Result<List<XUniformShelfDto>>();
            var entity = await ixuniformpurchaseManager.GetById(id);

            if (entity != null)
            {
                //获取采购单下的校服清单列表。
                var listUniformShelf = await uniformshelfManager.Find(a => a.UniformPurchaseId == entity.Id);
                if (listUniformShelf == null || listUniformShelf.Count == 0)
                {
                    r.flag = 0;
                    r.msg = "获取失败，没有需要生成的校服信息";
                    return r;
                }
                r.flag = 1;
                r.msg = "获取成功";
                r.data.rows = (from item in listUniformShelf
                               select new XUniformShelfDto()
                               {
                                   Id = item.Id,
                                   Uniformtype = item.Uniformtype,
                                   Name = item.Name,
                                   Brand = item.Brand,
                                   Sex = item.Sex,
                                   StandardNum = item.StandardNum,
                                   Price = item.Price,
                                   UnitName = item.UnitName,

                               }).ToList();
                r.data.other = new { SubscriptionDeadline = entity.SubscriptionDeadline, PurchaseNo = entity.PurchaseNo };
            }
            return r;
        }


        /// <summary>
        /// 用户评价(需登录)
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("usereloginvaluate")]
        public async Task<Result<string>> UserLoginEvaluate([FromBody] UniformEvaluateModel o)
        {
            var data = await ixuniformevaluateservicesManager.UserLoginEvaluate(o);
            return data;
        }
    }
}
