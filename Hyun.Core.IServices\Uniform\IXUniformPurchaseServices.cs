﻿using Hyun.Core.Model.Models.Uniform;
namespace Hyun.Core.IServices.Uniform
{

    ///<summary>
    ///XUniformPurchase接口方法
    ///</summary>
    public interface IXUniformPurchaseServices : IBaseServices<XUniformPurchase>
    {
        /// <summary>
        /// 校服采购-校服征订-保存征订单
        /// </summary>
        /// <param name="model">征订单信息</param>
        /// <returns></returns>
        Task<Result> SaveOrder(XUniformPurchaseDto model);
        /// <summary>
        /// 校服采购-采购管理-更新学生人数。
        /// </summary>
        /// <param name="schoolid"></param>
        /// <param name="gradeid"></param>
        /// <returns></returns>
        Task<Result> UpdatePurchaseOrderNum(long schoolid, int gradeid);
        /// <summary>
        /// 校服采购-采购管理-添加、修改提交保存
        /// </summary>
        /// <param name="model">XUniformPurchase对象</param>
        /// <returns></returns>
        Task<Result<XUniformPurchaseDto>> InsertUpdate(XUniformPurchaseDto o);
        /// <summary>
        /// 校服采购-采购管理-提交
        /// </summary>
        /// <param name="model">XUniformPurchase对象</param>
        /// <returns></returns>
        Task<Result> Submit(XUniformPurchaseDto model);

        /// <summary>
        /// 校服采购-采购管理-根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        Task<Result> FakeDeleteById(XUniformPurchaseDto model);
        /// <summary>
        /// 校服采购-删除附件
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> UpdateAttachmentDelete(XUniformPurchaseDto model);
        /// <summary>
        /// 校服采购-备查（区县）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> FilingConfirm(XUniformPurchaseDto model);
        /// <summary>
        /// 校服采购-采购管理-退回
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> FilingBackout(XUniformPurchaseDto model);
        /// <summary>
        ///  校服采购-采购管理-撤销
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Result> FilingRevoked(XUniformPurchaseDto model);
        /// <summary>
        /// 校服采购-采购管理-根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        Task<XUniformPurchase> GetById(long id);

        /// <summary>
        /// 校服采购-采购管理-根据查询条件获取数据集合
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns></returns>
        Task<List<XUniformPurchase>> Find(Expression<Func<XUniformPurchase, bool>> expression);

        /// <summary>
        /// 校服采购-采购管理-根据查询条件获取数据分页列表
        /// </summary>
        /// <param name="param">XUniformPurchaseParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseDto>> GetPaged(XUniformPurchaseParam param);
        /// <summary>
        /// 校服采购-校服征订-生成征订单列表查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseDto>> GetEditOrderPaged(XUniformPurchaseParam param);
        /// <summary>
        /// 校服采购-校服征订-班主任征订单列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseDto>> GetOrderTeacherPaged(XUniformPurchaseParam param);
        /// <summary>
        /// 校服调换列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseSwapDto>> GetSwapPaged(XUniformPurchaseParam param);

        /// <summary>
        /// 发起修改校服调换
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> SwapLaunch(XUniformPurchaseSwapModel o);

        /// <summary>
        /// 调换单列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseSwapDto>> GetSwapOrderPaged(XUniformPurchaseParam param);

        /// <summary>
        /// 获取班主任校服调换列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseSwapDto>> GetSwapOrderByTeacherPaged(XUniformPurchaseParam param);


        /// <summary>
        /// 校服资助列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XuniformSponsorDto>> GetSponsorPaged(XUniformPurchaseParam param);

        /// <summary>
        /// 根据校服采购Id获取校服资助信息
        /// </summary>
        /// <param name="id">校服采购Id</param>
        /// <returns></returns>
        Task<Result<XuniformSponsorDto>> GetSponsorById(long id);


        /// <summary>
        /// 校服资助-添加、修改提交保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        Task<Result<string>> SponsorInsertUpdate(XuniformSponsorModel o);

        /// <summary>
        /// 删除校服资助
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result<string>> SponsorDeleteById(long id);

        /// <summary>
        /// 删除校服资助附件
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Result<string>> SponsorFileDelete(long id);
        /// <summary>
        /// 家长获取采购结果信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseDto>> GetParentPaged(XUniformPurchaseParam param);


        /// <summary>
        /// 校服评价列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseEvaluateDto>> GetEvaluatePaged(XUniformEvaluateParam param);

        /// <summary>
        /// 单位查看评价列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        Task<PageModel<XUniformPurchaseEvaluateDto>> GetEvaluateList(XUniformEvaluateParam param);
    }
}

