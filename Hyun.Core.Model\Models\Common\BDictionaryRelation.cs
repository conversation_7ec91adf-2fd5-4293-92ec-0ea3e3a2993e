﻿namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///字典对应关系表
    ///</summary>
    [SugarTable("b_DictionaryRelation","字典对应关系表")]
    public class BDictionaryRelation : BaseEntity
    {

          public BDictionaryRelation()
          {

          }

           /// <summary>
           ///字典表Id
          /// </summary>
          public int DictionaryId { get; set; }

           /// <summary>
           ///对应字典表Id
          /// </summary>
          public int DictionaryToId { get; set; }

           /// <summary>
           ///关系类型（1：字典表关系）
          /// </summary>
          public int RelationType { get; set; } = 1;

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

