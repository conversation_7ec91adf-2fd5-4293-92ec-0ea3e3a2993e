namespace Hyun.Core.Model.Models.Uniform
{

    ///<summary>
    ///方案意见列表
    ///</summary>
    [SugarTable("x_UniformSchemeOpinion", "方案意见列表")]
    public class XUniformSchemeOpinion : BaseEntity
    {

        public XUniformSchemeOpinion()
        {

        }

        /// <summary>
        ///校服方案Id
        /// </summary>
        public long UniformSchemeId { get; set; }

        /// <summary>
        ///选用结果（1：同意  2：不同意）
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        ///选用意见
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Opinion { get; set; }

        /// <summary>
        ///采购方式
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? PurchaseMethod { get; set; }

        /// <summary>
        ///采购方式名称
        /// </summary>
        [SugarColumn(Length = 51, IsNullable = true)]
        public string PurchaseMethodName { get; set; }

        /// <summary>
        ///家长姓名
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string ParentName { get; set; }

        /// <summary>
        ///学生姓名
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string StudentName { get; set; }

        /// <summary>
        ///班级Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? UniformClassId { get; set; }
        /// <summary>
        ///学生Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? StudentId { get; set; }
        /// <summary>
        ///家长学生Id
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? ParentStudentId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public int? GradeId { get; set; }
        /// <summary>
        /// 年级名称
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string GradeName { get; set; }
        /// <summary>
        /// 班级名称
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string ClassName { get; set; }
        /// <summary>
        /// 班级Id
        /// </summary>
        public int? ClassId { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        [SugarColumn(Length = 31, IsNullable = true)]
        public string Mobile { get; set; }
        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;
    }
}

