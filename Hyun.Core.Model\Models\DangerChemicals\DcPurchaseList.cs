namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///物品采购清单
    ///</summary>
    [SugarTable("dc_PurchaseList","物品采购清单")]
    public class DcPurchaseList : BaseEntity
    {

          public DcPurchaseList()
          {

          }

           /// <summary>
           ///采购申请单Id
          /// </summary>
          public long PurchaseOrderId { get; set; }

           /// <summary>
           ///申请批次
          /// </summary>
          [SugarColumn(Length = 31)]
          public string BatchNo { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///分类Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///规格型号Id
          /// </summary>
          public long SchoolMaterialModelId { get; set; }

           /// <summary>
           ///品牌Id
          /// </summary>
          public long SchoolMaterialBrandId { get; set; }

           /// <summary>
           ///设备名称
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Name { get; set; }

           /// <summary>
           ///设备品牌
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Brand { get; set; }

           /// <summary>
           ///规格型号
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Model { get; set; }

        /// <summary>
        ///数量
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Num { get; set; }

           /// <summary>
           ///单位
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string UnitName { get; set; }

        /// <summary>
        ///参考价
        /// </summary>
        [SugarColumn(ColumnDataType = "money")]
        public decimal Price { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 1023,IsNullable = true)]
          public string Remark { get; set; }

           /// <summary>
           ///状态
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

