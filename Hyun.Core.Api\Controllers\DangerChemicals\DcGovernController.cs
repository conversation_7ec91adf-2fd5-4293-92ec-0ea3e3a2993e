﻿using Hyun.Core.Common.DB;
using Hyun.Core.Common.Extensions;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.ViewModels.Common;
using Hyun.Old.Util;
using System.Data;

namespace Hyun.Core.Api
{

    [Route("api/hyun/dcgovern")]
    [ApiController]
    [Authorize(Permissions.Name)]//
    public class DcGovernController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IDcGovernSetServices dcGovernSetManager;
        private readonly IDcGovernItemReportServices dcGovernItemReportManager;
        private readonly IPUnitServices unitManager;
        private readonly IDcGovernTaskUnitServices dcGovernTaskUnitManager;
        private readonly IDcGovernDeclareSummaryServices dcGovernDeclareSummaryManager;
        private readonly IDcGovernItemServices dcGovernItemManager;
        private readonly IDcGovernRectifyServices dcGovernRectifyManager;
        private readonly IDcGovernDeclareDetailServices dcGovernDeclareDetailManager;
        private readonly IDcGovernDeclareUnitConfigServices dcGovernDeclareUnitConfigManager;
        private readonly IVUserDetailServices vUserDetailManager;
        private readonly IBAttachmentServices bAttachmentManager;
        private readonly IBAttachmentDataServices bAttachmentDataManager;
        public DcGovernController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IDcGovernSetServices _dcGovernSetManager, IDcGovernItemReportServices _dcGovernItemReportManager,IPUnitServices _unitManager,
            IDcGovernTaskUnitServices _dcGovernTaskUnitManager,IDcGovernDeclareSummaryServices _dcGovernDeclareSummaryManager, IDcGovernItemServices _dcGovernItemManager,
            IDcGovernRectifyServices _dcGovernRectifyManager, IDcGovernDeclareDetailServices _dcGovernDeclareDetailManager, IDcGovernDeclareUnitConfigServices _dcGovernDeclareUnitConfigManager,
            IVUserDetailServices _vUserDetailManager, IBAttachmentServices _bAttachmentManager, IBAttachmentDataServices _bAttachmentDataManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            dcGovernSetManager = _dcGovernSetManager;
            dcGovernItemReportManager = _dcGovernItemReportManager;
            unitManager = _unitManager;
            dcGovernTaskUnitManager = _dcGovernTaskUnitManager;
            dcGovernDeclareSummaryManager = _dcGovernDeclareSummaryManager;
            dcGovernItemManager = _dcGovernItemManager;
            dcGovernRectifyManager = _dcGovernRectifyManager;
            dcGovernDeclareDetailManager = _dcGovernDeclareDetailManager;
            dcGovernDeclareUnitConfigManager = _dcGovernDeclareUnitConfigManager;
            vUserDetailManager = _vUserDetailManager;
            bAttachmentManager = _bAttachmentManager;
            bAttachmentDataManager = _bAttachmentDataManager;
        }

        /// <summary>
        /// 查询周天排查列表-查询
        /// </summary>
        /// <param name="param">查询实体</param>
        /// <users>
        /// 适用角色：单位排查员
        /// </users>
        [HttpPost]
        [Route("dcgovernitemreportgetbyid")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcGovernItemReport_GetById([FromBody] VDcGovernItemReportParam param)
        {
            Result r = new Result();
            long schoolId = 0;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                schoolId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt() || user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                schoolId = param.Id;
            }
            if (schoolId <= 0)
            {
                return baseFailed<PageModel<object>>("请选择单位");
            }
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Id", SortType = "ASC" } };
            }
            //获取当前是第几月，和第几周。 
            List<object> declareDate = new List<object>();
            DateTime beginDate = DateTime.Now;
            DateTime endDate = DateTime.Now;
            var numberCyclez = ComLib.GetDateWeekNumDatePeriod(DateTime.Now, ref beginDate, ref endDate);
            declareDate.Add(new
            {
                Opttype = 1,
                Name = "周填报时间区间",
                NumberCyclez = numberCyclez,
                BeginDate = beginDate,
                EndDate = endDate
            });
            numberCyclez = ComLib.GetLastMonthNumDatePeriod(null, ref beginDate, ref endDate);
            var BeginDay = -1;
            var EndDay = -1;
            long unitPId = 0;
            VUserDetail VUser = await vUserDetailManager.GetByUserId(user.UserId);
            if (VUser != null)
            {
                unitPId = VUser.UnitPId;
            }

            var dateSetList = await dcGovernSetManager.Find(new DcGovernSetParam { UnitPId = unitPId, unitId = user.UnitId,sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Id", SortType = "ASC" } } });
            if (dateSetList != null && dateSetList.Count > 0)
            {
                BeginDay = dateSetList[0].BeginDay ?? -1;
                EndDay = dateSetList[0].EndDay ?? -1;
            }
            declareDate.Add(new
            {
                Opttype = 2,
                Name = "月填报时间区间",
                NumberCyclez = numberCyclez,
                BeginDate = BeginDay,
                EndDate = EndDay
            });
            r.data.headers = declareDate;

            await dcGovernItemReportManager.Init(schoolId, DateTime.Now.Year, user.UserId, user.UnitId, user.UnitTypeId, DateTime.Now);

            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            param.schoolid = schoolId;
            var list = await dcGovernItemReportManager.GetStatisticsPaged(param);
            if (list != null && list.dataCount > 0)
            {
                PageModel<object> pg = new PageModel<object>();
                r.flag = 1;
                r.msg = $"{param.pageIndex}-{param.pageSize}/{list.dataCount}";
                

                var list_attach = await dcGovernItemReportManager.GetAttachmentPaged(new VDcGovernItemReportAttachmentParam
                {
                    FileCategory = FileCategory.GovernItemReport.ObjToInt(),
                    ModuleType = ModuleTypeEnum.Report.ObjToInt(),
                    IsDelete = false,
                    SchoolId = list.data.FirstOrDefault().SchoolId,
                    GovernYear = list.data.FirstOrDefault().GovernYear,
                    pageIndex = 1,
                    pageSize = int.MaxValue,
                    sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "ObjectId", SortType = "asc" } }
                });              
                if (list_attach!=null && list_attach.dataCount > 0)
                {
                    var data = list.data.Select(t => new { 
                        Id = t.Id,
                        GovernItemId = t.GovernItemId,
                        Name = t.Name,
                        CategoryId = t.CategoryId,
                        ItemSort = t.ItemSort,
                        Statuz = t.Statuz,
                        Nature = t.Nature,
                        Grade = t.Grade,
                        IsForeignDeclare = t.IsForeignDeclare,
                        RegTime = t.RegTime,
                        CategoryName = t.CategoryName,
                        SchoolId = t.SchoolId,
                        GovernYear = t.GovernYear,
                        IsTallyClaim = t.IsTallyClaim,
                        RectifyLimit = t.RectifyLimit,
                        Memo = t.Memo,
                        Suggest = t.Suggest,
                        UserId = t.UserId,
                        UnitId = t.UnitId,
                        UnitIdType = t.UnitIdType,
                        RegDate = t.RegDate,
                        IsRectify = t.IsRectify,
                        AttachmentList = GetAttachemntList(list_attach.data,t.Id)
                    });
                    pg.data = data.ToList<object>();
                }
                else
                {
                    var data = list.data.Select(t => new { t.Id, t.GovernItemId, t.Name, t.CategoryId, t.ItemSort, t.Statuz, t.Nature, t.Grade, t.IsForeignDeclare, t.RegTime, t.CategoryName, t.SchoolId, t.GovernYear, t.IsTallyClaim, t.RectifyLimit, t.Memo, t.Suggest, t.UserId, t.UnitId, t.UnitIdType, t.RegDate, t.IsRectify });
                    pg.data = data.ToList<object>();
                }
                return baseSucc(pg, list.dataCount,"");
            }
            else
            {
                return baseSucc(new PageModel<object>(), 0, "未查询到需要上报的数据");
            } 
        }

        private List<VDcGovernItemReportAttachment> GetAttachemntList(List<VDcGovernItemReportAttachment> list,long id)
        {
            List<VDcGovernItemReportAttachment> listAttachment = new List<VDcGovernItemReportAttachment>();
            if (list != null)
            {
                listAttachment = list.Where(m => m.ObjectId == id).ToList();
            }            
            return listAttachment;
        }

        /// <summary>
        /// 整改申报-保存
        /// </summary>
        /// <param name="o">填报实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgovernitemreportsave")]
        //<used>1</used>
        public async Task<Result<object>> DcGovernItemReport_Save([FromBody] DcGovernItemReportSaveDto o)
        {
            try
            {
                List<DcGovernItemReport> itemReportList = o.list;
                if (user.UnitTypeId == UnitTypes.School.ObjToInt())
                {
                    o.schoolid = user.UnitId;
                }
                else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
                {
                    var entity_unit = await unitManager.QueryById(o.schoolid);
                    if (entity_unit == null || entity_unit.PId != user.UnitId)
                    {
                        return baseFailed<object>("不可操作其他单位数据。");
                    }
                }
                else if (user.UnitTypeId == UnitTypes.City.ObjToInt())
                {
                    var entity_unit = await unitManager.QueryById(o.schoolid);
                    if (entity_unit != null)
                    {
                        var entity_Countyunit = await unitManager.QueryById(entity_unit.PId);
                        if (!(entity_Countyunit != null && entity_Countyunit.PId == user.UnitId))
                        {
                            return baseFailed<object>("不可操作其他单位数据。");
                        }
                    }
                    else
                    {
                        return baseFailed<object>("不可操作其他单位数据。");
                    }
                }
                else
                {
                    return baseFailed<object>("非法操作。");
                }

                if (o.reporttype == 3)
                {
                    DcGovernTaskUnit taskUnit = await dcGovernTaskUnitManager.GetById(o.GovernTaskUnitId);
                    if (taskUnit != null)
                    {
                        if (taskUnit.CheckingDate == null)
                        {
                            return baseFailed<object>("请先填写基本信息。");
                        }
                    }
                }
                int flag = 0;
                //循环添加
                if (itemReportList != null && itemReportList.Count > 0)
                {
                    var listdata = await dcGovernItemReportManager.GetPaged(new DcGovernItemReportParam { GovernYear = DateTime.Now.Year, SchoolId = o.schoolid, pageIndex = 1, pageSize = int.MaxValue });
                    if (listdata != null && listdata.data.Count > 0)
                    {
                        List<BAttachmentData> attAchmentDataListCatach = null;//缓存，查一次
                        var delAttAchmentList = new List<BAttachment>();//需删除的历史附件，修改产生的。
                        var updateEntityList = new List<DcGovernItemReport>();
                        var delAttamhList = new List<long>();
                        var addAttachmentList = new List<BAttachment>();
                        for (int i = 0; i < itemReportList.Count; i++)
                        {
                            var entity = listdata.data.Where(m => m.Id == itemReportList[i].Id).FirstOrDefault();
                            if (entity != null)
                            {
                                if (entity.IsTallyClaim == 1 && o.reporttype == 3)
                                {
                                    //区县、市级保存申报。已存在问题的，只能更新建议
                                    entity.Suggest = itemReportList[i].Suggest ?? "";
                                    entity.RegDate = DateTime.Now;
                                    updateEntityList.Add(entity);
                                }
                                else
                                {
                                    if (itemReportList[i].IsTallyClaim == 0)
                                    {
                                        itemReportList[i].RectifyLimit = null;
                                        itemReportList[i].Memo = "";
                                        itemReportList[i].Suggest = "";
                                        if (entity.IsTallyClaim == 1)
                                        {
                                            delAttamhList.Add(entity.Id);
                                        }
                                    }
                                    else
                                    {
                                        if (entity.IsTallyClaim == 1)
                                        {
                                            //修改问题

                                            //查出历史记录附件。
                                            var attachmentOldList =await bAttachmentManager.Find(m => m.ObjectId == entity.Id && m.UnitId == entity.SchoolId && m.FileCategory == FileCategory.GovernItemReport.ObjToInt() && m.IsDelete == 0);
                                            if (attachmentOldList != null && attachmentOldList.Count > 0)
                                            {
                                                if (itemReportList[i].AttachmentIdList!=null)
                                                {
                                                    var listAttachmentTemp = attachmentOldList.Where(m => !itemReportList[i].AttachmentIdList.Contains(m.Id));
                                                    if (listAttachmentTemp!=null && listAttachmentTemp.Count()> 0)
                                                    {
                                                        //需删除的附件
                                                        delAttAchmentList.AddRange(listAttachmentTemp);
                                                    }
                                                }
                                            }
                                          //新增问题。 
                                          var listTemp = await GetAttachmentDataList(attAchmentDataListCatach, itemReportList[i].AttachmentIdList);
                                            if (listTemp != null && listTemp.Count() > 0)
                                            {
                                                foreach (var item in listTemp)
                                                {
                                                    var attachement = bAttachmentManager.GetModel(item);
                                                    attachement.UnitId = user.UnitId;
                                                    attachement.UserId = user.UserId;
                                                    attachement.ObjectId = entity.Id;
                                                    attachement.Id = BaseDBConfig.GetYitterId();
                                                    addAttachmentList.Add(attachement);
                                                }
                                             
                                            }
                                        }
                                        else
                                        {
                                            //新增问题。 
                                            var listTemp = await GetAttachmentDataList(attAchmentDataListCatach, itemReportList[i].AttachmentIdList);
                                            if (listTemp != null && listTemp.Count() > 0)
                                            {
                                                foreach (var item in listTemp)
                                                {
                                                    var attachement = bAttachmentManager.GetModel(item);
                                                    attachement.UnitId = user.UnitId;
                                                    attachement.UserId = user.UserId;
                                                    attachement.ObjectId = entity.Id;
                                                    attachement.Id = BaseDBConfig.GetYitterId();
                                                    addAttachmentList.Add(attachement);
                                                }
                                            }
                                        }
                                    }
                                    entity.IsTallyClaim = itemReportList[i].IsTallyClaim;
                                    entity.RectifyLimit = itemReportList[i].RectifyLimit;
                                    entity.Memo = itemReportList[i].Memo ?? "";
                                    entity.Suggest = itemReportList[i].Suggest ?? "";
                                    entity.UnitId = user.UnitId;
                                    entity.UserId = entity.UserId;
                                    entity.RegDate = DateTime.Now;
                                    entity.UnitIdType = user.UnitTypeId;
                                    updateEntityList.Add(entity);
                                }
                            }
                        }
                        var result_data = await dcGovernItemReportManager.Update(updateEntityList);
                        if (result_data)
                        {
                            //删除符合要求的文件
                            if (delAttamhList.Count > 0)
                            {
                                var list_attach = await bAttachmentManager.GetPaged(new BAttachmentParam { FileCategory = FileCategory.GovernItemReport.ObjToInt(), ModuleType = 1, IsDelete = 0, ObjectIdz = delAttamhList, pageIndex = 1, pageSize = int.MaxValue });
                                if (list_attach != null && list_attach.data.Count > 0)
                                {
                                    list_attach.data.ForEach(m => m.IsDelete = 1);
                                    await bAttachmentManager.Update(list_attach);
                                }
                            }
                            if (delAttAchmentList.Count> 0)
                            {
                                delAttAchmentList.ForEach(m => m.IsDelete = 1);
                                await bAttachmentManager.Update(delAttAchmentList);
                            }
                            if (addAttachmentList.Count > 0)
                            {
                                await bAttachmentManager.Add(addAttachmentList);
                            }
                            flag = 1;
                        }

                    }
                }
                else
                {
                    flag = 1;
                }
                //0：保存  1：周申报  2：月申报
                int numberCyclez = 0;
                if (o.reporttype == 1)
                {
                    //获取一年的第几周
                    numberCyclez = dcGovernDeclareSummaryManager.GetWeek();
                    await dcGovernDeclareSummaryManager.Report(DateTime.Now.Year, o.schoolid, o.reporttype, numberCyclez, 0, user.UnitId, user.ID);
                }
                else if (o.reporttype == 2)
                {
                    //获取一年的上一个月份
                    numberCyclez = dcGovernDeclareSummaryManager.GetLastMonth();
                    await dcGovernDeclareSummaryManager.Report(DateTime.Now.Year, o.schoolid, o.reporttype, numberCyclez, 0, user.UnitId, user.ID);
                }
                else if (o.reporttype == 3)
                {
                    await dcGovernDeclareSummaryManager.Report(DateTime.Now.Year, o.schoolid, o.reporttype, 0, o.taskid, user.UnitId, user.ID);
                    if (flag == 1)
                    {
                        DcGovernTaskUnit taskUnit = await dcGovernTaskUnitManager.GetById(o.GovernTaskUnitId);
                        if (taskUnit != null)
                        {
                            taskUnit.Statuz = 1;
                            await dcGovernTaskUnitManager.Update(taskUnit);
                        }
                    }
                }
                return baseSucc<object>("", 1, "保存成功。");
            }
            catch (Exception e)
            {
                string error = string.Format("危化品整治，查询周天排查列表功能异常：{0}", e.Message);
                FileLog.LogMessage(error, ApplicationConfig.CommonLogFilePath + "DcGovernItemReport_Save\\");
                return baseFailed<object>("查询失败，请联系客服，以便我们及时进行排查，解决问题。");
            }
        }

        private async Task<IEnumerable<BAttachmentData>> GetAttachmentDataList(List<BAttachmentData> attAchmentDataListCatach, List<long> attIdList)
        {
            if (attIdList != null && attIdList.Count > 0)
            {
                if (attAchmentDataListCatach == null)
                {
                    var listAttTemp = await bAttachmentDataManager.Find(m => m.FileCategory == FileCategory.GovernItemReport.ObjToInt() && m.UnitId == user.UnitId && m.CreateId == user.UserId && m.IsDeleted == false && m.CreateTime > DateTime.Now.AddDays(-1));//查询一天内新增的。
                    if (listAttTemp != null && listAttTemp.Count > 0)
                    {
                        attAchmentDataListCatach = listAttTemp;
                    }
                    else
                    {
                        attAchmentDataListCatach = new List<BAttachmentData>();
                    }
                }
                if (attAchmentDataListCatach.Count > 0)
                {
                    return attAchmentDataListCatach.Where(m => attIdList.Contains(m.Id));
                }
            }
            return null;
        }

        /// <summary>
        /// 查询周申报列表-查询
        /// </summary>
        /// <param name="param">周申报列表查询实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndeclaresummaryweekfind")]
        //<used>1</used>
        public async Task<Result> DcGovernDeclareSummaryWeek_Find([FromBody] VDcGovernDeclareSummaryWeekParam param)
        {
            Result r = new Result();
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Id", SortType = "DESC" }, new SortBaseModel { SortCode = "NumberCyclez", SortType = "ASC" } };
            }
            param.SchoolId = user.UnitId;
            var list = await dcGovernDeclareSummaryManager.GetWeekLogPaged(param);
            r.flag = 1;
            r.data.total = list.dataCount;
            r.data.rows = list.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{list.dataCount}";
          
            return r;
        }

        /// <summary>
        /// 查询月申报列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndeclaresummarymonthfind")]
        //<used>1</used>
        public async Task<Result> DcGovernDeclareSummaryMonth_Find([FromBody] VDcGovernDeclareSummaryMonthParam param)
        {
            Result r = new Result();
            param.SchoolId = user.UnitId;
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Id", SortType = "DESC" }, new SortBaseModel { SortCode = "NumberCyclez", SortType = "ASC" } };
            }
            var list = await dcGovernDeclareSummaryManager.GetMonthPaged(param);
            r.flag = 1;
            r.data.total = list.dataCount;
            r.data.rows = list.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{list.dataCount}";
         
            return r;
        }

        /// <summary>
        /// 查询填报详情-查询
        /// </summary>
        /// <param name="param">查询实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndeclaredetailfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcGovernDeclareDetail>>> DcGovernDeclareDetail_Find([FromBody] VDcGovernDeclareDetailParam param)
        {
            long schoolId = 0;
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                schoolId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt() || user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                schoolId = param.Id;
            }
            if (schoolId <= 0)
            {
                return baseFailed<PageModel<VDcGovernDeclareDetail>>("请选择单位。");
            }
            PageModel<VDcGovernDeclareDetail> pg = new PageModel<VDcGovernDeclareDetail>();
            VDcGovernDeclareSummaryListParam vdcdslistParam = new VDcGovernDeclareSummaryListParam();
            vdcdslistParam.Id = param.Id;
            if (param.sortModel == null)
            {
                vdcdslistParam.sortModel = new List<SortBaseModel>() { new SortBaseModel() { SortCode = "Id", SortType = "ASC" } };
            }
            //获取单位信息
            var list_unitinfo = await dcGovernDeclareSummaryManager.GetStatistics(vdcdslistParam);
            if (list_unitinfo != null && list_unitinfo.Count > 0)
            {
                var unitinfo = list_unitinfo.FirstOrDefault();
                if(unitinfo.DatePeriod.IndexOf(" 0:00:00") > -1)
                {
                    unitinfo.DatePeriod = unitinfo.DatePeriod.Replace(" 0:00:00", "");
                }
                else if (unitinfo.DatePeriod.IndexOf(" 00:00:00") > -1)
                {
                    unitinfo.DatePeriod = unitinfo.DatePeriod.Replace(" 00:00:00", "");
                }
                pg.Other= new { unitinfo.GovernYear, unitinfo.ReportType, unitinfo.RegDate, unitinfo.UnitIdType, unitinfo.NumberCyclez, unitinfo.DatePeriod, unitinfo.UnitName, unitinfo.UserName };
            }

            var list = await dcGovernItemManager.GetDeclareDetailPaged(
                    new VDcGovernDeclareDetailParam 
                    { 
                        Id = param.Id, 
                        categoryid = param.categoryid, 
                        nature = param.nature,
                        grade = param.grade,
                        istallyclaim = param.istallyclaim,
                        Name = param.Name,
                        pageIndex = 1, 
                        pageSize = int.MaxValue 
                    }
                );
            if (list != null && list.dataCount > 0)
            {
                var list_attach = await dcGovernDeclareSummaryManager.GetAttachmentPaged(new VDcGovernItemDetailAttachmentParam { FileCategory = FileCategory.GovernDeclareDetail.ObjToInt(), ModuleType = ModuleTypeEnum.TrainPhoto.ObjToInt(), DeclareSummaryId = param.Id, SchoolId = list.data[0].SchoolId, GovernYear = list.data[0].GovernYear, pageIndex = 1, pageSize = int.MaxValue, sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "ObjectId", SortType = "ASC" } } });

                list.data.ForEach(t =>
                {
                    //t.DatePeriod = t.DatePeriod.IndexOf(" 00:00:00") > -1 ? t.DatePeriod.Replace(" 00:00:00", "") : t.DatePeriod.IndexOf(" 0:00:00") > -1 ? t.DatePeriod.Replace(" 0:00:00", "") : t.DatePeriod;
                    //t.RectifyLimit =(t.RectifyLimit==null || t.RectifyLimit.ToString() == "0001-01-01 00:00:00") ? null : t.RectifyLimit;
                    t.AttachmentList = GetAttachemntList(list_attach, t.GovernReportId);
                });
                pg.data = list.data;
                pg.dataCount = list.dataCount;
            }
            return baseSucc(pg, pg.dataCount);
        }
        /// <summary>
        /// 处理附件
        /// </summary>
        /// <param name="data"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        private List<object> GetAttachemntList(PageModel<VDcGovernItemDetailAttachment> data, long id)
        {
            List<object> listAttachment = null;
            if (data != null && data.data != null && data.data.Count > 0)
            {
                var listTemp = data.data.Where(m => m.ObjectId == id);
                if (listTemp!=null && listTemp.Count() >0)
                {
                    listAttachment = listTemp.ToList<object>();
                }
            }
            return listAttachment;
        }

        /// <summary>
        /// 问题隐患清单-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgovernrectifylistfind")]
        //<used>1</used>
        public async Task<Result<PageModel<object>>> DcGovernRectifyList_Find([FromBody] DcGovernRectifyParam param)
        {
            if (user.UnitTypeId == UnitTypes.School.ObjToInt())
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.Couty.ObjToInt())
            {
                param.CountyId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypes.City.ObjToInt())
            {
                param.CityId = user.UnitId;
            }
            if (param.sortModel == null)
            {
                param.sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "Statuz", SortType = "ASC" }, new SortBaseModel { SortCode = "RegDate", SortType = "DESC" } };
            }
            var list = await dcGovernRectifyManager.DcGovernRectifyList_Find(param);
            return baseSucc(new PageModel<object>() { data = list == null ? null : list.ToList(), dataCount = param.totalCount }, param.totalCount, "查询成功");
        }

        /// <summary>
        /// 获取申报整改信息-查询
        /// </summary>
        /// <param name="id">整改表Id</param>
        /// <param name="opttype">查看类型，1：查看申报信息 2：查看整改信息</param>
        /// <users>
        /// 适用角色：单位排查员
        /// </users>
        [HttpPost]
        [Route("dcgovernrectifygetbyid")]
        //<used>1</used>
        public async Task<Result<DcGovernRectifyDto>> DcGovernRectify_GetById(long id, int opttype)
        {
            var entity = await dcGovernRectifyManager.GetById(id);
            if (entity != null)
            {
                object other = null;
                //获取对应的附件
                if (opttype == 1)
                {
                    var list = await bAttachmentManager.Query(f => f.FileCategory == FileCategory.GovernItemReport.ObjToInt() && f.IsDelete == 0 && f.IsDeleted == false && f.ObjectId == entity.GovernReportId);
                    if (list != null && list.Count > 0)
                    {
                        other = list.Select(t => new { t.Id, t.ObjectId, t.ModuleType, t.Title, t.Path, t.FileCategory, t.Ext });
                    }
                }
                else if (opttype == 2 || opttype == 0)
                {
                    var list = await bAttachmentManager.Query(f => f.FileCategory == FileCategory.GovernItemRectify.ObjToInt() && f.IsDelete == 0 && f.IsDeleted == false && f.ObjectId == entity.GovernReportId);
                    if (list != null && list.Count > 0)
                    {
                        other = list.Select(t => new { t.Id, t.ObjectId, t.ModuleType, t.Title, t.Path, t.FileCategory, t.Ext });
                    }
                }
                return baseSucc(mapper.Map<DcGovernRectifyDto>(entity), 1, "", other);
            }
            return baseFailed<DcGovernRectifyDto>("未查询到当前问题隐患清单数据。");
        }

        /// <summary>
        /// 查看检查说明-查询
        /// </summary>
        /// <param name="id">危化品治理申报分类明细表Id</param>
        /// <param name="opttype">查看类型，1：查看申报信息 2：查看整改信息</param>
        /// <users>
        /// 适用角色：单位排查员
        /// </users>
        [HttpPost]
        [Route("governdeclaredetailgetbyid")]
        //<used>1</used>
        public async Task<Result> GovernDeclareDetail_GetById(long id, int opttype)
        {
            Result r = new Result();
            var entity = await dcGovernDeclareDetailManager.GetById(id);
            if (entity != null)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = entity; 

                //获取对应的附件
                if (opttype == 1)
                {
                    var list = await bAttachmentManager.GetPaged(new BAttachmentParam { pageIndex = 1, pageSize = int.MaxValue, FileCategory = FileCategory.GovernDeclareDetail.ObjToInt(), ModuleType = ModuleTypeEnum.TrainPhoto.ObjToInt(), IsDelete = 0, ObjectId = entity.GovernReportId });
                    if (list != null && list.dataCount > 0)
                    {
                        r.data.footer = list.data.Select(t => new { t.Id, t.ObjectId, t.ModuleType, t.Title, t.Path, t.FileCategory, t.Ext });
                    }
                }
                else if (opttype == 2 || opttype == 0)
                {
                    var list = await bAttachmentManager.GetPaged(new BAttachmentParam { 
                                                                        pageIndex = 1, 
                                                                        pageSize = int.MaxValue, 
                                                                        FileCategory = FileCategory.GovernItemRectify.ObjToInt(), 
                                                                        ModuleType = ModuleTypeEnum.Rectification.ObjToInt(), 
                                                                        IsDelete = 0, 
                                                                        ObjectId = entity.GovernReportId 
                                                                });
                    if (list != null && list.dataCount > 0)
                    {
                        r.data.footer = list.data.Select(t => new { t.Id, t.ObjectId, t.ModuleType, t.Title, t.Path, t.FileCategory, t.Ext });
                    }
                }
            }
            return r;
        }

        /// <summary>
        /// 危化品整改-保存
        /// </summary>
        /// <param name="o">参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgovernrectifyrectifysave")]
        //<used>1</used>
        public async Task<Result<string>> DcGovernRectify_RectifySave([FromBody] DcGovernRectifyDto o)
        { 
            try
            {
              var   r = await dcGovernRectifyManager.Rectify(o.Id, user.UnitId, FileCategory.GovernItemReport.ObjToInt(), ModuleTypeEnum.Report.ObjToInt(), o.Statuz, o.Measures, DateTime.Now, user.ID);
                if (r.flag==1)
                {
                    var entity = await dcGovernRectifyManager.GetById(o.Id);
                    //处理附件。 查询历史，删除历史不用的，添加新增的。
                    List<BAttachment> delList = new List<BAttachment> ();
                    var historyAttachmentList = await bAttachmentManager.Find(m=> m.FileCategory == FileCategory.GovernItemRectify.ObjToInt() && m.ModuleType == ModuleTypeEnum.Rectification.ObjToInt() && m.ObjectId == entity.GovernReportId && m.IsDelete == 0 && m.IsDeleted == false && m.UnitId == user.UnitId);
                    if (historyAttachmentList != null && historyAttachmentList.Count > 0)
                    {
                        for (int i = 0; i < historyAttachmentList.Count; i++)
                        {
                            if (o.AttachmentIdList!=null && o.AttachmentIdList.Contains(historyAttachmentList[i].Id))
                            {
                                continue;
                            }
                            else
                            {
                                delList.Add(historyAttachmentList[i]);
                            }
                        }
                    }
                    //处理新附件
                    List<BAttachment> addList = new List<BAttachment>();
                    if (o.AttachmentIdList != null && o.AttachmentIdList.Count > 0)
                    {
                        var newAttachmentDataList = await bAttachmentDataManager.Find(m => m.IsDeleted == false && m.UnitId == user.UnitId && m.ModuleType == ModuleTypeEnum.Rectification.ObjToInt() && m.FileCategory == FileCategory.GovernItemRectify.ObjToInt() && o.AttachmentIdList.Contains(m.Id));
                        if (newAttachmentDataList!=null && newAttachmentDataList.Count > 0)
                        {
                            for (int i = 0; i < newAttachmentDataList.Count; i++)
                            {
                                if (o.AttachmentIdList != null && o.AttachmentIdList.Contains(newAttachmentDataList[i].Id))
                                {
                                    var entityTemp = bAttachmentManager.GetModel(newAttachmentDataList[i]);
                                    entityTemp.UserId = user.UserId;
                                    entityTemp.ObjectId = entity.GovernReportId;
                                    entityTemp.UnitId = user.UnitId;
                                    addList.Add(entityTemp);
                                }
                            }
                        }
                    }
                    if (delList.Count > 0)
                    {
                        delList.ForEach(n => { });
                        for (int i = 0; i < delList.Count; i++)
                        {
                            var entityTemp = delList[i];
                            entityTemp.IsDelete = 1;
                            entityTemp.ModifyId = user.UserId;
                            entityTemp.ModifyBy = user.UserName;
                            entityTemp.ModifyTime = DateTime.Now;
                            await bAttachmentManager.Update(entityTemp, new List<string>() { "IsDelete", "ModifyId", "ModifyBy", "ModifyTime" });
                        }
                    }
                    if (addList.Count> 0)
                    {
                        await bAttachmentManager.Add(addList);
                    }
                    return baseSucc<string>(r.msg, r.flag);
                }
                else
                {
                    return baseFailed<string>(r.msg); 
                } 
            }
            catch (Exception e)
            {
                string error = string.Format("危化品整治，查询周天排查列表功能异常：{0}", e.Message);
                FileLog.LogMessage(error, ApplicationConfig.CommonLogFilePath + "DcGovernRectify_RectifySave\\");
                throw;
            }
        }

        /// <summary>
        /// 危化品治理 - 区、市周、月统计列表-查询
        /// </summary>
        /// <param name="param">查询参数实体</param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndeclaresummaryfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcGovernDeclareSummary>>> DcGovernDeclareSummary_Find([FromBody] VDcGovernDeclareSummaryParam param)
        {
            if (param.isFirst)
            {
                if (param.ReportType == 1)
                {
                    param.NumberCyclez = dcGovernDeclareSummaryManager.GetWeek();
                }
                else if (param.ReportType == 2)
                {
                    param.NumberCyclez = dcGovernDeclareSummaryManager.GetLastMonth();
                }
            }
            PageModel<VDcGovernDeclareSummary> pg = await dcGovernDeclareSummaryManager.GetWeekPaged(param);
            if (param.isFirst)
            {
                if (param.ReportType == 1)
                {
                    pg.Other = new { WeekNo = dcGovernDeclareSummaryManager.GetWeek() };
                }
                else if (param.ReportType == 2)
                {
                    pg.Other = new { MonthNo = dcGovernDeclareSummaryManager.GetLastMonth() };
                } 
            }
            return baseSucc(pg, pg.dataCount, "查询成功", pg.Other);
        }

        /// <summary>
        /// 申报单位管理列表-查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndeclareunitconfigfind")]
        //<used>1</used>
        public async Task<Result<PageModel<VDcGovernDeclareUnitConfig>>> Dc_GovernDeclareUnitConfig_Find([FromBody] VDcGovernDeclareUnitConfigParam param)
        {
            param.CountyId = user.UnitId;
            PageModel<VDcGovernDeclareUnitConfig> pg = await dcGovernDeclareUnitConfigManager.GetStatisticsPaged(param);
            //return baseSucc(pg, pg.dataCount);
            return baseSucc(pg, pg.dataCount, "查询成功");
        }

        /// <summary>
        /// 设置单位是否需要上报-保存
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isNeedReporting"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndeclareunitconfigset")]
        //<used>1</used>
        public async Task<Result<string>> Dc_GovernDeclareUnitConfig_Set(long id, int isNeedReporting)
        {
            Result r = new Result();
            var entityUnit = await unitManager.QueryById(id);
            if (entityUnit.PId != user.UnitId)
            {
                return baseFailed<string>("只能设置本区县的单位。");
            }
            var param = new DcGovernDeclareUnitConfigParam();
            param.SchoolId = id;
            param.CountyId = user.UnitId;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "ASC" });
            var list = await dcGovernDeclareUnitConfigManager.Find(param);
            DcGovernDeclareUnitConfig entity = null;
            if (list != null && list.Count > 0)
            {
                entity = list.FirstOrDefault();
                entity.Statuz = isNeedReporting;
                if (await dcGovernDeclareUnitConfigManager.Update(entity))
                {
                    return baseSucc(entity.Id.ToString(), 1, "保存成功。");
                }
                else
                {
                    return baseFailed<string>("保存失败,请刷新重新操作，如无法解决，请联系客服协助处理。");
                }
            }
            else
            {
                entity = new DcGovernDeclareUnitConfig();
                entity.CountyId = user.UnitId;
                entity.SchoolId = id;
                entity.RegDate = DateTime.Now;
                entity.UserId = user.UserId;
                entity.Sort = entityUnit.Sort;
                entity.Statuz = isNeedReporting;
                if (await dcGovernDeclareUnitConfigManager.Add(entity) > 0)
                {
                    return baseSucc(entity.Id.ToString(), 1, "保存成功。");
                }
                else
                {
                    return baseFailed<string>("保存失败,请刷新重新操作，如无法解决，请联系客服协助处理。");
                }
            }
        }

        /// <summary>
        /// 批量设置单位上报-保存批量
        /// </summary>
        /// <param name="SchoolIds"></param>
        /// <param name="isNeedReporting"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndeclareunitconfigbatchset")]
        //<used>1</used>
        public async Task<Result> Dc_GovernDeclareUnitConfig_BatchSet(string SchoolIds, int isNeedReporting)
        {
            Result r = new Result();
            var paramUnit = new PUnitParam();
            if (SchoolIds != null && SchoolIds.Length > 0)
            {
                paramUnit.Ids = SchoolIds;
            }
            else
            {
                r.flag = 0;
                r.msg = "请选择设置的单位。";
                return r;
            }
            paramUnit.Pid = user.UnitId;
            paramUnit.sortModel = new List<SortBaseModel>();
            paramUnit.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "ASC" });
            var listUnit = await unitManager.Find(paramUnit);
            int errorNum = 0;
            if (listUnit != null && listUnit.Count > 0)
            {
                var paramConfig = new DcGovernDeclareUnitConfigParam();
                paramConfig.CountyId = user.UnitId;
                paramConfig.SchoolIds = listUnit.Select(m => m.Id).ToList();
                paramConfig.sortModel = new List<SortBaseModel>();
                paramConfig.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "ASC" });
                var listConfig = await dcGovernDeclareUnitConfigManager.Find(paramConfig);
                foreach (var item in listUnit)
                {
                    DcGovernDeclareUnitConfig entity = null;
                    if (listConfig != null && listConfig.Count > 0)
                    {
                        var listTemp = listConfig.Where(m => m.SchoolId == item.Id);
                        if (listTemp.Count() > 0)
                        {
                            entity = listTemp.FirstOrDefault();
                        }
                    }
                    if (entity == null)
                    {
                        entity = new DcGovernDeclareUnitConfig();
                        entity.SchoolId = item.Id;
                        entity.CountyId = item.PId;
                        entity.Statuz = isNeedReporting;
                        entity.RegDate = DateTime.Now;
                        entity.UserId = user.UserId;
                        entity.Sort = item.Sort;
                        if (await dcGovernDeclareUnitConfigManager.Add(entity) < 1)
                        {
                            errorNum++;
                        }
                    }
                    else
                    {
                        entity.Statuz = isNeedReporting;
                        if (!await dcGovernDeclareUnitConfigManager.Update(entity))
                        {
                            errorNum++;
                        }
                    }
                }
            }
            if (errorNum == 0)
            {
                r.flag = 1;
                r.msg = "保存成功";
            }
            return r;
        }

        /// <summary>
        /// 危化品打印输出单位信息-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgovernsetget")]
        //<used>1</used>
        public async Task<Result<DcGovernSetDto>> Dc_GovernSet_Get()
        {
            Result r = new Result();
            var param = new DcGovernSetParam();
            param.unitId = user.UnitId;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "ASC" });
            var list = await dcGovernSetManager.Find(param);
            if (list != null && list.Count > 0)
            {
                var governSet = list.FirstOrDefault();
                r.flag = 1;
                r.msg = "查询成功"; 
                return baseSucc(mapper.Map<DcGovernSetDto>(governSet), 1, "查询成功");
            }
            return baseSucc<DcGovernSetDto>(new DcGovernSetDto (), 1,"查询成功"); ;
        }

        /// <summary>
        /// 危化品打印输出单位信息-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgovernunitsetsave")]
        //<used>1</used>
        public async Task<Result> Dc_GovernUnitSet_Save([FromBody] DcGovernSetDto o)
        {
            Result r = new Result();
            var param = new DcGovernSetParam();
            param.unitId = user.UnitId;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "ASC" });
            DcGovernSet governSet = null;
            var list = await dcGovernSetManager.Find(param);
            if (list != null && list.Count > 0)
            {
                governSet = list.FirstOrDefault();
                governSet.UnitName = o.UnitName;
                governSet.UserName = o.UserName;
                governSet.UserPhoneNumber = o.UserPhoneNumber;
                if (await dcGovernSetManager.Update(governSet))
                {
                    r.flag = 1;
                    r.msg = "保存成功";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "保存失败,请刷新重新操作，如无法解决，请联系客服协助处理。";
                }
            }
            else
            {
                governSet = new DcGovernSet();
                governSet.UnitName = o.UnitName;
                governSet.UserName = o.UserName;
                governSet.UserPhoneNumber = o.UserPhoneNumber;
                governSet.UnitId = user.UnitId;
                governSet.SetUserId = user.UserId;
                governSet.RegDate = DateTime.Now;
                if (await dcGovernSetManager.Add(governSet) > 0)
                {
                    r.flag = 1;
                    r.msg = "保存成功";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "保存失败,请刷新重新操作，如无法解决，请联系客服协助处理。";
                }
            }
            return r;
        }

        /// <summary>
        /// 保存申报时间设置-保存
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverndatetimesetsave")]
        //<used>1</used>
        public async Task<Result> Dc_GovernDateTimeSet_Save([FromBody] DcGovernSetDto o)
        {
            Result r = new Result();
            var param = new DcGovernSetParam();
            param.unitId = user.UnitId;
            param.sortModel = new List<SortBaseModel>();
            param.sortModel.Add(new SortBaseModel() { SortCode = "Id", SortType = "ASC" });
            var list = await dcGovernSetManager.Find(param);
            DcGovernSet governSet = null;
            if (list != null && list.Count > 0)
            {
                governSet = list.FirstOrDefault();
                governSet.BeginDay = o.BeginDay;
                governSet.EndDay = o.EndDay;
                if (await dcGovernSetManager.Update(governSet))
                {
                    r.flag = 1;
                    r.msg = "保存成功";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "保存失败,请刷新重新操作，如无法解决，请联系客服协助处理。";
                }
            }
            else
            {
                governSet = new DcGovernSet();
                governSet.BeginDay = o.BeginDay;
                governSet.EndDay = o.EndDay;
                governSet.UnitId = user.UnitId;
                governSet.SetUserId = user.UserId;
                governSet.RegDate = DateTime.Now;
                if (await dcGovernSetManager.Add(governSet) > 0)
                {
                    r.flag = 1;
                    r.msg = "保存成功";
                }
                else
                {
                    r.flag = 0;
                    r.msg = "保存失败,请刷新重新操作，如无法解决，请联系客服协助处理。";
                }
            }
            return r;
        }

        /// <summary>
        /// 获取危化品治理打印表数据-查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("dcgoverniteminfo")]
        //<used>1</used>
        public async Task<Result> Dc_GovernItemInfo()
        {
            Result r = new Result();
            var listItem = await dcGovernItemManager.GetStatisticsPaged(new VDcGovernItemParam { IsForeignDeclare = 1, pageIndex = 1, pageSize = int.MaxValue, sortModel = new List<SortBaseModel> { new SortBaseModel { SortCode = "CategoryId", SortType = "ASC" }, new SortBaseModel { SortCode = "ItemSort", SortType = "ASC" } } }) ;
            if(listItem !=null && listItem.dataCount > 0)
            {
                r.flag = 1;
                r.msg = "查询成功";
                r.data.rows = listItem.data.Select(t => new { t.Name, t.CategoryId, t.ItemSort, t.Statuz, t.Nature, t.Grade, t.IsForeignDeclare, t.DicName, t.ItemCount });
            }
            else
            {
                r.flag = 0;
            }

            return r;
        }

        /// <summary>
        /// 问题与隐患清单检查记录表打印-查询
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("dcproblemgovernrectifylistprint")]
        //<used>1</used>
        public async Task<Result> Dc_ProblemGovernRectifyList_Print([FromBody] PrintWhereModel o)
        {
            Result r = new Result();
            DcGovernSet governSet = (await dcGovernSetManager.GetPaged(new DcGovernSetParam { UnitId = user.UnitId, pageIndex = 1, pageSize = 1 })).data.FirstOrDefault();
            if (governSet != null)
            {
                VDcProblemGovernRectifyListParam param = new VDcProblemGovernRectifyListParam ();
                param.IsTallyClaim = 1;
                param.GovernTaskId = o.GovernTaskId;
                if (user.UnitTypeId == 1)
                {
                    param.CityId = user.UnitId;
                }
                else if (user.UnitTypeId == 2)
                {
                    param.CountyId = user.UnitId;
                }

                if (o.AreaId != 0)
                {
                    param.CountyId = o.AreaId;
                }
                if (!string.IsNullOrEmpty(o.SchoolName))
                {
                    param.SchoolName = o.SchoolName;
                }
                if (o.CategoryId != -1)
                {
                    param.CategoryId = o.CategoryId;
                }
                if (o.Nature != -1)
                {
                    param.Nature = o.Nature;
                }
                if (o.Grade != -1)
                {
                    param.Grade = o.Grade;
                }
                if (o.Statuz != -1)
                {
                    param.Statuz = o.Statuz;
                }
                param.pageIndex = 1;
                param.pageSize = int.MaxValue;

                r.data.headers = new { governSet.UnitName, governSet.UserName, governSet.UserPhoneNumber };
                var listItem = await dcGovernDeclareDetailManager.GetProblemPaged(param);
                if (listItem.dataCount > 0)
                {
                    r.flag = 1;
                    r.msg = "查询成功";
                    r.data.rows = listItem; 
                }
                else
                {
                    r.flag = 0;
                    r.msg = "未查询到数据";
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "请先设置“单位信息设置”";
            }
            return r;
        }

    }
}
