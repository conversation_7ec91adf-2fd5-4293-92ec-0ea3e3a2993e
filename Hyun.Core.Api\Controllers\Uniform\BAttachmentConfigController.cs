﻿using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models;
using Hyun.Core.IServices;
using System.Reflection.Emit;
using System.Net.WebSockets;
using Hyun.Core.Common.Extensions;
using Confluent.Kafka;
namespace Hyun.Core.Api
{

    /// <summary>
    /// 备案附件管理
    /// </summary>
    [Route("api/hyun/battachmentconfig")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BAttachmentConfigController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IPUnitServices ipunitManager;
        private readonly IBAttachmentConfigServices ibattachmentconfigservicesManager;

        public BAttachmentConfigController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IBAttachmentConfigServices _ibattachmentconfigservicesManager, IPUnitServices _ipunitManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ibattachmentconfigservicesManager = _ibattachmentconfigservicesManager;
            ipunitManager = _ipunitManager;
        }


        /// <summary>
        /// 查询备案附件管理列表
        /// </summary>
        /// <param name="param">
        /// BAttachmentConfigParam对象
        /// IsFilled： 是否必填（1：是，0：否）
        /// Key：关键字（模块名称、附件名称、备注文字）
        /// other中返回是否必填数据
        /// </param>
        /// <returns></returns>
        /// <getpaged>查询备案附件管理列表</getpaged>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<BAttachmentConfigDto>>> BAttachmentConfigGetPaged([FromBody] BAttachmentConfigParam param)
        {

            var msgdata = new Result<List<BAttachmentConfigDto>>();
            PageModel<BAttachmentConfig> pg = await ibattachmentconfigservicesManager.GetPaged(param);
            if (param.isFirst)
            {
                List<dropdownModel> listWhether = new List<dropdownModel>() { new dropdownModel() { label = "是", value = "1" }, new dropdownModel() { label = "否", value = "0" } };
                msgdata = baseSucc(mapper.Map<List<BAttachmentConfigDto>>(pg.data), pg.dataCount,
                    msg:$"{param.pageIndex}-{param.pageSize}/{pg.dataCount}", 
                    other: new { WhetherList = listWhether });
            }
            else
            {
                msgdata = baseSucc(mapper.Map<List<BAttachmentConfigDto>>(pg.data), pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 根据模块类型获取备案附件信息
        /// </summary>
        /// <param name="moduleType">
        /// 101：校服选用
        /// 102：校服采购
        /// 103：校服录入
        /// 104：选用组织
        /// 105：校服资助
        /// </param>
        /// <returns></returns>
        [HttpGet]
        [Route("getpagedbytype")]
        public async Task<Result<List<BAttachmentConfigDto>>> BAttachmentConfigGetPagedByType(int moduleType)
        {
            BAttachmentConfigParam param = new BAttachmentConfigParam();
            var msgdata = new Result<List<BAttachmentConfigDto>>();
            param.pageIndex = 1;
            param.pageSize = int.MaxValue;
            param.Statuz = 1;
            param.ModuleType = moduleType;
            PageModel<BAttachmentConfig> pg = await ibattachmentconfigservicesManager.GetPaged(param);
            msgdata = baseSucc(mapper.Map<List<BAttachmentConfigDto>>(pg.data), pg.dataCount, "查询成功");
            return msgdata;
        }



        /// <summary>
        /// 根据Id获取备案附件信息
        /// 备注：id传0,isFirst传true查询模块名称下拉框数据
        /// </summary>
        /// <param name="id">备案附件Id</param>
        /// <param name="isFirst">是否第一次调用</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getattachmentconfigbyid")]
        public async Task<Result<BAttachmentConfigDto>> BAttachmentConfigById(long id, bool isFirst = false)
        {
            var r = new Result<BAttachmentConfigDto>();
            BAttachmentConfig m = await ibattachmentconfigservicesManager.QueryById(id);
            if (m != null)
            {
                r.data.rows = mapper.Map<BAttachmentConfigDto>(m);
            }
            if (isFirst)
            {
                var listOpinionStatuz = EnumExtensions.EnumToList<ModuleTypeEnum>();
                if (listOpinionStatuz != null)
                {
                    List<dropdownModel> listModule = listOpinionStatuz.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();
                    r.data.other = new { OpinionStatuz = listModule };
                }
            }
            r.flag = 1;
            r.msg = "查询成功";
            return r;
        }


        /// <summary>
        /// 添加备案附件信息
        /// </summary>
        /// <param name="obj">
        /// BAttachmentConfigDto对象
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("add")]
        public async Task<Result<string>> BAttachmentConfigAdd([FromBody] BAttachmentConfigDto obj)
        {
          
            BAttachmentConfig o = mapper.Map<BAttachmentConfig>(obj);
            var r = await ibattachmentconfigservicesManager.InsertUpdate(o);
            return r;
        }

        /// <summary>
        /// 修改备案附件信息(对象中Id必传)
        /// </summary>
        /// <param name="obj">
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("edit")]
        public async Task<Result<string>> BAttachmentConfigEdit([FromBody] BAttachmentConfigDto obj)
        {
            BAttachmentConfig o = mapper.Map<BAttachmentConfig>(obj);
            var r = await ibattachmentconfigservicesManager.InsertUpdate(o);
            return r;
        }


        /// <summary>
        /// 根据Id删除备案附件信息
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("deletebyid")]
        public async Task<Result> BAttachmentConfigDeleteById(long id)
        {
            Result r = new Result();
            r = await ibattachmentconfigservicesManager.DeleteById(id);
            return r;
        }

        /// <summary>
        /// 根据Id启用禁用备案附件
        /// </summary>
        /// <param name="id">备案附件Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("setstatuz")]
        public async Task<Result> BAttachmentConfigSetStatuz(long id)
        {
            Result r = new Result();
            var obj = await ibattachmentconfigservicesManager.GetById(id);
            if (obj != null)
            {
                if(obj.Statuz == 0)
                {
                    obj.Statuz = 1;
                }
                else
                {
                    obj.Statuz = 0;
                }
                if(await ibattachmentconfigservicesManager.Update(obj))
                {
                    r.flag = 1;
                    r.msg = "设置成功";
                }
            }
            return r;
        }
    }
}
