namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///型号品牌表
    ///</summary>
    [SugarTable("dc_SchoolModelBrand","型号品牌表")]
    public class DcSchoolModelBrand : BaseEntity
    {

          public DcSchoolModelBrand()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///单位库Id
          /// </summary>
          public long SchoolCatalogId { get; set; }

           /// <summary>
           ///基础库Id
          /// </summary>
          public long BaseCatalogId { get; set; }

           /// <summary>
           ///单位物品型号Id
          /// </summary>
          public long SchoolMaterialModelId { get; set; }

           /// <summary>
           ///单位物品品牌Id
          /// </summary>
          public long SchoolMaterialBrandId { get; set; }

           /// <summary>
           ///状态（1：启用，0：禁用）
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///物品图片地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ImageUrl { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string Remark { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

