namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///危化品治理申报单位配置表
    ///</summary>
    [SugarTable("dc_GovernDeclareUnitConfig","危化品治理申报单位配置表")]
    public class DcGovernDeclareUnitConfig : BaseEntity
    {

          public DcGovernDeclareUnitConfig()
          {

          }

           /// <summary>
           ///区县Id
          /// </summary>
          public long CountyId { get; set; }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///状态(1:启是   0：否)
          /// </summary>
          public int Statuz { get; set; }

           /// <summary>
           ///创建时间
          /// </summary>
          public DateTime RegDate { get; set; }

           /// <summary>
           ///创建人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          public int Sort { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

