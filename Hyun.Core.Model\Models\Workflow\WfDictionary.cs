namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///审批字典表
    ///</summary>
    [SugarTable("wf_Dictionary", "审批字典表")]
    public class WfDictionary : BaseEntity
    {

        /// <summary>
        /// 子集
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<WfDictionary> children { get; set; }
  

        public WfDictionary()
        {
            children = new List<WfDictionary>();
        }

        /// <summary>
        /// 分类Id
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0")]
        public long ClassifyId { get; set; }

        /// <summary>
        ///类别名称
        /// </summary>
        [SugarColumn(Length = 31)]
        public string ClassifyName { get; set; }

        /// <summary>
        /// 深度
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0")]
        public int Depth { get; set; } = 0;

        /// <summary>
        /// 父级Id
        /// </summary>
        public long Pid { get; set; } = 0;
       

        /// <summary>
        ///名称
        /// </summary>
        [SugarColumn(Length = 31)]
        public string Name { get; set; }


        /// <summary>
        ///排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        ///备注说明
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Memo { get; set; }

        /// <summary>
        ///状态(1：启用 2：禁用)
        /// </summary>
        public int Statuz { get; set; }

        /// <summary>
        /// 类型（1：分类   2：子集）
        /// </summary>
        [SugarColumn(IsNullable = false,DefaultValue = "1")]
        public int DicType { get; set; } = 1;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


       
    }


}

