﻿using Hyun.Core.IServices.Uniform;
namespace Hyun.Core.Api
{

    [Route("api/hyun/xuniformorganization")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformOrganizationController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformOrganizationServices xuniformorganizationManager;
        private readonly IPUnitServices ipunitManager;
        private readonly IBAreaServices ibareaManager;
        private readonly IXUniformPurchaseServices uniformpurchaseManager;
        private readonly IBAttachmentServices ibattachmentManager;
        private readonly IXUniformConfigServices ixuniformConfigManager;
        public XUniformOrganizationController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IXUniformOrganizationServices _ixuniformorganizationservicesManager, IPUnitServices _ipunitManager, IBAreaServices _ibareaManager, IXUniformPurchaseServices _uniformpurchaseManager,IBAttachmentServices _ibattachmentManager, IXUniformConfigServices _ixuniformConfigManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            xuniformorganizationManager = _ixuniformorganizationservicesManager;
            ipunitManager = _ipunitManager;
            ibareaManager = _ibareaManager;
            uniformpurchaseManager = _uniformpurchaseManager;
            ibattachmentManager= _ibattachmentManager;
            ixuniformConfigManager = _ixuniformConfigManager;
        }

        #region 采购管理-选用组织查询列表


        /// <summary>
        /// 采购管理-选用组织-查询列表
        /// </summary>
        /// <param name="param">XUniformOrganizationParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getpaged")]
        public async Task<Result<List<XUniformOrganizationDto>>> XUniformOrganizationGetPaged([FromBody] XUniformOrganizationParam param)
        {
            Result<List<XUniformOrganizationDto>> r = new Result<List<XUniformOrganizationDto>>();
            param.UnitType = user.UnitTypeId;
            param.UserRoleIdList = new List<string>();
            if (user.UserRoleIds != null)
            {
                var roleIds = user.UserRoleIds.Split(',');
                param.UserRoleIdList.AddRange(roleIds);
            }
            if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
            {
                param.SchoolId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
            {
                param.CountyId = user.UnitId;
            }
            else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
            {
                param.CityId = user.UnitId;
            }
            PageModel<XUniformOrganizationDto> pg = await xuniformorganizationManager.GetPaged(param);
            if (pg != null && pg.data.Count > 0) {
                pg.data.ForEach(m => m.StatuzName = ((UniformFilingEnum)m.Statuz).GetDescription());
            }
            r.data.total = pg.dataCount;
            r.flag = 1;
            r.data.rows = pg.data;
            r.msg = $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}";
            List<BArea> listArea = null;
            if (param.isFirst == true)
            {
                List<dropdownModel> dropStatuzFiling = new List<dropdownModel>();
                List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                if (param.UserRoleIdList.Contains(RoleTypes.SchoolAdmin.ObjToInt().ToString()))
                {
                    var listFilingStatuz = EnumExtensions.EnumToList<UniformFilingEnum>();
                    if (listFilingStatuz != null)
                    {
                        foreach (var item in listFilingStatuz)
                        {
                            dropStatuzFiling.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                        }
                    }
                    r.data.other = new { StatuzList = dropStatuzFiling };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CoutyAdmin.ObjToInt().ToString()))
                {
                    var listFilingStatuz = EnumExtensions.EnumToList<UniformFilingEnum>();
                    if (listFilingStatuz != null)
                    {
                        foreach (var item in listFilingStatuz)
                        {
                            if (item.Value > UniformFilingEnum.SubmitNone.ObjToInt())
                            {
                                dropStatuzFiling.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                            }
                        }
                    }

                    var listSchool = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSchool != null)
                    {
                        foreach (var item in listSchool)
                        {
                            dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                    r.data.other = new { SchoolList = dropdownSchool, StatuzList = dropStatuzFiling };
                }
                else if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
                {
                    var listCounty = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        //获取所有区域Id
                        if (listArea == null)
                        {
                            listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                        }
                        foreach (var item in listCounty)
                        {
                            string name = item.Name;
                            if (listArea != null && listArea.Where(m => m.Id == item.AreaId).Count() > 0)
                            {
                                name = listArea.Where(m => m.Id == item.AreaId).FirstOrDefault().Name;
                            }
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = name });
                        }
                    }
                    var listFilingStatuz = EnumExtensions.EnumToList<UniformFilingEnum>();
                    if (listFilingStatuz != null)
                    {
                        foreach (var item in listFilingStatuz)
                        {
                            if (item.Value > UniformFilingEnum.SubmitNone.ObjToInt())
                            {
                                dropStatuzFiling.Add(new dropdownModel() { value = item.Value.ToString(), label = item.Description });
                            }
                        }
                    }
                    r.data.other = new { CountyList = dropdownCounty, SchoolList = dropdownSchool, StatuzList = dropStatuzFiling };
                }

            }
            if (param.UserRoleIdList.Contains(RoleTypes.CityAdmin.ObjToInt().ToString()))
            {
                if (listArea == null)
                {
                    listArea = await ibareaManager.Find(m => m.IsDeleted == false);
                }
                if (r.data.rows != null)
                {
                    r.data.rows.ForEach(f => f.CountyName = GetAreaName(f.CountyAreaId, f.CountyName, listArea));
                }
            }

            return r;
        }

        /// <summary>
        /// 采购管理-选用组织-查询列表获取区域名称
        /// </summary>
        /// <param name="countyAreaId">区县区域Id</param>
        /// <param name="name">区县单位名称，默认值</param>
        /// <param name="listArea">区域集合</param>
        /// <returns></returns>
        private string GetAreaName(long countyAreaId, string name, List<BArea> listArea)
        {
            if (listArea != null && listArea.Where(m => m.Id == countyAreaId).Count() > 0)
            {
                name = listArea.Where(m => m.Id == countyAreaId).FirstOrDefault().Name;
            }
            return name;
        }
        /// <summary>
        /// 采购管理-选用组织-根据Id获取详情信息
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("geteditbyid")]
        public async Task<Result<XUniformOrganizationDto>> GetEditById(long id)
        {
            Result<XUniformOrganizationDto> r = new Result<XUniformOrganizationDto>();
            var model = new XUniformOrganizationDto();
            if (id > 0)
            {
                var entity = await xuniformorganizationManager.GetById(id);
                if (entity == null)
                {
                    r.flag = 0;
                    r.msg = "当前选用组织信息已不存在。";
                    return r;
                }
                //model.PurchaseNo=entity.PurchaseNo;
                model.OrganizationYear = entity.OrganizationYear;
                model.OtherNum = entity.OtherNum;
                model.ParentNum = entity.ParentNum;
                model.SchoolAdminNum = entity.SchoolAdminNum;
                model.Statuz = entity.Statuz;
                model.StudentNum = entity.StudentNum;
                model.TeacherNum = entity.TeacherNum;

                var entityUnit = await ipunitManager.QueryById(entity.SchoolId);
                if (entityUnit != null)
                {
                    model.SchoolName = entityUnit.Name;
                    if (user.UnitTypeId == UnitTypeEnum.City.ObjToInt())
                    {
                        var entityCounty = await ipunitManager.QueryById(entityUnit.PId);
                        if (entityCounty != null)
                        {
                            model.CountyName = entityCounty.Name;
                            var entityArea = await ibareaManager.GetById(entityCounty.AreaId);
                            if (entityArea != null)
                            {
                                model.CountyName = entityArea.Name;
                            }
                        }
                    }
                }

                //获取附件。
                var list = await ibattachmentManager.Find(f => f.ObjectId == entity.Id && f.IsDeleted == false && f.ModuleType == ModuleTypeEnum.OrganizationCreate.ObjToInt());
                if (list != null && list.Count > 0)
                {
                    r.data.footer = (from item in list
                                     select new
                                     {
                                         Id = item.Id,
                                         Title = item.Title,
                                         Path = item.Path,
                                         Width = item.Width,
                                         Height = item.Height,
                                         DocType = item.DocType,
                                         IsDefault = item.IsDefault,
                                         Remark = item.Remark,
                                         FileCategory = item.FileCategory,
                                         Ext = item.Ext,
                                     });
                }
            }
            r.data.rows = model;
            r.flag = 1;
            r.msg = "获取数据成功。";
            return r;
        }

        #endregion

        #region 保存提交方法

        /// <summary>
        /// 采购管理-选用组织-保存信息
        /// </summary>
        /// <param name="model">XUniformOrganizationDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveedit")]
        public async Task<Result> SaveEdit([FromBody] XUniformOrganizationDto model)
        {
            model.SchoolId=user.UnitId;
            model.CountyId = user.UnitPId;
            model.CreateId = user.UserId;
            if (model.OtherNum > 1000000 || model.ParentNum > 1000000 || model.StudentNum > 1000000 || model.TeacherNum > 1000000 || model.SchoolAdminNum > 1000000)
            {
                return baseFailed("最大人数不能超过100万");
            }
            return await xuniformorganizationManager.InsertUpdate(model); 
        }

        /// <summary>
        /// 方案选用-根据Id删除附件数据
        /// </summary>
        /// <param name="id">校服选用表Id</param>
        /// <param name="attid">附件Id</param>
        /// <returns></returns>
        [HttpPut]
        [Route("delattachmentbyid")]
        public async Task<Result> DeleteAttachmentById(long id, long attid)
        {
            Result r = new Result();
            var model = new XUniformOrganizationDto();
            model.Id = id;
            model.AttachmentId = attid;
            model.SchoolId = user.UnitId;
            model.CreateId = user.UserId;
            r = await xuniformorganizationManager.UpdateAttachmentDelete(model);
            return r;
        }


        #endregion

        #region 提交备案


        /// <summary>
        /// 方案选用-提交备案
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpPut]
        [Route("savefiling")]
        public async Task<Result> FilingSubmit(long id)
        {
            Result r = new Result();
            var model = new XUniformOrganizationDto();
            model.Id = id;
            model.SchoolId = user.UnitId;
            model.CountyId=user.UnitPId;
            r = await xuniformorganizationManager.FilingSubmit(model);
            return r;
        }

        /// <summary>
        /// 方案选用-区县备查
        /// </summary>
        /// <param name="id">方案标识</param>
        /// <param name="statuz">状态 1：通过  2：退回</param>
        /// <param name="explanation">说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("confirmfiling")]
        public async Task<Result> FilingConfirm(long id, int statuz, string explanation = "")
        {
            Result r = new Result();
            var model = new XUniformOrganizationDto();
            model.Id = id;
            if (statuz == 1)
            {
                model.Statuz = UniformFilingEnum.Filinged.ObjToInt();
            }
            else
            {
                model.Statuz = UniformFilingEnum.SubmitNone.ObjToInt();
            }
            model.FilingExplanation = explanation;
            model.CountyId = user.UnitId;
            model.CreateId = user.UserId;
            r = await xuniformorganizationManager.FilingConfirm(model);
            return r;
        }

        /// <summary>
        /// 方案选用-区县退回
        /// </summary>
        /// <param name="id">方案标识</param>
        /// <param name="explanation">说明</param>
        /// <returns></returns>
        [HttpPut]
        [Route("filingbackout")]
        public async Task<Result> FilingBackout(long id, string explanation = "")
        {
            Result r = new Result();
            var model = new XUniformOrganizationDto();
            model.Id = id;
            model.FilingExplanation = explanation;
            model.CountyId = user.UnitId;
            r = await xuniformorganizationManager.FilingBackout(model);
            return r;
        }
        #endregion

    }
}
