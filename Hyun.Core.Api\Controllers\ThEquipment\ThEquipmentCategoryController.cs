﻿using FluentValidation;
using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.SearchModels.Common;
using Hyun.Core.Model.Validator;
using Hyun.Core.Services;
using SharpCompress.Common;
using SkyWalking.NetworkProtocol.V3;
using System;
using System.Collections.Generic;
namespace Hyun.Core.Api
{

    [Route("api/hyun/thequipmentcategory")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ThEquipmentCategoryController : BaseApiController
    {
        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IThEquipmentCategoryServices ithequipmentcategoryManager;
        private readonly IThEquipmentCategoryStageServices thEquipmentCategoryStageManager;
        private readonly IThEquipmentSchoolInfoServices thEquipmentSchoolInfoManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IPUnitServices ipunitManager;
        public ThEquipmentCategoryController(IMapper _mapper, IWebHostEnvironment _env, IUser _user, IThEquipmentCategoryServices _ithequipmentcategoryManager, IThEquipmentCategoryStageServices _thEquipmentCategoryStageManager, IThEquipmentSchoolInfoServices _thEquipmentSchoolInfoManager, IBDictionaryServices _dictionaryManager, IPUnitServices _ipunitManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            ithequipmentcategoryManager = _ithequipmentcategoryManager;
            thEquipmentCategoryStageManager = _thEquipmentCategoryStageManager;
            thEquipmentSchoolInfoManager = _thEquipmentSchoolInfoManager;
            dictionaryManager = _dictionaryManager;
            ipunitManager = _ipunitManager;
        }

        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="param">ThEquipmentCategoryParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategoryfind")]
        public async Task<Result<PageModel<ThEquipmentCategoryDto>>> ThEquipmentCategoryGetPaged([FromBody] ThEquipmentCategoryParam param)
        {
            PageModel<ThEquipmentCategoryDto> pg = new PageModel<ThEquipmentCategoryDto>();
            param.unitId = user.UnitId;
            if (user.UnitTypeId == UnitTypeEnum.School.ObjToInt())
            {
                param.unitId = user.UnitPId;
            }
            var pgResult = await ithequipmentcategoryManager.GetPaged(param);
            pg.data = mapper.Map<List<ThEquipmentCategoryDto>>(pgResult.data);
            pg.dataCount = pgResult.dataCount;
            object other = new object();
            if (param.isFirst)
            {
                long unitid = user.UnitId;
                if (user.UnitTypeId == UnitTypeEnum.School.ObjToInt())
                {
                    unitid = user.UnitPId;
                }
                var list = await ithequipmentcategoryManager.GetCategory(user.UnitId);

                //filePath :模版下载地址。 
                string downLoadFile = $"/Download/ThEquipmentCategory.xlsx";
                other = new { filePath = downLoadFile, CategoryList = list };
            }
            return baseSucc(pg, pg.dataCount, "查询成功", other);
        }

        /// <summary>
        /// 获取当前单位分类集合
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategory")]
        public async Task<Result<List<ThEquipmentCategoryDto>>> ThEquipmentCategory()
        {
            long unitid = user.UnitId;
            if (user.UnitTypeId == UnitTypeEnum.School.ObjToInt())
            {
                unitid = user.UnitPId;
            }
            var list = await ithequipmentcategoryManager.GetCategory(user.UnitId);
            return baseSucc(list, list.Count, "查询成功");
        }

        /// <summary>
        /// 根据Id获取对象
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategorybyid")]
        public async Task<Result<ThEquipmentCategoryDto>> ThEquipmentCategoryById(long id)
        {
            ThEquipmentCategory m = await ithequipmentcategoryManager.QueryById(id);
            if (m != null && m.UnitId == user.UnitId)
            {
                return baseSucc(mapper.Map<ThEquipmentCategoryDto>(m), 1);
            }
            return baseFailed<ThEquipmentCategoryDto>("执行失败，禁止获取其他单位信息。");
        }


        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="model">ThEquipmentCategoryDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategoryinsertupdate")]
        public async Task<Result<long>> ThEquipmentCategoryInsertUpdate([FromBody] ThEquipmentCategoryDto model)
        {
            ThEquipmentCategory o = mapper.Map<ThEquipmentCategory>(model);
            var validatorResult = new ThEquipmentCategoryValidator().Validate(o);
            if (!validatorResult.IsValid)
            {
                return baseFailed<long>(string.Join("<br/>", validatorResult.Errors.Select(m => m.ErrorMessage)));
            }
            model.Memo = DictionaryTypeCodeEnum.SchoolStage.ObjToString();
            List<BDictionary> list = await dictionaryManager.GetByTypeCode(DictionaryTypeCodeEnum.SchoolStage.ObjToInt().ToString());
            List<ThEquipmentCategoryStage> listStage = null;
            List<ThEquipmentCategoryStage> listAddStage = new List<ThEquipmentCategoryStage>();
            if (list != null && list.Count > 0)
            {
                if (model.SchoolStageName != null)
                {
                    SetAllStage(listAddStage, model.SchoolStageName, 0);
                    var listTemp = list.Where(m => model.SchoolStageName.Contains(m.DicName));
                    if (listTemp != null && listTemp.Count() > 0)
                    {
                        listAddStage.AddRange((from item in listTemp
                                               select new ThEquipmentCategoryStage()
                                               {
                                                   UnitId = user.UnitId,
                                                   SchoolStageId = int.Parse(item.DicValue),
                                                   Name = item.DicName,
                                                   Sort = item.Sequence ?? 0
                                               }));
                    }
                }
            }
            if (o.Id > 0)
            {

                ThEquipmentCategory entity = await ithequipmentcategoryManager.QueryById(model.Id);
                if (entity.UnitId != user.UnitId)
                {
                    return baseFailed<long>("修改失败，禁止修改其他单位信息。");
                }
                if (entity.SchoolStageName != model.SchoolStageName)
                {
                    //更新学段表
                    listStage = await thEquipmentCategoryStageManager.Find(n => n.EquipmentCategoryId == entity.Id);
                }
                entity.CategoryName = o.CategoryName;
                entity.EquipmentName = o.EquipmentName;
                entity.UnitName = o.UnitName;
                entity.Memo = o.Memo;
                entity.SchoolStageName = o.SchoolStageName;
                if (await ithequipmentcategoryManager.Update(o))
                {
                    //保存学段(保存前删除下)。
                    await thEquipmentCategoryStageManager.DeleteByCategoryId(new ThEquipmentCategoryStageDto() { EquipmentCategoryId = o.Id, ModifyBy = user.UserName, ModifyId = user.UserId, ModifyTime = DateTime.Now });

                    listAddStage.ForEach(n => n.EquipmentCategoryId = o.Id);
                    await thEquipmentCategoryStageManager.Add(listAddStage);
                    return baseSucc(o.Id, 1, "修改成功");
                }
                else
                {
                    return baseFailed<long>("");
                }
            }
            else
            {
                o.Id = BaseDBConfig.GetYitterId();
                o.UnitId = user.UnitId;
                if (await ithequipmentcategoryManager.Add(o) > 0)
                {
                    listAddStage.ForEach(n => n.EquipmentCategoryId = o.Id);
                    await thEquipmentCategoryStageManager.Add(listAddStage);
                    return baseSucc(o.Id, 1, "保存成功");
                }
                else
                {
                    return baseFailed<long>("");
                }
            }
        }

        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="model">ThEquipmentCategoryDto对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategorysetstatuz")]
        public async Task<Result> ThEquipmentCategorySetStatuz([FromBody] ThEquipmentCategoryDto model)
        {
            ThEquipmentCategory entity = await ithequipmentcategoryManager.QueryById(model.Id);
            if (entity == null)
            {
                return baseFailed("设置失败，当前数据已不存在。");
            }
            if (entity.UnitId != user.UnitId)
            {
                return baseFailed("修改失败，禁止修改其他单位信息。");
            }
            if (entity.Statuz == StatuzEnum.Enable.ObjToInt())
            {
                entity.Statuz = StatuzEnum.Disable.ObjToInt();
            }
            else
            {
                entity.Statuz = StatuzEnum.Enable.ObjToInt();
            }
            if (await ithequipmentcategoryManager.Update(entity, new List<string>() { "Statuz", "ModifyBy", "ModifyId", "ModifyTime" }))
            {
                return baseSucc("设置成功", 1);
            }
            else
            {
                return baseFailed("执行异常，请刷新重新操作。");
            }
        }

        /// <summary>
        /// 根据Id删除
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategorydelbyid")]
        public async Task<Result> ThEquipmentCategoryDelById(long id)
        {
            ThEquipmentCategory entity = await ithequipmentcategoryManager.QueryById(id);
            if (entity.UnitId != user.UnitId)
            {
                return baseFailed("删除失败，禁止删除其他单位信息。");
            }
            entity.IsDeleted = true;
            if (await ithequipmentcategoryManager.Update(entity, new List<string>() { "IsDeleted", "ModifyBy", "ModifyId", "ModifyTime" }))
            {
                return baseSucc("删除成功", 1);
            }
            else
            {
                return baseFailed("执行异常，请刷新重新操作。");
            }
        }


        /// <summary>
        /// 导入装备分类信息
        /// </summary>
        /// <param name="param"></param>
        /// <remarks>
        /// 根据分类、名称一致，禁止导入，重复已存在。
        /// </remarks>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategoryuploadfile")]
        public async Task<Result> ThEquipmentCategoryUploadFile([FromBody] ImportUnitParam param)
        {
            Result r = new Result();
            if (!user.IsSystemUser && !user.Roles.Contains(RoleTypes.CoutyAdmin))
            {
                r.flag = 0;
                r.msg = "您无权使用该功能";
                return r;
            }

            List<ThEquipmentCategoryDto> list = new ExcelHelper<ThEquipmentCategoryDto>().ImportFromExcel(env.ContentRootPath, param.FilePath, 1);
            list = list.Where(m => m.CategoryName != null && m.CategoryName.Trim().Length > 0 && m.EquipmentName != null && m.EquipmentName.Trim().Length > 0).ToList();
            if (list.Count > 0)
            {
                string errorMsg = "";
                //判断导入单位编码是否重复
                var listGroup = list
                    .GroupBy(f => new { f.CategoryName, f.EquipmentName })
                .Select(group => new ThEquipmentCategoryDto
                {
                    CategoryName = group.Key.CategoryName,
                    EquipmentName = group.Key.EquipmentName,
                    Total = group.Count()
                }).ToList();

                if (listGroup.Exists(f => f.Total > 1))
                {
                    var listTip = listGroup.Where(f => f.Total > 1).Select(f => "【装备分类：" + f.CategoryName + "装备名称：" + f.EquipmentName + "】").ToList();
                    errorMsg = string.Join(";", listTip);
                    r.flag = 0;
                    r.msg = $"导入的数据中{errorMsg}存在重复";
                    return r;
                }
                //获取所有单位信息
                var listDbData = await ithequipmentcategoryManager.Find(f => f.IsDeleted == false && f.UnitId == user.UnitId);
                List<ThEquipmentCategory> listAdd = new List<ThEquipmentCategory>();
                int index = 2;
                int sortMax = 5;
                if (listDbData!=null && listDbData.Count > 0)
                {
                    sortMax = listDbData.Max(m => m.Sort);
                    sortMax += 5;
                }
                foreach (ThEquipmentCategoryDto item in list)
                {
                    ThEquipmentCategory model = mapper.Map<ThEquipmentCategory>(item);
                    var validatorResult = new ThEquipmentCategoryValidator().Validate(model);
                    if (!validatorResult.IsValid)
                    {
                        errorMsg += $"第{index}行;{string.Join("<br/>", validatorResult.Errors.Select(m => m.ErrorMessage))}\n";
                    }
                    if (listDbData != null && listDbData.Where(m => m.CategoryName == item.CategoryName && m.EquipmentName == model.EquipmentName).Count() > 0)
                    {
                        errorMsg += $"第{index}行;装备分类、装备名称已存在相同的，禁止重复添加。\n";
                    }
                    model.UnitId = user.UnitId;
                    sortMax += 5;
                    model.Sort = sortMax;
                    listAdd.Add(model);
                    index++;
                }

                if (string.IsNullOrEmpty(errorMsg))
                {
                    List<BDictionary> listStage = await dictionaryManager.GetByTypeCode(DictionaryTypeCodeEnum.SchoolStage.ObjToInt().ToString());
                    var idlist = await ithequipmentcategoryManager.Add(listAdd);
                    if (idlist != null && idlist.Count > 0)
                    {
                        List<ThEquipmentCategoryStage> listAddStage = new List<ThEquipmentCategoryStage>();
                        foreach (var item in listAdd)
                        {
                            if (item.SchoolStageName != null)
                            {
                                SetAllStage(listAddStage, item.SchoolStageName, item.Id);
                                var listTemp = listStage.Where(m => item.SchoolStageName.Contains(m.DicName));
                                if (listTemp != null && listTemp.Count() > 0)
                                {
                                    listAddStage.AddRange((from itemtemp in listTemp
                                                           select new ThEquipmentCategoryStage()
                                                           {
                                                               EquipmentCategoryId = item.Id,
                                                               UnitId = user.UnitId,
                                                               SchoolStageId = int.Parse(itemtemp.DicValue),
                                                               Name = itemtemp.DicName,
                                                               Sort = itemtemp.Sequence ?? 0
                                                           }));
                                }
                            }
                        }
                        await thEquipmentCategoryStageManager.Add(listAddStage);
                        return baseSucc("导入成功!", 1);
                    }
                    else
                    {
                        return baseFailed("导入失败，请检查数据刷新重新操作。");
                    }
                }
                else
                {
                    return baseFailed(errorMsg);
                }
            }
            else
            {
                return baseFailed("未找到导入的数据");
            }
        }

        private void SetAllStage(List<ThEquipmentCategoryStage> list, string schoolstagename, long id)
        {
            if (list != null)
            {
                if (schoolstagename != null && schoolstagename.Contains("全部"))
                {
                    list.Add(new Model.Models.ThEquipmentCategoryStage()
                    {
                        EquipmentCategoryId = id,
                        UnitId = user.UnitId,
                        SchoolStageId = 0,
                        Name = "全部",
                        Sort = 0
                    });
                }
            }
        }

        #region 单位查询、填写保存。

        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="param">ThEquipmentCategoryParam对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategoryschoolfind")]
        public async Task<Result<PageModel<ThEquipmentSchoolInfoDto>>> ThEquipmentCategoryGetSchoolPaged([FromBody] ThEquipmentCategoryParam param)
        {
            param.UnitTypeId = user.UnitTypeId;
            if (user.UnitTypeId == UnitTypeEnum.School.ObjToInt())
            {
                param.SchoolId = user.UnitId;
            }
            else if (user.UnitTypeId == UnitTypeEnum.County.ObjToInt())
            {
                param.unitId = user.UnitId;
            }
            var pg = await ithequipmentcategoryManager.GetPagedBySchool(param);
            object other = new object();
            if (param.isFirst)
            {
                long unitid = user.UnitId;
                if (user.UnitTypeId == UnitTypeEnum.School.ObjToInt())
                {
                    unitid = user.UnitPId;
                }
                var list = await ithequipmentcategoryManager.GetCategory(unitid);

                //单位下拉
                List<dropdownModel> dropdownSchool = new List<dropdownModel>();
                if (user.UnitTypeId == UnitTypeEnum.County.ObjToInt())
                {
                    List<PUnit> listSchool = await ipunitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listSchool != null)
                    {
                        foreach (var item in listSchool)
                        {
                            dropdownSchool.Add(new dropdownModel() { value = item.Id.ToString(), label = item.Name });
                        }
                    }
                }
                other = new { CategoryList = list, SchoolList = dropdownSchool };
            }
            return baseSucc(pg, pg.dataCount, "查询成功", other, pg.Statistics);
        }


        /// <summary>
        /// 新增修改
        /// </summary>
        /// <param name="list">保存数据集合</param>
        /// <returns></returns>
        [HttpPost]
        [Route("thequipmentcategoryschoolsave")]
        public async Task<Result<DateTime>> ThEquipmentCategorySchoolSave([FromBody] List<ThEquipmentSchoolInfoDto> list)
        {
            if (list != null && list.Count > 0)
            {
                ThEquipmentSchoolInfo entity = null;
                var listData = await thEquipmentSchoolInfoManager.Find(n => n.IsDeleted == false && n.UnitId == user.UnitId);
                List<long> idList = new List<long>();
                for (int i = 0; i < list.Count; i++)
                {
                    bool IsChange = true;
                    var model = list[i];
                    if (listData != null)
                    {
                        entity = listData.Where(m => m.EquipmentCategoryId == list[i].EquipmentCategoryId).FirstOrDefault();
                    }
                    if (entity == null)
                    {
                        entity = new ThEquipmentSchoolInfo();
                        entity.UnitId = user.UnitId;
                        entity.EquipmentCategoryId = model.EquipmentCategoryId;
                    }
                    else
                    {
                        //验证是否有变动。
                        if (entity.StandardNum != model.StandardNum)
                        {

                        }
                        else if (entity.StockNum != model.StockNum)
                        {
                        
                        }
                        else if (entity.DifferenceNum != model.DifferenceNum)
                        {

                        }
                        else
                        {
                            IsChange = false;
                        }
                    }
                    entity.StandardNum = model.StandardNum;
                    entity.StockNum = model.StockNum;
                    if (entity.StandardNum != null && entity.StockNum != null)
                    {
                        entity.DifferenceNum = ((entity.StockNum ?? 0) - (entity.StandardNum ?? 0));
                    }
                    else if (entity.StandardNum != null)
                    {
                        entity.DifferenceNum = (entity.StandardNum ?? 0);
                        if (entity.DifferenceNum> 0)
                        {
                            entity.DifferenceNum = -entity.DifferenceNum;
                        }
                    }
                    else if (entity.StockNum != null)
                    {
                        entity.DifferenceNum = (entity.StockNum ?? 0);
                    }
                    else
                    {
                        entity.DifferenceNum = null;
                    }
                   
                    entity.Memo = model.Memo;

                    if (entity.Id > 0)
                    {
                        if (IsChange)
                        {
                            entity.ModifyBy = user.UserName;
                            entity.ModifyId = user.UserId;
                            entity.ModifyTime = DateTime.Now;
                            if (await thEquipmentSchoolInfoManager.Update(entity, new List<string>() { "StandardNum", "StockNum", "DifferenceNum", "Memo", "ModifyBy", "ModifyId", "ModifyTime" }))
                            {
                                idList.Add(entity.Id);
                            }
                        }
                        else
                        {
                            idList.Add(entity.Id);
                        }
                    }
                    else
                    {
                        entity.Id = BaseDBConfig.GetYitterId();
                        if (!(entity.StandardNum == null && entity.StockNum == null))
                        {
                            if (await thEquipmentSchoolInfoManager.Add(entity) > 0)
                            {
                                idList.Add(entity.Id);
                            }
                        }
                        else
                        {
                            idList.Add(entity.Id);
                        }
                    }
                }
                if (idList.Count > 0 && idList.Count == list.Count)
                {
                    return baseSucc(DateTime.Now, 1, "保存成功");
                }
                else
                {
                    return baseSucc(DateTime.Now, 1, $"保存成功【{idList.Count}】条,失败【{list.Count - idList.Count}】条");
                }
            }
            else
            {
                return baseFailed<DateTime>("保存失败，没有需要保存的数据。");
            }
        } 
        #endregion

    }
}
