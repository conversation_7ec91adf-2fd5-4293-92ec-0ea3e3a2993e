namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///报废记录
    ///</summary>
    [SugarTable("dc_Scrap", "报废记录")]
    public class DcScrap : BaseEntity
    {

        public DcScrap()
        {

        }

        /// <summary>
        ///单位物品库Id
        /// </summary>
        public long SchoolMaterialId { get; set; }

        /// <summary>
        ///报废数量
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? ScrapNum { get; set; }

        /// <summary>
        ///报废理由
        /// </summary>
        [SugarColumn(Length = 511, IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        ///报废时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ScrapDate { get; set; }

        /// <summary>
        ///创建时间
        /// </summary>
        public DateTime RegDate { get; set; }

        /// <summary>
        ///创建人Id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        ///废弃物状态（1：待处置；2：处置中；3：已处置[处置信息填写完成] ；4：处置审核不通过）
        /// </summary>
        public int WasetStatuz { get; set; }

        /// <summary>
        ///处置完成时间（处置信息填写完成后更新）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? DisposalDate { get; set; }

        /// <summary>
        ///废弃物处置dc_WasteDisposal表Id（处置申请时写入）
        /// </summary>
        public long WasteDisposalId { get; set; }

        /// <summary>
        ///数据类型（1：正常业务数据，2：冗余数据，为显示处置不通过时数据）
        /// </summary>
        public int DataType { get; set; } = 1;

        /// <summary>
        ///中立字段，某些表可使用某些表不使用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public new bool Enabled { get; set; } = true;


    }


}

