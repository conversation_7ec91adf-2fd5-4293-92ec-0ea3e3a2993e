﻿using Hyun.Core.Common.DB;
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.Validator;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;
using Org.BouncyCastle.Utilities.IO;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Diagnostics;
namespace Hyun.Core.Services
{

    ///<summary>
    ///WfProjectAuditUser方法
    ///</summary>
    public class WfProjectAuditUserServices : BaseServices<WfProjectAuditUser>, IWfProjectAuditUserServices
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IUser user;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="_user"></param>
        public WfProjectAuditUserServices(IUser _user)
        {
            user = _user;
        }


        /// <summary>
        /// 权限设置列表
        /// </summary>
        /// <returns></returns>
        public async Task<PageModel<ModuleAuditModel>> GetProcessAuditUserList()
        {
            List<ModuleAuditModel> listAuditModel = new List<ModuleAuditModel>();
            //获取“审批节点人员设置wf_ProjectAuditUser”数据信息
            List<WfProjectAuditUserDto> listAll = await this.Db.Queryable<WfProjectAuditUser>()
                //.InnerJoin<SysUserInfo>((PAU, U) => PAU.AuditUserId == U.UserExtensionId)
                .InnerJoin<SysUserExtension>((PAU,UE)=>PAU.AuditUserId == UE.Id && UE.Statuz == 1)
                .Where((PAU,UE) => PAU.AuditUnitId == user.UnitId && PAU.IsDeleted == false && UE.IsDeleted == false)
                .Select((PAU, UE) => new WfProjectAuditUserDto()
                {
                    Id = PAU.Id,
                    BusinessType = PAU.BusinessType,
                    ProcessId = PAU.ProcessId,
                    ProcessNodeId = PAU.ProcessNodeId,
                    AuditUnitId = PAU.AuditUnitId,
                    AuditUserId = PAU.AuditUserId,
                    AuditUserName = UE.Name

                }).ToListAsync();

            long cityId = 0;
            if(user.UnitTypeId == 3)
            {
                PUnit unitCounty = await this.Db.Queryable<PUnit>().Where(f => f.Id == user.UnitPId).FirstAsync();
                if (unitCounty != null)
                {
                    cityId = unitCounty.PId;
                }
            }

            //获取禁用流程表数据
            var listProcessId = await this.Db.Queryable<WfChildUnitDisable>().Where(f => f.IsDeleted == false && f.DisableUnitId == user.UnitId).Select(f => f.ProcessId).ToListAsync();

            //获取模块流程信息
            var listModuleProcess = await this.Db.Queryable<WfModule>()
                            .InnerJoin<WfProcess>((M, P) => M.Id == P.ModuleId)
                            .Where((M, P) => !listProcessId.Contains(P.Id) && M.IsDeleted == false && P.IsDeleted == false && M.Statuz == 1 && P.Statuz == 1 && ((P.Usage == 1 && P.UseUnitId == user.UnitId) || (P.Usage == 2 && P.UseUnitId == user.UnitId) || (P.Usage == 2 && P.UseUnitId == user.UnitPId) || (P.Usage == 2 && P.UseUnitId == cityId)))
                            .Select((M, P) => new WfProjectAuditUserDto
                            {
                                ModuleId = M.Id,
                                ModuleName = M.Name,
                                ProcessId = P.Id,
                                ProcessName = P.ProcessName,
                                IsOpen = P.IsOpen,
                            }).ToListAsync();

            
            var list = await this.Db.Queryable<WfProcessReturnSet>()
                .InnerJoin<WfModule>((PRS, M) => PRS.ModuleId == M.Id)
                .InnerJoin<WfProcess>((PRS, M, P) => PRS.ProcessId == P.Id)
                .LeftJoin<WfProcessNode>((PRS, M, P, PN) => PRS.ProcessNodeId == PN.Id)
                .LeftJoin<WfLookEachOtherSet>((PRS, M, P, PN, LEOS) => LEOS.ProcessId == P.Id && LEOS.ProcessNodeId == PN.Id && LEOS.UnitId == user.UnitId)
                .Where((PRS, M, P, PN, LEOS) => !listProcessId.Contains(P.Id) && PRS.IsDeleted == false && PN.ProcessLevel == user.UnitTypeId && M.Statuz == 1 && P.Statuz == 1 && ((P.Usage == 1 && P.UseUnitId == user.UnitId) || (P.Usage == 2 && P.UseUnitId == user.UnitId) || (P.Usage == 2 && P.UseUnitId == user.UnitPId) || (P.Usage == 2 && P.UseUnitId == cityId)))
                .Select((PRS, M, P, PN, LEOS) => new WfProjectAuditUserDto
                {
                    ModuleId = PRS.ModuleId,
                    ModuleName = M.Name,
                    ProcessId = PRS.ProcessId,
                    ProcessNodeId = PRS.ProcessNodeId,
                    ProcessName = P.ProcessName,
                    ProcessNodeName = PN.NodeShowName,
                    RoleName = PN.RoleName,
                    AuditUserName = "",
                    Sort = PRS.Sort ?? 0,
                    IsOpen = P.IsOpen,
                    IsLook = SqlFunc.IsNull(LEOS.IsLook, 2),
                    UseGroupValue = SqlFunc.IsNull(PN.UseGroupValue,2)
                }).ToListAsync();


            if (listModuleProcess.Count > 0)
            {
                //var listProcessAudit = list.GroupBy(f => new { f.ModuleId, f.ModuleName, f.ProcessId, f.ProcessName, f.IsOpen }).ToList();
                foreach (var p in listModuleProcess)
                {
                    if (p.IsOpen == 1)
                    {
                        list.Add(new WfProjectAuditUserDto
                        {
                            ModuleId = p.ModuleId,
                            ModuleName = p.ModuleName,
                            ProcessId = p.ProcessId,
                            ProcessNodeId = 20000,
                            ProcessName = p.ProcessName,
                            ProcessNodeName = "项目库",
                            RoleName = "",
                            IsLook = 0,
                            AuditUserName = "",
                            Sort = 20000,
                            UseGroupValue = 2
                        });
                    }
                }

                string strNames = "";
                List<long> listUserId = new List<long>();
                foreach (var item in list)
                {
                    strNames = "";
                    listUserId = new List<long>();
                    List<WfProjectAuditUserDto> listProjectAuditUser = listAll.Where(f => f.ProcessId == item.ProcessId && f.ProcessNodeId == item.ProcessNodeId).ToList();
                    if (listProjectAuditUser.Count > 0)
                    {
                        strNames = string.Join(",", listProjectAuditUser.Select(f => f.AuditUserName).ToList());
                        listUserId = listProjectAuditUser.Select(f => f.AuditUserId.Value).ToList();
                    }
                    item.AuditUserName = strNames;
                    item.ListAuditUserId = listUserId;
                }

                var listModuleAudit = listModuleProcess.GroupBy(f => new { f.ModuleId, f.ModuleName }).ToList();
                foreach (var m in listModuleAudit)
                {
                    List<WfProjectAuditUserDto> listDto = list.Where(f => f.ModuleId == m.Key.ModuleId).OrderBy(f=>f.ProcessId).ThenBy(f=>f.Sort).ToList();
                    if (listDto.Count > 0)
                    {
                        strNames = "";
                        listUserId = new List<long>();
                        List<WfProjectAuditUserDto> listProjectAuditUser = listAll.Where(f => f.ProcessId == m.Key.ModuleId && f.ProcessNodeId == 30000).ToList();
                        if (listProjectAuditUser.Count > 0)
                        {
                            strNames = string.Join(",", listProjectAuditUser.Select(f => f.AuditUserName).ToList());
                            listUserId = listProjectAuditUser.Select(f => f.AuditUserId.Value).ToList();
                        }

                        listDto.Add(new WfProjectAuditUserDto()
                        {
                            ModuleId = m.Key.ModuleId,
                            ModuleName = m.Key.ModuleName,
                            ProcessId = m.Key.ModuleId,
                            ProcessNodeId = 30000,
                            ProcessName = "--",
                            ProcessNodeName = "数据报表",
                            AuditUserName = strNames,
                            ListAuditUserId = listUserId,
                            IsLook = 0,
                            RoleName = "",
                            Sort = 30000,
                            UseGroupValue = 2
                        });

                        listAuditModel.Add(new ModuleAuditModel()
                        {
                            ModuleId = m.Key.ModuleId.Value,
                            ModuleName = m.Key.ModuleName,
                            ListAuditUser = listDto
                        });
                    }
                }
            }
            else
            {
                var listModule = await this.Db.Queryable<WfModule>().Where(f => f.IsDeleted == false && f.Statuz == 1 && ((f.Usage == 1 && f.UseUnitId == user.UnitId) || (f.Usage == 2 && f.UseUnitId == user.UnitId) || (f.Usage == 2 && f.UseUnitId == user.UnitPId) || (f.Usage == 2 && f.UseUnitId == cityId))).ToListAsync();
                foreach (var m in listModule)
                {
                    string strNames = "";
                    List<long> listUserId = new List<long>();
                    List<WfProjectAuditUserDto> listProjectAuditUser = listAll.Where(f => f.ProcessId == m.Id && f.ProcessNodeId == 30000).ToList();
                    if (listProjectAuditUser.Count > 0)
                    {
                        strNames = string.Join(",", listProjectAuditUser.Select(f => f.AuditUserName).ToList());
                        listUserId = listProjectAuditUser.Select(f => f.AuditUserId.Value).ToList();
                    }
                    List<WfProjectAuditUserDto> listDto = new List<WfProjectAuditUserDto>();
                    listDto.Add(new WfProjectAuditUserDto()
                    {
                        ModuleId = m.Id,
                        ModuleName = m.Name,
                        ProcessId = m.Id,
                        ProcessNodeId = 30000,
                        ProcessName = "--",
                        ProcessNodeName = "数据报表",
                        AuditUserName = strNames,
                        ListAuditUserId = listUserId,
                        IsLook = 0,
                        RoleName = "",
                        Sort = 30000,
                        UseGroupValue = 2
                    });

                    listAuditModel.Add(new ModuleAuditModel()
                    {
                        ModuleId = m.Id,
                        ModuleName = m.Name,
                        ListAuditUser = listDto
                    });
                }
            }
            PageModel<ModuleAuditModel> pageList = new PageModel<ModuleAuditModel>();
            pageList.data = listAuditModel;
            pageList.dataCount = listAuditModel.Count;
            return pageList;
        }

        /// <summary>
        /// 获取设置的人员名称
        /// </summary>
        /// <param name="processId">流程Id</param>
        /// <param name="processNodeId">流程节点Id</param>
        /// <returns></returns>

        public async Task<List<SetUserListModel>> GetSetUserList(long processId,long processNodeId)
        {
            List<SetUserListModel> list = new List<SetUserListModel>();
            list = await this.Db.Queryable<SysUserInfo>()
                 .InnerJoin<SysUserExtension>((UI, SUE) => UI.UserExtensionId == SUE.Id)
                 .InnerJoin<SysUserRole>((UI, SUE, UR) => UI.UserExtensionId == UR.UserId && UI.Statuz == 1)
                 .LeftJoin<WfProjectAuditUser>((UI, SUE, UR, AU) => UI.UserExtensionId == AU.AuditUserId && AU.ProcessId == processId && AU.ProcessNodeId == processNodeId && AU.AuditUnitId == user.UnitId)
                 .Where((UI, SUE, UR, AU) => SUE.UnitId == user.UnitId)
                 .WhereIF(user.UnitTypeId == 1, (UI, SUE, UR, AU) => UR.RoleId == 1200)
                 .WhereIF(user.UnitTypeId == 2, (UI, SUE, UR, AU) => UR.RoleId == 2200)
                 .WhereIF(user.UnitTypeId == 3, (UI, SUE, UR, AU) => UR.RoleId == 3200)
                 .GroupBy((UI, SUE, UR, AU) => new { UserId = UI.UserExtensionId, UserName = SUE.Name, AU.Id/*, IsCheck = SqlFunc.IsNull(AU.Id,0)*/})
                 .Select((UI, SUE, UR, AU) => new SetUserListModel { UserId = UI.UserExtensionId, UserName = SUE.Name, IsCheck = SqlFunc.IsNull(AU.Id, 0) })
                 .ToListAsync();
            return list;
        }

        /// <summary>
        /// 根据流程节点Id获取是否配置互看信息
        /// </summary>
        /// <param name="processId"></param>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        public async Task<WfLookEachOtherSet> GetProcessNodeLook(long processId, long processNodeId)
        {
            WfLookEachOtherSet lookSet = await this.Db.Queryable<WfLookEachOtherSet>().Where(f => f.IsDeleted == false && f.ProcessId == processId && f.ProcessNodeId == processNodeId && f.UnitId == user.UnitId).FirstAsync();
            return lookSet;
        }

        /// <summary>
        /// 确定设置用户
        /// </summary>
        /// <param name="userModel"></param>
        /// <returns></returns>
        public async Task<Result<string>> SetUserList(UserListModel userModel)
        {
            if (userModel.ListUserId.Count == 0)
            {
                //return Result<string>.Fail("请至少设置一个人员");
                await this.Db.Updateable<WfProjectAuditUser>().SetColumns(f => new WfProjectAuditUser() { IsDeleted = true}).Where(f => f.ProcessId == userModel.ProcessId && f.ProcessNodeId == userModel.ProcessNodeId && f.AuditUnitId == user.UnitId).ExecuteCommandAsync();
            }
            else
            {
                List<long> listSetUser = userModel.ListUserId;
                if (listSetUser.GroupBy(f => f).Any(g => g.Count() > 1))
                {
                    return Result<string>.Fail("非法操作，设置人员中存在重复数据!");
                }
                //WfProcess objProcess = await this.Db.Queryable<WfProcess>().Where(f => f.Id == userModel.ProcessId).FirstAsync();
                //if(objProcess == null)
                //{
                //    return Result<string>.Fail("非法操作，流程Id不存在!");
                //}
                List<SetUserListModel> listUserAll = await GetSetUserList(userModel.ProcessId, userModel.ProcessNodeId);
                List<long> listUserIdAll = listUserAll.Select(f => f.UserId).ToList();

                // 找出list1中存在但不在list2中的元素
                var listDiffUserId = listSetUser.Except(listUserIdAll).ToList();
                if (listDiffUserId.Count > 0)
                {
                    return Result<string>.Fail($"非法操作，设置人员中“{string.Join(",", listDiffUserId)}”不在该单位或不具备该权限!");
                }

                //查询原设置数据
                List<WfProjectAuditUser> listOldAuditUser = await this.Db.Queryable<WfProjectAuditUser>().Where(f => f.ProcessId == userModel.ProcessId && f.AuditUnitId == user.UnitId && f.ProcessNodeId == userModel.ProcessNodeId && f.IsDeleted == false).ToListAsync();
                List<long> listOldUserId = listOldAuditUser.Select(f => f.AuditUserId.Value).ToList();

                List<WfProjectAuditUser> listAuditUserAdd = new List<WfProjectAuditUser>();
                int businessType = 1;
                if (userModel.ProcessNodeId.Equals(20000))
                {
                    businessType = 2;
                }
                else if (userModel.ProcessNodeId.Equals(30000))
                {
                    businessType = 3;
                }
                //判断需要添加的数据
                List<long> listAdd = listSetUser.Except(listOldUserId).ToList();
                List<long> listDelete = listOldUserId.Except(listSetUser).ToList();

                if (listAdd.Count > 0)
                {
                    foreach (long userId in listAdd)
                    {
                        listAuditUserAdd.Add(new WfProjectAuditUser()
                        {
                            Id = BaseDBConfig.GetYitterId(),
                            BusinessType = businessType,
                            ProcessId = userModel.ProcessId,
                            ProcessNodeId = userModel.ProcessNodeId,
                            AuditUnitId = user.UnitId,
                            AuditUserId = userId,
                            Remark = "",
                            ModuleId = userModel.ModuleId
                        });
                    }

                    //添加
                    await this.Db.Insertable<WfProjectAuditUser>(listAuditUserAdd).ExecuteCommandAsync();
                }

                if (listDelete.Count > 0)
                {
                    //更新
                    await this.Db.Updateable<WfProjectAuditUser>().SetColumns(f => new WfProjectAuditUser() { IsDeleted = true }).Where(f => f.ProcessId == userModel.ProcessId && f.ProcessNodeId == userModel.ProcessNodeId && f.AuditUnitId == user.UnitId && listDelete.Contains(f.AuditUserId.Value)).ExecuteCommandAsync();
                }

                //处理互看功能
                //判断是否存在
                WfLookEachOtherSet obj = await this.Db.Queryable<WfLookEachOtherSet>().Where(f => f.IsDeleted == false && f.ProcessId == userModel.ProcessId && f.ProcessNodeId == userModel.ProcessNodeId && f.UnitId == user.UnitId).FirstAsync();
                if (obj != null)
                {
                    obj.IsLook = userModel.IsLook;
                    await this.Db.Updateable<WfLookEachOtherSet>(obj).ExecuteCommandAsync();
                }
                else
                {
                    obj = new WfLookEachOtherSet();
                    obj.Id = BaseDBConfig.GetYitterId();
                    obj.ModuleId = userModel.ModuleId;
                    obj.ProcessId = userModel.ProcessId;
                    obj.ProcessNodeId = userModel.ProcessNodeId;
                    obj.UnitId = user.UnitId;
                    obj.IsLook = userModel.IsLook;
                    await this.Db.Insertable<WfLookEachOtherSet>(obj).ExecuteCommandAsync();
                }
            }
            return Result<string>.Success("设置成功");
        }

        /// <summary>
        /// 根据用户Id获取该用户模块流程节点菜单配置信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<ModuleMenuModel>> GetModuleMenuList()
        {

            //获取禁用流程表数据
            var listProcessId = await this.Db.Queryable<WfChildUnitDisable>().Where(f => f.IsDeleted == false && f.DisableUnitId == user.UnitId).Select(f => f.ProcessId).ToListAsync();

            List<ModuleMenuModel> list = await this.Db.Queryable<WfProjectAuditUser>()
                .InnerJoin<WfProcess>((PAU, P) => PAU.ProcessId == P.Id && P.IsDeleted == false && P.Statuz == 1)
                .InnerJoin<WfModule>((PAU, P, M) => P.ModuleId == M.Id && M.IsDeleted == false)
                .LeftJoin<WfProcessNode>((PAU, P, M, PN) => PAU.ProcessNodeId == PN.Id && PN.IsDeleted == false)
                .LeftJoin<WfProcessReturnSet>((PAU, P, M, PN, PRS) => PN.Id == PRS.ProcessNodeId && PRS.IsDeleted == false)
                .Where((PAU, P, M, PN, PRS) => !listProcessId.Contains(P.Id) && PAU.IsDeleted == false && PAU.AuditUserId == user.ID)
                //.OrderBy((PAU, P, M, PN) => new {M.Sort,P.Id, ProcessNodeSort = PN.Sort })
                .Select((PAU, P, M, PN, PRS) => new ModuleMenuModel
                {
                    ModuleId = M.Id,
                    ModuleName = M.Name,
                    Logo = M.Logo,
                    ProcessId = P.Id,
                    ProcessName = P.ProcessName,
                    ProcessNodeId =SqlFunc.IsNull(PN.Id,PAU.ProcessNodeId.Value),
                    ProcessNodeName = SqlFunc.IsNull(PN.NodeShowName,""),
                    NodeType = SqlFunc.IsNull(PN.NodeType.Value,0),
                    TreatHandle = SqlFunc.IsNull(PN.TreatHandle,""),
                    TreatHandleUrl = SqlFunc.IsNull(PN.TreatHandleUrl, ""),
                    StopHandle = SqlFunc.IsNull(PN.StopHandle, ""),
                    StopHandleUrl = SqlFunc.IsNull(PN.StopHandleUrl, ""),
                    //Sort = SqlFunc.IsNull(PRS.Sort, PAU.ProcessNodeId.Value),
                    Sort =PRS.Sort == null ? 0: PAU.ProcessNodeId.Value,
                    IsBegin = PN.IsBegin,
                    ModuleSort = M.Sort
                })
                .MergeTable()
                .OrderBy(f => new {f.ModuleSort, f.Sort })
                .ToListAsync();
            if(list.Count == 0)
            {
                var listModule = await this.Db.Queryable<WfProjectAuditUser>()
                .InnerJoin<WfModule>((PAU, M) => PAU.ModuleId == M.Id && M.IsDeleted == false)
                .Where((PAU, M) => PAU.IsDeleted == false && PAU.AuditUserId == user.ID)
                .Select((PAU, M) => new ModuleMenuModel
                 {
                     ModuleId = M.Id,
                     ModuleName = M.Name,
                     Logo = M.Logo,
                     ProcessId = 0,
                     ProcessName = "",
                     ProcessNodeId = 0,
                     ProcessNodeName = "",
                     NodeType = 3,
                     TreatHandle = "",
                     TreatHandleUrl = "",
                     StopHandle = "",
                     StopHandleUrl = "",
                     Sort =SqlFunc.ToInt64(M.Sort),
                     IsBegin = 0
                 })
                 .MergeTable()
                .OrderBy(f => f.Sort)
                .ToListAsync();
                return listModule;
            }
            return list;
        }

        /// <summary>
        /// 判断是否存在分组设置
        /// </summary>
        /// <returns></returns>
        public async Task<bool> IsExistGroupProcessSet()
        {
            bool isTrue = false;
            var list = await this.Db.Queryable<WfGroupProcessSet>()
                .InnerJoin<WfProcess>((GPS, P) => GPS.ProcessId == P.Id)
                .Where((GPS, P) => GPS.IsDeleted == false && P.IsDeleted == false && P.Statuz == 1)
                .ToListAsync();
            if (list.Count > 0)
            {
                isTrue = true;
            }
            return isTrue;
        }

        /// <summary>
        /// 判断是否存在权限设置
        /// </summary>
        /// <returns></returns>
        public async Task<bool> IsExistPermissionSet()
        {
            bool isTrue = false;
            var list = await this.Db.Queryable<WfProcess>().Where(f => f.IsDeleted == false && f.UseUnitId == user.UnitId && f.Usage == 2 && f.Statuz == 1 && f.IsOpenControl == 1).ToListAsync();
            if (list.Count > 0)
            {
                isTrue = true;
            }
            return isTrue;
        }

    }
}

