﻿using Hyun.Core.Model;
using Hyun.Core.Model.Models;
using Hyun.Core.Model.SearchModels.Common;
namespace Hyun.Core.IServices
{

    ///<summary>
    ///PClassInfo接口方法
    ///</summary>
    public interface IPClassInfoServices : IBaseServices<PClassInfo>
    {

       /// <summary>
       /// 新增修改
       /// </summary>
       /// <param name="o">PClassInfo对象</param>
       /// <returns></returns>
       Task<Result> InsertUpdate(PClassInfoDto o);

        /// <summary>
        /// 根据Id删除数据【假删除】
        /// </summary>
        /// <param name="id">Id值</param>
        /// <returns></returns>
        Task<Result> FakeDeleteById(long id, long unitId);

       /// <summary>
       /// 根据Id集合批量删除数据【假删除】
       /// </summary>
       ///  <param name="ids">id集合逗号分隔</param>
       /// <returns></returns>
       Task<Result> FakeDeleteByIds(string ids);

       /// <summary>
       /// 根据Id删除数据【真删除】
       /// </summary>
       /// <param name="id">Id值</param>
       /// <param name="unitId">单位Id</param>
       /// <returns></returns>
       Task<Result> DeleteById(long id,long unitId);

        /// <summary>
        /// 根据Id查询对象
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
       Task<PClassInfo> GetById(long id);

        /// <summary>
        /// 根据查询条件获取数据集合
        /// </summary>
       /// <param name="expression">表达式</param>
        /// <returns></returns>
       Task<List<PClassInfo>> Find(Expression<Func<PClassInfo, bool>> expression);

        /// <summary>
        /// 适应装备平台查询方法
        /// </summary>
        /// <param name="param">PClassInfoParam实体参数</param>
        /// <param name="start">页码</param>
        /// <param name="pageLength">每页显示条数</param>
        /// <param name="orderBy">排序，例如 Id DESC</param>
        /// <returns></returns>
       Task<PageModel<PClassInfoDto>> GetPaged(PClassInfoParam param);

        /// <summary>
        /// 导入校服班级信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<Result> ImportClassInfo(List<PClassInfoDto> list);

        /// <summary>
        /// 年级升级
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <returns></returns>
        Task<Result> GradeUpgrade(long unitId);

        /// <summary>
        /// 获取单位年级班级信息
        /// </summary>
        /// <returns></returns>
        Task<List<PClassInfo>> GetClassList();

    }
}

