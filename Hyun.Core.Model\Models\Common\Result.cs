﻿using AutoMapper;
using System.Text.Json.Serialization;

namespace Hyun.Core.Model
{

    public class Data
    {
        /// <summary>
        /// 记录总数
        /// </summary>
        public long total = 0;


        /// <summary>
        /// 当前数据表头
        /// </summary>
        public object headers;

        /// <summary>
        /// 当前数据表体
        /// </summary>
        public object rows;

        /// <summary>
        /// 当前数据表尾
        /// </summary>
        public object footer;

        /// <summary>
        /// 其它数据
        /// </summary>
        public object other;

        /// <summary>
        /// 其它数据
        /// </summary>
        public object other1;

        /// <summary>
        /// 其它数据2
        /// </summary>
        public object other2;


        /// <summary>
        /// 其它数据3
        /// </summary>
        public object other3;
    }


    /// <summary>
    /// web service 响应消息类
    /// </summary>
    public class Result
    {
        /// <summary>
        /// 响应结果标识
        /// -1:session超时
        /// 0：失败
        /// 1：成功
        /// 2+：由各业务存储过程自定义
        /// </summary>
        public int flag = 0;

        /// <summary>
        /// 响应结果消息
        /// </summary>
        public string msg = "执行失败。";

        //public TokenInfoViewModel token = null;

        /// <summary>
        /// 返回数据
        /// </summary>
        public Data data = new Data();

        /// <summary>
        /// 用户Id（Core新增）
        /// </summary>
        [JsonIgnore]
        public long userId = 0;

        /// <summary>
        /// 单位id（Core新增）
        /// </summary>
        [JsonIgnore]
        public long unitId = 0;

        /// <summary>
        /// 返回id
        /// </summary>
        [JsonIgnore]
        public long Id = 0;
    }



    /// <summary>
    /// web service 响应消息类
    /// </summary>
    public class Result<T>
    {
        /// <summary>   
        /// 响应结果标识
        /// -1:session超时
        /// 0：失败
        /// 1：成功
        /// 2+：由各业务存储过程自定义
        /// </summary>
        public int flag = 0;

        /// <summary>
        /// 响应结果消息
        /// </summary>
        public string msg = "执行失败。";

        //public TokenInfoViewModel token = null;

        /// <summary>
        /// 返回数据
        /// </summary>
        public Data2<T> data = new Data2<T>();

        /// <summary>
        /// 用户Id（Core新增）
        /// </summary>
        [JsonIgnore]
        public long userId = 0;

        /// <summary>
        /// 单位id（Core新增）
        /// </summary>
        [JsonIgnore]
        public long unitId = 0;

        public Result() { }

       


        /// <summary>
        /// 返回成功
        /// </summary>
        /// <param name="msg">消息</param>
        /// <param name="flag"></param>
        /// <returns></returns>
        public static Result<T> Success(string msg, int flag = 1)
        {
            return new Result<T>()
            {
                flag = flag,
                msg = msg
            };
        }
        /// <summary>
        /// 返回成功
        /// </summary>
        /// <param name="msg">消息</param>
        /// <param name="rows">数据</param>
        /// <param name="dataCount">总记录数</param>
        /// <returns></returns>
        public static Result<T> Success(string msg, T rows, long dataCount)
        {
            return Message(1, msg, rows, dataCount);
        }
        /// <summary>
        /// 返回失败
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="flag"></param>
        /// <returns></returns>
        public static Result<T> Fail(string msg, int flag = 0)
        {
            return new Result<T>()
            {
                flag = flag,
                msg = msg
            };
        }
        /// <summary>
        /// 返回失败
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="rows"></param>
        /// <param name="dataCount"></param>
        /// <returns></returns>
        public static Result<T> Fail(string msg, T rows, long dataCount)
        {
            return Message(0, msg, rows, dataCount);
        }


        /// <summary>
        /// 返回消息
        /// </summary>
        /// <param name="flag"></param>
        /// <param name="msg"></param>
        /// <param name="rows"></param>
        /// <param name="dataCount">总记录数</param>
        /// <param name="other"></param>
        /// <returns></returns>
        public static Result<T> Message(int flag, string msg, T rows, long dataCount, object other = null)
        {
            return new Result<T>()
            {
                flag = flag,
                msg = msg,
                data = new Data2<T>()
                {
                    rows = rows,
                    other = other,
                    total = dataCount
                }
            };
        }


        public Result(int flag, string msg, long userId, long unitId, Data2<T> data)
        {
            this.flag = flag;
            this.msg = msg;
            this.data = data;
            this.userId = userId;
            this.unitId = unitId;
        }

        public Result<TOut> ConvertTo<TOut>()
        {
            return new Result<TOut>(flag, msg, userId, unitId, default);
        }


        public Result<TOut> ConvertTo<TOut>(IMapper mapper)
        {
            var model = ConvertTo<TOut>();

            if (data != null)
            {
                model.data = mapper.Map<Data2<TOut>>(data);
            }

            return model;
        }


        public Result<TOut> ConvertTo<TOut>(IMapper mapper, Action<IMappingOperationOptions> options)
        {
            var model = ConvertTo<TOut>();
            if (data != null)
            {
                model.data = mapper.Map<Data2<TOut>>(data, options);
            }

            return model;

        }
    }

    public class Data2<T>
    {
        /// <summary>
        /// 记录总数
        /// </summary>
        public long total = 0;


        /// <summary>
        /// 当前数据表头
        /// </summary>
        public object headers;

        /// <summary>
        /// 当前数据表体
        /// </summary>
        public T rows ;

        /// <summary>
        /// 当前数据表尾
        /// </summary>
        public object footer;

        /// <summary>
        /// 其它数据
        /// </summary>
        public object other;

        /// <summary>
        /// 其它数据
        /// </summary>
        public object other1;
    }

}
