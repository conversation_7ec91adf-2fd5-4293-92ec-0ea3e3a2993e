﻿using Hyun.Core.Model.Model;

namespace Hyun.Core.Api.Models
{
    public class SchoolGuidModel
    {


        public long SchoolId { get; set; }

        public string SchoolName { get; set; }

        public string SchoolGuid { get; set; }

        public int Tp { get; set; }
    }


    public class UserValidateModel
    {
        /// <summary>
        /// 账号Id
        /// </summary>
        public long AccountId { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        public DateTime? UserValidate { get; set; }
    }

    public class RoleSetModel
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        public string ChkRoleName { get; set; } 
        
        /// <summary>
        /// 角色Id集合
        /// </summary>
        public string RoleIds { get; set; }

        /// <summary>
        /// 单位类别
        /// </summary>
        public int UnitType { get; set; }
    }

    public class RoleBatchSetModel
    {
        public string ListUserIds { get; set; }
        public string ListRoleIds { get; set; }     
    }


    public class PlatformRoleSetModel
    {
        public string ChkRoleName { get; set; } 

        public string RoleIds { get; set; } 

        public int RoleType { get; set; }

        public int AdministratorType { get; set; }
    }

    public class DepartmentBatchSetModel
    {
        public List<long> ListUserId { get; set; }

        public List<long> ListDepartment { get; set; }
    }

    public class UserPswdModel
    {
        public string OldPwd { get; set; } 
        public string NewPwd { get; set; }
    }

    public class ApplyAfterConfirmModel
    {
        public long id { get; set; }
        public int surplusStatuz { get; set; }

        public int wasetStatuz { get; set; }

        public decimal backNum { get; set; }

        public List<DcWasteRecordModel> wasteList { get; set; }
    }

    public class ApplyBatchAuditModel
    {
        public string ids { get; set; }

        public int processNumber { get; set; }

        public int statuz { get; set; }

        public string remark { get; set; }

        public int currentStatuz { get; set; }

        public int isWithdraw { get; set; }
    }

    public class ApplyAuditModel
    {
        public long id { get; set; }
        public int processNumber { get; set; } 
        public int statuz { get; set; } 
        public string remark { get; set; }
        public int isWithdraw { get; set; }
    }

    public class CollarModel
    {
        public long id { get; set; }

        public string checkCode { get; set; }

        public string withCode { get; set; }

        public long withUserId { get; set; }

        public decimal num { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ThirdEasyGrantModel
    {
        public long id { get; set; }

        public int isNeedSendMessage { get; set; }

        public List<DcThirdMaterialIdGrant> list { get; set; }
    }

    public class TeamBuid
    {
        /// <summary>
        /// Id值
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 文本内容值
        /// </summary>
        public string TxtValue { get; set; }
    }

    public class DepartmentUserUpdateModel
    {
        public string id { get; set; }
        public long odDepartId { get; set; }
        public long newDepartId { get; set; }
    }

    public class JudgeModel
    {
        public long unitExchangeId { get; set; }
        public string remark { get; set; }
        public int statuz { get; set; }
        public string roleIdz { get; set; }
    }

    public class ExchangeUnitModel
    {
        public long unitExchangeId { get; set; }
        public string remark { get; set; } 
        public int statuz { get; set; }
        public string roleIdz { get; set; } 
        public int isUseNewAccount { get; set; }
    }

    public class WebSiteProfilesModel
    {
        public string name { get; set; } 
        public string logo { get; set; } 
        public long IdName { get; set; }
        public long IdLogo { get; set; }
    }

    public class DeviceCodeExtesionModel
    {
        public int dictype { get; set; } 
        public long id { get; set; }
        public string name { get; set; }
        public int sort { get; set; }
        public long deviceCodeId { get; set; }
        public string remark { get; set; }
        public string devicecodeIds { get; set; }
    }

    public class RegisterModel
    {
        public string phoneNumber { get; set; }
        public string uid { get; set; }
        public string codeNum { get; set; }
    }


    public class CompanyRegisterModel
    {
        public string codeNum { get; set; }
        public string phoneNumber { get; set; }
        public string AcctName { get; set; }
        public string Pwd { get; set; }
        public string PersonPhone { get; set; }
        public string CompanyName { get; set; }

        public string phoneId { get; set; }

        public string RankeyId { get; set; }
    }

    public class CompanySubmitModel
    {

        public string CompanyName { get; set; }
        public string LegalPerson { get; set; }

        public string OrganizationCode { get; set; }

        public string BusLinUrl { get; set; }

        public string Name { get; set; }

        public string Phone { get; set; }

        public long AreaId { get; set; }

        public string Address { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string DetailAddress { get; set; }

    }

    public class FindUserPwdModel
    {
        public string AcctName { get; set; }
        public string Moble { get; set; }
        public string Pwd { get; set; }
        public string Code { get; set; }

        public string phoneId { get; set; }

        public string RankeyId { get; set; }
    }

    public class WebSiteConfigModel
    {
        public long ConfigId { get; set; }
        public string ConfigKey { get; set; } 
        public string ConfigValue { get; set; } 
        public string Memo { get; set; }
    }

    public class WebSiteConfigSuperModel
    {
        public long ConfigId { get; set; } 
        public long unitId { get; set; }
        public string ConfigKey { get; set; } 
        public string ConfigValue { get; set; }
        public int ConfigType { get; set; }
        public string Memo { get; set; }
    }

    public class ApiAppSecretModel
    {
        public long id { get; set; }
        public int length { get; set; }
        public bool isHas { get; set; }
    }

    public class MenuPermissionModel
    {
        public int objType { get; set; } 
        public long objId { get; set; } 
        public int funType { get; set; }
        public string funIds { get; set; }
    }

    public class WxWorkTokenResult
    {
        /// <summary>
        /// 错误编码
        /// </summary>
        public int errcode { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string errmsg { get; set; }

        /// <summary>
        /// token值
        /// </summary>
        public string access_token { get; set; }

        /// <summary>
        /// 过期时长
        /// </summary>
        public int expires_in { get; set; }
    }

    /// <summary>
    /// 获取企业微信用户列表接口返回实体
    /// </summary>
    public class WxWorkUserListResult
    {
        /// <summary>
        /// 错误编码
        /// </summary>
        public int errcode { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string errmsg { get; set; }

        /// <summary>
        /// 用户列表
        /// </summary>
        public List<Dept_userItem> dept_user { get; set; }
    }

    /// <summary>
    /// 企业微信用户
    /// </summary>
    public class Dept_userItem
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public string userid { get; set; }

        /// <summary>
        /// 部门id
        /// </summary>
        public int department { get; set; }
    }

    public class DictionaryLinkAgeModel
    {
        public long parentId { get; set; }
        public string nodeIds { get; set; }
        public long moduleId { get; set; }

        public long processFieldId { get; set; }
        public string typeCode { get; set; }
        public string typePcode { get; set; }

    }
}
