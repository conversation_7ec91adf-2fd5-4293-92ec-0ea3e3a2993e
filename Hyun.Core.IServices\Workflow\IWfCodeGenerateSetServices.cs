﻿namespace Hyun.Core.IServices
{

    ///<summary>
    ///WfCodeGenerateSet接口方法
    ///</summary>
    public interface IWfCodeGenerateSetServices : IBaseServices<WfCodeGenerateSet>
    {

        /// <summary>
        /// 根据节点Id及编码获取编码配置信息
        /// </summary>
        /// <param name="processNodeId"></param>
        /// <param name="fieldCode"></param>
        /// <returns></returns>
        Task<Result> GetGenerateCodeSet(long processNodeId, string fieldCode);

        /// <summary>
        /// 保存编码配置信息
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        Task<Result<string>> SaveGenerateCodeSet(WfCodeGenerateSetDto m);

        /// <summary>
        /// 生成项目编码
        /// </summary>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        Task<List<dropdownModel>> GenerateProjectCode(long processNodeId);

        /// <summary>
        /// 根据流程节点获取项目编码
        /// </summary>
        /// <param name="processNodeId"></param>
        /// <returns></returns>
        Task<List<dropdownModel>> GetProjectCode(long processNodeId);
    }
}

