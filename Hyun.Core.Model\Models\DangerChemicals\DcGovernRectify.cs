namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///危化品治理整改
    ///</summary>
    [SugarTable("dc_GovernRectify","危化品治理整改")]
    public class DcGovernRectify : BaseEntity
    {

          public DcGovernRectify()
          {

          }

           /// <summary>
           ///单位Id
          /// </summary>
          public long SchoolId { get; set; }

           /// <summary>
           ///整治年度
          /// </summary>
          public int GovernYear { get; set; }

           /// <summary>
           ///条目Id
          /// </summary>
          public long GovernItemId { get; set; }

           /// <summary>
           ///整治填报表Id
          /// </summary>
          public long GovernReportId { get; set; }

           /// <summary>
           ///整改期限
          /// </summary>
          public DateTime RectifyLimit { get; set; }

           /// <summary>
           ///整改建议
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Suggest { get; set; }

           /// <summary>
           ///整改备注
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Memo { get; set; }

           /// <summary>
           ///申请人Id
          /// </summary>
          public long UserId { get; set; }

           /// <summary>
           ///创建单位Id
          /// </summary>
          public long UnitId { get; set; }

           /// <summary>
           ///填报单位类型
          /// </summary>
          public int UnitIdType { get; set; }

           /// <summary>
           ///申请时间
          /// </summary>
          public DateTime RegDate { get; set; }

        /// <summary>
        ///整改状态（0：待整改 1:已整改）
        /// </summary>
        public int Statuz { get; set; } = -10000;

           /// <summary>
           ///整改措施
          /// </summary>
          [SugarColumn(Length = 511,IsNullable = true)]
          public string Measures { get; set; }

           /// <summary>
           ///整改时间
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public DateTime? RectifyDate { get; set; }

           /// <summary>
           ///整改申请人Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? RectifyUserId { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

