namespace Hyun.Core.Model.Models
{

    ///<summary>
    ///模块流程状态配置
    ///</summary>
    [SugarTable("b_ModuleProcessStatuzConfig","模块流程状态配置")]
    public class BModuleProcessStatuzConfig : BaseEntity
    {

          public BModuleProcessStatuzConfig()
          {

          }

           /// <summary>
           ///模块Id
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public long? ModuleProcessConfigId { get; set; }

           /// <summary>
           ///状态值
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? Statuz { get; set; }

           /// <summary>
           ///状态描述
          /// </summary>
          [SugarColumn(Length = 31,IsNullable = true)]
          public string StatuzDesc { get; set; }

           /// <summary>
           ///市级详情链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string DetailUrl1 { get; set; }

           /// <summary>
           ///市级列表链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ListUrl1 { get; set; }

           /// <summary>
           ///区县详情链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string DetailUrl2 { get; set; }

           /// <summary>
           ///区县列表链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ListUrl2 { get; set; }

           /// <summary>
           ///单位详情链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string DetailUrl3 { get; set; }

           /// <summary>
           ///单位列表链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ListUrl3 { get; set; }

           /// <summary>
           ///企业详情链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string DetailUrl4 { get; set; }

           /// <summary>
           ///企业列表链接地址
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string ListUrl4 { get; set; }

           /// <summary>
           ///适应角色（逗号分隔）
          /// </summary>
          [SugarColumn(Length = 255,IsNullable = true)]
          public string RoleIdz { get; set; }

           /// <summary>
           ///排序
          /// </summary>
          public int Sort { get; set; }

           /// <summary>
           ///备注
          /// </summary>
          [SugarColumn(Length = 1024)]
          public string Remark { get; set; }

           /// <summary>
           ///启用禁用状态(1：启用 2：禁用)
          /// </summary>
          [SugarColumn(IsNullable = true)]
          public int? SetStatuz { get; set; }

           /// <summary>
           ///适应角色名称（逗号分隔）
          /// </summary>
          [SugarColumn(Length = 1024)]
          public string RoleNamez { get; set; }

           /// <summary>
           ///中立字段，某些表可使用某些表不使用
          /// </summary>
          [SugarColumn(IsIgnore = true)]
          public new bool  Enabled { get; set; } = true;


    }


}

