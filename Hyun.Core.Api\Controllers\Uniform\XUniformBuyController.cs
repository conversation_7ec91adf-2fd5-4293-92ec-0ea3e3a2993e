﻿
using Hyun.Core.Common.HttpContextUser;
using Hyun.Core.Model.Models.Uniform;
using Hyun.Core.IServices.Uniform;
using Microsoft.AspNetCore.Mvc;
using Hyun.Core.Services.Uniform;
using Hyun.Core.Model;
using Hyun.Core.Services;
using Hyun.Core.Model.Models;
using Hyun.Old.Util;
using Com.Ctrip.Framework.Apollo.Enums;
using Dm;
using Hyun.Core.Model.Models.Workflow;

namespace Hyun.Core.Api.Controllers.Uniform
{

    [Route("api/hyun/xuniformbuy")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class XUniformBuyController : BaseApiController
    {

        private readonly IWebHostEnvironment env;
        private readonly IMapper mapper;
        private readonly IUser user;
        private readonly IXUniformBuyServices uniformBuyManager;
        private readonly IBDictionaryServices dictionaryManager;
        private readonly IBAttachmentServices attachmentManager;
        private readonly IXUniformSchemeServices unitformSchemeManager;
        private readonly IPUnitServices unitManager;
        private readonly IBAreaServices areaManager;

        public XUniformBuyController(IWebHostEnvironment _env, IMapper _mapper, IUser _user, IXUniformBuyServices _uniformBuyManager, IBDictionaryServices _dictionaryManager, IBAttachmentServices _attachmentManager, IXUniformSchemeServices _unitformSchemeManager, IPUnitServices _unitManager, IBAreaServices _areaManager)
        {
            mapper = _mapper;
            env = _env;
            user = _user;
            uniformBuyManager = _uniformBuyManager;
            dictionaryManager = _dictionaryManager;
            attachmentManager = _attachmentManager;
            unitformSchemeManager = _unitformSchemeManager;
            unitManager = _unitManager;
            areaManager = _areaManager;
        }

        /// <summary>
        ///  采购申请列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getuniformbuylist")]
        public async Task<Result<List<XUniformBuyDto>>> GetUniformBuyList([FromBody] XUniformBuyParam param)
        {
            var msgdata = new Result<List<XUniformBuyDto>>();
            PageModel<XUniformBuy> pg = await uniformBuyManager.GetPaged(param);
            if (param.isFirst)
            {
                //采购申请状态
                var listEnumUsage = EnumExtensions.EnumToList<UniformFilingEnum>();
                List<dropdownModel> listPurchaseStatuz = listEnumUsage.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

                //招标公告公开
                var listEnumBidPublic = EnumExtensions.EnumToList<BidPublic>();
                List<dropdownModel> listPublic = listEnumBidPublic.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();


                //获取组织形式
                List<BDictionary> listOrganizational = await dictionaryManager.GetByTypeCode("506000");
                List<dropdownModel> listOrganizationalDropdown = listOrganizational.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                //获取采购方式
                List<BDictionary> listMethod = await dictionaryManager.GetByTypeCode("507000");
                List<dropdownModel> listMethodDropdown = listMethod.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

                if(user.UnitTypeId == 3)
                {
                    msgdata = baseSucc(mapper.Map<List<XUniformBuyDto>>(pg.data), pg.dataCount,
                  $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                  new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listOrganizational = listOrganizationalDropdown, listMethod = listMethodDropdown });
                }
                else if (user.UnitTypeId == 2)
                {
                    var listSchoolInfo = await unitManager.Find(f => f.UnitType == UnitTypes.School.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    List<dropdownModel> listSchool = listSchoolInfo.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();
                    msgdata = baseSucc(mapper.Map<List<XUniformBuyDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listOrganizational = listOrganizationalDropdown, listMethod = listMethodDropdown, listSchool = listSchool });
                }
                else if (user.UnitTypeId == 1)
                {
                    //var listAreaInfo = await areaManager.Query(f => f.Pid == user.AreaId && f.IsDeleted == false);
                    //List<dropdownModel> listArea = listAreaInfo.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.Name }).ToList();
                    List<dropdownModel> dropdownCounty = new List<dropdownModel>();
                    List<BArea> listArea = null;
                    var listCounty = await unitManager.Find(f => f.PId == user.UnitId && f.UnitType == UnitTypes.Couty.ObjToInt() && f.IsDeleted == false && f.Statuz == 1);
                    if (listCounty != null)
                    {
                        //获取所有区域Id
                        if (listArea == null)
                        {
                            listArea = await areaManager.Find(m => m.IsDeleted == false);
                        }
                        foreach (var item in listCounty)
                        {
                            dropdownCounty.Add(new dropdownModel() { value = item.Id.ToString(), label = GetAreaName(item.AreaId, item.Name, listArea) });
                        }
                    }
                    msgdata = baseSucc(mapper.Map<List<XUniformBuyDto>>(pg.data), pg.dataCount,
                   $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}",
                   new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listOrganizational = listOrganizationalDropdown, listMethod = listMethodDropdown, listArea = dropdownCounty });
                }
            }
            else
            {
                msgdata = baseSucc(mapper.Map<List<XUniformBuyDto>>(pg.data), pg.dataCount, $"{param.pageIndex}-{param.pageSize}/{pg.dataCount}");
            }
            return msgdata;
        }

        /// <summary>
        /// 保存修改采购申请
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("uniformbuysave")]
        public async Task<Result<string>> UniformBuySave([FromBody] XUniformBuyDto o)
        {
            return await uniformBuyManager.InsertUpdate(o);
        }

        /// <summary>
        /// 根据Id获取采购申请数据信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("uniformbuygetbyid")]
        public async Task<Result> UniformBuyGetById(long id)
        {
            Result r = new Result();
            r.flag = 1;
            r.msg = "查询成功";

            //采购申请状态
            var listEnumUsage = EnumExtensions.EnumToList<UniformFilingEnum>();
            List<dropdownModel> listPurchaseStatuz = listEnumUsage.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

            //招标公告公开
            var listEnumBidPublic = EnumExtensions.EnumToList<BidPublic>();
            List<dropdownModel> listPublic = listEnumBidPublic.Select(f => new dropdownModel { value = f.Value.ToString(), label = f.Description }).ToList();

            //获取组织形式
            List<BDictionary> listOrganizational = await dictionaryManager.GetByTypeCode("506000");
            List<dropdownModel> listOrganizationalDropdown = listOrganizational.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

            //获取采购方式
            List<BDictionary> listMethod = await dictionaryManager.GetByTypeCode("507000");
            List<dropdownModel> listMethodDropdown = listMethod.Select(f => new dropdownModel { value = f.DicValue, label = f.DicName }).ToList();

            //选用批次
            List<XUniformScheme> listScheme = await unitformSchemeManager.Query(f => f.IsDeleted == false && f.FilingStatuz == 100 && f.SchoolId == user.UnitId);
            List<dropdownModel> listSchemeDropdown = listScheme.Select(f => new dropdownModel { value = f.Id.ToString(), label = f.SchemeNo }).ToList();

            string downLoadFile = $"/Download/校服清单模板.xlsx";

            r.data.other = new { listBidPublic = listPublic, listStatuz = listPurchaseStatuz, listOrganizational = listOrganizationalDropdown, listMethod = listMethodDropdown, listUniformSchemeId = listSchemeDropdown, filePath = downLoadFile };

            XUniformBuy obj = await uniformBuyManager.QueryById(id);
            if (obj != null)
            {
                r.data.rows = mapper.Map<XUniformBuyDto>(obj);
                List<BAttachment> listAttach = await attachmentManager.Query(f => f.IsDelete == 0 && f.IsDeleted == false && f.ObjectId == id && f.ModuleType == ModuleTypeEnum.PurchaseApply.ToEnumInt());
                r.data.footer = listAttach;
            }
            return r;
        }

        /// <summary>
        /// 提交采购申请
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("uniformbuysubmit")]
        public async Task<Result<string>> UniformBuySubmit(long id)
        {
            return await uniformBuyManager.Submit(id);
        }

        /// <summary>
        /// 采购申请删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("uniformbuydeletebyid")]
        public async Task<Result<string>> UniformBuyDeleteById(long id)
        {
            return await uniformBuyManager.DeleteById(id);
        }

        /// <summary>
        /// 采购申请审核
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("uniformbuyaudit")]
        public async Task<Result<string>> UniformBuyAudit([FromBody] BuyAuditModel o)
        {
            return await uniformBuyManager.Audit(o);
        }

        /// <summary>
        /// 采购申请撤销、退回
        /// OptType（1：撤销 2：退回）
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("uniformbuyrevoke")]
        public async Task<Result<string>> UniformBuyRevoke([FromBody] BuyRevokeModel o)
        {
            return await uniformBuyManager.Revoke(o);
        }

        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="id"></param>
        /// <param name="attid"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("delattachmentbyid")]
        public async Task<Result> DeleteAttachmentById(long id, long attid)
        {
            Result r = new Result();
            var model = new XUniformBuyDto();
            model.Id = id;
            model.AttachmentId = attid;
            model.SchoolId = user.UnitId;
            model.CreateId = user.UserId;
            r = await uniformBuyManager.UpdateAttachmentDelete(model);
            return r;
        }

        private string GetAreaName(long countyAreaId, string name, List<BArea> listArea)
        {
            if (listArea != null && listArea.Where(m => m.Id == countyAreaId).Count() > 0)
            {
                name = listArea.Where(m => m.Id == countyAreaId).FirstOrDefault().Name;
            }
            return name;
        }
    }
}
